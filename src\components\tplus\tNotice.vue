<template>
    <view class="notice" :animation="animationData" v-if="noticeVisible">
        <slot></slot>
    </view>
</template>
<script>
export default {
    props: {
        visible: { type: Boolean, default: false },
        duration: { type: Number, default: 1500 }
    },
    data(){
        return {
            isShow: false,
            noticeVisible: false,
            animationData: {}
        }
    },
    watch: {
        visible(val) {
            this.isShow = val;
        },
        isShow(val){
            if(val !== this.visible){
                this.$emit('update:visible', val);
            }
            val ? this.show() : this.hide()
        }
    },
    methods: {
        async show() {
            if(!this.isShow){
                this.isShow = true;
            }else{
                this.noticeVisible = true;
                await this.$nextTick();
                const animation = uni.createAnimation({ duration: 200 });
                animation.opacity(1).step()
                this.animationData =  animation.export();
                if(this.duration > 0){
                    await this.$Utils.sleep(this.duration + 200);
                    this.hide()
                }
            }
        },
        async hide() {
            if(this.isShow){
                this.isShow = false;
            }else{
                const animation = uni.createAnimation({ duration: 200 })
                animation.opacity(0).step()
                animation.height(0).step()
                this.animationData =  animation.export();
                await this.$Utils.sleep(450);
                this.animationData =  {};
                this.noticeVisible = false;
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.notice{
    height: 80rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    font-size: 26rpx;
    color: #fdfdfd;
    background-color: #85b6ff;
    opacity: 0;
}
</style>