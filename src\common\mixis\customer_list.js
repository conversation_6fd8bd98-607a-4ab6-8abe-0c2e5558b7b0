export default {
    onLoad(options){
		//#ifndef MP
        if (this.$isWxWork() == 'wxwork' || this.$isWxWork() == 'com-wx-pc') {
			let token = uni.getStorageSync("wxwork_token");
			if (!token) {
			  return
			}
		} else {
			/* let website_id = options.website_id
			let token = localStorage.getItem("token" + website_id)
			if (!token) {
			  localStorage.setItem('backUrl', location.href)
			  this.$router.push("https://yun.tfcs.cn")
			} */
		}
		//#endif

		/* if (options.tel) {
			this.params.mobile = options.tel;
		} */


        uni.$on("getDataAgain", () => {
			this.$refs.customerList.search();
		});

		console.log("监听通过列表添加的跟进");
        uni.$on('upCustomerFollowContent',(data)=>{
            console.log('监听到跟进从列表直接添加的跟进内容');
            console.log('返回参数',data);
			this.$refs.customerList.upCustomerFollowContent(data);
        });

		this._sysSetScrollTop(0);

    },
	methods:{
		_sysSetScrollTop(scrollTop){
			this.$store.commit('setScrollTop', scrollTop);
		}
	},
	onPageScroll (e) {
		//同步更新
		// 防抖操作
		if (this.sysScrollTimeout) {
			console.log('清除定时器');
			clearTimeout(this.sysScrollTimeout);
		}
		this.sysScrollTimeout = setTimeout(() => {
			this._sysSetScrollTop(e.scrollTop);
		}, 1000);
		//异步更新
		//this.$store.dispatch('updateScrollTop', e.scrollTop);
	},
	onUnload () {
		console.log("移除监听 getDataAgain 和 upCustomerFollowContent");
		uni.$off("getDataAgain");
		uni.$off('upCustomerFollowContent');
	},
	async onPullDownRefresh(){
		this.$refs.customerList.search();
		await this.$Utils.sleep(600)
		uni.stopPullDownRefresh();
	},
	onReachBottom () {
		this.$refs.customerList.getList();
	},
    onBackRefresh(){
		this.$refs.customerList.search();
    }
}