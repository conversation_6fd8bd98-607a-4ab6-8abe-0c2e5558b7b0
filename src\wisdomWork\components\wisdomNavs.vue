<template>
    <view class="tabs">
        <tabBar :tabs="list" :fixed-top="false" :equispaced="false" :now-index="curTabIndex" @click="handleTabClick"></tabBar>
    </view>
</template>
    
<script>
import tabBar from '@/components/tabBar.vue';
export default {
    props: {
        datas: { type: Array, default: ()=>[] },
        value: { type: String, default: '' },
    },
    components: {
        tabBar
    },
    data() {
        return {
            curTabIndex: 0,         //当前选中的tab索引
        }
    },
    computed: {
        list(){
            return this.datas.map( (e, index) => {
                e.index = index;
                e.description = e.label;
                return e;
            });
        }
    },
    watch: {
        curTabIndex: {
            handler(val) {
                let tab = this.list.find(e => e.index === val);
                this.$emit('input', tab ? tab.name : '');
            },
            immediate: true
        },
    },
    mounted() {
        this.$watch('value', {
            handler(val) {
                if(val === ''){
                    this.$emit('input', this.list[0]?.name);
                }else{
                    let tab = this.list.find(e => e.name === val);
                    this.curTabIndex = tab ? tab.index : 0;
                }
            },
            immediate: true
        })
    },
    methods: {
        handleTabClick(e){
            if(this.curTabIndex !== e.index){
                this.curTabIndex = e.index;
                this.$nextTick(()=>{
                    this.$emit('change', e);
                })
            }
        }
    }
}
</script>

<style scoped lang="scss"> 
.tabs{
    position: relative;
    &::after{
        content: " ";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1rpx;
        background-color: #f2f3f5;
    }
}
::v-deep .nav-box{
    .nav-list{
        height: 96rpx;
        .nav-item {
            width: auto;
            padding: 0 32rpx;
            margin: 0;
            font-size: 32rpx;
            height: 100%;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            &.active {
                color: #007aff;
                &:before{
                    height: 6rpx;
                    width: 32rpx;
                }
            }
        }
    }
}
</style>