<template>
  <view
    class="my-input flex-row"
    :class="[size, border ? 'border' : '', disabled ? 'disabled' : '']"
  >
    <!-- <view class="flex-row flex-1 items-center"> -->

      <view class="flex-row flex-1 items-center">
       <!-- <view>
        <textarea fixed='true' contenteditable="true" auto-height="true"
          class="input textarea"
        v-if="type === 'textarea'"
        :style="{ height: height }"
        :value="value"
        :maxlength="maxlength"
        :focus="isfocus"
        :disabled="disabled"
        :placeholder="placeholder"
        @input="$emit('input', $event.detail.value)"
        @blur="blur"
        :placeholder-style="placeholderStyle"
            ></textarea>
            <view style="text-align: right;padding-bottom: 24rpx;" v-if="type === 'textarea'">{{count|| value ? value.length : 0 }} / {{ maxLength }}</view>
       </view> -->
             <textarea
        class="input textarea uni-textarea"
        v-if="type === 'textarea'"
        :style="{ height: height }"
        :value="value"
        :maxlength="maxlength"
        :focus="isfocus"
        :disabled="disabled"
        :placeholder="placeholder"
        @input="$emit('input', $event.detail.value)"
        @blur="blur"
        :placeholder-style="placeholderStyle"
      ></textarea>
      <!-- placeholder-style="color:#8a929f;" -->
      <input
      v-if="type !== 'textarea'"
        class="input flex-1"
        :type="type"
        :value="value"
        :maxlength="maxlength"
        :focus="isfocus"
        :disabled="disabled"
        :placeholder="placeholder"
        @input="$emit('input', $event.detail.value)"
        @blur="blur"
        @focus="focus"
        :placeholder-style="placeholderStyle"
      />
      <text v-if="unit" class="unit">{{ unit }}</text>
    </view>
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: 'myInput',
  inheritAttrs: false,
  components: {},
  props: {
    value: [String, Number],
    placeholder: {
      type: String,
      default: '请输入',
    },
    maxlength: {
      type: [String, Number],
      default: 120,
    },
    unit: String,
    border: Boolean,
    disabled: Boolean,
    size: {
      type: String,
      default: 'base',
    },
    type: {
      type: String,
      default: 'text',
    },
    height: {
      type: String,
      default: '',
    },
    isfocus: {
      type: Boolean,
      default: false,
    },
    placeholderStyle: {
      type: String,
      default: 'color:#8a929f;',
    },
  },
  data() {
    return {
      maxLength:100,
      count:0
    }
  },
  methods: {
    blur() {
      uni.hideKeyboard()
      this.$emit('blur', this.value)
    },
    focus() {
      this.$emit('focus', this.value)
    },
    input(){
      this.count = this.content.length;
    }
  },
}
</script>

<style lang="scss">
::v-deep .uni-textarea{
width: 100%;
z-index: 999;
}
.my-input {
  align-items: center;
  justify-content: space-between;
  border-radius: 12rpx;
  background-color: #fff;
  &.disabled {
    background-color: #f8f8f8;
    .input {
      color: #999;
    }
  }
  .input {
    font-size: 32rpx;
    &.textarea {
      // width: 100%;
    }
  }
  .unit {
    margin-left: 12rpx;
    font-size: $uni-font-size-base;
    color: #999;
  }
  &.border {
    border: 1rpx solid #dde1e9;
    padding: 8rpx;
  }
  &.big {
    border-radius: 12rpx;
    font-size: 36rpx;
    .input {
      font-size: 36rpx;
    }
    &.border {
      padding: 12rpx;
    }
  }
  &.small {
    border-radius: 8rpx;
    font-size: 28rpx;
    .input {
      font-size: 28rpx;
    }
    &.border {
      padding: 6rpx;
    }
  }
  &.mini {
    border-radius: 6rpx;
    font-size: 24rpx;
    .input {
      font-size: 24rpx;
    }
    &.border {
      padding: 4rpx;
    }
  }
}
</style>
