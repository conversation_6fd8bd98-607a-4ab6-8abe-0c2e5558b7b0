<template>
    <view class="add-forms" v-show="lazyOvered">
        <addFormTpl :form="cateForm" v-model="cateForm.value" v-if="!isEdit"></addFormTpl>
        <addFormMobile v-model="mobiles.forms" :options="mobiles.options" :addAble="mobiles.addAble"></addFormMobile>
        <addFormTpl v-for="form in curOtherForms" :key="form.name" :form="form" v-model="form.value"></addFormTpl>
    </view>
</template>
<script>
import { getCustomerForms } from '@/common/utils/customer'
import addFormTpl from './addFormTpl.vue'
import addFormMobile from './addFormMobile.vue'
export default {
    props: {
        isEdit: { type: Boolean, default: false },
        current: { type: String, default: '' },
        customer: { type: Object, default: () => {} },
    },
    components: {
        addFormTpl,
        addFormMobile
    },
    data(){
        return{
            cateForm: {
                name: "current",
                require: true,
                title: "客户分类",
                type: "radio-button",
                value:  '',
                options :  [
                    { label: '私客', value: 'my' },
                    { label: '公海', value: 'seas' },
                    { label: '流转客', value: 'trans' },
                ]
            },
            mobiles: {},
            otherForms: [],
            lazyOvered: false
        }
    },
    computed:{
        isTrans(){
            return this.current === 'trans'
        },
        customerId(){
            return this.customer.id;
        },
        curOtherForms(){
            return this.otherForms.filter(item => !item.cate || item.cate == this.current)
        }
    },
    watch: {
        current: {
            handler(val){
                this.cateForm.value = val
            },
            immediate: true
        },
        'cateForm.value'(val){
            this.$emit('update:current', val)
        }
    },
    created(){
        this.init();
    },
    methods:{
        async init(){
            uni.showLoading();
            await this.getForms();

            if(this.isEdit){
                this.$watch('customerId', {
                    handler(){
                        this.setFormsData();
                        this.$nextTick(()=>{
                            this.lazyOvered = true;
                            uni.hideLoading();
                        })
                    },
                    immediate: true
                })
            }else{
                this.$nextTick(()=>{
                    this.lazyOvered = true;
                    uni.hideLoading();
                })
            }
        },
        async getForms(){
            const forms = await getCustomerForms();
            this.forms = forms.map(e => {
                e.options = e.value || [];
                e.value = '';

                if(e.type == 'select'){
                    e.filterable  = e.filterable || false;
                    e.labelKey = e.labelKey || 'name';
                    e.valueKey = e.valueKey || 'id';
                    e.childrenKey = e.childrenKey  || 'children';

                    if(e.multiple){
                        e.value = []
                    }else{
                        if(e.name.includes('|')){
                            e.value = []
                        }
                    }
                }
                return e;
            });

            //客户姓名-手机号
            const subsidiaryMobile = forms.find(e => e.name == 'subsidiary_mobile');
            const mobileOptions = forms.filter(e => [ 'cname', 'mobile'].includes(e.name)).map(e => {
                if(e.name == 'mobile'){
                    e.isMainNumber = true;      //是否主号
                    if(!this.isEdit){
                        e.disabled = false; 
                    }
                    e.is_del = 0;               //是否删除，同后端所需字段
                }
                return e;
            })
            const mobileForms = [];
            mobileForms.push(mobileOptions.map(e => ({...e})))
       
            this.mobiles = {
                forms: mobileForms,
                options: mobileOptions.map(e => ({...e, isMainNumber: false, require: false, disabled: false })),
                addAble: !!subsidiaryMobile
            }

            console.log(this.mobiles);

            //其他表单数据
            const filters = [ 'cname', 'mobile', 'subsidiary_mobile']
            this.otherForms = this.forms.filter(e => !filters.includes(e.name))
        },
        setFormsData(){
            const customer = this.customer;
            if(customer.type === 0 ){
                customer.type = '';
            }
            if(customer.sex === 0 ){
                customer.sex = '';
            }
            if(customer.source_id === 0){
                customer.source_id = '';
            }
            if(customer.source2_id === 0){
                customer.source2_id = '';
            }
            if(customer.level_id === 0){
                customer.level_id = '';
            }
            if(customer.province_id === 0){
                customer.province_id = '';
            }
            if(customer.area_id === 0){
                customer.area_id = '';
            }
            if(customer.city_id === 0){
                customer.city_id = '';
            }
            
            const mobileForms = this.mobiles.forms[0].map(e => {
                e.value = customer[e.name] ?? '';
                return e;
            });
            const telList = Array.isArray(customer.subsidiary_tel_list) ? customer.subsidiary_tel_list : [];
            const hasMainNumber = telList.some(e => e.is_main);

            if(!hasMainNumber){
                telList.unshift({ is_main: 1 })
            }
            this.mobiles.forms = telList.map(item => {
                if(item.is_main){
                    if(!this.isTrans){
                        if(this.isEdit){
                            mobileForms[1].value = mobileForms[1].value.replace(/(\d{3})\d{5}(\d{3})/, "$1*****$2");
                        }
                    }
                    return mobileForms
                }else{
                    return this.mobiles.options.map(e => {
                        e = {...e};
                        if(e.name == 'cname'){
                            e.value = item.name ?? '';
                        }else{
                            e.id = item.id; 
                            e.disabled = item.tel !== '';
                            e.value = this.isTrans ? item.tel : item.tel.replace(/(\d{3})\d{5}(\d{3})/, "$1*****$2");
                            e.mobile = item.tel;
                        }
                        return e;
                    })
                }
            })
        
            for(const item of this.otherForms){
                let value = '';
                if(item.name.includes('|')){
                    value = item.name.split('|').map(e => {
                        return customer[e] ?? ''
                    })
                }else{
                    value = customer[item.name] ?? '';
                    if(item.type == 'select' && item.multiple && !Array.isArray(value)){
                        value = value ==='' ? [] : String(value).split(',').map(e=>{return item.valueFormat=='number'?e*1:e});
                    }
                }
                item.value = value;
            }
            
       
            console.log(this.mobiles);

        },
        isSelector(item){
            return item.type === 'select' || item.type === 'radio-button'
        },
        isEmpty(item){
            if(item.name.includes('|')){
                if(item.value.length === 0){
                    return true;
                }
                if(item.value.join('')===''){
                    return true;
                }
            }
            if(Array.isArray(item.value)){
                return item.value.length == 0;
            }

            return item.value === '';
        },
        checkRequire(item){
            if(item.require){
                if(this.isEmpty(item)){
                    uni.showToast({title: (this.isSelector(item) ? '请选择':'请填写')+item.title, icon: 'none'})
                    return false;
                }
            }
            return true;
        },  
        formatValue(val, type){
            switch(type){
                case 'string':
                    return String(val);
                case 'number':
                    return Number(val);
                default:
                    return val;
            }
        },     
        checkSubmit(){
            const params = {}

            const subsidiaryMobiles = [];       //附属号码列表
            let order = 2;  //附属号码排序值
            for(const forms of this.mobiles.forms){
                //主号
                const mobileForm = forms.find(e => e.name == 'mobile') || {};
                if(mobileForm.isMainNumber){
                    for(const item of forms){
                        if(!this.checkRequire(item)){
                            return false;
                        }
                        params[item.name] = item.value;
                    }
                    order = 0;
                }else{
                    let cnameForm = forms.find(e => e.name == 'cname'),
                        cname = cnameForm ? cnameForm.value.trim() : '',
                        mobile = !mobileForm.disabled ? String(mobileForm.value ?? '').trim() : mobileForm.mobile;

                    if(cname !== '' || mobile !== ''){    
                        subsidiaryMobiles.push({id: mobileForm.id || 0, name: cname, tel: mobile, order: order, is_del: mobileForm.is_del});
                    }
                }
            }
            const hasSubsidiaryMobile = !!this.forms.find(e => e.name == 'subsidiary_mobile')
            if(hasSubsidiaryMobile){
                params['subsidiary_mobile'] = subsidiaryMobiles;
            }

        

            for(const item of this.curOtherForms){
                if(!this.checkRequire(item)){
                    return false;
                }
                params[item.name] = item.value;
            }

            const datas = {};
            for(const key in params){
                const value = params[key];
                const filed = this.forms.find(e => e.name == key)
                const valueType = filed && filed.valueType || '';
                if(key.includes('|') && Array.isArray(value)){
                    key.split('|').forEach((item, index) => {
                        datas[item] = this.formatValue(value[index] ?? '', valueType);
                    })
                }else{
                    datas[key] = this.formatValue(value, valueType);
                }
            }

            return datas;
        }
    }
}
</script>

<style lang="scss" scoped>
.add-forms{
    padding: 32rpx;
    background-color: #fff;
}
</style>