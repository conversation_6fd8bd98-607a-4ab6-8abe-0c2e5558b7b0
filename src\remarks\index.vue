<template>
  <view class="approval">
    <view class="approval-search">
      <view class="search-box">
        <view class="search-type">
          <text @click="isSearchBox = !isSearchBox">{{ (deptSelectedLable || adminSelectedLable) || '部门 / 成员' }}</text>
          <text class="icon"></text>
          <uni-icons v-if="param.department_id || param.user_name" style="padding: 10rpx;" type="clear" color="#c1c1c1"
            size="26" @click="cancleDeptSeled"></uni-icons>
        </view>
        <view class="search-box">
          <input :value="param.number" placeholder-class="my-input" confirm-type="search" placeholder="请输入审批编号"
            @confirm="searchConfirm" @input="searchChange" />
        </view>
      </view>
      <view class="search-type-select" v-if="isSearchBox">
        <text @click="showDepartmentPicker">部门检索</text>
        <text @click="showAdminPicker">成员检索</text>
      </view>
    </view>
    <view class="approval-filter">
      <scroll-view scroll-x>
        <text :class="filterChecked == index ? 'checked' : ''" v-for="(item, index) in filter_data" :key="index"
          @click="filterChange(item, index)">{{
            item.name
          }}</text>
      </scroll-view>
    </view>
    <view class="approval-body">
      <view class="approval-box-item" v-for="(item, index) in approvalList" :key="index">
        <view class="item-head" @click="toDetail(item)">
          <text>ID: {{ item.id }}</text>
          <text>{{ item.ctime }}</text>
        </view>
        <view class="item-body">
          <view>
            <text @click="toDetail(item)">审批编号：</text>
            <text @click="copyCode(item.number)">{{ item.number }}</text>
            <myIcon @click="copyCode(item.number)" style=" margin-left: 10rpx;" type="fuzhi" color="#bbb3b3" size="32rpx">
            </myIcon>
          </view>
          <view @click="toDetail(item)">
            <text>审批类型：</text>
            <text>{{ item.cat_name.title }}</text>
          </view>
          <view @click="toDetail(item)">
            <text>审批事项：</text>
            <text>{{ item.item ? item.item : '--' }}</text>
          </view>
        </view>
        <view class="item-btm" @click="toDetail(item)">
          <view>
            <text>{{ item.applicant.user_name[0] }}</text>
            <text>由 {{ item.applicant.user_name }} 提交审批</text>
          </view>
          <text :class="['item-state', item.status == 1 ? 'item-state2' : '']">{{ approvalStatus(item.status) }}</text>
        </view>
      </view>
      <loadMore :status="load_status" @reload="getApprovalList()" />
    </view>
    <view class="approval-filter-btm">
      <text :class="stateFilterChecked == index ? 'filter-btm-checked' : ''" v-for="(item, index) in approvalStateFilter"
        :key="index" @click="btmFilterChange(item, index)">{{ item.name }}</text>
    </view>
    <tDepartmentPicker :visible.sync="dialogs.deptPicker" v-model="department_id" @confirm="confirmSeledDept">
    </tDepartmentPicker>
    <tMemberPicker :visible.sync="dialogs.adminPicker" v-model="admin_id" @confirm="confirmSeledAdmin"></tMemberPicker>
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
import tDepartmentPicker from '@/components/tplus/tDepartmentPicker.vue';
import tMemberPicker from '@/components/tplus/tMemberPicker.vue';
import loadMore from '@/components/loadMore'
export default {
  components: {
    tDepartmentPicker, tMemberPicker, loadMore, myIcon
  },
  data() {
    return {
      isSearchBox: false,//搜索类型显示/隐藏
      load_status: 'loading',
      stateFilterChecked: 0,//底部筛选
      filterChecked: 0,//头部筛选
      filter_data: [
        { name: '全部', value: '' },
        { name: '审批中', value: 0 },
        { name: '已通过', value: 1 },
        { name: '已驳回', value: 2 },
        { name: '已撤销', value: 3 },
        { name: '抄送我', value: 4, is_owner: 1 },
        { name: '我的申请', value: 5, is_my: 1 },
      ],
      approvalStateFilter: [
        { name: '全部', value: '' },
        { name: '成交审批', value: 18 },
        { name: '变更状态', value: 19 },
        { name: '删除客户', value: 21 },
      ],
      searchValue: 0,
      approvalList: [],
      keywords: '',//搜索关键词
      searchType: '',//搜索类型
      searchValue: '',//搜索值
      department_id: '',//部门ID
      admin_type: 0,//成员类型
      admin_id: '',	//成员ID
      dialogs: {
        deptPicker: false,
        adminPicker: false
      },
      deptSelectedLable: '',//已选中的部门label
      adminSelectedLable: '',//已选中的成员label
      searchType: [
        { value: 1, text: "部门" },
        { value: 2, text: "成员" },
      ],
      param: {
        page: 1,
        total: 47,
        per_page: 10,
        cat_id: '',
        status: '',
        is_my: 0,
        is_owner: 0,
        number: ''
      }
    }
  },
  computed: {

  },
  onReachBottom() {
    if (this.load_status === 'loadend') {
      this.param.page++
      this.getApprovalList()
    }
  },
  onLoad() {
    this.getApprovalList()
    uni.$on('refreshData', () => {
      this.approvalList = []
      this.getApprovalList()
    })
  },
  methods: {
    cancleDeptSeled() {
      this.param.department_id = '';
      this.deptSelectedLable = '';
      this.param.user_name = ''
      this.adminSelectedLable = ''
      this.approvalList = []
      this.getApprovalList()
    },
    //显示部门选择
    showDepartmentPicker() {
      this.isSearchBox = false
      this.dialogs.deptPicker = true;
    },
    //显示成员选择
    showAdminPicker() {
      this.isSearchBox = false
      this.dialogs.adminPicker = true;
    },
    btmFilterChange(item, index) {
      this.param.page = 1
      this.stateFilterChecked = index
      this.param.cat_id = item.value
      this.approvalList = []
      this.getApprovalList()
    },
    filterChange(item, index) {
      this.param.page = 1
      if (item.value > 3) {
        this.param.status = ''
        if (item.value == 4) {
          this.param.is_my = 0
          this.param.is_owner = 1
        }
        if (item.value == 5) {
          this.param.is_owner = 0
          this.param.is_my = 1
        }
      } else {
        this.param.is_owner = 0
        this.param.is_my = 0
        this.param.status = item.value
      }
      this.filterChecked = index
      this.approvalList = []
      this.getApprovalList()
    },
    approvalStatus(status) {
      switch (status) {
        case 0:
          status = '待审批'
          break;
        case 1:
          status = '已通过'
          break;
        case 2:
          status = '已驳回'
          break;
        case 3:
          status = '已撤销'
          break;
        default:
          status = '未知'
          break;
      }
      return status
    },
    getApprovalList() {
      this.load_status = 'loading'
      this.$ajax.get('/admin/house/approveList/1', this.param, (res) => {
        if (res.statusCode == 200) {
          console.log(res.data, '列表数据---');
          this.approvalList = this.approvalList.concat(res.data.data)
          if (res.data.data.length < this.param.per_page) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'loadend'
          }
        } else {
          this.load_status = 'nomore'
        }
      })
    },
    searchConfirm(e) {
      if (!e.detail.value) return
      this.approvalList = []
      this.param.number = e.detail.value
      this.getApprovalList()
    },
    searchChange(e) {
      if (e.detail.value) {
        this.approvalList = []
        this.param.number = e.detail.value
        this.getApprovalList()
      } else {
        this.param.number = ''
        this.approvalList = []
        this.getApprovalList()
      }
    },
    confirmSeledDept(data) {
      if (data.value.length) {
        this.param.department_id = data.value.slice(-1)[0]
        this.deptSelectedLable = data.label.slice(-1)[0]
        this.approvalList = []
        this.getApprovalList()
      }
    },
    confirmSeledAdmin(data) {
      if (data.label.length) {
        this.param.user_name = data.label[0]
        this.adminSelectedLable = data.label[0]
        this.approvalList = []
        this.getApprovalList()
        this.dialogs.adminPicker = false
      }
    },
    copyCode(code) {
      uni.setClipboardData({
        data: code,
      });
    },
    toDetail(item) {
      this.$navigateTo(`/approval/detail?id=${item.id}`)
    }
  }
}
</script>
<style lang="scss" scoped>
page {
  background-color: #f6f6f6;
}

.icon {
  display: inline-block;
  width: 0;
  height: 0;
  margin: 4px 0 0 8px;
  border: 4px solid transparent;
  border-top-color: #b8b9ba;
}

.my-input {
  font-size: 28rpx;
  color: #292C3966;
}

.approval {
  .approval-search {
    position: sticky;
    top: 0;
    padding: 24rpx 32rpx;
    background-color: #fff;
    z-index: 9;
    border-bottom: 1px solid #f6f6f6;

    .search-box {
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 80rpx;
      background-color: #f6f6f6;
      border-radius: 16rpx;

      .search-type {
        display: flex;
        flex-direction: row;
        align-items: center;
        font-size: 28rpx;
        color: #292C39B2;
        margin-left: 30rpx;
      }

      .search-type::after {
        content: '';
        display: inline-block;
        width: 1px;
        height: 24rpx;
        margin: 0 20rpx;
        background-color: #292C39B2;
      }
    }

    .search-type-select {
      position: absolute;
      top: 100rpx;
      left: 40rpx;
      padding: 20rpx;
      border-radius: 3px;
      box-shadow: 0 1px 5px rgba($color: #000000, $alpha: 0.1);
      background-color: #fff;
      z-index: 9;

      text {
        color: #666;
        font-size: 28rpx;
        padding: 20rpx;
      }

      &>text:nth-child(1) {
        border-bottom: 1px solid #f6f6f6;
      }
    }
  }

  .approval-filter {
    width: 100%;
    margin: 30rpx 0;
    white-space: nowrap;
    padding-left: 32rpx;

    text {
      display: inline-block;
      text-align: center;
      font-size: 28rpx;
      padding: 16rpx 20rpx;
      color: #292C39B2;
    }

    .checked {
      border-radius: 4px;
      color: #488AF6;
      background: #488AF633;
    }
  }

  .approval-body {
    width: 100%;
    padding: 0 32rpx;
    margin-bottom: 120rpx;

    .approval-box-item {
      width: 100%;
      border-radius: 8px;
      margin-bottom: 30rpx;
      background-color: #fff;

      .item-head {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 82rpx;
        padding: 24rpx;
        font-size: 24rpx;
        color: #292C3966;
      }

      .item-body {
        padding-left: 24rpx;

        view {
          display: flex;
          flex-direction: row;

          &>text:nth-child(1) {
            color: #292C39B2;
          }

          &>text:nth-child(2) {
            color: #292C39;
          }
        }

        &>view:nth-child(-n + 2) {
          margin-bottom: 30rpx;
        }
      }

      .item-btm {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 112rpx;
        padding: 24rpx;

        view {
          display: flex;
          flex-direction: row;
          align-items: center;

          text {
            font-size: 24rpx;
            color: #292C3966;
          }

          &>text:nth-child(1) {
            width: 64rpx;
            height: 64rpx;
            border-radius: 50%;
            margin-right: 20rpx;
            text-align: center;
            line-height: 64rpx;
            font-size: 32rpx;
            color: #fff;
            background-color: #488AF6;
          }
        }

        .item-state {
          font-size: 24rpx;
          padding: 8rpx 20rpx;
          border-radius: 4px;
          color: #488AF6;
          background: #488AF633;
        }

        .item-state2 {
          font-size: 24rpx;
          padding: 8rpx 20rpx;
          border-radius: 4px;
          color: #13A834;
          background: #13A83433;
        }
      }
    }
  }

  .approval-filter-btm {
    position: fixed;
    bottom: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
    padding-bottom: 40rpx;
    border-top: 1px solid #f6f6f6;
    background-color: #fff;


    text {
      color: #292C39B2;
      font-size: 32rpx;
      padding: 18rpx 20rpx;
    }

    .filter-btm-checked {
      color: #488AF6;
    }
  }
}
</style>