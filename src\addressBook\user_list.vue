<template>
  <view class="user-list">
    <view class="list-item" v-for="(item, index) in userList" :key="index" @click="toDetail(item)">
      <view class="item-l">
        <text>{{ item.user_name[0] }}</text>
        <text>{{ item.user_name }}</text>
        <!-- <text v-if="item.roles[0].name == '站长'">创始人</text> -->
      </view>
      <view class="item-r">
        <text>{{ item.phone }}</text>
        <text :style="item.status == 0 ? 'color:#f65248;background: #F6524833' : ''">{{ item.status == 1 ? '启用' : '禁用'
          }}</text>
      </view>
    </view>
    <loadMore :status="load_status" />
    <view class="user-btm">
      <view class="btm-box">
        <view class="btm-box-item" v-for="(item, index) in regulate" :key="index" @click="toSetMember(index)">
          <image :src="item.url" />
          <text>{{ item.text }}</text>
        </view>
      </view>
    </view>
    <my-popup position="bottom" ref="show_edit_pop" :show="show_edit_pop">
      <view class="inherit">
        <uni-icons v-if="!load" style="position: absolute;top: 0;right: 0; padding: 30rpx;" type="closeempty"
          color="#d1d1d1" size="26" @click="show_edit_pop = false"></uni-icons>
        <text class="title">{{ inheritType == 1 ? '离职继承' : '在职继承' }}</text>
        <view class="inherit-item">
          <text>{{ inheritType == 1 ? '离职人员' : '在职人员' }}</text>
          <text v-if="inheritType == 1" @click="selectMember">{{ member1 ? member1 : '请选择离职人员' }}</text>
          <text v-if="inheritType == 2" @click="selectMember">{{ member1 ? member1 : '请选择在职人员' }}</text>
          <myIcon @click="selectMember" style="padding: 0 20rpx;" type="xiala" color="#808080" size="24rpx">
          </myIcon>
        </view>
        <view class="inherit-item">
          <text>继承人员</text>
          <text @click="selectMember2">{{ member2 ? member2 : '请选择继承人员' }}</text>
          <myIcon @click="selectMember2" style="padding: 0 20rpx;" type="xiala" color="#808080" size="24rpx">
          </myIcon>
        </view>
        <button v-if="!load" type="primary" @click='inherit'>确定继承</button>
        <view class="warn" v-else>
          <text>正在继承相关数据，请稍后...</text>
          <text>过程需要一些时间，请勿进行其它操作</text>
          <progress :percent="pgList[progress]" :duration="duration" active show-info activeColor="#10AEFF"
            stroke-width="6" />
        </view>
        <view v-if="progress == 2 && showStatus" class="status">
          <uni-icons type="checkbox" size="80" color="#10AEFF"></uni-icons>
          <text>数据继承完毕</text>
        </view>
      </view>
    </my-popup>
    <tMemberPicker :visible.sync="dialogs.adminPicker" v-model="admin_id" @confirm="confirmSeledAdmin"></tMemberPicker>
    <tMemberPicker :visible.sync="dialogs.adminPicker2" v-model="admin_id2" @confirm="confirmSeledAdmin2">
    </tMemberPicker>
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
import myPopup from '@/components/myPopup'
import loadMore from '@/components/loadMore'
import tMemberPicker from '@/components/tplus/tMemberPicker.vue';
export default {
  components: {
    myIcon,
    myPopup,
    tMemberPicker,
    loadMore
  },
  data() {
    return {
      load_status: 'loading',
      showStatus: false,
      duration: 1000,
      progress: 0,
      pgList: [0, 75, 100],
      load: false,
      inheritType: 1,//1离职 2在职
      admin_id: null,
      admin_id2: null,
      member1: '',
      member2: '',
      dialogs: {
        adminPicker: false,
        adminPicker2: false
      },
      inheritParam: {
        from_id: 3,
        to_id: 1681
      },
      show_edit_pop: false,
      param: {
        page: 1,
        per_page: 20,
        user_name: '',
        phone: '',
        department_id: 0,
      },
      isSearchbody: false,
      isSearchTypeBox: false,
      userList: [],
      regulate: [
        { url: 'https://img.tfcs.cn/tfyfxbb/tianjiachengyuan.png', text: '添加成员' },
        { url: 'https://img.tfcs.cn/tfyfxbb/jueseguanli.png', text: '角色管理' },
        { url: 'https://img.tfcs.cn/tfyfxbb/lizhijicheng.png', text: '离职继承' },
        { url: 'https://img.tfcs.cn/tfyfxbb/zaizhijicheng.png', text: '在职继承' }
      ]
    }
  },
  computed: {
    isAdmin() {
      let userInfo = JSON.parse(uni.getStorageSync('userInfo'))
      return userInfo.roles.some(item => item.name == '站长')
    },
    // isMe() {
    //   let userInfo = JSON.parse(uni.getStorageSync('userInfo'))
    //   return userInfo.id == this.userData.id
    // }
  },
  onLoad(options) {
    if (options.id) {
      this.param.department_id = Number(options.id)
    }
    if (options.title) {
      uni.setNavigationBarTitle({
        title: options.title
      })
    }
    uni.$on('refreshData', () => {
      this.param.page = 1
      this.userList = []
      this.getUserList()
    })
    this.getUserList()
  },
  onReachBottom() {
    if (this.load_status = 'nomore') return
    this.param.page++
    this.getUserList()
  },
  methods: {
    getUserList() {
      this.$ajax.get('/admin/personnelMatters/memberList', this.param, (res) => {
        if (res.statusCode == 200) {
          if (res.data.data.length < this.param.per_page) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'loadend'
          }
          this.userList = this.userList.concat(res.data.data)
        }
      })
    },
    // 离职继承
    setDimissionInherit() {
      this.$ajax.post('/admin/personnelMatters/inheritMember', this.inheritParam, (res) => {
        if (res.statusCode == 200) {
          this.duration = 30
          this.progress = 2
          setTimeout(() => {
            this.showStatus = true
            setTimeout(() => {
              this.admin_id = null
              this.admin_id2 = null
              this.member1 = ''
              this.member2 = ''
              this.showStatus = false
              this.load = false
            }, 1500)
          }, 3000)
        }
      })
    },
    // 在职继承
    setInherit() {
      this.$ajax.post('/admin/personnelMatters/inheritMemberOnJob', this.inheritParam, (res) => {
        if (res.statusCode == 200) {
          this.duration = 30
          this.progress = 2
          setTimeout(() => {
            this.showStatus = true
            setTimeout(() => {
              this.admin_id = null
              this.admin_id2 = null
              this.member1 = ''
              this.member2 = ''
              this.showStatus = false
              this.load = false
            }, 1500)
          }, 3000)
        }
      })
    },
    toDetail(item) {
      console.log(item, '用户信息');
      this.$navigateTo(`/addressBook/user_detail?phone=${item.phone}`)
    },
    toSetMember(index) {
      switch (index) {
        case 0:
          if (this.isAdmin) {
            this.$navigateTo(`/addressBook/add_member`)
          } else {
            uni.showToast({
              title: '暂无权限',
              icon: 'none'
            })
          }
          break;
        case 1:
          if (this.isAdmin) {
            this.$navigateTo(`/addressBook/role_management`)
          } else {
            uni.showToast({
              title: '暂无权限',
              icon: 'none'
            })
          }
          break;
        case 2:
          if (this.isAdmin) {
            this.show_edit_pop = true
            this.inheritType = 1
          } else {
            uni.showToast({
              title: '暂无权限',
              icon: 'none'
            })
          }
          break;
        case 3:
          if (this.isAdmin) {
            this.show_edit_pop = true
            this.inheritType = 2
          } else {
            uni.showToast({
              title: '暂无权限',
              icon: 'none'
            })
          }
          break;

        default:
          break;
      }
    },
    confirmSeledAdmin(data) {
      if (data.value.length) {
        this.member1 = data.label[0]
      }
      this.dialogs.adminPicker = false
    },
    confirmSeledAdmin2(data) {
      if (data.value.length) {
        this.member2 = data.label[0]
      }
      this.dialogs.adminPicker2 = false
    },
    selectMember() {
      this.dialogs.adminPicker2 = false
      this.dialogs.adminPicker = true
    },
    selectMember2() {
      this.dialogs.adminPicker = false
      this.dialogs.adminPicker2 = true
    },
    inherit() {
      if (!this.member1 || !this.member2) return
      this.inheritParam.from_id = this.admin_id
      this.inheritParam.to_id = this.admin_id2
      this.progress = 1
      this.load = true
      if (this.inheritType == 1) {
        this.setDimissionInherit()
      } else if (this.inheritType == 2) {
        this.setInherit()
      }
    }
  },
}
</script>
<style lang="scss" scoped>
.inherit {
  position: relative;
  padding: 32rpx;
  border-radius: 8px 8px 0 0;
  background-color: #ffffff;

  .title {
    width: 100%;
    text-align: center;
    font-size: 36rpx;
    font-weight: 500;
    color: #333;
    padding: 20rpx 0 22rpx;
  }

  .inherit-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 30rpx 20rpx;
    font-size: 32rpx;

    &>text:nth-child(1) {
      margin-right: 30rpx;
      color: #999;
    }

    &>text:nth-child(2) {
      color: #666;
    }
  }

  button {
    width: 100%;
    margin: 40rpx 0 20rpx;
    background-color: #488AF6;
    color: #fff;
  }

  .warn {
    width: 100%;
    margin: 40rpx 0 20rpx;

    text {
      margin-bottom: 20rpx;
    }

    &>text:nth-child(1) {
      font-size: 32rpx;
      font-weight: 500;
      color: #666;
    }

    &>text:nth-child(2) {
      font-size: 28rpx;
      font-weight: 400;
      color: #a1a1a1;
    }
  }

  .status {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 40rpx 0 20rpx;

    text {
      margin-top: 20rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #666;
    }
  }
}

.user-list {
  padding-bottom: 200rpx;

  .list-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    height: 128rpx;
    padding: 32rpx;

    .item-l {
      display: flex;
      flex-direction: row;
      align-items: center;

      &>text:nth-child(1) {
        width: 60rpx;
        height: 60rpx;
        margin-right: 15rpx;
        border-radius: 50%;
        font-size: 32rpx;
        line-height: 60rpx;
        text-align: center;
        color: #fff;
        background-color: #488AF6;
      }

      &>text:nth-child(2) {
        font-size: 32rpx;
        font-weight: 500;
        color: #292C39;
      }

      &>text:nth-child(3) {
        margin-left: 15rpx;
        font-size: 24rpx;
        padding: 6rpx 16rpx;
        color: #292C39;
        border-radius: 4px;
        color: #488AF6;
        background: #488AF633;
      }
    }

    .item-r {
      display: flex;
      flex-direction: row;
      align-items: center;

      &>text:nth-child(1) {
        margin-right: 15rpx;
        font-size: 32rpx;
        color: #292C3966;
      }

      &>text:nth-child(2) {
        border-radius: 4px;
        padding: 6rpx 16rpx;
        font-size: 24rpx;
        color: #13A834;
        background: #13A83433;
      }
    }
  }

  .user-btm {
    position: fixed;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #f6f6f6;

    .btm-box {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding: 0 32rpx;
      background-color: #fff;

      .btm-box-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20rpx 10rpx 40rpx;

        image {
          display: inline-block;
          width: 80rpx;
          height: 80rpx;
          margin-bottom: 20rpx;
        }

        text {
          font-size: 24rpx;
          color: #488AF6;
        }
      }
    }
  }
}
</style>