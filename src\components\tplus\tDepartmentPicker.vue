<template>
    <myPopup :show="show" @close="cancle">
        <view class="filter-wrapper">
            <view class="header">
                <view class="action cancle" @click.stop="cancle">取消</view>
                <view class="title"></view>
                <view class="action confirm" @click.stop="confirm">确认</view>
            </view>
            <view class="body">
                <tCustomPickerView v-model="selectedId" :multiple="multiple" :datas="list" ref="picker" v-if="show"
                    :filter-placeholder="switchTitle" filterable :map="{ label: 'name', value: 'id', children: 'subs' }"
                    :loading="loading"></tCustomPickerView>
            </view>
        </view>
    </myPopup>
</template>

<script>
import myPopup from '@/components/myPopup';
import tCustomPickerView from '@/components/tplus/tCustomPickerView';
export default {
    components: {
        myPopup, tCustomPickerView
    },
    props: {
        value: { type: [Number, String, Array], default: '' },
        visible: { type: Boolean, default: false },
        multiple: { type: Boolean, default: false },
        type: { type: Number, default: 1 }//1部门 2角色
    },
    data() {
        return {
            loading: false,
            show: false,
            isInited: false,
            curValue: '',
            list: []
        }
    },
    computed: {
        selectedId: {
            get() {
                return this.curValue || '';
            },
            set(val) {
                this.curValue = val;
            }
        },
        switchTitle() {
            return this.type == 1 ? '搜索部门' : '搜索角色'
        }
    },
    watch: {
        value: {
            handler(val) {
                this.selectedId = val
            },
            immediate: true
        },
        visible(val) {
            this.show = val;
            if (val && !this.isInited) {
                this.list = []
                this.isInited = true;
                if (this.type == 1) {
                    this.getList();
                } else if (this.type == 2) {
                    this.getRoleList()
                }
            }
        },
        show(val) {
            this.$emit('update:visible', val)
        }
    },
    created() {

    },
    methods: {
        // 获取部门列表
        getList() {
            this.loading = true;
            this.$ajax.get('/admin/personnelMatters/departments', {}, res => {
                this.loading = false;
                if (res.statusCode == 200) {
                    this.list = res.data || [];
                    console.log(this.list, '部门数据')
                }
            }, er => {
                console.log(er)
                this.loading = false;
            })
        },
        // 获取角色列表
        getRoleList() {
            this.$ajax.get('/admin/role/list', {}, (res) => {
                if (res.statusCode == 200) {
                    this.list = res.data || [];
                    console.log(this.list, '角色数据')
                    console.log(res);
                }
            })
        },
        cancle() {
            this.show = false;
            this.selectedId = this.value;
            console.log(this.selectedId);
        },
        confirm() {
            this.show = false;
            this.$emit('input', this.selectedId);
            this.$emit('confirm', this.$refs.picker.getCheckedItem());
        },
    }
}

</script>

<style scoped lang="scss"> .filter-wrapper {
     background-color: #fff;
     border-top-left-radius: 24rpx;
     border-top-right-radius: 24rpx;
     overflow: hidden;
 }

 .header {
     position: relative;
     display: flex;
     flex-direction: row;
     justify-content: space-between;
     align-items: center;
     height: 46px;

     .title {
         flex: 1;
         color: #999;
         text-align: center;
         display: inline-block;
         max-width: 50%;
         overflow: hidden;
         white-space: nowrap;
         text-overflow: ellipsis;
     }

     .action {
         padding: 10px 16px;
         font-size: 17px;

         &.cancle {
             color: #888;
         }

         &.confirm {
             color: #007aff;
         }
     }
 }
</style>