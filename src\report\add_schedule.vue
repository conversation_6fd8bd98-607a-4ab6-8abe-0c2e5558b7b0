<template>
  <view class="list">
    <view class="row title " @click="pickerStatus = true" v-if="is_visit">
      <view class="label">
        客户状态：
      </view>
      <view class="ctn" style="color:#0174ff">
        {{ status_name }}
      </view>
    </view>
    <text class="label" v-if="!is_visit">跟进内容：</text>
    <textarea
      v-if="!is_visit"
      placeholder="请填写跟进内容"
      v-model="form_add.description"
    ></textarea>
    <text class="label">资料图片：</text>
    <view class="add_img"
      ><upload
        :time_watermark="1"
        :imageList="is_visit ? visit_form_add.files : form_add.files"
        :upload_category="9"
        :is_visit="is_visit"
      ></upload
    ></view>
    <view class="btn" @click="onClick">提交</view>
    <!-- 弹出选择框 -->
    <VuePicker
      :data="check_list"
      title="选择客户状态"
      cancelText="取消"
      confirmText="确认"
      :showToolbar="true"
      @confirm="confirmStatus"
      :visible.sync="pickerStatus"
    />
    <Mpop
      ref="pop"
      direction="center"
      :is_close="true"
      :is_mask="true"
      :width="90"
    >
      <textarea
        v-model="visit_form_add.cancel_reason"
        cols="30"
        rows="10"
        placeholder="请输入无效原因"
      ></textarea>
    </Mpop>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import Mpop from "@/components/ming-pop";
import myIcon from "@/components/my-icon";
import upload from "@/components/upload";
import VuePicker from "vue-pickers";
export default {
  components: { myIcon, upload, VuePicker, Mpop },
  data() {
    return {
      form_add: {
        customer_reported_id: "",
        description: "",
        files: [],
      },
      is_visit: false,
      visit_form_add: {
        customer_reported_id: "",
        status: "",
        files: [],
      },
      check_list: [
        [
          { value: "2", label: "已到访" },
          { value: "3", label: "已认筹" },
          { value: "4", label: "已认购" },
          { value: "5", label: "已成交" },
          { value: "10", label: "已无效" },
        ],
      ],
      pickerStatus: false,
      status_name: "",
    };
  },
  onLoad(options) {
    if (options.id) {
      this.form_add.customer_reported_id = options.id;
      this.visit_form_add.customer_reported_id = options.id;
    }
    if (options.is_visit) {
      this.is_visit = true;
      this.visit_form_add.status = options.customer_status;
      this.check_list[0].map((item) => {
        if (options.customer_status == item.value) {
          this.status_name = item.label;
        }
      });
    }
  },
  methods: {
    onClick() {
      if (!this.form_add.description && !this.is_visit) {
        uni.showToast({
          title: "提交内容不能为空",
          icon: "none",
        });
        return;
      }
      if (!this.is_visit) {
        this.$ajax.post(
          "/client/customer/reported/follow_up/create",
          this.form_add,
          (res) => {
            if (res.statusCode === 200) {
              uni.showToast({
                title: "提交成功",
                success: () => {
                  setTimeout(() => {
                    this.$navigateTo(
                      `/report/report_detail?id=${this.form_add.customer_reported_id}&data_type=2`
                    );
                  }, 1000);
                },
              });
            } else {
              uni.showToast({
                title: res.data.message || "提交失败",
                icon: "none",
              });
            }
          }
        );
      } else {
        if (this.visit_form_add.files.length < 1) {
          uni.showToast({
            title: "必须上传资料图片",
            icon: "none",
          });
          return;
        }
        this.$ajax.post(
          "/client/customer/reported/audit/status/v3",
          this.visit_form_add,
          (res) => {
            if (res.statusCode === 200) {
              uni.showModal({
                title: "跳转提示",
                content: "上传成功！是否继续添加跟进记录？",
                success: (res) => {
                  if (res.confirm) {
                    this.$navigateTo(
                      `/report/add_schedule?id=${this.visit_form_add.customer_reported_id}`
                    );
                  } else if (res.cancel) {
                    this.$navigateTo("/project_broker/all_customers");
                  }
                },
              });
            } else {
              uni.showToast({
                title: res.data.message || "上传失败",
                icon: "none",
              });
            }
          }
        );
      }
    },
    confirmStatus(res) {
      res.map((item) => {
        this.visit_form_add.status = item.value;
        if (item.value == 10) {
          this.$refs.pop.show();
        }
        this.status_name = item.label;
      });
    },
  },
};
</script>

<style scoped lang="scss">
.list {
  padding: 24rpx 48rpx;
  textarea {
    width: 100%;
    height: 400rpx;
    border-radius: 10rpx;
    background: #f3f2f7;
    padding: 24rpx;
    margin-top: 30rpx;
  }
  text {
    margin: 30rpx 0;
    font-size: 32rpx;
  }
  .add_img {
  }
  .btn {
    margin-top: 40rpx;
    width: 100%;
    border-radius: 10rpx;
    padding: 30rpx;
    align-items: center;
    color: #fff;
    font-size: 32rpx;
    height: 100rpx;
    background: #708efc;
  }
  .title {
    padding: 30rpx 0;
    margin-bottom: 10rpx;
    font-size: 32rpx;
    align-items: center;
    color: #333;
  }
}
.label {
  color: #999;
  margin-right: 30rpx;
}
</style>
