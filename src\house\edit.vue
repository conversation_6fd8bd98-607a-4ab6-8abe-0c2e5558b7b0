<template>
  <view class="add-container">
    <view class="step_block">
      <view class="form mb0">
        <!-- 小区 -->
        <template v-if="form.auth_view_vip">
          <view class="form_item bottom-line">
            <view class="left flex-row">
              <view class="label">小区名称</view>
              <view class="input flex-1" :class="{ has_value: form.community_id }">{{
                community_name || form.community_id || '请检索并选择小区'
              }}</view>
              <icons type="jinrujiantou" size="32" color="#c0c0c0"></icons>
            </view>
          </view>
          <!--  @click="toSelectCommunity" -->
          <template v-if="form.community_id">
            <!-- 楼栋-->
            <template v-if="isCheckCommutity == 1">
              <view class="form_item bottom-line">
                <view class="left flex-row">
                  <view class="label">楼栋</view>
                  <view
                    class="input flex-1 flex-row space-between"
                    :class="{ has_value: current_loudong }"
                    @click="selectLoudong"
                  >
                    <text>{{ current_loudong || '请选择楼栋' }}</text>
                  </view>
                  <icons type="jinrujiantou" size="32" color="#c0c0c0"></icons>
                </view>
              </view>
              <view
                class="form_item bottom-line"
                v-if="form.danyuan || !show_unit || form.building_danyuan_id == 0"
              >
                <view class="left flex-row">
                  <view class="label">总楼层 </view>
                  <my-input
                    v-model="form.total_floor"
                    size="big"
                    class="flex-1"
                    placeholder="请输入总楼层"
                    unit="层"
                  ></my-input>
                </view>
              </view>
              <view class="form_item bottom-line" v-if="!form.building_unit_id">
                <view class="left flex-row">
                  <view class="label">所在楼层</view>
                  <my-input
                    v-model="form.sz_floor"
                    size="big"
                    class="flex-1"
                    placeholder="请输入所在楼层"
                    unit="层"
                  ></my-input>
                </view>
              </view>
            </template>
            <template v-else>
              <view class="form_item bottom-line">
                <view class="left flex-row">
                  <view class="label">楼栋</view>
                  <my-input
                    v-model="form.loudong"
                    size="big"
                    class="flex-1"
                    placeholder="请输入楼栋"
                    unit="号楼"
                  ></my-input>
                </view>
              </view>
              <view class="form_item bottom-line">
                <view class="left flex-row">
                  <view class="label">单元</view>
                  <my-input
                    v-model="form.danyuan"
                    size="big"
                    class="flex-1"
                    placeholder="请输入单元"
                    unit="单元"
                  ></my-input>
                </view>
              </view>
              <view class="form_item bottom-line">
                <view class="left flex-row">
                  <view class="label">房号</view>
                  <my-input
                    v-model="form.fanghao"
                    size="big"
                    class="flex-1"
                    placeholder="请输入房号"
                    unit="室"
                  ></my-input>
                </view>
              </view>

              <view class="form_item bottom-line" v-if="!form.building_danyuan_id">
                <view class="left flex-row">
                  <view class="label">总楼层</view>
                  <my-input
                    v-model="form.total_floor"
                    size="big"
                    class="flex-1"
                    placeholder="请输入总楼层"
                    unit="层"
                  ></my-input>
                </view>
              </view>
              <view class="form_item bottom-line" v-if="!form.building_unit_id">
                <view class="left flex-row">
                  <view class="label">所在楼层</view>
                  <my-input
                    v-model="form.sz_floor"
                    size="big"
                    class="flex-1"
                    placeholder="请输入所在楼层"
                    unit="层"
                  ></my-input>
                </view>
              </view>
            </template>
          </template>
          <view class="showSingle" v-if="isOpenShowingSingle && form.community_id">
            单边代理已开启，仅同店和平台巡检可查看
          </view>
        </template>
        <view class="form_item bottom-line">
          <view class="left flex-row">
            <view class="label">朝向装修 </view>
            <my-picker
              ref="shangquan"
              class="flex-1"
              :options="zhuangxiu_range"
              @change="onZhuangxiuChange"
            ></my-picker>
          </view>
          <icons type="jinrujiantou" size="32" color="#c0c0c0"></icons>
        </view>

        <!-- 户型 -->
        <view class="form_item bottom-line" v-if="huxing_range.length">
          <view class="left flex-row">
            <view class="label">户型</view>
            <my-popup-customer
              class="flex-1"
              :multiple="true"
              title="请选择户型"
              ref="huxing"
              :option="huxing_range"
              @change="onHuxingChange"
            ></my-popup-customer>
            <icons type="jinrujiantou" size="32" color="#c0c0c0"></icons>
          </view>
        </view>

        <!-- <view class="form_item bottom-line">
          <view class="left flex-row">
            <view class="label">钥匙 </view>
            <view class="level_list flex-row items-center justify-between flex-1">
              <view
                class="level_item flex-1"
                :class="{ active: form.has_key == item.value }"
                v-for="item in [
                  { value: 0, name: '无钥匙' },
                  { value: 1, name: '有钥匙' },
                ]"
                :key="item.value"
                @click="selectLevel(item, 'has_key', 'value')"
              >
                <view class="level_item_name">
                  {{ item.name }}
                </view>
              </view>
            </view>
          </view>
          <icons type="jinrujiantou" size="32" color="#c0c0c0"></icons>
        </view> -->
        <view class="form_item bottom-line" v-if="form.trade_type === 1 || form.trade_type === 3">
          <view class="left flex-row">
            <view class="label">房屋售价</view>
            <my-input
              v-model="form.sale_price"
              size="big"
              class="flex-1"
              placeholder="请输入"
              unit="万元"
            ></my-input>
          </view>
        </view>
        <view class="form_item bottom-line" v-if="form.trade_type === 2 || form.trade_type === 3">
          <view class="left flex-row">
            <view class="label">出租价格</view>
            <my-input
              v-model="form.rent_price"
              size="big"
              class="flex-1"
              placeholder="请输入"
              unit="元/月"
            ></my-input>
          </view>
        </view>
        <!-- 面积 -->
        <view class="form_item bottom-line">
          <view class="left flex-row">
            <view class="label">建筑面积</view>
            <my-input
              placeholder="请输入建筑面积"
              v-model="form.mianji"
              size="big"
              class="flex-1"
              unit="m²"
            ></my-input>
          </view>
        </view>
        <!-- <view class="form_item bottom-line">
          <view class="left flex-row">
            <view class="label">交易状态</view>
            <my-select
              placeholder="请选择交易状态"
              :range="form_options.tradeStatus"
              value_name="values"
              :show_icon="false"
              size="big"
              class="flex-1"
              v-model="form.trade_status"
            ></my-select>
          </view>
          <icons type="jinrujiantou" size="32" color="#c0c0c0"></icons>
        </view> -->
      </view>
      <view class="form">
        <view class="form_item bottom-line">
          <view class="left flex-row" @click="showFormTel">
            <view class="label">业主手机号</view>
            <view class="owner flex-1">{{
              form.tel.length > 0 ? form.tel[0].owner + form.tel[0].owner_tel : '请添加'
            }}</view>
          </view>
        </view>
        <view class="showSingle" v-if="isOpenShowingSingle">
          单边代理已开启，仅同店和平台巡检可查看
        </view>
      </view>

      <view class="form mb0" v-if="form.privacy_status == 1">
        <view class="form_item">
          <view class="left flex-row">
            <view class="label">隐私设置</view>
            <view class="level_list flex-row items-center justify-between flex-1">
              <view
                class="level_item flex-1"
                :class="{ active: form.privacy_type == item.values }"
                v-for="item in privacyList"
                :key="item.values"
                @click="selectLevel(item, 'privacy_type', 'values')"
              >
                <view class="level_item_name">
                  {{ item.name }}
                </view>
              </view>
            </view>
          </view>
        </view>
        <view
          class="showSingle"
          style="line-height: 1.3"
          v-show="form.privacy_type == 2 && form.privacy_status == 1"
        >
          <view> 1,房源业主电话将隐藏不显示 </view>
          <view> 2,只能联系房源维护人 </view>
          <view> 3,当前成员设置上限为{{ form.num_privacy }}条 </view>
        </view>
      </view>
    </view>
    <view class="btn_box">
      <my-button
        block
        type="primary"
        size="big"
        :round="false"
        :loading="subming"
        @click="handleSubmit"
        >提交</my-button
      >
    </view>
    <my-popup ref="selectLou">
      <view class="popup_box">
        <view class="back_pop flex-row" v-if="loudong_level > 1" @click="backPre">返回上级</view>
        <view class="popup_title items-center"
          >请选择{{ loudong_level == 1 ? '楼栋' : loudong_level == 2 ? '单元' : '房号' }}
        </view>
        <view class="hide_pop flex-row" @click="hidePop">取消</view>
        <view class="popup_tips flex-row space-between">
          <text class="popup_tips_left">您也可以直接选择</text>
          <text class="popup_tips_right" @click="toWrite">没有找到？试试手动输入</text>
        </view>
        <view class="popup_list">
          <template v-if="loudong_level == 1">
            <template v-if="!customer_write">
              <view
                class="popup_list_item flex-row space-between"
                v-for="(item, index) in loudong_list"
                :key="index"
                @click="onSelectLoudong(item.value)"
              >
                <text class="flex-1 popup_list_item_title">{{ item.name }}</text>
                <icons type="jinrujiantou" size="32" color="#c0c0c0"></icons>
              </view>
            </template>
            <template v-else>
              <view class="customer_write form">
                <view class="form_item">
                  <view class="left flex-row">
                    <view class="label"> 楼栋 </view>
                    <my-input
                      v-model="form.loudong"
                      size="big"
                      class="flex-1"
                      placeholder="请输入楼栋"
                      unit="号楼"
                    ></my-input>
                  </view>
                </view>
                <view class="opt_btns flex-row align-center justify-center">
                  <view class="customer_sub" @click="customConfirm">确定</view>
                  <view class="customer_sub cancel" @click="customer_write = false"> 取消</view>
                </view>
              </view>
            </template>
          </template>
          <template v-if="loudong_level == 2">
            <template v-if="!customer_write">
              <view
                class="popup_list_item flex-row space-between"
                v-for="(item, index) in danyuan_list"
                :key="index"
                @click="onSelectDanyuan(item.value)"
              >
                <text class="flex-1 popup_list_item_title">{{ item.name }}</text>
                <icons type="jinrujiantou" size="32" color="#c0c0c0"></icons>
              </view>
            </template>
            <template v-else>
              <view class="customer_write form">
                <view class="form_item">
                  <view class="left flex-row">
                    <view class="label">单元 </view>
                    <my-input
                      v-model="form.danyuan"
                      size="big"
                      class="flex-1"
                      placeholder="请输入单元"
                      unit="单元"
                    ></my-input>
                  </view>
                </view>
                <view class="opt_btns flex-row items-center justify-center">
                  <view class="customer_sub" @click="customConfirm">确定</view>
                  <view
                    class="customer_sub cancel"
                    v-if="!form.loudong"
                    @click="customer_write = false"
                  >
                    取消</view
                  >
                </view>
              </view>
            </template>
          </template>
          <template v-if="loudong_level == 3">
            <template v-if="!customer_write">
              <view
                class="popup_list_item flex-row space-between"
                v-for="(item, index) in fanghao_list"
                :key="index"
                @click="onSelectFanghao(item.value)"
              >
                <text class="flex-1 popup_list_item_title">{{ item.name }}</text>
                <icons type="jinrujiantou" size="32" color="#c0c0c0"></icons>
              </view>
            </template>
            <template v-else>
              <view class="customer_write form">
                <view class="form_item">
                  <view class="left flex-row">
                    <view class="label">房号</view>
                    <my-input
                      v-model="form.fanghao"
                      size="big"
                      class="flex-1"
                      placeholder="请输入房号"
                    ></my-input>
                  </view>
                </view>
                <!-- unit="号" -->
                <view class="opt_btns flex-row items-center justify-center">
                  <view class="customer_sub" @click="customConfirm">确定</view>
                  <view class="customer_sub cancel" v-if="!form.danyuan" @click="customCancel">
                    取消</view
                  >
                </view>
              </view>
            </template>
          </template>
        </view>
      </view>
    </my-popup>
    <my-popup ref="selectLou" :show="showLouDong">
      <view class="popup_box">
        <view class="back_pop flex-row" v-if="loudong_level > 1" @click="backPre">返回上级</view>
        <view class="popup_title items-center"
          >请选择{{ loudong_level == 1 ? '楼栋' : loudong_level == 2 ? '单元' : '房号' }}
        </view>
        <view class="hide_pop flex-row" @click="hidePop">取消</view>
        <view class="search_box flex-row items-center">
          <view class="search flex-1">
            <search @input="searchList" v-model="searchKey"></search>
          </view>
          <view class="confirm" @click="confirmSearch"> 确定 </view>
        </view>
        <scroll-view scroll-y class="popup_list">
          <view
            class="popup_list_item"
            v-for="item in houseList"
            :key="item.id"
            @click="selectHouse(item)"
          >
            {{ item.name }}
          </view>
        </scroll-view>
      </view>
    </my-popup>
    <my-popup ref="success_add" position="center" :show="showSuccess">
      <view class="popup_box success_add">
        <view class="popup_box_img">
          <image src="/static/img/<EMAIL>"> </image>
        </view>
        <view class="popup_box_text">恭喜您上架成功 获得查看卡奖励</view>
        <view class="btns">
          <myButton size="big" :round="false" @click="iKnow" block>确定</myButton>
        </view>
      </view>
    </my-popup>
    <my-popup ref="showFormTel" position="bottom" :show="formTelShow" :touch_mask_close="true">
      <view class="form_tel">
        <view class="form_tel_title flex-row justify-center items-center">
          <view class="title_name"> 业主电话 </view>
          <view class="submit" @click="confirmOwner"> 确定 </view>
        </view>
        <view class="form_owner_info" v-for="(item, index) in ownerList" :key="item.id">
          <view class="owner flex-row align-center">
            <view class="flex-1">
              <input
                type="text"
                placeholder="业主姓名"
                :disabled="item.id ? true : false"
                v-model="item.owner"
              />
            </view>
            <view style="width: 100rpx; margin-right: 20rpx">
              <my-select
                placeholder="请选择性别"
                :range="[
                  {
                    values: 1,
                    name: '先生',
                  },
                  {
                    values: 2,
                    name: '女士',
                  },
                ]"
                value_name="values"
                :show_icon="true"
                size="small"
                v-model="item.sex"
              ></my-select>
            </view>

            <view
              class="icon"
              style="width: 40rpx"
              @click="addOwnerTel"
              v-if="index == 0 && ownerList.length < 3"
            >
              <icons type="a-tianjia1x" color="#999" size="32rpx"></icons>
            </view>
            <view
              class="icon"
              style="width: 40rpx"
              @click="addOwnerTel"
              v-if="index == 0 && ownerList.length == 3"
            >
            </view>
            <view
              class="icon"
              style="width: 40rpx"
              @click="removeOwner(item, index)"
              v-if="index > 0"
            >
              <icons type="yichu" color="red" size="32rpx"></icons>
            </view>
          </view>
          <view class="owner_tel owner flex-row align-center">
            <view class="flex-1">
              <input
                type="text"
                placeholder="业主电话"
                :disabled="item.id ? true : false"
                v-model="item.owner_tel"
              />
            </view>
            <view style="width: 100rpx; margin-right: 20rpx">
              <my-select
                placeholder="请选择关系"
                :range="typeList"
                value_name="values"
                :show_icon="true"
                size="small"
                class="flex-1"
                v-model="item.type"
              ></my-select>
            </view>
            <view class="icon" style="width: 40rpx"> </view>
          </view>
        </view>
        <!-- <view
          class="add_other flex-row items-center"
          v-if="ownerList.length < 3"
          @click="addOwnerTel"
        >
          <view class="icon">
            <icons type="a-tianjia1x" color="#999" size="22rpx"></icons>
          </view>
          <view class="add_owner"> 添加其他业主 </view>
        </view> -->
      </view>
    </my-popup>
    <myPopup :show="show_sub_loudong" position="center">
      <view class="show_sub_loudong">
        <view class="serch_info flex-row items-center space-between">
          <view class="search">
            <view class="search_title"> 楼栋 </view>
            <view class="search_inp">
              <input
                type="text"
                v-model="current_l.loudong"
                placeholder="输入楼栋"
                placeholder-class="pls"
                @input="handlerInput($event, 'loudong')"
              />
            </view>
          </view>
          <view class="search">
            <view class="search_title"> 单元 </view>
            <view class="search_inp">
              <input
                type="text"
                v-model="current_l.danyuan"
                placeholder="输入单元"
                placeholder-class="pls"
                @input="handlerInput($event, 'danyuan')"
              />
            </view>
          </view>
          <view class="search">
            <view class="search_title"> 房号 </view>
            <view class="search_inp">
              <input
                type="text"
                v-model="current_l.fanghao"
                placeholder="输入房号"
                placeholder-class="pls"
                @input="handlerInput($event, 'fanghao')"
              />
            </view>
          </view>
        </view>
        <view class="flex-row btn_group items-center justify-center">
          <view class="btn" @click="cancel">取消</view>
          <view class="btn bg_highlight" @click="submitLoudongData">确定</view>
        </view>
      </view>
    </myPopup>
  </view>
</template>

<script>
// import upload from './upload.vue'
import myInput from './components/myInput'
import mySelect from './components/mySelect'
import myPopupCustomer from './components/my-popup-customer'
import myPopup from './components/myPopup'
import search from './components/search'
import myPicker from './components/myPicker'
import icons from '@/components/my-icon'
import myButton from './components/myButton'
export default {
  name: 'edit',
  components: {
    myInput,
    mySelect,
    myPopupCustomer,
    myPopup,
    search,
    myPicker,
    icons,
    myButton,
    // upload,
  },
  data () {
    return {
      form_options: {
        tradeType: [],
      },
      huxing_range: [
        {
          label: '户型',
          value: [],
          required: false,
          items: [
            {
              title: '室',
              identifier: 'shi',
              range: [],
            },
            {
              title: '厅',
              identifier: 'ting',
              range: [],
            },
            {
              title: '卫',
              identifier: 'wei',
              range: [],
            },
            {
              title: '阳台',
              identifier: 'balcony',
              range: [],
            },
            {
              title: '厨房',
              identifier: 'kitchen',
              range: [],
            },
          ],
        },
      ],

      zhuangxiu_range: [
        {
          label: '朝向',
          value: [],
          required: true,
          items: [
            {
              title: '',
              identifier: 'chaoxiang',
              range: [],
            },
          ],
        },
        {
          label: '装修',
          value: [],
          required: true,
          items: [
            {
              title: '',
              identifier: 'zhuangxiu',
              range: [],
            },
          ],
        },
      ],
      loudong_list: [],
      danyuan_list: [],
      fanghao_list: [],
      loudong_units: [],
      danyuan_units: [],
      fanghao_units: [],
      loudongIndex: 4,
      danyuanIndex: 4,
      // loudongName: '号楼',
      // danyuanName: '单元',
      form: {
        trade_type: 1,
        community_id: '',
        building_unit_id: '',
        shi: '',
        ting: '',
        wei: '',
        mianji: '',
        zhuangxiu: 2,
        chaoxiang: 2,
        has_key: 0,
        tel: [],
        id: '',
        trade_status: '',
        total_floor: '',
        fanghao: '',
        loudong: "",
        danyuan: "",
        privacy_type: '',
      },
      commission_type: [3],
      community_name: '',
      subming: false,
      agree_agreement: '0',
      showLouDong: false,
      loudong_level: 1, //当前选中的是 楼栋 1 还是 单元 2 还是 房号 3
      showDanyuanDanwei: false,
      showLoudongDanwei: false,
      showfanghaoDanwei: false,
      loudongdanwei: '',
      danyuandanwei: '',
      loudongName: '',
      danyuanName: '',
      fanghaoName: '',
      apply_form: {
        fanghao: '',
        danyuan: '',
        loudong: '',
        loudongdanwei: '',
        danyuandanwei: '',
      },
      levelArr: ['A', 'B', 'C'],
      show_copy_info: false,
      copy_content: '',
      showSuccess: false,
      isCheckCommutity: 1, //是否开启房源唯一验证 1 开启 0 不开启 如果不开启的话楼栋单元房号手动输入 传参需要传递 loudong danyuan fanghao 字段 删除building_unit_id 字段   如果开启房源唯一验证 按照现有逻辑走  选择楼栋 单元 房号传参传building_unit_id  删除loudong danyuan fanghao 字段
      showRegion: false,
      levelIndex: 0,
      levelList: [
        {
          name: 'A级',
          tip: '重点关注',
          id: 'A',
        },
        {
          name: 'B级',
          tip: '日常维护',
          id: 'B',
        },
        {
          name: 'C级',
          tip: '仅作记录',
          id: 'C',
        },
      ],
      formTelShow: false,
      imgList: [],
      upload_api: '/uploadHousePhoto',
      maxCount: 20,
      img_size: 102400,
      ownerList: [],
      show_sub_loudong: false,
      current_l: {
        loudong: '',
        danyuan: '',
        fanghao: '',
      },
      loudongRoomName: '',
      delArr: [],
      delTelArr: [],
      ownerList: [
        {
          owner: '',
          owner_tel: '',
          sex: 1,
          type: 1
        },
      ],
      show_sub_loudong: false,
      current_l: {
        loudong: '',
        danyuan: '',
        fanghao: '',
      },
      customer_write: false,
      loudongRoomName: '',
      is_open_company: 0,
      typeList: [],
      current_loudong_name: "",
      current_danyuan_name: '',
      show_unit: true,
      houseList: [],
      searchKey: '',
      isOpenShowingSingle: false,
      privacyList: [
        {
          name: '同店可查看',
          values: 1
        },
        {
          name: '联系维护人',
          values: 2
        },
      ]
    }
  },
  watch: {
    'form.community_id': {
      handler (val, old_value) {
        // 小区有改变需要清空楼栋单元的值

        if (old_value) {
          this.form.loudong = ''
          this.form.danyuan = ''
          this.form.fanghao = ''
          this.form.sz_floor = ''
          this.form.total_floor = ''
          this.current_loudong = ''
          this.form.building_unit_id = ''
          this.form.region_id = ''
          this.loudongIndex = 4
          this.danyuanIndex = 4
          this.loudongName = ''
          this.danyuanName = ''
          this.shangquan_range[0].value = []
        }
        this.apply_form.community_id = val
        // setTimeout(() => {
        //   if (!this.apply_form.community_id) {
        //     this.$Router.replace({
        //       name: 'add_private',
        //     })
        //   }
        // }, 500)
        // 获取小区的楼栋
        // this.isCheckCommutity = 1
        if (this.isCheckCommutity == 1) {
          this.getLoudong(val)
        }
      },
      // immediate: true,
    },
    // lookDay(val) {
    //   this.form.showing_stime = `${val} ${this.startTime}`
    //   this.form.showing_etime = `${val} ${this.endTime}`
    // },
  },

  onLoad (options) {
    if (options.id) {
      this.form.id = options.id
    }
    this.getHouseDetail()
    this.getOptions()
    this.getTelTypeList()
    this.getOPenShowSingle()
    // this.getHouseUnits()
    // // #ifdef  H5-WEIXIN
    // this.upload_api = '/wxUploadHousePhoto'
    // this.getWxConfig(['chooseImage', 'uploadImage', 'getLocalImgData'], (wx) => {
    //   this.wx = wx
    // })
    // //  #endif
    uni.$on('upload', (imgs) => {
      this.imgList = imgs || []
    })
    uni.$on('setImg', (res) => {
      let { item } = res
      let index = this.imgList.findIndex((img) => img.url == item.url)
      if (item.is_del) {
        this.delArr.push(item)
        this.imgList.splice(index, 1)
      } else {
        this.$set(this.imgList, this.imgList[index], item)
      }
    })

    uni.$on('selectcommunity', (res) => {
      this.form.community_id = res.id
      this.community_name = res.title
      this.isCheckCommutity = res.is_verify
      // 如果小区没有商圈 添加商圈
      if (!res.region_id) {
        this.showRegion = true
      } else {
        this.showRegion = false
      }
      // 如果时申请添加的小区则发布房源status需要传2需要
      if (res.is_apply || res.manual) {
        this.form.status = 2
        this.form.title = res.title
      } else {
        this.form.status = ''
        this.form.title = ''
      }
    })
  },
  computed: {
    current_loudong: {
      get () {
        if (this.loudongName || this.danyuanName || this.fanghaoName) {
          return this.loudongName + ' ' + this.danyuanName + ' ' + this.fanghaoName
        } else {
          return ''
        }
      },
      set () {
        if (this.loudongName || this.danyuanName || this.fanghaoName) {
          return this.loudongName + ' ' + this.danyuanName + ' ' + this.fanghaoName
        } else {
          return ''
        }
      }

    },
  },
  onUnload () {
    uni.$off('selectcommunity')
    uni.$off('setImg')
  },

  methods: {
    // ...mapActions(['getUserInfo']),
    getHouseDetail () {
      this.$ajax
        .get(`/admin/house/privateHousesDetailByEdit/${this.form.id}`, {}, (res) => {
          uni.hideLoading()
          if (res.statusCode === 200) {
            this.oldForm = res.data
            // if (this.oldForm.is_share == 1 || this.oldForm.is_verify == 1) {
            // this.getLoudong(this.oldForm.community_id)
            // }
            this.creatHuxing()
            // this.form = res.data.data
          } else {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
            })
          }
        }, err => {
          uni.hideLoading()
        })

    },
    getOPenShowSingle () {
      this.$ajax.get("/admin/house/unilateralAgent", {}, (res) => {
        if (res.statusCode === 200) {
          this.isOpenShowingSingle = res.data.status
        }
      })
    },
    getOptions () {
      uni.showLoading({
        title: '加载中',
        mask: true,
      })
      this.$ajax
        .get('/admin/house/houseSelectFieldsByLm', {}, (res) => {
          uni.hideLoading()
          if (res.statusCode === 200) {
            this.form_options = res.data
            this.tradeStatus = Array.from(this.form_options.tradeStatus)


            // this.shangquan_range[0].items[0].range = this.form_options.area_region.map((item) => ({
            //   value: item.values,
            //   name: item.name,
            // }))
            // 朝向
            this.zhuangxiu_range[0].items[0].range = this.form_options.direction.map((item) => ({
              value: item.values,
              name: item.name,
            }))
            // 装修
            this.zhuangxiu_range[1].items[0].range = this.form_options.decoration.map((item) => ({
              value: item.values,
              name: item.name,
            }))
          } else {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
            })
          }
        }, () => {
          uni.hideLoading()
        })

    },
    onShangquanChange (e) {
      e.forEach((item) => {
        item.forEach((el) => {
          this.form[el.identifier] = el.value
        })
      })
    },
    onZhuangxiuChange (e) {
      e.forEach((item) => {
        item.forEach((el) => {
          this.form[el.identifier] = el.value
        })
      })
    },
    getLoudong (community_id) {
      this.$ajax.get('/admin/house/searchBuilding', { community_id }, (res) => {
        if (res.statusCode === 200) {
          this.loudong_list = [...res.data]
          this.houseList = [...res.data]
        } else {
          this.loudong_list = []
        }
      })
    },
    onSelectLoudong (loudong_value) {
      this.form.loudong = ''
      this.form.danyuan = ''
      this.form.building_danyuan_id = ''
      this.form.building_unit_id = ''
      var _current = this.loudong_list.find((item) => item.value == loudong_value)
      if (_current) {
        // this.form.loudongdanwei = _current.loudongdanwei
        this.current_loudong_id = _current.loudong
        this.form.building_loudong_id = _current.loudong
        this.current_loudong_name = _current.value
        this.form.loudong = _current.value
        this.loudongName = _current.name
        if (_current.is_unit) {
          this.show_unit = true
          this.getUnit(_current.loudong)
          this.loudong_level = 2
        } else {
          this.show_unit = false
          this.loudong_level = 3
          this.getHouseNum()
        }

        // this.showLouDong = false
      }
    },
    getUnit (loudong_id) {
      this.$ajax.get(
        '/admin/house/searchUnit',
        { community_id: this.form.community_id, loudong: loudong_id },
        (res) => {
          if (res.statusCode === 200) {
            this.danyuan_list = [
              ...res.data,
              // { name: '手动填写单元', danyuan: '', value: '', danyuandanwei: '' },
            ]
            this.houseList = [
              ...res.data,
            ]
          } else {
            // this.danyuan_list = [{ name: '手动填写单元', danyuan: '', value: '', danyuandanwei: '' }]
          }
        }
      )
    },
    onSelectDanyuan (danyuan_value) {
      this.form.fanghao = ''
      this.form.danyuan = ''
      this.fanghaoName = ''
      this.form.building_unit_id = ''
      var _current = this.danyuan_list.find((item) => item.value === danyuan_value)
      if (_current) {
        // this.form.danyuandanwei = _current.danyuandanwei
        this.form.danyuan = _current.value
        this.form.building_danyuan_id = _current.danyuan
        this.form.total_floor = _current.sz_floor
        this.current_danyuan_name = _current.value
        this.danyuanName = _current.name
        this.getHouseNum(_current.danyuan)
        this.loudong_level = 3
        // this.showLouDong = false
      }
    },
    getHouseNum (danyuan_id = '') {
      let params = {
        community_id: this.form.community_id,
        loudong: this.current_loudong_id,
      }
      if (danyuan_id) {
        params.danyuan = danyuan_id
      }
      this.$ajax.get(
        '/admin/house/searchRoom',
        params,
        (res) => {
          if (res.statusCode === 200) {
            this.fanghao_list = [
              ...res.data,
              // {
              //   name: '手动填写户号',
              //   value: '',
              // },
            ]
            this.houseList = [
              ...res.data
            ]
          } else {
            // this.fanghao_list = [{ name: '手动填写户号', value: '' }]
          }
        }
      )
    },
    onSelectFanghao (fanghao_id) {
      var _current = this.fanghao_list.find((item) => item.value === fanghao_id)
      if (_current) {
        this.fanghaoName = _current.name
        this.form.building_unit_id = fanghao_id
        this.showLouDong = false
        this.checkFanghao()
      }
    },
    checkFanghao () {
      if (this.form.status == 2) return
      if (this.form.building_unit_id) {
        // if (!this.loudongName || !this.danyuanName || !this.fanghaoName) return
      } else {
        if (!this.form.loudong && !this.form.building_loudong_id) return
        if ((!this.form.danyuan && (this.form.building_danyuan_id == 0 || !this.form.building_danyuan_id)) && this.show_unit) return
        if (!this.form.fanghao) return
      }

      let params = {}
      if (this.form.building_unit_id) {
        params = {
          community_id: this.form.community_id,
          building_unit_id: this.form.building_unit_id,
        }
      } else {
        params = {
          community_id: this.form.community_id,
          loudong: this.form.loudong || this.form.building_loudong_id,
          danyuan: this.form.danyuan || this.form.building_danyuan_id,
          fanghao: this.form.fanghao,
        }
      }
      params.id = this.form.id
      this.$ajax.get('/admin/house/houseRepeat', params, (res) => {
        if (res.statusCode === 200 && res.data.status != 1) {
          return
        }
        if (res.data && res.data.id) {
          uni.showModal({
            title: '提示',
            content: '该房源已经存在 ',
            confirmText: '立即查看',
            cancelText: '放弃上架',
            success: (result) => {
              if (result.confirm) {
                this.$navigateTo("/house/detail?id=" + res.data.id)
              } else {
                this.form.fanghao = ''
                this.fanghaoName = ''
                this.form.building_unit_id = ''
              }
            }

          })
        }
      })
    },
    creatHuxing () {
      // 室厅卫
      this.huxing_range[0].items[0] = this.huxing_range[0].items[0] = this.createOption(
        1,
        6,
        'shi',
        '室',
        '卧室数量'
      )
      this.huxing_range[0].items[1] = this.createOption(1, 4, 'ting', '厅', '客厅餐厅数量')
      this.huxing_range[0].items[2] = this.createOption(1, 4, 'wei', '卫', '卫生间数量')
      this.huxing_range[0].items[3] = this.createOption(1, 3, 'balcony', '阳', '阳台数量')
      this.huxing_range[0].items[4] = this.createOption(1, 3, 'kitchen', '厨', '厨房数量')
      this.handleData(this.oldForm)
    },

    createOption (start = 1, end = 10, identifier, name, title, isChecked = false) {
      var i = start
      var res = {
        identifier,
        name,
        title,
        range: [],
      }
      while (i <= end) {
        res.range.push({
          value: i,
          name: `${i}${name}`,
          isChecked: isChecked,
        })
        i++
      }
      return res
    },
    onHuxingChange (e) {
      e.filter((item) => item.identifier).forEach((item) => {
        this.form[item.identifier] = item.value
      })
    },
    showTip (tip) {
      if (!this.form.area_id) {
        uni.showToast({
          title: tip,
          icon: 'none',
        })
      }
    },
    toSelectCommunity () {
      this.$navigateTo('/house/select_community')
    },

    handleSubmit () {
      let params = Object.assign({}, this.form)
      if (this.isCheckCommutity) {
        if (params.building_unit_id > 0) {

          params.loudong = ''
          params.danyuan = ''
          params.fanghao = ''
          params.total_floor = ''
          params.sz_floor = ''
          this.loudongIndex = ''
          this.loudongName = ''
          this.danyuanIndex = ''
          this.danyuanName = ''
        } else {
          params.building_unit_id = ''
          console.log(params);
          params.loudong = params.loudong.replace(/[\u4e00-\u9fa5]/g, '')
          params.danyuan = params.danyuan.replace(/[\u4e00-\u9fa5]/g, '')
          params.loudong_no = this.loudongName.replace(/[\u4e00-\u9fa5]/g, '')
          params.danyuan_no = this.danyuanName.replace(/[\u4e00-\u9fa5]/g, '')

          if (isNaN(Number(params.total_floor))) {
            uni.showToast({
              title: '总楼层必须是数字',
              icon: 'none',
            })
            return
          }
          if (isNaN(Number(params.sz_floor))) {
            uni.showToast({
              title: '所在楼层必须是数字',
              icon: 'none',
            })
            return
          }
          if (
            Number(params.total_floor) >= 0 &&
            Number(params.sz_floor) >= 0 &&
            Number(params.sz_floor) > Number(params.total_floor)
          ) {
            uni.showToast({
              title: '所在楼层不能大于总楼层',
              icon: 'none',
            })
            return
          }
          // params.loudong = params.loudong + this.loudongName
          // params.danyuan = params.danyuan + this.danyuanName
        }
      } else {
        if (params.loudong == this.form.loudong_no && params.danyuan == this.form.danyuan_no && this.fanghao == params.fanghao) {
          // 没有修改楼栋单元房号   删除loudong  building_unit_id  community_id 这三个字段
          delete params.loudong
          delete params.building_unit_id
          delete params.community_id
        } else {
          // 修改楼栋单元房号   删除building_unit_id  building_loudong_id  building_danyuan_id
          params.loudong_no = params.loudong
          params.danyuan_no = params.danyuan
          params.is_change_loudong = 1
          params.building_danyuan_id = 0
          params.building_loudong_id = 0
          params.building_unit_id = 0

        }
      }
      // if (this.isCheckCommutity == 1 || params.is_share == 1) {
      //   params.loudong = ''
      //   params.danyuan = ''
      //   params.fanghao = ''
      //   params.total_floor = ''
      //   params.sz_floor = ''
      //   this.loudongIndex = ''
      //   this.loudongName = ''
      //   this.danyuanIndex = ''
      //   this.danyuanName = ''
      // } else {
      //   params.building_unit_id = ''
      //   if (isNaN(Number(params.total_floor))) {
      //     uni.showToast({
      //       title: '总楼层必须是数字',
      //       icon: 'none',
      //     })
      //     return
      //   }
      //   if (isNaN(Number(params.sz_floor))) {
      //     uni.showToast({
      //       title: '所在楼层必须是数字',
      //       icon: 'none',
      //     })
      //     return
      //   }
      //   if (
      //     Number(params.total_floor) >= 0 &&
      //     Number(params.sz_floor) >= 0 &&
      //     Number(params.sz_floor) > Number(params.total_floor)
      //   ) {
      //     uni.showToast({
      //       title: '所在楼层不能大于总楼层',
      //       icon: 'none',
      //     })
      //     return
      //   }
      //   params.loudong = params.loudong + this.loudongName
      //   params.danyuan = params.danyuan + this.danyuanName
      // }
      // params.tel = params.tel.concat(this.delTelArr)
      for (let key in params) {
        if (Object.prototype.toString.call(params[key]) === '[object Array]') {
          if (params[key].length > 0 && typeof params[key][0] === 'object') {
            params[key] = JSON.stringify(params[key])
          } else {
            params[key] = params[key].join(',')
          }
        }
        if (params[key] === '' || params[key] === null) {
          if (key == 'building_loudong_id' || key == 'building_danyuan_id') {
            console.log(1);
          } else {
            delete params[key]
          }
        }
      }
      if (params.sale_price) {
        params.sale_price = (Number(params.sale_price) * 10000).toFixed()
      }
      this.subming = true
      this.$ajax
        .post('/admin/house/editPrivateHouse', params, (res) => {
          this.subming = false

          if (res.statusCode === 200) {

            setTimeout(() => {
              uni.showToast({
                title: '编辑成功',
                icon: 'none',
                mask: true,
              })
              uni.navigateBack()
              // this.$navigateBack(2)
              setTimeout(() => {
                uni.$emit('getDataAgain', { from: 'private' })
              }, 200)

              // this.$Router.replace({
              //   name: 'private_list',
              // })
            }, 1000)
          } else {
            uni.showToast({
              title: res.data.message || '编辑失败',
              icon: 'none',
              mask: true,
            })
            this.subming = false
          }
        }, () => {
          this.subming = false
        })
      // .catch((err) => {
      //   console.log(err)
      //   this.subming = false
      // })
    },
    iKnow () {
      // setTimeout(() => {
      //   this.$Router.replace({
      //     name: 'user_house_list_new',
      //     query: {
      //       type: 7,
      //     },
      //   })
      // })
    },
    selectLoudong () {
      this.loudong_level = 1
      this.showLoudongDanwei = false
      this.showLouDong = true
      this.houseList = this.loudong_list
    },
    selectDanyuan () {
      this.loudong_level = 2
      this.showDanyuanDanwei = false
      this.showLouDong = true
    },
    selectFanghao () {
      this.loudong_level = 3
      this.showfanghaoDanwei = false
      this.showLouDong = true
    },
    toWrite () {
      this.customer_write = true
    },
    setName (level, name) {
      let res = ''
      switch (level) {
        case 1:
          res = name + '号楼'
          break
        case 2:
          res = name + '单元'
          break
        case 3:
          res = name + '室'
          break

        default:
          res = name + '号楼'
          break
      }
      return res
    },
    customConfirm () {
      switch (this.loudong_level) {
        case 1:
          this.form.building_loudong_id = ''
          this.loudongName = this.setName(this.loudong_level, this.form.loudong)
          setTimeout(() => {
            this.loudong_level = 2
          }, 200)
          this.checkFanghao()

          break
        case 2:
          this.form.building_danyuan_id = ''
          this.danyuanName = this.setName(this.loudong_level, this.form.danyuan)
          setTimeout(() => {
            this.loudong_level = 3
          }, 200)
          this.checkFanghao()
          break
        case 3:
          console.log(11)
          this.form.building_unit_id = ''
          this.fanghaoName = this.setName(this.loudong_level, this.form.fanghao)
          this.checkFanghao()
          this.showLouDong = false
          break

        default:
          break
      }
    },
    customCancel () {
      switch (this.loudong_level) {
        case 1:
          this.form.loudong = ''
          break
        case 2:
          this.form.danyuan = ''
          break
        case 3:
          this.form.fanghao = ''
          break
        default:
          break
      }
      this.customer_write = false
    },

    hidePop () {
      // console.log(this.toWrite,123123123);
      // if (this.customerWrite) {
      //   this.showLouDongCustomer = false
      // } else {
      this.showLouDong = false
      this.houseList = this.loudong_list
      this.loudong_level = 1
      // }
    },
    handleData (data) {
      for (const key in data) {
        if (
          (typeof data[key] == 'string' || typeof data[key] == 'number') &&
          (data[key] || data[key] == 0)
        ) {
          if (key == 'title') {
            this.community_name = data[key]
          } else if (key == 'is_verify') {
            this.isCheckCommutity = data[key]
          } else if (key == 'loudong_unit') {
            this.loudongName = data['loudong']
          } else if (key == 'danyuan_unit') {
            this.danyuanName = data['danyuan']
          } else {
            this.form[key] = data[key]
          }

        } else if (typeof data[key] == 'object') {
          if (key == 'tel') {
            this.ownerList = JSON.parse(JSON.stringify(data[key]))
            this.form[key] = JSON.parse(JSON.stringify(data[key]))
          }
        }
      }
      console.log(this.form);
      this.fanghao = this.form.fanghao
      if (this.isCheckCommutity == 0) {
        this.form.loudong = this.form.loudong_no
        this.form.danyuan = this.form.danyuan_no
        // this.form.fanghao = this.form.fanghao

      } else {
        if (this.form.fanghao) {
          this.fanghaoName = this.form.fanghao
          if (this.form.building_unit_id > 0) {
            this.form.fanghao = ''
          }

        }
        if (this.form.building_danyuan_id > 0) {
          this.form.danyuan = ''
        }
        if (this.form.building_unit_id > 0) {
          this.show_unit = true
        }
      }



      // // 楼栋单元门牌 this.oldForm.is_share == 1 || this.oldForm.is_verify == 1
      // if (this.form.is_share == 1 || this.form.is_verify == 1) {
      //   this.current_loudong = this.form.loudong + this.form.danyuan + this.form.fanghao
      //   // this.form.building_unit_id =
      // } else {
      //   this.loudongRoomName =
      //     (this.form.loudong_no ? this.form.loudong_no + this.loudongName : '') +
      //     (this.form.danyuan_no ? this.form.danyuan_no + this.danyuanName : '') +
      //     (this.form.fanghao ? this.form.fanghao : '')
      //   this.current_l.loudong = this.form.loudong_no
      //   this.current_l.danyuan = this.form.danyuan_no
      //   this.current_l.fanghao = this.form.fanghao
      //   this.form.danyuan = this.form.danyuan_no
      //   this.form.loudong = this.form.loudong_no
      // }

      // 装修朝向
      if (data.zhuangxiu) {
        this.zhuangxiu_range[1].value = [data.zhuangxiu]
      }
      if (data.chaoxiang) {
        this.zhuangxiu_range[0].value = [data.chaoxiang]
      }
      //室厅卫 阳台厨房
      let shi_index = this.huxing_range[0].items[0].range.findIndex(
        (item) => item.value == data.shi
      )
      let ting_index = this.huxing_range[0].items[1].range.findIndex(
        (item) => item.value == data.ting
      )
      let wei_index = this.huxing_range[0].items[2].range.findIndex(
        (item) => item.value == data.wei
      )
      let yang_index = this.huxing_range[0].items[3].range.findIndex(
        (item) => item.value == data.balcony
      )
      let chu_index = this.huxing_range[0].items[4].range.findIndex(
        (item) => item.value == data.kitchen
      )
      if (shi_index >= 0) {
        this.huxing_range[0].value[0] = data.shi
        this.huxing_range[0].items[0].range[shi_index].isChecked = true
      }
      if (ting_index >= 0) {
        this.huxing_range[0].value[1] = data.ting
        this.huxing_range[0].items[1].range[ting_index].isChecked = true
      }
      if (wei_index >= 0) {
        this.huxing_range[0].value[2] = data.wei
        this.huxing_range[0].items[2].range[wei_index].isChecked = true
      }
      if (yang_index >= 0) {
        this.huxing_range[0].value[3] = data.balcony
        this.huxing_range[0].items[3].range[yang_index].isChecked = true
      }
      if (chu_index >= 0) {
        this.huxing_range[0].value[4] = data.kitchen
        this.huxing_range[0].items[4].range[chu_index].isChecked = true
      }
      // if (wei_index >= 0) this.huxing_range[0].items[2].range[wei_index].isChecked = true
      if (this.huxing_range[0].value.length > 0) {
        this.$refs.huxing.initValue()
      }

      if (this.form.sale_price) {
        this.form.sale_price = this.form.sale_price / 10000
      }
      this.$forceUpdate()
      // this.$refs.huxing.handleSelectted()
      // this.huxing_range[0].value=huxing_value
    },
    backPre () {
      if (this.loudong_level == 3) {
        //fanghao
        if (this.show_unit) {
          this.loudong_level = 2
          this.houseList = this.danyuan_list || []
          // this.searchKey = this.form.building_danyuan_id || this.form.danyuan || ''
        } else {
          this.loudong_level = 1
          this.houseList = this.loudong_list || []
          // this.searchKey = this.form.building_loudong_id || this.from.loudong || ''
        }

      } else if (this.loudong_level == 2) {
        this.loudong_level = 1
        this.houseList = this.loudong_list || []
        // this.searchKey = this.form.building_loudong_id || this.from.loudong || ''
      }

    },
    uploadDone (e) {
      this.form.pic = e.detail
    },
    changeTradeType (e) {
      console.log(e)
      // this.checkTradeStatus(e)
    },
    checkTradeStatus (e) {
      if (e == 1) {
        //出售
        this.form_options.tradeStatus = this.form_options.tradeStatus.filter(
          (item) =>
            item.values == 2 ||
            item.values == 3 ||
            item.values == 7 ||
            item.values == 10 ||
            item.values == 12
        )
      } else if (e == 2) {
        this.form_options.tradeStatus = this.tradeStatus.filter(
          (item) => item.values == 1 || item.values == 3 || item.values == 6 || item.values == 11
        )
      } else {
        this.form_options.tradeStatus = this.tradeStatus
      }
    },
    changeLevel (e) {
      this.levelIndex = e.detail.value
      this.form.level = this.levelArr[e.detail.value]
    },
    selectLevel (item, type = 'level', value = 'id') {
      // this.form[type] = item[value]
      console.log(type);
      this.$set(this.form, type, item[value])
      // if (type == 'trade_type') {
      //   this.checkTradeStatus(item[value])
      // }
    },
    showFormTel () {
      this.formTelShow = true
      if (this.ownerList.length == 0) {
        this.ownerList.push({
          owner: '',
          owner_tel: '',
          sex: 1,
          type: 1
        })
      }
    },
    getTelTypeList () {
      this.$ajax.get("/admin/house/telType", {}, res => {
        console.log(res);
        if (res.statusCode == 200) {
          this.typeList = res.data
        }
      })
    },
    addOwnerTel () {
      if (this.ownerList.length >= 3) return
      this.ownerList.push({
        owner: '',
        owner_tel: '',
        sex: 1,
        type: 1
      })
    },
    removeOwner (item, index) {
      if (item.id > 0) {
        uni.showToast({
          title: '不可删除',
          icon: 'none'
        })
        return
        // item.is_del = 1
        // this.delTelArr.push(item)
      }
      this.ownerList.splice(index, 1)
    },
    chooseImage () {
      // #ifdef H5-WEIXIN
      if (this.clicking) return
      this.clicking = true
      setTimeout(() => {
        this.clicking = false
      }, 500)
      this.wechatChooseImg()
      // #endif
      // #ifndef H5-WEIXIN
      this.chooseImg()
      // #endif
    },
    chooseImg () {
      uni.chooseImage({
        count: this.maxCount - this.imgList.length,
        sizeType: ['original'],
        success: (res) => {
          if (res.tempFiles.length > this.maxCount - this.imgList.length) {
            uni.showToast({
              title: '最多只能上传' + this.maxCount + '张图片',
              icon: 'none',
            })
            return false
          }
          let sizeOk = true
          res.tempFiles.map((item) => {
            if (item.size / 1024 > this.img_size) {
              sizeOk = false
            }
          })
          if (!sizeOk) {
            uni.showToast({
              title: '上传图片不能大于' + this.img_size + 'KB',
              icon: 'none',
            })
            return
          }
          const tempFilePaths = res.tempFilePaths
          let i = 0
          let _this = this
          uni.showLoading({
            title: '正在上传',
            mask: true,
          })

          // #ifdef MP-BAIDU
          setTimeout(() => {
            //解决百度小程序上传第一张图片不显示的问题
            // this.imgList = Array.from(this.imgs)
          }, 500)
          // #endif
          // #ifndef MP-BAIDU
          // this.imgList = Array.from(this.imgs)
          // #endif

          this.$emit('chooseDon', tempFilePaths)
          function upload () {
            _this
              .$uploadFile(_this.upload_api, tempFilePaths[i])
              .then((res) => {
                if (!res.data.data) {
                  uni.showToast({
                    title: res.data.message || '上传失败',
                    icon: 'none',
                  })
                  _this.$emit('error', res)
                  return
                }
                _this.imgList.push({
                  url: res.data.data,
                  category_id: 1,
                  descp: '',
                  is_cover: 0,
                })

                i++
                if (i < tempFilePaths.length) {
                  upload()
                } else {
                  uni.hideLoading()
                }
              })
              .catch((err) => {
                console.log(err)
                uni.hideLoading()
              })
          }
          upload()
        },
      })
    },
    wechatChooseImg () {
      //选择图片
      // this.setWxConfig()
      this.clicking = false
      if (!this.wx) {
        uni.showToast({
          title: '上传组件未初始化完毕，请稍后重试',
          icon: 'none',
        })
        // this.setWxConfig()
        this.$emit('chooseimgfail')
        return
      }

      let _this = this
      let chooseCount = this.maxCount - this.imgList.length
      this.wx.chooseImage({
        count: chooseCount > 9 ? 9 : chooseCount, // 默认9
        sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: function (res) {
          var tempFilePaths = res.localIds // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
          if (tempFilePaths.length > _this.maxCount - _this.imgList.length) {
            uni.showToast({
              title: '最多只能上传' + _this.maxCount + '张图片',
              icon: 'none',
            })
            return false
          }
          let i = 0
          uni.showLoading({
            title: '正在上传',
            mask: true,
          })
          function upload () {
            _this.wx.getLocalImgData({
              localId: tempFilePaths[i], // 图片的localID
              success: function (res) {
                var localData = res.localData // localData是图片的base64数据，可以用img标签显示
                _this.$ajax.post(_this.upload_api, { base64: localData }).then((res) => {
                  if (res.data.status == 200) {
                    _this.imgList.push({
                      url: res.data.data,
                      category_id: 1,
                      descp: '',
                      is_cover: 0,
                    })
                  } else {
                    uni.showToast({
                      title: res.data.message || '上传失败',
                      icon: 'none',
                    })
                  }
                  i++
                  if (i < tempFilePaths.length) {
                    upload()
                  } else {
                    uni.hideLoading()
                  }
                })
              },
            })
          }
          upload()
        },
      })
    },
    confirmOwner () {
      // let i = this.ownerList.findIndex((item) => !item.owner && !item.owner_tel)
      // if (i >= 0) {
      //   this.ownerList.splice(i, 1)
      // }
      let emptyIndexArr = []
      this.ownerList.map((item, index) => {
        if (item.owner && item.owner_tel) {
          console.log()
        } else if (!item.owner && !item.owner_tel) {
          emptyIndexArr.push(index)
        } else if (item.owner && !item.owner_tel) {
          uni.showToast({
            title: '请输入业主手机号',
            icon: 'none',
          })
          throw Error()
        } else if (!item.owner && item.owner_tel) {
          uni.showToast({
            title: '请输入业主姓名',
            icon: 'none',
          })
          throw Error()
        }
      })
      if (emptyIndexArr.length > 0) {
        emptyIndexArr.map((item) => {
          this.ownerList.splice(item, 1)
        })
      }

      this.form.tel = this.ownerList
      this.formTelShow = false
    },
    PreViewImg (item, idx) {
      this.$navigateTo({
        name: 'preview',
      })
      item.idx = idx
      setTimeout(() => {
        uni.$emit('getImgUrl', item)
      }, 300)
    },
    showSubLoudong () {
      this.show_sub_loudong = true
    },
    submitLoudongData () {
      let { loudong, danyuan, fanghao } = this.current_l
      console.log(this.current_l)
      this.form.loudong = loudong
      this.form.danyuan = danyuan
      this.form.fanghao = fanghao
      this.loudongRoomName =
        (loudong ? loudong + this.loudongName : '') +
        (danyuan ? danyuan + this.danyuanName : '') +
        (fanghao ? fanghao : '')
      this.show_sub_loudong = false
    },
    cancel () {
      // this.form.loudong = ''
      // this.form.danyuan = ''
      // this.form.fanghao = ''
      this.show_sub_loudong = false
    },
    handlerInput (e, type) {
      let text = e.detail.value.match(/[a-zA-Z0-9]/g)
      if (text && text.length) {
        this.$nextTick(() => {
          this.$set(this.current_l, type, text.join(''))
        })
      } else {
        this.$nextTick(() => {
          this.$set(this.current_l, type, '')
        })
      }
    },
    selectHouse (item) {
      if (this.loudong_level == 1) {
        this.onSelectLoudong(item.value)

      } else if (this.loudong_level == 2) {
        this.onSelectDanyuan(item.value)
      } else if (this.loudong_level == 3) {
        this.onSelectFanghao(item.value)
        this.houseList = this.loudong_list
      }
      setTimeout(() => {
        this.searchKey = ''
      }, 100);
    },
    confirmSearch () {
      if (!this.searchKey) return
      if (this.loudong_level == 1) {
        let current = this.loudong_list.find(item => item.value == this.searchKey)
        if (current) {
          console.log(current, "current_lo");
          this.onSelectLoudong(current.value)
        } else {
          this.form.loudong = this.searchKey.replace(/[\u4e00-\u9fa5]/g, '')
          this.form.building_loudong_id = ''
          this.danyuanName = ''
          this.form.building_danyuan_id = ''
          this.fanghao = ''
          this.form.building_unit_id = ''
          this.fanghaoName = ''
          this.loudongName = this.setName(this.loudong_level, this.searchKey.replace(/[\u4e00-\u9fa5]/g, ''))
          this.loudong_level = 2

        }

      } else if (this.loudong_level == 2) {
        let current = this.danyuan_list.find(item => item.value == this.searchKey)
        if (current) {
          this.onSelectDanyuan(current.value)
        } else {
          this.form.danyuan = this.searchKey.replace(/[\u4e00-\u9fa5]/g, '')
          this.form.building_danyuan_id = ''
          this.fanghao = ''
          this.form.building_unit_id = ''
          this.fanghaoName = ''
          this.danyuanName = this.setName(this.loudong_level, this.searchKey.replace(/[\u4e00-\u9fa5]/g, ''))
          this.loudong_level = 3
        }

      } else if (this.loudong_level == 3) {
        let current = this.fanghao_list.find(item => item.name == this.searchKey.replace(/[\u4e00-\u9fa5]/g, ''))
        if (current) {
          this.onSelectFanghao(current.value)
        } else {
          this.form.fanghao = this.searchKey.replace(/[\u4e00-\u9fa5]/g, '')
          this.fanghaoName = this.setName(this.loudong_level, this.searchKey.replace(/[\u4e00-\u9fa5]/g, ''))
          this.form.building_unit_id = ''
          console.log(this.form.building_loudong_id, this.form.building_danyuan_id, this.form.building_unit_id, this.form.loudong, this.form.danyuan, this.form.fanghao);
          this.checkFanghao()
          this.showLouDong = false
        }
      }
      setTimeout(() => {
        this.searchKey = ''
      }, 100);
    },
    searchList () {
      if (this.loudong_level == 1) {
        this.houseList = this.loudong_list.filter(item => item.name.includes(this.searchKey + ''))
      } else if (this.loudong_level == 2) {
        this.houseList = this.danyuan_list.filter(item => item.name.includes(this.searchKey + ''))
      } else if (this.loudong_level == 3) {
        this.houseList = this.fanghao_list.filter(item => item.name.indexOf(this.searchKey + '') != -1
        )

      }


    }
  },
}
</script>

<style scoped lang="scss">
.add-container {
  // #ifdef H5
  min-height: calc(100vh - 44px);
  // #endif
  // #ifndef H5
  min-height: 100vh;
  // #endif
  background-color: #f8f8f8;
  // padding-bottom: 245rpx;
  position: relative;
  .field_title {
    padding: 24rpx 48rpx;
    font-size: 22rpx;
    color: #8a929f;
  }
}

.upload-box {
  padding: 24rpx 0;
  align-items: center;
  .upload_btn {
    width: 128rpx;
    height: 128rpx;
    text-align: center;
    justify-content: center;
    background: #f2f2f2;
  }
  .cover_img {
    width: 128rpx;
    height: 128rpx;
  }
  .upload_tip {
    margin-left: 24rpx;
    margin-right: 6rpx;
    justify-content: center;
    .tip_title {
      font-size: 36rpx;
      color: #2e3c4e;
      margin-bottom: 16rpx;
    }
    .tip_content {
      font-size: 22rpx;
      color: #999;
    }
  }
}
.form {
  margin-bottom: 24rpx;
  padding-top: 12rpx;
  background-color: #fff;
  &.customer_write {
    .form_item {
      > .left {
        > .label {
          font-weight: 500;
        }
      }
    }
    .opt_btns {
      margin-top: 20rpx;
      .customer_sub {
        padding: 20rpx 30rpx;
        color: #fff;
        border-radius: 8rpx;
        background: #2d84fb;
        margin-right: 20rpx;
        &.cancel {
          background: #fff;
          color: #666;
        }
      }
    }
  }
  &.mb0 {
    margin-bottom: 0;
  }
  .form_item {
    flex-direction: row;
    // margin-bottom: 12rpx;
    padding: 34rpx 48rpx;
    &.popup_form_item {
      .popup_input {
        margin-right: 10rpx;
      }
      ::v-deep .select-box {
        width: auto;
      }
    }
    .level_list {
      .level_item {
        background: #fff;
        border: 2rpx solid #dedede;
        border-radius: 4rpx;
        text-align: center;
        padding: 16rpx 0;
        &.active {
          border: 2rpx solid #3e81d6;
          .level_item_name {
            color: #3e81d6;
          }
          .level_item_tip {
            color: #3e81d6;
          }
        }
        ~ .level_item {
          margin-left: 10rpx;
        }
        .level_item_name {
          font-size: 28rpx;
          color: #8d9099;
          // font-weight: 700;
        }
        .level_item_tip {
          font-size: 22rpx;
          margin-top: 10rpx;
          color: #8d9099;
        }
      }
    }
    .my_inp {
      &.border {
        padding: 8rpx;
        border: 2rpx solid #d9cccc;
        border-radius: 8rpx;
      }
    }
    .icon {
      &.border {
        margin-left: 8rpx;
        padding: 12rpx;
        min-width: 160rpx;
        border-radius: 8rpx;
        border: 2rpx solid #d9cccc;
        position: relative;
        .novalue {
          color: #999999;
        }
        .sanjiao {
          position: absolute;
          right: 12rpx;
          width: 0;
          height: 0;
          margin-left: 8rpx;
          border-right: 10rpx solid transparent;
          border-left: 10rpx solid transparent;
          border-top: 10rpx solid #d9cccc;
        }
      }
    }
    > .left {
      flex: 1;
      align-items: center;
      justify-content: space-between;
      position: relative;
      > .label {
        // line-height: 1;
        margin-right: 30rpx;
        font-size: 32rpx;
        font-weight: 600;
        color: #2e3c4e;
        min-width: 128rpx;
      }
      .owner {
        font-size: 36rpx;
        color: #999;
      }

      .mask {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        z-index: 10;
      }
      .tip {
        font-size: 24rpx;
        color: #fe6c17;
      }
      .date_row {
        justify-content: space-between;

        .select {
          font-size: 30rpx;
          flex-wrap: nowrap;
          white-space: nowrap;
        }
        .text-center {
          text-align: center;
        }
        .placeholder {
          color: #8a929f;
        }
      }
    }
    ::v-deep .icon-jinrujiantou {
      padding: 12rpx;
    }
    .input-group {
      .input-item {
        align-items: center;
        height: 40rpx;
        font-size: 26rpx;
        ~ .input-item {
          margin-left: 16rpx;
        }
        .label {
          margin-right: 12rpx;
        }
        input {
          line-height: 1;
          height: 36rpx;
          flex: 1;
        }
      }
    }
    .custom_row {
      padding: 16rpx 16rpx;
      .flex-row {
        align-items: center;
      }
    }
  }
}
.submit_type {
  padding: 0 48rpx 24rpx;
  font-size: 22rpx;
  color: #8a929f;
}
.btn_box {
  padding: 48rpx;
  background-color: #fff;
  // position: absolute;
  // right: 0;
  // bottom: 0;
  // left: 0;
}
.input {
  font-size: 36rpx;
  color: #8a929f;
  &.has_value {
    color: #2e3c4e;
  }
}
.xieyi {
  margin-bottom: 20rpx;
  color: #999;
  .title {
    text-decoration: underline;
  }
}

.check_box {
  margin-right: 15rpx;
}
.no_checked {
  display: inline-block;
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  box-sizing: border-box;
  border: 4rpx solid #dedede;
}
.popup_box {
  padding: 24rpx 48rpx;
  background: #fff;
  position: relative;
  max-height: 60vh;
  min-height: 40vh;
  overflow-y: auto;
  .hide_pop {
    position: absolute;
    top: 0;
    right: 0;
    padding: 24rpx;
    color: #2d84fb;
  }
  .back_pop {
    position: absolute;
    top: 0;
    left: 0;
    padding: 24rpx;
    color: #2d84fb;
  }
  .popup_title {
    font-size: 28rpx;
    color: #2e3c4e;
    font-weight: 600;
    margin-bottom: 24rpx;
  }
  .popup_tips {
    margin: 0 0 24rpx;
    &_left {
      font-size: 22rpx;
      color: #2e3c4e;
    }
    &_right {
      font-size: 22rpx;
      color: #2d84fb;
    }
  }
  .popup_list {
    height: 860rpx;
    overflow-y: auto;
    &_item {
      padding: 36rpx 0;
      &_title {
        font-size: 28rpx;
        color: #2e3c4e;
      }
    }
  }
  .search_box {
    .confirm {
      margin-left: 20rpx;
      padding: 10rpx;
      border-radius: 4rpx;
      background: #2d84fb;
      color: #fff;
    }
  }
  .write_ok {
    padding: 24rpx;
    background: #fff;
  }
  &.success_add {
    justify-content: center;
    align-items: center;
    width: 80vw;
    border-radius: 20rpx;
    min-height: auto;
    .popup_box_img {
      width: 100%;
      height: 400rpx;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .popup_box_text {
      color: #2d84fb;
    }
    .btns {
      margin-top: 40rpx;
      width: 100%;
    }
  }
}

.quick_copy {
  background: #fff;
  .open_menu {
    padding: 24rpx 48rpx;
  }
  .quick_copy_left {
    align-items: center;
    .quick_copy_left_img {
      width: 38rpx;
      height: 38rpx;
      margin-right: 24rpx;
      image {
        width: 100%;
      }
    }
  }
  .copy_icon {
    width: 0;
    height: 80rpx;
    overflow: hidden;
    align-items: center;
    transition: width 0 ease 2s;
  }
  .copy_content {
    padding: 24rpx 48rpx;
    height: 192rpx;
    overflow: hidden;
    background: #fff;
    // margin: 24rpx 0 48rpx;
    border-radius: 8rpx;
    color: #999;
    .copy_c {
      // background: #8a929f;
      border: 2rpx solid #f8f8f8;
      border-radius: 10rpx;
      padding: 10rpx;
      textarea {
        background: #fff;
        width: 100%;
        height: 100%;
      }
    }
  }
}
.form_tel {
  padding: 30rpx 48rpx;
  background: #fff;
  .form_tel_title {
    font-size: 32rpx;
    padding: 30rpx 0;
    position: relative;
    color: #333;
    font-weight: 700;
    .submit {
      position: absolute;
      right: 0;
      top: 10rpx;
      background: #2d84fb;
      color: #fff;
      padding: 20rpx 30rpx;
      font-size: 30rpx;
    }
  }
  .form_owner_info {
    padding: 30rpx 0 20rpx;
    .img {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      overflow: hidden;
      image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .icon {
      margin-right: 15rpx;
    }
    .owner {
      // border: 2rpx solid #999;
      padding: 30rpx 10rpx;
      font-size: 28rpx;
    }
    // .owner_tel {
    //   margin-left: 18rpx;
    // }
  }
  .add_other {
    padding: 40rpx 0;
    .icon {
      margin-right: 15rpx;
    }
    .add_owner {
      color: #999;
      font-size: 28rpx;
    }
  }
}

.imgs {
  background: #fff;
  overflow-x: auto;
  padding-top: 20rpx;
  .upload {
    width: 160rpx;
    height: 160rpx;
    min-width: 160rpx;
    background: #f7f7f7;
    margin-right: 20rpx;
  }
  .img_list {
    overflow-x: auto;
    .img {
      width: 160rpx;
      height: 160rpx;
      min-width: 160rpx;
      margin-right: 20rpx;
      image {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.show_sub_loudong {
  background: #fff;
  padding: 48rpx;
  width: 80vw;
  margin: 0 auto;
  border-radius: 10rpx;
  .search_inp {
    border: 2rpx solid #f0f0f0;
    border-radius: 4rpx;
    padding: 10rpx;
    ~ .search_inp {
      margin-left: 20rpx;
    }
    input {
      color: #666;
    }
    .pls {
      color: #d6d6d6;
    }
  }
  .btn_group {
    // min-height: 100rpx;
    // box-shadow: 0 -6rpx 10rpx 0 #f1f1f1;
    margin-top: 40rpx;

    .btn {
      padding: 12rpx 24rpx;
      flex: 1;
      text-align: center;
      &.bg_highlight {
        background-color: #2d84fb;
        color: #fff;
      }
    }
  }
}
.showSingle {
  padding: 5rpx 48rpx;
  color: #f56c6c;
  font-size: 22rpx;
}
</style>
