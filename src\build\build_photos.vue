<template>
  <view class="list">
    <tabBar
      ref="tab_bar"
      :top="0"
      :tabs="photos_category_list"
      :nowIndex="
        photos_category_list.findIndex((item) => item.value == params.category)
      "
      @click="onClickCate"
    ></tabBar>
    <view class="photos-list top-line">
      <!-- 效果 -->
      <block v-for="(item, index) in photos_category_list" :key="index">
        <view class="title" :class="'title' + index">{{
          item.description
        }}</view>
        <view class="img-box row" v-if="item.photos_list.length > 0">
          <block v-for="(item1, index1) in item.photos_list" :key="index1">
            <image
              @click="$previewImage(item1)"
              mode="aspectFill"
              :src="item1"
              class="img-style"
            ></image>
          </block>
        </view>
        <view class="img-box row" v-else>
          <view class="center">
            <noData></noData>
          </view>
        </view>
      </block>
    </view>
  </view>
</template>

<script>
import noData from "@/components/noData.vue";
import tabBar from "@/components/tabBar";
export default {
  components: { tabBar, noData },
  computed: {},
  data() {
    return {
      photos_category_list: [],
      params: {
        category: 0,
      },
      build_id: "",
    };
  },
  onReady() {},
  onLoad(options) {
    this.getWxConfig();
    this.build_id = options.build_id;
    this.getDcitionaryCategory();
  },
  methods: {
    getDcitionaryCategory() {
      this.$setDictionary((e) => {
        e.find((item) => {
          switch (item.name) {
            case "BUILD_IMG_CATEGORY":
              let arr = item.childs.map((item) => {
                item.photos_list = [];
                return item;
              });
              this.getBuildImgs(this.build_id, arr);
              break;
          }
        });
      });
    },
    getBuildImgs(id, arr) {
      this.$ajax.get(`/common/build/img/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          var imgList = res.data;
          arr.map((item, index) => {
            arr[index].photos_list = [];
            imgList.map((item1) => {
              if (item1.category == item.value) {
                return arr[index].photos_list.push(item1.img);
              }
            });
            // 获取图片数组长度，赋值给数量显示
            arr[index].statistics = arr[index].photos_list.length;
            this.photos_category_list = arr;
          });
        }
      });
    },
    onClickCate(e) {
      uni
        .createSelectorQuery()
        .select(".photos-list")
        .boundingClientRect((res) => {
          uni
            .createSelectorQuery()
            .select(".title" + e.index)
            .fields({ rect: true, scrollOffset: true }, (data) => {
              uni.pageScrollTo({
                duration: 300,
                scrollTop: data.top - res.top, //滚动到实际距离是元素距离顶部的距离减去最外层盒子的滚动距离
              });
            })
            .exec();
        })
        .exec();
      this.params.category = e.value;
    },
  },
  onPullDownRefresh() {
    this.getBuildImgs(this.build_id);
    uni.stopPullDownRefresh();
  },
};
</script>

<style scoped lang="scss">
.list {
  .photos-list {
    margin-top: 80rpx;
    padding: 0 48rpx;
    margin-bottom: 120rpx;
    .title {
      padding: 50rpx 0;
      color: #4d4d4d;
      font-size: 28rpx;
    }
    .img-box {
      flex-wrap: wrap;
      justify-content: space-between;
      .img-style {
        margin: 6rpx 0;
        width: 202rpx;
        height: 202rpx;
        border-radius: 16rpx;
      }
      &::after {
        content: "";
        width: 30%;
      }
    }
  }
  button::after {
    border: none;
  }
  // 底部按钮
  .bottom-btn {
    position: fixed;
    width: 100%;
    height: 112rpx;
    background: #fff;
    bottom: 0;
    border-top: 1rpx solid #eee;
    padding: 8rpx 48rpx;
    align-items: center;
    justify-content: space-between;
    .left {
      button {
        font-size: 22rpx;
        color: #999;
        background: #fff;
        padding: 0;
        border-radius: 0;
        line-height: 1.2;
      }
      .icon-ctn {
        margin-right: 24rpx;
        align-items: center;
        text {
          font-size: 22rpx;
          color: #999;
        }
      }
    }
  }
}
.center {
  display: flex;
  justify-content: center;
  width: 100%;
}
</style>
