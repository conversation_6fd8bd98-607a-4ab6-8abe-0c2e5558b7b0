<template>
  <view class="detail" v-if="showContent">
    <!-- 焦点图 -->
    <view class="focus-box">
      <swiper class="banner" :indicator-dots="false" :circular="true" :duration="300" indicator-active-color="#f65354"
        @change="swiperChange" :current="swiperCurrent">
        <template v-if="focus.length && show">
          <swiper-item v-for="(item, index) in focus" :key="index">
            <view class="swiper-item" @click="preImgs(item)">
              <image :src="item.url | imageFilter('w_6401')" mode="aspectFill"></image>
            </view>
          </swiper-item>
        </template>
        <template v-if="!focus.length && show">
          <swiper-item>
            <view class="swiper-item" @click="preImgs()">
              <image :src="'https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/house/quesheng.png'
                | imageFilter('w_6401')
                " mode="aspectFill"></image>
            </view>
          </swiper-item>
        </template>
      </swiper>
      <view class="img-total">共{{ focusLen || 0 }}张</view>

      <view class="cate-box">
        <view class="cate-list flex-row">
          <view class="cate" :class="cateActive == cateName.value ? 'active' : ''" v-for="(cateName, i) in cusBtn"
            :key="i" @click="switchFocus(cateName.value)">
            {{ cateName.name }}
          </view>
        </view>
      </view>
    </view>
    <!-- 主要信息 -->
    <view class="container main_info">
      <view class="title flex-row items-center">
        <!-- <text v-else class="status status2">新上</text> -->
        <text class="title_con flex-1">{{ detail.title }}</text>
        <!-- <view class="right" @click="editHouse">维护</view> -->
        <!-- <view
          v-if="user_info.open_lm == 1 && detail.is_release != 1"
          class="right"
          @click="addToLianmai"
          >发布到联卖</view
        > -->
      </view>
      <view class="huxing flex-row">
        <view class="items-center flex-row flex-1">
          <view class="huxing_price flex-row">
            <template v-if="detail.trade_type === 1 || detail.trade_type === 3">
              <template v-if="detail.sale_price">
                <text class="value">{{ detail.sale_price | priceFilter }}</text>
                <text class="unit">万</text>
                <template v-if="detail.price_change && detail.price_change.num > 0">
                  <text :class="detail.price_change.direct == 1 ? 'up' : 'down'">
                    {{ detail.price_change.direct == 1 ? '↑' : '↓' }}
                  </text>
                </template>
              </template>
              <template v-else><text class="price_con">面议</text></template>
            </template>
            <template v-if="detail.trade_type === 2">
              <template v-if="detail.rent_price">
                <text class="value">{{ detail.rent_price | priceFilterRent }}</text>
                <text class="unit">元/月</text>
              </template>
              <template v-else>
                <text class="value">面议</text>
              </template>
            </template>
          </view>
          <view class="stw flex-row">
            <view class="value" v-if="detail.shi">
              {{ detail.shi }}
            </view>
            <view class="unit" v-if="detail.shi"> 室 </view>
            <view class="value" v-if="detail.ting">
              {{ detail.ting }}
            </view>
            <view class="unit" v-if="detail.ting"> 厅 </view>
            <view class="value" v-if="detail.wei">
              {{ detail.wei }}
            </view>
            <view class="unit" v-if="detail.wei"> 卫 </view>
          </view>
          <view class="mianji flex-row">
            <view class="value">
              {{ detail.mianji }}
            </view>
            <view class="unit"> m² </view>
          </view>
        </view>
        <view class="btn" @click="showEditPop">维护 </view>
        <!-- <view class="btn" @click="editHouse">维护 </view> -->
      </view>
      <view class="loudong_danyuan flex-row align-center">
        <text>{{ detail.loudong }}-{{ detail.danyuan }}-{{ detail.fanghao }}</text>
        <view v-if="!show_fanghao && !(detail.unilateral_agent == 1 && detail.unilateral_agent_auth == 0)
          " @click="showFanghao" class="icon flex-row align-center">
          <img src="/static/img/eye.png" alt="" mode="widthFix" />
        </view>
      </view>
      <view class="house_label">
        <view class="house_area small mt24 flex-row items-center">
          <!-- <view class="flex-row mr12 zushou" v-if="detail.trade_type">
              {{ detail.trade_type }}
            </view> -->
          <!-- <view class="flex-row items-center du mr12" v-if="detail.wtr_id">
            <image src="/static/icon/index/<EMAIL>" mode="widthFix"></image>
          </view> -->
          <template v-if="!(detail.unilateral_agent == 1 && detail.unilateral_agent_auth == 0)">
            <view class="flex-row items-center justify-center level a mr12" v-if="detail.level == 'A'">
              <picker :range="levelArr" :value="levelIndex" @change="setLevels" mode="selector">
                A级</picker>
            </view>
            <view class="flex-row items-center justify-center level b mr12" v-if="detail.level == 'B'">
              <picker :range="levelArr" :value="levelIndex" @change="setLevels" mode="selector">
                B级</picker>
            </view>
            <view class="flex-row items-center justify-center level c mr12" v-if="detail.level == 'C'">
              <picker :range="levelArr" :value="levelIndex" @change="setLevels" mode="selector">
                C级</picker>
            </view>
          </template>
          <template v-else>
            <view class="flex-row items-center justify-center level mr12" @click="setLevels"
              :class="{ c: detail.level == 'C', b: detail.level == 'B', a: detail.level == 'A' }">
              {{ detail.level }}</view>
          </template>

          <view class="flex-row items-center justify-center company mr12" v-if="detail.is_share == 1">
            公盘</view>
          <view class="flex-row items-center justify-center istop mr12" :class="{
            wuxiao: detail.trade_status_id == 8,
            zanhuan: detail.trade_status_id == 5,
            chengjiao: detail.trade_status_id == 100 || detail.trade_status_id == 101,
            suoding: detail.trade_status_id == 102,
          }" v-if="detail.trade_status_id">{{ detail.trade_status | statusFilter }}</view>
          <view class="flex-row items-center weituo key mr12" v-if="detail.wtr_id">
            <view class="img mr12">
              <image src="/static/icon/index/<EMAIL>" mode="widthFix"></image>
            </view>
            <text>委托</text>
          </view>
          <view class="flex-row items-center jujiao key mr12" v-if="detail.is_top == 1">
            <!-- <view class="img mr12">
              <image src="/static/icon/index/<EMAIL>" mode="widthFix"></image>
            </view> -->
            <text>聚焦</text>
          </view>
          <view class="flex-row items-center vip key mr12" v-if="detail.djwtr_id">
            <view class="img mr12">
              <image src="/static/icon/index/<EMAIL>" mode="widthFix"></image>
            </view>
            <text>VIP</text>
          </view>
          <view class="flex-row items-center key mr12" v-if="detail.ysr && detail.ysr.id">
            <view class="img mr12">
              <image src="/static/icon/index/<EMAIL>" mode="widthFix"></image>
            </view>
            <text>有钥匙</text>
          </view>
        </view>
      </view>

      <!-- <view class="house_info flex-row items-center items-between">
        <view class="house_info_item flex-1">
          <view class="house_price flex-row">
            <template v-if="detail.trade_type === 1 || detail.trade_type === 3">
              <template v-if="detail.sale_price">
                <text class="price_con">{{ detail.sale_price | priceFilter }}</text>
                <template v-if="detail.price_change && detail.price_change.num > 0">
                  <text :class="detail.price_change.direct == 1 ? 'up' : 'down'">
                    {{ detail.price_change.direct == 1 ? '↑' : '↓' }}
                  </text>
                </template>
              </template>
              <template v-else>
                <text class="price_con">面议</text>
              </template>
            </template>
            <template v-if="detail.trade_type === 2">
              <template v-if="detail.rent_price">
                <text class="price_con">{{ detail.rent_price | priceFilterRent }}</text>
                <text class="price_unit">元/月</text>
              </template>
              <template v-else>
                <text class="price_con">面议</text>
              </template>
            </template>
          </view>
          <view class="house_price_unit"
            >{{ detail.trade_type === 1 || detail.trade_type === 3 ? '售价' : '租金' }}
          </view>
        </view>
        <view class="house_info_item items-center flex-1">
          <view class="house_price">
            {{ `${detail.shi}室${detail.ting}厅${detail.wei}卫` }}
          </view>
          <view class="house_price_unit"> 户型 </view>
        </view>
        <view class="house_info_item items-end flex-1">
          <view class="house_price"> {{ detail.mianji }}m² </view>
          <view class="house_price_unit"> 面积 </view>
        </view>
      </view> -->
    </view>
    <view class="jiangjia" v-if="detail.price_change && detail.price_change.num > 0">
      <view class="pr_top">
        <view class="pr_label_item flex-row items-center">
          <view class="pr_label flex-row items-center flex-1">
            <view class="img">
              <image src="/static/icon/jiang.png" mode="widthFix" alt="" />
            </view>
            <view class="pr_label_name">
              近期业主调价{{ detail.price_change.num }}次
              {{ detail.price_change.direct_desc }}
              {{ detail.price_change.range | formatPrice(detail.trade_type_status) }}
            </view>
          </view>
          <!-- <view class="pr_label_right flex-row flex-1"></view> -->
        </view>
      </view>
    </view>

    <!-- 房屋信息 -->
    <view class="container other_info">
      <image v-if="detail.trade_status === 1" class="buy_success" src="/static/icon/buy_success.png" mode="widthFix" />
      <!-- <view class="label flex-row items-center space-between">
        <text class="flex-1">房屋信息</text>
        <text class="bianhao" @click="copyBianhao"> 房源编号：{{ detail.id }} </text>
      </view> -->
      <view class="info_list flex-row">
        <view class="info_item" v-if="detail.area">
          <text class="label">区域</text>
          <text class="value">{{ detail.area || '-' }}</text>
        </view>
        <view class="info_item" v-if="detail.community_name">
          <text class="label">小区</text>
          <text class="value">{{ detail.community_name || '-' }}</text>
        </view>
        <view class="info_item" v-if="detail.chaoxiang">
          <text class="label">朝向</text>
          <text class="value">{{ detail.chaoxiang || '-' }}</text>
        </view>
        <view class="info_item" v-if="detail.zhuangxiu">
          <text class="label">装修</text>
          <text class="value">{{ detail.zhuangxiu }}</text>
        </view>
        <view class="info_item" v-if="detail.area_name">
          <text class="label">区域</text>
          <text class="value">{{ detail.area_name }}</text>
        </view>
        <view class="info_item" v-if="detail.region_name">
          <text class="label">商圈</text>
          <text class="value">{{ detail.region_name }}</text>
        </view>
        <view class="info_item" v-if="detail.region_name">
          <text class="label">编号</text>
          <text class="value">{{ detail.id }}</text>
        </view>
        <view class="info_item" v-if="detail.trade_type == 3">
          <text class="label">租金</text>
          <text class="value">{{ detail.rent_price }}元/月</text>
        </view>
        <!-- <view class="info_item" v-if="detail.loudong">
          <text class="label">楼栋</text>
          <text class="value">{{ detail.loudong }}</text>
        </view>
        <view class="info_item" v-if="detail.danyuan">
          <text class="label">单元</text>
          <text class="value">{{ detail.danyuan }}</text>
        </view>
        <view class="info_item" v-if="detail.fanghao">
          <text class="label">房号</text>
          <text class="value">{{ detail.fanghao }}</text>
        </view> -->
        <view class="info_item" v-if="detail.sz_floor">
          <text class="label">楼层</text>
          <text class="value">{{ detail.sz_floor }}层/共{{ detail.total_floor }}层</text>
        </view>
        <view class="info_item" v-if="detail.kitchen > 0">
          <text class="label">厨房数量</text>
          <text class="value">{{ detail.kitchen || '-' }}</text>
        </view>
        <view class="info_item" v-if="detail.balcony > 0">
          <text class="label">阳台数量</text>
          <text class="value">{{ detail.balcony || '-' }}</text>
        </view>
      </view>
    </view>

    <!-- <view class="container other_info">
      <view class="label img_label flex-row items-center">
        <text class="flex-1">房屋图片</text>
        <view class="upload" v-if="photoList.length > 0 && detail.protect == 0">
          <view class="edit_img">补充图片</view>
          <view class="uploads">
            <myUpload
              @uploadDone="uploadSuccess($event)"
              :action="upload_api"
              :del="false"
              :upInfo="upInfo"
              :chooseType="1"
              :maxCount="10"
            ></myUpload>
          </view>
        </view>
      </view>
    </view> -->
    <!-- <template v-if="photoList.length > 0">
      <view class="tab-box flex-row items-center">
        <tabs
          :options="img_tab_lists"
          :format="{ name: 'name', value: 'type' }"
          :equispaced="false"
          class="flex-1"
          v-model="img_tab_type"
        ></tabs>
      </view>
      <view class="img_swiper">
        <swiper
          v-for="(item, index) in imgs"
          :key="index"
          v-show="img_tab_type === item.type"
          class="adviser-swiper"
          :duration="260"
          display-multiple-items="1"
          next-margin="170rpx"
        >
          <swiper-item v-for="(items, idx) in item.imgs" :key="idx">
            <view class="swiper-item">
              <image mode="aspectFill" class="img_url" :src="items" alt="" />
            </view>
          </swiper-item>
        </swiper>
      </view>
    </template> -->
    <!-- <template v-else>
      <view class="no_photos flex-row items-center justify-center">
        暂未上传图片
        <view class="upload" v-if="photoList.length == 0 && detail.protect == 0">
          <view>立即上传</view>
          <view class="uploads">
            <myUpload
              :del="false"
              :upInfo="upInfo"
              @uploadDone="uploadSuccess($event)"
              :action="upload_api"
              :chooseType="1"
              :maxCount="10"
            ></myUpload>
          </view>
        </view>
      </view>
    </template> -->
    <view class="tab-box">
      <tabs :options="tab_list" :format="{ name: 'name', value: 'type' }" :equispaced="false" v-model="tab_type"
        @change="changeTabType"></tabs>
    </view>

    <!-- 跟进记录 -->
    <view v-show="tab_type === 2 || tab_type === 4" class="container" :class="{ no_vip_member: user_info.vip_id == 0 }">
      <template>
        <!-- #ifdef H5 -->
        <timeLine v-if="follow_list.length > 0" :list="follow_list" v-slot="{ prop }">
          <view class="timeline_item">
            <view class="time flex-row items-center space-between">
              <text>{{ prop.ctime }}</text>
              <text class="agent_name" v-if="prop.crm_user">
                {{ prop.crm_user && prop.crm_user.user_name }}/{{
                  prop.crm_user && prop.crm_user.department
                }}
              </text>
            </view>
            <view class="content">
              <text v-html="prop.content"></text>
            </view>
            <view class="img_list">
              <image @click="preFollowImgs(prop.images, index)" v-for="(img, index) in prop.images" :key="index"
                :src="img | imageFilter('w_80')" mode="aspectFill" />
            </view>
          </view>
        </timeLine>
        <!-- #endif -->
        <!-- #ifndef H5 -->
        <timeLine v-if="follow_list.length > 0">
          <view class="item" v-for="prop in follow_list" :key="prop.id">
            <view class="dot" v-if="prop.is_finish !== 0"> </view>
            <view class="timeline_item">
              <view class="time flex-row items-center space-between">
                <text>{{ prop.ctime }}</text>
                <text class="agent_name" v-if="prop.crm_user">
                  {{ prop.crm_user && prop.crm_user.user_name }}/{{
                    prop.crm_user && prop.crm_user.department
                  }}
                </text>
              </view>

              <view class="content">
                <text>{{ prop.content }}</text>
              </view>
              <view class="img_list">
                <image @click="preFollowImgs(prop.images, index)" v-for="(img, index) in prop.images" :key="index"
                  :src="img | imageFilter('w80')" mode="aspectFill" />
              </view>
            </view>
          </view>
        </timeLine>
        <!-- #endif -->
        <view class="see_more" v-if="follow_list.length > 0" @click="toSeeMore">查看更多</view>
        <view class="see_more no_more" v-else>没有更多了</view>
      </template>
      <!-- <template v-if="vipInfo.is_vip == 0 || vipInfo.is_vip == 2">
        <view class="no_vip items-center">
          <image src="/static/img/<EMAIL>"></image>
          <text class="no_vip_text"> 该板块内容仅对会员开放 </text>
        </view>
      </template> -->
    </view>
    <!-- 钥匙记录 -->
    <view v-show="tab_type === 1" class="container">
      <view class="key_tab flex-row items-center">
        <view class="key_tab_item" @click="toWeituoKey(2)"> 添加钥匙 </view>
        <view class="key_tab_item" @click="toWeituoKey(1)"> 添加委托 </view>
        <view class="key_tab_item" @click="toWeituoKey(3)"> 独家委托 </view>
      </view>
      <timeLine v-if="key_list.length > 0" :list="key_list" v-slot="{ prop }">
        <view class="timeline_item">
          <view class="time flex-row items-center space-between"><text>{{ prop.ctime }} </text> <text>{{ prop.user_name
          }}</text></view>
          <view class="user_info">
            <!-- <view class="flex-1">
              <view class="name"> 添加人：</view>
            </view> -->
            <!-- <view class="tel" v-if="prop.agent.tel" @click="mkPhone(prop.agent.tel)">
                <icons type="dianhua" size="30" color="#2d84fb"></icons>
                {{ prop.agent.tel }}</view
              > -->
          </view>
          <view class="content">
            <text>钥匙编号：{{ prop.key_num }}</text>
          </view>
        </view>
      </timeLine>

      <!-- <view class="see_more" v-if="key_list.length > 0" @click="toSeeMore">查看更多</view> -->
      <view class="see_more no_more" v-else>没有更多了</view>
    </view>
    <!-- 审批 -->
    <view v-show="tab_type === 5" class="container approve">
      <template v-if="approveList.length > 0">
        <view class="approve_list">
          <view class="approve_item" v-for="(item, index) in approveList" :key="index">
            <approveItem :item="item"></approveItem>
          </view>
        </view>
      </template>
      <template v-if="approveList.length == 0">
        <view class="see_more no_more">没有更多了</view>
      </template>
    </view>

    <!-- 访客浏览 -->
    <view v-show="tab_type === 3" class="container" :class="{ no_vip_member: user_info.vip_id == 0 }">
      <!-- <view>访客浏览</view> -->
      <template>
        <timeLine v-if="detail.browse && detail.browse.length > 0" :list="detail.browse" v-slot="{ prop }">
          <view class="timeline_item">
            <view class="time">{{ prop.ctime }}</view>
            <view class="user_info">
              <image class="avatar" :src="prop.head_image" mode="aspectFill" />
              <view class="name">{{ prop.cname }}</view>
            </view>
            <view class="content">{{ prop.content || '浏览了该房源' }}</view>
          </view>
        </timeLine>
        <view class="nomore" v-else>还没有访客浏览记录</view>
      </template>
    </view>

    <!-- 底部菜单 -->
    <view class="container top-line flex-row footer_btn_group">
      <!-- <view class="option_btn option_btn_cloumn flex-1" @click="goBack">
        <image src="/static/img/<EMAIL>"></image>
        <text>返回</text>
      </view> -->
      <view class="option_btn flex-1">
        <my-button type="primary" :round="false" size="big" @click="concatOwner">电话</my-button>
      </view>
      <view class="option_btn flex-1">
        <my-button type="primary" :round="false" size="big" :plain="true" @click="followPrivate(detail)">跟进</my-button>
      </view>
      <view class="option_btn more_btn flex-1">
        <Dropdown :is_customer_titleBar="is_customer_titleBar" ref="dropdown">

          <my-button type="primary" :round="false" size="big" :plain="true">
            <text class="more_con row">更多 ⋮ </text>
          </my-button>


          <view class="options_list" slot="dropdown_list">
            <!-- <DropdownItem @click="addToLianmai">发布到联卖</DropdownItem> -->

            <!-- 已经上架显示带看 -->
            <!-- <DropdownItem
              v-if="detail.lmstatus"
              plain
              :round="false"
              size="big"
              @click="addShowing(detail)"
              >发布集中带看</DropdownItem
            > -->
            <DropdownItem plain :round="false" size="big" @click="houseGroup">一键房源群发</DropdownItem>
            <DropdownItem plain :round="false" size="big" @click="applyApprove('13_8')">标无效</DropdownItem>
            <!-- <template v-if="website_id==176"> -->
            <DropdownItem plain :round="false" size="big" @click="applyApprove">审批</DropdownItem>
            <!-- </template> -->
            <DropdownItem plain :round="false" size="big" @click="copyPrivate">复制</DropdownItem>
            <!-- <DropdownItem
              plain
              :round="false"
              size="big"
              v-if="detail.is_share == 0 && user_info.is_share && user_info.company_id > 0"
              @click="setCompanyHouse(detail)"
              >设为公司房源</DropdownItem
            > -->
            <DropdownItem v-if="detail.current_login_user &&
              detail.current_login_user.privilege &&
              detail.current_login_user.privilege.auth_is_top
              " plain :round="false" size="big" @click="houseTop(detail)">聚焦房源</DropdownItem>
            <!-- <view class="add_btn flex-1 flex-row items-center justify-center" @click="setTixing"
        >添加提醒</view
      > -->
            <DropdownItem plain :round="false" size="big" @click="setTixing(detail)">添加提醒</DropdownItem>
          </view>
        </Dropdown>
      </view>
    </view>
    <!-- 申请 -->
    <my-popup position="bottom" :show="show_apply" @close="show_apply = false">
      <apply :apply_options="applyInfo" :house_id="id" ref="apply_form" @success="applySuccess"></apply>
    </my-popup>
    <!--电话选择 -->
    <my-popup position="bottom" @close="show_tel_pop = false" :show="show_tel_pop">
      <view class="tel_pop">
        <view class="tel_pop_title bottom-line">{{ detail.show_whr_tel == 1 ? '联系维护人' : '联系业主' }}
        </view>
        <view class="tel_pop_list">
          <view class="contact-info" v-for="(item, index) in detail.tel" :key="index">
            <text>{{ item.owner }} {{ item.owner_tel }}</text>
            <view>
              <text @click="mkPhone(item)">电话</text>
              <text @click="callPhone(item)">外呼</text>
            </view>
          </view>
          <view class="tel_pop_title bottom-line">跟进内容 </view>
          <view class="foll_content flex-row">
            <textarea class="flex-1" style="margin-top: 20rpx;" v-model="follow_params1.content"
              placeholder="请输入跟进内容（企业内公开）" rows="3"></textarea>
          </view>
          <view class="tel_pop_item bottom-line flex-row items-center justify-center" @click="submitFollow">

            <myButton type="primary" :round="false" size="big">提交跟进 </myButton>
          </view>
          <!-- <view class="tel_pop_item bottom-line" @click="show_tel_pop = false"> 取消 </view> -->
        </view>
      </view>
    </my-popup>
    <!--电话选择 -->
    <my-popup position="bottom" ref="show_edit_pop" @close="show_edit_pop = false" :show="show_edit_pop">
      <view class="edit_pop">
        <view class="tel_pop_title bottom-line"> 房源维护 </view>
        <view class="flex-row items-center edit_item" :class="{ 'bottom-line': index != edit_list.length - 1 }"
          v-for="(item, index) in edit_list" :key="item.id" @click="toPath(item)">
          <view class="edit_img">
            <image :src="item.icon" mode="widthFix"> </image>
          </view>
          <view class="name">
            {{ item.name }}
          </view>
        </view>
      </view>
    </my-popup>
    <my-popup position="center" ref="show_vr_pop" @close="show_vr_pop = false" :show="show_vr_pop">
      <view class="vr_pop" v-if="show_vr_pop">
        <view class="tel_pop_title bottom-line"> 添加VR </view>
        <view class="vr_pop_form">
          <view class="vr_pop_form_item flex-row items-center">
            <view class="label">选择相机</view>
            <view class="flex-1">
              <selectDown valueName="name" :list="cameraList" :multiple="false" @input="changeSelect" defaultValue=""
                placeholder="请选择相机">
              </selectDown>
            </view>
          </view>
          <view class="vr_pop_form_item flex-row items-center">
            <view class="label">描述</view>
            <view class="desc flex-1">
              <textarea rows="5" v-model="vr_params.memo"></textarea>
            </view>
          </view>
          <view class="vr_pop_form_item flex-row items-center">
            <view class="btns flex-row items-center flex-1 space-between">
              <view class="btn confirm" @click="submitVR"> 确认 </view>
              <view class="btn cancel" @click="cancelVR"> 取消 </view>
            </view>
          </view>
        </view>
      </view>
    </my-popup>
    <myPopup ref="showPhone" :show="show_phone_pop" position="bottom" @close="show_phone_pop = false">
      <view class="p_con" v-if='show_phone_pop'>
        <view class="title">
          <uni-icons type="left" size="30" color="#333" @click="goBackFollow"></uni-icons>
          <text style="color: #333;">拨打电话</text>
          <text style="width: 60rpx;"></text>
        </view>
        <view class="p_content">
          <view class="p_item flex-row items-center">
            <view class="label" style="margin-right: 10px;"> 选择外显号码 </view>
            <view class="value flex-1">
              <uni-data-select v-model="show_id" :localdata="phoneList" @change="change"></uni-data-select>
            </view>
          </view>
          <view class="btns flex-row">
            <view class="btn flex-1" @click="conMakePhone"> 确认拨打 </view>
          </view>
        </view>
      </view>
    </myPopup>
  </view>
</template>

<script>
import myButton from './components/myButton'
// import icons from '@/components/ui/icons'
import Tabs from './components/Tabs'
import timeLine from './components/timeLine'
import myUpload from './components/upload'
import apply from './components/applyPrivate'
import Dropdown from './components/Dropdown'
import DropdownItem from './components/DropdownItem'
import myPopup from '@/components/myPopup'
import selectDown from './components/w-select'
import approveItem from './components/approveItem'
// import { getUserInfo } from '@/page_outside/utils/user.js'
import {
  copyPrivate,
  tixing,
  followPrivate,
  addToLianmai,
  setCompanyHouse,
  // houseDel,
  houseTop,
} from '@/page_outside/private.js'
export default {
  components: {
    myButton,
    Tabs,
    timeLine,
    selectDown,
    // icons,
    Dropdown,
    apply,
    DropdownItem,
    myPopup,
    myUpload,
    approveItem,
    // loadMore,
  },
  data: () => ({
    userInfo: { tel: '', name: "" },
    show_phone_pop: false,
    phoneList: [],
    show_id: null,
    id: '',
    swiperCurrent: 0,
    img_group: [],
    focus_cate: 'vr',
    current_img_cateid: 1,
    detail: {
      shi: '',
      ting: '',
      wei: '',
      pic: [],
      video: [],
      vr: [],
      is_release: 1,
    },
    tab_type: 2,
    tab_list: [
      {
        type: 2,
        name: '跟进记录',
      },
      // {
      //   type: 5,
      //   name: '审批',
      // },
    ],
    img_tab_type: 1,
    img_tab_lists: [
      {
        type: 1,
        name: '室内图',
      },
      {
        type: 2,
        name: '户型图',
      },
      {
        type: 3,
        name: '室外图',
      },
    ],
    imgs: [
      {
        type: 1,
        imgs: [],
      },
      {
        type: 2,
        imgs: [],
      },
      {
        type: 3,
        imgs: [],
      },
    ],
    follow_loading: 'loadend',
    follow_params: {
      page: 1,
      rows: 2,
      is_system: 0
    },
    follow_params1: {
      content: '',
      info_id: '',
      effect_type: 1,
      type: 1
    },
    follow_list: [],
    tel_res: {},
    is_customer_titleBar: true,
    show_apply: false,
    applyInfo: {},
    show_tel_pop: false,
    //  action: '/common/house/uploadByMobile',
    upload_api: '/common/file/upload/admin',
    levelArr: ['A', 'B', 'C'],
    levelIndex: 0,
    approveList: [],
    upInfo: {
      category: 6,
    },
    photoList: [],
    show_fanghao: false,
    key_list: [],
    focus: [],
    focusLen: 0,
    cateActive: 1,
    cusBtn: [],
    show: false,
    website_id: 0,
    showContent: false,
    show_edit_pop: false,
    edit_list: [
      {
        id: 1,
        name: "修改房源信息",
        icon: "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/house/house_edit.png",
      }, {
        id: 2,
        name: "上传图片",
        icon: "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/house/add_img.png",
      }, {
        id: 3,
        name: "VR拍摄",
        icon: "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/house/phone_photo.png",
      },
    ],
    cameraList: [],
    show_vr_pop: false,
    vr_params: {
      memo: "",
      photographer_id: ""
    },
    uploadJudge: 0
  }),
  computed: {
    user_info() {
      return this.$store.state.user_info
    },
  },
  onLoad(options) {

    let token = uni.getStorageSync("wxwork_token");
    if (!token) {
      if (this.$isWxWork() == 'wxwork') {
        return
      } else {
        localStorage.setItem('backUrl', location.href)
        this.$router.push("https://yun.tfcs.cn")
      }
    }
    if (options.id) {
      this.id = options.id
      this.follow_params1.info_id = options.id
      this.getData()

    }
    this.showContent = true

    if (options.website_id) {
      this.website_id = options.website_id
    }
    uni.$on('loginSuccess', () => {
      this.getData()
    })

    uni.$on('getDataAgain', () => {
      this.getData()
      // this.getPhotos()
    })
  },
  onUnload() {
    uni.$off('loginSuccess')
    uni.$off('getDataAgain')
  },
  onShow() {
    // this.getUnFollow()
  },
  filters: {
    // cateName (value) {
    //   switch (value) {
    //     case 1:
    //       return '室内图'
    //     case 2:
    //       return '户型图'
    //     case 3:
    //       return '室外图'
    //     default:
    //       return '其他图片'
    //   }
    // },
    priceFilter(val) {
      if (!val) {
        return '面议'
      }
      if (isNaN(Number(val))) {
        return val
      }
      let price = val / 10000
      if (parseInt(price) == parseFloat(price)) {
        return parseInt(price)
      } else {
        return Number(price).toFixed(2)
      }
    },
    priceFilterRent(price) {
      if (isNaN(Number(price))) {
        return price
      }
      if (parseInt(price) == parseFloat(price)) {
        return parseInt(price)
      } else {
        return Number(price).toFixed(2)
      }
    },

    statusFilter(val) {
      if (val) {
        return val.substr(0, 2)
      }
      return val
    },
    formatPrice(val, type) {
      console.log(val, type);
      let unit = "元"

      if (Number(val) >= 10000) {
        if (type == 1 || type == 3) {
          unit = "万元"
        } else {
          unit = "万元/月"
        }
        return Number(val) / 10000 + unit
      } else {
        if (type == 1 || type == 3) {
          unit = "元"
        } else {
          unit = "元/月"
        }
        return val + unit
      }
    }
  },
  methods: {
    goBackFollow() {
      this.show_phone_pop = false
      this.show_tel_pop = true
    },
    callPhone(item) {
      console.log(item);
      this.userInfo.tel = item.owner_tel
      this.userInfo.name = item.owner
      this.show_tel_pop = false
      this.getphoneList(item)
      this.show_phone_pop = true
    },
    change(e) {
      console.log(e);
    },
    getphoneList() {
      this.$ajax.get('/admin/call_clue/getSeatsPhone', {}, res => {
        if (res.statusCode == 200) {
          this.phoneList = res.data.map(item => {
            item.value = item.show_id
            item.text = item.phone
            return item
          })
          this.show_id = this.phoneList[0].show_id
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
            duration: 2000
          })
        }
      })
    },
    postData(data) {
      let params = {
        call_phone_id: data.call_id,
        call_name: this.userInfo.name,
        call_phone: data.callee,
        call_show_phone: data.caller,
        type: 2,
        house_id: this.detail.id
      }
      this.$ajax.post("/common/call_module/addCallOutRecord", params, res => {
        console.log(res);
      })
    },
    conMakePhone() {
      let params = {
        show_id: this.show_id,
        phone: (this.userInfo.tel + '').replace(" ", '').replace(" ", '')
      }
      let api = `/admin/call_clue/directCallPhone`
      // if (this.telType == 3) {
      //   api = '/admin/call_clue/anyOneCallByCrm'
      //   params = {
      //     show_id: this.show_id,
      //     phone: (this.userInfo.tel + '').replace(" ", '').replace(" ", ''),
      //     client_id: this.client_id
      //   }
      // }
      params = {
        show_id: this.show_id,
        phone: (this.userInfo.tel + '').replace(" ", '').replace(" ", ''),
        client_id: this.client_id
      }

      this.$ajax.post(api, params, res => {
        if (res.statusCode == 200) {
          this.call_info = res.data
          if (res.data.call_id) {
            this.postData(res.data)
          }
          uni.makePhoneCall({
            phoneNumber: res.data.telX,
            success: () => {
              console.log("正在拨打中 请稍后");
            },
          });
          this.show_phone_pop = false
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
            duration: 2000
          })
        }
      })
    },
    getData() {
      this.$ajax.get(
        `/admin/house/privateHousesDetail/${this.id}`,
        {},
        (res) => {
          this.loading = false
          uni.hideLoading()
          if (res.statusCode === 200) {
            // if (!res.data.vr) {
            //   res.data.vr = []
            // }
            if (!res.data.video) {
              res.data.video = []
            }
            console.log(res.data, '-------')
            this.detail = res.data
            this.detail.trade_type_status = res.data.trade_type
            this.levelIndex = this.levelArr.findIndex((item) => item == this.detail.level)
            if (this.detail.unilateral_agent == 1 && this.detail.unilateral_agent_auth == 0) {
              if (this.detail.fanghao) {
                this.detail.fanghao = this.detail.fanghao.replace(/\d/g, "**");
              }
              if (this.detail.danyuan) {
                this.detail.danyuan = this.detail.danyuan.replace(/\d/g, "*");
              }
              if (this.detail.loudong) {
                this.detail.loudong = this.detail.loudong.replace(/\d/g, "**");
              }
            } else {
              if (this.detail.fanghao) {
                this.fanghao = this.detail.fanghao
                this.detail.fanghao = this.detail.fanghao[0] + "**";
              }
            }

            if (res.data.share) {
              this.share = res.data.data.share
            } else {
              this.share = {
                title: this.detail.community_name,
                content: this.detail.community_name,
                pic: this.detail.pic && this.detail.pic.length > 0 ? this.detail.pic[0] : '',
              }
            }
            this.getPhotos()
            this.$ajax.get("/admin/house/remindPhoneFollow", {}, res => {
              if (res.statusCode == 200) {
                this.unfollowInfo = res.data
                if (res.data.remindPhoneFollow == 1 && res.data.count > 0 && res.data.info && res.data.info.info_id == this.detail.id) {
                  this.concatOwner()
                } else if (res.data.remindPhoneFollow == 1 && res.data.count > 0 && res.data.info && res.data.info.info_id != this.detail.id) {
                  this.$navigateTo(`/house/detail?id=${this.unfollowInfo.info.info_id}`)
                }
              }
            })
            if (this.detail.is_share) {
              this.tab_list = [
                {
                  type: 2,
                  name: '跟进记录',
                },
                {
                  type: 1,
                  name: '钥匙委托',
                },
                {
                  type: 4,
                  name: '维护记录',
                },
              ]
            }
            // this.getUserInfo()
            this.getFollowList()
            // this.handleFocusData(this.detail.pic, this.detail.video, this.detail.vr)
          } else {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
              mask: true,
            })
          }
        },
        (err) => {
          uni.hideLoading()
          console.log(err)
          this.loading = false
        }
      )
    },
    getPhotos() {
      this.$ajax.get(
        `/admin/house/housePhotoSk/${this.id}`,
        {}, res => {
          if (res.statusCode == 200) {
            this.uploadJudge = res.data.can_up;
            let photoList = []
            if (res.data.photo_first) {
              photoList = res.data.photo_first.list?.concat(res.data.photos)
            } else {
              photoList = res.data.photo_first?.concat(res.data.photos)
            }

            this.photoList = photoList
            this.handleImgs(photoList)
          }
          uni.hideLoading()
          this.show = true
        }, () => { uni.hideLoading() })
    },
    handleImgs(pics = this.detail.photos) {
      if (pics == undefined) {
        return
      }
      this.cusBtn = []
      let pic = [],
        vr = [],
        huxing = [],
        shiwai = []
      pics.map((item) => {
        if (item.category_id == 1) {
          pic.push({
            type: 1,
            name: "室内图",
            url: item.url
          })
        } else if (item.category_id == 2) {
          huxing.push({
            type: 2,
            name: "户型图",
            url: item.url
          })
        } else {
          shiwai.push({
            type: 3,
            name: "室外图",
            url: item.url
          })
        }
      })
      if (this.detail.vr) {
        vr = [
          {
            type: "vr",
            name: 'VR',
            url: this.detail.vr_cover
          }
        ]
      }
      this.focus = [...vr, ...pic, ...huxing, ...shiwai]
      // this.focus = pic.concat(huxing).concat(shiwai)
      this.focusLen = this.focus.length
      this.vrLen = vr.length
      this.picLen = pic.length
      this.huxingLen = huxing.length
      this.shiwaiLen = shiwai.length
      if (this.vrLen > 0) {
        this.cusBtn.push({
          name: 'VR',
          value: 'vr',
        })

      }
      if (this.picLen > 0) {
        this.cusBtn.push({
          name: '室内图',
          value: 1,
        })

      }

      if (this.huxingLen > 0) {
        this.cusBtn.push(
          {
            name: '户型图',
            value: 2,
          })

      }
      if (this.shiwaiLen > 0) {
        this.cusBtn.push({
          name: '室外图',
          value: 3,
        })
      }
      this.cateActive = this.cusBtn[0].value
    },
    swiperChange(e) {
      this.swiperCurrent = e.detail.current
      this.cateActive = this.focus[this.swiperCurrent].type
    },
    submitFollow() {
      let api = '/admin/house/addPrivateHouseFollow'
      this.$ajax
        .post(api, this.follow_params1, (res) => {
          this.loading = false
          if (res.statusCode === 200) {
            uni.showToast({
              title: '提交成功',
              icon: 'success',
            })
            this.show_tel_pop = false

          } else {
            uni.showToast({
              title: res.data.message || "添加失败",
              icon: 'none',
            })
          }
        }, () => {
          this.loading = false
        })
    },
    switchFocus(type) {

      this.cateActive = type
      switch (type + '') {
        case 'vr':
          this.swiperCurrent = 0
          break
        case '1':
          this.swiperCurrent = this.vrLen
          break
        case '2':
          this.swiperCurrent = this.vrLen + this.picLen
          break
        case '3':
          this.swiperCurrent = this.vrLen + this.picLen + this.huxingLen
          break

        default:
          this.swiperCurrent = 0
      }
    },
    showFollow(type = 'detail') {
      this.$navigateTo(`/house/follow_up?follow_id=${this.id}&from=private&type=${type}`)
      // this.show_follow = true
      // this.$refs.follow_form.init()
    },
    handleFocusData(imgs = [], videos = [], vrs = []) {
      var img_group = []
      imgs
        .sort((a, b) => a.category_id - b.category_id) //先根据category_id排序
        .forEach((item) => {
          if (img_group.length === 0) {
            img_group.push({ cate_id: item.category_id, imgs: [item] })
            return
          }
          // 查找img_group中是否已经有这个图片分类
          let current_index = img_group.findIndex((img) => img.cate_id === item.category_id)
          if (current_index > -1) {
            img_group[current_index].imgs.push(item)
          } else {
            img_group.push({ cate_id: item.category_id, imgs: [item] })
          }
        })
      this.img_group = img_group
      this.focus = [...vrs, ...videos, ...imgs]
      if (vrs.length > 0) {
        this.focus_cate = 'vr'
      } else if (videos.length) {
        this.focus_cate = 'video'
      } else if (imgs.length) {
        this.focus_cate = 'img'
        this.current_img_cateid = imgs[0].category_id
      }
    },
    preImgs(item = {}) {
      if (item.type == "vr") {
        if (!this.detail.vr) {
          uni.showToast({
            title: "暂无vr 请先拍摄",
            icon: "none"
          })
          return
        }
        this.$navigateTo('/house/vr_detail?url=' + encodeURIComponent(this.detail.vr))
        return
      }
      // if (this.detail.unilateral_agent == 1 && this.detail.unilateral_agent_auth == 0) {
      //   return
      // }
      this.$navigateTo('/house/photos?id=' + this.id)
      // uni.previewImage({
      //   current: index,
      //   urls: this.detail.pic.map((item) => formatImg(item.url, 'w8601')),
      //   indicator: 'number',
      // })
    },
    preVideo() { },
    toVr() { },
    getFollowList(page) {
      if (page) {
        this.follow_params.page = page
      }
      if (this.follow_params.page === 1) {
        this.follow_list = []
      }
      this.follow_loading = 'loading'
      this.$ajax.get(
        `/admin/house/privateHouseFollows/${this.id}`,
        this.follow_params,
        (res) => {
          this.follow_loading = 'loadend'
          if (res.statusCode === 200) {
            this.follow_list = this.follow_list.concat(res.data)
            console.log(this.follow_list, '???????');
            if (res.data.length < this.follow_params.rows) {
              this.follow_loading = 'nomore'
            }
          } else {
            this.follow_loading = 'nomore'
          }
        },
        () => {
          this.follow_loading = 'loaderror'
        }
      )
    },
    preFollowImgs(image_list, index) {
      uni.previewImage({
        current: index,
        urls: image_list.map((item) => formatImg(item, 'w8601')),
        indicator: 'number',
      })
    },
    addToLianmai() {
      addToLianmai({ id: this.id })
    },
    concatOwner() {
      this.$ajax.post("/admin/house/houseTelLog2", { id: this.detail.id }, (res) => {
        console.log(res);
        if (res.statusCode == 200) {
          if (res.data && res.data.tel.length) {
            if (res.data.log_id > 0) {
              this.follow_params1.log_id = res.data.log_id
            }
            this.$set(this.detail, "tel", res.data.tel)
            this.show_tel_pop = true
          } else {
            uni.showToast({
              title: "暂无联系电话",
              icon: 'none'
            })
          }

        }
      })

    },
    mkPhone(item) {
      uni.makePhoneCall({
        phoneNumber: item.owner_tel
      })
    },
    static(item) {
      this.$ajax.post(
        `/v1/wapLm/privateHousesDial/${this.id}`,
        {
          owner: item.owner,
          owner_tel: item.owner_tel,
        },
        () => {
          this.getFollowList()
        }
      )
    },
    toPage(name, query = {}) {
      this.$navigateTo({
        name,
        query,
      })
    },
    goBack() {
      this.$navigateBack()
    },
    showApply() {
      this.applyInfo = {
        mianji: this.detail.mianji,
        trade_type: this.detail.trade_type,
        sale_price: this.detail.sale_price ? this.detail.sale_price / 10000 : 0,
        rent_price: this.detail.rent_price,
      }
      this.show_apply = true
      this.$refs.apply_form.getApplyOptions()
    },
    applySuccess() {
      this.show_apply = false
      this.getData()
    },
    toSeeMore() {
      this.$navigateTo(`/house/followList?id=${this.id}&from=private&is_system=${this.tab_type}`)
      // this.$navigateTo({
      //   name: 'followList',
      //   query: {
      //     id: this.id,
      //     from: 'private',
      //   },
      // })
    },
    copyBianhao() {
      this.$copyText(this.detail.id, () => {
        uni.showToast({
          title: '复制成功',
          icon: 'none',
        })
      })
    },
    showEditPop() {
      this.show_edit_pop = true
    },
    toPath(e) {
      switch (+e.id) {
        case 1:
          this.editHouse()

          break;
        case 2:
          if (this.uploadJudge) {
            this.preImgs({ type: "image" })
          } else {
            this.showFollow("shikan")
          }


          break;
        case 3:
          this.getCameraList()
          this.vr_params.photographer_id = ''
          this.vr_params.memo = ''

          this.show_vr_pop = true
          // this.preImgs({ type: "vr" })

          break;

        default:
          break;
      }
      this.show_edit_pop = false
    },
    getCameraList() {
      this.$ajax.get(`/admin/house/vr/cameraListByDep/${this.id}`, {}, (res) => {
        console.log(res);
        if (res.statusCode == 200) {
          this.cameraList = res.data
        }
      })
    },
    editHouse() {
      if (this.detail.unilateral_agent == 1 && this.detail.unilateral_agent_auth == 0) {
        uni.showToast({
          title: '请联系房源维护人',
          icon: "none"
        })
        // this.$message.warning("请联系房源维护人")
        return
      }
      this.$navigateTo(`/house/edit?id=${this.detail.id}`)
      // this.$navigateTo({
      //   name: 'edit_private',
      //   query: {
      //     id: this.detail.id,
      //   },
      // })
    },
    applyApprove(type = '') {
      this.$navigateTo("/house/applyApprove?id=2&house_id=" + this.detail.id + "&type=" + type)
    },
    uploadSuccess(e) {
      let pic = []
      e.map((item) => {
        let obj = { category_id: this.img_tab_type, descp: '', is_cover: 0, url: item }
        pic.push(obj)
      })
      this.uploadImg(JSON.stringify(pic))
    },
    uploadImg(pic) {
      this.$ajax.post(`/admin/house/privateHousesUpPic/${this.id}`, { pic }, (res) => {

        if (res.statusCode == 200) {
          uni.showToast({
            title: res.message || '上传成功',
            icon: 'none',
          })
          this.getPhotos()
          this.getFollowList(1)
        }
      })
    },
    copyPrivate() {
      this.$ajax.get(`/admin/house/shareHouse/${this.detail.id}`, {}, res => {
        if (res.statusCode == 200) {
          // this.$set(this.detail, 'current_login_user', res.data)
          copyPrivate(this.detail)
        } else {
          uni.showToast({
            title: res.data.message || '请求失败',
            icon: 'none'
          })
        }
      })
      // copyPrivate(this.detail)
    },
    followPrivate() {
      followPrivate(this.detail, 'private')
    },
    setLevels(e) {
      if (this.detail.unilateral_agent == 1 && this.detail.unilateral_agent_auth == 0) {
        uni.showToast({
          title: '请联系房源维护人',
          icon: 'none'
        })
        return
      }
      this.$ajax.post(
        '/admin/house/editPrivateHouse',
        { id: this.id, level: this.levelArr[e.detail.value] },
        (res) => {

          if (res.statusCode == 200) {
            uni.showToast({
              title: res.data.message || '设置成功',
              icon: 'none',
            })
            this.$set(this.detail, 'level', this.levelArr[e.detail.value])
            // this.getData()
          }
        }
      )
    },

    setTixing() {
      tixing(this.detail)
      // this.$navigateTo(`/popup/remind?remind_id=${e.id}`)
    },
    setCompanyHouse() {
      setCompanyHouse(this.detail).then(() => {
        this.getData()
      })
    },
    houseTop() {
      houseTop(this.detail).then(() => {
        this.getData()
      })
    },
    houseGroup() {
      this.$navigateTo("/house/house_send?house_id=" + this.detail.id + "&protect=" + this.detail.protect)
      // this.$navigateTo({
      //   name: 'house_send',
      //   query: {
      //     from: 'index',
      //     house_id: this.detail.id,
      //   },
      // })
    },
    changeTabType(e) {
      if (e == 1) {
        this.getKeyList()
      }
      if (e == 2) {
        this.follow_params.is_system = 0
        this.getFollowList(1)
      }
      if (e == 4) {
        this.follow_params.is_system = 1
        this.getFollowList(1)
      }
      if (e == 5) {
        this.getApproveList()
      }
    },
    getKeyList() {
      this.$ajax.get(`/admin/house/keyList/${this.detail.id}`, {}, res => {
        if (res.statusCode == 200) {
          this.key_list = res.data.list
        }
      })
    },
    getApproveList() {
      this.$ajax.get(`/admin/house/approveList/2`, { page: 1, rows: 5 }, (res) => {
        if (res.statusCode == 200) {
          this.approveList = res.data.data
        }
      })
    },
    showFanghao() {
      this.$ajax.get(`/admin/house/checkRoomNumber/${this.detail.id}`, {}, (res) => {
        if (res.statusCode == 200) {
          this.show_fanghao = true;
          this.$set(this.detail, "fanghao", res.data.fanghao || this.fanghao);
        }
      })
    },
    toWeituoKey(type) {
      // if (this.detail.unilateral_agent == 1 && this.detail.unilateral_agent_auth == 0) {
      //   uni.showToast({
      //     title: '请联系房源维护人',
      //     icon: "none"
      //   })
      //   // this.$message.warning("请联系房源维护人")
      //   return
      // }
      if (type == 2) {
        this.$navigateTo("/house/weituo?type=" + type + "&house_id=" + this.detail.id + "&protect=" + this.detail.protect)
      } else if (type == 1) {
        this.applyApprove('16_0')
      } else if (type == 3) {
        this.applyApprove('17_0')
      }

    },
    changeSelect(e) {
      if (e.length) {
        this.vr_params.photographer_id = e[0].id
      } else {
        this.vr_params.photographer_id = ''
      }
    },
    submitVR() {
      this.vr_params.id = this.id
      this.$ajax.post(`/admin/house/vr/addOrder`, this.vr_params, (res) => {
        if (res.statusCode == 200) {
          uni.showToast({
            title: res.message || res.data?.message || "订单创建成功",
            icon: "none"
          })
          this.show_vr_pop = false
        } else {
          uni.showToast({
            title: res.data?.message || "订单创建失败",
            icon: "none"
          })
        }
      })
    },
    cancelVR() {
      this.show_vr_pop = false
    }
  },
}
</script>

<style lang="scss">
.contact-info {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 20rpx;
  color: #666;

  view {
    display: flex;
    flex-direction: row;
    align-items: center;

    text {
      font-size: 28rpx;
      padding: 10rpx 20rpx;
      border-radius: 3px;
    }

    &>text:nth-child(1) {
      margin-right: 40rpx;
      color: #2d84fb;
      background-color: #c6e1ff;
      box-shadow: 0px 1px 2px #2d84fb;
    }

    &>text:nth-child(2) {
      color: #23c0a6;
      background-color: #befeea;
      box-shadow: 0 1px 2px #62aca0;
    }
  }

  &>text:nth-child(1) {
    font-size: 32rpx;
    margin-right: 40rpx;
    box-shadow: 0;
  }
}

.p_con {
  padding: 40rpx;
  background: #fff;
  border-radius: 8rpx;
  position: relative;
  padding-top: 140rpx;
  width: 100vw;

  /* height: 50vh; */
  .title {
    font-size: 40rpx;
    font-weight: 600;
    padding: 40rpx 0;
    text-align: center;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    text-align: center;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .p_content {
    .p_item {
      .label {
        margin-right: 5rpx;
      }
    }

    .btns {
      margin-top: 200rpx;

      .btn {
        text-align: center;
        background: #2d84fb;
        padding: 30rpx 0;
        color: #fff;
        border-radius: 10rpx;
      }
    }
  }
}

.morebtn {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border: 2rpx solid #2d84fb;
  height: 100%;
}

// 顶部焦点图
.detail {
  padding-bottom: 125rpx;
}

.mr12 {
  margin-right: 12rpx;
}

.container {
  padding: 24rpx 48rpx;

  &.other_info {
    padding-top: 0;

    .label {
      margin-bottom: 48rpx;
    }
  }

  &.btns {
    .add_btn {
      padding: 20rpx 0;
      border: 2rpx solid #2d84fb;
      color: #2d84fb;
      border-radius: 10rpx;

      ~.add_btn {
        margin-left: 20rpx;
      }
    }
  }

  &.approve {
    // padding-top: 0;
    background: #f7f7f7;
  }

  &.no_vip_member {
    padding-bottom: 130rpx;
  }

  .approve_list {
    background: #f7f7f7;

    .approve_item {
      // margin-bottom: 20rpx;
    }
  }

  .no_vip {
    margin: 68rpx 0;

    image {
      width: 280rpx;
      height: 280rpx;
    }

    .no_vip_text {
      font-size: 28rpx;
      margin-top: 24rpx;
      color: #8a929f;
    }
  }
}

.shoucang_box {
  .share {
    font-weight: normal;
    font-size: 22rpx;
    color: #959ca8;

    margin-right: 24rpx;

    .share_text {
      margin-left: 4rpx;
    }

    image {
      width: 32rpx;
      height: 32rpx;
    }
  }
}

.title_shoucang {
  font-weight: normal;

  image {
    width: 32rpx;
    height: 32rpx;
  }

  .shoucang {
    font-size: 22rpx;
    color: #959ca8;
    margin-left: 4rpx;
  }
}

.main_info {
  .title {
    // margin-bottom: 24rpx;
    line-height: 1.3;
    font-size: 40rpx;
    font-weight: bold;
    color: #2e3c4e;
    overflow: hidden;

    .title_con {
      font-size: 40rpx;
      line-height: 1.5;
      margin: 24rpx 0;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .right {
      font-size: 24rpx;
      color: #ffffff;
      background: #2d84fb;
      padding: 12rpx 24rpx;
      border-radius: 8rpx;
    }

    .status {
      padding: 6rpx 12rpx;
      background: #e5f2ff;
      font-size: 22rpx;
      border-radius: 8rpx;
      margin-right: 16rpx;
      color: #3d97fc;

      &.status1 {
        background: #ffe5e5;
        color: #fe1717;
      }

      &.status2 {
        background: #e5f2ff;
        color: #3d97fc;
      }
    }
  }

  .huxing {
    line-height: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-bottom: 18rpx;
    white-space: nowrap;

    .value {
      font-size: 36rpx;
      font-weight: bold;
      color: #ff656b;
    }

    .unit {
      font-size: 26rpx;
      margin-bottom: 2rpx;
      color: #ff656b;
    }
  }

  .huxing_price {
    align-items: flex-end;
    margin-right: 24rpx;
  }

  .stw {
    align-items: flex-end;
    margin-right: 24rpx;
  }

  .mianji {
    align-items: flex-end;
    font-size: 22rpx;
    color: #ff656b;
  }

  .btn {
    line-height: 64rpx;
    padding: 0 30rpx;
    color: #fff;
    text-align: center;
    margin-left: auto;
    background: #ff656b;
    box-shadow: 0 2px 8px 0 rgba(#ff656b, 0.4);
    border-radius: 32rpx;
    min-width: 140rpx;
  }

  .yongjin_c {
    .yongjin_type {
      .yongjin_tag {
        padding: 4rpx 12rpx;
        border: 2rpx solid #ebedf1;
        border-radius: 4rpx;
        font-size: 22rpx;
        background: #f8f8f8;
        color: #89929f;

        &.yongjin_tag_type {
          margin-left: 24rpx;
        }
      }
    }
  }

  .info {
    align-items: center;

    .left {
      font-size: 32rpx;
      color: #8a929f;
    }

    .price {
      font-weight: bold;
      color: #fe6c17;
    }

    .mianji {
      margin-left: 24rpx;
    }

    .huxing {
      margin-left: 24rpx;
    }

    .label-list {
      margin-top: 18rpx;

      .label {
        margin-right: 24rpx;
        font-size: 22rpx;
        padding: 3rpx 8rpx;
        color: #2d84fb;
        background-color: rgba($color: #2d84fb, $alpha: 0.1);
        // background-color: #f8f8f8;;
      }
    }

    ::v-deep .my-btn.base {
      padding: 0 24rpx;
    }
  }

  .yongjin {
    display: flex;
    align-items: center;
    height: 64rpx;
    line-height: 64rpx;
    border-top-left-radius: 32rpx;
    border-bottom-left-radius: 32rpx;
    border-top-right-radius: 12rpx;
    border-bottom-right-radius: 12rpx;
    font-size: 28rpx;
    background-color: rgba($color: #feb317, $alpha: 0.15);
    color: #fe6c17;

    .icon {
      margin-left: 8rpx;
      width: 48rpx;
      height: 48rpx;
      margin-right: 24rpx;
    }
  }
}

.other_info {
  position: relative;

  .buy_success {
    width: 180rpx;
    height: 180rpx;
    position: absolute;
    right: 48rpx;
  }

  >.label {
    margin-bottom: 24rpx;
    font-size: 40rpx;
    font-weight: bold;
    color: #2e3c4e;
    position: relative;

    .upload {
      font-size: 24rpx;
    }

    .bianhao {
      font-size: 22rpx;
      color: #3399ff;
      padding: 0 20rpx;
      padding: 10rpx 20rpx;
      // border: 6rpx solid #2d84fb;
      border-radius: 10rpx;
    }

    &.img_label {
      margin-bottom: 0;
    }

    .title_jubao {
      justify-content: center;
      align-items: center;
      font-size: 22rpx;
      font-weight: normal;
      color: #8a929f;

      .toJubao {
        margin-left: 5rpx;
      }
    }
  }

  .info_list {
    flex-wrap: wrap;
    margin-top: 48rpx;

    .info_item {
      width: 33%;
      padding: 0 8rpx 0 0;
      // flex: 1;
      flex-shrink: 0;
      margin-bottom: 48rpx;

      &:nth-child(3n-1) {
        margin: 0 0.5%;
      }

      &.flex-2 {
        flex: 2;
      }

      &.vacancy {
        height: 0;
        margin: 0;
      }

      .label {
        margin-bottom: 24rpx;
        font-size: 20rpx;
        color: #8a929f;
      }

      .value {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 28rpx;
      }
    }
  }
}

.upload {
  position: relative;

  .uploads {
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    opacity: 0;
    z-index: 1;

    ::v-deep .img-list .icon-box {
      margin-top: 0;
    }

    ::v-deep .upload {
      display: block;
      height: 100%;

      .upload-box {
        width: 100%;
        margin-bottom: 0;
      }

      .upload-btn {
        width: 100%;
        padding-bottom: 200rpx;
      }
    }
  }
}

.no_photos {
  height: 200rpx;
  background: #f8f8f8;

  .upload {
    color: #2d84fb;
  }
}

.tab-box {
  position: sticky;
  top: 0;
  z-index: 3;
  background-color: #fff;
  padding-left: 34rpx;

  ::v-deep .nav-item {
    padding: 0 14rpx;
  }
}

.footer_btn_group {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9;
  background-color: #fff;

  .option_btn {
    display: block;
    text-align: center;

    &.more_btn {
      .more_con {
        width: 100%;
      }

      ::v-deep text span {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
      }
    }

    .more_con {
      padding: 0 20rpx;
      font-size: 34rpx;
      justify-content: center;
      // height: 84rpx;
    }

    .more {
      display: block;
      width: 3rpx;
      margin-left: 30rpx;
      font-size: 44rpx;
      transform: rotate(270deg);
      transform-origin: 0 40rpx;
      // line-height: 10rpx;
      // overflow: hidden;
      // word-wrap: break-word;
      // line-height: 18rpx;
      // padding: 6rpx 0;
    }

    &.option_btn_cloumn {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 32rpx;
      color: #8a929f;

      image {
        width: 45rpx;
        height: 34rpx;
      }
    }

    ~.option_btn {
      margin-left: 24rpx;
    }

    ::v-deep .my-btn.big {
      padding: 0 16rpx;
    }
  }
}

.timeline_item {
  .time {
    margin-bottom: 16rpx;
    font-size: 24rpx;
    font-weight: bold;
    color: #999;
  }

  .agent_name {
    font-weight: normal;
  }

  .user_info {
    margin-bottom: 16rpx;
    flex-direction: row;
    align-items: center;

    .avatar {
      margin-right: 12rpx;
      width: 62rpx;
      height: 62rpx;
      border-radius: 50%;
    }

    .name {
      font-size: 24rpx;
      color: #555555;
    }

    .tname {
      margin-top: 6rpx;
      font-size: 23rpx;
      color: #8a929f;
    }

    .tel {
      margin-left: 12rpx;
      flex-direction: row;
      align-items: center;
      font-size: 26rpx;
      color: #2d84fb;
    }
  }

  .content {
    margin-bottom: 16rpx;
    margin-top: 24rpx;
    font-size: 28rpx;
    line-height: 1.5;
    word-break: break-all;

    // overflow: hidden;
    // text-overflow: ellipsis;
    // -webkit-line-clamp: 2;
    // -webkit-box-orient: vertical;
    // display: -webkit-box;
    .status {
      margin-right: 12rpx;
      font-weight: bold;

      &.status1 {
        color: #3cc53c;
      }

      &.status2 {
        color: #8a929f;
      }

      &.status3 {
        color: #2d84fb;
      }

      &.status4 {
        color: #fe6c17;
      }
    }
  }

  .img_list {
    flex-direction: row;
    flex-wrap: wrap;

    >image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 4rpx;
      margin-right: 16rpx;
      margin-bottom: 16rpx;
    }
  }
}

.nomore {
  padding: 0 48rpx;
  text-align: center;
  color: #8a929f;
}

.house_info {
  padding: 24rpx 0;

  &_item {
    font-size: 32rpx;
    color: #fe6c17;

    .house_price {
      .up {
        font-weight: 600;
      }

      .down {
        font-weight: 600;
        color: #00caa7;
      }
    }

    .house_price_unit {
      margin-top: 24rpx;
      color: #8a929f;
    }
  }
}

.look_time {
  flex-direction: row;
  align-items: center;
  margin: 24rpx 0 0;
  line-height: 36rpx;
  padding: 16rpx;
  border-radius: 4rpx;
  font-size: 22rpx;
  color: #fff;
  background-image: linear-gradient(270deg, #00f1a5 0%, #00d6a0 100%);

  &.today {
    background-image: linear-gradient(270deg, #ffcb9b 0%, #ff6e18 100%);
  }

  &.invalid {
    background-image: linear-gradient(270deg, #e6e6e6 0%, #a7a7a7 100%);
  }

  .label {
    margin-left: 16rpx;
    margin-right: 32rpx;
  }
}

.img_swiper {
  padding-left: 48rpx;

  .swiper-item {
    margin-right: 48rpx;
    border-radius: 10rpx;
    width: calc(100% - 40rpx);
    height: 100%;
    margin-right: 40rpx;
    overflow: hidden;

    .img_url {
      width: 100%;
      height: 100%;
    }
  }
}

.see_more {
  text-align: center;
  padding: 20rpx 4rpx;
  background-color: #ecf4ff;
  color: #2d84fb;

  &.no_more {
    background: #fff;
    color: #999;
  }
}

.item {
  position: relative;
  padding: 0 20rpx 36rpx 32rpx;
  border-left: 2rpx solid#dde1e9;
}

.dot {
  content: '';
  height: 14rpx;
  width: 14rpx;
  border-radius: 50%;
  background-color: #3399ff;
  position: absolute;
  left: -8rpx;
  top: 0;
  z-index: 2;
}

.tel_pop {
  width: 100%;
  background: #fff;
  text-align: center;

  .tel_pop_title {
    padding: 24rpx 0;
    font-size: 22rpx;
    color: #999;
    background: #e6e6e6;
  }

  .tel_pop_list {
    background: #fff;

    .tel_pop_item {
      padding: 32rpx 0;
      font-size: 34rpx;
      font-weight: 600;
      color: #333;

      .btn {
        display: inline-block;
        padding: 10rpx 20rpx;
        border-radius: 8rpx;
        background: #2d84fb;
        color: #fff;
      }
    }

    .foll_content {
      text-align: left;
      padding: 0 20rpx;
    }
  }
}

.loudong_danyuan {
  font-size: 22rpx;
  color: #999;
  align-items: center;

  .icon {
    width: 38rpx;
    height: 38rpx;
    margin-left: 10rpx;
  }
}

.house_area {
  color: #8a929f;
  white-space: nowrap;
  overflow-x: auto;

  .zushou {
    background: #4198ff;
    color: #fff;
    font-size: 22rpx;
  }

  &.small {
    font-size: 22rpx;
  }

  &.mtb12 {
    margin: 12rpx 0;
  }

  &.mb12 {
    margin-bottom: 12rpx;
  }

  &.mt12 {
    margin-top: 12rpx;
  }

  &.mt24 {
    margin-top: 24rpx;
  }

  .du {
    width: 32rpx;
    height: 32rpx;
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .level {
    // width: 42rpx;
    // height: 42rpx;
    color: #fff;
    padding: 8rpx 18rpx;

    &.a {
      background-image: linear-gradient(180deg, #f9762e, #fc0606 100%);
    }

    &.b {
      background-image: linear-gradient(180deg, #f8a707, #f85d02 100%);
    }

    &.c {
      background-image: linear-gradient(180deg, #feda38, #fdb508 100%);
    }
  }

  .company {
    background: #dbe8fa;
    color: #2d84fb;
    height: 42rpx;
    padding: 4rpx 18rpx;
    font-size: 22rpx;
  }

  .istop {
    background: #dbe8fa;
    color: #2d84fb;
    height: 42rpx;
    padding: 4rpx 18rpx;
    font-size: 22rpx;

    &.wuxiao {
      color: #fff;
      background: #d1cbcb;
    }

    &.zanhuan {
      color: #fff;
      background: #fda148;
    }

    &.chengjiao {
      color: #fff;
      background: #2f84f7;
    }

    &.tingzhi {
      color: #fff;
      background: #f74c4c;
    }
  }

  .key {
    font-size: 22rpx;
    padding: 4rpx 8rpx;
    background: #dbe8fa;
    height: 42rpx;
    color: #2d84fb;

    &.jujiao {
      color: #fb1d15;
      padding: 4rpx 18rpx;
      background: rgba(251, 29, 21, 0.2);
    }

    &.vip {
      background: #3a3f53;
      color: #f3c840;
    }

    .img {
      width: 32rpx;
      height: 32rpx;
      overflow: hidden;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }

  .line {
    padding: 0 12rpx;
  }

  .price_info {
    align-items: flex-end;
  }

  .price {
    font-size: 32rpx;
    color: #fe6c17;

    &.price_unit {
      font-size: 22rpx;
    }
  }
}

.key_tab {
  padding-bottom: 24rpx;

  .key_tab_item {
    padding: 5px 10px;
    border: 2rpx solid #f7f7f7;
    background: #f7f7f7;
    color: #666;
    border-radius: 5rpx;
    margin-right: 24rpx;
  }
}

// 顶部焦点图
.focus-box {
  position: relative;

  swiper.banner {
    height: 75vw;
    background: #f7f7f7;
  }

  .swiper-item {
    height: 100%;
  }

  .swiper-item image {
    width: 100%;
    height: 100%;
  }

  .img-total {
    position: absolute;
    padding: 4upx 20upx;
    background-color: rgba($color: #000000, $alpha: 0.5);
    border-radius: 20upx;
    left: 20upx;
    bottom: 20upx;
    color: #fff;
  }

  .zhuanfa {
    position: absolute;
    bottom: 20upx;
    right: 20upx;
    padding: 10rpx;
    background: #333;
    border-radius: 50%;
    overflow: hidden;
    z-index: 2;
  }

  .cate-box {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 24upx;
    display: block;
    text-align: center;
    font-size: 0;

    .cate-list {
      display: inline-block;
      padding: 6rpx;
      border-radius: 11px;
      overflow: hidden;
      color: #fff;
      background-color: rgba(0, 0, 0, 0.5);
    }
  }

  .cate {
    display: inline-block;
    padding: 10rpx 20rpx;
    font-size: 22rpx;

    // background-color: #fff;
    &.active {
      background: linear-gradient(45deg, #fd9ea3, #fb656a);
      color: #fff;
      padding: 10rpx 26rpx;
      border-radius: 22rpx;
    }
  }
}

.jiangjia {
  margin-bottom: 48rpx;
  padding: 0 48rpx;

  .pr_top {
    .pr_label_item {
      .pr_label {
        // margin-bottom: 12rpx;
        background: #fff1f5;
        color: #fb656a;
        font-size: 22rpx;
        align-items: center;
        white-space: nowrap;
        padding: 8rpx 8rpx 8rpx 4rpx;
        // margin-top: 10rpx;
        // margin-right: 10rpx;
        border-radius: 2rpx;
        font-weight: 700;

        .img {
          width: 40rpx;
          height: 40rpx;
          margin-right: 5rpx;
          overflow: hidden;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .pr_label_name {
          color: #fb656a;
          font-size: 22rpx;
        }
      }
    }
  }
}

.edit_pop {
  width: 100%;
  background: #fff;
  text-align: center;
  padding: 0 0 30rpx;

  .tel_pop_title {
    padding: 24rpx 0;
    font-size: 28rpx;
    color: #999;
    background: #e6e6e6;
  }

  .edit_item {
    padding: 30rpx 24rpx;

    .edit_img {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      margin-right: 30rpx;
      overflow: hidden;

      image {
        width: 100%;
      }
    }

    .name {
      font-size: 28rpx;
      font-weight: 600;
    }
  }
}

.vr_pop {
  width: 80vw;
  border-radius: 10rpx;
  padding: 0 48rpx 48rpx;
  background: #fff;

  .tel_pop_title {
    padding: 24rpx 0;
    font-size: 28rpx;
    color: #999;
    font-weight: 600;
    // background: #e6e6e6;
  }

  .label {
    width: 150rpx;
  }

  .vr_pop_form {
    .vr_pop_form_item {
      margin-top: 40rpx;

      ::v-deep .wSelect .content {
        width: auto !important;
        left: 200rpx;
        right: 48rpx;
      }

      .desc {
        height: 120rpx;

        textarea {
          width: 100%;
          height: 100%;
          border: 1px solid #dcdfe6;
          text-align: left;
          padding: 8rpx;
        }
      }

      .btns {
        padding: 20rpx 0;

        .btn {
          font-size: 26rpx;
          color: #fff;
          padding: 20rpx 0;
          border-radius: 8rpx;
          flex: 1;

          &.confirm {
            background: #2d84fb;
            margin-right: 20rpx;
          }

          &.cancel {
            background: #f6f6f6;
            color: #999;
          }
        }
      }
    }
  }
}
</style>
