<template>
<myPopup :show="show"  @close="show = false">
    <view class="container">
        <view class="header">
            <tabBar :tabs="tabList" :fixed-top="false" :equispaced="false" :now-index="curTabIndex" @click="handleTabClick"></tabBar>
        </view>
        
        <scroll-view scroll-y style="max-height: 50vh;">
            <view class="body" v-if="show">
                <view class="form-row">
                    <view class="row-label">二维码</view>
                    <view class="row-cont upload-wrapper">
                        <upload :chooseType="1" action="/common/file/upload/admin" :upInfo="{category:6}" :max-count="9"
                            :imgs="params.url"
                            @delImg="handleUploadSuccess"
                            @uploadDone="handleUploadSuccess"
                        ></upload>
                    </view>
                </view>
                <view class="form-row">
                    <view class="row-label">备注</view>
                    <view class="row-cont textarea-wrapper">
                        <textarea v-model="params.remarks" placeholder="请输入备注信息" maxlength="-1" adjust-position :show-confirm-bar="false"
                            confirm-type="确认" @confirm="confirm" class="reason-textarea"/>
                    </view>
                </view>
            </view>
        </scroll-view>
        <view class="footer">
            <button @click="cancle">关闭</button>
            <button type="primary" :loading="submiting" @click="confirm">保存当前项目</button>
        </view>
    </view>
</myPopup>
</template>

<script>
import myPopup from '@/components/myPopup';
import upload from "@/components/customer/upload.vue";
import tabBar from '@/components/tabBar.vue';
export default {
    components: {
        myPopup, upload, tabBar
    },
    props: {
        visible: { type: Boolean, default: false },
		report: { type: Object, default: () => ({})}
	},
    data(){
        return {
            show: false,
            submiting: false,
            curTabIndex: 0,
            projectList: [],
        }
    },
    computed: {
        tabList(){
            return this.projectList.map((e, index) => {
                e.index = index;
                e.description = e.project_name;
                return e;
            })
        },
        params: {
            set(val){
                this.projectList[this.curTabIndex] = val;
            },
            get(){
                return this.projectList[this.curTabIndex]
            }
        }
    },
    watch: {
        visible(val){
            this.show = val;
        },
        show(val){
            val != this.visible && this.$emit('update:visible', val);
            if(val){
                this.curTabIndex = 0;
                const data = this.report;
                const projectNames = data.report_project ? data.report_project.split(',') : [];
                this.projectList = data.project_id ? data.project_id.split(',').map((project_id, index) => {
                    const projectData = (data.project || []).find(e=>e.project_id == project_id) || {};
                    return {
                        id: projectData.id || '',
                        report_id: data.id,
                        project_id,
                        project_name: projectNames[index] || '--',
                        url: projectData.url ? projectData.url.split(',') : [],
                        remarks: projectData.remarks || ''
                    }
                }) : [];
            }  
        },
    },
    methods: {
        handleTabClick(e){
            this.curTabIndex = e.index;
        },
        cancle(){
            this.show = false;
        },
        async confirm(){
            if(this.params.remarks === ''){
                uni.showToast({
                    title: '请填写备注信息',
                    icon: 'none',
                });
                return;
            }

            this.submiting = true;
            try{
                const data = await this.submit()
                uni.showToast({
                    title: data && data.msg ? data.msg : '保存成功',
                    icon: 'none',
                });
                this.$emit('success');
            }catch(e){}
            this.submiting = false;
        },
        handleUploadSuccess(e){
            this.params.url = e || [];
        },
        submit () {
            const params = {...this.params};
            params.url = params.url.join(',');
            !params.id && delete params.id;
            return new Promise((resolve, reject) => {
                this.$ajax.post('/admin/crm/report/update_project_info', params, res => {
                    if (res.statusCode == 200) {
                        resolve(res.data);
                    }else{
                        uni.showToast({
                            title: res?.data?.message || '更改状态失败',
                            icon: 'none'
                        });
                    }
                    reject();
                }, er => {
                    reject();
                })
            })
        }
    },
}
</script>

<style scoped lang="scss"> 
.container{
    background: #fff;
    line-height: 1;
    
    .header{
        position: relative;
        &:after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            height: 1px;
            clear: both;
            border-bottom: 1px solid #e5e5e5;
            color: #e5e5e5;
            -webkit-transform-origin: 0 100%;
            transform-origin: 0 100%;
            -webkit-transform: scaleY(.5);
            transform: scaleY(.5);
        }
        ::v-deep{
            .nav-list{
                height: 88rpx;
                line-height: 88rpx;
            }
            .col4 .nav-item{
                width: auto;
                padding: 0 16rpx;
            }
        }
       
    }
}


.body{
    padding: 12rpx 32rpx 32rpx;
    .form-row{
        padding: 8rpx 0;
        margin-top: 28rpx;
        overflow: hidden;
        flex-direction: column;
        .row-label{
            color: #3c3c3c;
            white-space: nowrap;
            padding-right: 24rpx;
            font-size: 32rpx;
            font-weight: 600;
        }
        .row-cont{
            margin-top: 24rpx;
            flex-direction: row;
            flex-wrap: wrap;
            &.upload-wrapper{
                margin-top: 14rpx;
            }
            &.status-wrapper{
                margin-top: 0;   
            }
            .status-item{
                display: inline-flex;
                flex-direction: row;
                height: 66rpx;
                align-items: center;
                font-size: 28rpx;
                color: #4E5969;
                background-color: #f6f6f6;
                border: 1rpx solid #f1f2f3;
                padding: 0 28rpx;
                margin: 24rpx 24rpx 0 0;
                &.active{
                    color: #2d84fb;
                    background-color: #e5eeff;
                    border-color: #d9e8ff;
                }
            }
        }
    }
    .textarea-wrapper{
        background: #F8F8F8;
        padding: 12px 12px 0px 12px;
    }
    .reason-textarea{
        height: 200rpx;
    }
}
.footer{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 24rpx 0;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    button{
        flex: 1;
        margin: 0 24rpx;
        &[type=primary]{
            background-color: #2d84fb;
        }
    }
}
</style>