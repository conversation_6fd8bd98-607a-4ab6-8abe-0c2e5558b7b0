<template>
  <view>
    <view style="height:100vh">
      <view class="top">
        <view class="info" v-if="false">
          <text class="tit">分组</text>
          <picker @change="sexChange" :range="sex" range-key="name" class="lab">
            <view :class="param.sex ? '' : 'novalue'" class="row val">
              <text class="input c2">{{ param.sex || "请选择" }}</text>
              <myIcon
                class="icon"
                type="xiala"
                size="30rpx"
                color="#b0b0b0"
              ></myIcon>
            </view>
          </picker>
        </view>
        <view class="info" v-if="false">
          <text class="tit">二级分组</text>
          <picker @change="sexChange" :range="sex" range-key="name" class="lab">
            <view :class="param.sex ? '' : 'novalue'" class="row val">
              <text class="input c2">{{ param.sex || "请选择" }}</text>
              <myIcon
                class="icon"
                type="xiala"
                size="30rpx"
                color="#b0b0b0"
              ></myIcon>
            </view>
          </picker>
        </view>
        <view class="info">
          <text class="tit">标题<text>*</text></text>
          <view class="row val input1">
            <input
              type="text"
              class="input c2"
              maxlength="50"
              v-model="form_info.title"
              placeholder="仅展示给企业内部成员，最多50个字"
            />
          </view>
        </view>
        <view class="info">
          <text class="tit">内容<text>*</text></text>
          <view class="row val input1">
            <textarea
              name=""
              v-model="form_info.content"
              id=""
              cols="30"
              placeholder="请输入回复内容"
              rows="10"
            ></textarea>
          </view>
        </view>
      </view>
      <view class="top" v-if="false">
        <view class="info">
          <text class="tit">请选择文件类型</text>
          <view class="list remind">
            <radio-group
              @change="radioChange"
              class="uni-group c2 add_raido row"
            >
              <label
                class="sex-box-row labelr"
                v-for="item in list"
                :key="item.value"
              >
                <image class="pic" :src="item.src" mode="widthFix" />
                <view class="tit">{{ item.description }}</view>
                <radio
                  :value="item.value"
                  :checked="item.value === current"
                ></radio>
              </label>
            </radio-group>
          </view>
          <view class="tag c2">请上传xlsx格式文件（大小不超过2M）</view>
          <view class="upload">本地上传</view>
        </view>
      </view>
      <view class="line"></view>
      <view class="foot row">
        <view class="c2" @click="$navigateBack()">取消</view>
        <view @click="onCreateInfo">确定</view>
      </view>
    </view>
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
export default {
  components: {
    myIcon,
  },
  data() {
    return {
      param: {
        sex: "",
        area: "",
        xq: "",
      },
      sex: [
        { name: "男", id: 1 },
        { name: "女", id: 2 },
      ],
      list: [
        { value: "1", description: "xlsx", src: "../static/customer/xlsx.png" },
        { value: "2", description: "链接", src: "../static/customer/lj.png" },
        { value: "3", description: "mp3", src: "../static/customer/mp3.png" },
        { value: "4", description: "文本", src: "../static/customer/wb.png" },
        {
          value: "5",
          description: "小程序",
          src: "../static/customer/xcx.png",
        },
      ],
      current: 1,
      form_info: {
        title: "",
        content: "",
      },
    };
  },
  methods: {
    sexChange(e) {
      this.param.sex = this.sex[e.detail.value].name;
    },
    radioChange(e) {
      this.current = e.detail.value;
      // console.log(this.current);
    },
    onCreateInfo() {
      if (!this.form_info.title || !this.form_info.content) {
        uni.showToast({
          title: "请检查内容后提交",
          icon: "none",
        });
        return;
      }
      this.$ajax.post("/qywx/words/create", this.form_info, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "添加成功",
          });
          setTimeout(() => {
            this.$navigateTo("/customer/talk");
          }, 500);
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.line {
  height: 95px;
}
.foot {
  position: fixed;
  bottom: 0;
  width: 100%;
  background: #fff;
  padding: 12px 12px 40px 12px;
  justify-content: space-between;
  view {
    width: 48%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 6px;
    border: 1px solid #2d84fb;
    color: #fff;
    background: #2d84fb;
    font-weight: 500;
    &.c2 {
      border: 1px solid #dde1e9;
      background: #fff;
      color: #8a929f;
    }
  }
}
.top {
  margin: 12px;
  padding: 0 12px 12px 12px;
  background: #fff;
  border-radius: 6px;
  .list {
    .add_raido {
      justify-content: space-between;
      display: flex;
      align-items: center;
      text-align: center;
      .labelr {
        .pic {
          width: 48px;
        }
        .tit {
          margin: 4px 0 8px 0;
        }
      }
    }
  }
  .info {
    .upload {
      width: 100%;
      color: #2d84fb;
      background: #eaf3ff;
      height: 40px;
      line-height: 40px;
      text-align: center;
      border-radius: 4px;
      margin: 12px 0;
      font-weight: 500;
    }
    .tag {
      font-size: 11px;
      margin-top: 24px;
    }
    .tit {
      margin: 12px 0;
      font-size: 16px;
      font-weight: 500;
      text {
        color: #ff2e13;
      }
    }
    .val {
      justify-content: space-between;
      border: 1px solid #dde1e9;
      border-radius: 4px;
      align-items: center;
      padding: 10px 12px;
      .input {
        flex: 1;
        font-size: 14px;
      }
      &.input1 {
        padding: 8px 12px;
      }
    }
  }
}
page {
  background: #f6f6f6;
  color: #2e3c4e;
}
.c2 {
  color: #8a929f;
}
</style>
