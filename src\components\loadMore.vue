<template>
  <view class="load">
    <text>{{ filterText(status) }}</text>
    <view v-if="status == ''" @click="onLoadMore">加载更多</view>
  </view>
</template>

<script>
export default {
  components: {},
  props: {
    status: {
      type: String,
      default: "loading", // loading, loadend, nomore
    },
    load_text: {
      type: Object,
      default: () => {
        return {
          loading: "加载中...",
          loadend: "",
          empty: "— 暂无数据 —",
          nomore: "— 已经到底了 —",
        };
      },
    },
    showLoadMore: { type: Boolean, default: false }
  },
  data() {
    return {};
  },
  methods: {
    filterText(val) {
      if (val) {
        return this.load_text[val] || "";
      } else {
        return "";
      }
    },
    onLoadMore(){
      this.$emit('load-more')
    }
  },
};
</script>

<style scoped lang="scss">
.load {
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 26rpx;
  color: #999;
}
</style>
