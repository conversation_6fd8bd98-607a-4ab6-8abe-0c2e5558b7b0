<template>
  <view class="list">
    <view class="content row">
      <view class="left">报备楼盘</view>
      <view class="right">{{ customer_info.build_name }}</view>
    </view>
    <view class="content row">
      <view class="left">报备人姓名</view>
      <view class="right">{{
        customer_info.u_name ||
          customer_info.u_nickname ||
          customer_info.u_user_name
      }}</view>
    </view>
    <view class="content row">
      <view class="left">报备人电话</view>
      <view class="right">{{ customer_info.u_phone }}</view>
    </view>
    <view class="content row">
      <view class="left">客户姓名</view>
      <view class="right">{{ customer_info.customer_name }}</view>
    </view>
    <view class="content row">
      <view class="left">客户电话</view>
      <view class="right">{{ customer_info.customer_phone }}</view>
    </view>
    <view class="content row" @click="pickerVisibleStatus = true">
      <view class="left">报备状态</view>
      <view class="right row check">
        <input type="text" disabled="true" v-model="status_name" />
        <myIcon type="you" size="28rpx" color="#999"></myIcon>
      </view>
    </view>
    <view class="btn" @click="onSubmit">更新状态</view>
    <!-- 弹出选择框 -->
    <VuePicker
      :data="pickDataStatus"
      title="选择用户状态"
      cancelText="取消"
      confirmText="确认"
      :showToolbar="true"
      @confirm="confirmStatus"
      :visible.sync="pickerVisibleStatus"
    />
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import VuePicker from "vue-pickers";
import myIcon from "@/components/my-icon";
export default {
  components: {
    myIcon,
    VuePicker,
  },
  data() {
    return {
      form: {
        customer_reported_id: "",
        status: "",
      },
      status_name: "",
      customer_info: {},
      status_list: [
        { value: "2", label: "已到访" },
        { value: "3", label: "已认筹" },
        { value: "4", label: "已认购" },
        { value: "10", label: "已无效" },
      ],
      pickDataStatus: [],
      pickerVisibleStatus: false,
    };
  },
  onLoad(options) {
    if (options) {
      this.form.customer_reported_id = parseInt(options.id);
      this.form.status = parseInt(options.status);
      if (this.form.status === 5 || parseInt(options.status) === 10) {
        this.status_list = [];
      }
    }
    this.getCustomerInfo();
  },
  methods: {
    getCustomerInfo() {
      this.$ajax.get(
        `/client/customer/reported/query/project/id/${this.form.customer_reported_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.customer_info = res.data;
            this.$getDictionaryList("REPORTED_STATUS", {}, (res) => {
              if (res.statusCode === 200) {
                res.data.data.map((item) => {
                  if (this.customer_info.status == item.value) {
                    this.status_name = item.description;
                  }
                });
              }
            });
            // 如果状态小于更改状态，删除数组内容，否则就存在用户回改未限制
            let arr = this.status_list.filter((item) => {
              let value = parseInt(item.value);
              return res.data.status <= value;
            });
            this.pickDataStatus.push(arr);
          } else {
            uni.showToast({
              title: res.data.message || "获取信息失败",
              icon: "none",
            });
          }
        }
      );
    },
    confirmStatus(res) {
      res.map((item) => {
        this.form.status = item.value;
        this.status_name = item.label;
      });
    },
    onSubmit() {
      if (this.form.status == 2) {
        this.$navigateTo(
          `/report/add_schedule?id=${this.customer_info.id}&customer_status=${this.form.status}&is_visit=1`
        );
        return;
      }
      if (this.form.status === 5) {
        uni.showToast({
          title: "该客户已成交",
          icon: "none",
        });
        return;
      }
      if (this.form.status === 10) {
        uni.showToast({
          title: "该客户已无效",
          icon: "none",
        });
        return;
      }
      this.$ajax.post(
        "/client/customer/reported/audit/status",
        this.form,
        (res) => {
          if (res.statusCode === 200) {
            uni.showToast({
              title: "修改成功",
            });
            this.pickDataStatus = [];
            this.getCustomerInfo();
          } else if (res.statusCode === 422) {
            uni.showToast({
              title: res.data.message || "已选状态不能重复操作",
              icon: "none",
            });
          } else {
            uni.showToast({
              title: res.data.message || "修改失败",
              icon: "none",
            });
          }
        }
      );
    },
  },
};
</script>

<style scoped lang="scss">
.list {
  padding: 24rpx 48rpx;
  .content {
    align-items: center;
    .left {
      width: 280rpx;
      font-size: 32rpx;
      padding: 32rpx 0;
    }
    .right {
      padding: 32rpx 0;
      width: 100%;
      color: #999;
      border-bottom: 2rpx solid #999;
    }
    .check {
      justify-content: space-between;
      align-items: center;
    }
  }
  .btn {
    margin-top: 100rpx;
    align-items: center;
    background: #0174ff;
    padding: 30rpx;
    border-radius: 22px;
    color: #fff;
  }
}
</style>
