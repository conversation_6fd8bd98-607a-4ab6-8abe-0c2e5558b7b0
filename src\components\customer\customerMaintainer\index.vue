<template>
    <view  class="customer-maintainer-container">
        <view class="timeline">
            <view class="timeline-item current" v-for="(maintainer,index) in maintainerList" :key="maintainer.name">
                <view class="timeline-item-header">
                    <view class="title">{{maintainer.title}}</view>
                    <view class="op-group" v-if="!isTrans">
                        <view class="op-more" v-if="maintainer.opMoreAbled" @click.stop="openOpPopper(index)">
                            <image :src="'/static/admin/customer/<EMAIL>' | imgDomain" class="icon-more"/>
                            <view class="popper" v-if="maintainer.opPopperShow">
                                <view class="popper-content">
                                    
                                    <view class="popper-item" @click.stop="addShareFollower">
                                        <view class="icon">
                                            <!-- <image :src="'/static/admin/customer/tianjia.png' | imgDomain"/> -->
                                            <uni-icons type="plusempty" size="18"></uni-icons>
                                        </view>
                                        <view class="title">共享维护人</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                
                <view class="timeline-item-body">
                    <view class="user-info" v-for="(user, userIndex) in maintainer.list" :key="userIndex">
                        <view class="left"><image :src="user.avatar" class="avatar-image"/></view>
                        <view class="right">
                            <view class="row top-row">
                                <view class="base">
                                    <text class="name">{{user.name}}</text>
                                    <text class="depart">{{user.department}}</text>
                                </view>

                                <view class="op-group">
                                    <view class="op-more" v-if="user.opMoreAbled" @click.stop="openOpUserPopper(index, userIndex)">
                                        <image :src="'/static/admin/customer/<EMAIL>' | imgDomain" class="icon-more"/>
                                        <view class="popper" v-if="user.opPopperShow">
                                            <view class="popper-content">
                                                <view class="popper-item" @click.stop="delShareFollower(user)">
                                                    <view class="icon">
                                                        <uni-icons type="trash" size="20"></uni-icons>
                                                    </view>
                                                    <view class="title">删除维护人</view>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="row bottom-row">
                                <text class="time">{{user.titleLabel || '创建时间'}}：{{user.time}}</text>
                            </view>
                        </view>
                    </view>
                </view>
                
            </view>
        </view>

        <addShareFollower v-if="dialogs.addShareFollower" ref="addShareFollower"></addShareFollower>
    </view>
</template>
<script>
import config from "@/page_outside/config/index";
import addShareFollower from '@/components/customer/customerMaintainer/addShareFollower.vue';
export default {
    name: 'customerMaintainer',
    props: {
        datas: { type: Object, default: ()=>{}},
        current: { type: String, default: 'my' }
    },
    components: {
        addShareFollower
    },
    data(){
        return {
            maintainerList: [],  //维护人列表
            dialogs: {
                addShareFollower: false
            }
        }
    },
    filters: {
        imgDomain(src){
            return config.imgDomain +src
        }
    },
    computed: {
        isTrans(){
            return this.current === 'trans'
        },
    },
    watch: {
        datas: {
            handler(d){
                //设置维护人数据 
                this.setMaintainerData(d);
            },
            immediate: true
        }
    },
    methods: {
        //判断是否有共享操作权限
        hasOpPermission(data){
            const user_info = uni.getStorageSync("userInfo") ? JSON.parse(uni.getStorageSync("userInfo")) : {};
            let hasPermission = false;
            if(user_info.id){
                //当前为维护人
                if(user_info.id == data.follow_user?.id){
                    hasPermission = true;
                }else{
                    console.log(user_info.id,data.admin_list);
                    //当前为客户管理员或创始人
                    if(this.$Utils.isNotEmptyArray(data.admin_list)){
                        hasPermission = data.admin_list.includes(user_info.id+'');
                    }
                }
            }
            return hasPermission;
        },
        //设置维护人数据 
        setMaintainerData(data){
            //是否有操作权限
            const hasPermission = this.hasOpPermission(data);
            //默认头像
            const avatar = config.imgDomain + '/static/admin/customer/nan2.png';
            this.maintainerList = [];
            //接待录入人
            this.maintainerList.push({
                title: '接待录入人',
                name: 'create_user',
                list: [
                    {
                        name: data.create_user_name || data.create_user?.user_name || '--',
                        avatar,
                        department: data.create_user?.department_name || '--',
                        time: data.created_at || '--' 
                    }
                ]
            });
            //跟进维护人
            this.maintainerList.push({
                title: '跟进维护人',
                name: 'follow_user',
                opMoreAbled: hasPermission,
                opPopperShow: false,
                list: [
                    {
                        name: data.follow_user_name || data.follow_user?.user_name || '--',
                        avatar,
                        department: data.follow_user?.department_name || '--',
                        time: data.get_time || '--' 
                    }
                ]
            });
            //共享维护人
            if(this.$Utils.isNotEmptyArray(data.share_admins)){
                const shareUserList = [];
                this.maintainerList.push({
                    title: '共享维护人',
                    name: 'share_user',
                    list: data.share_admins.map( user => {
                        return {
                            id: user.id,
                            share_id: user.share_id,
                            name: user.user_name || '--',
                            avatar,
                            department: user.department_name || '--',
                            time: user.created_at || '--',
                            opMoreAbled: hasPermission,
                        }
                    })
                });
            }
            //首次带看人
            this.maintainerList.push({
                title: '首次带看人',
                name: 'first_look_user',
                list: [
                    {
                        name: data.first_look_user?.user_name || '--',
                        avatar,
                        department: data.first_look_user?.department_name || '--',
                        time: data.first_look_user?.first_look_at || '--',
                        titleLabel: '首次带看时间'
                    }
                ]
            });
            //客源成交人
            this.maintainerList.push({
                title: '客源成交人',
                name: 'deal_user',
                list: [
                    {
                        name: data.deal_user_name || data.deal_user?.user_name || '--',
                        avatar,
                        department: data.deal_user?.department_name || '--',
                        time: data.deal_at || '--' 
                    }
                ]
            });
            
        },
        openOpPopper(index){
            if(this.maintainerList[index].opPopperShow){
                return;
            }
            this.$emit('pageClick', {
                handler(index){
                    this.$set(this.maintainerList[index], 'opPopperShow', false);
                },
                thisObj: this,
                params: [index]
            })
            this.$set(this.maintainerList[index], 'opPopperShow', true);
        },
        openOpUserPopper(index, userIndex){
            if(this.maintainerList[index].list[userIndex].opPopperShow){
                return;
            }
            this.$emit('pageClick', {
                handler(index){
                    this.$set(this.maintainerList[index].list[userIndex], 'opPopperShow', false);
                },
                thisObj: this,
                params: [index, userIndex]
            })
            this.$set(this.maintainerList[index].list[userIndex], 'opPopperShow', true);
        },
        //添加共享维护人
        async addShareFollower(){
            this.dialogs.addShareFollower = true;
            await this.$nextTick();
            this.$refs.addShareFollower.open(this.datas.id).onSuccess(()=>{
                this.$emit('addShareFollowerSuccess');
            });
        },

        //删除共享维护人
        delShareFollower({share_id}){
            this.$ajax.get("/admin/crm/share_follow/del_share_follow", {
                share_id: share_id
            }, (res) => {
                if (res.statusCode === 200) {
                    uni.showToast({
                        title: res.data?.msg || "删除成功",
                    });
                    this.$emit('addShareFollowerSuccess');
                } else {
                    uni.showToast({
                        title: res.data.message,
                        icon: "none",
                    });
                }
            });
        }
    }
}


</script>
<style lang="scss" scoped>
.timeline{
    padding: 2rpx 32rpx 24rpx 46rpx;
    .timeline-item {
        position: relative;
        padding: 0 0 54rpx 44rpx;
        border-left: 5rpx solid #e4e4e4;
        &:last-child{
            padding-bottom: 28rpx;
        }
        &::after {
            content: "";
            height: 32rpx;
            width: 32rpx;
            box-sizing: border-box;
            border-radius: 50%;
            position: absolute;
            border: 4rpx solid #3399ff;
            background-color: #fff;
            left: -18rpx;
        }
        .timeline-item-header{
            display: flex;
            flex-direction: row;
            height: 40rpx;
            align-items: flex-start;
            justify-content: space-between;
            .title{
                font-size: 28rpx;
                flex: 1;
                display: inline-block;
                padding-top: 4rpx;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            
        }

        .timeline-item-body{
            .user-info{
                display: flex;
                flex-direction: row;
                padding: 24rpx 24rpx 24rpx 0;
                .left{
                    width: 96rpx;
                    height: 96rpx;
                    margin-right: 24rpx;
                    .avatar-image{
                        width:100%;
                        height:100%
                    }
                }
                .right{
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    width: calc(100% - 120rpx);
                    .row{
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        padding: 6rpx 0;
                        .name, .depart{
                            display: inline;
                            font-weight: 400;
                        }
                        .depart{
                            color: rgba(41, 44, 57, .4);
                            padding-left: 16rpx;
                        }
                        .time{
                            font-size: 24rpx;
                            color: rgba(41, 44, 57, .4);
                        }
                    }
                    .top-row{
                        .base{
                            flex: 1;
                            display: inline-block;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                    }
                    
                }
            }
        }
        
    }
}
.customer-maintainer-container{
    .op-more{
        position: relative;
    }
    .op-group{
        
        .op-more{
            height: 32rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 2rpx;
            .icon-more{
                width: 28rpx;
                height: 8rpx;
            }
        }
    }
    .popper{
        position: absolute;
        z-index: 3;
        top: 13px;
        right: 0;
        background-color: #fff;
        border: 1px solid #ebeef5;
        border-radius: 6px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        padding: 4px 0;
        line-height: 1;
        .popper-item{
            padding: 20rpx 28rpx;
            font-size: 28rpx;
            color: #6c6f74;
            white-space: nowrap;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            .icon-image{
                width:18px;
                height: 18px;
                image{
                    width: 100%;
                    height: 100%;
                }
            }
            .title{ padding-left: 5px;}
        }
    }
}


</style>