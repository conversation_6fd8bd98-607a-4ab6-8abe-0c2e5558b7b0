<template>
  <view class="list">
    <view>内容描述</view>
    <view class="article-content">
      <u-parse
        :content="content.content"
        @linkpress="navigate"
        :tag-style="tagStyle"
      ></u-parse>
    </view>
    <view class="create_time">{{ content.created_at }}</view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import uParse from "@/components/uParse/parse.vue";
export default {
  components: {
    uParse,
  },
  data() {
    return {
      tagStyle: {
        video: "max-width:100%",
      },
      content: {},
      help_id: "",
    };
  },
  onLoad(options) {
    if (options) {
      this.help_id = options.id;
    }
    this.getData();
  },
  methods: {
    getData() {
      this.$ajax.get(`/common/help_center/query/${this.help_id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.content = res.data;
        } else {
          uni.showToast({
            title: res.data.message || "获取数据失败",
            icon: "none",
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  padding: 24rpx 48rpx;
}
</style>
