<template>
  <view class="uni-list list">
    <checkbox-group @change="checkboxChange" v-if="memberList.length">
      <label
        class="uni-list-cell uni-list-cell-pd flex-row bottom-line"
        v-for="item in memberList"
        :key="item.id"
      >
        <view>
          <checkbox :value="item.id + ''" :checked="item.checked" />
          <!-- <radio :value="'' + item.id" :checked="index === current" /> -->
        </view>
        <view class="prelogo">
          <image :src="item.avatar"></image>
        </view>
        <view class="cname">{{ item.cname }} -{{ item.department }}</view>
      </label>
    </checkbox-group>
    <view class="no_data flex-row items-center justify-center" v-else>暂无可选择成员</view>
    <view class="footer flex-row justify-end items-center">
      <view class="cancel" @click="cancel" v-if="from == 2">取消</view>
      <view class="confirm" @click="submit" v-if="from == 1">移动到</view>
      <view class="confirm" @click="submit" v-if="from == 2">确定</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      memberList: [],
      current: -1,
      // id: '',
      selected: [],
    }
  },
  created() {
    // if (options.id) {
    //   this.id = options.id
    // }
    this.getMemberList()
  },
  watch: {
    values: {
      handler(val) {
        console.log(val)
        this.setSelected(val)
      },
    },
  },
  props: {
    id: {
      type: [String, Number],
    },
    from: {
      type: [String, Number],
      default: 1, // 1移动成员  2 选择审批人 抄送人
    },
    values: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    checkboxChange(e) {
      this.selected = e.detail.value
    },
    getMemberList() {
      this.$ajax.get(`/v1/wapLm/getAgentsByAddDepartment/${this.id}`).then((res) => {
        if (res.data.status == 200) {
          this.memberList = res.data.data.map((item) => {
            item.checked = false
            return item
          })
          if (this.values && this.values.length) {
            this.setSelected(this.values)
          }
        } else {
          uni.showToast({
            title: res.data.message,
            icon: 'none',
          })
        }
      })
    },
    setSelected(val) {
      this.selected = []
      if (val && val.length) {
        val.map((value) => {
          this.memberList.map((item) => {
            if (item.id == value.id) {
              item.checked = true
              return item
            }
          })
          this.selected.push(value.id)
          console.log(this.selected, ' this.selected')
        })
      }
    },
    cancel() {
      this.$emit('cancel')
    },
    submit() {
      if (this.selected.length == 0) {
        uni.showToast({
          title: '请选择成员',
          icon: 'none',
        })
        return
      }
      this.selectedNode = []
      this.memberList.map((item) => {
        if (this.selected.includes(item.id + '')) {
          this.selectedNode.push(item)
        }
      })

      this.$emit('selectMemberOk', { selected: this.selected, selectedNode: this.selectedNode })
    },
  },
}
</script>

<style scoped lang="scss">
.list {
  padding-bottom: 80rpx;
  .no_data {
    color: #999;
  }
}
.uni-list {
  checkbox-group {
    padding-left: 24rpx;
    uni-checkbox {
      ::v-deep .uni-checkbox-input {
        margin-right: 0;
        border-radius: 50%;
      }
    }
  }
  .prelogo {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin: 0 40rpx;
    overflow: hidden;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .cname {
    font-size: 28rpx;
    font-weight: bolder;
    color: #333;
  }

  .uni-list-cell {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 20rpx 0;
  }
  .footer {
    padding: 20rpx;
    background: #f8f8f8;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    .confirm {
      font-size: 28rpx;
      padding: 10rpx 20rpx;
      color: #fff;
      background: #2d84fb;
      border-radius: 8rpx;
    }
    .cancel {
      margin-right: 40rpx;
      padding: 10rpx 20rpx;
    }
  }
}
</style>
