<template>
    <view>
        <view class="top">
            <view class="filters">
                <view class="filters-item">
                    <wisdomSearchSelect placeholder="带看日期" v-model="params.date_value" :datas="dateGroups" :params.sync="params" auto-popup @confirm="search" @clear="search"></wisdomSearchSelect>
                </view>
                <view class="filters-item">
                    <wisdomMemberSelect placeholder="成员" v-model="params.admin_id" @select="search"></wisdomMemberSelect>
                </view>
                <view class="filters-item">
                    <wisdomSearchSelect placeholder="排序" v-model="params.sort" :datas="sortGroups" :params.sync="params" auto-popup @confirm="search" @clear="search"></wisdomSearchSelect>
                </view>
            </view>
        </view>

        <wisdomTable :api="getList" :headers="headers" ref="table" summary></wisdomTable>
    </view>
</template>

<script>
import wisdomSearchSelect from './components/wisdomSearchSelect';
import wisdomMemberSelect from './components/wisdomMemberSelect';
import wisdomTable from './components/wisdomTable';
import { getSeedUserData } from '@/common/utils/wisdom-work.js';
export default {
    components: {
        wisdomSearchSelect,
        wisdomMemberSelect,
        wisdomTable
    },
    data(){
        return {
            dateGroups: [{
                title: ' ', field: 'date_type', default: 1, options: [
                    { label: '带看日期', value: 1 },
                    { label: '带看创建时间', value: 2 }
                ]
            },{
                title: ' ', field: 'date_value', options: [
                    { label: '全部', value: '' },
                    { label: '今天', value: 'today' },
                    { label: '昨天', value: 'yestoday' },
                    { label: '本周', value: 'now_week' },
                    { label: '上周', value: 'last_week' },
                    { label: '本月', value: 'now_month' },
                    { label: '上月', value: 'last_month' },
                ]
            }],
            sortGroups: [{
                title: ' ', field: 'sort', options: [
                    { label: '带看客户量降序', value: 'custom_num' },
                    { label: '首看降序', value: 'first_take_num' },
                    { label: '复看降序', value: 'again_take_num' },
                    { label: '带看总量降序', value: 'total_take_num' },
                    { label: '陪看降序', value: 'accompany_take_num' },
                ]   
            }],
            params: {
                date_type: 1,
                date_value: '',
                start_date: '',
                end_date: '',
                admin_id: '',
                sort: ''
            },
            headers: [
                { label: '姓名', field: 'user_name', width: 130, fixed: true },
                { label: '带看客户量', field: 'custom_num', width: 110 },
                { label: '首看次数', field: 'first_take_num', width: 110 },
                { label: '复看次数', field: 'again_take_num', width: 110 },
                { label: '带看总次数', field: 'total_take_num', width: 110 },
                { label: '陪看次数', field: 'accompany_take_num', width: 110 },
                { label: '最近带看日期', field: 'date', width: 150 },
            ],
            list: [],
        }
    },
    onLoad(options){
        this.params.date_value = options.date || '';
    },
    methods: {
        async getList(page){
            const [start_date, end_date ] = this.$Utils.getDateRange(this.params.date_value, 'YYYY-MM-DD');
            const params = { ...this.params, start_date, end_date, page };
            delete params.date_value;
            const res = await getSeedUserData(params);
            return {
                list: (res.data || []),
                pageSize: res.per_page || 0
            }
        },
        async search(){
            await this.$refs.table.search();
        }
    },
    async onPullDownRefresh(){
		await this.search();
		uni.stopPullDownRefresh();
	},
	onReachBottom () {
		this.$refs.table.getList();
	},
}
</script>

<style lang="scss" scoped>
@import "@/common/style/wisdom_work/wisdom_work_top_filters.scss";
</style>