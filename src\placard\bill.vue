<template>
    <view>
        <view class="New_Era">
        </view>
        <view class="code">
            <view class="head_sculpture">
                <view class="wolf">
                    <view class="sculpture">
                    </view>
                    <text>
                        张三
                    </text>
                </view>
                <view class="invite">
                    <text>
                        邀请您参加大抽奖
                    </text>
                </view>
            </view>
            <view class="Reading_red">
                <view class="Sharing_red">
                    <view>
                        <image src="./photo/red_envelopes.png"></image>
                    </view>
                    <text class="read">
                        阅读红包
                    </text>
                    <text class="open">
                        (打开可得)
                    </text>
                    <text class="highest">
                        最高3.8元
                    </text>
                </view>
                <view class="Sharing_red">
                    <view>
                        <image src="./photo/red_packet.png"></image>
                    </view>
                    <text class="read">
                        分享红包
                    </text>
                    <text class="open">
                        (分享新用户可得)
                    </text>
                    <text class="highest">
                        最高88元
                    </text>
                </view>
                <view class="Sharing_red">
                    <view>
                        <image src="./photo/or_kickback.png"></image>
                    </view>
                    <text class="read">
                        榜单奖励
                    </text>
                    <text class="open">
                        (排行榜奖金)
                    </text>
                    <text class="highest">
                        暂未设置
                    </text>
                </view>
            </view>
            <view class="bottom">
                <view class="bottom_left">
                    <!-- <view class="world"> -->
                    <text class="word_top">扫描右侧二维码领红包</text>
                    <text class="word_bottom">有效区域：枣庄市-滕州市 荆河街道.....</text>
                    <!-- </view> -->
                </view>
                <view class="botton_right">
                    <view class="two_dimensional_code">

                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {
            requestData: {
                lat: "",
                lng: "",
                id: "4"
            },

        }
    },
    onLoad(option) {
        let that = this
        if (option.id) {
            that.requestData.id = option.id
        }
        wx.getLocation({
            type: 'wgs84',
            success(res) {
                console.log(res);
                console.log('纬度' + res.latitude)
                console.log('经度' + res.longitude)
                that.requestData.lat = res.latitude
                that.requestData.lng = res.longitude
                console.log(that.requestData);
                // if (that.requestData.lat & that.requestData.lng !== "") {
                    that.Poster_Request()
                // }
            }
        })
        // if (that.requestData.lat & that.requestData.lng !== "") {
        //     that.Poster_Request()
        // }
    },
    methods: {
        Poster_Request() {
            this.$ajax.get(`/poster/poster_details?id=${this.requestData.id}&lng=${this.requestData.lng}&lat=${this.requestData.lat}`, {},
                (res) => {
                    console.log(res);
                })
        }
    },
}
</script>
<style scoped lang="scss">
.New_Era {
    width: 100%;
    height: 1000rpx;
    background-image: url("./photo/picture.png");
    background-repeat: no-repeat;
    background-size: 100% 125%;
}

.code {
    width: 100%;
    height: 622rpx;

    // background-color: aquamarine;
    .head_sculpture {
        width: 100%;
        height: 180rpx;
        // background-color: brown;
        margin-top: -55rpx;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        .sculpture {
            width: 120rpx;
            height: 120rpx;
            background-color: aquamarine;
            border-radius: 50%;
            margin-left: 40rpx;
        }

        .wolf {
            width: 300rpx;
            height: 180rpx;
            // background-color: cadetblue;
        }

        text {
            color: #000000;
            font-size: 14px;
            margin-left: 75rpx;
            margin-top: 20rpx;
            font-weight: 600;
        }

        .invite {
            width: 330rpx;
            height: 180rpx;

            // background-color: blueviolet;
            text {
                margin-top: 110rpx;
                color: #858A8E;
                font-weight: 500;
            }
        }

    }

    .Reading_red {
        width: 100%;
        height: 270rpx;
        // background-color: brown;
        border: 4px solid white;
        border-bottom-color: #F6F6F6;
        margin-top: 20rpx;
        display: flex;
        justify-content: space-between;
        flex-direction: row;

        .Sharing_red {
            width: 240rpx;
            height: 250rpx;

            // background-color: aquamarine;
            view {
                width: 100rpx;
                height: 100rpx;
                // background-color:brown ;
                // border: 1px dashed #7c8084;
                margin: 0 auto;
                margin-top: 10rpx;
            }

            image {
                width: 90%;
                height: 90%;
                margin: 0 auto;
            }

            text {
                text-align: center;
                margin-top: 15rpx;
            }

            .read {
                font-size: 14px;
                font-weight: 600;
                color: #14161c;
            }

            .open {
                font-size: 11px;
                color: #858a8e;
                margin-top: 20rpx;
            }

            .highest {
                font-size: 14px;
                color: #ff0000;
            }
        }
    }

    .bottom {
        width: 100%;
        height: 208rpx;
        // background-color: aqua;
        display: flex;
        flex-direction: row;

        .bottom_left {
            width: 65%;
            height: 208rpx;
            text-align: left;
            line-height: 45rpx;

            .word_top {
                font-size: 32rpx;
                color: #14161c;
                margin-left: 50rpx;
                margin-top: 50rpx;
            }

            .word_bottom {
                font-size: 22rpx;
                color: #858a8e;
                margin-left: 50rpx;

            }
        }

        .botton_right {
            width: 35%;
            height: 208rpx;

            // background-color: cadetblue;
            .two_dimensional_code {
                width: 160rpx;
                height: 160rpx;
                background-color: aqua;
                margin: 22rpx auto;
            }
        }
    }
}
</style>