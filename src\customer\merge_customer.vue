<template>
  <view class="merge">
    <view class="info row">
      <text class="tit">合并类型：</text>
      <view class="lab row type_val">
        <text
          v-for="item in type_list"
          :key="item.id"
          :class="{ on: item.id === form_info.type }"
          class="type"
          @click="form_info.type = item.id"
          >{{ item.title }}</text
        >
      </view>
    </view>
    <view class="client-list">
      <view
        class="client-item row"
        :class="{ isactive: item.id === is_select_id }"
        v-for="item in client_list"
        :key="item.id"
        @click="is_select_id = item.id"
      >
        <view class="left">
          {{ item.cname[0] }}
        </view>
        <view class="right">
          <view class="t">{{ item.cname }}</view>
          <view class="b">{{ item.mobile }}</view>
        </view>
      </view>
    </view>
    <load-more :status="load_status"></load-more>
    <view class="btn-bottom">
      <view class="btn-box row">
        <view class="btn plain" @click="$navigateBack()">取消</view>
        <view class="btn" @click="onCreateData">提交</view>
      </view>
    </view>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore.vue";
export default {
  components: {
    loadMore,
  },
  data() {
    return {
      form_info: {
        one_client_id: "",
        two_client_id: "",
        type: 1,
      },
      params: {
        page: 1,
        per_page: 10,
        client_id: "",
      },
      type_list: [
        { id: 1, title: "合并到其他" },
        { id: 2, title: "合并到当前" },
      ],
      client_list: [],
      load_status: "",
      is_select_id: "",
    };
  },
  onLoad(options) {
    this.params.client_id = options.id;
    this.getMergeCustomer();
  },
  methods: {
    getMergeCustomer() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.client_list = [];
      }
      this.$ajax.get("/qywx/client/merge_client_list", this.params, (res) => {
        this.load_status = "loadend";
        if (res.statusCode === 200) {
          this.client_list = this.client_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    onCreateData() {
      if (!this.is_select_id) {
        uni.showToast({
          title: "请选择客户",
          icon: "none",
        });
        return;
      }
      if (this.form_info.type == 1) {
        this.form_info.one_client_id = this.params.client_id;
        this.form_info.two_client_id = this.is_select_id;
      } else {
        this.form_info.one_client_id = this.is_select_id;
        this.form_info.two_client_id = this.params.client_id;
      }
      this.$ajax.post("/qywx/client/merge", this.form_info, (res) => {
        if (res.statusCode === 200) {
          this.$navigateTo(
            `/customer/detail?id=${this.params.client_id}&form=2`
          );
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getMergeCustomer();
  },
};
</script>

<style scoped lang="scss">
page {
  color: #2e3c4e;
}
.merge {
}
.info {
  padding: 24px 12px 0;
  align-items: center;
  // height: 110rpx;
  line-height: 55px;
  .lab {
    align-items: center;
    flex: 1;
  }

  .type_val {
    justify-content: space-between;
    font-size: 14px;
    height: 70px;
    .v {
      width: 72px;
      height: 63px;
      background: #f8f8f8;
      color: #8a929f;
      text-align: center;
      border: 1px solid #f8f8f8;
      line-height: 16px;
      padding-top: 12px;
      text:last-child {
        font-size: 12px;
        margin-top: 5px;
      }
      &.on {
        color: #2d84fb;
        background: #fff;
        border: 1px solid #2d84fb;
        border-radius: 4px;
      }
    }
    .type {
      width: 45%;
      background: #f8f8f8;
      border: 1px solid #f8f8f8;
      border-radius: 4px;
      color: #8a929f;
      text-align: center;
      height: 40px;
      line-height: 40px;
      &.on {
        border: 1px solid #2d84fb;
        background: #ffffff;
        color: #2d84fb;
      }
    }
  }
  .tit {
    font-weight: 500;
    width: 90px;
  }
}
.client-list {
  .client-item {
    text-align: center;
    align-items: center;
    padding: 10px 12px;
    border-bottom: 1px solid #f6f6f6;
    .left {
      width: 30px;
      line-height: 30px;
      height: 30px;
      border-radius: 50%;
      color: #fff;
      background: #2d84fb;
      margin-right: 12px;
    }
    .right {
      align-items: flex-start;
      line-height: 20px;
    }
    &.isactive {
      background: #f6f6f6;
    }
  }
}
.btn-bottom {
  height: 100px;
  .btn-box {
    background: #fff;
    justify-content: space-between;
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 24px;
    .btn {
      height: 42px;
      width: 100%;
      line-height: 42px;
      text-align: center;
      color: #fff;
      background: #2d84fb;
      border-radius: 6px;
    }
    .plain {
      background: #fff;
      color: #333;
      border: 1px solid #999;
      margin-right: 20px;
    }
  }
}
</style>
