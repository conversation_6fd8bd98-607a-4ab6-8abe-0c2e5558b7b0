<template>
  <view class="popup-page" :class="{ center: position == 'center', show: show }">
    <view
      @touchmove.stop.prevent="stopMove"
      ref="popRef"
      class="popup-box"
      :class="{
        show: show,
        bottom: position == 'bottom',
        top: position == 'top',
        left: position == 'left',
        right: position == 'right',
        center: position == 'center',
        top_0: top_0,
      }"
      :style="{ bottom: bottom, height: height || slot_height }"
    >
      <view class="slot_container" id="slot_container">
        <slot></slot>
      </view>
    </view>
    <view
      class="mask"
      :class="{ show: show && showMask }"
      @click.stop.prevent="clickMask"
      @touchmove.stop.prevent="stopMove"
    ></view>
  </view>
</template>

<script>
export default {
  data () {
    return {
      slot_height: 'initial',
    }
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    position: {
      type: String,
      default: 'bottom',
    },
    showMask: {
      type: Boolean,
      default: true,
    },
    touch_mask_close: {
      type: Boolean,
      default: true,
    },
    top_0: {
      type: Boolean,
      default: false,
    },
    bottom: {
      type: [String],
      default: '0',
    },
    height: {
      type: [String, Number],
      default: 0,
    },
  },
  model: {
    prop: 'show',
    event: 'close',
  },
  mounted () {
    // this.$nextTick(() => {
    //   var query = uni.createSelectorQuery().in(this)
    //   query
    //     .select('#slot_container')
    //     .boundingClientRect((data) => {
    //       console.log(data)
    //       this.slot_height = data.height + 'px'
    //     })
    //     .exec()
    // })
  },
  methods: {
    close () {
      this.$emit('input', false)
      this.$emit('close', false)
    },
    clickMask () {
      if (!this.touch_mask_close) {
        return
      }
      this.close()
    },
    stopMove () { },
  },
}
</script>

<style lang="scss">
.popup-page {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  transition: 0.26s;
  z-index: -1;
  &.center {
    justify-content: space-between;
    align-items: center;
  }
  &.show {
    z-index: 110;
  }
  .popup-box {
    position: fixed;
    overflow-x: hidden;
    // border-top-left-radius: 40rpx;
    // border-top-right-radius: 40rpx;
    // background-color: #fff;
    z-index: 110;
    &.center {
      position: initial;
      // .slot_container {
      // }
    }
  }
  .popup-box.bottom {
    left: 0;
    width: 100%;
    bottom: 0;
    max-height: 100vh;
    transform: translateY(1000%);
    transition: 0.3s;
  }
  .popup-box.bottom.show {
    transform: translateY(0);
  }
  .popup-box.top {
    width: 100%;
    // top: 0;
    /* #ifdef H5 */
    top: 44px;
    /* #endif */
    /* #ifndef H5 */
    top: var(--window-top);
    /* #endif */
    max-height: 100vh;
    transform: translateY(-110%);
    transition: 0.4s;
    &.top_0 {
      top: 0;
    }
  }
  .popup-box.top.show {
    transform: translateY(0);
  }
  .popup-box.center {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    opacity: 0;
    align-items: center;
    justify-content: center;
    z-index: -1;
    transition: 0.26s;
    text-align: center;
  }
  .popup-box.center.show {
    opacity: 1;
    z-index: 110;
    transform: translateY(0);
  }
  .popup-box.left {
    top: 0;
    bottom: 0;
    left: 0;
    max-width: 60vw;
    min-width: 30vw;
    transform: translateX(-100%);
    transition: 0.3s;
  }
  .popup-box.left.show {
    transform: translateX(0);
  }
  .popup-box.right {
    top: 0;
    bottom: 0;
    right: 0;
    max-width: 60vw;
    min-width: 30vw;
    transform: translateX(100%);
    transition: 0.3s;
  }
  .popup-box.right.show {
    transform: translateX(0);
  }
  .mask {
    position: fixed;
    height: 100vh;
    width: 100%;
    top: 0;
    left: 0;
    background-color: rgba($color: #000000, $alpha: 0);
    z-index: -1;
    transition: 0.3s;
  }
  .mask.show {
    left: 0;
    background-color: rgba($color: #000000, $alpha: 0.5);
    z-index: 100;
  }
}
</style>
