<template>
  <view v-if="popupShow" class="privacy-popup" :class="{ bottom: isBottom }">
    <view class="privacy-box" :class="{ bottom: isBottom }">
      <view class="title">{{ siteName || "" }}隐私保护指引</view>
      <view class="content">
        <view class="des">
          在使用「{{ siteName || "小程序" }}」服务之前，您应当阅读并同意
          <text class="link" @tap="openPrivacyContract">{{
            privacyContractName
          }}</text
          >，以便我们向您提供更优质的服务！
        </view>
        <view class="des">
          我们将尽全力保护您的个人信息及合法权益，感谢您的信任！
        </view>
      </view>

      <view class="btns">
        <button class="item reject" @tap="onRefuse">拒绝</button>
        <button
          id="agree-btn"
          class="item agree"
          open-type="agreePrivacyAuthorization"
          @agreeprivacyauthorization="onAgree"
        >
          同意
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "privacyPopup",
  data() {
    return {
      popupShow: false, //是否显示 popup 弹出
      privacyContractName: "", //隐私协议名称
      privacyAuthResolve: null, //授权Resolve回调
    };
  },
  props: {
    position: {
      type: String,
      default: "center",
    },
  },
  computed: {
    siteName() {
      return this.$store.state.siteName;
    },
    isBottom() {
      return this.position === "bottom";
    },
  },
  mounted() {
    //阻止多个隐私弹层
    if (this.$store.state.isPrivacyPopupMounted) {
      return;
    }
    this.$store.commit("privacyPopupMounted");

    // 查询隐私授权情况
    if (wx.getPrivacySetting) {
      wx.getPrivacySetting({
        success: (res) => {
          console.log(res,"隐私");
          console.log(12121212121);
          if (res.needAuthorization) {
            this.privacyContractName = res.privacyContractName;
            this.onNeedPrivacyAuthorization();
          }
        },
      });
    }
  },

  methods: {
    //监听隐私接口需要用户授权事件
    onNeedPrivacyAuthorization() {
      if (wx.onNeedPrivacyAuthorization) {
        console.log("onNeedPrivacyAuthorization");
        wx.onNeedPrivacyAuthorization((resolve, eventInfo) => {
          console.log("触发本次事件的接口是：" + eventInfo.referrer);
          this.show();
          this.privacyAuthResolve = resolve;
        });
      }
      this.show();
    },
    show() {
      this.popupShow = true;
      uni.hideTabBar({
        animation: true,
      });
    },
    hide() {
      this.popupShow = false;
      uni.showTabBar({
        animation: true,
      });
    },

    //用户点击阅读隐私指引
    openPrivacyContract() {
      wx.openPrivacyContract({
        fail: (e) => {
          console.log("阅读隐私指引 fail:", e);
        },
      });
    },
    //拒绝
    onRefuse() {
      /* // 上报微信平台
				if (typeof this.privacyAuthResolve == 'function') {
					this.privacyAuthResolve({
						event: 'disagree',
					});
				} */

      //直接退出小程序
      //wx.exitMiniProgram();
      if (typeof this.privacyAuthResolve == "function") {
        this.privacyAuthResolve({
          event: "disagree",
        });
      }

      this.hide();
    },
    //同意
    onAgree() {
      // 上报微信平台
      if (typeof this.privacyAuthResolve == "function") {
        this.privacyAuthResolve({
          buttonId: "agree-btn",
          event: "agree",
        });
      }
      this.popupShow = false;
      this.hide();
    },
  },
};
</script>

<style lang="scss" scoped>
.privacy-popup {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  &.bottom {
    align-items: flex-end;
  }
}
.privacy-box {
  width: 632rpx;
  padding: 48rpx;
  box-sizing: border-box;
  background: #fff;
  border-radius: 16rpx;
  &.bottom {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
    padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
    border-radius: 50rpx 50rpx 0 0;
    .btns .reject {
      width: 240rpx;
    }
    .btns .agree {
      width: 400rpx;
    }
  }
  .title {
    text-align: center;
    color: #333;
    font-weight: bold;
    font-size: 38rpx;
  }
  .content {
    color: #666;
    background-color: #fff;
    padding-top: 30rpx;
    text-align: justify;
    line-height: 1.65;
    .des {
      font-size: 30rpx;
      margin-top: 10rpx;
    }

    .des .link {
      color: #488af6;
    }
  }
}

.btns {
  margin-top: 48rpx;
  margin-bottom: 12rpx;
  display: flex;
  width: 100%;
  justify-content: center;
  flex-direction: row;
  .item {
    width: 200rpx;
    overflow: visible;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    border: none !important;
  }
  .reject {
    background: #f4f4f5;
    color: #666;
    font-size: 14px;
    font-weight: 300;
    margin-right: 16rpx;
  }
  .agree {
    // width: 320rpx;
    background: linear-gradient(133deg, #488af6 0%, #166cf9 93.84%);
    color: #fff;
    font-size: 16px;
  }
}
</style>