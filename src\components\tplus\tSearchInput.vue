<template>
    <view class="search-input-wrapper">
        <view class="search-input">
            <myIcon class="icon" type="ic_sousuo3x1" color="#bbb3b3" size="32rpx"></myIcon>
            <view class="search-input-view">
                <input type="text" confirm-type="search" v-model="keywords" @confirm="handelInputConfirm" :focus="focus" placeholder-style="font-size:28rpx;color: rgba(41, 44, 57, 0.4)"
                    :placeholder="placeholder" />
            </view>
        </view>
    </view>
</template>
<script>
import myIcon from '@/components/my-icon';
export default {
    name: 'tPicker',
    props: {
        value: { type: String, default: ''},
        focus: { type: Boolean, default: false},
        placeholder: {type: String, default: '请输入内容搜索'},
    },
    components: {
        myIcon
    },
    data(){
        return {
            keywords: '',
        }
    },
    watch: {
        value: {
            handler(val){
                this.keywords = val;
            },
            immediate: true
        }
    },
    methods: {
        handelInputConfirm(e){
            this.$emit('input', e.detail.value)
            this.$nextTick(()=>{
                this.$emit('search', e)
            })
		},
    }
}
</script>
<style lang="scss" scoped>
.search-input-wrapper{
	display: flex;
	flex-direction: row;
    height: 110rpx;
    padding: 24rpx 32rpx 8rpx;
    background-color: #fff;
    .search-input{
		flex: 1;
        height: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        background-color: #F6F6F6;
        border-radius: 16rpx;
        padding: 0 24rpx;
        .search-input-view{
			flex: 1;
			display: inline-block;
			padding-left: 16rpx;
		}
    }
}
</style>