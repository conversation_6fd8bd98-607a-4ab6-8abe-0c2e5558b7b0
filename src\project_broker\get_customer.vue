<template>
  <view class="item">
    <view class="item-type row" v-for="(item, index) in list" :key="index">
      <view style="width: 100%">
        <text class="detail" @click="copyCtn(item.content)">{{
          item.content
        }}</text>
        <view class="image-box row">
          <block v-for="(item1, index1) in item.imgs">
            <video v-if="$isVideoReg(item1)" :src="item1"></video>
            <image
              v-if="!$isVideoReg(item1)"
              :src="item1 | imageFilter('w_220')"
              mode="aspectFill"
              @click.stop="prevImg(item.imgs, index1)"
            ></image>
          </block>
        </view>
        <view class="row row-bottom">
          <myIcon
            v-if="$isVideoReg(item.imgs[0])"
            type="a-xiazai3x"
            color="#333"
            @click="onDownload(item)"
          ></myIcon>
          <view> </view>
          <view class="bottom row" v-if="is_project_cus">
            <view
              class="bottom-box row"
              style="color:#e01111"
              @click="onDelete(item)"
            >
              <myIcon
                type="shanchu"
                color="#e01111"
                size="28rpx"
                style="margin-right:8rpx"
              ></myIcon>
              删除
            </view>
            <view
              class="bottom-box row"
              @click="
                $navigateTo(
                  `/project_broker/share_content?id=${project_id}&is_edit=${item.id}`
                )
              "
            >
              <myIcon
                type="ic_fankui3x1"
                style="margin-right:8rpx"
                size="28rpx"
              ></myIcon>
              编辑
            </view>
          </view>
        </view>
        <view class="updated">最后修改时间：{{ item.updated_at }}</view>
      </view>
    </view>
    <load-more :status="load_status"></load-more>
    <view
      v-if="is_project_cus"
      class="tianjia"
      @click="$navigateTo(`/project_broker/share_content?id=${project_id}`)"
    >
      <myIcon
        class="tianjia-end"
        type="open"
        color="#fff"
        size="32rpx"
      ></myIcon>
    </view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import loadMore from "../components/loadMore";
import myIcon from "../components/my-icon";
export default {
  components: { loadMore, myIcon },
  data() {
    return {
      list: [],
      load_status: "",
      params: {
        page: 1,
      },
      project_id: "",
      is_project_cus: false,
    };
  },
  onLoad(options) {
    this.project_id = options.id;
    if (options.isPro == 3) {
      this.is_project_cus = true;
    }
    this.getDataList(options.id);
  },
  methods: {
    getDataList(id) {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.list = [];
      }
      this.$ajax.get(
        `/common/project/share/content/all/${id}`,
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.list = this.list.concat(res.data.data);
            this.list.map((item, index) => {
              this.list[index].imgs = [];
              this.list[index].imgs.push(
                item.img_1,
                item.img_2,
                item.img_3,
                item.img_4,
                item.img_5,
                item.img_6,
                item.img_7,
                item.img_8,
                item.img_9
              );
              this.list[index].imgs = this.list[index].imgs.filter(function(s) {
                return s && s.trim();
              });
            });
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
            this.params.page = 1;
          }
        }
      );
    },
    copyCtn(content) {
      this.$copyText(content, () => {
        uni.showToast({
          title: "复制成功",
          icon: "none",
        });
      });
    },
    prevImg(img, idx) {
      uni.previewImage({
        urls: img,
        current: idx,
      });
    },
    onDelete(item) {
      uni.showModal({
        title: "提示",
        content: "是否删除",
        cancelText: "取消",
        confirmText: "确认",
        success: (res) => {
          if (res.confirm) {
            this.$ajax.get(
              `/common/project/share/content/delete/${item.id}`,
              {},
              (res) => {
                if (res.statusCode === 200) {
                  uni.showToast({
                    title: "删除成功",
                    icon: "none",
                  });
                  this.getDataList(this.project_id);
                } else {
                  uni.showToast({
                    title: res.data.message,
                    icon: "none",
                  });
                }
              }
            );
          }
        },
      });
    },
    onDownload(item) {
      var ua = navigator.userAgent.toLowerCase();
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        uni.showToast({
          title: "请在右上角...点击【在浏览器打开】",
          icon: "none",
        });
      } else {
        this.downVideo(item.img_1, "share"); //调用下载函数
        uni.showLoading({
          title: "正在下载",
        });
      }
    },
    downVideo(url, name) {
      var xhr = new XMLHttpRequest();
      xhr.open("GET", url, true);
      xhr.responseType = "arraybuffer"; // 返回类型blob
      xhr.onload = function() {
        if (xhr.readyState === 4 && xhr.status === 200) {
          let blob = this.response;
          // 转换一个blob链接
          let u = window.URL.createObjectURL(
            new Blob([blob], { type: "video/mp4" })
          );
          let a = document.createElement("a");
          a.download = name;
          a.href = u;
          a.style.display = "none";
          document.body.appendChild(a);
          a.click();
          a.remove();
          uni.showToast({
            title: "下载完成",
            icon: "none",
          });
        }
      };
      xhr.send();
    },
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList(this.project_id);
  },
};
</script>

<style scoped lang="scss">
.item {
  padding: 48rpx;
}
.item-type {
  margin-bottom: 48rpx;
  // &:last-child {
  //   margin-bottom: 120rpx;
  // }
  .detail {
    margin-top: 16rpx;
    font-size: 28rpx;
    color: #666666;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // display: -webkit-box;
    // -webkit-line-clamp: 2;
    // -webkit-box-orient: vertical;
  }
  .image-box {
    margin-top: 24rpx;
    flex-wrap: wrap;
    align-items: flex-start;
    image {
      border-radius: 8rpx;
      margin: 4rpx;
      width: 210rpx;
      height: 210rpx;
    }
    video {
      border-radius: 8rpx;
      margin: 4rpx;
      width: 100%;
      height: 310rpx;
    }
  }
}
.tianjia {
  position: fixed;
  right: 48rpx;
  bottom: 80rpx;
  padding: 24rpx 0;
  .tianjia-end {
    text-align: center;
    border-radius: 50%;
    background: #0097fe;
    padding: 24rpx;
  }
}
.bottom {
  justify-content: flex-end;
  margin-top: 24rpx;
  .bottom-box {
    align-items: center;
    margin-left: 24rpx;
    font-size: 26rpx;
  }
}
.row-bottom {
  align-items: flex-end;
  justify-content: space-between;
}
.updated {
  margin-top: 24rpx;
  justify-content: flex-end;
  color: #999;
  font-size: 24rpx;
}
</style>
