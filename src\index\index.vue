<template>
  <view class="index">
    <view style="min-height: 100vh">
      <view
        class="search row"
        :style="{ position: !$store.state.in_mobile ? 'relative' : 'fixed' }"
      >
        <view
          v-if="site_info.city_type == 1"
          class="right"
          @click="$navigateTo('/index/search-build')"
        >
          <myIcon class="icon" type="ic_sousuo3x1" color="#D1D1D1" size="36rpx"></myIcon>
          <input
            class="uni-input"
            :disabled="true"
            type="text"
            placeholder="请输入楼盘名称"
            placeholder-style="font-size:26rpx"
          />
        </view>
        <view
          class="new-search row"
          v-if="site_info.city_type == 2"
          @click="$navigateTo('/index/search-build')"
        >
          <view class="left-n row right-line" @click.stop="onClickMap">
            {{ city.name || '获取定位' }}
            <image
              src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5index/xialajiantou.png"
            ></image>
          </view>
          <view class="line"></view>
          <view class="right-n row">
            <myIcon class="icon" type="ic_sousuo3x1" color="#D1D1D1" size="32rpx"></myIcon>
            <input
              class="uni-input"
              :disabled="true"
              type="text"
              placeholder="请输入楼盘名称"
              placeholder-style="font-size:26rpx"
            />
          </view>
        </view>
      </view>
      <view class="content" :style="{ marginTop: !$store.state.in_mobile ? '0' : '' }">
        <!-- banner -->
        <view class="img-box" v-if="site_info.carousel != 2">
          <banner
            :indicatorDots="true"
            :customIcon="false"
            :customIndicator="false"
            :focus="imgList"
            :radius="true"
            :height="site_info.carousel_height || 210"
          ></banner>
        </view>
        <view class="build-type">
          <!-- <view class="build_list row">
          <swiper
            :disable-touch="build_type_list.length >= 4 ? false : true"
            :display-multiple-items="4"
            class="build-swiper"
          >
            <swiper-item
              class="build-swiper-item"
              v-for="(item, index) in build_type_list"
              :key="index"
            >
              <view
                v-if="item.home_display == 1"
                class="build-item"
                @click="clickType(item.value)"
              >
                <view class="img-box-status"
                  ><image mode="aspectFill" :src="item.icon"></image
                ></view>
                <view class="font">{{ item.name }}</view>
              </view>
            </swiper-item>
          </swiper>
        </view> -->

          <view class="report row" v-if="site_info.button_style == 2">
            <view
              class="report-item row"
              v-for="item in report_list"
              :key="item.index"
              @click="$navigateTo(`${item.path}`)"
              :style="{
                backgroundImage: `url(${item.icon}?x-oss-process=style/w_220)`,
              }"
            >
              <view class="report-font">{{ item.name }}</view>
            </view>
          </view>
          <view class="build_list row" v-else>
            <view
              class="build-item"
              v-for="item in new_type"
              :key="item.id"
              @click="onClickType(item)"
            >
              <view class="img-box-status">
                <image mode="aspectFill" :src="item.img | imageFilter('w_150')"></image>
              </view>
              <view class="font">{{ item.name }}</view>
            </view>
          </view>
          <view class="build-box">
            <view class="build-title row">
              <view class="t-l">{{ site_info.build_menu_name || '推荐楼盘' }}</view>
              <view class="t-r row" @click="onProjectList"
                >更多
                <myIcon style="margin-left: 10rpx" type="you" color="#6F6F6F" size="24rpx"></myIcon>
              </view>
            </view>
            <!-- <my-build
            v-for="item in build_list"
            :key="item.id"
            :item="item"
            @click="$navigateTo(`/build/detail?buildID=${item.build_id}`)"
            ><template v-slot:yong v-if="display_brokerage">
              <view class="price-right row" v-if="item.brokerage_rule">
                <text class="yong">佣</text>
                <text class="jiage">{{
                  item.store_brokerage_rule || item.brokerage_rule
                }}</text>
              </view>
            </template></my-build
          > -->
            <myBuild
              v-if="build_list.length"
              @click="onBuildDetail"
              :list="build_list"
              :is_display="display_brokerage"
              :type="site_info.build_menu_style || 1"
            ></myBuild>
          </view>
        </view>
      </view>
    </view>
    <myLoading ref="loading" :custom="false" :shadeClick="true" :type="1"></myLoading>
    <load-more :status="load_status"></load-more>
    <shareTip :show="recommend_friends" @hide="recommend_friends = false"></shareTip>
    <BottomBar @click="switchTab" :current="currentTabIndex"></BottomBar>
  </view>
</template>

<script>
import { mapActions, mapMutations, mapState } from "vuex";
import myIcon from "@/components/my-icon";
import colorIcon from "@/components/color-icon";
import shareTip from "@/components/shareTip";
// import myBuild from "@/components/build-item";
import myBuild from "@/components/new_build_list";
import myLoading from "@/components/my-loading";
import banner from "@/components/banner";
import loadMore from "@/components/loadMore";
import location from "../page_outside/tools/get_location.js";
export default {
  mixins: [location],
  components: {
    myLoading,
    banner,
    myIcon,
    myBuild,
    colorIcon,
    loadMore,
    shareTip,
  },
  data () {
    return {
      build_type_list: [],
      build_status_list: [],
      build_category_list: [],
      report_list: [
        {
          index: 1,
          name: "我的客户",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5index/wdkh1.png",
          path: "/client/list?type=-1",
        },
        {
          index: 2,
          name: "快速报备",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5index/ksbb1.png",
          path: "/report/report_client?currentTel=1",
        },
      ],
      new_type: [
        {
          id: 1,
          name: "我的客户",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5index/wdkh.png",
          path: "/client/list?type=-1",
        },
        {
          id: 2,
          name: "快速报备",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5index/ksbb.png",
          path: "/report/report_client?currentTel=1",
        },
        {
          id: 3,
          name: "佣金明细",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5index/yjmx.png",
          path: "/commission/commission_detail",
        },
        {
          id: 4,
          name: "推荐好友",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5index/tjhy.png",
        },
      ],
      build_list: [],
      params: {
        page: 1,
        build_category: 0,
        region_0: 0,
        region_1: 0,
      },
      imgList: [],
      load_status: "",
      recommend_friends: false,
      site_info: {},
      currentTabIndex: 0,
      display_brokerage: false,
    };
  },
  onShow () {
    if (["wxwork", "com-wx-pc"].includes(this.$isWxWork())) {
      // 企业微信弹窗解决
      // this.getWxQyWxConfig();
    }
    this.setUrlWebsiteId();
  },

  onReady () {
    // this.$refs.loading.open();
  },
  onLoad (options) {
    this.init();
  },
  computed: { ...mapState(["siteConfig", "city"]) },
  watch: {
    city (val, oval) {
      if (this.site_info.city_type == 1) {
        return;
      }
      this.params.page = 1;
      this.params.region_0 = val.region_0;
      this.params.region_1 = val.region_1;
      this.getDataList();
    },
  },
  methods: {
    ...mapActions(["getSetting"]),
    ...mapMutations(["setCityData"]),
    //
    switchTab (index, item) {
      if (this.currentTabIndex == index) {
        return;
      }
      uni.switchTab({
        url: item.path,
      });
    },
    init () {
      // 佣金是否显示
      this.getSetting((e) => {
        this.site_info = e;
        /**
         * @description 佣金显示规则
         * @display === 1 所有状态显示
         * @display === 0 仅在登录状态下显示
         * */
        this.getBuildLabels(e.id);
        let display = e.login_display_brokerage_rule;
        let token = uni.getStorageSync("token" + this.$store.state.website_id);
        if (display == 1 || token) {
          this.display_brokerage = true;
        } else if (display == 0) {
          this.display_brokerage = false;
        }
        uni.setNavigationBarTitle({
          title: e.name,
        });
        this.share = {
          forward_title: e.share_title,
          forward_desc: e.share_description,
          forward_pic: e.share_img,
          forward_config: "index",
        };
        // if (this.$isWxWork() === "wxwork" || this.$isWxWork() === "com-wx-pc") {
        //   this.getWxQyWxConfig();
        // }
        // 总控开启是否是多城市模式
        if (e.city_type == 2) {
          this.getLocation(); // 获取位置
          this.params.page = 1;
          this.params.region_0 = this.city.region_0;
          this.params.region_1 = this.city.region_1;
        } else {
          this.params.page = 1;
          this.getDataList();
        }
        // 添加查询是否授权公众号，授权后转发功能正常使用
        this.$ajax.get(
          "/common/sys_conf/wx_open_auth/query/base/pub",
          {},
          (res) => {
            if (res.statusCode === 200) {
              if (res.data.id !== 0 && res.data.updated_at !== "") {
                // 微信公众号分享
                this.share = {
                  forward_title: e.share_title,
                  forward_desc: e.share_description,
                  forward_pic: e.share_img,
                };
                this.getWxConfig();
              }
            }
          }
        );
      });
      this.$setDictionary((e) => {
        e.find((item) => {
          switch (item.name) {
            case "BUILD_STATUS":
              // 匹配楼盘属性
              this.build_status_list = item.childs;
              break;
            // 匹配楼盘类型
            case "BUILD_CATEGORY":
              this.build_category_list = item.childs;
              break;
            default:
              break;
          }
        });
      });
    },
    // 获取默认全部标签
    getBuildLabels (website_id) {
      this.$ajax.get(
        `/common/build/category/home?website_id=${website_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.build_type_list = res.data;
            this.build_type_list.sort(this.compare("sort"));
          }
        }
      );
    },
    // 数组排序
    compare (key) {
      return function (value1, value2) {
        var val1 = value1[key];
        var val2 = value2[key];
        return val2 - val1;
      };
    },
    // 获取楼盘数据 测试pushgit
    getDataList () {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.build_list = [];
      }
      this.$ajax.get(
        // /common/project/list?home_recommend=1
        "/common/project/list?home_recommend=1",
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            console.log(res, "111");
            // this.$refs.loading.close();
            this.getImgList();
            this.build_list = this.build_list.concat(res.data.data);
            if (res.data.data.length == 0) {
              this.load_status = "nomore";
              uni.showToast({
                title: "没有更多数据了",
                icon: "none",
              });
            }
            // 匹配楼盘属性，住宅类型
            this.$matchBuildType(
              this.build_list,
              this.build_status_list,
              this.build_category_list
            );
          } else {
            if (this.site_info.city_type == 2) {
              uni.showToast({
                title: "当前定位城市不存在，即将显示全部城市",
                icon: "none",
                success: () => {
                  this.setCityData({
                    name: "全部",
                    region_0: 0,
                    region_1: 0,
                    location_name: this.city.name,
                  });
                  this.params.page = 1;
                  this.params.region_0 = 0;
                  this.params.region_1 = 0;
                  this.getDataList();
                },
              });
            } else {
              uni.showToast({
                title: res.data.message,
                icon: "none",
              });
            }
          }
        }
      );
    },
    // 点击切换项目类型
    clickType (value) {
      this.$navigateTo(`/build/list?type_value=${value}`);
    },
    getImgList () {
      this.$ajax.get(
        `/common/banner/website/all/1`,
        {
          region_0: this.params.region_0,
          region_1: this.params.region_1,
        },
        (res) => {
          if (res.statusCode === 200) {
            if (res.data.length > 0) {
              this.imgList = res.data;
            } else {
              this.imgList = [
                { img: "https://img.tfcs.cn/static/img/que.jpg" },
              ];
            }
          }
        }
      );
    },
    // 点击tabbar添加参数
    setUrlWebsiteId () {
      let nowlink = window.location.href;
      const reg = /\?.+=.{0,}/;
      if (
        nowlink.indexOf("?website_id=") === -1 &&
        nowlink.indexOf("&website_id=") === -1
      ) {
        if (reg.test(nowlink)) {
          nowlink += `&website_id=${this.$store.state.website_id || 1}`;
        } else {
          nowlink += `?website_id=${this.$store.state.website_id || 1}`;
        }
        history.replaceState(null, " ", nowlink);
      }
      this.$store.commit(
        "setWebsiteid",
        this.$getQueryString("website_id") || 1
      );
    },
    onBuildDetail (item) {
      this.$navigateTo(`/build/detail?buildID=${item.build_id}`);
    },
    onProjectList () {
      uni.switchTab({
        url: "/index/project",
      });
    },
    onClickType (item) {
      switch (item.id) {
        case 1:
        case 2:
        case 3:
          this.$navigateTo(item.path);
          break;
        default:
          this.recommend_friends = true;
          break;
      }
    },
    onClickMap () {
      if (this.city.name !== this.city.location_name) {
        this.$navigateTo("/build/city_list");
      } else {
        var that = this;
        uni.showModal({
          title: "提示",
          content: `当前定位为${that.city.name}，是否切换其他区域`,
          success: (res) => {
            if (res.confirm) {
              this.$navigateTo("/build/city_list");
            } else if (res.cancel) {
            }
          },
        });
      }
    },
  },
  onPullDownRefresh: function () {
    this.params.page = 1;
    this.getDataList();
    uni.stopPullDownRefresh();
  },
  onReachBottom () {
    if (this.load_status === "nomore" || this.build_list.length == 0) {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
  onTabItemTap () {
    this.setUrlWebsiteId();
    uni.$emit("closeChat");
  },
};
</script>

<style lang="scss" scoped>
.icon-baobei-xiala {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22icon-baobei-xiala%22%20viewBox%3D%220%200%201031%201024%22%3E%3Cpath%20d%3D%22M515.2%20768c-6.4%200-19.2-6.4-25.6-12.8l-480-512c-12.8-12.8-12.8-32%200-44.8%2012.8-12.8%2032-12.8%2044.8%200l480%20512c12.8%2012.8%2012.8%2032%200%2044.8C534.4%20768%20528%20768%20515.2%20768z%22%20%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M515.2%20768c-6.4%200-12.8%200-19.2-6.4-12.8-12.8-12.8-32%200-44.8l480-505.6c12.8-12.8%2032-12.8%2044.8%200%2012.8%2012.8%2012.8%2032%200%2044.8l-480%20505.6C534.4%20761.6%20528%20768%20515.2%20768z%22%20%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
.icon-font {
  margin: 0 8rpx;
}
.left-title {
  // width: 100rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.city {
  align-items: center;
  .name {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: #333333;
    letter-spacing: 0;
    line-height: 14px;
  }
  .icon {
    margin: 0 16rpx;
  }
  &::after {
    height: 28rpx;
    content: '';
    background: #d8d8d8;
    border: 1px solid #979797;
  }
}
.content {
  margin-top: 100rpx;
  .img-box {
    padding: 14rpx 48rpx 0;
    image {
      height: 400rpx;
      width: 100%;
    }
  }
  .build-type {
    padding: 48rpx;
    .build_list {
      justify-content: space-around;
      text-align: center;
      .build-swiper {
        width: 100%;
        height: 160rpx;
      }
    }
    .build-item {
      align-items: center;
    }
    .img-box-status {
      height: 92rpx;
      width: 92rpx;
      image {
        height: 100%;
        width: 100%;
      }
    }
    .font {
      font-size: 24rpx;
      margin-top: 16rpx;
    }
  }
  .report {
    justify-content: space-between;
    color: #fff;
    font-size: 28rpx;
    .report-item {
      width: 308rpx;
      height: 96rpx;
      line-height: 96rpx;
      border-radius: 20rpx;
      position: relative;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      .report-font {
        position: absolute;
        right: 80rpx;
      }
    }
  }
  .build-box {
    margin-top: 40rpx;
    .build-title {
      color: #333333;
      line-height: 40rpx;
      justify-content: space-between;
      .t-l {
        font-weight: bold;
        font-size: 30rpx;
      }
      .t-r {
        font-size: 24rpx;
        color: #6f6f6f;
        align-items: center;
      }
    }
  }
}
.search {
  padding: 22rpx 48rpx;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  height: 100rpx;
  top: 0;
  left: 0;
  background: #fff;
  width: 100%;
  z-index: 10;
  .left {
    font-size: 28rpx;
    color: #333;
    align-items: center;
    text {
      margin-right: 6rpx;
    }
  }
  .right {
    position: relative;
    width: 100%;
    .icon {
      position: absolute;
      top: 14rpx;
      left: 28rpx;
    }
    input {
      font-size: 28rpx;
      padding-left: 96rpx;
      background: #eee;
      height: 64rpx;
      // width: 530rpx;
      border-radius: 32rpx;
    }
  }
  .new-search {
    height: 80rpx;
    width: 100%;
    background: #f3f3f3;
    border-radius: 40rpx;
    align-items: center;
    .left-n {
      padding: 0 30rpx;
      font-size: 28rpx;
      color: #191c2f;
      align-items: center;
      image {
        margin-left: 10rpx;
        width: 14rpx;
        height: 14rpx;
      }
    }
    .line {
      width: 2rpx;
      height: 30rpx;
      background: #d1d1d1;
    }
    .right-n {
      align-items: center;
      padding-left: 20rpx;
      .uni-input {
        margin-left: 10rpx;
        pointer-events: none;
      }
    }
  }
}
.price-right {
  align-items: center;
  .yong {
    line-height: 40rpx;
    text-align: center;
    width: 40rpx;
    height: 40rpx;
    color: #fff;
    background: #fec923;
    border-radius: 4px;
  }
  .jiage {
    font-size: 24rpx;
    color: #333;
  }
}
.uni-input {
  line-height: 64rpx;
  pointer-events: none;
}
</style>
