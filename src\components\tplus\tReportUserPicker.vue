<template>
    <myPopup :show="show" @close="cancle">
        <view class="filter-wrapper">
            <view class="header">
                <view class="action cancle" @click.stop="cancle">取消</view>
                <view class="title"></view>
                <view class="action confirm" @click.stop="confirm">确认</view>
            </view>
            <view class="body">
                <tCustomPickerView v-model="selectedId" :multiple="multiple" :datas="list" ref="picker" v-if="show"
                    filter-placeholder="搜索成员" filterable :map="{ label: 'name', value: 'values' }"
                    :loading="loading"></tCustomPickerView>
            </view>
        </view>
    </myPopup>
</template>

<script>
import myPopup from '@/components/myPopup';
import tCustomPickerView from '@/components/tplus/tCustomPickerView';
export default {
    components: {
        myPopup, tCustomPickerView
    },
    props: {
        value: { type: [Number, String, Array], default: '' },
        visible: { type: Boolean, default: false },
        multiple: { type: Boolean, default: false },
    },
    data() {
        return {
            loading: false,
            show: false,
            isInited: false,
            curValue: '',
            list: []
        }
    },
    computed: {
        selectedId: {
            get() {
                return this.curValue || '';
            },
            set(val) {
                this.curValue = val;
            }
        }
    },
    watch: {
        value: {
            handler(val) {
                this.selectedId = val
            },
            immediate: true
        },
        visible(val) {
            this.show = val;
            if(val && !this.isInited){
                this.isInited = true;
                this.getList();
            }
        },
        show(val) {
            this.$emit('update:visible', val)
        }
    },
    created() {

    },
    methods: {
        getReportUserIds(){
            return new Promise((resolve,reject) => {
                this.$ajax.get("/admin/crm/config/get_crm_fixed_config", {key: 'report_uid'}, (res) => {
                    if (res.statusCode === 200) {
                        resolve(res.data ? String(res.data).split(',').map(e=>e*1) : [])
                    } else {
                        reject(res)
                        uni.showToast({
                            title: res.data.message,
                            icon: "none",
                        });
                    }
                }, er=>{
                    reject(er)
                })
            })
        },
        async getList(){
            this.loading = true;
            const uids = await this.getReportUserIds().catch(e => {});
            if(!uids){
                this.loading = false;
                return;
            }
            

            this.$ajax.get("/admin/crm/client_follow/userListDepartment", {}, (res) => {
                this.loading = false;
                if (res.statusCode === 200) {
                    this.list = (res.data || []).filter(e => uids.includes(e.values));
                } else {
                    uni.showToast({
                        title: res.data.message,
                        icon: "none",
                    });
                }
            }, er=>{
                console.log(er)
                this.loading = false;
            })
        },
        cancle() {
            this.show = false;
            this.selectedId = this.value;
        },
        confirm() {
            this.show = false;
            this.$emit('input', this.selectedId);
            this.$emit('confirm', this.$refs.picker.getCheckedItem());
        },
    }
}

</script>

<style scoped lang="scss"> .filter-wrapper {
     background-color: #fff;
     border-top-left-radius: 24rpx;
     border-top-right-radius: 24rpx;
     overflow: hidden;
 }

 .header {
     position: relative;
     display: flex;
     flex-direction: row;
     justify-content: space-between;
     align-items: center;
     height: 46px;

     .title {
         flex: 1;
         color: #999;
         text-align: center;
         display: inline-block;
         max-width: 50%;
         overflow: hidden;
         white-space: nowrap;
         text-overflow: ellipsis;
     }

     .action {
         padding: 10px 16px;
         font-size: 17px;

         &.cancle {
             color: #888;
         }

         &.confirm {
             color: #007aff;
         }
     }
 }
</style>