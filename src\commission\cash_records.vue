<template>
  <view class="list">
    <view class="record" v-for="item in withdraw_detail" :key="item.id">
      <text class="top-title">{{ getTime(item.created_at) }}</text>
      <view class="content">
        <view class="top row">
          <text class="left">{{
            item.audit_status == "通过"
              ? "提现成功"
              : item.audit_status === "未审核"
              ? "提现未审核"
              : "提现失败"
          }}</text>
          <text class="right">+{{ item.amount }}</text>
        </view>
        <view class="bottom row">
          <text class="left">{{ item.created_at }}</text>
          <text class="right">审核状态：{{ item.audit_status }}</text>
        </view>
      </view>
    </view>
    <load-more :status="load_status"></load-more>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore";
export default {
  components: {
    loadMore,
  },
  data() {
    return {
      withdraw_detail: [],
      params: {
        page: 1,
        total: "",
      },
      load_status: "",
      audit_list: [],
    };
  },
  onLoad() {
    this.$getDictionaryList("AUDIT_STATUS", {}, (res) => {
      if (res.statusCode === 200) {
        this.audit_list = res.data.data;
        this.getWithdraw();
      }
    });
  },
  methods: {
    getWithdraw() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.withdraw_detail = [];
      }
      this.$ajax.get(
        "/client/my/all/brokerage/withdraw",
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.withdraw_detail = this.withdraw_detail.concat(res.data.data);
            this.withdraw_detail.map((item) => {
              this.audit_list.map((obj) => {
                if (item.audit_status == obj.value) {
                  item["audit_status"] = obj["description"];
                }
              });
            });
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
            }
          } else {
            uni.showToast({
              title: res.data.message || "获取失败",
              icon: "none",
            });
          }
        }
      );
    },
    getTime(oldVal) {
      let time = oldVal.replace(/\s[\x00-\xff]*/g, "");
      let nowTime = this.$getTime();
      if (time == nowTime) {
        return (oldVal = "本月");
      } else {
        return oldVal;
      }
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.getWithdraw();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    this.params.page++;
    this.getWithdraw();
  },
};
</script>

<style scoped lang="scss">
page {
  background: #f5f5f5;
}
.record {
  .top-title {
    padding: 24rpx 48rpx;
  }
  .content {
    line-height: 40rpx;
    padding: 24rpx 48rpx;
    background: #fff;
    .top {
      justify-content: space-between;
      font-size: 28rpx;
      color: #333;
    }
    .bottom {
      justify-content: space-between;
      font-size: 22rpx;
      color: #999;
    }
  }
}
</style>
