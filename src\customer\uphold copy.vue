<template>
  <view>
    <view class="all">
      <view class="title c2">客户信息</view>
      <view class="form">
        <view class="info row">
          <text class="tit">姓名</text>
          <view class="lab row">
            <input type="text" class="c2 input" v-model="push_form.cname" placeholder="请输入客户姓名" />
            <!-- <myIcon class="icon" type="you" size="30rpx" color="#b0b0b0"></myIcon> -->
            <!-- <uni-easyinput v-model="push_form.cname" type="text" placeholder="请输入客户姓名" :clear="false" /> -->
          </view>
        </view>
        <view class="info row" v-if="type == 1" style="align-items: flex-start">
          <text class="tit">电话</text>
          <view class="lab-box row">
            <view class="lab-item row" v-for="(domain, index) in other_mobile" :key="index">
              <input maxlength="11" v-model="domain.mobile" type="text" class="c2 input tar flex-1" placeholder="请输入客户电话" />
              <image v-if="index === 0" class="jia"
                src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/tianjia.png" mode="widthFix"
                @click.prevent="addDomain" />
              <image v-if="index !== 0" class="jia" style="height: 16px"
                src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/jianqu.png" mode="widthFix"
                @click.prevent="removeDomain(domain)" />
            </view>
          </view>
        </view>
        <!-- 维护资料 -->
        <view class="info row" v-if="type == 2" style="align-items: flex-start">
          <text class="tit">电话</text>
          <view class="lab-box row">
            <view class="lab-item row" v-for="(domain, index) in telArr" :key="index + domain.mobile">
              <input maxlength="11" disabled v-model="domain.mobile" type="text"  class="c2 input tar flex-1"
                placeholder="请输入客户电话" />
              <image v-if="index === 0" class="jia" 
                src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/tianjia.png" mode="widthFix"
                @click.prevent="addDomain" />
              <image v-if="index !== 0 || (index === 1 && telArr.length)" class="jia" style="height: 16px"
                src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/jianqu.png" mode="widthFix"
                @click.prevent="removeDomains(index)" />
              <!-- <div v-if="index != 0" style="width: 52rpx; height: 32rpx"></div> -->
            </view>
            <view class="lab-item row" v-for="(domain, index) in other_mobile" :key="index">
              <input maxlength="11" v-model="domain.mobile" type="text" class="c2 input tar flex-1" placeholder="请输入客户电话" />
              <image v-if="index === 0 && !telArr.length" class="jia"
                src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/tianjia.png" mode="widthFix"
                @click.prevent="addDomain" />
              <image v-if="index !== 0 || (index === 0 && telArr.length)" class="jia" style="height: 16px"
                src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/jianqu.png" mode="widthFix"
                @click.prevent="removeDomain(domain)" />
            </view>
          </view>
        </view>
        <view class="info row">
          <text class="tit">性别</text>
          <uni-data-select v-model="push_form.sex" :localdata="sex_list" :clear="false"
            @change="change"></uni-data-select>
        </view>
        <!--录入客户 城市 -->
        <view class="info row" v-if="type == 1 && is_show_city == 1">
          <text class="tit"> 城市</text>
				<uni-data-picker
				placeholder="请选择城市"
				popup-title="请选择所在地区"
				:localdata="dataTree"
				 v-model="id"
				@change="onchange"
				@nodeclick="onnodeclick"
				@popupopened="onpopupopened"
				@popupclosed="onpopupclosed"
				:map="{
					text:'name',value:'id'
				}"
				>
				</uni-data-picker>
        </view>
        <!-- 维护资料 -->
              <view class="info row" v-if="type == 2 && c_detail.is_show_city==1">
          <text class="tit"> 城市</text>
				<uni-data-picker
				placeholder="请选择城市"
				popup-title="请选择所在地区"
				:localdata="dataTree"
				 v-model="city_name"
				@change="onchange"
				@nodeclick="onnodeclick"
				@popupopened="onpopupopened"
				@popupclosed="onpopupclosed"
				:map="{
					text:'name',value:'id'
				}"
				>
				</uni-data-picker>
        </view>
        <!-- 录入客户 -->

        <view class="info row" v-if="type == 1 && type_list.length > 0">
          <text class="tit">类型</text>
          <uni-data-select v-model="push_form.type" :localdata="type_list" :clear="false"
            @change="onChangeType"></uni-data-select>
        </view>
        <!-- 维护资料 -->
        <view class="info row" v-if="type == 2 && type_list.length > 0">
          <text class="tit">类型</text>
          <uni-data-select v-model="push_form.type" :localdata="type_list" :clear="false"
            @change="onChangeType"></uni-data-select>
        </view>
        <!-- 等级 -->
        <view class="info row" v-if="type == 1">
          <text class="tit">等级</text>
          <uni-data-select v-model="push_form.level_id" :localdata="level_list" :clear="false"
            @change="change"></uni-data-select>
        </view>
        <!-- 维护资料 -->
        <view class="info row" v-if="type == 2">
          <text class="tit">等级</text>
          <uni-data-select  v-model="push_form.level_id" :localdata="level_list" :clear="false"
            @change="changes"></uni-data-select>
        </view>
        <!-- 标签 -->
        <view class="info row" v-if="type == 1">
          <text class="tit">标签</text>
          <view style="height: auto; min-height: 55px" class="lab row" @click="label_hide = true">
            <input type="text" class="c2 input" placeholder="请选择" disabled="true" />
            <view class="TagLabel">
              <view v-for="(item, index) in UpLabel" :key="index" class="TagLabel-box">
                {{ item.name }}
              </view>
            </view>
            <myIcon class="icon" type="you" size="30rpx" color="#b0b0b0"></myIcon>
          </view>
        </view>
        <view class="info row" v-if="type == 2">
          <text class="tit">标签</text>
          <view style="height: auto; min-height: 55px" class="lab row" @click="label_hide = true">
            <input type="text" class="c2 input" placeholder="请选择" disabled="true" />
            <view class="TagLabel">
              <view v-for="(item, index) in UpLabel" :key="index" class="TagLabel-box">
                {{ item.name }}
              </view>
            </view>
            <myIcon class="icon" type="you" size="30rpx" color="#b0b0b0"></myIcon>
          </view>
        </view>
      </view>
      <view class="title c2">需求</view>
      <!-- 来源 -->
      <view class="forms" v-if="source_list.length > 0">
        <view class="info row">
          <text class="tit">来源</text>
          <!-- <picker @change="onChangeSource" :range="source_list" range-key="title" class="lab" :value="is_source_index">
            <view :class="push_form.source_id ? '' : 'novalue'" class="row val">
              <text class="input c2">{{ source_list[is_source_index].title || '请选择' }}</text>
              <myIcon class="icon" type="you" size="30rpx" color="#b0b0b0"></myIcon>
            </view>
          </picker> -->
          <uni-data-select  v-model="push_form.source_id" :localdata="source_list" :clear="false"
            @change="onChangeSource"></uni-data-select>
        </view>
        <!-- 意向 -->
        <view class="infoer">
          <text class="tit">意向</text>
          <!-- <view class="labs">
            <input v-model="push_form.intention_community" type="text" class="c2 input flex-1" placeholder="请输入意向" />
          </view> -->
          <view class='top-card-contenters' >
            <textarea style="display: flex;flex-direction: row; align-items: center;" fixed='true' contenteditable="true"
              v-model="push_form.intention_community" auto-height="true"  @input="updateCounts"
             placeholder="请在这里输入" maxlength='100'></textarea>
            <view style="text-align: right;padding-bottom: 24rpx;">{{ counts || push_form.intention_community ? push_form.intention_community.length
              : 0 }} / {{
    maxLength }}</view>
          </view>
        </view>
        <!-- 备注 -->
        <view class="infoer">
          <text class="tit">备注</text>
        <view class='top-card-contenter'>
          <view >
            <textarea style="display: flex;flex-direction: row; align-items: center;" fixed='true' contenteditable="true"
              v-model="push_form.remark" auto-height="true"  @input="updateCount"
             placeholder="请在这里输入" maxlength='100'></textarea>
            <view style="text-align: right;padding-bottom: 24rpx;">{{ count || push_form.remark ? push_form.remark.length
              : 0 }} / {{
    maxLength }}</view>
          </view>
        </view>
        </view>
      </view>
    </view>
    <view class="btn-bottom">
      <view class="btn-box row items-center">
        <view class="flex-1 flex-row items-center" v-if="type == 1">
          <view style="margin-right: 8rpx"> 私客 </view>
          <view class="flex-row items-center justify-center">
            <switch :checked="push_form.add_type == 1" @change="checkboxChange" />
          </view>
        </view>
        <view class="btn plain flex-1" @click="$navigateBack()">取消</view>
        <view class="btn flex-1" @click="onCreateData">提交</view>
      </view>
    </view>
    <myPopup :show="label_hide" @hide="label_hide = false">
      <view class="Label-box">
        <view class="Label-box-header">
          <span @click="Label_reset">重置</span>
          <span @click="confirmUpLabel">确定</span>
        </view>
        <view class="Label-box-footer">
          <view class="Label-main-left">
            <scroll-view :scroll-y="true" class="filter_labels">
              <view v-for="item in Label_list" :key="item.id" @click="changeSelect(item)"
                :class="{ active: item.is_true }">
                {{ item.name }}
              </view>
            </scroll-view>
          </view>
          <view class="Label-main-right">
            <scroll-view scroll-y class="filter_labels">
              <view v-for="item in Label_list" :key="item.id" class="filter_labels_box">
                <view class="filter_labels_main" v-for="(arr, index) in item.label" :key="index"
                  v-show="arr.parentid == Label_parentid">
                  <view style="flex: 1; min-width: " @click="LabelTitleChange(arr)">{{
                    arr.name
                  }}</view>
                  <checkbox-group class="filter_labels_select" @change="LabelChange($event, arr)">
                    <label>
                      <checkbox :value="arr.id.toString()" :checked="arr.is_select" />
                    </label>
                  </checkbox-group>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>
    </myPopup>
  </view>
</template>

<script>
import myIcon from "@/components/my-icon";
import myPopup from "@/components/myPopup";
import { __esModule } from '../components/time-picker/utils.min';
export default {
  components: {
    myIcon,
    myPopup,
  },

  data() {
    return {
      type: 1, //1:录入客户 2：维护资料
      push_form: {
        cname: "",
        source_id: 19,
        level_id: 12,
        type: "",
        sex: 1,
        subsidiary_mobile: "",
        intention_community: "",
        add_type: 1,
        // intention_street: "",
        remark: "",
        label: "", // 客户标签
      },
      is_source_index: 0,
      sex_list: [
        { value: 1, text: "男" },
        { value: 2, text: "女" },
      ],
      level_list: [],
      type_list: [],
      telArr: [],
      other_mobile: [],
      other_mobile_back: [],
      source_list: [],
      is_type_index: 0,
      label_hide: false, // 控制客户标签弹出框
      Label_list: [], // 客户标签列表
      Label_parentid: 311, // 存储当前选中的父级标签id,默认311
      selected_label: [], // 已选中的标签
      UpLabel: [], // 展示的客户标签
      c_detail: {},
      count: 0, 
      counts: 0, 
      maxLength: 100,
      id: '',
      dataTree:[],
      is_show_city:"",
 city_name:[]
    };
  },
  onLoad(options) {
    if (options.wxuserid) {
      this.wxuserid = options.wxuserid;
      this.getQwUserDetail();
    }
    this.push_form.wxqy_id = options.wxuserid;
    if (options.type) {
      this.type = options.type;
    }
    this.getLevelList();
    this.getTypeList();
    this.getSourceList();
    this.getCustomerLabel();
    if (options.id) {
      this.push_form.id = options.id;
      this.getUserDetail();
    }
    let name;
    if (this.type == 1) {
      name = "录入客户";
    } else {
      name = "维护资料";
    }
    wx.setNavigationBarTitle({
      title: name,
    });
    // 判断录入客户和维护客户
    if(this.type == 1) {
      this.other_mobile = [{mobile:""}]
    }else if (this.type == 2){
      this.other_mobile = []
    }
    this.cityBtn()
    this.showCity()
  },
  methods: {
    onnodeclick(e) {
				console.log(e,'点击');

			},
			onpopupopened(e) {
				console.log('popupopened');
           this.cityBtn()
			},
			onpopupclosed(e) {
				console.log('关闭弹窗回调');
			},
			onchange(e) {
    
          console.log('关闭弹窗 => 给你的最终数据:', e.detail.value);
       this.push_form.province_id = e.detail.value[0]?.value
       this.push_form.city_id = e.detail.value[1]?.value
       this.push_form.area_id = e.detail.value[2]?.value
       console.log(this.push_form.province_id);
       console.log(this.push_form.city_id);
       console.log(this.push_form.area_id);
       
			},
        // 城市三级联动
cityBtn(){
  this.$ajax.get('/qywx/region/list',{},(res)=>{
    if(res.statusCode === 200){
       this.dataTree = res.data
    }
  })
			},
      // 判断is_show_city 是1显示0隐藏
showCity(){
  this.$ajax.get('/admin/crm/config/get_crm_fixed_config',{key:'is_show_city' },(res)=>{
    if(res.statusCode === 200){
   console.log(res,"===");
   this.is_show_city = res.data
   console.log(this.is_show_city,"000");
    }
  })
			},

    change(e) {
      console.log(e, "[[]]");
    },
    changes(e) {
      console.log(e, "[[]]");
    },
    disMove(e) { e.preventDefault; e.stopPropagation },
    // 客户详情
    getUserDetail() {
      this.$ajax.get(`/qywx/client/info/${this.push_form.id}`, {}, (res) => {
        if (res.statusCode === 200) {
          let c_detail = res.data;
          this.c_detail = res.data;
          this.push_form = {
            id: c_detail.id,
            cname: c_detail.cname,
            source_id:"" ,
            sex:"",
            level_id:"",
            // type: c_detail.type,
            type: c_detail.client_type.id,
            intention_community: c_detail.intention_community,
            remark: c_detail.remark,
          };
          if(c_detail.source_id == 0 || c_detail.source_id === null){
            console.log(333);
            this.push_form.source_id = 19
          }else{
            this.push_form.source_id = c_detail.source_id
          }
          if(c_detail.sex == 0 || c_detail.sex === null){
            console.log(222);
            this.push_form.sex = 1
          }else{
            this.push_form.sex = c_detail.sex
          }
          if( c_detail.level_id == 0 ||  c_detail.level_id === null){
            console.log(111);
            this.push_form.level_id = 12
          }else{
            this.push_form.level_id = c_detail.level_id
          }
          
          if(c_detail.province!=null || c_detail.city!= null || c_detail.area !=null){
            this.city_name.push(c_detail.province,c_detail.city,c_detail.area)
         console.log(this.city_name,"000");
          }else{
            console.log(222);
          }

        

        
          let tel = c_detail.mobile ? (c_detail.subsidiary_mobile ? (c_detail.mobile + "," + c_detail.subsidiary_mobile) : c_detail.mobile) : ''
          // let tel = c_detail.mobile ? (c_detail.mobile + "," + c_detail.subsidiary_mobile) : '';
          let telarr = tel ? tel.split(",") : [];
          this.telArr = telarr.map((item) => {
            return { mobile: item.replace(/(\d{3})\d{5}(\d{3})/, "$1*****$2") }
          });
          this.other_mobile_back = telarr.map((item) => {
            return { mobile: item };
          });
          // 将维护资料客户信息赋值
          this.CustomerType(c_detail);
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    updateCount() {
      this.count = this.push_form.remark.length; // 更新字数计数
      // this.value ? this.value.length :
    },
    updateCounts() {
      this.counts = this.push_form.intention_community.length; // 更新字数计数
      // this.value ? this.value.length :
    },
    // 获取维护客户信息进行赋值
    CustomerType(val) {
      // console.log(val,"查看参数");
      // 更新 类型
      // this.type_list.map((item, index) => {
      //   if (item.id == val.client_type.id) {
      //     this.is_type_index = index;
      //   }
      // })
      // // 更新 来源
      // if (this.source_list.length != 0) {
      //   this.source_list.map((item, index) => {
      //     if (item.id == val.source.id) {
      //       this.is_source_index = index;
      //     }
      //   })
      // }
      // 更新 标签
      let defaults = val.label.split(","); // 已选择的标签
      let num = [];
      defaults.map((item) => {
        this.Label_list.map((arr) => {
          arr.label.map((list) => {
            if (list.id == item) {
              list.is_select = true;
              this.selected_label.push({ name: list.name, values: list.id });
            }
          })
        })
      })
      this.UpLabel = Object.assign({}, this.selected_label);
      // console.log(this.selected_label,"结果");
      this.setCustomerLabel();
    },
    // 获取企业微信用户信息并赋值这里点录入的时候， 姓名， 来源，添加方式，  可以传进去。 客户来源——客户意向，   添加方式——备注
    getQwUserDetail() {
      this.$ajax.get(`/qywx/client/qws_info/${this.wxuserid}`, {}, (res) => {
        if (res.statusCode === 200) {
          let c_detail = res.data.wx_info;
          this.c_detail = res.data;
          this.push_form = {
            cname: c_detail.name,
            sex: c_detail.gender || 1,
            intention_community: c_detail.type == 1 ? '微信用户' : '企业微信',
            remark: c_detail.add_way_title,
          };
          let tel = c_detail.mobile || c_detail.tel
          let telarr = tel ? tel.split(",") : [];
          this.telArr = telarr.map((item) => {
            return { mobile: item.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2") }
          });
          this.other_mobile_back = [{ mobile: c_detail.mobile || c_detail.tel }]
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    checkboxChange(e) {
      console.log(e);
      if (e.detail.value) {
        this.push_form.add_type = 1
      } else {
        this.push_form.add_type = 2
      }
    },
    getSourceList() {
      this.$ajax.get("/qywx/source/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.source_list = res.data;
          console.log(this.source_list,"this.source_list");
          for(let item of this.source_list){
            item.value = item.id
            item.text = item.title
            delete item.id
            delete item.title
            delete item.order
            delete item.is_default
          }
          // if (!this.push_form.id) {
          //   this.push_form.source_id = res.data.filter((item, index) => {
          //     // 默认选中来源方式
          //     if (item.is_default == 1) {
          //       this.is_source_index = index;
          //     }
          //     return item.is_default == 1;
          //   })[0]?.id;
          // } else {
          //   this.source_list.map((item, index) => {
          //     this.is_source_index = index;
          //   });
          // }
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    getTypeList() {
      this.$ajax.get("/qywx/type/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.type_list = res.data;
 
          for(let trpe of this.type_list){
            trpe.text = trpe.title
            trpe.value = trpe.id
            delete trpe.title
            delete trpe.id
          }
          console.log(this.type_list[0].value,"999");
          this.push_form.type = this.type_list[0]?.value ||"没有来源"
          // if (!this.push_form.id) {
          //   this.push_form.type = res.data.filter((item, index) => {
          //     // 默认选中来源方式
          //     if (item.is_default == 1) {
          //       this.is_type_index = index;
          //     }
          //     return item.is_default;
          //   })[0]?.id;
          // } else {
          //   this.type_list.map((item, index) => {
          //     this.is_type_index = index;
          //   });
          // }
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    getLevelList() {
      this.$ajax.get("/qywx/level/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.level_list = res.data;
          // console.log(this.level_list, "000000");
          // uniapp官网严格要求下拉内容，进行转化
          for(let item of this.level_list){
            // console.log(item.title,item.desc,"===");
            item.text = item.title + item.desc
            // console.log(item.text,"---");
            delete item.title
            delete item.desc
            item.value = item.id
            delete item.id
            // console.log(item);
          }
          // console.log(this.level_list,"====");
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },

    removeDomain(item) {
      var index = this.other_mobile.indexOf(item);
      if (index !== -1) {
        this.other_mobile.splice(index, 1);
      }
    },
    removeDomains(index) {
      this.telArr.splice(index, 1)
      this.other_mobile_back.splice(index, 1)
      console.log(this.other_mobile_back, 'this.telArr');
    },
    addDomain() {
      this.other_mobile.push({
        mobile: "",
      });
    },
    onChangeSource(e) {
      console.log(e);
      // this.is_source_index = e.detail.value;
      // this.push_form.source_id = this.source_list[e.detail.value].id;
    },
    onChangeType(e) {
      console.log(e);
      // this.is_type_index = e.detail.value;
      // this.push_form.type = this.type_list[e.detail.value].id;
    },
    // 点击客户等级时触发
    selectGrade(item) {
      this.push_form.level_id = item.id;
      this.$forceUpdate();
    },
    onCreateData() {
      let arr = [], othertel = []
      // 当前有手机号
      // this.push_form.subsidiary_mobile = ''

      if (this.c_detail && this.c_detail.mobile) {
        this.push_form.mobile = this.c_detail.mobile;
        if (this.other_mobile.length) {
          arr = this.other_mobile.map((item) => {
            return item.mobile;
          });
        }
        othertel = this.other_mobile_back.map(item => item.mobile).filter((item, index) => {
          if (index) {
            return item;
          }
        })
        // console.log(othertel, 'othertel');
        this.push_form.subsidiary_mobile = arr.concat(othertel).join(",")
        // console.log(this.push_form.subsidiary_mobile, ' this.push_form.subsidiary_mobile');
      } else {
        if (this.other_mobile.length > 0) {
          let arr = this.other_mobile.map((item) => {
            return item.mobile;
          });
          let othertel = arr.filter((item, index) => {
            if (index) {
              return item;
            }
          });
          this.push_form.mobile = arr[0];
          this.push_form.subsidiary_mobile = othertel.join(",");
          // this.push_form.subsidiary_mobile = toString(this.telArr)
        }
      }
      if (!this.push_form.mobile) {
        uni.showToast({
          title: "请检查联系方式",
          icon: "none",
        });
        return;
      }
      if (!this.push_form.cname) {
        uni.showToast({
          title: "请检查客户姓名",
          icon: "none",
        });
        return;
      }
      if (!this.push_form.sex) {
        uni.showToast({
          title: "请检查客户性别",
          icon: "none",
        });
        return;
      }
      // if (!this.push_form.level_id) {
      //   uni.showToast({
      //     title: "请检查客户等级",
      //     icon: "none",
      //   });
      //   return;
      // }
      // if (!this.push_form.type) {
      //   uni.showToast({
      //     title: "请检查客户类型",
      //     icon: "none",
      //   });
      //   return;
      // }
      // if (!this.push_form.source_id) {
      //   uni.showToast({
      //     title: "请检查客户来源",
      //     icon: "none",
      //   });
      //   return;
      // }
      let num = [];
      this.selected_label.map((item) => {
        num.push(item.values);
      })
      this.push_form.label = num.toString();
      console.log(this.push_form, 'this.push_form');
      for (let index in this.push_form.subsidiary_mobile) {
        console.log(index, '123');
      }
      // return
      // console.log(this.push_form.province_id,"000");
      //  console.log(this.push_form.city_id,"00");
      //  console.log(this.push_form.area_id,"000");
      let url = this.push_form.id
        ? "/qywx/client/update"
        : "/qywx/client/create";
      this.$ajax.post(url, this.push_form, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "操作成功",
          });
          uni.$emit("getDataAgain")
          setTimeout(() => {
            this.$navigateBack()
          }, 300);

          // this.$navigateTo(
          //   `/customer/detail?form=2&id=${res.data || this.push_form.id}`
          // );
        } else if (res.statusCode === 422) {
          let that = this;
          const cus_id = res.data.data && (res.data.data.id != '' && res.data.data.id != undefined) ? res.data.data.id : 0; // 赋值客户id
          this.customerID = cus_id; // 赋值客户id
          // 当客户手机号重复录入时，返回维护跟进人follow_id，如果有就提示已有维护人立即查看。如果没有就提示是否认领
          if ((res.data.data && res.data.data.follow_id) && (res.data.data.follow_id != undefined && res.data.data.follow_id != 0)) {
            this.view_hide = true; // 显示查看客户模态框
            uni.showModal({
              title: "提示",
              cancelText: "取消",
              content: res.data.message,
              confirmText: "立即查看",
              success: (res) => {
	            if(res.confirm) {  
	            	console.log('comfirm') 
                that.$navigateTo(`/customer/detail?id=${that.customerID}&form=2`);//点击确定之后执行的代码
	            } else {  
	            	console.log('cancel') //点击取消之后执行的代码
	            	}  
	            } 
            });
          } else {
            if (this.push_form.id) {
              uni.showToast({
                title: res.data.message || '编辑失败'
              })
              return
            }
            if(that.customerID){
              uni.showModal({
              title: "提示",
              cancelText: "取消",
              content: res.data.message,
              confirmText: "立即认领",
              success: function (res) {
              if(res.confirm){
                let form = {
                  ids: that.customerID + "",
                };
                that.$ajax.post("/qywx/client/get", form, (res) => {
                  console.log(res);
                  if (res.statusCode === 200) {
                    uni.showToast({
                      title: "领取成功",
                      icon: "none",
                    });
                    that.$navigateTo(`/customer/detail?id=${that.customerID}&form=2`);
                  } else {
                    uni.showToast({
                      title: res.data.message,
                      icon: "none",
                    });
                  }
                });
              }else{
                console.log('cancel') //点击取消之后执行的代码
              }
              },
            });
            }else{
              uni.showModal({
              title: "提示",
              cancelText: "取消",
              content: res.data.message,
              confirmText: "确定",
              success: (res) => {
	            if(res.confirm) {  
	            	console.log('comfirm') 
	            } else {  
	            	console.log('cancel') //点击取消之后执行的代码
	            	}  
	            } 
            });
            }
        
          }
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    // 获取客户标签列表
    getCustomerLabel() {
      this.$ajax.get("/admin/crm/label/list", {}, (res) => {
        if (res.statusCode == 200) {
          this.Label_list = res.data;
          this.Label_list.map((item, index) => {
            if (index == 0) {
              item.is_true = true;
            } else {
              item.is_true = false;
            }
            item.label.map((arr) => {
              arr.is_select = false;
            })
          })
          // console.log(this.Label_list, "Label_list");
        }
      })

    },
    // 为已选中的标签蓝色高亮
    setCustomerLabel() {
      // console.log(this.Label_list,"我后执行");
      this.Label_list.map((item) => {
        item.label.map((list) => {
          if (list.is_select) {
            if (item.id == list.parentid) {
              item.is_true = true;
            }
          }
        })
      })
    },
    // 当前选中的标签发生改变
    changeSelect(item) {
      // 取消没有选中标签的蓝色高亮
      this.Label_list.map((list) => {
        list.is_true = false;
      })
      this.Label_parentid = item.id; // 存储当前选中的标签id
      item.is_true = true; // 设置当前选中标签为蓝色高亮
      // console.log(item,"item");
      this.setCustomerLabel();
    },
    // 标签多选框发生改变
    LabelChange(e, item) {
      // console.log(e);
      // 如果选中
      if (e.detail.value[0] != "" && e.detail.value[0] != undefined) {
        item.is_select = true;
      } else { // 如果没选中
        item.is_select = false;
      }
      if (item.is_select) {
        // 选中后将标签名和id赋值
        this.selected_label.push({ name: item.name, values: item.id })
      } else {
        // 如果移除选中就从数据中删除
        this.selected_label.map((arr, index) => {
          if (arr.values == item.id) {
            this.selected_label.splice(index, 1)
          }
        })
      }
      // console.log(this.selected_label,"已选择");
    },
    // 点击标签标题时触发
    LabelTitleChange(item) {
      // console.log(item);
      let num = {};
      if (item.is_select) {
        num = {
          detail: {
            value: []
          }
        }
      } else {
        num = {
          detail: {
            value: [item.id]
          }
        }
      }
      this.LabelChange(num, item);
      this.$forceUpdate();
    },
    // 重置已选择的客户标签
    Label_reset() {
      this.Label_parentid = 311;
      // 隐藏底部弹出框
      // this.label_hide = false;
      // 清空已选择的客户标签
      this.selected_label = [];
      this.UpLabel = [];
      this.Label_list.map((item, index) => {
        if (index == 0) {
          item.is_true = true;
        } else {
          item.is_true = false;
        }
        item.label.map((arr) => {
          arr.is_select = false;
        })
      })
    },
    // 确定选择
    confirmUpLabel() {
      this.label_hide = false;
      // 浅拷贝
      this.UpLabel = Object.assign({}, this.selected_label);
    },
  },
};
</script>
<style scoped lang="scss">
.infoer{
  // display: flex;
  // flex-direction: row;
  // justify-content: space-between;
  // text-align: center;
  // align-items: center;
}
.labs{
 height: 80rpx;
//  text-align: right;
//  border: 2rpx solid #333;
}
page {
  background: #f6f6f6;
  color: #2e3c4e;
}
.beizu {
  // width: 10%;
  margin-top: 32rpx;
  margin-left: 16rpx;
}

.top-card-contenter {

  border-radius: 8rpx;
  border: 2rpx solid #F0F1F5;
  background: #F8F8F8;
  padding: 24rpx 24rpx 0rpx 24rpx;
  margin-top: 32rpx;
margin-bottom: 32rpx;
  // align-items: center;
  &.top-card-contenter:last-child{
    margin-bottom: 0;
  }
}

.top-card-contenters {

  border-radius: 8rpx;
  border: 2rpx solid #F0F1F5;
  background: #F8F8F8;
  padding: 24rpx 24rpx 0rpx 24rpx;
  margin-top: 32rpx;
  margin-bottom: 32rpx;
  // align-items: center;
}

.textarear {
  position: relative;
  // margin-top: 8px;
  padding: 24rpx 0rpx;
  width: 100%;
  padding-left:0rpx;
  outline: none;
  user-select: none
  // margin-left: 42rpx;
}

.title {
  padding: 32rpx 32rpx;
  // color: rgba(41, 44, 57, 0.40);
  font-size: 28rpx;
}

.c2 {
  // color: #8a929f;
}

.all {
  // margin-top: 32rpx;
}

.form {
  // margin: 12px 0;
  background: #fff;
  border-radius: 6px;
  padding: 32rpx;
  font-size: 16px;
  padding-top: 0;

  .tag {
    flex-wrap: wrap;

    text {
      font-size: 11px;
      height: 24px;
      line-height: 24px;
      width: 68px;
      text-align: center;
      background: #f1f4fa;
      border-radius: 12px;
      margin-right: 12px;
      margin-top: 12px;
    }
  }

  .info {
    align-items: center;
    // height: 110rpx;
    line-height: 55px;

    &.ali_s {
      align-items: flex-start;
      line-height: 1;

      .lab {
        height: auto;
      }

      .tit {
        padding-top: 10px;
      }

      textarea {
        padding: 10px 5px;
        width: auto;
      }
    }

    .jia {
      width: 16px;
      margin-left: 10px;
    }

    .lab {
      // height: 55px;
      align-items: center;
      flex: 1;
      // border-bottom: 1px solid #d8d8d8;

      .val {
        align-items: center;
      }

      .tag_val {
        margin-right: 24px;
      }

      .input {
        flex: 1;
        text-align: right;
      }

      .icon {
        margin-top: 2px;
        margin-left: 10px;
      }
    }

    .sex-box {
      justify-content: flex-end;
      align-items: center;

      .sex-item {
        margin-left: 20px;
        background: #f8f8f8;
        padding: 0 10px;
        height: 30px;
        font-size: 13px;
        align-items: center;
        border: 1px solid #f8f8f8;
        border-radius: 4px;
        color: #8a929f;
        text-align: center;

        &.is_active {
          border: 1px solid rgba(45, 132, 251, 1);
        }
      }

      image {
        border-radius: 4px;
        margin-left: 10px;
        border: 1px solid #fff;
        // margin-right: 14px;
        width: 20px;
        height: 20px;
      }
    }

    .type_val {
      justify-content: space-between;
      font-size: 14px;
      height: 70px;

      .v {
        width: 72px;
        height: 63px;
        background: #f8f8f8;
        color: #8a929f;
        text-align: center;
        border: 1px solid #f8f8f8;
        line-height: 16px;
        padding-top: 12px;

        text:last-child {
          font-size: 12px;
          margin-top: 5px;
        }

        &.on {
          color: #2d84fb;
          background: #fff;
          border: 1px solid #2d84fb;
          border-radius: 4px;
        }
      }

      .type {
        width: 45%;
        background: #f8f8f8;
        border: 1px solid #f8f8f8;
        border-radius: 4px;
        color: #8a929f;
        text-align: center;
        height: 40px;
        line-height: 40px;

        &.on {
          border: 1px solid #2d84fb;
          background: #ffffff;
          color: #2d84fb;
        }
      }
    }

    .tit {
      // font-weight: 500;
      min-width: 110rpx;
    }
  }
}
.forms {
  // margin: 12px 0;
  background: #fff;
  border-radius: 6px;
  padding: 32rpx;
  font-size: 16px;
  padding-top: 20rpx;

  .tag {
    flex-wrap: wrap;

    text {
      font-size: 11px;
      height: 24px;
      line-height: 24px;
      width: 68px;
      text-align: center;
      background: #f1f4fa;
      border-radius: 12px;
      margin-right: 12px;
      margin-top: 12px;
    }
  }

  .info {
    align-items: center;
    // height: 110rpx;
    line-height: 55px;
margin-bottom: 32rpx;
    &.ali_s {
      align-items: flex-start;
      line-height: 1;

      .lab {
        height: auto;
      }

      .tit {
        padding-top: 10px;
      }

      textarea {
        padding: 10px 5px;
        width: auto;
      }
    }

    .jia {
      width: 16px;
      margin-left: 10px;
    }

    .lab {
      // height: 55px;
      align-items: center;
      flex: 1;
      // border-bottom: 1px solid #d8d8d8;

      .val {
        align-items: center;
      }

      .tag_val {
        margin-right: 24px;
      }

      .input {
        flex: 1;
        text-align: right;
      }

      .icon {
        margin-top: 2px;
        margin-left: 10px;
      }
    }

    .sex-box {
      justify-content: flex-end;
      align-items: center;

      .sex-item {
        margin-left: 20px;
        background: #f8f8f8;
        padding: 0 10px;
        height: 30px;
        font-size: 13px;
        align-items: center;
        border: 1px solid #f8f8f8;
        border-radius: 4px;
        color: #8a929f;
        text-align: center;

        &.is_active {
          border: 1px solid rgba(45, 132, 251, 1);
        }
      }

      image {
        border-radius: 4px;
        margin-left: 10px;
        border: 1px solid #fff;
        // margin-right: 14px;
        width: 20px;
        height: 20px;
      }
    }

    .type_val {
      justify-content: space-between;
      font-size: 14px;
      height: 70px;

      .v {
        width: 72px;
        height: 63px;
        background: #f8f8f8;
        color: #8a929f;
        text-align: center;
        border: 1px solid #f8f8f8;
        line-height: 16px;
        padding-top: 12px;

        text:last-child {
          font-size: 12px;
          margin-top: 5px;
        }

        &.on {
          color: #2d84fb;
          background: #fff;
          border: 1px solid #2d84fb;
          border-radius: 4px;
        }
      }

      .type {
        width: 45%;
        background: #f8f8f8;
        border: 1px solid #f8f8f8;
        border-radius: 4px;
        color: #8a929f;
        text-align: center;
        height: 40px;
        line-height: 40px;

        &.on {
          border: 1px solid #2d84fb;
          background: #ffffff;
          color: #2d84fb;
        }
      }
    }

    .tit {
      // font-weight: 500;
      min-width: 110rpx;
    }
  }
}
.lab-box {
  flex-direction: column;
  flex: 1;

  .lab-item {
    justify-content: space-between;
    width: 100%;
    // height: 55px;
    margin-top: 24rpx;
    align-items: center;
    // border-bottom: 1px solid #d8d8d8;

    .tar {
      text-align: right;
    }
  }
}

.btn-bottom {
  height: 100px;

  .btn-box {
    background: #fff;
    justify-content: space-between;
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 24px;

    .btn {
      height: 42px;
      width: 100%;
      line-height: 42px;
      text-align: center;
      color: #fff;
      background: #2d84fb;
      border-radius: 6px;
    }

    .plain {
      background: #fff;
      color: #333;
      border: 1px solid #999;
      margin-right: 20px;
    }
  }
}

.Label-box {
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: #ffffff;

  .Label-box-header {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid #e5e5e5;

    span {
      padding: 0 14px;
      font-size: 17px;
      line-height: 45px;
      box-sizing: border-box;
      color: #888888;
    }

    & span:last-child {
      color: #007aff;
    }
  }

  .Label-box-footer {
    width: 100%;
    height: 238px;
    display: flex;
    flex-direction: row;
    // padding: 12px 0;
    box-sizing: border-box;

    .Label-main-left,
    .Label-main-right {
      height: 100%;
      flex: 1;
      padding: 12px 0;
      padding-left: 24px;
      overflow: hidden;
    }

    .Label-main-left {
      .filter_labels {
        height: 100%;
        font-size: 13px;
        line-height: 40px;

        &.active {
          color: #3172f6;
        }
      }
    }

    .Label-main-right {
      background: #f9f9f9;

      .filter_labels {
        height: 100%;
        font-size: 13px;
        line-height: 40px;

        .filter_labels_box {
          display: flex;
          flex-direction: column;

          // justify-content: space-between;
          .filter_labels_main {
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            .filter_labels_select {
              text-align: center;
              margin-right: 24px;
              width: 30px !important;
            }
          }
        }
      }
    }
  }
}

.TagLabel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  align-items: center;

  .TagLabel-box {
    // width: 73px;
    height: 30px;
    line-height: 30px;
    box-sizing: border-box;
    padding: 0 5px;
    font-size: 12px;
    border: 1px solid #e9e9eb;
    background-color: #f4f4f5;
    border-radius: 4px;
    color: #909399;
    margin-bottom: 5px;
    margin-left: 2px;
  }
}
</style>
