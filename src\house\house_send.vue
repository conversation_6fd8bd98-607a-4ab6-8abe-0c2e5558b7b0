<template>
  <view class="house_send">
    <view class="tips"> 请选择站点 </view>
    <view class="site_list">
      <view
        class="site_item flex-box"
        v-for="site in siteList"
        :key="site.id"
        @click="toBinding(site)"
      >
        <view class="checked">
          <image
            :src="site.is_checked ? '/static/img/checked.png' : '/static/img/unchecked.png'"
          ></image>
        </view>
        <!-- <view class="img">
          <image :src="site.logo" mode="widthFix"></image>
        </view> -->
        <view class="top flex-row items-center">
          <view class="name flex-1">{{ site.name }} </view>
          <view class="service flex-row items-center">
            <text>
              {{ site.services }}
            </text>
            <text
              :style="{
                color: site.is_open ? '#2d84fb' : '#999',
                'margin-left': '10rpx',
              }"
            >
              {{ site.is_open ? '已开通' : '未开通' }}
            </text>
          </view>
        </view>
        <view class="bottom flex-row items-center space-between">
          <view class="img flex-1">
            <!-- <image :src="site.logo" mode="aspectFill"></image> -->
          </view>
          <view class="status" :class="{ status1: site.is_binding == 0 }">
            {{ site.is_binding == 1 ? '账号已绑定' : '未绑定账号' }}
          </view>
        </view>
      </view>
    </view>
    <view class="btns flex-row">
      <my-button
        class="flex-1"
        type="primary"
        :round="false"
        size="big"
        @click="submit"
        :loading="submiting"
        >确定</my-button
      >
    </view>
  </view>
</template>

<script>
import myButton from './components/myButton'
export default {
  data () {
    return {
      siteList: [],
      submiting: false,
      from: 'my',
      phone: '',
      protect: 0
    }
  },
  components: { myButton },

  onLoad (options) {
    if (options.from) {
      this.from = options.from
    }
    if (options.house_id) {
      this.house_id = options.house_id
    }
    if (options.protect) {
      this.protect = options.protect
    }
    uni.showLoading({
      title: '加载中',
    })
    this.getSiteList()
    uni.$on('bindSiteSuccess', () => {
      this.getSiteList()
    })
  },
  onUnload () {
    uni.$off('bindSiteSuccess')
  },
  methods: {
    submit () {
      this.submiting = true
      if (this.protect == 1) {
        uni.showToast({
          title: '当前房源处于保护期内不能群发',
          icon: 'none'
        })
        return
      }
      let pushArr = []
      this.siteList.map((item) => {
        if (item.is_checked) {
          pushArr.push(item.id)
        }
      })
      let push_ids = pushArr.join(',')
      this.$ajax
        .post(`/admin/house/releaseToMassSite/${this.house_id}`, { push_ids: push_ids }, (res) => {
          if (res.statusCode == 200) {
            uni.showToast({
              title: res.data?.message || '发送成功',
              icon: 'none',
            })
            setTimeout(() => {
              uni.navigateBack()
              // this.$navigateBack()
            }, 1000)
          } else {
            uni.showToast({
              title: res.data?.message || '发送失败',
              icon: 'none',
            })
          }
          this.submiting = false
        }, (err) => {
          uni.showToast({
            title: err.data?.message || '发送失败',
            icon: 'none',
          })
          this.submiting = false
        })
      // .catch(() => {

      // })
    },
    getSiteList () {
      this.$ajax
        .get('/admin/house/getPushSite', {}, (res) => {
          if (res.statusCode == 200) {
            this.siteList = res.data.sites.map((item) => {
              item.is_checked = false
              return item
            })
            this.phone = res.data.user.phone
          }
          uni.hideLoading()
        }, () => {
          uni.hideLoading()
        })

    },
    toBinding (site) {
      if (site.is_open == 0) {
        uni.showToast({
          title: "当前站点没开通,请选择其他站点",
          icon: 'none'
        })
        return
      }
      if (site.is_binding == 0) {
        this.$navigateTo("/house/reg?bind_phone=" + this.phone + "&id=" + site.id + "&protect=" + this.protect + "&house_id=" + this.house_id)
        // this.$navigateTo({
        //   name: 'bind_site',
        //   query: {
        //     id: site.id,
        //   },
        // })
        return
      }
      site.is_checked = !site.is_checked
      // this.$forceUpdate()
    },
  },
}
</script>

<style lang="scss" scoped>
.house_send {
  padding: 24rpx 48rpx 122rpx;
  .tips {
    padding: 24rpx 0;
  }
  .site_list {
    .site_item {
      background: #fff;
      position: relative;
      border: 1rpx solid rgba(221, 225, 233, 1);
      box-shadow: 0 0 8rpx 0 rgba(221, 225, 233, 0.2);
      border-radius: 8rpx;
      margin-bottom: 24rpx;
      padding: 30rpx 24rpx;
      .img {
        width: 320rpx;
        height: 60rpx;
        color: #f0f;
        border-radius: 6rpx;
        overflow: hidden;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .name {
        font-size: 32rpx;
        font-weight: 600;
        margin-bottom: 20rpx;
        color: #2e3c4e;
      }
      .status {
        font-size: 28rpx;
        color: #2d84fb;
        &.status1 {
          color: #8a929f;
        }
      }
      .checked {
        position: absolute;
        right: 0;
        top: 0;
        width: 32rpx;
        height: 24rpx;
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .btns {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20rpx 48rpx;
    background: #fff;
  }
}
</style>
