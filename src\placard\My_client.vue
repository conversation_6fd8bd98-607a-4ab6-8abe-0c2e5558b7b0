<template>
    <view>
        <view class="Search">
            <view class="search_for">
                <image src="./photo/Group 284.png"></image>
                <input type="text" value="" placeholder="搜索">
            </view>
        </view>
        <view class="whole">
            <text class="whole_world">全部(3)</text>
            <view class="connection">
                <view class="head_sculpture">
                    <view class="head_sculpture_left">
                        <view class="sculpture_left"></view>
                        <view class="sculpture_right">
                            <text class="word_top">Ssd.阳光</text>
                            <text class="word_bottom">168****1245</text>
                        </view>
                        <view class="sculpture_center">已拨号</view>
                    </view>
                    <view class="head_sculpture_right">
                        <view class="sculpture_telephone">
                            <image src="./photo/telephone.png"></image>
                        </view>
                    </view>
                </view>
                <view class="foot_sculpture">
                    <view class="moniker">
                        <view>
                            <text class="moniker_connection">客户姓名</text>
                        </view>
                        <view>
                            <text>张三</text>
                        </view>
                    </view>
                    <view class="moniker">
                        <view>
                            <text class="moniker_connection">参与活动</text>
                        </view>
                        <view>
                            <text>绿城618线上章三直播说房（石家庄···</text>
                        </view>
                    </view>
                    <view class="moniker">
                        <view>
                            <text class="moniker_connection">首推归属</text>
                        </view>
                        <view>
                            <text>李四</text>
                        </view>
                    </view>
                    <view class="moniker">
                        <view>
                            <text class="moniker_connection">首推时间</text>
                        </view>
                        <view>
                            <text>2023-02-17 08:32:24</text>
                        </view>
                    </view>
                </view>
            </view>
            <view class="connection">
                <view class="head_sculpture">
                    <view class="head_sculpture_left">
                        <view class="sculpture_left"></view>
                        <view class="sculpture_right">
                            <text class="word_top">Ssd.阳光</text>
                            <text class="word_bottom">168****1245</text>
                        </view>
                        <view class="sculpture_center">已拨号</view>
                    </view>
                    <view class="head_sculpture_right">
                        <view class="sculpture_telephone">
                            <image src="./photo/telephone.png"></image>
                        </view>
                    </view>
                </view>
                <view class="foot_sculpture">
                    <view class="moniker">
                        <view>
                            <text class="moniker_connection">客户姓名</text>
                        </view>
                        <view>
                            <text>张三</text>
                        </view>
                    </view>
                    <view class="moniker">
                        <view>
                            <text class="moniker_connection">参与活动</text>
                        </view>
                        <view>
                            <text>绿城618线上章三直播说房（石家庄···</text>
                        </view>
                    </view>
                    <view class="moniker">
                        <view>
                            <text class="moniker_connection">首推归属</text>
                        </view>
                        <view>
                            <text>李四</text>
                        </view>
                    </view>
                    <view class="moniker">
                        <view>
                            <text class="moniker_connection">首推时间</text>
                        </view>
                        <view>
                            <text>2023-02-17 08:32:24</text>
                        </view>
                    </view>
                </view>
            </view>
            <view class="connection">
                <view class="head_sculpture">
                    <view class="head_sculpture_left">
                        <view class="sculpture_left"></view>
                        <view class="sculpture_right">
                            <text class="word_top">Ssd.阳光</text>
                            <text class="word_bottom">168****1245</text>
                        </view>
                        <view class="sculpture_center">已拨号</view>
                    </view>
                    <view class="head_sculpture_right">
                        <view class="sculpture_telephone">
                            <image src="./photo/telephone.png"></image>
                        </view>
                    </view>
                </view>
                <view class="foot_sculpture">
                    <view class="moniker">
                        <view>
                            <text class="moniker_connection">客户姓名</text>
                        </view>
                        <view>
                            <text>张三</text>
                        </view>
                    </view>
                    <view class="moniker">
                        <view>
                            <text class="moniker_connection">参与活动</text>
                        </view>
                        <view>
                            <text>绿城618线上章三直播说房（石家庄···</text>
                        </view>
                    </view>
                    <view class="moniker">
                        <view>
                            <text class="moniker_connection">首推归属</text>
                        </view>
                        <view>
                            <text>李四</text>
                        </view>
                    </view>
                    <view class="moniker">
                        <view>
                            <text class="moniker_connection">首推时间</text>
                        </view>
                        <view>
                            <text>2023-02-17 08:32:24</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

    </view>
</template>
<script>
export default {
    data() {
        return {

        }
    },
}
</script>
<style scoped lang="scss">
.Search {
    width: 100%;
    height: 150rpx;

    // background-color: #1c49a4;
    .search_for {
        width: 90%;
        height: 70rpx;
        background-color: #F5F6F8;
        color: #D1D1D1;
        margin: 0 auto;
        border-radius: 50px;
        text-align: center;
        margin-top: 30rpx;
        display: flex;
        flex-direction: row;
        align-content: center;
        justify-content: center;
        flex-wrap: wrap;
        image {
            width: 50rpx;
            height: 50rpx;
            margin-left: 150rpx;
        }
        
    }
    
}
::v-deep.uni-input-wrapper {
            width: 45%;
        }
.whole {
    width: 100%;
    height: 1390rpx;
    overflow: hidden;
    background-color: #F5F6F8;

    .whole_world {
        color: #000000;
        font-size: 14px;
        font-weight: 600;
        margin-left: 40rpx;
        margin-top: 30rpx;
    }

    .connection {
        width: 90%;
        height: 390rpx;
        background-color: #fff;
        margin: 0 auto;
        margin-top: 50rpx;
        border-radius: 11px;

        .head_sculpture {
            width: 90%;
            height: 120rpx;
            margin: 0 auto;
            border-bottom: 1px solid #DBDBDB;
            // background-color: chocolate;
            display: flex;
            flex-wrap: wrap;

            .head_sculpture_left {
                width: 60%;
                height: 120rpx;
                // background-color: rgb(146, 255, 127);
                display: flex;
                flex-direction: row;

                .sculpture_left {
                    width: 80rpx;
                    height: 80rpx;
                    background-color: palevioletred;
                    border-radius: 50%;
                    margin-top: 20rpx;
                }

                .sculpture_right {
                    .word_top {
                        font-size: 15px;
                        font-weight: 550;
                        margin-top: 30rpx;
                        margin-left: 20rpx;
                    }

                    .word_bottom {
                        font-size: 13px;
                        margin-top: 10rpx;
                        margin-left: 20rpx;
                    }
                }

                .sculpture_center {
                    width: 90rpx;
                    height: 30rpx;
                    border: 1px solid #2D84FB;
                    color: #2D84FB;
                    text-align: center;
                    border-radius: 6px;
                    margin-top: 30rpx;
                    font-size: 15rpx;
                    margin-left: -20rpx;
                }
            }

            .head_sculpture_right {
                width: 40%;
                height: 120rpx;
                // background-color: rgb(54, 134, 23);
                display: flex;
                flex-direction: row-reverse;

                .sculpture_telephone {
                    width: 50rpx;
                    height: 50rpx;
                    // background-color: #2D84FB;
                    margin-top: 28rpx;
                    margin-right: 30rpx;

                    image {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }

        .foot_sculpture {
            width: 92%;
            height: 250rpx;
            // background-color: peru;
            margin: 10rpx auto;

            .moniker {
                width: 100%;
                height: 50rpx;
                // background-color: aquamarine;
                margin-top: 10rpx;
                display: flex;
                flex-wrap: wrap;
                align-content: space-between;
                .moniker_connection{
                    color: #8A929F;
                    font-size: 14px;
                }

            }
        }

    }
}
</style>