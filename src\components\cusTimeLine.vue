<template>
  <view class="time_line">
    <view
      class="item"
      :class="{ current: item.is_finish !== 0 }"
      v-for="(item, index) in lineData"
      :key="index"
    >
      <template v-if="custom">
        <slot :slotItem="item" :slotIndex="index"></slot>
      </template>
      <template v-else>
        <view class="title">{{ item.created_at }}</view>
        <view class="info row">
          <image
            src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png"
            mode="aspectFill"
          />

          <text class="row" style="flex:1"
            ><text v-if="item.user_name">{{ item.user_name }}</text
            >{{ item.content }}
            <text v-if="item.type_title" class="row"
              >【{{ item.type_title.title }}】</text
            ></text
          >
        </view>
      </template>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    lineData: Array,
    custom: {
      type: [<PERSON><PERSON><PERSON>],
      default: false,
    },
  },
  data() {
    return {};
  },
};
</script>

<style lang="scss" scope>
.time_line {
  padding: 10px 15px;
  color: #8a929f;
  .item {
    position: relative;
    padding: 0 10px 18px 16px;
    border-left: 2px solid rgba(241, 244, 250, 1);
    .info {
      align-items: center;
      margin-top: 10px;
      font-size: 16px;
      text {
        margin: 0 20px 0 12px;
      }
      image {
        width: 22px;
        height: 22px;
        border-radius: 50%;
      }
    }
    .title {
      font-size: 11px;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
    }
    .time {
      font-size: 12px;
      font-weight: bold;
      color: #999;
    }
  }
  .item::after {
    content: "";
    height: 14px;
    width: 14px;
    box-sizing: border-box;
    border-radius: 50%;
    position: absolute;
    border: 2px solid #3399ff;
    background-color: #fff;
    left: -8px;
    top: 0;
  }
}
</style>
