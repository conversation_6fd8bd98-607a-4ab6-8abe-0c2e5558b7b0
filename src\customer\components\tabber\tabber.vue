<template>
  <view class="footer" v-show="!hideTabBar">
    <view class="border-row border-top flex-row" :style="{ 'padding-bottom': paddingBottomHeight + 'rpx' }">
      <block v-for="(item, index) in tabListHandler" :key="index">
        <view class="navigator" :class="currentTabIndex == index ? 'on' : ''" @tap="switchTab(index, item)">
          <view class="icon">
            <image :src="currentTabIndex == index ? item.active_icon : item.icon" :class="!item.text ? 'publish' : ''">
            </image>
            <text v-if="item.badge" class="uni_badge">{{ item.badge }}</text>
            <text v-if="item.badgeDot" class="uni_badge uni_badge_dot"></text>
          </view>
          <view class="text" :style="[
            currentTabIndex == index ? { color: tintColor } : { color: color },
          ]">{{ item.text }}</view>
        </view>
      </block>
    </view>
  </view>
</template>
  
<script>
export default {
  data() {
    return {
      paddingBottomHeight: 0, //苹果X以上手机底部适配高度
      currentTabIndex: this.current,
      website_id: '',
      tabListHandler: [],
      hideTabBar: false
    };
  },
  computed: {
    isReportAuditor(){
      return this.$store.state.isReportAuditor;
    }
  },
  watch: {
    isReportAuditor: {
      handler(val){
        val !== null && (this.hideTabBar = val);
      },
      immediate: true
    }
  },
  created() {
    console.log(uni.getSystemInfoSync());
    this.website_id = uni.getStorageSync("website_id");
    console.log(this.website_id, '0293877777');
    this.tabListHandler = [
      {
        icon: "../../../static/icon/index/Frame (2).png",
        active_icon: "../../../static/icon/index/Frame (1).png",
        text: "工作台",
        path: `/customer/index?website_id=${this.website_id}`,
      },
      {
        icon: "../../../static/icon/index/ic_xiaoxi.png",
        active_icon: "../../../static/icon/index/ic_xiaoxi (1).png",
        text: "消息",
        path: `/customer/messages?website_id=${this.website_id}`,
      },
      {
        icon: "../../../static/icon/index/ic_wode.png",
        active_icon: "../../../static/icon/index/ic_wode (1).png",
        text: "我的",
        path: `/customer/my?website_id=${this.website_id}`,
      },

    ]
  },
  props: {
    current: { type: [Number, String], default: 0 },
    backgroundColor: { type: String, default: "#FFFFFF" },
    color: { type: String, default: "#808080" },
    tintColor: { type: String, default: "#3172F6" },
    // tabList: {
    //   type: Array,
    //   default: function() {
    //     const website_id = this.website_id
    //     return 
    //     [
    //     {
    //       icon: "../../../static/icon/index/Frame (2).png",
    //       active_icon: "../../../static/icon/index/Frame (1).png",
    //       text: "工作台",
    //       path: `/customer/index?website_id=${ website_id}`,
    //     },
    //     {
    //       icon: "../../../static/icon/index/ic_xiaoxi.png",
    //       active_icon: "../../../static/icon/index/ic_xiaoxi (1).png",
    //       text: "消息",
    //       path: `/customer/messages?website_id=${website_id}`,
    //     },
    //     {
    //       icon: "../../../static/icon/index/ic_wode.png",
    //       active_icon: "../../../static/icon/index/ic_wode (1).png",
    //       text: "我的",
    //       path: `/customer/my?website_id=${ website_id}`,
    //     },
    //   ]
    //   } ,
    // },
  },
  methods: {
    switchTab(index, item) {
      console.log(index, item, '0192837465');
      // this.currentTabIndex = index
      this.$emit("click", index, item);
    },
  },
};
</script>
  
<style scoped lang="scss">
.footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 99999;
  padding-bottom: calc(constant(safe-area-inset-bottom));
  padding-bottom: calc(env(safe-area-inset-bottom));
  background: #fff;
}

view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.flex-row {
  flex-direction: row;
}

.border-top {
  position: relative;
}

/* 上边框 */
.border-top:before {
  border-top: 1px solid #ccc;
  content: "";
  display: block;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  transform-origin: left top;
  -webkit-transform-origin: left top;
}

@media screen and(-webkit-min-device-pixel-ratio:2) {
  .border-top:before {
    transform: scaleY(0.5);
    -webkit-transform: scaleY(0.5);
  }
}

@media screen and(-webkit-min-device-pixel-ratio:3) {
  .border-top:before {
    transform: scaleY(0.3333);
    -webkit-transform: scaleY(0.3333);
  }
}

.border-row {
  z-index: 9999;
  width: 100%;
  background: #fff;
  height: 110rpx;
  position: sticky;
  padding-bottom: 40rpx;
  bottom: 0;
  justify-content: space-around;

  .navigator {
    align-items: center;
    justify-content: center;

    .icon {
      image {
        width: 48rpx;
        height: 48rpx;
      }

      .publish {
        width: 48px;
        height: 48px;
      }
    }

    .text {
      font-size: 24rpx;
      margin-top: 6rpx;
    }
  }
}
</style>
  