//获取客户状态
import ajax from "@/page_outside/tools/ajax";


let 
_ajax = (type, api, params, errMsg, cb) => {
    return new Promise((resolve, reject) => {
        ajax[type](api, params, res => {
            if (res.statusCode == 200) {
                resolve(res.data);
                cb && cb(res.data)
            }else{
                if(errMsg || res?.data?.message){
                    uni.showToast({
                        title: res?.data?.message || errMsg,
                        icon: 'none'
                    });
                }
            }
            reject();
        }, er => {
            reject();
        })
    })
},
_promise = {
    get(api, params, errMsg = '', cb){
        return _ajax('get', api, params, errMsg, cb)
    },
    post(api, params, errMsg = '', cb){
        return _ajax('post', api, params, errMsg, cb)
    }
};

//检查是否是审核员
export function checkIsReportAuditor(id){
    return _promise.get(`/admin/crm/report/is_auditor`, {}, '检查是否是审核员时失败');
}

//获取报备详情
export function getReportDetail(id){
    return _promise.get(`/admin/crm/report/details/${id}`, {}, '获取报备详情失败');
}