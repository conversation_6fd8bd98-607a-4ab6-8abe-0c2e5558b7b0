<template>
  <scroll-view
    class="scroll"
    :class="{ has_mark: current > -1 }"
    scroll-x
    :scroll-into-view="'_' + (current >= 2 ? current - 2 : current)"
  >
    <view class="list">
      <!-- :class="{ current: current === index }" -->
      <view
        class="item"
        :id="'_' + index"
        
        v-for="(item, index) in list"
        :key="index"
        @click="clickItem(item)"
        :style="{ backgroundImage: `url('${imgUrl}/yidongduan/build/progress/${bg_img(index,2,item)}.png')`}"
      >
        <!-- :style="{ backgroundImage: `url('${imgUrl}')` }" -->
        <view
          v-if="index > 0"
          class="left"
          :style="{
            backgroundImage: `url('${imgUrl}/yidongduan/build/progress/${bg_img(index,1)}.png')`,
          }"
        ></view>
        <view
          v-if="index < list.length - 1"
          class="right"
          :style="{
            backgroundImage: `url('${imgUrl}/yidongduan/build/progress/${bg_img(index,3)}.png')`,
          }"
        ></view>
        <!-- <image
          v-if="current === index"
          class="mark"
          :src="'/yidongduan/build/progress/mark.png' | imageFilter('m_320')"
        /> -->
        <!-- :class="{ status: item.status == 1, level: index == 0 }" -->
        <text class="name" >{{
          item.name
        }}</text>
        <!-- <text class="time">{{ item.ktime }}</text> -->
      </view>
    </view>
  </scroll-view>
</template>

<script>
// import { formatImg } from '@/page_outside/tools/index.js'
import config from '@/page_outside/config/index.js'

export default {
  name: "salesProgress",
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {

    }
  },
  computed: {
    current () {
      let i= 0
      for (let index = 0; index < this.list.length; index++) {
        const element = this.list[index];
        if(element.status==1){
          i = index
        }
        
      }
      return i
      // return this.list.findIndex(item => item.status == 1)
    },
    imgUrl () {
      return config.imgDomain
    }
  },
  methods: {
    clickItem (item) {
      this.$emit("clickItem", item)
    },
    bg_img (index, postion) {
      console.log(index,postion,this.current);
      var num = 1
      if (this.current==-1) {
        return "b"+(postion+"")
      }
      if (index <= this.current) {
        num = "c"+(postion+"")
      }
      // if (index === this.current) {
      //   num = 4
      // }
      if (index > this.current ) {
        num =  "b"+(postion+"")
      }
      return num 
      // return formatImg(`/yidongduan/build/progress/0${num + postion}.png`)
    }
  },
};
</script>

<style scoped lang="scss">
.scroll {
  width: 100%;
  &.has_mark {
    margin-top: -24rpx;
    .list {
      padding-top: 24rpx;
    }
  }
}
.list {
  display: flex;
  height: 80rpx;
  flex-direction: row;
  flex-wrap: nowrap;
  background-size: 100%;
  background-repeat: no-repeat;
  margin-right: 48rpx;
  .item {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    box-sizing: border-box;
    width: 24%;
    margin-right: 32rpx;
    // padding-left: 12rpx;
    justify-content: center;
    text-align: center;
    font-size: 22rpx;
    color: #6f6f6f;
    background-color: #f2f2f2;
    position: relative;
    background-size: 100% 100%;
    &.item:last-child{
      margin: 0;
    }
    .left {
      width: 20rpx;
      height: 100%;
      content: '';
      position: absolute;
      left: -20rpx;
      background-size: 100% 100%;
    }
    .right {
      width: 20rpx;
      height: 100%;
      content: '';
      position: absolute;
      right: -20rpx;
      background-size: 100% 100%;
    }
    &.current {
      color: #fff;
    }
    .mark {
      width: 100rpx;
      height: 48rpx;
      position: absolute;
      top: -24rpx;
      right: -4rpx;
    }

    .name {
      // text-align: center;
      // margin-left: 44rpx;
    
      &.level {
        color: #fff !important;
      }
      &.status {
        color: #2d84fb;
      }
      // margin-top: 12rpx;
   
    }
    &.name:last-child{
        // z-index: 6666;
        margin-left: 30rpx;
      }
    .time {
      font-weight: 22rpx;
    }
  }
}
</style>
