<template>
  <view class="page">
    <textarea
      placeholder="请在这里输入"
      placeholder-style="font-size:28rpx;color:#999"
      maxlength="200"
      v-model="content"
    ></textarea>
    <view class="word_length">{{ content.length }}/200</view>
    <button class="btn" :class="{ disabled: !content }" @click="handleAdd()">
      保存
    </button>
    <view class="ordinary_btn" @click="$navigateBack()">取消</view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      content: "",
      id: "",
    };
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id;
      this.queryContent(this.id);
    }
  },
  methods: {
    // 根据id
    queryContent(id) {
      this.$ajax.get(`/client/im/everyday_language/query/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.content = res.data.content;
        }
      });
    },
    handleAdd() {
      if (!this.content) {
        uni.showToast({
          title: "请输入内容",
          icon: "none",
        });
        return;
      }
      if (this.id) {
        this.$ajax.post(
          "/client/im/everyday_language/update",
          {
            id: this.id,
            content: this.content,
          },
          (res) => {
            if (res.statusCode === 200) {
              uni.showToast({
                title: "添加成功",
              });
              setTimeout(() => {
                this.$navigateBack();
              }, 1200);
            } else {
              uni.showToast({
                title: res.data.message,
                icon: "none",
              });
            }
          }
        );
      } else {
        this.$ajax.post(
          "/client/im/everyday_language/create",
          { content: this.content },
          (res) => {
            if (res.statusCode === 200) {
              uni.showToast({
                title: "添加成功",
              });
              setTimeout(() => {
                this.$navigateBack();
              }, 1200);
            } else {
              uni.showToast({
                title: res.data.message,
                icon: "none",
              });
            }
          }
        );
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page {
  padding: 24rpx 48rpx;
  background-color: #fff;
}
textarea {
  width: 100%;
  height: 200rpx;
  padding: 24rpx;
  box-sizing: border-box;
  border: 1rpx solid #d8d8d8;
}
.word_length {
  padding: 24rpx 0;
  text-align: right;
  color: #666;
}
button {
  width: 100%;
  margin-top: 48rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  background-color: $uni-color-primary;
  color: #fff;
  &.disabled {
    background-color: rgba($color: $uni-color-primary, $alpha: 0.5);
  }
}
.ordinary_btn {
  margin-top: 24rpx;
  padding: 24rpx;
  text-align: center;
  color: #666;
}
</style>
