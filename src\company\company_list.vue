<template>
  <view class="list">
    <view class="list_back">
      <view class="company_card">
        <view class="card_title">本月佣金金额</view>
        <view class="money">{{
          statistics_info.brokerage_amount > 0
            ? statistics_info.brokerage_amount + "元"
            : "当月暂无佣金信息"
        }}</view>
        <view class="top-line row">
          <view class="box row">
            <view class="top">{{ statistics_info.employee_total }}</view>
            <view class="bottom">员工数</view>
          </view>
          <view class="box row">
            <view class="top">{{ statistics_info.customer_total }}</view>
            <view class="bottom">客户数</view>
          </view>
          <view class="box row">
            <view class="top">{{ statistics_info.deal_total }}</view>
            <view class="bottom">成交数</view>
          </view>
          <view class="box row">
            <view class="top">{{ statistics_info.store_total }}</view>
            <view class="bottom">门店数</view>
          </view>
          <view class="box row">
            <view class="top">{{ statistics_info.visit_total }}</view>
            <view class="bottom">到访量</view>
          </view>
        </view>
      </view>
    </view>
    <view class="menu row">
      <view
        v-for="item in click_category"
        :key="item.id"
        class="image-box row"
        @click="onEmployee(company_info.id, item.id)"
        ><image :src="item.icon" mode="aspectFill"></image>
        <view class="menu_list">{{ item.desc }}</view></view
      >
    </view>
    <view class="ctn-box" v-if="statistics_info.manager_level === 10">
      <view class="title">公司信息</view>
      <view
        class="company-list"
        @click="$navigateTo(`/company/management?id=${company_info.id}`)"
      >
        <view class="more row">
          更多
          <myIcon type="you" class="you" size="24rpx"></myIcon>
        </view>
        <view class="ctn row">
          <view class="label">公司名称：</view>
          <view class="value">{{ company_info.name }}</view>
        </view>
        <view class="ctn row">
          <view class="label">公司区域：</view>
          <view class="value row"
            >{{ company_info.region_0_name }} {{ company_info.region_1_name }}
            <text>{{ company_info.employee }}人</text></view
          >
        </view>
      </view>
    </view>

    <view class="ctn-box" v-if="company_list.length > 0">
      <view class="title">门店列表</view>
      <view
        class="company-list"
        @click="$navigateTo(`/company/management?id=${item.id}`)"
        v-for="item in company_list"
        :key="item.id"
      >
        <view class="more row">
          更多
          <myIcon type="you" class="you" size="24rpx"></myIcon>
        </view>
        <view class="ctn row">
          <view class="label">门店名称：</view>
          <view class="value">{{ item.name }}</view>
        </view>
        <view class="ctn row">
          <view class="label">门店区域：</view>
          <view class="value row"
            >{{ item.region_0_name }} {{ item.region_1_name }}
            <text>{{ item.employee }}人</text></view
          >
        </view>
      </view>
    </view>

    <load-more :status="load_status"></load-more>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore";
import myIcon from "../components/my-icon";
export default {
  components: { loadMore, myIcon },
  data() {
    return {
      params: {
        page: 1,
        company_id: "",
      },
      report_params: {
        page: 1,
      },
      company_list: [],
      load_status: "",
      statistics_info: {},
      company_info: {},
      click_category: [
        {
          id: 1,
          desc: "员工管理",
          icon: "../static/company/yuangong.png",
          path: "/company/employee_list",
        },
        {
          id: 2,
          desc: "公司统计",
          icon: "../static/company/gongsi.png",
          path: "/company/company_tables?type=company",
        },
        {
          id: 3,
          desc: "业绩排行",
          icon: "../static/company/yeji.png",
          path: "/company/quarterly_statistics",
        },
        {
          id: 4,
          desc: "客户分析",
          icon: "../static/company/kehu.png",
          path: "/company/company_tables?type=reported",
        },
        {
          id: 5,
          desc: "收入管理",
          icon: "../static/company/shouru.png",
          path: "/company/company_tables?type=reported",
        },
        {
          id: 6,
          desc: "楼盘分析",
          icon: "../static/company/fenxi.png",
          path: "/company/build_data",
        },
        {
          id: 7,
          desc: "到访排行",
          icon: "../static/company/daofang.png",
          path: "/company/visit_rank",
        },
      ],
    };
  },
  onLoad(options) {
    this.params.company_id = options.id;
    this.getDataList();
    this.getAreaStatistics();
    this.getCompanyInfo(options.id);
  },
  methods: {
    // 获取区域列表数据统计
    getAreaStatistics() {
      this.$ajax.get(
        `/client/company/statistics/home`,
        { company_id: this.params.company_id, store_category: 1 },
        (res) => {
          if (res.statusCode === 200) {
            this.statistics_info = res.data;
          } else {
            uni.showToast({
              title: res.data.message || "获取数据失败",
              icon: "none",
            });
          }
        }
      );
    },
    getCompanyInfo(id) {
      this.$ajax.get(`/client/company/query/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.company_info = res.data;
        } else {
          uni.showToast({
            title: res.data.message || "获取公司信息失败",
            icon: "none",
          });
        }
      });
    },
    onEmployee(company_id, id) {
      var actions = {
        1: [`/company/employee_list?company_id=${company_id}&all=1`],
        2: [
          `/company/company_tables?type=company&company_id=${company_id}&all=1`,
        ],
        3: [`/company/quarterly_statistics?company_id=${company_id}&all=1`],
        4: [
          `/company/company_tables?type=reported&company_id=${company_id}&all=1`,
        ],
        5: [`/company/income_management?company_id=${company_id}&all=1`],
        6: [`/company/build_data?company_id=${company_id}&all=1`],
        7: [`/company/visit_rank?company_id=${company_id}`],
      };
      this.checkStatus(id, actions);
    },
    checkStatus(status, actions) {
      let action = actions[status];
      this.$navigateTo(action[0]);
    },
    getDataList() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.company_list = [];
      }
      this.$ajax.get(
        `/client/company/manager/store/search`,
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.company_list = this.company_list.concat(res.data.data);
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
            }
          } else {
            uni.showToast({
              title: res.data.message || "获取失败",
              icon: "none",
            });
          }
        }
      );
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.getDataList();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>
<style scoped></style>
<style scoped lang="scss">
.list {
  .ctn-box {
    padding: 0 48rpx;
    margin-top: 24rpx;
    .title {
      font-size: 32rpx;
      font-weight: bolder;
      margin-left: 10rpx;
    }
  }
  .company-list {
    padding: 48rpx 48rpx 30rpx;
    width: 100%;
    background: #fff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.04);
    margin: 20rpx 0;
    position: relative;
    .ctn {
      line-height: 48rpx;
    }
  }
  .list_back {
    background: #5371f3;
    padding: 24rpx;
    .company_card {
      height: 300rpx;
      background: #fff;
      border-radius: 10rpx;
      padding: 24rpx;
      .money {
        margin: 24rpx 0;
        color: #5371f3;
        font-size: 40rpx;
      }
    }
    .top-line {
      justify-content: space-around;
      padding: 24rpx;
      .box {
        flex-direction: column;
        align-items: center;
        .top {
          font-size: 32rpx;
          font-weight: bold;
        }
        .bottom {
          margin-top: 24rpx;
          font-size: 28rpx;
          color: #999;
        }
      }
    }
  }
}
.more {
  align-items: center;
  font-size: 26rpx;
  color: #999;
  justify-content: flex-end;
  .you {
  }
}
.label {
  width: 200rpx;
}
.value {
  color: #999;
  justify-content: space-between;
  width: 100%;
}

.menu {
  padding: 48rpx;
  justify-content: flex-start;
  flex-wrap: wrap;
  .image-box {
    flex-direction: column;
    align-items: center;
    width: 25%;
    image {
      width: 96rpx;
      height: 96rpx;
    }
    .menu_list {
      font-size: 24rpx;
      height: 45rpx;
      width: 100rpx;
      align-items: center;
      color: #333;
      margin-top: 10rpx;
    }
  }
}
</style>
