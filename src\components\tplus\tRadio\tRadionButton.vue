<template>
    <view class="radio-group radio-button-group">
        <view class="radio-item" :class="{active: item.value === value}" v-for="(item, index) in options" :key="index" @click="onClick(item)">
            {{item.label}}
        </view>
    </view>
</template>

<script>
export default {
    props: {
        options: {type: Array, default: []},
        value: {type: String, default: ''}
    },
    data(){
        return {

        }
    },
    methods: {
        onClick(item){
            if(this.value !== item.value){
                this.$emit('input', item.value);
                this.$emit('change', item.value)
            }
        }
    }

}
</script>

<style lang="scss" scoped>
.radio-button-group{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: -24rpx;
    .radio-item{
        display: inline-block;
        color: #86909C;
        font-size: 28rpx;
        text-align: center;
        line-height: 1;
        min-width: 143rpx;
        padding: 16rpx 16rpx;
        border: 1px solid #e6e8e9;
        border-radius: 8rpx;
        margin: 24rpx 32rpx 0 0;
        &:last-child{
            margin-right: 0;
        }
        &.active{
            color: $color-primary;
            border-color: $color-primary;
        }
    }
}
</style>