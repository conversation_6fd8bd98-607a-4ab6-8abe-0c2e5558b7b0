{"name": "tfyfen<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "build:quickapp-webview-huawei": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build", "build:quickapp-webview-union": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "dev:quickapp-webview-huawei": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build --watch", "dev:quickapp-webview-union": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest -i", "test:h5": "cross-env UNI_PLATFORM=h5 jest -i", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest -i", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest -i", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest -i", "deploy": "node ./upload/upload.js"}, "dependencies": {"@dcloudio/uni-app-plus": "^2.0.0-27920200618002", "@dcloudio/uni-h5": "^2.0.0-27920200618002", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-mp-360": "^2.0.0-27920200618002", "@dcloudio/uni-mp-alipay": "^2.0.0-27920200618002", "@dcloudio/uni-mp-baidu": "^2.0.0-27920200618002", "@dcloudio/uni-mp-qq": "^2.0.0-27920200618002", "@dcloudio/uni-mp-toutiao": "^2.0.0-27920200618002", "@dcloudio/uni-mp-weixin": "^2.0.0-27920200618002", "@dcloudio/uni-quickapp-native": "^2.0.0-27920200618002", "@dcloudio/uni-quickapp-webview": "^2.0.0-27920200618002", "@dcloudio/uni-stat": "^2.0.0-27920200618002", "@dcloudio/uni-ui": "^1.5.0", "clipboard": "^2.0.6", "core-js": "^3.6.5", "echarts": "^5.3.3", "flyio": "^0.6.2", "html2canvas": "^1.0.0-rc.5", "js-audio-recorder": "^1.0.7", "moment": "^2.29.4", "regenerator-runtime": "^0.12.1", "vconsole": "^3.15.1", "vue": "^2.6.11", "vue-avatar": "^2.3.3", "vue-jsonp": "^0.1.8", "vue-pickers": "^2.5.2", "vuex": "^3.2.0", "weixin-js-sdk": "^1.6.0"}, "devDependencies": {"@dcloudio/types": "*", "@dcloudio/uni-automator": "^2.0.0-27920200618002", "@dcloudio/uni-cli-i18n": "^2.0.1-34920220630001", "@dcloudio/uni-cli-shared": "^2.0.0-27920200618002", "@dcloudio/uni-i18n": "^2.0.1-34920220630001", "@dcloudio/uni-migration": "^2.0.0-27920200618002", "@dcloudio/uni-template-compiler": "^2.0.0-27920200618002", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.0-27920200618002", "@dcloudio/vue-cli-plugin-uni": "^2.0.0-27920200618002", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.0-27920200618002", "@dcloudio/webpack-uni-mp-loader": "^2.0.0-27920200618002", "@dcloudio/webpack-uni-pages-loader": "^2.0.0-27920200618002", "@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-service": "~4.4.0", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.0", "chalk": "^4.1.0", "compressing": "^1.5.1", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-prettier": "^6.10.1", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-vue": "^6.2.2", "inquirer": "^7.3.3", "jest": "^25.4.0", "mini-types": "*", "miniprogram-api-typings": "^2.8.0-2", "node-sass": "^4.14.0", "node-ssh": "^11.1.1", "ora": "^5.3.0", "postcss-comment": "^2.0.0", "sass-loader": "^8.0.2", "scp2": "^0.5.0", "shelljs": "^0.8.4", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4", "ios >= 8"], "uni-app": {"scripts": {}}}