<template>
  <view class="approval-detail">
    <view class="detail-head">
      <view class="head-item">
        <text>发起人：</text>
        <view style="display: flex;flex-direction: row;" v-if="approveDetail.applicant.user_name">
          <text>{{ approveDetail.applicant.user_name }}</text>
          <text>（{{ approveDetail.applicant.department }}）</text>
        </view>
      </view>
      <view class="head-item">
        <text>ID：</text>
        <text>{{ approveDetail.id }}</text>
      </view>
      <view class="head-item">
        <text>审批类型：</text>
        <text>{{ approveDetail.cat_name.title }}</text>
      </view>
      <view class="head-item">
        <text>审批状态：</text>
        <text :class="['item-state', approveDetail.status == 1 ? 'item-state2' : '']">{{
          approvalStatus(approveDetail.status)
        }}</text>
      </view>
      <view class="head-item">
        <text>申请时间：</text>
        <text>{{ approveDetail.ctime }}</text>
      </view>
      <view class="head-item">
        <text>审批编号：</text>
        <view style="display: flex;flex-direction: row; align-items: center;" @click="copyCode(approveDetail.number)">
          <text style="color: #292C39;">{{ approveDetail.number }}</text>
          <myIcon style=" margin-left: 10rpx;" type="fuzhi" color="#bbb3b3" size="32rpx">
          </myIcon>
        </view>
      </view>
    </view>
    <view class="detail-matter" v-if="approveDetail.item">
      <text class="matter-title">审批事项</text>
      <text class="matter-text">{{ approveDetail.item }}</text>
    </view>
    <view class="detail-body">
      <view class="body-title">
        <text>审批流程</text>
        <text>（已管理员预设不可修改审批人和删除抄送人）</text>
      </view>
      <view class="body-item">
        <view class="item-title">
          <text class="icon"></text>
          <text>发起人</text>
        </view>
        <view class="item-box">
          <view class="item-info"
            style="display: flex;flex-direction: row;align-items: center;justify-content: space-between;">
            <view class="info-l">
              <text>{{ approveDetail.applicant.user_name[0] }}</text>
              <text>{{ approveDetail.applicant.user_name }}</text>
            </view>
            <text class="info-r">{{ approveDetail.ctime }}</text>
          </view>
        </view>

      </view>
      <view class="body-item">
        <view class="item-title">
          <text class="icon"></text>
          <text>审批人</text>
        </view>
        <view class="item-box">
          <view class="item-info">
            <view class="info-l" v-if="isAdmin" v-for="(item, index) in approveDetail.approver_uid" :key="index">
              <text>{{ item.user_name ? item.user_name[0] : '' }}</text>
              <text>{{ item.user_name ? item.user_name : '' }}</text>
            </view>
            <view class="info-l" v-if="!isAdmin">
              <text>{{ approver ? approver[0] : '' }}</text>
              <text>{{ approver ? approver : '' }}</text>
            </view>
            <view class="info-text">
              <textarea v-if="approveDetail.status == 0" :value="param.sys_memo" auto-height
                placeholder="请输入审批意见" @input="updateMemo"></textarea>
              <text v-else style="color: #a1a1a1;">审批意见：{{ approveDetail.sys_memo || '无' }}</text>
            </view>
          </view>
        </view>

      </view>
      <view class="body-item">
        <view class="item-title">
          <text class="icon"></text>
          <text>抄送人</text>
        </view>
        <view class="item-box">
          <view class="item-info">
            <view class="info-l" v-if="param.recipients && !approveDetail.recipients.length">
              <text>{{ adminSelectedLable[0] }}</text>
              <text>{{ adminSelectedLable }}</text>
            </view>
            <view class="info-l" v-if="!param.recipients && approveDetail.recipients.length"
              v-for="(item, index) in approveDetail.recipients" :key="index">
              <text>{{ item.user_name[0] }}</text>
              <text>{{ item.user_name }}</text>
            </view>
            <text v-if="!param.recipients && !approveDetail.recipients.length" class="add-member"
              @click="openMemberPicker">+</text>
          </view>
        </view>

      </view>
    </view>
    <view class="detail-btm" v-if="approveDetail.status == 0">
      <button type="default" @click="setApproval(2)">拒绝</button>
      <button type="primary" @click="setApproval(1)">同意</button>
    </view>
    <view class="detail-btm" style="color: #a1a1a1;" v-else>{{ approvalStatus(approveDetail.status) }}</view>
    <tMemberPicker :visible.sync="dialogs.adminPicker" v-model="admin_id" @confirm="confirmSeledAdmin"></tMemberPicker>
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
import tMemberPicker from '@/components/tplus/tMemberPicker.vue';
export default {
  components: {
    tMemberPicker, myIcon
  },
  data() {
    return {
      id: '',
      admin_id: "",
      adminSelectedLable: '',//已选中的成员label
      dialogs: {
        adminPicker: false
      },
      opinion: '',
      isCarbonCopyRecipients: false,
      approveDetail: {
        applicant: { // 默认初始化 applicant
          user_name: '',
          department: ''
        },
        status: "",
        ctime: '',
        number: '',
        cat_name: { title: '' },
        recipients: [],
        approver_uid: []
      },
      param: { sys_memo: '' }
    }
  },
  computed: {
    userInfo() {
      return JSON.parse(uni.getStorageSync('userInfo'))
    },
    isAdmin() {
      let userInfo = JSON.parse(uni.getStorageSync('userInfo'))
      return userInfo.roles.some(item => item.name == '站长')
    },
    approver() {
      const item = this.approveDetail.approver_uid.find((item) => item.id == this.userInfo.id)
      let approver = ''
      if (item) {
        approver = item.user_name
      } else {
        approver = ''
      }
      return approver || ''
    }
  },
  onLoad(opinion) {
    this.id = opinion.id
    this.getApproveDetail(this.id)
  },
  methods: {
    getApproveDetail(id) {
      this.$ajax.get(`/admin/house/approveDetail/${id}`, {}, (res) => {
        if (res.statusCode == 200) {
          this.approveDetail = res.data
        }
      })
    },
    copyCode(code) {
      uni.setClipboardData({
        data: code,
      });
    },
    openMemberPicker() {
      this.dialogs.adminPicker = true
    },
    confirmSeledAdmin(data) {
      this.dialogs.adminPicker = false
      this.adminSelectedLable = data.label[0]
      this.param.recipients = data.value[0]
    },
    //赋值审批意见
    updateMemo(event) {
      this.param.sys_memo = event.target.value;
    },
    setApproval(type) {
      this.param.status = type
      this.param.id = this.id
      this.$ajax.post('/admin/house/dealApprove', this.param, (res) => {
        if (res.statusCode == 200) {
          uni.$emit('refreshData');
          uni.navigateBack(1)
        }
      })
    },
    approvalStatus(status) {
      switch (status) {
        case 0:
          status = '待审批'
          break;
        case 1:
          status = '已通过'
          break;
        case 2:
          status = '已驳回'
          break;
        case 3:
          status = '已撤销'
          break;
        default:
          status = '未知'
          break;
      }
      return status
    },
  },
}
</script>
<style lang="scss" scoped>
page {
  background-color: #f6f6f6;
}


.approval-detail {
  width: 100%;

  .detail-head {
    padding: 32rpx;
    background-color: #fff;

    .head-item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      font-size: 32rpx;
      height: 44rpx;
      line-height: 44rpx;
      margin: 13rpx 0;

      &>text:nth-child(1) {
        color: #292C39B2;
      }

      &>text:nth-child(2) {
        color: #292C39;
      }

      .item-state {
        font-size: 24rpx;
        padding: 0 20rpx;
        border-radius: 4px;
        color: #488AF6 !important;
        background: #488AF633;
      }

      .item-state2 {
        font-size: 24rpx;
        padding: 0 20rpx;
        border-radius: 4px;
        color: #13A834 !important;
        background: #13A83433;
      }
    }
  }

  .detail-matter {
    display: flex;
    flex-direction: column;
    margin-top: 30rpx;
    padding: 30rpx 32rpx;
    background: #fff;

    .matter-title {
      font-size: 36rpx;
      color: #292C39;
      margin-bottom: 30rpx;
    }

    .matter-text {
      line-height: 1.3;
      font-size: 32rpx;
      color: #292C39B2;
    }
  }

  .detail-body {
    margin-top: 30rpx;
    padding: 32rpx;
    background-color: #fff;

    .body-title {
      display: flex;
      flex-direction: row;
      align-items: center;

      &>text:nth-child(1) {
        font-size: 32rpx;
        color: #292C39;
      }

      &>text:nth-child(2) {
        font-size: 24rpx;
        color: #292C3966;
      }
    }

    &>view:nth-child(2) {
      margin-top: 40rpx;
    }

    .body-item {
      .item-title {
        display: flex;
        flex-direction: row;
        align-items: center;

        text {
          font-size: 32rpx;
          font-weight: 500;
          color: #292C39;
        }

        .icon {
          width: 38rpx;
          height: 38rpx;
          border-radius: 50%;
          border: 2px solid #488AF6;
          margin-right: 20rpx;
        }
      }

      .item-box {
        border-left: 1px solid #a5a5a5;
        margin-left: 18rpx;
        padding: 0 0 40rpx 40rpx;

        .item-info {

          .info-l {
            display: flex;
            flex-direction: row;
            align-items: center;
            margin-top: 30rpx;

            text {
              color: #292C39B2;
              font-size: 28rpx;
            }


            &>text:nth-child(1) {
              width: 40rpx;
              height: 40rpx;
              background-color: #488AF6;
              margin-right: 15rpx;
              font-size: 24rpx;
              text-align: center;
              line-height: 40rpx;
              border-radius: 50%;
              color: #fff;
            }
          }

          .info-r {
            margin-top: 30rpx;
            color: #292C3966;
            font-size: 28rpx;
          }

          .info-text {
            margin-top: 30rpx;
            padding: 20rpx;
            width: 100%;
            min-height: 160rpx;
            border-radius: 8px;
            background-color: #f0f1f5;
          }

          .add-member {
            width: 64rpx;
            height: 64rpx;
            margin-top: 30rpx;
            border-radius: 50%;
            font-size: 40rpx;
            line-height: 64rpx;
            text-align: center;
            color: #488AF6;
            background: #488AF633;
          }
        }
      }
    }
  }

  .detail-btm {
    position: fixed;
    bottom: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
    padding: 20rpx 32rpx 40rpx;
    border-top: 1px solid #f6f6f6;
    background-color: #fff;

    button {
      width: 50%;
    }

    &>button:nth-child(1) {
      margin-right: 30rpx;
    }

    &>button:nth-child(2) {
      background-color: #488AF6;
    }
  }
}
</style>