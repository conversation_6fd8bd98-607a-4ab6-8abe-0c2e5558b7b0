<template>
  <view class="list bottom-line">
    <view class="msg_content">
      <view class="msg_box row">
        <view class="admin-img">
          <image
            mode="aspectFill"
            :src="
              headImg
                ? headImg
                : 'https://dss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=2561659095,299912888&fm=26&gp=0.jpg '
                  | imageFilter('w_80')
            "
          ></image>
          <text class="labels">项目助理</text>
        </view>
        <view class="msg-content">
          <view class="msg-content-top " @click="clickCard">
            <view class="top row">
              <view class="top-l">{{ name }}</view>
              <!-- <view class="top-r"> 满意度{{ grade }}分 </view> -->
            </view>
            <view class="bottom">
              <text v-if="number">{{ number }}人咨询过他</text>
              <view style="font-size:28rpx">{{
                phone ? phone : "暂未绑定联系方式"
              }}</view>
            </view>
          </view>
          <view class="contact_method row">
            <image
              v-if="phone"
              @click="clickTel"
              src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/dianhua.png"
            ></image>
            <image
              @click="$emit('clickMsg')"
              src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/zixun.png"
            ></image>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    headImg: String,
    name: String,
    grade: String,
    number: String,
    phone: String,
  },
  methods: {
    clickCard() {
      this.$emit("clickCard");
    },
    clickTel() {
      this.$emit("clickTel");
    },
  },
};
</script>

<style scoped lang="scss">
.list {
  position: relative;
  margin: 36rpx 0 0;
  padding-bottom: 36rpx;
  .msg_box {
    align-items: center;
    justify-content: space-between;
    .admin-img {
      position: relative;
      image {
        width: 88rpx;
        height: 88rpx;
        border-radius: 50%;
      }
      .labels {
        position: absolute;
        border-radius: 8px;
        background: #3172f6;
        font-size: 20rpx;
        color: #ffff;
        padding: 4rpx;
        bottom: -6rpx;
      }
    }
    .msg-content {
      width: 100%;
      margin-left: 24rpx;
      justify-content: space-between;
      .msg-content-top {
        justify-content: space-between;
        width: 300rpx;
      }
      .top {
        align-items: center;
        margin-bottom: 20rpx;
        .top-l {
          font-size: 32rpx;
          font-weight: 400;
        }
        .top-r {
          align-items: center;
          line-height: 32rpx;
          width: 134rpx;
          height: 32rpx;
          font-size: 22rpx;
          color: #fff;
          background-image: linear-gradient(-45deg, #f7918f 0%, #fb656a 100%);
          border-radius: 8px;
          border-radius: 8px;
        }
      }
      .bottom {
        width: 265px;
        font-size: 22rpx;
        color: #666;
        text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .contact_method {
        position: absolute;
        right: 0;
        top: 10%;
        image {
          margin: 0 40rpx;
          width: 64rpx;
          height: 64rpx;
          &:last-child {
            margin: 0;
          }
        }
      }
    }
  }
}
</style>
