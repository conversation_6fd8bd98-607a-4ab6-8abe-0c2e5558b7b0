<template>
	<view class="container">
		<button class="jump-btn" @click="navigateToThirdMiniProgram(appid, path)">打开小程序</button>
	</view>
</template>
<script>

    export default {
        data() {
            return {
                appid: '',
                path: '',
                package_id: 0,
                share_id: 0,
                website_id: 0,
                type: '',
                plugin_id: 0,
                expire_code : 0,
                extraData: {}
            }
        },
        onLoad(options){
            //let params = "type=index&appid="+config.appid_tplus_mini+"&package_id=" + item.id+"&share_id="+(this.user.front_user_id || 0)+"&website_id="+this.website_id;
            this.appid = options.appid;
            console.log(options)
            if(options.type){
                if(options.type=='index'){
                    this.path = 'index/index';
                }else if(options.type=='detail'){
                    this.path = 'index/detail';
                }
                this.path = encodeURIComponent(this.path);
                this.package_id = options.package_id || 0;
                this.share_id = options.share_id || 0;
                this.website_id = options.website_id || 0;
                this.type = options.type || 0;
                this.plugin_id = options.plugin_id || 0;
                this.expire_code = options.expire_code || 0;
                this.extraData = {
                        'package_id': this.package_id,
                        'share_id': this.share_id,
                        'website_id': this.website_id,
                        'plugin_id': this.plugin_id,
                        'expire_code': this.expire_code,
                        'type': this.type
                    }
                console.log(this.extraData)         
            }else{
                this.path = options.path;
            }
        },
        methods: {
            navigateToThirdMiniProgram(appid, path){
                if(!appid || appid == 'undefined') return;
                uni.navigateToMiniProgram({
                    appId: appid,
                    path: path && path != 'undefined' ? (function(p) {
                        try {
                            const decoded = decodeURIComponent(p);
                            return decoded !== p ? decoded : p;
                        } catch(e) {
                            return p;
                        }
                    })(path) : '',
                    extraData: {
                        'package_id': this.package_id,
                        'share_id': this.share_id,
                        'website_id': this.website_id,
                        'plugin_id': this.plugin_id,
                        'expire_code': this.expire_code,
                        'type': this.type
                    },
                    envVersion: 'develop',
                    success(res) {
                        console.log("返回上一个页面")
                        try{
                            uni.navigateBack();
                        }catch(e){
                            console.log(e)
                        }
                    },
                    fail(res) {
                    }
                })
            }
        }
    }
</script>

<style>
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f8f8f8;
}

.jump-btn {
    width: 80%;
    height: 100rpx;
    line-height: 100rpx;
    font-size: 36rpx;
    color: #ffffff;
    background: linear-gradient(to right, #4bb0ff, #6149f6);
    border-radius: 50rpx;
    border: none;
    box-shadow: 0 10rpx 20rpx rgba(97, 73, 246, 0.2);
    transition: all 0.3s ease;
}

.jump-btn:active {
    transform: scale(0.98);
    box-shadow: 0 5rpx 10rpx rgba(97, 73, 246, 0.2);
}
</style>