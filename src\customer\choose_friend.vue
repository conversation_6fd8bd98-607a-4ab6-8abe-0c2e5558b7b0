<template>
  <view class="list">
    <view class="search row">
      <view class="right flex-1">
        <myIcon class="icon" type="ic_sousuo3x1" color="#D1D1D1" size="36rpx"></myIcon>
        <input
          class="uni-input"
          type="search"
          placeholder="请输入"
          v-model="params.name"
          placeholder-style="font-size:26rpx"
          @confirm="search"
        />
      </view>
    </view>
    <view class="uni-list">
      <!-- <radio-group @change="checkboxChange"> -->
      <!-- <checkbox-group @change="checkboxChange"> -->
      <view v-for="(i, index) in member" :key="i.id">
        <view class="flex-row items-center deaprt" @click="changeOpen(i, index)">
          <view class="deaprt_name flex-1">
            {{ i.name }}
          </view>
          <view class="icon">
            <myIcon v-if="i.open" class="icon" type="shangla" color="#D1D1D1" size="36rpx"></myIcon>
            <myIcon v-else class="icon" type="xiala" color="#D1D1D1" size="36rpx"></myIcon>
          </view>
        </view>
        <view
          class="flex-row items-center deaprt"
          @click.prevent.stop="changeRadio(i)"
          v-if="i.children && i.children.length"
        >
          <view class="check_box">
            <myIcon type="ic_queren3x" color="#2d84fb" v-if="i.checked"></myIcon>
          </view>
          <view class="prelogo">
            <view class="name_first">
              {{ i.name && i.name[0] }}
            </view>
          </view>
          <view class="cname flex-1">
            <view class="cname_top"> {{ i.name }} </view>
          </view>
        </view>
        <view v-if="i.subs && i.subs.length && i.open && openChange">
          <view
            class="flex-row items-center deaprt bottom-line"
            v-for="(item, idx) in i.subs"
            :key="item.id"
          >
            <template v-if="item.subs && item.subs.length && openChange">
              <memberList
                :memberList="item"
                @changeOpen="changeOpen($event, idx, index)"
                @changeRadio="changeRadio"
              ></memberList>
            </template>
            <template v-else>
              <template v-if="openChange">
                <view class="flex-row items-center deaprt" @click.prevent.stop="changeOpen(item)">
                  <view class="deaprt_name flex-1">
                    {{ item.name }}
                  </view>
                  <view class="icon">
                    <myIcon
                      v-if="item.open"
                      class="icon"
                      type="shangla"
                      color="#D1D1D1"
                      size="36rpx"
                    ></myIcon>
                    <myIcon v-else class="icon" type="xiala" color="#D1D1D1" size="36rpx"></myIcon>
                  </view>
                </view>
                <view
                  class="flex-row items-center deaprt"
                  @click.prevent.stop="changeRadio(item)"
                  v-if="item.children && item.children.length"
                >
                  <view class="check_box">
                    <myIcon type="ic_queren3x" color="#2d84fb" v-if="item.checked"></myIcon>
                  </view>
                  <view class="prelogo">
                    <view class="name_first">
                      {{ item.name && item.name[0] }}
                    </view>
                  </view>
                  <view class="cname flex-1">
                    <view class="cname_top"> {{ item.name }} </view>
                  </view>
                </view>
              </template>
            </template>
          </view>
        </view>
      </view>
      <!-- </radio-group> -->
      <load-more :status="load_status"></load-more>
      <view class="btn-bottom">
        <view class="btn-box row">
          <view class="btn c2" @click="$navigateBack()">取消</view>
          <view class="btn" @click="onCreateData">提交</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore.vue";
import myIcon from "@/components/my-icon";
import memberList from "./components/memberList.vue"
export default {
  components: {
    loadMore,
    myIcon,
    memberList
  },
  data () {
    return {
      // memberList: [
      //   {
      //     id: 1,
      //     name: '科技部',
      //     pid: 0,
      //     children: [
      //       {
      //         name: "qwe",
      //         id: 2,
      //         pid: 1,
      //         children: [
      //           {
      //             name: "宗",
      //             pid: 2,
      //             id: 9,
      //           },
      //           {
      //             name: "sddfsd",
      //             pid: 2,
      //             id: 10,
      //           }
      //         ]
      //       },
      //       {
      //         name: "df  ",
      //         id: 13,
      //         pid: 1,
      //         children: [
      //           {
      //             name: "宗",
      //             pid: 13,
      //             id: 14,
      //           },
      //           {
      //             name: "sddfsd",
      //             pid: 13,
      //             id: 15,
      //           }
      //         ]
      //       },
      //       {
      //         name: "宗",
      //         id: 12,
      //         pid: 2
      //       },
      //       {
      //         name: "宗",
      //         id: 11,
      //         pid: 2
      //       }
      //     ]
      //   },
      //   {
      //     id: 5,
      //     name: '科技部',
      //     pid: 0,
      //     children: [
      //       {
      //         name: "宗",
      //         id: 6,
      //         pid: 5
      //       },
      //       {
      //         name: "宗",
      //         id: 7,
      //         pid: 5
      //       },
      //       {
      //         name: "宗",
      //         id: 8,
      //         pid: 5
      //       }
      //     ]
      //   }
      // ],
      id: '',
      selected: '',
      load_status: "",
      params: {
        page: 1,
        per_page: 10,
        type: 2,
      },
      change: true,
      openChange: true,
      departmentList: [],
      member: []
    }
  },
  onLoad (options) {
    if (options.id) {
      this.id = options.id
    }
    // 获取部门列表
    this.getDepartment()
    // this.departmentList = this.setMember(this.departmentList)
    // console.log(this.departmentList);
    // this.getdepartmentList()
  },
  watch: {
    departmentList: {
      handler (val) {
        this.member = JSON.parse(JSON.stringify(val))
        console.log(this.member);
      },
    },

  },
  methods: {
    checkboxChange (e) {
      this.selected = e.detail.value
    },
    getDepartment () {
      this.$ajax.get("/qywx/common/departments/search", {}, (res) => {
        if (res.statusCode === 200) {
          this.departmentList = this.setMember(res.data);
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    setMember (member, id) {
      member.map(item => {
        item.open = false

        if (item.subs && item.subs.length) {
          item.subs.map(child => {
            child.open = false
            if (child.subs && child.subs.length) {
              this.setMember(item.subs)
            }
            return child
          })

        }
        return item
      })
      return member
    },
    setChecked (id, member, type) {


      for (let index = 0; index < member.length; index++) {
        let ele = member[index]
        if (ele.id == id) {
          ele[type] = true

        } else {
          ele[type] = false
          if (ele.subs && ele.subs.length) {
            for (let i = 0; i < ele.subs.length; i++) {
              const element = ele.subs[i];
              if (element.id == id) {
                element[type] = true
                if (type == "open") {
                  if (ele.id == element.pid) {
                    ele[type] = true
                  }

                }
              } else {
                element[type] = false
              }
              if (element.subs && element.subs.length) {
                this.setChecked(id, element.subs, type)
              }

            }
          }
        }

      }

      return member

    },
    async changeOpen (e) {
      this.openChange = false
      this.member = await this.setChecked(e.id, this.departmentList, "open")
      this.openChange = true
      // e.open = true
      this.$forceUpdate()
    },
    async changeRadio (e) {

      this.change = false
      this.departmentList = await this.setChecked(e.id, this.departmentList, "checked")
      this.change = true
      this.selected = e.id
      // this.departmentList = await this.setMember(this.departmentList)
      // console.log(this.departmentList);
      // e.checked = true
      this.$forceUpdate()
    },

    onCreateData () {
      this.form_info.client_id = this.selected.join(',')
      if (!this.form_info.client_id) {
        uni.showToast({
          title: "请选择用户",
          icon: "none",
        });
        return;
      }

      this.$ajax.post("/qywx/client/bind", this.form_info, (res) => {
        if (res.statusCode === 200) {
          this.$navigateTo(
            `/customer/detail?id=${this.form_info.client_id}&form=2`
          );
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    submit () {
      if (this.selected.length == 0) {
        uni.showToast({
          title: '请选择成员',
          icon: 'none',
        })
        return
      }
      // let agent_id = this.selected.join(',')
      // this.$ajax.post('/v1/wapLm/addAgentsByDepartment', { id: this.id, agent_id }).then((res) => {
      //   if (res.data.status == 200) {
      //     uni.showToast({
      //       title: res.data.message,
      //       icon: 'none',
      //     })
      //     setTimeout(() => {
      //       this.$navigateBack()
      //       setTimeout(() => {
      //         uni.$emit('addMemberSuccess')
      //       }, 300)
      //     }, 1000)
      //   }
      // })
    },
  },
}
</script>

<style scoped lang="scss">
page {
  color: #2e3c4e;
  padding-bottom: 80rpx;
}
.list {
  padding: 0 24px;
}
.uni-list {
  padding-top: 60px;
  .deaprt {
    padding: 10px 0;
    width: 100%;
    .deaprt {
      padding-left: 20px;
    }
    .deaprt_name {
      color: #414346;
      font-family: PingFang SC;
      font-weight: medium;
      font-size: 16px;
    }
  }
  checkbox-group {
    // padding-left: 24rpx;
    ::v-deep uni-label {
      width: 100% !important;
    }
    uni-checkbox {
      ::v-deep .uni-checkbox-input {
        margin-right: 0;
        border-radius: 50%;
      }
    }
  }
  radio-group {
    // padding-left: 24rpx;
    ::v-deep uni-label {
      width: 100% !important;
    }
    uni-radio {
      ::v-deep .uni-radio-input {
        margin-right: 0;
        border-radius: 50%;
      }
    }
  }
  .uni-list-cell {
    padding: 10px 0;
    ~ .uni-list-cell {
      padding-left: 20px;
    }
    .check_box {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: 1px solid #d1d1d1;
    }
  }
  .prelogo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin: 0 10px;
    background: #2d84fb;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    .name_first {
      color: #fff;
      font-size: 16px;
    }
    image {
      width: 100%;
      height: 100%;
    }
  }
  .cname {
    font-size: 16px;
    // font-weight: bolder;
    color: 414346;
    .cname_bottom {
      margin-top: 5px;
    }
    .c_label {
      background: #f4f4f4;
      padding: 3px 5px;
      border-radius: 2px;
      color: #959a9d;
      font-size: 10px;
      &:not(last-child) {
        margin-right: 5px;
      }
    }
  }
  .souce {
    font-size: 14px;
    color: #868686;
  }

  .uni-list-cell {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 20rpx 0;
  }
  .footer {
    padding: 20rpx;
    background: #f8f8f8;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    max-width: 1500rpx;
    .confirm {
      font-size: 28rpx;
      padding: 10rpx 20rpx;
      color: #fff;
      background: #2d84fb;
      border-radius: 8rpx;
    }
  }
}

.search {
  padding: 22rpx 48rpx;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  height: 100rpx;
  top: 0;
  left: 0;
  background: #fff;
  width: 100%;
  z-index: 10;
  .right {
    position: relative;
    .icon {
      position: absolute;
      top: 14rpx;
      left: 28rpx;
    }
    input {
      font-size: 28rpx;
      padding-left: 96rpx;
      background: #eee;
      height: 64rpx;
      // width: 530rpx;
      border-radius: 32rpx;
    }
  }
}
.btn-bottom {
  height: 100px;
  .btn-box {
    background: #fff;
    justify-content: space-between;
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 24px;
    .btn {
      width: 48%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      border-radius: 6px;
      border: 1px solid #2d84fb;
      color: #fff;
      background: #2d84fb;
      font-weight: 500;
      &.c2 {
        border: 1px solid #dde1e9;
        background: #fff;
        color: #8a929f;
      }
    }
  }
}
</style>
