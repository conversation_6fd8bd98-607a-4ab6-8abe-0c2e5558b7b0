<template>
  <view
    class="my-btn"
    :class="[
      type,
      loading || disabled ? 'disabled' : '',
      size,
      block ? 'block' : '',
      text ? 'text' : '',
      plain ? 'plain' : '',
      round ? 'round' : '',
    ]"
    @click="onClick"
  >
    <icons
      v-if="icon || loading"
      :type="loading ? 'loading' : icon"
      :color="fontColor"
      :size="28"
      :_style="{ marginRight: '12rpx' }"
    ></icons>
    <text class="text">
      <slot></slot>
    </text>
  </view>
</template>

<script>
import icons from '@/components/my-icon'
export default {
  name: 'myButton',
  components: {
    icons,
  },
  props: {
    size: {
      type: String,
      default: 'base',
    },
    icon: {
      type: String,
      default: '',
    },
    plain: {
      type: Boolean,
      default: false,
    },
    round: {
      type: Boolean,
      default: true,
    },
    type: {
      type: String,
      default: 'primary',
    },
    block: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: <PERSON><PERSON>an,
      default: false,
    },
    text: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {}
  },
  computed: {
    fontColor() {
      if (this.plain) {
        switch (this.type) {
          case 'primary':
            return '#2d84fb'
          case 'warning':
            return '#feb317'
          case 'success':
            return '#30c449'
          case 'info':
            return '#8a929f'
          default:
            return '#fff'
        }
      } else {
        return '#fff'
      }
    },
  },
  methods: {
    onClick() {
      if (this.loading || this.disabled) {
        return
      }
      this.$emit('click')
    },
  },
}
</script>

<style scoped lang="scss">
// .icon-weituoguanli {
//   animation: rotating 2s linear infinite;
// }
.my-btn {
  flex-direction: row;
  align-items: center;
  text-align: center;
  align-self: flex-start;
  flex-direction: row;
  justify-content: center;
  color: #fff;
  // animation: rotating 2s linear infinite;
  .text {
    line-height: 1;
  }
  &.base {
    height: 64rpx;
    padding: 0 32rpx;
    border-radius: 8rpx;
    font-size: 28rpx;
    &.round {
      border-radius: 32rpx;
    }
  }
  &.big {
    height: 80rpx;
    align-items: center;
    padding: 0 40rpx;
    border-radius: 10rpx;
    font-size: 32rpx;
    &.round {
      border-radius: 40rpx;
    }
  }
  &.sbig {
    height: 88rpx;
    padding: 0 44rpx;
    border-radius: 12rpx;
    font-size: 36rpx;
    &.round {
      border-radius: 44rpx;
    }
  }
  &.small {
    height: 48rpx;
    padding: 0 24rpx;
    border-radius: 6rpx;
    font-size: 22rpx;
    &.round {
      border-radius: 24rpx;
    }
  }
  &.block {
    align-self: stretch;
  }
  &.primary {
    background-color: #2d84fb;
    background-image: linear-gradient(
      135deg,
      mix(#2d84fb, #fff, 65%) 0%,
      #2d84fb 100%
    );
    box-shadow: 0 6rpx 12rpx -6rpx rgba(#2d84fb, 0.4);
    &.plain {
      border: 1rpx solid #2d84fb;
      color:#2d84fb;
      background: none;
      box-shadow: none;
      ::v-deep .iconfont {
        color: #2d84fb;
      }
    }
    &.text {
      color: #2d84fb;
      background: none;
      box-shadow: none;
    }
  }
  &.warning {
    background-color: #fe6c17;
    background-image: linear-gradient(
      135deg,
      mix(#fe6c17, #fff, 65%) 0%,
     #fe6c17 100%
    );
    box-shadow: 0 6rpx 12rpx -6rpx rgba(#fe6c17, 0.4);
    &.plain {
      border: 1rpx solid #fe6c17;
      color: #fe6c17;
      background: none;
      box-shadow: none;
      ::v-deep .iconfont {
        color: #fe6c17;
      }
    }
    &.text {
      color: #fe6c17;
      background: none;
      box-shadow: none;
    }
  }
  &.success {
    background-color: #30c449;
    background-image: linear-gradient(
      135deg,
      mix(#30c449, #fff, 65%) 0%,
      #30c449 100%
    );
    box-shadow: 0 6rpx 12rpx -6rpx rgba(#30c449, 0.4);
    &.plain {
      border: 1rpx solid #30c449;
      color: #30c449;
      background: none;
      box-shadow: none;
      ::v-deep .iconfont {
        color: #30c449;
      }
    }
    &.text {
      color: #30c449;
      background: none;
      box-shadow: none;
    }
  }
  &.info {
    background-color: #8a929f;
    background-image: linear-gradient(
      135deg,
      mix(#8a929f, #fff, 65%) 0%,
      #8a929f 100%
    );
    box-shadow: 0 6rpx 12rpx -6rpx rgba(#8a929f, 0.4);
    &.plain {
      border: 1rpx solid #dde1e9;
      color: #8a929f;
      background: none;
      box-shadow: none;
      ::v-deep .iconfont {
        color: #8a929f;
      }
    }
    &.text {
      color: #8a929f;
      background: none;
      box-shadow: none;
    }
  }
  &.disabled {
    opacity: 0.6;
    // color: #dedede !important;
    // ::v-deep .iconfont {
    //   color: #dedede;
    // }
  }
  ::v-deep .iconfont {
    color: #fff;
  }
}
</style>
