import config from "../page_outside/config/index";

var chat = {
  methods: {
    // 连接socket
    handleConnectSocket() {
      if (!this.im.imToken) {
        uni.showToast({
          title: "聊天token为空",
          icon: "none",
        });
        return;
      }
      this.im.socketTask = uni.connectSocket({
        url: `${config.imSocket}?imToken=${this.im.imToken}&userToken=${this.im.userToken}`,
        success: (res) => {
          console.log(res);
        },
        fail: (err) => {
          console.log(err);
        },
      });
      // 监听连接成功
      this.im.socketTask.onOpen((res) => {
        this.im.socketOpen = true;
        this.onMessage();
        console.log("webSocket连接已打开！");
        // 发送一条初始化消息
        this.sendMessage(this.initMsg);
        this.socketOnOpen && this.socketOnOpen();
        this.heartbeat();
      });
    },
    // 发送消息
    /***
     * @param {Object} msg 消息内容
     * @param {string} type 消息类型  text:文本；image:图片；map:位置；
     */
    sendMessage(msg, type = "text") {
      // init:好友列表初始化; Heartbeat:心跳维持; directChat:直聊初始化;unReadMessage:请求未读消息;messageLog:请求聊天记录
      let types = [
        "init",
        "Heartbeat",
        "directChat",
        "unReadMessage",
        "messageLog",
      ];
      if (types.includes(msg.flag)) {
        if (this.im.socketOpen && msg) {
          this.im.socketTask.send({
            data: JSON.stringify(msg),
          });
        }
        return;
      }
      if (msg == "") {
        uni.showToast({
          title: "请输入内容",
          icon: "none",
        });
        return;
      }
      let send_msg = {
        flag: "sendMessage",
        from_nickname:
          this.user_info.name ||
          this.user_info.nickname ||
          this.user_info.user_name,
        from_headimage: this.user_info.avatar,
        from_id: this.im.myChatInfo.platform + this.im.myChatInfo.from_id,
        from_user_id: this.im.myChatInfo.from_id,
        to_id: this.im.myChatInfo.platform + this.im.myChatInfo.to_id,
        type: type,
        content: msg,
        dialog_id: this.im.myChatInfo.dialog_id,
      };
      if (!this.im.socketOpen) {
        uni.showToast({
          title: "聊天已断开",
          icon: "none",
        });
        return;
      }
      if (this.im.socketOpen && send_msg) {
        this.im.socketTask.send({
          data: JSON.stringify(send_msg),
          success: () => {
            this.saveMsg(send_msg, 1, this.im.chatIndex);
            this.im.chatIndex = 0;
            this.send_content = "";
          },
          fail: (err) => {
            this.im.socketOpen = false;
            console.log(err);
            uni.showToast({
              title: "发送失败",
              icon: "none",
              mask: true,
            });
          },
        });
      } else {
        this.im.socketOpen = false;
        uni.showToast({
          title: "发送失败，请重试",
          icon: "none",
        });
      }
    },
    /**
     * 监听socket连接成功事件
     */
    onOpen() {
      this.im.socketTask.onSocketOpen((res) => {
        this.im.socketOpen = true;
        console.log("WebSocket连接已打开！");
        this.sendMessage(this.initMsg);
        this.heartbeat();
      });
    },
    /**
     * @param {Object} send_msg 消息内容
     * @param {Number} is_my 是否是自己发出的消息  1自己 0好友
     * @param {Number} friend_index 好友所在好友列表索引
     */
    saveMsg(send_msg, is_my, friend_index) {
      let time = new Date();
      let msg_time =
        (time.getHours() < 10 ? "0" + time.getHours() : time.getHours()) +
        ":" +
        (time.getMinutes() < 10 ? "0" + time.getMinutes() : time.getMinutes());
      let updated_at = this.$getTime("YMDhm");
      if (
        send_msg.from_id !==
        this.im.myChatInfo.platform + this.im.myChatInfo.from_id
      ) {
        let msg = {
          is_my,
          time: msg_time,
          updated_at: updated_at,
          content: JSON.stringify(send_msg.content),
          type: send_msg.type,
          from_user_id: this.im.myChatInfo.to_id,
          to_headimage: send_msg.from_headimage,
        };
        this.im.nowChat.chatList.push(msg);
      } else {
        let msg = {
          is_my,
          time: msg_time,
          updated_at: updated_at,
          content: JSON.stringify(send_msg.content),
          type: send_msg.type,
          from_user_id: send_msg.from_user_id,
          from_headimage: send_msg.from_headimage,
        };
        this.im.nowChat.chatList.push(msg);
      }
      let pages = getCurrentPages();
      let nowPage = pages[pages.length - 1].route;
      if (nowPage === "im_list/msg_detail") {
        this.$nextTick(() => {
          // #ifdef MP-BAIDU
          setTimeout(() => {
            uni.$emit("handleScroll");
          }, 100);
          // #endif
          // #ifndef MP-BAIDU
          uni.$emit("handleScroll");
          // #endif
        });
      }
      if (this.im.friendList.length > friend_index) {
        //   将好友从从列表移动到第一条
        this.im.friendList[friend_index] = {
          u_id: this.im.myChatInfo.to_id,
          u_avatar: send_msg.from_headimage,
          u_name: send_msg.from_nickname,
          im_m_created_at_str: msg_time,
          im_m_content: JSON.stringify(send_msg.content),
          unread_msg_total: ++this.im.friendList[friend_index].unread_msg_total,
        };

        // 直接更新数组某个元素的属性，视图上不会同步更新，要使用这种方式。
        /**
         * @param photoArr 所要更新的数组
         * @param pIndex   所要更新的数组的哪一项
         * @param pItem    更新后的值
         */
        var that = this;
        that.$set(that.im.friendList, 0, this.im.friendList[friend_index]);
        if (friend_index > 0) {
          // 不在第一个就放进第一个
          let newChat = this.im.friendList.splice(friend_index, 1);
          this.im.friendList.unshift(newChat[0]);
        }
      }
    },
    // 已读发送者信息 (全部已读)
    haveReadMsgAll(to_id) {
      this.$ajax.get(`/client/im/msg/read_all/${to_id}`, {}, (res) => {
        if (res.statusCode === 200) {
          console.log("全部已读");
        } else {
          uni.showToast({
            title: res.data.message || "read all error",
            icon: "none",
          });
        }
      });
    },
    //   接收消息
    onMessage() {
      this.im.socketTask.onMessage((res) => {
        let resData = JSON.parse(res.data);
        console.log(res.data);
        switch (resData.flag) {
          // 聊天消息
          case "sendMessage":
            this.checkMsg(resData);
            // 获取未读消息数量
            console.log("接收：", resData.content);
            setTimeout(() => {
              this.$getUnreadMsg();
            }, 1000);
            break;
          case "sendMessageStatus":
            console.log("发送：", resData.content);
            this.pushMyMsg(JSON.stringify(resData.content), resData.is_online);
          default:
            break;
        }
      });
    },
    /**
     *   @param {Object} msg  接收好友消息
     *  */

    checkMsg(msg) {
      let chat_id = msg.content.dialog_id;
      let friend_index;
      // 检测是否在好友列表
      for (let i = 0; i < this.im.friendList.length; i++) {
        let content = JSON.parse(this.im.friendList[i].im_m_content);
        if (content.dialog_id == chat_id) {
          friend_index = i;
          break;
        }
      }
      // 如果不是好友列表发送
      if (friend_index === undefined) {
        //将好友插入列表
        let friend = {
          u_avatar: msg.from_headimage,
          u_name: msg.from_nickname,
          im_m_created_at_str: msg.time,
          im_m_content: JSON.stringify(msg.content),
          unread_msg_total: 0,
        };
        this.im.friendList.unshift(friend);
        friend_index = 0;
      }
      this.saveMsg(msg, 0, friend_index);
    },

    /**
     * 维持连接
     */
    heartbeat() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      console.log("开始监听心跳");
      let time_out = 30000;
      this.timer = setInterval(() => {
        if (!this.im.socketOpen && this.timer) {
          clearInterval(this.timer);
          return;
        }
        this.sendMessage({ flag: "Heartbeat" });
      }, time_out);
      // 60s刷新未读消息
      // let startTime = new Date().getTime();
      // let count = 0;
      // let _this = this;
      // function handle() {
      //   if (!_this.im.socketOpen) {
      //     return;
      //   }
      //   count++;
      //   var offset = new Date().getTime() - (startTime + count * time_out);
      //   var nextTime = time_out - offset;
      //   if (nextTime < 0) nextTime = 0;
      //   setTimeout(handle, nextTime);
      //   _this.sendMessage({ flag: "Heartbeat" });
      // }
      // setTimeout(handle, time_out);
    },
    // 消息推送
    /**
     * @param {String} chat_id 对话窗口id
     * @param {String} content 聊天内容
     * */
    pushMyMsg(content, online_status) {
      this.$ajax.post(
        "/client/im/msg/create",
        {
          im_session_id: this.im.myChatInfo.dialog_id,
          content: content,
          online_status: online_status,
        },
        (res) => {
          if (res.statusCode === 200) {
            // this.haveReadMsg(res.data.to_user_id, res.data.id);
          } else {
            uni.showToast({
              title: res.data.message || "发送失败",
              icon: "none",
            });
          }
        }
      );
    },
    // 发送消息已读
    haveReadMsg(from_id, msg_id) {
      this.$ajax.get(`/client/im/msg/read/${from_id}/${msg_id}`, {}, (res) => {
        if (res.statusCode === 200) {
        } else {
          uni.showToast({
            title: res.data.message || "create have read message error",
            icon: "none",
          });
        }
      });
    },
    //关闭socket
    closeSocket(type) {
      this.closeType = type;
      this.im.socketOpen = false;
      if (this.im.socketTask) {
        this.im.socketTask.close({
          success: () => {
            this.im.socketTask = null;
          },
        });
      }
    },
    /**
     * 监听socket错误
     */
    onSocketError() {
      this.im.socketTask.onError((res) => {
        console.log("socket错误:", res);
        this.im.socketOpen = false;
        uni.showToast({
          title: "连接失败",
          icon: "none",
        });
      });
    },

    /**
     * 监听socket关闭事件
     */
    onClose() {
      this.im.socketOpen = false;
      this.im.socketTask.onClose((res) => {
        if (this.timer) {
          clearInterval(this.timer);
        }
        console.log("WebSocket 已关闭！", res);
      });
    },
  },
};
module.exports = chat;
