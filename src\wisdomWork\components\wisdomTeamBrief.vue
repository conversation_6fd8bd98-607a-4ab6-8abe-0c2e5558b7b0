<template>
    <view>
        <wisdomTitleBar>
            数据简报
            <template #right>
                <wisdomFilters v-model="params.times" :dates.sync="params.dates" @filter="getCrmData"></wisdomFilters>
            </template>
        </wisdomTitleBar>
        <wisdomDataGrids :data="statistics"></wisdomDataGrids>
    </view>
</template>

<script>
import wisdomTitleBar from './wisdomTitleBar';
import wisdomFilters from './wisdomFilters';
import wisdomDataGrids from './wisdomDataGrids';
import { teamCrmData } from '@/common/utils/wisdom-work.js'
export default {
    props: {
        loading: { type: Boolean, default: false }
    },
    components: {
        wisdomTitleBar,
        wisdomFilters,
        wisdomDataGrids
    },
    data() {
        return {
            params: {
                times: 'yestoday',
                dates: []
            },
            statistics: []
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.getCrmData();
        })
    },
    methods: {
        //获取团队 crm 数据统计
        async getCrmData(){
            let [ start_date, end_date ] = this.params.dates;
            const data = await teamCrmData({ start_date, end_date });
            this.$emit('update:loading', false);
            this.statistics = data?.data || [];
        },
    }
}
</script>
<style lang="scss" scoped>

</style>