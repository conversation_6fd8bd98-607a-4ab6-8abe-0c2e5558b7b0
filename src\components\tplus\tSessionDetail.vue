<template>
    <view class="session-detail" :class="{'prevent-scroll': show}" @touchmove.stop.prevent="disabledScroll">
        <view class="popup-box" :class="{ 'show': show }" @touchmove.stop.prevent="disabledScroll">
            <view class="popup-header" @touchmove.stop.prevent="disabledScroll">
                <view class="title-container">
                    <text class="title">{{ title }}</text>
                </view>
                <view class="close-btn" @click="close">
                    <text class="close-icon">×</text>
                </view>
            </view>
            
            <!-- 使用简单的div滚动替代scroll-view，避免H5端的兼容性问题 -->
            <view class="popup-content" ref="scrollContainer" @scroll="handleScroll">
                <!-- 加载更多提示 -->
                <view class="load-more-tip" v-if="!loaded && !loading" @click="loadMoreMessages">
                    <text>点击加载更多历史消息</text>
                </view>
                
                <view class="loading-wrapper" v-if="loading">
                    <view class="loading">加载中...</view>
                </view>
                
                <view class="no-more" v-if="loaded">
                    <text>没有更多了</text>
                </view>
                
                <view class="message-list">
                    <view class="message-list-item" v-for="item in list" :key="item._key" :ref="'msg_' + item._key">
                        <!-- 时间显示 -->
                        <view class="time-container" v-if="item.time">
                            <text class="time">{{ formatTime(item.time) }}</text>
                        </view>
                        <view class="message-row" :class="{ 'right': !item.isUser }">
                            <view class="avatar">
                                <image :src="item.avatar || 'https://img.tfcs.cn/static/img/que.jpg'" mode="aspectFill"></image>
                            </view>
                            <view class="popper">
                                <view class="popper-title" v-if="!item.isUser">{{ item.nickname }}</view>
                                <view class="popper-content" :class="{ 'user-message': item.isUser }">
                                    {{ item.content }}
                                    <view class="popper-arrow"></view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                
                <!-- 底部留白 -->
                <view class="bottom-space"></view>
            </view>
        </view>
        <view
            class="mask"
            :class="{ 'show': show }"
            @click="close"
            @touchmove.stop.prevent="disabledScroll"
        ></view>
    </view>
</template>

<script>
export default {
    components: {},
    props: {
        height: {
            type: String,
            default: '90vh'
        }
    },
    data() {
        return {
            show: false,
            loading: false,
            loaded: false,
            title: '',
            params: {
                per_page: 10,
                last_time: 0,
                ies_uid: '',
                ies_uniq_id: '',
                user_mapping_id: '',
                user_type: 1,
                mobile: '',
                data_type: 0,
                view_from: 'client'
            },
            list: [],
            lastLoadTime: 0, // 防抖时间戳
            isInitialLoad: true, // 是否是初次加载
        }
    },
    computed: {
        contentHeight() {
            return 'calc(' + this.height + ' - 100rpx)';
        }
    },
    methods: {
        disabledScroll() {
            return false;
        },
        
        open(params) {
            this.title = (params.nick_name || params.ies_uniq_id || '用户') + '的会话信息';
            this.params = { ...params };
            this.loaded = false;
            this.list = [];
            this.lastLoadTime = 0;
            this.isInitialLoad = true;
            this.show = true;

            // 禁用页面滚动
            this.preventPageScroll();

            // 加载数据
            this.$nextTick(() => {
                this.getList(false).then(() => {
                    // 滚动到底部显示最新消息
                    this.scrollToBottom();
                }).catch((error) => {
                    console.error('初始加载失败:', error);
                });
            });

            return this;
        },
        
        close() {
            this.show = false;
            this.restorePageScroll();
        },

        preventPageScroll() {
            this.$emit('prevent-scroll', true);
        },

        restorePageScroll() {
            this.$emit('prevent-scroll', false);
        },
        
        // 简化的滚动事件处理
        handleScroll(e) {
            e.stopPropagation();
            // 移除复杂的滚动逻辑，避免递归
        },
        
        // 手动触发加载更多
        loadMoreMessages() {
            if (this.loading || this.loaded) return;
            
            // 防抖处理
            const now = Date.now();
            if (this.lastLoadTime && (now - this.lastLoadTime < 1000)) {
                return;
            }
            this.lastLoadTime = now;
            
            this.getList(true);
        },
        
        async getList(isLoadingMore = false) {
            if (!this.params || (!this.params.user_mapping_id && !this.params.ies_uid && !this.params.ies_uniq_id && !this.params.mobile)) {
                console.warn('缺少必要的参数，无法获取会话数据');
                this.loading = false;
                return Promise.reject('缺少必要的参数');
            }

            this.loading = true;

            // 构建请求参数
            let requestParams = {};
            let requestUrl = '';
            
            if(this.params.data_type == 1){
                // 抖音私信
                requestParams = {
                    ies_uid: this.params.ies_uid || 0,
                    ies_uniq_id: this.params.ies_uniq_id || '',
                    user_mapping_id: this.params.user_mapping_id,
                    user_type: this.params.user_type || 2,
                    view_from: this.params.view_from || 'client'
                };
                requestUrl = '/admin/douyin/private_letter/user/chat';
            } else if(this.params.data_type == 2){
                // 微信视频号私信
                requestParams = {
                    mobile: this.params.mobile,
                    view_from: this.params.view_from || 'client'
                };
                requestUrl = '/admin/weixin/private_letter/user/chat';
            } else if(this.params.data_type == 3){
                // 微信小店私信
                requestParams = {
                    mobile: this.params.mobile,
                    view_from: this.params.view_from || 'client'
                };
                requestUrl = '/admin/weixin/private_letter/user/chat/shop';
            }

            // 如果是加载更多，添加last_time参数
            if (isLoadingMore && this.params.last_time) {
                requestParams.last_time = this.params.last_time;
            }

            console.log('请求参数:', requestParams);

            return new Promise((resolve, reject) => {
                this.$ajax.get(requestUrl, requestParams, (res) => {
                    this.loading = false;

                    if (res.statusCode === 200) {
                        // 保存last_time用于加载更多
                        if (res.data.last_time) {
                            this.params.last_time = res.data.last_time;
                        }

                        // 处理是否有更多数据
                        const hasMoreData = res.data.has_more && res.data.list.length > 0;
                        this.loaded = !hasMoreData;

                        // 转换数据格式
                        const formattedMessages = this.formatMessages(res.data);

                        if (isLoadingMore) {
                            // 加载更多时，将新消息添加到列表前面
                            this.list = [...formattedMessages, ...this.list];
                        } else {
                            // 首次加载时，直接设置列表
                            this.list = formattedMessages;
                        }
                        
                        resolve();
                    } else {
                        uni.showToast({
                            title: res.data?.message || '获取会话数据失败',
                            icon: 'none'
                        });
                        reject(res.data?.message || '获取会话数据失败');
                    }
                }, (err) => {
                    this.loading = false;
                    uni.showToast({
                        title: '网络错误，请重试',
                        icon: 'none'
                    });
                    reject('网络错误，请重试');
                });
            });
        },

        // 简化的日期解析函数
        parseDate(dateStr) {
            if (!dateStr) return new Date();
            try {
                // 将日期字符串转换为标准格式
                const parts = dateStr.split(/[- :]/);
                if (parts.length >= 6) {
                    return new Date(parts[0], parts[1]-1, parts[2], parts[3], parts[4], parts[5]);
                } else if (parts.length >= 3) {
                    return new Date(parts[0], parts[1]-1, parts[2]);
                }
            } catch (e) {
                console.warn('日期解析失败:', dateStr, e);
            }
            return new Date();
        },

        formatMessages(data) {
            if (!data || !data.list || !Array.isArray(data.list)) {
                return [];
            }

            // 获取用户和管理员信息
            const userInfo = data.ies_uid_user || {};
            const adminList = data.admin_ies_list || {};

            return data.list.map((item, index) => {
                // 判断是否需要显示时间
                let showTime = index === 0;
                if (index > 0) {
                    const prevTime = this.parseDate(data.list[index - 1].add_time).getTime();
                    const currTime = this.parseDate(item.add_time).getTime();
                    showTime = (currTime - prevTime) > 5 * 60 * 1000; // 5分钟
                }

                // 判断消息类型
                const isUser = item.type === 1;

                // 获取头像和昵称
                let avatar = '';
                let nickname = '';

                if (isUser && userInfo) {
                    avatar = userInfo.avatar || 'https://img.tfcs.cn/static/img/que.jpg';
                    nickname = userInfo.nick_name || '用户';
                } else if (!isUser && item.admin_ies_uid && adminList[item.admin_ies_uid]) {
                    avatar = adminList[item.admin_ies_uid].avatar || 'https://img.tfcs.cn/static/img/que.jpg';
                    nickname = adminList[item.admin_ies_uid].nick_name || '客服';
                }

                return {
                    _key: item.id,
                    isUser: isUser,
                    avatar: avatar,
                    nickname: nickname,
                    content: item.content,
                    add_time: item.add_time,
                    time: showTime ? item.add_time : ''
                };
            });
        },

        // 滚动到底部
        scrollToBottom() {
            this.$nextTick(() => {
                if (this.$refs.scrollContainer) {
                    const container = this.$refs.scrollContainer;
                    container.scrollTop = container.scrollHeight;
                }
            });
        },

        // 格式化时间显示
        formatTime(timeStr) {
            if (!timeStr) return '';
            
            const date = this.parseDate(timeStr);
            const today = new Date();
            const todayStr = today.getFullYear() + '-' +
                            String(today.getMonth() + 1).padStart(2, '0') + '-' +
                            String(today.getDate()).padStart(2, '0');
            
            const dateStr = date.getFullYear() + '-' +
                          String(date.getMonth() + 1).padStart(2, '0') + '-' +
                          String(date.getDate()).padStart(2, '0');
            
            const timePartStr = String(date.getHours()).padStart(2, '0') + ':' +
                              String(date.getMinutes()).padStart(2, '0');
            
            // 如果是今天，只显示时间
            if (dateStr === todayStr) {
                return timePartStr;
            }
            
            // 如果不是今天，显示简短日期和时间
            return String(date.getMonth() + 1).padStart(2, '0') + '-' +
                   String(date.getDate()).padStart(2, '0') + ' ' +
                   timePartStr;
        },
    }
}
</script>

<style lang="scss" scoped>
.session-detail {
    position: relative;
    z-index: 999;

    /* 当弹窗显示时，防止滚动穿透 */
    &.prevent-scroll {
        touch-action: none; /* 禁止所有触摸操作 */
        overscroll-behavior: contain; /* 防止滚动穿透 */
    }

    .popup-box {
        position: fixed;
        left: 0;
        width: 100%;
        bottom: 0;
        height: 90vh;
        background-color: #fff;
        border-top-left-radius: 20rpx;
        border-top-right-radius: 20rpx;
        transform: translateY(100%);
        transition: transform 0.3s;
        z-index: 999;
        display: flex;
        flex-direction: column;

        &.show {
            transform: translateY(0);
        }
    }

    .popup-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx;
        border-bottom: 1rpx solid #f5f5f5;
        height: 100rpx;
        box-sizing: border-box;

        .title-container {
            flex: 1;
            .title {
                font-size: 32rpx;
                font-weight: bold;
                color: #333;
            }
        }

        .close-btn {
            width: 50rpx;
            height: 50rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;

            .close-icon {
                font-size: 40rpx;
                color: #999;
            }
        }
    }

    .popup-content {
        flex: 1;
        height: calc(90vh - 100rpx);
        background-color: #f7f7f7;
        padding: 0 30rpx;
        box-sizing: border-box;
        overflow-y: auto;
        /* 防止滚动穿透 */
        overscroll-behavior: contain;
        /* 隐藏滚动条 */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }
    
    .popup-content::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
    }

    .load-more-tip {
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #4a90e2;
        font-size: 28rpx;
        padding: 20rpx 0;
        cursor: pointer;
        border-bottom: 1rpx solid #e5e5e5;
        margin-bottom: 20rpx;
    }
    
    .load-more-tip:hover {
        background-color: #f0f0f0;
    }

    .loading-wrapper, .no-more {
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #b9b9b9;
        font-size: 24rpx;
        padding: 20rpx 0;
    }

    .message-list {
        padding: 20rpx 0;
    }

    .time-container {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 20rpx 0;
        position: relative; // 添加相对定位
        left: 0; // 确保从左边开始
        right: 0; // 确保延伸到右边
        text-align: center; // 文本居中

        .time {
            font-size: 22rpx;
            color: #fff;
            background-color: rgba(224, 225, 226, 0.8); // 使用半透明背景色
            padding: 4rpx 16rpx;
            border-radius: 20rpx; // 更圆润的边角
            display: inline-block; // 使背景色宽度与文字一致
            max-width: 180rpx; // 限制最大宽度
            white-space: nowrap; // 防止换行
            overflow: hidden; // 超出部分隐藏
            text-overflow: ellipsis; // 超出部分显示省略号
            box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1); // 添加轻微阴影
        }
    }

    .message-row {
        display: flex;
        margin-bottom: 40rpx;
        align-items: flex-start; // 确保头像和消息顶部对齐
        flex-direction: row; // 明确指定为行布局
        width: 100%; // 确保占据整行宽度

        &.right {
            flex-direction: row-reverse;
            justify-content: flex-start; // 确保内容从右侧开始

            .popper {
                margin-right: 20rpx;
                margin-left: 0;
                align-self: flex-end; // 使整个popper靠右对齐
                display: flex;
                flex-direction: column;
                align-items: flex-end; // 使内部内容靠右对齐

                .popper-title {
                    text-align: right;
                    width: 100%; // 确保标题占据整个宽度
                }

                .popper-content {
                    background-color: #efeff0; // 客服消息的背景色，根据要求调整
                    display: inline-block; // 使容器宽度自适应内容
                    width: auto; // 自适应宽度
                    text-align: left; // 文本内容仍然左对齐

                    .popper-arrow:after {
                        left: auto;
                        right: -10rpx;
                        border-left-width: 10rpx;
                        border-right-width: 0;
                        border-left-color: #efeff0; // 箭头颜色与背景色一致
                        border-right-color: transparent;
                        margin: 0; // 移除所有边距
                    }
                }
            }
        }
    }

    .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        overflow: hidden;
        background-color: #eee;
        flex-shrink: 0;
        display: inline-block; // 确保头像是内联块元素
        vertical-align: top; // 顶部对齐

        image {
            width: 100%;
            height: 100%;
        }
    }

    .popper {
        max-width: 75%;
        margin-left: 20rpx;
        flex: 1; 
        display: inline-block; 
        vertical-align: top;
        white-space: normal; 

        .popper-title {
            font-size: 24rpx;
            color: #999;
            margin-bottom: 10rpx;
        }

        .popper-content {
            position: relative;
            padding: 20rpx;
            background-color: #fff;
            border-radius: 10rpx;
            font-size: 28rpx;
            font-weight: bold; 
            line-height: 1.5;
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            box-sizing: border-box;
            max-width: 100%; 
            display: inline-block; 
            width: auto; 

            &.user-message {
                background-color: #cbe0ff; 
            }

            .popper-arrow:after {
                content: '';
                position: absolute;
                top: 20rpx;
                left: -18rpx;
                border: 10rpx solid transparent;
                border-right-color: #fff;
                border-right-width: 10rpx; 
                margin: 0; 
            }

            &.user-message .popper-arrow:after {
                border-right-color: #cbe0ff; 
            }
        }
    }

    .bottom-space {
        height: 100rpx;
    }

    .mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 998;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s;

        &.show {
            opacity: 1;
            visibility: visible;
        }
    }
}
</style>
