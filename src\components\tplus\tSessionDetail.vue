<template>
    <view class="session-detail" :class="{'prevent-scroll': show}" @touchmove.stop.prevent="disabledScroll">
        <view class="popup-box" :class="{ 'show': show }" @touchmove.stop.prevent="disabledScroll">
            <view class="popup-header" @touchmove.stop.prevent="disabledScroll">
                <view class="title-container">
                    <text class="title">{{ title }}</text>
                </view>
                <view class="close-btn" @click="close">
                    <text class="close-icon">×</text>
                </view>
            </view>
            <scroll-view
                class="popup-content"
                scroll-y
                @scrolltoupper="loadMoreMessages"
                upper-threshold="100"
                :scroll-into-view="scrollToView"
                :scroll-with-animation="scrollWithAnimation"
                :show-scrollbar="false"
                :scroll-top="scrollTopValue"
                @touchstart="touchStart"
                @touchmove="touchMove"
                @touchend="touchEnd"
                @scroll="onScroll"
            >
                <!-- 添加下拉刷新区域 -->
                <view class="pull-down-area" v-if="!loaded">
                    <view class="pull-indicator" :class="{'pulling': isPulling, 'loading': pullLoading}">
                        <text>{{ pullText }}</text>
                    </view>
                </view>
                
                <view class="loading-wrapper" v-if="loading && !pullLoading">
                    <view class="loading">加载中...</view>
                </view>
                <view class="no-more" v-else-if="loaded">
                    <text>没有更多了</text>
                </view>
                <view class="message-list">
                    <view class="message-list-item" v-for="item in list" :key="item._key" :id="'msg_' + item._key">
                        <!-- 将时间显示放在单独的容器中，确保它独立居中 -->
                        <view class="time-container" v-if="item.time">
                            <text class="time">{{ formatTime(item.time) }}</text>
                        </view>
                        <view class="message-row" :class="{ 'right': !item.isUser }">
                            <view class="avatar">
                                <image :src="item.avatar || 'https://img.tfcs.cn/static/img/que.jpg'" mode="aspectFill"></image>
                            </view>
                            <view class="popper">
                                <view class="popper-title" v-if="!item.isUser">{{ item.nickname }}</view>
                                <view class="popper-content" :class="{ 'user-message': item.isUser }">
                                    {{ item.content }}
                                    <view class="popper-arrow"></view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <!-- 底部留白，防止内容被遮挡 -->
                <view class="bottom-space"></view>
            </scroll-view>
        </view>
        <view
            class="mask"
            :class="{ 'show': show }"
            @click="close"
            @touchmove.stop.prevent="disabledScroll"
        ></view>
    </view>
</template>

<script>
export default {
    components: {
    },
    props: {
        height: {
            type: String,
            default: '90vh'
        }
    },
    data() {
        return {
            show: false,
            loading: false,
            loaded: false,
            title: '',
            params: {
                per_page: 10,
                last_time: 0,
                ies_uid: '',
                ies_uniq_id: '',
                user_mapping_id: '',
                user_type: 1,
                mobile: '',
                data_type: 0,
                view_from: 'client'
            },
            list: [],
            scrollToView: '',
            scrollTopValue: 0, // 用于控制 scroll-view 的滚动位置
            lastLoadTime: 0, // 上次加载历史消息的时间戳，用于防抖
            scrollWithAnimation: true, // 是否使用滚动动画
            isLoadingLocked: true, // 初始时锁定加载，防止自动触发
            scrollPositionInfo: null, // 记录滚动位置信息
            previousScrollHeight: 0, // 记录加载前的滚动内容高度
            initialLoad: true, // 标记是否是初次加载
            
            // 下拉刷新相关
            startY: 0, // 触摸开始位置
            moveY: 0, // 触摸移动位置
            isPulling: false, // 是否正在下拉
            pullLoading: false, // 是否正在加载
            pullText: '下拉加载更多', // 下拉提示文本
            pullThreshold: 60, // 下拉触发阈值
        }
    },
    computed: {
        contentHeight() {
            // 计算内容区域高度，减去头部高度
            return 'calc(' + this.height + ' - 100rpx)';
        }
    },
    methods: {
        // 触摸开始
        touchStart(e) {
            if (this.loading || this.loaded || this.isLoadingLocked) return;
            
            // 记录起始触摸位置
            this.startY = e.touches[0].clientY;
        },
        
        // 触摸移动
        touchMove(e) {
            if (this.loading || this.loaded || this.isLoadingLocked) return;
            
            this.moveY = e.touches[0].clientY;
            
            const distance = this.moveY - this.startY;
            
            if (distance > 0 && this.scrollTopValue < 10) {
                this.isPulling = true;
                
                // 更新提示文本
                if (distance > this.pullThreshold) {
                    this.pullText = '释放立即加载';
                } else {
                    this.pullText = '下拉加载更多';
                }
                
                // 阻止默认滚动行为
                e.preventDefault();
            }
        },
        
        // 触摸结束
        touchEnd(e) {
            if (this.loading || this.loaded || this.isLoadingLocked || !this.isPulling) return;
            
            const distance = this.moveY - this.startY;
            this.isPulling = false;
            
            // 如果下拉距离超过阈值，触发加载
            if (distance > this.pullThreshold) {
                this.pullLoading = true;
                this.pullText = '正在加载...';
                setTimeout(() => {
                    this.loadMoreByPull();
                }, 300);
            } else {
                this.pullText = '下拉加载更多';
            }
        },
        
        // 通过下拉触发加载
        loadMoreByPull() {
            if (!this.triggerLoadMore(true)) {
                this.pullLoading = false;
                this.pullText = this.loaded ? '没有更多了' : '下拉加载更多';
                return;
            }
            
        },
        
        disabledScroll() {
            // 阻止滑动穿透
            return false;
        },
        
        stopMove() {
            return this.disabledScroll();
        },
        
        handleScroll(e) {
            e.stopPropagation();
        },
        
        open(params) {
            this.title = (params.nick_name || params.ies_uniq_id || '用户') + '的会话信息';
            this.params = { ...params };
            this.loaded = false;
            this.list = [];
            this.scrollToView = ''; // 重置滚动位置
            this.scrollTopValue = 0; // 重置滚动位置值
            this.lastLoadTime = 0; // 重置上次加载时间
            this.scrollWithAnimation = true; // 重置滚动动画
            this.isLoadingLocked = true; // 初始时锁定加载，防止自动触发
            this.scrollPositionInfo = null; // 重置滚动位置信息
            this.previousScrollHeight = 0; // 重置内容高度记录
            this.pullLoading = false; // 重置下拉加载状态
            this.isPulling = false; // 重置下拉状态
            this.pullText = '下拉加载更多'; // 重置下拉提示文本
            this.initialLoad = true; // 标记为初次加载
            this.show = true;

            // 禁用页面滚动
            this.preventPageScroll();

            // 使用 nextTick 确保组件已渲染
            this.$nextTick(() => {
                this.loading = true; // 设置加载状态
                this.getList(false).then(() => {
                    // 首次加载完成后延迟解锁，防止自动触发加载
                    setTimeout(() => {
                        this.isLoadingLocked = false;
                        this.initialLoad = false;
                        console.log('初始加载完成，解锁加载状态');
                    }, 2000);
                });
            });

            return this;
        },
        
        close() {
            this.show = false;

            // 恢复页面滚动
            this.restorePageScroll();
        },

        // 禁用页面滚动
        preventPageScroll() {
            this.$emit('prevent-scroll', true);
        },

        // 恢复页面滚动
        restorePageScroll() {
            this.$emit('prevent-scroll', false);
        },
        
        onScroll(e) {
            // 处理滚动事件，防止滚动穿透
            e.stopPropagation();
        },
        
        // 记录当前滚动位置信息
        recordScrollPosition() {
            if (this.list.length === 0) return;
            
            // 创建一个查询对象
            const query = uni.createSelectorQuery().in(this);
            
            // 查找第一条消息元素
            query.select('.message-list-item').boundingClientRect(data => {
                if (data) {
                    // 记录第一条消息的信息
                    this.scrollPositionInfo = {
                        id: this.list[0]._key,
                        top: data.top,
                        height: data.height
                    };
                    
                    // 记录当前内容的总高度
                    query.select('.popup-content').boundingClientRect(contentData => {
                        if (contentData) {
                            this.previousScrollHeight = contentData.height;
                        }
                    }).exec();
                }
            }).exec();
        },
        
        // 恢复滚动位置
        restoreScrollPosition() {
            if (!this.scrollPositionInfo || this.list.length === 0) return;
            
            // 延时执行，确保DOM已更新
            setTimeout(() => {
                const query = uni.createSelectorQuery().in(this);
                
                // 查找之前记录的消息元素
                query.select(`#msg_${this.scrollPositionInfo.id}`).boundingClientRect(data => {
                    if (data) {
                        // 计算新的滚动位置
                        const newScrollTop = this.scrollTopValue + (data.top - this.scrollPositionInfo.top);
                        
                        // 设置新的滚动位置
                        this.scrollWithAnimation = false; // 禁用动画，避免视觉跳动
                        this.setScrollTop(Math.max(0, newScrollTop));
                        
                        // 重置滚动动画状态
                        setTimeout(() => {
                            this.scrollWithAnimation = true;
                        }, 100);
                    }
                }).exec();
            }, 50);
        },
        
        // 解锁加载
        unlockLoading() {
            setTimeout(() => {
                // 只有在非初次加载时才解锁
                if (!this.initialLoad) {
                    this.isLoadingLocked = false;
                }
                if (this.loaded) {
                    this.pullText = '没有更多了';
                }
            }, 800);
        },
        
        loadMoreMessages() {
            // 使用统一的触发方法
            this.triggerLoadMore(true);
        },
        
        async getList(isLoadingMore = false) {
            // 如果没有必要的参数，则不进行请求
            if (!this.params || (!this.params.user_mapping_id && !this.params.ies_uid && !this.params.ies_uniq_id && !this.params.mobile)) {
                console.warn('缺少必要的参数，无法获取会话数据');
                this.loading = false;
                this.isLoadingLocked = false; // 解除锁定
                this.pullLoading = false; // 确保重置下拉加载状态
                return Promise.reject('缺少必要的参数');
            }

            this.loading = true;

            // 构建请求参数
            let requestParams = {};
            let requestUrl = '';
            if(this.params.data_type == 1){
                //抖音私信
                requestParams = {
                    ies_uid: this.params.ies_uid || 0,
                    ies_uniq_id: this.params.ies_uniq_id || '',
                    user_mapping_id: this.params.user_mapping_id,
                    user_type: this.params.user_type || 2,
                    view_from: this.params.view_from || 'client'
                };
                requestUrl = '/admin/douyin/private_letter/user/chat';
            }
            if(this.params.data_type == 2){
                //微信视频号私信
                requestParams = {
                    mobile: this.params.mobile,
                    view_from: this.params.view_from || 'client'
                };
                requestUrl = '/admin/weixin/private_letter/user/chat';
            }

            if(this.params.data_type == 3){
                //微信小店私信
                requestParams = {
                    mobile: this.params.mobile,
                    view_from: this.params.view_from || 'client'
                };
                requestUrl = '/admin/weixin/private_letter/user/chat/shop';
            }

            // 如果是加载更多，添加last_time参数
            if (isLoadingMore && this.params.last_time) {
                requestParams.last_time = this.params.last_time;
            }

            console.log('请求参数:', requestParams);

            // 使用 Promise 包装请求
            return new Promise((resolve, reject) => {
                // 发起请求
                this.$ajax.get(requestUrl, requestParams, (res) => {
                    this.loading = false;

                    if (res.statusCode === 200) {
                        // 保存last_time用于加载更多
                        if (res.data.last_time) {
                            this.params.last_time = res.data.last_time;
                        }

                        // 处理是否有更多数据
                        const hasMoreData = res.data.has_more && res.data.list.length > 0;
                        this.loaded = !hasMoreData;

                        // 如果没有更多数据，记录日志并更新提示文本
                        if (this.loaded) {
                            console.log('没有更多历史消息了');
                            this.pullText = '没有更多了';
                        }

                        // 转换数据格式
                        const formattedMessages = this.formatMessages(res.data);

                        // 如果没有消息，并且是加载更多，则标记为已加载完毕
                        if (formattedMessages.length === 0 && isLoadingMore) {
                            this.loaded = true;
                            this.pullText = '没有更多了';
                        }

                        if (isLoadingMore) {
                            // 加载更多时，将新消息添加到列表前面
                            this.list = [...formattedMessages, ...this.list];

                            // 恢复滚动位置，确保用户看到的内容保持不变
                            this.$nextTick(() => {
                                this.restoreScrollPosition();
                            });
                        } else {
                            // 首次加载时，直接设置列表
                            this.list = formattedMessages;

                            // 首次加载完成后滚动到最新消息
                            this.scrollToLatestMessage();
                        }
                        
                        // 解锁加载状态，添加延时确保DOM已完全渲染
                        this.unlockLoading();
                        
                        // 重置下拉加载状态
                        this.pullLoading = false;
                        this.pullText = this.loaded ? '没有更多了' : '下拉加载更多';
                        
                        resolve();
                    } else {
                        uni.showToast({
                            title: res.data?.message || '获取会话数据失败',
                            icon: 'none'
                        });
                        // 解锁加载状态
                        this.isLoadingLocked = false;
                        
                        // 重置下拉加载状态
                        this.pullLoading = false;
                        this.pullText = '下拉加载更多';
                        
                        reject(res.data?.message || '获取会话数据失败');
                    }
                }, (err) => {
                    this.loading = false;
                    // 解锁加载状态
                    this.isLoadingLocked = false;
                    
                    // 重置下拉加载状态
                    this.pullLoading = false;
                    this.pullText = '下拉加载更多';
                    
                    uni.showToast({
                        title: '网络错误，请重试',
                        icon: 'none'
                    });
                    reject('网络错误，请重试');
                });
            });
        },

        // 设置滚动位置
        setScrollTop(scrollTop) {
            this.scrollTopValue = scrollTop;

            // 使用 nextTick 确保视图更新
            this.$nextTick(() => {
                // 在某些平台上，可能需要延迟执行
                setTimeout(() => {
                    // 重新设置一次，确保滚动生效
                    this.scrollTopValue = scrollTop;
                }, 50);
            });
        },

        // 添加一个新的日期格式化辅助函数
        parseDate(dateStr) {
            if (!dateStr) return new Date();
            // 将日期字符串转换为iOS兼容的格式
            const parts = dateStr.split(/[- :]/);
            if (parts.length >= 6) {
                // 使用数组方式创建日期对象
                return new Date(parts[0], parts[1]-1, parts[2], parts[3], parts[4], parts[5]);
            } else if (parts.length >= 3) {
                // 只有日期没有时间的情况
                return new Date(parts[0], parts[1]-1, parts[2]);
            }
            // 如果格式不正确，返回当前时间
            return new Date();
        },

        formatMessages(data) {
            if (!data || !data.list || !Array.isArray(data.list)) {
                return [];
            }

            // 获取用户和管理员信息
            const userInfo = data.ies_uid_user || {};
            const adminList = data.admin_ies_list || {};

            return data.list.map((item, index) => {
                // 判断是否需要显示时间
                // 如果是第一条消息或者与前一条消息时间相差超过5分钟，则显示时间
                let showTime = index === 0;
                if (index > 0) {
                    const prevTime = this.parseDate(data.list[index - 1].add_time).getTime();
                    const currTime = this.parseDate(item.add_time).getTime();
                    showTime = (currTime - prevTime) > 5 * 60 * 1000; // 5分钟
                }

                // 判断消息类型（用户或管理员）
                const isUser = item.type === 1;

                // 获取头像和昵称
                let avatar = '';
                let nickname = '';

                if (isUser && userInfo) {
                    avatar = userInfo.avatar || 'https://img.tfcs.cn/static/img/que.jpg';
                    nickname = userInfo.nick_name || '用户';
                } else if (!isUser && item.admin_ies_uid && adminList[item.admin_ies_uid]) {
                    avatar = adminList[item.admin_ies_uid].avatar || 'https://img.tfcs.cn/static/img/que.jpg';
                    nickname = adminList[item.admin_ies_uid].nick_name || '客服';
                }

                return {
                    _key: item.id,
                    isUser: isUser,
                    avatar: avatar,
                    nickname: nickname,
                    content: item.content,
                    add_time: item.add_time,
                    time: showTime ? item.add_time : ''
                };
            });
        },

        // 新增方法：滚动到最新消息
        scrollToLatestMessage() {
            if (this.list.length > 0) {
                // 临时锁定加载，防止滚动过程中触发加载
                this.isLoadingLocked = true;
                this.$nextTick(() => {
                    // 设置滚动到最后一条消息
                    this.scrollToView = 'msg_' + this.list[this.list.length - 1]._key;

                    // 额外延时确保滚动生效，解决某些设备上可能的滚动问题
                    setTimeout(() => {
                        this.scrollToView = 'msg_' + this.list[this.list.length - 1]._key;
                        
                        // 如果是初次加载，保持锁定状态
                        // 如果不是初次加载，延迟解锁
                        if (!this.initialLoad) {
                            setTimeout(() => {
                                this.isLoadingLocked = false;
                            }, 1000);
                        }
                    }, 100);
                });
            }
        },

        // 格式化时间显示
        formatTime(timeStr) {
            if (!timeStr) return '';
            // 使用parseDate函数解析时间
            const date = this.parseDate(timeStr);
            
            // 获取今天的日期
            const today = new Date();
            const todayStr = today.getFullYear() + '-' +
                            String(today.getMonth() + 1).padStart(2, '0') + '-' +
                            String(today.getDate()).padStart(2, '0');
            
            // 获取传入时间的日期部分
            const dateStr = date.getFullYear() + '-' +
                          String(date.getMonth() + 1).padStart(2, '0') + '-' +
                          String(date.getDate()).padStart(2, '0');
            
            // 获取时间部分
            const timePartStr = String(date.getHours()).padStart(2, '0') + ':' +
                              String(date.getMinutes()).padStart(2, '0');
            
            // 如果是今天，只显示时间
            if (dateStr === todayStr) {
                return timePartStr;
            }
            
            // 如果不是今天，显示简短日期和时间
            return String(date.getMonth() + 1).padStart(2, '0') + '-' +
                   String(date.getDate()).padStart(2, '0') + ' ' +
                   timePartStr;
        },

        // 触发加载的方法统一使用此方法，确保行为一致
        triggerLoadMore(isManualTrigger = false) {
            // 如果正在加载、已经加载完所有数据或加载被锁定，则不处理
            if (this.loading || this.loaded || this.isLoadingLocked) {
                return false;
            }

            // 增加防抖时间间隔，确保不会连续触发
            const now = Date.now();
            if (this.lastLoadTime && (now - this.lastLoadTime < 800)) {
                console.log('加载过于频繁，忽略此次请求');
                return false;
            }
            console.log('触发加载更多历史消息 - ' + (isManualTrigger ? '用户手动触发' : '系统触发'));
            // 记录本次加载时间
            this.lastLoadTime = now;
            // 锁定加载，防止连续触发
            this.isLoadingLocked = true;
            // 记录当前滚动位置
            this.recordScrollPosition();
            // 传递 isLoadingMore=true 参数，表示这是加载更多历史消息
            this.getList(true);
            return true;
        },
    }
}
</script>

<style lang="scss" scoped>
.session-detail {
    position: relative;
    z-index: 999;

    /* 当弹窗显示时，防止滚动穿透 */
    &.prevent-scroll {
        touch-action: none; /* 禁止所有触摸操作 */
        overscroll-behavior: contain; /* 防止滚动穿透 */
    }

    /* 下拉刷新区域样式 */
    .pull-down-area {
        width: 100%;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .pull-indicator {
            font-size: 24rpx;
            color: #b9b9b9;
            transition: all 0.3s;
            
            &.pulling {
                transform: scale(1.1);
            }
            
            &.loading {
                color: #4a90e2;
            }
        }
    }

    .popup-box {
        position: fixed;
        left: 0;
        width: 100%;
        bottom: 0;
        height: 90vh;
        background-color: #fff;
        border-top-left-radius: 20rpx;
        border-top-right-radius: 20rpx;
        transform: translateY(100%);
        transition: transform 0.3s;
        z-index: 999;
        display: flex;
        flex-direction: column;

        &.show {
            transform: translateY(0);
        }
    }

    .popup-header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 30rpx;
        border-bottom: 1rpx solid #f5f5f5;
        height: 100rpx;
        box-sizing: border-box;

        .title-container {
            flex: 1;
            .title {
                font-size: 32rpx;
                font-weight: bold;
                color: #333;
            }
        }

        .close-btn {
            width: 50rpx;
            height: 50rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;

            .close-icon {
                font-size: 40rpx;
                color: #999;
            }
        }
    }

    .popup-content {
        flex: 1;
        height: calc(90vh - 100rpx);
        background-color: #f7f7f7;
        padding: 0 30rpx;
        box-sizing: border-box;
        overflow-y: auto;
        /* 防止滚动穿透 */
        overscroll-behavior: contain;
        touch-action: pan-y; /* 只允许垂直方向的触摸操作 */
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
            width: 0;
            height: 0;
            color: transparent;
            -webkit-appearance: none;
            background: transparent;
        }
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }

    .loading-wrapper, .no-more {
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #b9b9b9;
        font-size: 24rpx;
        padding: 20rpx 0;
    }

    .message-list {
        padding: 20rpx 0;
    }

    .time-container {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 20rpx 0;
        position: relative; // 添加相对定位
        left: 0; // 确保从左边开始
        right: 0; // 确保延伸到右边
        text-align: center; // 文本居中

        .time {
            font-size: 22rpx;
            color: #fff;
            background-color: rgba(224, 225, 226, 0.8); // 使用半透明背景色
            padding: 4rpx 16rpx;
            border-radius: 20rpx; // 更圆润的边角
            display: inline-block; // 使背景色宽度与文字一致
            max-width: 180rpx; // 限制最大宽度
            white-space: nowrap; // 防止换行
            overflow: hidden; // 超出部分隐藏
            text-overflow: ellipsis; // 超出部分显示省略号
            box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1); // 添加轻微阴影
        }
    }

    .message-row {
        display: flex;
        margin-bottom: 40rpx;
        align-items: flex-start; // 确保头像和消息顶部对齐
        flex-direction: row; // 明确指定为行布局
        width: 100%; // 确保占据整行宽度

        &.right {
            flex-direction: row-reverse;
            justify-content: flex-start; // 确保内容从右侧开始

            .popper {
                margin-right: 20rpx;
                margin-left: 0;
                align-self: flex-end; // 使整个popper靠右对齐
                display: flex;
                flex-direction: column;
                align-items: flex-end; // 使内部内容靠右对齐

                .popper-title {
                    text-align: right;
                    width: 100%; // 确保标题占据整个宽度
                }

                .popper-content {
                    background-color: #efeff0; // 客服消息的背景色，根据要求调整
                    display: inline-block; // 使容器宽度自适应内容
                    width: auto; // 自适应宽度
                    text-align: left; // 文本内容仍然左对齐

                    .popper-arrow:after {
                        left: auto;
                        right: -10rpx;
                        border-left-width: 10rpx;
                        border-right-width: 0;
                        border-left-color: #efeff0; // 箭头颜色与背景色一致
                        border-right-color: transparent;
                        margin: 0; // 移除所有边距
                    }
                }
            }
        }
    }

    .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        overflow: hidden;
        background-color: #eee;
        flex-shrink: 0;
        display: inline-block; // 确保头像是内联块元素
        vertical-align: top; // 顶部对齐

        image {
            width: 100%;
            height: 100%;
        }
    }

    .popper {
        max-width: 75%;
        margin-left: 20rpx;
        flex: 1; 
        display: inline-block; 
        vertical-align: top;
        white-space: normal; 

        .popper-title {
            font-size: 24rpx;
            color: #999;
            margin-bottom: 10rpx;
        }

        .popper-content {
            position: relative;
            padding: 20rpx;
            background-color: #fff;
            border-radius: 10rpx;
            font-size: 28rpx;
            font-weight: bold; 
            line-height: 1.5;
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            box-sizing: border-box;
            max-width: 100%; 
            display: inline-block; 
            width: auto; 

            &.user-message {
                background-color: #cbe0ff; 
            }

            .popper-arrow:after {
                content: '';
                position: absolute;
                top: 20rpx;
                left: -18rpx;
                border: 10rpx solid transparent;
                border-right-color: #fff;
                border-right-width: 10rpx; 
                margin: 0; 
            }

            &.user-message .popper-arrow:after {
                border-right-color: #cbe0ff; 
            }
        }
    }

    .bottom-space {
        height: 100rpx;
    }

    .mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 998;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s;

        &.show {
            opacity: 1;
            visibility: visible;
        }
    }
}
</style>
