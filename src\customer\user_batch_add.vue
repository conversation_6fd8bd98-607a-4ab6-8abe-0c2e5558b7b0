<template>
  <view class="batchadd">
    <view class="desc">
      系统为您分配了{{
        total
      }}个客户号码，快去复制号码添加客户吧，管理员可在系统后台查看添加状态。
      <view class="desc-tip" @click="clickUphold">如何快速添加客户>></view>
    </view>
    <view style="margin-bottom:24px" @click="pickerVisibleCer = true"
      >选择状态：{{ type_name }}</view
    >
    <view class="title row">
      共<text>{{ total }}</text
      >个客户
    </view>
    <t-table>
      <t-tr class="back" color="#000">
        <t-th v-for="(item, index) in tables_th" :key="index">{{ item }}</t-th>
      </t-tr>
      <t-tr v-for="(item, index) in statistics_list" :key="index">
        <t-td>{{ item.type == 1 ? "已添加" : "未添加" }}</t-td>
        <t-td>{{ item.mobile }}</t-td>
        <t-td>{{ item.name }}</t-td>
        <t-td @click="copyMobile(item)">复制</t-td>
      </t-tr>
    </t-table>
    <load-more :status="load_status"></load-more>
    <!-- 弹出选择框 -->
    <!-- <VuePicker
      :data="pickDataCer"
      title="请选择状态"
      cancelText="取消"
      confirmText="确认"
      :showToolbar="true"
      @confirm="confirmCer"
      :visible.sync="pickerVisibleCer"
    /> -->
  </view>
</template>

<script>
import tTable from "@/components/t-table/t-table.vue";
import tTh from "@/components/t-table/t-th.vue";
import tTr from "@/components/t-table/t-tr.vue";
import tTd from "@/components/t-table/t-td.vue";
import loadMore from "@/components/loadMore.vue";
// import VuePicker from "vue-pickers";
export default {
  components: {
    tTable,
    tTh,
    tTr,
    tTd,
    loadMore,
    // VuePicker,
  },
  data() {
    return {
      tables_th: ["添加状态", "电话号码", "客户姓名", "操作"],
      statistics_list: [],
      load_status: "",
      params: {
        page: 1,
        user_id: "",
        type: 0,
      },
      total: 0,
      pickerVisibleCer: false,
      pickDataCer: [],
      type_name: "请选择",
    };
  },
  onLoad(options) {
    this.params.user_id = options.user_id;
    let arr = [
      {
        value: 0,
        label: "全部",
      },
      {
        value: 1,
        label: "未添加",
      },
      {
        value: 2,
        label: "已添加",
      },
    ];
    this.pickDataCer.push(arr);
    this.getDataList();
  },
  methods: {
    confirmCer(res) {
      res.map((item) => {
        this.type_name = item.label;
        this.params.type = item.value;
      });
      this.params.page = 1;
      this.getDataList();
    },
    getDataList() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.statistics_list = [];
      }
      this.$ajax.get(
        "/client/customer/add_user/HfriendMember",
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.statistics_list = this.statistics_list.concat(res.data.data);
            this.total = res.data.total;
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
            }
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
            this.load_status = "loadend";
          }
        }
      );
    },
    copyMobile(item) {
      this.$copyText(item.mobile, () => {
        uni.showToast({
          title: "复制成功",
          icon: "none",
        });
      });
    },
    clickUphold() {
      //录入客户
      this.$navigateTo("uphold?type=1");
    },
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>
<style scoped lang="scss">
.batchadd {
  padding: 20px;
  background: #fff;
  .desc {
    padding: 8px;
    background: #fff2ee;
    margin-bottom: 20px;
    line-height: 20px;
    .desc-tip {
      color: #0083ff;
    }
  }
  .title {
    margin-bottom: 20px;
    text {
      color: #0083ff;
    }
  }
}
</style>
