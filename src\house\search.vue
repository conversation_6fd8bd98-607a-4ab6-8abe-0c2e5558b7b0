<template>
  <view class="search-box">
    <view class="sticky">
      <view class="search-input">
        <uni-easyinput :styles="styles" :placeholderStyle='placeholderStyle' prefixIcon="search" v-model="keyword"
          placeholder="小区名称 / 业主电话 / 房源编号" @input="inputChange">
        </uni-easyinput>
        <text @click="back">取消</text>
      </view>
      <scroll-view class="nav-list" scroll-x>
        <view class="filter-item" :class="index == active ? 'active' : ''" v-for="(item, index) in filterList"
          :key="index" @click="filterChange(item)">
          {{ item.name }}
        </view>
      </scroll-view>
      <view style="background-color: #fff;">
        <view class="doorplate-search" v-if="active == 1 && showDoorplate">
          <view class="search-box">
            <text>楼栋</text>
            <uni-easyinput :inputBorder="false" placeholder="输入楼栋" v-model="doorplate.loudong">
            </uni-easyinput>
            <text>单元</text>
            <uni-easyinput :inputBorder="false" placeholder="输入单元" v-model="doorplate.danyuan">
            </uni-easyinput>
            <text>门牌</text>
            <uni-easyinput :inputBorder="false" placeholder="输入门牌" v-model="doorplate.fanghao">
            </uni-easyinput>
          </view>
          <view class="search-btn">
            <text @click="reset">重 置</text>
            <text @click="doorplateSearch(doorplate)">查 找</text>
          </view>
        </view>
        <text class="show-doorplate" v-if="active == 1 && showDoorplate"
          @click="showDoorplate = !showDoorplate">&#9650;</text>
        <text class="show-doorplate" v-if="active == 1 && !showDoorplate"
          @click="showDoorplate = !showDoorplate">&#9660;</text>
      </view>
    </view>
    <view class="house-list">
      <view class="house" v-for="house in house_list" :key="house.id">
        <privateItem class="" :house="house" @toDetail="checkPerssion($event, toPrivateDetail)"
          @copy="checkPerssion($event, copyPrivate)" @follow="checkPerssion($event, followPrivate)"
          @addLianmai="checkPerssion($event, addToLianmai)" @addShowing="checkPerssion($event, addToShowing)"
          @houseGroup="checkPerssion($event, houseGroup)" @setLevels="checkPerssion($event, setLevels)"
          @setLevel="checkPerssion($event, setLevels)" @tixing="checkPerssion($event, tixing)"
          @houseTop="checkPerssion($event, houseTop)" @houseDel="checkPerssion($event, houseDel)"></privateItem>
      </view>
      <loadMore v-if="keyword != ''" :status="load_status" @reload="getList()" />
    </view>
    <view class="search-default" v-if="keyword == '' && showSearchDefault">
      <image src='https://img.tfcs.cn/backup/yidongduan/customer/customer_search.png' mode="widthFix" />
      <text>暂无内容</text>
    </view>
    <my-popup :show="show_set_showing" @close="show_set_showing = false">
      <view>
        <setShowing :is_status="true" :house="current_house" @success="setShowingSuccess" />
      </view>
    </my-popup>
  </view>
</template>
<script>
import privateItem from './components/privateItem.vue'
import loadMore from '@/components/loadMore'
import { copyPrivate } from '@/page_outside/private.js'
import myPopup from '@/components/myPopup.vue'
export default {
  components: {
    privateItem,
    loadMore,
    myPopup
  },
  data() {
    return {
      showSearchDefault: true,
      showDoorplate: true,
      current_house: {},
      show_set_showing: false,
      isLoad: false,
      load_status: 'loading',
      active: 0,
      keyword: "",
      placeholderStyle: "color:#a1a1a1;font-size:14px",
      styles: {
        color: '#333',
        border: 0,
        borderRadius: '16rpx',
        backgroundColor: '#f3f3f3'
      },
      filterList: [
        { name: '全部结果', type: 0 },
        { name: '小区名称', type: 1 },
        { name: '业主电话', type: 2 },
        { name: '房源编号', type: 3 },
      ],
      doorplate: {
        loudong: '',
        danyuan: '',
        fanghao: ''
      },
      params: {
        page: 1,
        keyword: "",
        rows: 10,
      },
      house_list: [],
      unfollowInfo: {}
    }
  },
  onReachBottom() {
    if (this.load_status == 'loading') return
    this.params.page++
    this.getList()
  },
  onLoad(options) {
    if (options.is_owner) {
      this.params.is_owner = options.is_owner
    }
    this.getUnfollow()
  },
  methods: {
    getList() {
      this.load_status = 'loading'
      let params = Object.assign({}, this.params);
      this.$ajax.get(`/admin/house/privateHouses`, params, (res) => {
        if (res.statusCode == 200) {
          this.house_list = this.house_list.concat(res.data.data)
          this.showSearchDefault = false
          if (res.data.data.length < this.params.rows) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'loadend'
          }
        }
      })
    },
    getUnfollow() {
      this.$ajax.get("/admin/house/remindPhoneFollow", {}, res => {
        if (res.statusCode == 200) {
          this.unfollowInfo = res.data
        }
      })
    },
    followPrivate(e) {
      this.$navigateTo(`/house/follow_up?follow_id=${e.id}&from=private`)
    },
    initData() {
      this.params.page = 1
      this.getList()
    },
    houseDel(e) {
      uni.showModal({
        title: '是否删除该房源？',
        success: (result) => {
          if (result.confirm) {
            this.$ajax.post('/v1/wapLm/editPrivateHouse', { id: e.id, is_del: 1 }, (res) => {
              uni.showToast({
                title: res.data.message,
                icon: 'none',
              })
              if (res.data.status == 200) {
                this.params.page = 1
                this.initData()
              }
            })
          } else if (result.cancel) {
            console.log('用户点击取消');
          }

        },
      })
    },
    addToLianmai(house) {
      this.showModal({
        title: '确定发布到联卖房源吗？',
        confirm: () => {
          this.$ajax.get(`/v1/wapLm/releaseToLm/${house.id}`).then((res) => {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
            })
          })
        },
      })
    },
    copyPrivate(e) {
      this.$ajax.get(`/admin/house/shareHouse/${e.id}`, {}, res => {
        if (res.statusCode == 200) {
          this.$set(e, 'current_login_user', res.data)
          copyPrivate(e)
        } else {
          uni.showToast({
            title: res.data.message || '请求失败',
            icon: 'none'
          })
        }
      })
    },
    setShowingSuccess() {
      this.show_set_showing = false
      this.getList()
    },
    houseTop(e) {
      uni.showModal({
        title: '是否聚焦该房源？',
        success: (result) => {
          if (result.confirm) {
            this.$ajax.post('/v1/wapLm/editPrivateHouse', { id: e.id, is_top: 1 }, (res) => {
              uni.showToast({
                title: res.data.message,
                icon: 'none',
              })
              if (res.data.status == 200) {
                this.params.page = 1
                this.initData()
              }
            })
          } else if (result.cancel) {
            console.log('用户点击取消');
          }
        },
      })
    },
    setChangeFilter() {
      if (!this.keyword) return
      this.house_list = []
      this.params.page = 1
      this.params.keyword = ""
      switch (this.active) {
        case 0:
          this.params.keyword = this.keyword
          break;
        case 1:
          this.params.keyword = this.keyword
          break;
        case 2:
          this.params.tel = this.keyword
          break;
        case 3:
          this.params.hid = this.keyword
          break;
        default:
          break;
      }
      this.getList()
    },
    inputChange(e) {
      if (!e) return
      this.setChangeFilter()
    },
    filterChange(e) {
      if (e.type == this.active) return
      this.active = e.type
      this.setChangeFilter()
    },
    toPrivateDetail(e) {
      this.$navigateTo(`/house/detail?id=${e.id}`)
    },
    addToShowing(e) {
      this.current_house = e
      this.show_set_showing = true
    },
    houseGroup(house) {
      this.$navigateTo("/house/house_send?house_id=" + house.id + "&protect=" + house.protect)
    },
    tixing(e) {
      this.$navigateTo(`/house/remind?remind_id=${e.id}`)
    },
    setLevels(e) {
      if (e.unilateral_agent == 1 && e.unilateral_agent_auth == 0) {
        uni.showToast({
          title: '请联系房源维护人',
          icon: 'none',
        })
        return
      }
      this.$ajax.post('/admin/house/editPrivateHouse', { id: e.house.id, level: e.level }, (res) => {
        if (res.statusCode == 200) {
          uni.showToast({
            title: '等级更改成功',
            icon: 'none',
          })
          this.params.page = 1
          this.getList()
        } else {
          uni.showToast({
            title: '等级更改失败',
            icon: 'none',
          })
        }
      }, () => {
        uni.showToast({
          title: '等级更改失败',
          icon: 'none',
        })
      })
    },
    checkPerssion(e, callback) {
      if (this.unfollowInfo.remindPhoneFollow && this.unfollowInfo.count > 0) {
        uni.showModal({
          title: "提示",
          content: `您有${this.unfollowInfo.count}条未跟进记录需要跟进,立即跟进？`,
          confirmText: "立即跟进",
          success: (res) => {
            if (res.confirm) {
              this.$navigateTo(`/house/detail?id=${this.unfollowInfo.info.info_id}`)
            }
          }
        })
      } else {
        callback && callback(e)
      }
    },
    back() {
      uni.navigateBack()
    },
    reset() {
      this.doorplate.loudong = ''
      this.doorplate.danyuan = ''
      this.doorplate.fanghao = ''
    },
    doorplateSearch(e) {
      this.params.page = 1
      this.params.keyword = ""
      this.params.page = 1
      this.params.loudong = e.loudong
      this.params.danyuan = e.danyuan
      this.params.fanghao = e.fanghao
      this.house_list = []
      this.getList()
    }
  }
}
</script>
<style lang="scss">
page {
  background-color: #f3f3f3;
}

.is-input-border {
  border: 0 !important;
  border-radius: 16rpx !important;
  background-color: #f6f6f6 !important;
}

.search-default {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-top: 20%;

  image {
    display: inline-block;
    width: 300rpx;
    height: 300rpx;
  }

  text {
    color: #292C3966;
  }
}

.show-doorplate {
  text-align: center;
  padding: 15rpx;
  color: #a1a1a1;
}

.active {
  color: #3d80fd;
}

.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  margin-left: -18rpx;
  display: block;
  width: 36rpx;
  height: 3px;
  align-items: center;
  background-color: #3d80fd;
}

.sticky {
  position: sticky;
  top: 0;
  z-index: 9;
  box-shadow: 0 -1px #fff;
  background-color: #fff;
}

.search-box {
  width: 100vw;
  // padding: 0 20rpx;

  .search-input {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 32rpx 8rpx;

    text {
      color: #3d80fd;
      padding-left: 20rpx;
    }
  }

  .nav-list {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 38rpx * 2;
    font-size: 24rpx;

    color: #999;
    white-space: nowrap;
    background-color: #fff;

    .filter-item {
      position: relative;
      display: inline-block;
      width: 25%;
      height: 100%;
      line-height: 38rpx * 2;
      text-align: center;
    }

  }

  .doorplate-search {
    display: flex;
    width: 90%;
    margin: 0 auto;
    padding: 0 20rpx;
    border-radius: 3px;
    background-color: #fff;

    .search-box {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      // .uni-easyinput__content-input {
      //   background-color: #f3f3f3 !important;
      // }

      // .uni-easyinput__content {
      //   background-color: #f3f3f3 !important;
      // }

      text {
        color: #666;
      }
    }

    .search-btn {
      display: flex;
      flex-direction: row;
      margin: 20rpx 0;

      text {
        color: #fff;
        font-size: 28rpx;
        padding: 10rpx 20rpx;
        border-radius: 3px;
      }

      &>text:nth-child(1) {
        margin-right: 25rpx;
        background-color: rgb(251, 167, 0);
        box-shadow: 0 3px 10rpx rgba($color: #523501, $alpha: 0.1);
      }

      &>text:nth-child(2) {
        background-color: #3d80fd;
        box-shadow: 0 3px 10rpx rgba($color: #0d0152, $alpha: 0.1);
      }
    }
  }

  .search-fold {
    width: 100%;
  }

  .house-list {
    width: 100vw;

    .house {
      padding: 20rpx 0;
    }
  }
}
</style>