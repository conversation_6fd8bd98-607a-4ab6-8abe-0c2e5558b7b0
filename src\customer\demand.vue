<template>
  <view class="need">
    <!-- 信息 -->
    <uni-collapse ref="collapse" accordion v-model="value" @change="change" v-if="userInfo.name">
      <uni-collapse-item :title="userInfo.name">
        <template #title>
          <view class="username">
            <view class="phone_bottom">{{ name }}</view>
            <view>{{userInfo.name}}</view>
          </view>
        </template>
        <view class="content">
          <text style="color: rgba(41, 44, 57, 0.40);font-size: 28rpx;margin-bottom:24rpx"
            v-if="admin_userlist  && admin_userlist.user_name">{{ admin_userlist.user_name }} / {{ admin_userlist.department_name }}</text>
          <text class="text" style="color: rgba(41, 44, 57, 0.40);font-size: 28rpx;margin-bottom:24rpx">{{ logs_list_time
          }} </text>

          <text
            style="color: #292C39;font-size: 28rpx;white-space: normal; word-break: break-all; word-wrap: break-word;">{{
              logs_list_content }}</text>
        </view>
      </uni-collapse-item>
    </uni-collapse>

    <view style="width: 100%;height: 36rpx;background: #fff;" v-else></view>
    
    <view class="need-box">
      <view class="form-box">

        <!-- 手机号 -->
        <template v-if="telList.length">
          <view class="phone_one" v-for="(item, index) in telList" :key="index">
            <view>
              <view class="user_phone" v-if="!isShowTelFullNumber">
                {{ item | mobileFilter }}
              </view>
              <view class="user_phone" @click="copyPhone(item)"
                v-else>
                {{ item }}
              </view>
            </view>
            <view style=" color: rgba(41, 44, 57, 0.40);font-size: 24rpx; margin-left: 10rpx;">
                <view v-if="!mobile_place" @click="searchAddress">归属地查询</view>
              <view v-else>{{mobile_place }}</view>
              </view>
            <view style="display: flex;flex-direction: row;">
              <view class="user_type" style="background: rgba(72, 138, 246, 0.20); color: #488AF6" v-if="isShowTelFullNumber || calls != 1"
                @click="conPhone(item)">
                <!-- {{ telType > 0 ? '外呼' : '电话' }} -->
                拨打
              </view>
              <view class="user_type" style="margin-left: 24rpx;" v-if="calls == 1" @click="makePhoneCall(item)">外呼</view>
            </view>

          </view>
        </template>
        <view v-if="tel" style="margin-bottom: 40rpx;">
          <view class="labelnew"> 接通状态 </view>
          <view class="type-box flex-row">
            <view class="type-item" v-for="item in last_call_statuslidt" :key="item.id"
              :class="{ is_type: item.id == call_status }" @click="onChangeTypes(item)">
              <view class="checked">
                <image v-show="item.id == call_status" src="@/static/img/checked.png"></image>
              </view>
              {{ item.name }}
            </view>
          </view>
        </view>

        <!--我司成交 不再显示客户状态-->
        <template v-if="!isUsDeal">
          <view class="labelnew">客户状态</view>
          <view class="type-box flex-row">
            <view class="type-item" v-for="item in follow_status_list" :key="item.id"
              :class="{ is_type: item.id == follow_id }" @click="onChangeType(item)">
              <view class="checked">
                <image v-show="item.id == follow_id" src="@/static/img/checked.png"></image>
              </view>
              {{ item.title }}
            </view>
          </view>
        </template>

        <view class="label pd26 follow_label flex-row">
          <text class="labelname" :class="{ actives: follow_type == 1 }" @click="follow_type = 1">跟进备注</text>
          <!-- <view :class="{ labelname_shu: follow_type == 1 }"></view> -->
          <!-- <template v-if="website_id == 176 || website_id < 8"> -->
          <text class="labelname" :class="{ actives: follow_type == 2 }" @click="follow_type = 2">语音跟进</text>
          <!-- <view :class="{ labelname_shua: follow_type == 2 }"></view> -->
          <!-- </template> -->
        </view>
        <!-- @好友-start -->
        <view v-if="showAtSelect" class="at-select-box">
          <view class="at-select-users">
            <view @click="hideAtUser" class="close-at-select">关闭</view>
            <scroll-view scroll-y="true" class="at-select-list">
              <view @click="insertAtUser(item)" class="at-select-item" v-for="(item, index) in atUsers" :key="index">
                <!-- <image :src="item.avatar" mode="widthFix"></image> -->
                <text>{{ item.nickname + (item.remark ? '(' + item.remark + ')' : '') }}</text>
              </view>
              <view v-if="!atUsers.length" class="fastim-data-none">没有更多了...</view>
            </scroll-view>
          </view>
        </view>
        <!-- mask -->
        <view v-if="maskShow" @click="maskClick" :style="maskStyle" class="mask"></view>
        <!-- @好友-end -->
        <view class="write-textarea" v-if="follow_type == 1">
          <!-- <textarea :show-confirm-bar="false" :focus="imMessageFocusBool" :cursor="imMessageFocusCursor"
            :cursor-spacing="14" maxlength="-1" @blur="imMessageBlur" @input="imMessageInput" @focus="imMessageFocus"
            @confirm="sendButtonConfirm" v-model="remark" placeholder="请输入跟进内容（企业内公开)"
            class="im-message textarea uni-textarea" ref="gain"
            :class="messageContenteditable ? 'disabled' : ''"></textarea> -->
          <view class='top-card-contenter'>
            <textarea fixed='true' contenteditable="true" auto-height="true" :show-confirm-bar="false"
              :focus="imMessageFocusBool" :cursor="imMessageFocusCursor" :cursor-spacing="14" maxlength="-1"
              @blur="imMessageBlur" @input="imMessageInput" @focus="imMessageFocus" @confirm="sendButtonConfirm"
              v-model="remark" placeholder="请输入跟进内容（企业内公开)" class="im-message" ref="gain"
              :class="messageContenteditable ? 'disabled' : ''"></textarea>
            <view style="text-align: right;padding-bottom: 24rpx;">{{ count || remark ? remark.length : 0 }} / {{ maxLength
            }}</view>
          </view>

          <view class="select_btn flex-row items-center">
            <view class="add_friend" @click="addAt"> @同事 </view>
            <view class="add_friend" :class="{ to_pic: uploadImgLength }" @click="toPic">
              <view class="title"> +图片 </view>
            </view>
          </view>
        </view>
        <!-- <textarea
          v-if="follow_type == 1"
          placeholder-class="placeholderClass"
          placeholder="请输入跟进内容（企业内公开）"
          class="textarea"
          v-model="remark"
        ></textarea> -->
        <template v-else>
          <view class="recordedbox">
            <recorder ref="recorder" @recorded="uploadVoice" :use_wx_voice="use_wx_voice"
              @recordering="is_recordering = true" @recordered="is_recordering = false" :recorder_max_time="30000"
              :hasVioce="params.url ? true : false">
              <view class="vioce_data flex-row items-center">
                <view class="voice_data_icon" :class="{ animation: is_recordering }">
                  <view class="voice_data_icon_top">
                    <myIcon type="luyin" size="70rpx" color="#fff"></myIcon>
                    <!-- <image
                    mode="widthFix"
                    :src="'/static/admin/customer/follow/<EMAIL>' | imageFilter('w_80')"
                  ></image> -->
                  </view>
                  <view class="voice_data_icon_bot">
                    {{ params.url ? '重新录制' : '长按录音' }}
                  </view>
                </view>
              </view>
            </recorder>
          </view>
          <!--语音记录 -->
          <view class="flex-row items-center voice_log" v-if="params.url">
            <view class="voice_name"> 语音记录 </view>
            <view class="voice_con flex-1 flex-row items-center" @click="playVoice(params.url)">
              <image class="play_vioce_icon" mode="widthFix" :src="(voice_playing
                ? '/static/icon/voice/play_voice.gif'
                : '/static/icon/voice/voice_icon.png') | imageFilter('w_80')
                "></image>
              <text>{{ parseInt(params.time / 1000) }}''</text>
              <!-- <view class="voice_con_left"> -->
              <!-- <image
                mode="widthFix"
                :src="'/static/admin/customer/follow/<EMAIL>' | imageFilter('w_80')"
              >
              </image> -->
              <!-- </view> -->
            </view>
            <view class="voice_oper" @click="removeVoice"> 删除 </view>
          </view>
        </template>
      </view>
    </view>
    <view class="text_msg flex-row items-center" v-if='!temp_disable'>
      <!-- <view class="uncheck" v-if="!selectTextMsg" @click="selectMsgType">
          </view> -->
      <view class="check" v-if="selectTextMsg" @click="selectMsgType">
        <image mode="widthFix" :src="'/static/admin/customer/follow/<EMAIL>' | imageFilter('w_80')">
        </image>
      </view>
      <view class="check_box" v-if="messageShow">
        <view class="check_con" @click="toSelectTextMsg"> 发送短信通知客户 </view>
        <view class="icon" @click="toSelectTextMsg">
          <view>
            请选择
          </view>
          <myIcon type="jinrujiantou" size="32rpx" color="#2E3C4E" style="margin-left: 10rpx"></myIcon>
        </view>
      </view>
    </view>

    <view class="text_msg flex-row items-center"  v-if="isOpenReport">
      <view class="check_box" @click="reportCustomer">
        <view class="check_con"> 报备客户 </view>
        <view class="icon">
          <view>
            报备跟进
          </view>
          <myIcon type="jinrujiantou" size="32rpx" color="#2E3C4E" style="margin-left: 10rpx"></myIcon>
        </view>
      </view>
    </view>

    <view v-if="isLoading" class="allbgc"> 
      <view class="overlay loader"></view>
     </view>
    <myButton v-if="!from" type="primary" :round="false" class="onbtn" @click="onCreateData">提交跟进</myButton>
    <view v-else class="foot row">
      <view class="c2" @click="$navigateBack()">取消</view>
      <view @click="onCreateData">提交跟进</view>
    </view>

    <myPopup ref="showPhone" :show="show_phone_pop" position="center" width="80%" @close="show_phone_pop = false">
      <view class="p_con" v-if='show_phone_pop'>
        <view class="title"> 拨打电话 </view>

        <view class="p_content">
          <view class="p_item flex-row items-center">
            <view class="label"> 选择外显号码 </view>
            <view class="value flex-1">
              <selectDown valueName="phone" :multiple="false" v-model="show_id" :localdata="phoneList"
                @change="changeSelect" defaultValue="" placeholder="请选择外显号码">
              </selectDown>
            </view>
          </view>
          <view class="btns flex-row items-center">
            <view class="btn flex-1" @click="debouncedMakePhone"> 确认拨打 </view>
          </view>
        </view>
      </view>
    </myPopup>
  </view>
</template>

<script>
import myIcon from "@/components/my-icon.vue";
import myButton from "@/components/myButton";
import recorder from '@/components/recorder.vue'
import myPopup from "@/components/outbound/myPopup";
import selectDown from '@/components/outbound/uni-data-select'
import { mapState } from 'vuex';
const innerAudioContext = uni.createInnerAudioContext();
let cursor = 0, defaultWriteHeight = 46, atUsersEd = [], _this = null
export default {
  components: {
    myIcon,
    myButton,
    recorder,
    myPopup,
    selectDown
  },
  data() {
    return {
      isLoading: false,
      buttonLabel: "",
      maxLength: 300,
      count: 0,
      value: ['0'],
      jietongstatus: true,
      last_call_statuslidt: [
        { id: 1, name: '已接通' },
        { id: 2, name: '未接通' },
        { id: 3, name: '未拨打' },
      ],
      follow_status_list: [],
      follow_id: 0,
      call_status: '1',
      remark: "",
      client_id: "",
      follow_type: 1,
      from: '',
      userInfo: { tel: '', name: "" },
      siteConfig: {},
      voice_status: "开始录音",
      selectTextMsg: false,
      is_recordering: false,
      voice_playing: false,
      params: {
        url: "",
        time: 0
      },
      showDropdown: false,
      userList: [],
      atFriendsList: [],
      website_id: 0,
      use_wx_voice: false,
      imMessageFocusBool: false,
      imMessageFocusCursor: 0,
      messageContenteditable: false,
      showAtSelect: false,
      maskShow: false,
      atUsers: [],
      imWriteHeight: 0,
      imageList: [],
      show_phone_pop: false,
      show_id: '',
      phoneList: [],
      call_info: {},
      show_type: '',
      telList: [],
      calls: 0,
      has_roles: 0,
      tel_log_id: "",
      tel: "",
      tel_log_idss: '',
      temp_disable: false,
      name: '',// 截取的头像名
      last_call_status: '',// 判断接通未接通
      messageShow: false,// 控制短信
      remind: '',//@用户存入id
      remindArray: [],
      tel_log_ider: '',// 若直接跳转强制跟进页面这个来接收tel_log_id来判断
      logs_list_time: '',
      logs_list_content: '',
      admin_userlist: {},
      logs_list: [],
      log_params: {
        page: 1,
        per_page: 10,
        client_id: "",
      }, // 线索
      call_open_crm: 2,
      mobile_place:'',
      isUsDeal: 0,  //是否我司成交
      submiting: false,
      isShowTelFullNumber: true,   //电话是否可见
      current: 'my',
      sys_source:'customer_list',
      customer_index:-1,
      //最新的一条跟进内容
      follow_lastest:{},
      lastClickTime: 0,
      debounceDelay: 1000, // 防抖时间设置为1000毫秒
    };
  },
  computed: {
    ...mapState(['crmConfig']),
    isOpenReport(){
      return this.crmConfig.is_open_report == 1 && this.tel;
    },
    isTrans(){
      return this.current === 'trans';
    },

    uploadImgLength(){
      return this.imageList.length
    }
  },
  //   // 接通未接通
  //   watch:{
  //     last_call_status:{
  //       handler(naval) {
  // this.last_call_status_id= naval
  //       console.log(naval,'this.last_call_status_id');

  //       },
  //       immediate: true,
  //     },
  //   },
  onLoad(option) {

    this.sys_source = option.sys_source || 'customer_list';
    this.customer_index = option.customer_index===0 ? 0 : (option.customer_index || -1);

    if(this.sys_source=='customer_list' && this.customer_index>=0){
      console.log("从列表中中添加跟进,操作的客户列表索引是 ",this.customer_index);
    }

    this.isUsDeal = option.is_us_deal == 1 ? 1: 0;   //是否我司成交
    // console.log(option.last_call_status,'last_call_status123456');
    // if(option.last_call_status){
    //   this.last_call_status = option.last_call_status
    // }
    _this = this
    if (option.website_id) {
      this.website_id = option.website_id
    }else{
      this.website_id = uni.getStorageSync('website_id')
    }
    // uni.$on("getCustomerInfo", (res) => {
    //   console.log(res);
    //   this.userInfo = res
    //   if (res.tel) {
    //     this.userInfo.tel = this.userInfo.tel.splice(2, 1, " ")
    //     this.userInfo.tel = this.userInfo.tel.splice(7, 1, " ")
    //   }
    // })
    uni.$on("selectedOk", res => {
      if (!this.selectTextMsg) {
        this.selectTextMsg = true
      }
      console.log(res, 1232);
      this.textMsgType = res
    })
    uni.$on("uploadOk", res => {
      console.log(res, "uploadOk");
      this.imageList = res
    })
    // var ua = window.navigator.userAgent.toLowerCase();
    // if (ua.match(/microMessenger/i) == 'micromessenger') {
    //   this.use_wx_voice = true
    // }
    if (option.from) {
      this.from = option.from
      console.log(option.from, '11111');
    }
    if (option.name) {
      // console.log(option.name,'option.name');
      this.userInfo.name = decodeURIComponent(option.name)
      // console.log(this.userInfo.name.split('')[0],'this.userInfo.name.split');
      this.name = this.userInfo.name.substring(0, 1)
    }
    // this.handle("https://yun.tfcs.cn/fenxiao/static/tribute/tribute.js")
    // console.log(option.logs_list_content,'线索内容');
    // if(option.logs_list_content){
    //   this.logs_list_content = option.logs_list_content
    // }
    // if(option.logs_list_time){
    //   this.logs_list_time = option.logs_list_time
    // }
    this.client_id = option.id;
    this.current = option.current || 'my'
    this.getFollowList();
    this.getUserList()
    this.getLogsData()

    if (option.tel) {
      this.tel = option.tel
      // this.userInfo.tel = option.tel
      this.telType = option.telType
      if (option.tel_log_id) {
        this.tel_log_id = option.tel_log_id
      }
      
      this.getTel()
      this.outboundCalls()
      this.searchAddress()
      
      // this.show_type = option.show_type
      // console.log(option.tel);
      // let tel = this.userInfo.tel
      // if (this.telType == 2 || this.telType == 3) {
      //   tel = (this.userInfo.tel + '').replace(/^(.{3}).*(.{3})$/, "$1*****$2")
      // }
      // tel = tel.split("")
      // tel.splice(3, 0, " ")
      // tel.splice(8, 0, " ")
      // this.tel = tel.join("")
      console.log(this.userInfo.tel);
    }
    if (option.tracking) {
      this.follow_id = option.tracking
    }
    if (option.has_roles > 0) {
      this.has_roles = option.has_roles
    }
    if (option.call_open_crm) {
      this.call_open_crm = option.call_open_crm
    }
    if(option.mobile_plac){
      this.mobile_place = option.mobile_plac
    }
    let send_template = uni.getStorageSync("send_template1") ? JSON.parse(uni.getStorageSync("send_template1")) : {}
    if (send_template && send_template.client_id) {
      if (this.client_id == send_template.client_id) {
        let sed_tem = new Date(send_template.date),
          now_tem = new Date()
        let oy = sed_tem.getFullYear(),
          om = sed_tem.getMonth(),
          od = sed_tem.getDate(),
          ny = now_tem.getFullYear(),
          nm = now_tem.getMonth(),
          nd = now_tem.getDate()
        if (oy = ny && od == nd && om == nm) {
          this.temp_disable = true
        }
      }
    }

    // 监听语音播放
    innerAudioContext.onPlay(() => {
      this.voice_playing = true
    })
    // 监听语音播放停止
    innerAudioContext.onStop(() => {
      this.voice_playing = false
    })
    // 监听语音自然播放结束事件
    innerAudioContext.onEnded(() => {
      this.voice_playing = false
    })
    // 监听语音播放失败事件
    innerAudioContext.onError(() => {
      if (this.wx && this.voiceLocalId) {
        this.localIdPlaying = true
        this.wx.playVoice({
          localId: this.voiceLocalId // 需要播放的音频的本地ID，由stopRecord接口获得
        });
        this.voice_playing = true
        this.wx.onVoicePlayEnd({
          success: (res) => {
            this.localIdPlaying = false
            this.voice_playing = false
          }
        });
      } else {
        uni.showToast({
          title: '播放失败，请重试',
          icon: 'none'
        })
        this.voice_playing = false
      }
    })
    this.textmessage()
    this.getInfo()

    
    
  },
  mounted() {

  },
  filters: {
    filterTel(val) {
      console.log(val, '/////');
      let tel = val
      if ((_this.telType == 1 || _this.telType == 0) || _this.has_roles) {
        tel = tel.split("")
        tel.splice(3, 0, " ")
        tel.splice(8, 0, " ")
        tel = tel.join("")
      } else {
        tel = (val + '').replace(/^(.{3}).*(.{3})$/, "$1*****$2")
      }
      return tel
      // console.log(tel,'显示隐号');
    },
    mobileFilter(val) {
      let reg = /^(.{3}).*(.{3})$/;
      if (val) {
        return val.replace(reg, "$1*****$2");
      }
    },
  },
  onUnload() {
    uni.$off("selectedOk")
    uni.$off("uploadOk")
    // uni.removeStorageSync("telInfo")
  },
  methods: {
    // 线索接口
    getLogsData() {
      this.log_params.client_id = this.client_id
      this.$ajax.get("/qywx/client_clue/search", this.log_params, (res) => {
        if (res.statusCode === 200) {
          this.logs_list = res.data.data;
          this.admin_userlist = this.logs_list[0]?.admin_user
          this.logs_list_content = this.logs_list[0]?.content || "暂无线索"// 线索内容
          this.logs_list_time = this.logs_list[0]?.created_at || "  " //线索时间
        }
      });
    },
    clientBtn() {
      uni.showLoading({
        title: '加载中'
      });
      let api = `/qywx/client/see_tel/${this.client_id}`
      if(this.isTrans){
        api = `/admin/private_client/see_tel/${this.client_id}`
      }
      this.$ajax.get(api, {}, (res) => {
        if (res.statusCode === 200) {
          console.log(res.data.tel_log_id, '000000');
          this.tel_log_idss = res.data.tel_log_id
          this.telList = res.data.tel
          //号码是否可见
          this.isShowTelFullNumber = res.data.type == 1 ? true : false;
        }else{
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
        uni.hideLoading();
      }, er=>{
        uni.hideLoading();
      })
    },
    verifyFollow(){
      this.$ajax.get('/admin/crm/client/verify_follow', {}, (res) => {
        if (res.statusCode === 200) {
          if (res.data && res.data.id > 0) {
            if(res.data.client_id == this.client_id){
              this.tel_log_idss = res.data.id
            }
          }
        }else{
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      })
    },
    // 归属地查询
    searchAddress() {
      let url = '/qywx/client/query_mobile_place/';
      if(this.isTrans){
        url = '/admin/private_client/query_mobile_place/';
      }
      this.$ajax.get(`${url}${this.client_id}`, {}, res => {
        if (res.statusCode == 200) {
          console.log(res, '000000000077777 77777 ');
          this.mobile_place = res.data
        }
      })
    },
    // 客户状态
    onChangeType(e) {
      this.follow_id = e.id;
    },
    // 接通状态
    onChangeTypes(e) {
      this.call_status = e.id
    },
    insertAtUser(item) {
      // this.remind = item.id
      this.remindArray.push(item.values)
      // console.log(this.remindArray,'this.remind.push(item.values).splice(',')');
      // 将@用户传入id给后端
      this.remind = this.remindArray.join(',')
      console.log(this.remind, 'this.remindthis.remind');
      item.nickname += '  '
      atUsersEd.push({
        id: item.id,
        nickname: item.nickname
      })
      this.hideAtUser()

      // 找到用户输入的搜索词，删除
      // 将当前@的用户全称输入至输入框
      let atSearchIdx = -1;
      let beforeCursor = this.remark.substr(0, cursor)
      console.log(beforeCursor, this.remark);
      for (let i = (beforeCursor.length - 1); i >= 0; i--) {
        if (beforeCursor[i] == ' ' && beforeCursor[i + 1] == ' ') {
          break;
        } else if (beforeCursor[i] == '@') {
          atSearchIdx = (i + 1)
        }
      }
      let delContent = beforeCursor.substr(atSearchIdx)
      this.remark = this.remark.substring(0, atSearchIdx) + item.nickname + this.remark.substring(atSearchIdx + delContent.length)

      // 聚焦到指定字符后
      this.imMessageFocusCursor = (cursor - delContent.length) + item.nickname.length

      setTimeout(() => {
        this.imMessageFocusBool = true
      }, 100)
    },
    //获取需要强制跟进的记录信息
    getNeedForceFollowLog(){
      return new Promise(resolve => {
        this.$ajax.get('/qywx/client/verify_follow', {}, res => {
            if (res.statusCode == 200) {
              resolve(res.data);
            }
          })
      })
      
    },
    async getTel() {
      this.clientBtn();
      this.verifyFollow();
      return;
      let telInfo = uni.getStorageSync("telInfo") ? JSON.parse(uni.getStorageSync("telInfo")) : {

      }
      this.telList = telInfo.tel || []

      console.log(this.tel_log_idss, 'this.tel_log_idss');
      if (telInfo.tel_log_id == '') {
        console.log("强制跟进");
        this.clientBtn()
      } else {
        this.tel_log_idss = telInfo.tel_log_id
      }
      // let api = `/admin/crm/client/see_tel/${this.client_id}`
      // this.$ajax.get(api, {}, (res) => {
      //   if (res.statusCode === 200) {
      //     this.telList = res.data.tel
      //     // this.show_type = res.data.type
      //   }
      // })
    },
    addAt() {
      this.imMessageFocusBool = true
      this.remark += '@ '
      // this.count = this.content.length;
      this.imMessageInput({
        detail: { value: this.remark }
      })
      var that = this
      that.showAtSelect = true
      that.maskShow = true
      that.maskStyle = 'background:rgba(0, 0, 0, 0.1)';
      // setTimeout(() => {

      // }, 100)
    },
    maskClick() {
      this.showAtSelect = false
      this.maskShow = false
    },
    imMessageBlur: function () {
      this.imMessageFocusBool = false
      if (!this.showTool) {
        this.writeBottom = 0;
        this.writeHeight = defaultWriteHeight;
      }
      // this.inputStatus(false)
    },
    imMessageFocus(e) {
      // this.clickTool(false)

      let writeHeight = () => {
        this.writeBottom = e.detail.height || 0
        this.writeHeight = (parseInt(this.writeBottom) + defaultWriteHeight);
      }
      let userPlatform = uni.getSystemInfoSync.platform
      if (userPlatform == 'ios') {
        // #ifdef APP-PLUS
        // uni.onKeyboardHeightChange(res => {
        // 	this.writeBottom = res.height || e.detail.height || 0
        // 	this.writeHeight = (parseInt(this.writeBottom) + defaultWriteHeight);
        // 	uni.offKeyboardHeightChange(() => {})
        // })
        // #endif

        // #ifndef APP-PLUS
        writeHeight()
        // #endif
      } else {
        writeHeight()
      }

      // this.scrollIntoFooter(0, 99993)
    },
    imMessageInput: function (e) {
      console.log(e, '99999');
      cursor = e.detail.cursor || e.detail.value.length

      // 从光标位置向前搜索@和空格符号
      let atSearchIdx = -1;
      let beforeCursor = e.detail.value.substr(0, cursor)
      for (let i = (beforeCursor.length - 1); i >= 0; i--) {
        if (beforeCursor[i] == ' ' && beforeCursor[i + 1] == ' ') {
          break;
        } else if (beforeCursor[i] == '@') {
          atSearchIdx = i
        }
      }
      if (atSearchIdx !== -1) {
        this.atUser(beforeCursor.substr(atSearchIdx + 1));
      } else if (this.showAtSelect) {
        this.hideAtUser()
      }

      this.remark = e.detail.value;
      // this.imMessageChange()
      // this.inputStatus()
    },
    atUser(keywords = '') {
      var that = this
      that.showAtSelect = true
      that.maskShow = true
      that.maskStyle = 'background:rgba(0, 0, 0, 0.1)';

      // 获得输入框高度
      let imWrite = uni.createSelectorQuery().select('.im-message');
      imWrite.fields({
        size: true
      }, data => {
        that.imWriteHeight = data.height
      }).exec()


    },
    hideAtUser: function () {
      this.maskShow = false
      this.maskStyle = ''
      this.showAtSelect = false
    },
    changeSelect(e) {
      console.log(e);
    },
    getUserList() {
      this.$ajax.get("/admin/crm/client_follow/userListDepartment", {}, res => {
        //console.log(res.data, '@用户111');
        if (res.statusCode == 200) {
          res.data.map(item => {
            item.id = item.values
            item.nickname = item.name
            //console.log(item, '@用户222');
            return item
          })
          this.atUsers = res.data
        }
      })
    },
    // handle (statistics, id = "tribute") {
    //   console.log(statistics);
    //   // console.log("执行统计")
    //   // return
    //   //每次执行前，先移除上次插入的代码

    //   document.getElementById(id) && document.getElementById(id).remove();
    //   var hm = document.createElement("script");
    //   hm.src = statistics;
    //   hm.id = id
    //   var s = document.getElementsByTagName("script")[0];
    //   s.parentNode.insertBefore(hm, s);
    // },
    getInfo() {
      this.$ajax.get(`/common/website/query/${this.website_id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.siteConfig = res.data || {}
        }
      })
    },

    getFollowList() {
      this.$ajax.get("/qywx/tracking/list", { type: 2 }, (res) => {
        if (res.statusCode === 200) {
          this.follow_status_list = res.data;
          if (this.follow_id == 0) {
            this.follow_id = this.follow_status_list.length ? this.follow_status_list[0].id : 0
          }
          // this.follow_id = this.follow_status_list.length ? this.follow_status_list[0].id : ''
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    getphoneList(callback) {
      this.$ajax.get('/admin/call_clue/getSeatsPhone', {}, res => {
        if (res.statusCode == 200) {
          this.phoneList = res.data.map(item => {
            item.value = item.show_id
            item.text = item.phone
            return item
          })
          callback && callback()
        }else{
          uni.showToast({
            title: res.data.message,
            icon: "none",
            duration: 2000
          })
        }
      })
    },
    postData(data) {
      let params = {
        call_phone_id: data.call_id,
        call_name: this.userInfo.name,
        call_phone: data.callee,
        call_show_phone: data.caller,
        type: this.isTrans ? 3 : 1,
        client_id: this.client_id
      }
      this.$ajax.post("/common/call_module/addCallOutRecord", params, res => {
        console.log(res);
      })
    },
    conPhone(item) {
      console.log(item, '点击拨打直接跳转手机拨打页面');
      uni.makePhoneCall({
        phoneNumber: item,
      });
    },
    debouncedMakePhone(item) {
      // console.log(item);
      const currentTime = Date.now();
      if (currentTime - this.lastClickTime > this.debounceDelay) {
        this.lastClickTime = currentTime;
        this.conMakePhone(item);
      }
    },
    conMakePhone(item) {
      if (!this.show_id) {
        uni.showToast({
          title: "请选择外显号码",
          icon: "none"
        })
        return
      }
      let params = {
        show_id: this.show_id,
        phone: (this.userInfo.tel + '').replace(" ", '').replace(" ", '')
      }

      let api = `/admin/call_clue/directCallPhone`
      if (this.telType == 3) {
        api = '/admin/call_clue/anyOneCallByCrm'
        params = {
          show_id: this.show_id,
          phone: (this.userInfo.tel + '').replace(" ", '').replace(" ", ''),
          client_id: this.client_id
        }
      }

      this.$ajax.post(api, params, res => {
        if (res.statusCode == 200) {
          console.log(res, '000099999');
          this.call_info = res.data
          if (res.data.call_id) {
            this.postData(res.data)
          }
          // uni.showToast({
          //   title: '正在跳转拨号页面 请稍后',
          //   icon: "none",
          //   duration: 2000
          // })
          uni.makePhoneCall({
            phoneNumber: res.data.telX,
            success: () => {
              console.log("正在拨打中 请稍后");
            },
          });
          this.show_phone_pop = false
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
            duration: 2000
          })
        }
      })
    },
    copyPhone(phone) {
      // if (this.show_type == 2) return
      if ((this.telType != 2 && this.telType != 3) || this.has_roles > 0) {
        this.$copyText(phone, () => {
          uni.showToast({
            title: '复制成功',
            icon: "none",
          });
        })
      }
    },
    outboundCalls() {
      let url = `/qywx/client/verify_call/${this.client_id}`;
      if(this.isTrans){
        url = '/admin/private_client/call/verify/'
      }
      this.$ajax.get(url, {}, (res) => {
        if (res.statusCode == 200) {
          this.calls = res.data
          console.log(res, '判断显示外呼和电话');
        }else{
          uni.showToast({
            title: res?.data?.message || '获取客户信息失败',
            icon: "none"
          })
        }
      })
    },
    makePhoneCall(phone) {
      console.log(phone, '1111');
      this.userInfo.tel = phone
      if (this.telType > 0) {
        if (!this.phoneList.length) {
          this.getphoneList(() => {
            this.show_phone_pop = true
            this.show_id = this.phoneList.length ? this.phoneList[0].show_id : ""
          })
        } else {
          this.show_phone_pop = true
          this.show_id = this.phoneList.length ? this.phoneList[0].show_id : ""
        }
        return
      }
      uni.makePhoneCall({
        phoneNumber: (this.userInfo.tel + '').replace(" ", '')
      })
    },
    toSelectTextMsg() {
      this.$navigateTo("/customer/textMsgs")
      setTimeout(() => {
        uni.$emit("giveUp", this.textMsgType)
      }, 200);
    },
    // 客户详情提醒跟进中短信提醒是否显示
    textmessage() {
      this.$ajax.get("/qywx/client/verify_tel_follow", {}, res => {
        if (res.statusCode == 200) {
          console.log(res, '00998822');
          if (res.data == 1) {
            this.messageShow = true
          } else {
            this.messageShow = false
          }
        }
      })
    },
    // 折叠面板
    change(e) {
      console.log(e);
    },
    onCreateData() {
      let form = {
        client_id: this.client_id,
      };
      console.log(this.client_id, 'this.client_id');
      // content: this.remark,
      if (!this.follow_id) {
        uni.showToast({
          title: "请选择跟进类型",
          icon: "none",
        });
      }
      // // console.log(form.tel_log_id,'00000');
      // if(uni.getStorageSync('telInfo')) {
      //   console.log(2222);
      //   let telInfo =  JSON.parse(uni.getStorageSync('telInfo'))
      // }
      // let telInfo =  JSON.parse(uni.getStorageSync('telInfo'))
      // 电话普通跟进
      let url = "/qywx/client/follow/create"
      if(this.isTrans){
        url = '/admin/private_client_follow/create'
      }

      if (this.tel) {
        console.log(111111999999);
        // 电话强制跟进
        url = '/qywx/client_follow/tel_follow'
        if(this.isTrans){
          url = '/admin/private_client_follow/tel_follow'
        }
        // 客户状态id
        form.tracking_id = this.follow_id
        // 通话状态
        form.call_status = this.call_status
        form.remind = this.remind

        // form.type = 1
        // this.getTel()
        // console.log(this.tel_log_idss, '000009988s');
        // 强制跟进Id
        if (this.tel_log_idss != 0) {
          form.tel_log_id = this.tel_log_idss
        }
        // follow_type == 2 语音跟进
        if (this.follow_type == 2) {
          // url = "/admin/crm/client_follow/yy_create"
          // console.log(1111111)
          form.url = this.params.url
          form.time = this.params.time
          console.log(form.time, 'form.time录音333');
        } else {
          form.content = this.remark
        }
        if (this.call_info && this.call_info.call_id) {
          form.call_phone_id = this.call_info.call_id
          form.call_name = this.userInfo.name
          form.call_phone = this.call_info.callee
          form.call_show_phone = this.call_info.caller
        }
      } else {
        form.tracking_id = this.follow_id
        form.remind = this.remind
        console.log(6666666);
        // follow_type == 2 语音跟进
        if (this.follow_type == 2) {
          // url = "/admin/crm/client_follow/yy_create"
          form.url = this.params.url
          form.time = this.params.time
          console.log(form.time, 'form.time录音333');
          console.log(7777777);
        } else {
          form.content = this.remark
        }
        form.type = this.follow_id
      }
      if (this.follow_type == 1 && form.content.length < 5) {
        uni.showToast({
          title: '跟进内容最少5个汉字',
          icon: "none"
        })
        return
      }
      form.file_path = this.imageList && this.imageList.length ? this.imageList.join(",") : ''
      if(this.submiting){
        return;
      }
      this.submiting = true;
      console.log(url,'--------------------------------');
      if (process.env.NODE_ENV === 'development') {
          this.submiting = false;
          this.buttonLabel = "点击加载";
          uni.showToast({
              title: '开发模式下模拟操作跟进成功',
              icon: 'none',
          });

          setTimeout(()=>{
            this.$navigateBack();
          },1000);
          
          if(this.sys_source == 'customer_detail'){
            console.log("从客户详情添加跟进，返回刷新数据");
            setTimeout(() => {
              uni.$emit("getDataAgain");
            }, 200);
          }else{
            let return_data = {
                  "customer_index" : this.customer_index,
                  "admin_id": 395,
                  "client_id": 2129,
                  "content": form.content,
                  "id": 36846,
                  "type": 0,
                  "type_title": "",
                  "admin_info": {
                      "id": 395,
                      "user_name": "王健",
                      "wx_work_department_id": "39,252"
                  }
              };
            uni.$emit('upCustomerFollowContent',return_data);
            console.log("从客户列表添加跟进 返回不刷新数据");
          }
          return;
      }

      try{
      this.$ajax.post(url, form, (res) => {
        this.submiting = false;
        console.log('-------res-------');
        console.log(res);
        if (res.statusCode === 200) {
          this.follow_lastest = res.data;
          this.follow_lastest.customer_index = this.customer_index;
          console.log(55555);
          this.isLoading = true;
      this.buttonLabel = "";

      setTimeout(() => {
        // 在这里执行你的异步操作
        if (this.selectTextMsg) {
            // console.log('强制跟进返回详情页面1111');
            uni.showToast({
              title: '跟进成功',
              icon: "none"
            })
            this.postMsgRemind()
          } else {
            // console.log('强制跟进返回详情页面2222');
            //this.$navigateTo(`/customer/detail?id=${this.client_id}`)
            uni.showToast({
              title: '跟进成功',
              icon: "none"
            })
            this.$store.commit('setNeedRefreshWhenShow', true);
            this.$navigateBack();
            if(this.sys_source == 'customer_detail'){
              setTimeout(() => {
                uni.$emit("getDataAgain")
              }, 200);
            }else{
              uni.$emit('upCustomerFollowContent',this.follow_lastest);
              console.log("从客户列表添加跟进 返回不刷新数据");
            }
            /* setTimeout(() => {
              uni.$emit("getDataAgain")
            }, 200); */
          }
        // 操作完成后，隐藏加载器
        this.isLoading = false;
        this.buttonLabel = "点击加载";
      }, 2000); // 这里使用setTimeout模拟异

        } else {
          // console.log('强制跟进返回详情页面3333');

          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
      }catch(e){
        this.submiting = false;
      }
    },
    selectMsgType() {
      this.selectTextMsg = !this.selectTextMsg
      if (this.selectTextMsg && !this.textMsgType) {
        this.toSelectTextMsg()
      }
    },
    postMsgRemind() {
      let params = {}
      params.name = this.textMsgType.name
      params.mobile = this.textMsgType.mobile
      params.client_id = this.client_id
      params.type = this.textMsgType.id
      let url = "/admin/crm/client/sms_remind";
      if(this.isTrans){
        url = "/admin/private_client/sms_remind";
      }
      this.$ajax.post(url, params, res => {
        console.log(res);
        if (res.statusCode == 200) {
          uni.setStorageSync("send_template1", JSON.stringify({ date: +new Date(), client_id: this.client_id }))
          this.$navigateBack();
          if(this.sys_source == 'customer_detail'){
            setTimeout(() => {
              uni.$emit("getDataAgain")
            }, 200);
          }else{
            uni.$emit('upCustomerFollowContent',this.follow_lastest);
            console.log("从客户列表添加跟进 返回不刷新数据");
          }
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none"
          })
          setTimeout(() => {
            this.$navigateBack();
            if(this.sys_source == 'customer_detail'){
              setTimeout(() => {
                uni.$emit("getDataAgain")
              }, 200);
            }else{
              uni.$emit('upCustomerFollowContent',this.follow_lastest);
              console.log("从客户列表添加跟进 返回不刷新数据");
            }
          }, 1000);

        }
      })
    },
    removeVoice() {
      this.params.url = ''
      this.params.time = 0
    },
    // 上传录音文件并发送
    uploadVoice(e) {
      console.log("录音555");
      if (e.duration < 1000) {
        uni.showToast({
          title: "录制时间太短",
          icon: 'none'
        })
        // recorder.destroy()
        return
      }

      if (this.use_wx_voice) {
        this.wx = e.wx
        this.voiceLocalId = e.localId
        this.wx.uploadVoice({
          localId: e.localId, // 需要上传的音频的本地ID，由stopRecord接口获得
          isShowProgressTips: 1, // 默认为1，显示进度提示
          success: (res) => {
            var serverId = res.serverId; // 返回音频的服务器端ID
            console.log(serverId)
            uploadServerId(serverId, e.duration)
            // this.sendMessage({voice: serverId, duration}, 'voice')
          }
        });
        // 将serverId传递给后台，后台进行语音的下载和转码
        var uploadServerId = (serverId, duration) => {
          uni.showLoading({
            title: "正在上传"
          })
          this.$ajax.get('/common/file/upload/admin?category=104', { media_id: serverId, duration: Math.ceil(duration / 1000) }, res => {
            uni.hideLoading()
            console.log(res.data, '录音444444');
            if (res.data.code === 1) {
              console.log(res.data.url)
              this.voiceUrl = res.data.url
              this.voice_duration = duration
              this.params.url = res.data.url
              this.params.time = e.duration
              console.log(this.params.time, 'this.params.time录音22');
            } else {
              uni.showToast({
                title: res.data.msg,
                icon: 'none'
              })
            }
          }, err => {
            uni.hideLoading()
          })
        }
        return
      }
      uni.showLoading({
        title: "正在上传"
      })
      console.log(e);
      // 获取上传后的录音文件路径，然后执行发送消息
      this.$uploadFile('/common/file/upload/admin?category=104', e.file, { duration: Math.ceil(e.duration / 1000) }, (res) => {
        uni.hideLoading()
        let result = res.data
        console.log(res);
        // // #ifdef MP-BAIDU
        // result = res.data
        // // #endif
        // // #ifndef MP-BAIDU
        // result = JSON.parse(res.data)
        // // #endif
        if (!result.url) {
          uni.showToast({
            title: result.msg || "上传失败",
            icon: "none"
          })
          return
        }
        this.voiceUrl = result.url
        this.voice_duration = e.duration
        this.params.url = result.url
        this.params.time = e.duration
        console.log(this.params.time, 'e.duration录音111');
      }, err => {
        uni.hideLoading()
        console.log(err, '上传录音文件失败');
        // recorder.destroy()
      })
    },
    // 播放语音
    playVoice(source = this.params.url) {
      console.log(source);
      // source = 'https://imvc.tengfangyun.com/tengfangyun/20230626/05f05a5f7fb72e8d104e22d1677f016fb9118b07.wav'
      if (this.voice_playing) {
        innerAudioContext.stop()
        return
      }
      this.voice_playing = true
      console.log(innerAudioContext);
      innerAudioContext.src = source
      innerAudioContext.play()
    },
    toPic() {
      if (this.imageList && this.imageList.length) {
        uni.setStorageSync("uploadImg", JSON.stringify(this.imageList || []))
      }

      this.$navigateTo("/customer/pics")
    },
    //报备客户
    reportCustomer(){
      let cname = this.userInfo.name || '';
      let tel = encodeURIComponent(this.telList.join(','));
      this.$navigateTo(`/report/add?client_id=${this.client_id}&cname=${cname}&tel=${tel}`);
    }
  },
};
</script>

<style scoped lang="scss">
// 按钮自定义加载效果

.button-container {
  position: relative;
}

.custom-button {
  background-color: #007bff;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  z-index: 1;
}

.overlay {
  position: fixed;
  top: 50%;
  left: 42%;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.loader {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #007bff;
  border-radius: 50%;
  width: 110rpx;
  height: 110rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.allbgc{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}
.user_type {
  padding: 12rpx 24rpx;
  background: #12D367;
  border-radius: 10rpx;
  color: #fff;
  font-size: 24rpx;
  // width: 50%;
}

.user_phone {
  width: 50%;
  color: #292C39;
  font-size: 36rpx;
  // margin-bottom: 40rpx;
}

.top-card-contenter {
  border-radius: 8rpx;
  border: 2rpx solid #F0F1F5;
  background: #F8F8F8;
  padding: 24rpx 24rpx 0rpx 24rpx;
  margin-top: 24rpx;
  // align-items: center;
  .im-message{
    min-height: 150rpx;
  }
}

.phone_one {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-top: 8rpx;
  padding-bottom: 40rpx;
}

::v-deep .uni-collapse-item__title-text {
  margin-left: 70rpx;
}

.content {
  padding: 30rpx;
  background: #F6F6F6;

}

.text {
  font-size: 28rpx;
  color: #666;
  line-height: 40rpx;
}

.recordedbox {
  width: 100%;
  height: 440rpx;
  background: #F8F8F8;
  margin-top: 16rpx;

}

.labelname_shu {
  position: absolute;
  top: 333px;
  left: 50px;
  background: #2d84fb;
  width: 64rpx;
  height: 6rpx;
  border-radius: 99px;
}

.labelname_shua {
  position: absolute;
  top: 333px;
  left: 150px;
  background: #2d84fb;
  width: 64rpx;
  height: 6rpx;
  border-radius: 99px;
}

.labelname {
  color: rgba(41, 44, 57, 0.70);
  font-size: 36rpx;
  margin-right: 48rpx;
}

.actives {
  position: relative;
  color: #292C39;
  font-size: 36rpx;
  font-weight: 500;
}

.check_box {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 24rpx;
  // margin: 0 32rpx;
  background: #fff;
  width: 100%;
  // border-radius: 16rpx;
  
}

.username{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 22rpx 32rpx;
}
.phone_bottom {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: #2d84fb;
  color: #fff;
  text-align: center;
  line-height: 56rpx;
  margin-right: 16rpx;
}

page {
  background: #F6F6F6;
}

.items-center {
  align-items: center;
}

.form-box {
  width: 100%;
  padding: 32rpx;
}

.need {
  // margin-top: 12px;
  padding-bottom: 200rpx;
  &-box {
    // margin: 0 18px;
    // padding: 14px;
    // border-radius: 5px;
    background: #fff;
    align-items: center;
    justify-content: space-between;
  }
}

::v-deep .uni-textarea {
  width: 100%;
  // z-index: 999;
}

.textarea {
  margin-top: 8px;
  padding: 6px 16px;
  // background: #F8F8F8;
  // border: 1px solid #e8e8e8;
  // // height: 153px;
}

.placeholderClass {
  font-size: 13px;
  color: #bcbcbc;
}

.onbtn {
  position: fixed;
  bottom: 50rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 654rpx;
  
  &.base {
    height: 80rpx;
  }
  ::v-deep .my-btn.base{
    height: 80rpx;
  }
}

.uer_name_top_new {
  padding: 32rpx;
}

.phone-box {
  // padding: 50rpx 0;

  .user_name {
    width: 10rpx;
    margin-top: 10rpx;
    font-size: 28rpx;
    color: #2e3c4e;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    z-index: 9999;
  }

  .phone_info_c {
    align-items: center;
    margin-bottom: 32rpx;
  }

  .user_phone {
    width: 50%;
    color: #292C39;
    font-size: 36rpx;
  }

  .user_type {
    padding: 12rpx 24rpx;
    background: #12D367;
    border-radius: 10rpx;
    color: #fff;
    font-size: 24rpx;
    width: 50%;
    // margin-left: 280rpx;
  }
}

.type-items {
  // margin-bottom: 24rpx;
  // margin-left: 10px;
  // width: 200rpx;
  width: 295rpx;
  padding: 24rpx;
  background: #ffffff;
  border-radius: 8rpx;
  // margin-right: 24rpx;
  color: #8d9099;
  border: 2rpx solid #dedede;
  // background: #f7f7f7;
  text-align: center;
  position: relative;
  overflow: hidden;

  &:nth-child(3n) {
    margin-right: 0;
  }

  &.is_type {
    border: 2rpx solid rgba(45, 132, 251, 1);
    color: #2d84fb;
    background: #fff;
  }

  .checked {
    position: absolute;
    right: 0;
    top: 0;
    width: 32rpx;
    height: 24rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }
}

.form-box {
  .type-box {
    flex: 1;
    flex-wrap: wrap;
    // margin-top: 20px;
    justify-content: space-between;

    .type-item {
      // margin-bottom: 24rpx;
      // margin-left: 10px;
      // width: 200rpx;
      width: 32%;
      padding: 24rpx;
      background: #ffffff;
      border-radius: 8rpx;
      // margin-right: 24rpx;
      color: #8d9099;
      border: 2rpx solid #dedede;
      // background: #f7f7f7;
      text-align: center;
      position: relative;
      overflow: hidden;

      &:nth-child(3n) {
        margin-right: 0;
      }

      &.is_type {
        border: 2rpx solid rgba(45, 132, 251, 1);
        color: #2d84fb;
        background: #fff;
      }

      .checked {
        position: absolute;
        right: 0;
        top: 0;
        width: 32rpx;
        height: 24rpx;

        image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  .mar-tb {
    margin: 24rpx 0 48rpx;
  }

  .mar-t {
    margin: 24rpx 0 0;
  }
}

.foot {
  position: fixed;
  bottom: 0;
  width: 100%;
  background: #fff;
  padding: 12px 12px 40px 12px;
  justify-content: space-between;

  view {
    width: 48%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 6px;
    border: 1px solid #2d84fb;
    color: #fff;
    background: #2d84fb;
    font-weight: 500;

    &.c2 {
      border: 1px solid #dde1e9;
      background: #fff;
      color: #8a929f;
    }
  }
}

@keyframes rotate {
  0% {
    box-shadow: 0 0 1rpx 32rpx rgba($color: #2d84fb, $alpha: 0.06);
  }

  50% {
    box-shadow: 0 0 1rpx 84rpx rgba($color: #2d84fb, $alpha: 0.15);
  }

  100% {
    box-shadow: 0 0 1rpx 136rpx rgba($color: #2d84fb, $alpha: 0.3);
  }
}

.vioce_data {
  user-select: none;
  justify-content: center;
  margin: 0 0 86rpx;

  .voice_data_icon {
    width: 240rpx;
    height: 240rpx;
    border-radius: 50%;
    text-align: center;
    justify-content: center;
    background: #2d84fb;
    margin-top: 90rpx;

    &.animation {
      animation: rotate 1.2s linear infinite;
    }

    .voice_data_icon_top {
      width: 72rpx;
      height: 72rpx;
      margin: 0 auto 10rpx;

      image {
        width: 100%;
      }
    }

    .voice_data_icon_bot {
      font-size: 24rpx;
      color: #fff;
    }
  }
}

.voice_log {
  .voice_name {
    color: #2e3c4e;
    font-size: 32rpx;
  }

  .voice_con {
    flex: 1;
    background: #2d84fb;
    padding: 10rpx 20rpx;
    border-radius: 8rpx;
    margin: 10rpx 20rpx;

    image {
      width: 40rpx;
      height: 40rpx;
    }

    text {
      color: #fff;
      margin-left: 10rpx;
    }
  }

  .voice_oper {
    color: #f63131;
    font-size: 32rpx;
  }
}

.follow_label {
  margin: 64rpx 0 24rpx;

  .label_name {
    position: relative;

    ~.label_name {
      margin-left: 36rpx;
    }

    &.active {
      :after {
        content: '';
        position: absolute;
        bottom: -20rpx;
        left: 0;
        right: 0;
        height: 4rpx;
        border-radius: 1rpx;
        background: #2d84fb;
      }
    }
  }
}

.text_msg {
  margin-top: 50rpx;
  justify-content: flex-end;

  .uncheck {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    border: 2rpx solid #8a929f;
  }

  .check {
    width: 40rpx;
    height: 40rpx;
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .check_con {
    // margin-left: 20rpx;
    color: rgba(41, 44, 57, 0.70);
    font-size: 32rpx;
  }

  .icon {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 20rpx;

    font-size: 32rpx;
    color: rgba(41, 44, 57, 0.70);
  }
}

.at-select-box {
  // position: relative;
}

.at-select-users {
  position: absolute;
  background: #ffffff;
  width: 100vw;
  left: 0;
  bottom: 0;
  min-height: 300rpx;
  max-height: 70vh;
  overflow-y: auto;
  box-sizing: border-box;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  z-index: 9992;
}

.close-at-select {
  font-size: 26rpx;
  float: right;
  color: #999999;
  padding: 20rpx;
}

.at-select-list {
  clear: both;
  max-height: 700rpx;
}

.fastim-data-none {
  text-align: center;
  font-size: 30rpx;
  line-height: 80rpx;
  height: 80rpx;
  color: #999999;
}

.at-select-item {
  display: flex;
  align-items: center;
  height: 90rpx;
  padding-left: 20rpx;
}

.at-select-item image {
  height: 60rpx;
  width: 60rpx;
  border-radius: 16rpx;
}

.at-select-item text {
  padding-left: 10rpx;
}

.mask {
  z-index: 9990;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,.5);
}

::v-deep .uni-collapse-item__title-box {
  background: #F6F6F6;
  position: relative;
}

::v-deep .uni-collapse-item__title.uni-collapse-item-border {
  background: #F6F6F6;
}

.select_btn {
  margin-top: 40rpx;

  .add_friend {
    padding: 16rpx 32rpx;
    background: #F8F8F8;
    color: #2e3c4e;
    border-radius: 8rpx;

    ~.add_friend {
      margin-left: 20rpx;
    }

    &.to_pic {
      position: relative;

      &:after {
        content: '';
        width: 8rpx;
        height: 8rpx;
        position: absolute;
        top: 6rpx;
        right: 6rpx;
        background: #f63131;
        border-radius: 50%;
      }
    }
  }
}

.labelnew {
  color: rgba(41, 44, 57, 0.70);
  font-size: 32rpx;
  margin-top: 24rpx;
  margin-bottom: 32rpx;
}

.p_con {
  padding: 40rpx;
  background: #fff;
  border-radius: 8rpx;
  position: relative;
  padding-top: 140rpx;
  width: 80vw;

  /* height: 50vh; */
  .title {
    font-size: 40rpx;
    font-weight: 600;
    padding: 40rpx 0;
    text-align: center;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    text-align: center;
  }

  .p_content {
    .p_item {
      .label {
        margin-right: 5rpx;
      }
    }

    .btns {
      margin-top: 200rpx;

      .btn {
        background: #2d84fb;
        padding: 30rpx 0;
        color: #fff;
        border-radius: 10rpx;
      }
    }
  }
}</style>
