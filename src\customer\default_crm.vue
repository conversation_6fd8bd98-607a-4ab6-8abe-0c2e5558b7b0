<template>
  <view class="default" v-if="showContent">
    <view class="top-card" v-if="wx_work_userid">
      <view class="top-card-title row">客户详情 </view>
      <view class="top-card-content row">
        <view class="top-card-content-label">客户姓名</view>
        <view class="top-card-content-right">{{ wx_info.name }}</view>
      </view>
      <view class="top-card-content row">
        <view class="top-card-content-label">客户性别</view>
        <view class="top-card-content-right">{{
          wx_info.gender == 1 ? '男' : wx_info.sex == 2 ? '女' : '--'
        }}</view>
      </view>
      <view class="top-card-content row">
        <view class="top-card-content-label">客户来源</view>
        <view class="top-card-content-right">{{
          wx_info.type == 1 ? '微信用户' : '企业微信用户'
        }}</view>
      </view>
      <view class="top-card-content row">
        <view class="top-card-content-label">添加时间</view>
        <view class="top-card-content-right">{{ wx_info.add_time }}</view>
      </view>
      <view class="top-card-content row">
        <view class="top-card-content-label">添加方式</view>
        <view class="top-card-content-right">{{ wx_info.add_way_title }}</view>
      </view>
    </view>

    <view class="desc">录入客户</view>
    <view class="default-box">
      <view class="content-box">
        <view class="title">安全保密，仅自己可见</view>
        <view @click="clickUphold" class="btn blue">立即录入</view>
      </view>
      <image
        class="back"
        src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/crm/index/ljlr.png"
      ></image>
    </view>
    <view class="desc">查询客户</view>
    <view class="default-box">
      <view class="content-box">
        <view class="title">客户信息快速查询</view>
        <view class="i-box row">
          <input
            v-model="phone"
            type="text"
            placeholder="请输入手机号查询"
            class="input"
            maxlength="11"
          />
          <view @click="clickSearch" class="btn blue">立即查询</view>
        </view>
      </view>
    </view>
    <view class="desc">关联CRM</view>
    <view class="default-box">
      <view class="content-box">
        <view class="title">与CRM中的客户资料快速关联</view>
        <view class="btn green" @click="onClickBind">立即关联</view>
      </view>
      <image
        class="back"
        src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/crm/index/glcrm.png"
      ></image>
    </view>
  </view>
</template>

<script>
import myButton from "../house/components/myButton";
export default {
  components: {
    myButton,
  },
  data () {
    return {
      phone: "",
      wx_work_userid: "",
      wx_info: {},
      showContent: false
    };
  },
  onLoad (options) {

    if (["wxwork", "com-wx-pc"].includes(this.$isWxWork())) {
      console.log("企业微信环境");
      // 企业微信弹窗解决
      this.getWxQyWxConfig(
        [
          "agentConfig",
          "getContext",
          "onMenuShareWechat",
          "scanQRCode", //  企业微信扫码
          "onMenuShareAppMessage", // 企业微信自定义‘网页内容分享’
          "selectExternalContact",
          "navigateToAddCustomer",
          "getCurExternalContact",
        ],
        (wx) => {
          var _this = this;
          _this.wx = wx;
          _this.wx.invoke("getCurExternalContact", {}, function (res) {
            if (res.err_msg == "getCurExternalContact:ok") {
              _this.wx_work_userid = res.userId;
              _this.getData()
            } else {
              _this.showContent = true
            }
          });
        }
      );
    }
    // else {
    //   // TODO
    //   if (options.website_id == 176) {
    //     this.wx_work_userid = 'wm-VQJYQAABaPDlf4UPTMNqm40Rq5WXw'
    //     this.getData()
    //   }

    // }
  },
  methods: {
    clickUphold () {
      this.$navigateTo(`uphold?type=1&wxuserid=${this.wx_work_userid}`);
    },
    getData () {
      this.$ajax.get(`/qywx/client/qws_info/${this.wx_work_userid}`, {}, res => {
        console.log(res);
        if (res.statusCode == 200) {
          this.wx_info = res.data.wx_info
        }
        this.showContent = true
      }, () => {
        this.showContent = true
      })
    },
    clickSearch () {
      this.$navigateTo(`search_res?type=1&tel=${this.phone}&from=${this.from}`);
      // this.$navigateTo(`my_list?type=1&tel=${this.phone}&from=${this.from}`);
    },
    onClickBind () {
      this.$navigateTo(`search_res?type=1&from=${this.from}`);
      // this.$navigateTo(
      //   `/customer/wx_work_user?wxuserid=${this.wx_work_userid}`
      // );
    },
  },
};
</script>

<style scoped lang="scss">
page {
  background: #f6f6f6;
}
.default {
  padding: 14px 12px;
  .desc {
    font-size: 16px;
    color: #2e3c4e;
    margin: 12px 0;
  }
  .default-box {
    background: #fff;
    border-radius: 4px;
    position: relative;
    height: 101px;
    overflow: hidden;
    padding: 12px;
    justify-content: space-around;
    .content-box {
      .title {
        color: #2e3c4e;
        margin-bottom: 16px;
      }
      .btn {
        padding: 0 20px;
        height: 32px;
        line-height: 32px;
        background-image: linear-gradient(135deg, #4daaff 0%, #2671ff 100%);
        box-shadow: 0px 3px 6px -3px rgba(46, 124, 255, 0.3);
        border-radius: 4px;
        width: fit-content;
        color: #fff;
        &.green {
          background-image: linear-gradient(135deg, #00f1a5 0%, #00d6a0 100%);
          box-shadow: 0px 3px 6px 0px rgba(0, 223, 167, 0.4);
        }
      }
    }
    .i-box {
      .input {
        background: #ffffff;
        border: 0.5px solid rgba(221, 225, 233, 1);
        border-radius: 4px;
        padding: 6px 10px;
        height: 32px;
        margin-right: 12px;
      }
    }
    .back {
      position: absolute;
      width: 107px;
      height: 107px;
      right: 0;
      bottom: 0;
    }
  }
}
.onbtn {
  position: fixed;
  display: flex;
  justify-content: center;
  bottom: 50rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 654rpx;
  &.base {
    height: 80rpx;
  }
}
.top-card {
  padding: 14px;
  background: #fff;
  border-radius: 5px;
  margin: 0 0 10px;
  position: relative;
  &-title {
    justify-content: space-between;
    font-size: 18px;
  }
  &-left {
    .pic {
      width: 40px;
      height: 40px;
    }
  }
  &-right {
    margin-left: 12px;
    .setting {
      width: 20px;
      height: 20px;
      position: absolute;
      right: 14px;
    }
    &-top {
      align-items: center;
      &_name {
        font-size: 14px;
        color: #282a2f;
      }
      &_qw {
        height: 18px;
        width: 18px;
        margin-left: 4px;
      }
      &_tracking {
        color: #3e8afd;
        margin: 0 6px;
        font-size: 12px;
      }
      &_level {
        color: #fff;
        padding: 2px 12px;
        border-radius: 2px;
      }
    }
    &-bottom {
      margin-top: 10px;
      color: #828488;
      font-size: 12px;
    }
  }
  &-bot-bottom {
    margin-top: 16px;
    width: 100%;
    border-top: 1px solid #eeeeee;
    padding-top: 12px;
    font-size: 12px;
    justify-content: space-between;
    color: #828488;
  }
  &-content {
    margin-top: 16px;
    &-label {
      color: #737373;
    }
    &-right {
      margin-left: 20px;
    }
    .textarea {
      margin-top: 8px;
      padding: 6px 16px;
      background: #f5f7fa;
      border: 1px solid #e8e8e8;
      height: 63px;
      width: 100%;
    }
    .placeholderClass {
      font-size: 13px;
      color: #bcbcbc;
    }
  }
  &-label-list {
    margin-top: 8px;
    flex-wrap: wrap;
    .item {
      margin-bottom: 4px;
      padding: 4px 12px;
      border-radius: 4px;
      background: #e8f1ff;
      color: #2f6aff;
      margin-right: 10px;
    }
  }
  .chakan {
    font-size: 13px;
    color: #2f6aff;
    position: absolute;
    right: 14px;
  }
  .level {
    width: 60px;
    margin-top: 10px;
    text-align: center;
    border-radius: 4px;
    background: #f1f4fa;
    color: #737373;
    height: 28px;
    line-height: 28px;
    font-size: 14px;
  }

  &-tabs {
    font-size: 14px;
    justify-content: space-around;
    &-item {
      position: relative;
      &.isactive {
        color: #2f6aff;
        &::after {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          content: '';
          height: 4px;
          background: #2d84fb;
          width: 30px;
          display: block;
          margin-top: 18px;
        }
      }
    }
  }
}
</style>
