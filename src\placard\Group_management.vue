<template>
    <view>
        <view class="Search">
            <view class="search_for">
                <image src="./photo/Group 284.png"></image>
                <input type="text" value="" placeholder="搜索">
            </view>
        </view>
        <view class="whole">
            <view class="Live_room">
                <view>绿城618线上章三直播说房</view>
                <image src="./photo/Dropdown Arrow.png"></image>
            </view>
            <view class="Live_area">
                <view class="area">
                    <view>区域</view>
                    <image src="./photo/Dropdown Arrow.png"></image>
                </view>
                <view class="broker">
                    <view>经纪人</view>
                    <image src="./photo/Dropdown Arrow.png"></image>
                </view>
            </view>
            <view class="agent">
                <view class="head_sculpture">
                    <view class="head_sculpture_left">
                        <view class="sculpture_left"></view>
                        <view class="sculpture_right">
                            <text class="word_top">经纪人</text>
                            <text class="word_bottom">168****1245</text>
                        </view>
                    </view>
                    <view class="head_sculpture_right">
                        <view class="sculpture_telephone">
                            <image src="./photo/telephone.png"></image>
                        </view>
                    </view>
                </view>
                <view class="foot_sculpture">
                    <view class="moniker">
                        <view class="moniker_left">
                            <text>楼盘小程序线索</text>
                        </view>
                        <view class="moniker_right">
                            <text>绿城618线上章三直播说房（石家庄···</text>
                        </view>
                    </view>
                    <view class="moniker">
                        <view class="moniker_left">
                            <text>轨迹跟踪</text>
                        </view>
                        <view class="moniker_right">
                            <text>龙泉街道 ></text>
                        </view>
                    </view>
                    <view class="moniker">
                        <view class="moniker_left">
                            <text>报备时间</text>
                        </view>
                        <view class="moniker_right">
                            <text>2023-02-17 08:32:24</text>
                        </view>
                    </view>
                </view>
            </view>
            <view class="agent">
                <view class="head_sculpture">
                    <view class="head_sculpture_left">
                        <view class="sculpture_left"></view>
                        <view class="sculpture_right">
                            <text class="word_top">经纪人</text>
                            <text class="word_bottom">168****1245</text>
                        </view>
                    </view>
                    <view class="head_sculpture_right">
                        <view class="sculpture_telephone">
                            <image src="./photo/telephone.png"></image>
                        </view>
                    </view>
                </view>
                <view class="foot_sculpture">
                    <view class="moniker">
                        <view class="moniker_left">
                            <text>楼盘小程序线索</text>
                        </view>
                        <view class="moniker_right">
                            <text>绿城618线上章三直播说房（石家庄···</text>
                        </view>
                    </view>
                    <view class="moniker">
                        <view class="moniker_left">
                            <text>轨迹跟踪</text>
                        </view>
                        <view class="moniker_right">
                            <text>龙泉街道 ></text>
                        </view>
                    </view>
                    <view class="moniker">
                        <view class="moniker_left">
                            <text>报备时间</text>
                        </view>
                        <view class="moniker_right">
                            <text>2023-02-17 08:32:24</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

    </view>
</template>
<script>
export default {
    data() {
        return {

        }
    },
}
</script>
<style scoped lang="scss">
.Search {
    width: 100%;
    height: 150rpx;

    // background-color: #1c49a4;
    .search_for {
        width: 90%;
        height: 70rpx;
        background-color: #F5F6F8;
        color: #D1D1D1;
        margin: 0 auto;
        border-radius: 50px;
        text-align: center;
        margin-top: 30rpx;
        display: flex;
        flex-direction: row;
        align-content: center;
        justify-content: center;
        flex-wrap: wrap;

        image {
            width: 50rpx;
            height: 50rpx;
            margin-left: 150rpx;
        }

    }

}

::v-deep.uni-input-wrapper {
    width: 45%;
}

.whole {
    width: 100%;
    height: 1390rpx;
    overflow: hidden;
    background-color: #F5F6F8;

    .Live_room {
        width: 90%;
        height: 80rpx;
        background-color: #fff;
        margin: 0 auto;
        margin-top: 20rpx;
        border-radius: 7px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        padding: 20rpx;

        image {
            width: 30rpx;
            height: 20rpx;
        }
    }

    .Live_area {
        width: 90%;
        height: 80rpx;
        // background-color: aquamarine;
        margin: 0 auto;
        margin-top: 20rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .area {
            width: 45%;
            height: 80rpx;
            background-color: #fff;
            border-radius: 7px;
            padding: 20rpx;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            image {
                width: 30rpx;
                height: 20rpx;
            }
        }

        .broker {
            width: 50%;
            height: 80rpx;
            background-color: #fff;
            border-radius: 7px;
            padding: 20rpx;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            image {
                width: 30rpx;
                height: 20rpx;
            }
        }
    }

    .agent {
        width: 90%;
        height: 350rpx;
        background-color: #fff;
        margin: 0 auto;
        margin-top: 30rpx;
        border-radius: 7px;

        .head_sculpture {
            width: 90%;
            height: 120rpx;
            margin: 0 auto;
            border-bottom: 1px solid #DBDBDB;
            // background-color: chocolate;
            display: flex;
            flex-wrap: wrap;

            .head_sculpture_left {
                width: 60%;
                height: 120rpx;
                // background-color: rgb(146, 255, 127);
                display: flex;
                flex-direction: row;

                .sculpture_left {
                    width: 80rpx;
                    height: 80rpx;
                    background-color: palevioletred;
                    border-radius: 50%;
                    margin-top: 20rpx;
                }

                .sculpture_right {
                    .word_top {
                        font-size: 15px;
                        font-weight: 550;
                        margin-top: 30rpx;
                        margin-left: 20rpx;
                    }

                    .word_bottom {
                        font-size: 13px;
                        margin-top: 10rpx;
                        margin-left: 20rpx;
                    }
                }
            }

            .head_sculpture_right {
                width: 40%;
                height: 120rpx;
                // background-color: rgb(54, 134, 23);
                display: flex;
                flex-direction: row-reverse;

                .sculpture_telephone {
                    width: 50rpx;
                    height: 50rpx;
                    // background-color: #2D84FB;
                    margin-top: 28rpx;
                    margin-right: 30rpx;

                    image {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }

        .foot_sculpture {
            width: 92%;
            height: 250rpx;
            // background-color: peru;
            margin: 10rpx auto;

            .moniker {
                width: 100%;
                height: 50rpx;
                // background-color: aquamarine;
                margin-top: 10rpx;
                display: flex;
                flex-wrap: wrap;
                align-content: space-between;
                .moniker_left{
                    color: #8A929F;
                    font-size: 27rpx;
                }
                .moniker_right{
                    color: #2E3C4E;
                    font-size: 25rpx;
                }
            }
        }
    }

}
</style>