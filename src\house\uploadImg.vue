<template>
  <view class="upload">
    <view class="form">
      <view
        class="uploadImg add_content"
        :class="{
          'items-center': pic.length == 0,
          'justify-center': pic.length == 0,
          'flex-row ': pic.length > 0,
          uploadImg_right: pic.length == 0,
        }"
      >
        <myUpload
          :imgs="pic"
          :del="false"
          :action="action"
          :upInfo="upInfo"
          @uploadDone="uploadDone"
        ></myUpload>
        <view class="tip" v-if="pic.length == 0">上传图片</view>
      </view>
    </view>
  </view>
</template>

<script>
import myUpload from './components/upload'
export default {
  components: {
    myUpload,
  },
  data() {
    return {
      pic: [],
      token: '',
      website_id: '',
      action: '/common/house/uploadByMobile',
      upInfo: {
        up_token: '',
        category: 3,
        tenant_id: '',
      },
    }
  },
  onLoad(options) {
    // let token = ''
    if (options.website_id) {
      this.website_id = options.website_id
      this.upInfo.website_id = options.website_id
      // token = uni.getStorageSync(`token${options.website_id}`)
    }
    if (options.tenant_id) {
      this.upInfo.tenant_id = options.tenant_id
    }
    if (options.identify) {
      this.identify = options.identify
      this.getupToken()
    }
    if (options.up_token) {
      this.up_token = options.up_token
      this.upInfo.up_token = options.up_token
    }

  },
  methods: {
    uploadDone(res) {
      this.pic = res
    },
    getupToken() {
      this.$ajax.get(`/common/house/houseGetUpToken/${this.identify}`, {}, (res) => {
        if (res.statusCode == 200) {
          this.upInfo.up_token = res.data.token
        }else if (res.statusCode == 422) {
          console.log(res);
          uni.showToast({
            title: res.data.message,
            icon:'none'
          })
        }
      })
    },
    postCode() {
      var url
      let params = {
        website_id: this.website_id,
        category: 1,
        code: this.code,
      }
      if (this.$isWxWork() === 'wxwork' || this.$isWxWork() === 'com-wx-pc') {
        //判断当前环境切换链接
        url = '/auth/client/login/wx_work/3rd'
      } else {
        url = '/auth/client/login/wx_public'
      }
      this.$ajax.post(url, params, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: '登录成功',
          })
          // uni.setStorageSync("token", res.data.token);
          uni.setStorageSync('token' + this.params.website_id, res.data.token)
          // setTimeout(() => {
          //   history.go(-2);
          // }, 1200);
          // uni.switchTab({
          //   url: `/index/mine`,
          // });
          // let loginUrl = uni.getStorageSync("loginUrl");
          // if (loginUrl) {
          //   window.open(loginUrl);
          // }
        } else {
          uni.showToast({
            title: res.data.message || '登录失败',
            icon: 'none',
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.uploadImg {
  &.uploadImg_right {
    margin-top: 100rpx;
    margin-right: -196rpx;
  }
  .tip {
    margin-top: 10px;
    margin-left: -196rpx;
  }
}
</style>
