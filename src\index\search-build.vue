<template>
  <view class="list">
    <view class="search-box row">
      <input
        type="text"
        placeholder="请输入楼盘名称"
        v-model="value"
        @input="isInput"
      />
      <text style="color:#0174ff" @click="goBack">{{ search_desc }}</text>
    </view>
    <view class="hot_search" v-if="!isValue">
      <view class="title">热门搜索</view>
      <view class="hot-content">
        <myTag
          class="tag"
          type="info"
          size="big"
          v-for="item in hot_list"
          :key="item.id"
          @click="handleSearch(item.id, item.build_id)"
          >{{ item.build_name }}</myTag
        >
      </view>
    </view>

    <view class="hot_search" v-if="isToken && !isValue">
      <view class="search-title row">
        <view class="title">搜索历史</view>
        <!-- <view class="clear">清空</view> -->
      </view>
      <block v-for="item in search_cache" :key="item.id">
        <view class="cache-list row">
          <view class="item row" @click="onClickVal(item)">
            <myIcon type="shizhong" color="#999"></myIcon>
            <view class="item-name">
              {{ item.keyword }}
            </view>
          </view>
          <view class="guanbi" @click="closeSearchVal(item)">
            <myIcon type="guanbi" color="#999"></myIcon>
          </view>
        </view>
      </block>
      <view class="loadmore" @click="loadmore">{{ load_more_text }}</view>
    </view>
    <view class="search-list " v-if="isValue">
      <view
        class="line row"
        v-for="item in search_list"
        :key="item.id"
        @click="handleSearch(item.id, item.build_id)"
      >
        <view class="left">{{ item.build_name }}</view>
        <view class="right"
          ><myIcon type="you" color="#999" size="28rpx"></myIcon
        ></view>
      </view>
      <loadMore :status="load_status"></loadMore>
    </view>
  </view>
</template>

<script>
import myTag from "@/components/my-tag";
import myIcon from "@/components/my-icon";
import loadMore from "@/components/loadMore";
export default {
  components: { myTag, myIcon, loadMore },
  data() {
    return {
      hot_list: [],
      search_list: [],
      value: "",
      isValue: false,
      load_status: "",
      isToken: false,
      search_cache: [],
      load_more_text: "点击加载更多",
      parmas: {
        page: 1,
      },
      search_desc: "取消",
    };
  },
  watch: {
    // value(newval, val) {
    //   var obj = {
    //     detail: {
    //       value: newval,
    //     },
    //   };
    //   this.handleInput(obj);
    // },
    value(newval) {
      if (newval.length > 0) {
        var obj = {
          detail: {
            value: newval,
          },
        };
        this.handleInput(obj);
      } else {
        this.isValue = false;
      }
    },
  },
  onLoad() {
    this.getHotList();
  },
  onShow() {
    var token = uni.getStorageSync("token" + this.$store.state.website_id);
    if (token) {
      this.isToken = true;
      // 如果登录获取输入历史记录
      this.getKeyWord();
    }
  },
  methods: {
    goBack() {
      if (this.search_desc === "取消") {
        uni.switchTab({
          url: "/",
        });
      } else if (this.search_desc === "搜索") {
        var obj = {
          detail: {
            value: this.value,
          },
        };
        this.handleInput(obj);
      }
    },
    // 获取小区数据作为热门假数据
    getHotList() {
      this.$ajax.get("/common/project/list?hot_search=1", {}, (res) => {
        if (res.statusCode === 200) {
          this.hot_list = res.data.data;
        }
      });
    },
    handleInput(val) {
      this.$debounce(this.handleInputDebounce, 500)(val);
    },
    handleInputDebounce(val) {
      let value = val.detail.value;
      if (value) {
        this.load_status = "loading";
        this.$ajax.get(
          `/common/project/list?build_name=${value}&is_record=1`,
          {},
          (res) => {
            this.load_status = "loadend";
            if (res.statusCode === 200) {
              this.search_list = res.data.data;
              if (this.search_list.length <= 0) {
                this.load_status = "nomore";
              }
              this.isValue = true;
            }
          }
        );
      } else if (!value) {
        this.isValue = false;
      }
    },
    handleSearch(id, build_id) {
      this.value = "";
      this.isValue = false;
      this.$navigateTo(`/build/detail?buildID=${build_id}`);
    },
    getKeyWord() {
      this.load_more_text = "正在加载";
      // is_record 默认0不记录
      if (this.parmas.page === 1) {
        this.search_cache = [];
      }
      this.$ajax.get(
        "/client/search_keyword/search?category=0",
        this.parmas,
        (res) => {
          if (res.statusCode === 200) {
            this.load_more_text = "点击加载更多";
            this.search_cache = this.search_cache.concat(res.data.data);
            if (res.data.data.length === 0) {
              this.load_more_text = "没有更多了";
              return;
            }
          } else {
            uni.showToast({
              title: res.data.message || "获取失败",
              icon: "none",
            });
          }
        }
      );
    },
    closeSearchVal(item) {
      this.parmas.page = 1;
      this.$ajax.get(`/client/search_keyword/delete/${item.id}`, {}, (res) => {
        if (res.statusCode === 200) {
          console.log("删除该记录");
          this.getKeyWord();
        }
      });
    },
    loadmore() {
      if (this.load_more_text === "没有更多了") {
        return;
      }
      this.parmas.page++;
      this.getKeyWord();
    },
    onClickVal(item) {
      this.value = item.keyword;
    },
    isInput(e) {
      if (e.detail.value) {
        this.search_desc = "搜索";
      } else {
        this.search_desc = "取消";
        this.isValue = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.list {
  padding: 24rpx 48rpx;
  .search-box {
    justify-content: space-between;
    align-items: center;
    input {
      font-size: 28rpx;
      padding-left: 24rpx;
      background: #eee;
      height: 64rpx;
      width: 530rpx;
      border-radius: 32rpx;
    }
  }
  .hot_search {
    margin-top: 100rpx;
    .title {
      font-weight: bolder;
      font-size: 32rpx;
    }
    .hot-content {
      flex-direction: row;
      flex-wrap: wrap;
      .tag {
        margin: 20rpx;
        margin-left: 0;
      }
    }
    .search-title {
      justify-content: space-between;
      align-items: center;
      .clear {
        color: #0097fe;
      }
    }
  }
  .search-list {
    position: fixed;
    width: 88%;
    top: 100rpx;
    font-size: 28rpx;
    .line {
      color: #999;
      background: #fff;
      justify-content: space-between;
      padding: 20rpx 0;
      border-bottom: 2rpx solid #d8d8d8;
    }
  }
  .cache-list {
    margin-top: 30rpx;
    justify-content: space-between;
    color: #999;
    .item {
      align-items: center;
      .item-name {
        margin-left: 10rpx;
      }
    }
  }
  .loadmore {
    font-size: 30rpx;
    color: #999;
    margin: 30rpx auto 0;
  }
}
</style>
