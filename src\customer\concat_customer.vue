<template>
  <view class="uni-list list">
    <checkbox-group @change="checkboxChange">
      <label
        class="uni-list-cell uni-list-cell-pd flex-row bottom-line"
        v-for="item in memberList"
        :key="item.id"
      >
        <view>
          <checkbox :value="item.id + ''" :checked="item.checked" />
          <!-- <radio :value="'' + item.id" :checked="index === current" /> -->
        </view>
        <view class="prelogo">
          <image :src="item.avatar"></image>
        </view>
        <view class="cname">{{ item.cname }}</view>
      </label>
    </checkbox-group>
    <view class="footer flex-row justify-end items-center">
      <view class="confirm" @click="submit">确定</view>
    </view>
  </view>
</template>

<script>
export default {
  data () {
    return {
      memberList: [],
      current: -1,
      id: '',
      selected: [],
    }
  },
  onLoad (options) {
    if (options.id) {
      this.id = options.id
    }
    this.getMemberList()
  },
  methods: {
    checkboxChange (e) {
      this.selected = e.detail.value
    },
    getMemberList () {
      this.$ajax.get(`/v1/wapLm/getAgentsByAddDepartment/${this.id}`).then((res) => {
        if (res.data.status == 200) {
          this.memberList = res.data.data
        } else {
          uni.showToast({
            title: res.data.message,
            icon: 'none',
          })
        }
      })
    },
    submit () {
      if (this.selected.length == 0) {
        uni.showToast({
          title: '请选择成员',
          icon: 'none',
        })
        return
      }
      // let agent_id = this.selected.join(',')
      // this.$ajax.post('/v1/wapLm/addAgentsByDepartment', { id: this.id, agent_id }).then((res) => {
      //   if (res.data.status == 200) {
      //     uni.showToast({
      //       title: res.data.message,
      //       icon: 'none',
      //     })
      //     setTimeout(() => {
      //       this.$navigateBack()
      //       setTimeout(() => {
      //         uni.$emit('addMemberSuccess')
      //       }, 300)
      //     }, 1000)
      //   }
      // })
    },
  },
}
</script>

<style scoped lang="scss">
.list {
  padding-bottom: 80rpx;
}
.uni-list {
  checkbox-group {
    padding-left: 24rpx;
    uni-checkbox {
      ::v-deep .uni-checkbox-input {
        margin-right: 0;
        border-radius: 50%;
      }
    }
  }
  .prelogo {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    margin: 0 40rpx;
    overflow: hidden;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .cname {
    font-size: 28rpx;
    font-weight: bolder;
    color: #333;
  }

  .uni-list-cell {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 20rpx 0;
  }
  .footer {
    padding: 20rpx;
    background: #f8f8f8;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    max-width: 1500rpx;
    .confirm {
      font-size: 28rpx;
      padding: 10rpx 20rpx;
      color: #fff;
      background: #2d84fb;
      border-radius: 8rpx;
    }
  }
}
</style>
