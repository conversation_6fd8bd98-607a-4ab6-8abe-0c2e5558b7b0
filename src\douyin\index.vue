<template>
  <view class="page">
    <view class="top">
      <titleBar
        :fixed="false"
        :top="status_top + 10 + 'px'"
        textcolor="#fff"
        iconSize="48rpx"
        backgroundColor="#313a4c"
        title="数据看板"
      ></titleBar>
    </view>
    
    <view class="container">
      <view class="container_top"> </view>
      <view class="top_info">
        <view class="top_info_top">粉丝数量</view>
        <view class="top_info_middle flex-row items-center">
          <view class="top_info_midddle_left flex-1"> {{ top.total_fans }} </view>
          <view
            class="top_info_midddle_right flex-row items-center"
            v-if="top.avatar && top.avatar.length"
          >
            <image
              v-for="(item, index) in top.avatar"
              :key="index"
              :src="item.avatar | imageFilter('w_80')"
            ></image>
          </view>
        </view>
        <view class="top_info_bottom flex-row items-center">
          <view class="top_info_bottom_left flex-row items-center flex-1">
            <text>矩阵账号 </text>
            <text class="num">{{ top.user_count }}</text>
            <text> 个</text>
          </view>
          <view class="top_info_bottom_right flex-row items-center">
            <text>昨日播放 </text>
            <text class="num">{{ top.new_play | filterNum }}</text>
            <text v-if="top.new_play > 10000"> 万</text>
          </view>
          <view class="top_info_bottom_icon">
            <image :src="'/static/h5crm/douyin/arrow.png' | imageFilter('w_80')"></image>
          </view>
        </view>
      </view>
      <view class="info flex-row items-center">
        <view class="top_info flex-1">
          <view class="top_info_top">主页访问</view>
          <view class="top_info_middle flex-row items-center">
            <view class="top_info_midddle_left"> {{ middle.profile_uv }}</view>
          </view>
          <view class="top_info_bottom flex-row items-center">
            <view class="top_info_bottom_right flex-row items-center">
              <text>累计访问 </text>
              <text class="num">{{ middle.total_profile_uv | filterNum }}</text>
              <text v-if="middle.total_profile_uv > 10000"> 万</text>
            </view>
          </view>
        </view>
        <view class="top_info flex-1">
          <view class="top_info_top">作品新增</view>
          <view class="top_info_middle flex-row items-center">
            <view class="top_info_midddle_left"> {{ middle.new_issue }}</view>
          </view>
          <view class="top_info_bottom flex-row items-center">
            <view class="top_info_bottom_right flex-row items-center">
              <text>作品总量 </text>
              <text class="num">{{ middle.total_issue | filterNum }}</text>
              <text v-if="middle.total_issue > 10000"> 万</text>
            </view>
          </view>
        </view>
      </view>
      <view class="kanban">
        <view class="kanban_title flex-row items-center">
          <view class="img">
            <image
              mode="widthFix"
              :src="'/static/h5crm/douyin/chart.png' | imageFilter('w_80')"
            ></image>
          </view>
          <view class="title"> 数据看板 </view>
        </view>
        <view class="kanban_content">
          <view class="kanban_item flex-row bottom-line">
            <view class="top_info flex-1 right-line">
              <view class="top_info_top flex-row items-center">
                <view class="img">
                  <image :src="'/static/h5crm/douyin/play.png' | imageFilter('w_80')"></image>
                </view>
                <view class="title"> 播放量 </view>
              </view>
              <view class="top_info_middle flex-row items-center">
                <view class="top_info_midddle_left flex-row items-center"
                  ><text v-if="bottom.new_play && bottom.new_play.indexOf('-') < 0">+</text>
                  {{ bottom.new_play }}</view
                >
              </view>
              <view class="top_info_bottom flex-row items-center">
                <view class="top_info_bottom_left flex-row items-center flex-1">
                  <text>总播放量</text>
                  <text class="num">{{ bottom.total_play | filterNum }}</text>
                  <text v-if="bottom.total_play > 10000"> 万</text>
                </view>
              </view>
            </view>
            <view class="top_info flex-1">
              <view class="top_info_top flex-row items-center">
                <view class="img">
                  <image :src="'/static/h5crm/douyin/like.png' | imageFilter('w_80')"></image>
                </view>
                <view class="title"> 点赞量 </view>
              </view>
              <view class="top_info_middle flex-row items-center">
                <view class="top_info_midddle_left flex-row items-center">
                  <text v-if="bottom.new_like && bottom.new_like.indexOf('-') < 0">+</text
                  >{{ bottom.new_like }}</view
                >
              </view>
              <view class="top_info_bottom flex-row items-center">
                <view class="top_info_bottom_left flex-row items-center flex-1">
                  <text>总点赞量</text>
                  <text class="num">{{ bottom.total_like | filterNum }}</text>
                  <text v-if="bottom.total_like > 10000"> 万</text>
                </view>
              </view>
            </view>
          </view>
          <view class="kanban_item flex-row">
            <view class="top_info flex-1 right-line">
              <view class="top_info_top flex-row items-center">
                <view class="img">
                  <image :src="'/static/h5crm/douyin/comment.png' | imageFilter('w_80')"></image>
                </view>
                <view class="title"> 评论量 </view>
              </view>
              <view class="top_info_middle flex-row items-center">
                <view class="top_info_midddle_left flex-row items-center">
                  <text v-if="bottom.new_comment && bottom.new_comment.indexOf('-') < 0">+</text>
                  {{ bottom.new_comment }}</view
                >
              </view>
              <view class="top_info_bottom flex-row items-center">
                <view class="top_info_bottom_left flex-row items-center flex-1">
                  <text>总评论量</text>
                  <text class="num">{{ bottom.total_comment | filterNum }}</text>
                  <text v-if="bottom.total_comment > 10000"> 万</text>
                </view>
              </view>
            </view>
            <view class="top_info flex-1">
              <view class="top_info_top flex-row items-center">
                <view class="img">
                  <image :src="'/static/h5crm/douyin/share.png' | imageFilter('w_80')"></image>
                </view>
                <view class="title"> 分享量 </view>
              </view>
              <view class="top_info_middle flex-row items-center">
                <view class="top_info_midddle_left flex-row items-center">
                  <text v-if="bottom.new_share && bottom.new_share.indexOf('-') < 0">+</text
                  >{{ bottom.new_share }}</view
                >
              </view>
              <view class="top_info_bottom flex-row items-center">
                <view class="top_info_bottom_left flex-row items-center flex-1">
                  <text>总分享量</text>
                  <text class="num">{{ bottom.total_share | filterNum }}</text>
                  <text v-if="bottom.total_share > 10000"> 万</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- <view class="btm"> -->
     <botBars :currentIndex="1"></botBars>
    <!-- </view> -->
  </view>
</template>

<script>
import titleBar from '@/components/titleBar.vue'
import botBars from "./components/bottomBars"

export default {
  components: { titleBar,botBars },
  data () {
    return {
      top: {},
      middle: {},
      bottom: {}
    }
  },
  filters: {
    filterNum (val) {
      let name = val
      if (val > 10000) {
        name = +(val / 10000).toFixed(2)
      }
      return name
    }
  },
  computed: {
    status_top () {
      let systemInfo = uni.getSystemInfoSync()
      return systemInfo.statusBarHeight
    }
  },
  onLoad () {
    this.getData()
  },
  methods: {
    getData () {
      this.$ajax.get('/qywx/byte_dance/statistics', {}, res => {
        console.log(res);
        if (res.statusCode == 200) {
          this.top = res.data.top
          this.middle = res.data.middle
          this.bottom = res.data.bottom
        }
      })
    }
  }
}
</script>

<style lang ="scss" scoped>
.page {
  width: 100vw;
  line-height: 1.5;
  min-height: 100vh;
  padding-bottom: 160rpx;
  background: #f7f7f7;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
/* .flex-1{

} */
.items-center {
  align-items: center;
}
.container_top {
  height: 360rpx;
  width: 100vw;
  background: #313a4c;
}
.info {
  gap: 48rpx;
  margin: 0 48rpx 40rpx;
  .top_info {
    margin: 0;
  }
}
/* .box {
  height: 100px;
  width: 100%;
  background: linear-gradient(180deg, #ffa5a5 0%, #7a1c1c 100%);
  border-radius: 12px;
  overflow: hidden;
  box-sizing: border-box;
  .btn {
    width: 100%;
    border-radius: 12px;
    height: 100%;
    background: #f00;
    margin: 10px;
  }
} */
.top_info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 24rpx;
  gap: 24rpx;
  background: #fff;
  margin: -136rpx 48rpx 48rpx;
  border-radius: 16rpx;
  .top_info_top {
    font-size: 28rpx;
    color: #999;
    .img {
      width: 64rpx;
      height: 64rpx;
      margin-right: 16rpx;
      overflow: hidden;
      image {
        width: 100%;
      }
    }
  }
  .top_info_middle {
    width: 100%;
    /* padding: 24rpx 0; */
    .top_info_midddle_left {
      font-weight: 600;
      font-size: 48rpx;
      color: #333;
    }
    .top_info_midddle_right {
      image {
        width: 64rpx;
        height: 64rpx;
        border-radius: 50%;
        ~ image {
          margin-left: -24rpx;
        }
      }
    }
  }
  .top_info_bottom {
    color: #999999;
    font-size: 24rpx;
    width: 100%;
    flex: 1;
    .num {
      font-weight: 500;
      color: #333333;
    }
    .top_info_bottom_left {
    }
    .top_info_bottom_right {
    }
    .top_info_bottom_icon {
      margin-left: 36rpx;
      image {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}
.kanban {
  margin: 0 48rpx;
  .kanban_title {
    margin-bottom: 20rpx;
    .title {
      font-size: 32rpx;
      color: #333;
    }
    .img {
      width: 48rpx;
      height: 48rpx;
      margin-right: 8rpx;
      overflow: hidden;
      image {
        width: 100%;
      }
    }
  }
}
.kanban_content {
  .kanban_item {
    /* gap: 48rpx; */
    .top_info {
      margin: 0;
      border-radius: 0;
    }
  }
}
</style>