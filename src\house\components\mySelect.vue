<template>
  <view class="select-box" :class="{ one_row: oneRow, 'bottom-line': bottomLine }">
    <label v-if="label">{{ label }}</label>
    <view
      class="select-row flex-row"
      :class="[size, border ? 'border' : '', disabled ? 'disabled' : '']"
      @click="pickerClick"
    >
      <view class="select" v-if="multiple || customer">
        <view class="flex-row space-between items-center">
          <slot>
            <view
              :class="(!multiple && value) || (multiple && value.length > 0) ? '' : 'novalue'"
              >{{ viewSelect }}</view
            >
          </slot>
          <view class="flex-row items-center">
            <text v-if="unit" class="unit">{{ unit }}</text>
            <icons
              v-if="show_icon"
              type="jinrujiantou"
              color="#999"
              :size="size === 'small' ? 28 : 32"
            ></icons>
          </view>
        </view>
        <my-popup
          position="bottom"
          :show="show_customer_select"
          @close="show_customer_select = false"
        >
          <view class="option_list" :class="{ has_bottombar }">
            <view class="header flex-row">
              <text class="cancel_btn" @click.stop.prevent="show_customer_select = false"
                >取消</text
              >
              <text class="tip">请选择</text>
              <text class="ok_btn" @click.stop.prevent="onConfirmSelect">完成</text>
            </view>
            <scroll-view scroll-y class="content">
              <view
                class="item bottom-line"
                v-for="(item, index) in range"
                :key="index"
                :class="{
                  active:
                    tepm_current_value &&
                    (multiple
                      ? tepm_current_value.includes(item[value_name])
                      : tepm_current_value === item[value_name]),
                }"
                @click.stop.prevent="onClickSelect(item)"
              >
                <text class="text">{{ item[label_name] }}</text>
                <view class="icon"><icons type="chenggong" size="28" color="#2d84fb"></icons></view>
              </view>
            </scroll-view>
          </view>
        </my-popup>
      </view>
      <picker
        v-else
        :disabled="disabled"
        @change="pickerChange"
        :value="index"
        :range="range"
        :range-key="label_name"
      >
        <view class="flex-row space-between items-center">
          <slot>
            <view :class="index >= 0 ? '' : 'novalue'">{{
              index >= 0 ? range[index][label_name] : placeholder
            }}</view>
          </slot>
          <view class="flex-row items-center">
            <text v-if="unit" class="unit">{{ unit }}</text>
            <icons
              v-if="show_icon"
              type="jinrujiantou"
              color="#999"
              :size="size === 'small' ? 28 : 32"
            ></icons>
          </view>
        </view>
      </picker>
    </view>
  </view>
</template>

<script>
import icons from '@/components/my-icon'
import myPopup from '@/components/myPopup'
export default {
  props: {
    multiple: {
      type: Boolean,
      default: false,
    },
    customer: {
      type: Boolean,
      default: false,
    },
    has_bottombar: {
      type: Boolean,
      default: false,
    },
    label: String,
    name: String,
    value: {
      type: [String, Number, Array],
      default: '',
    },
    range: {
      type: Array,
      default: () => [],
    },
    value_name: {
      type: String,
      default: 'value',
    },
    label_name: {
      type: String,
      default: 'name',
    },
    border: Boolean,
    unit: String,
    size: {
      //small base
      type: [String],
      default: 'base',
    },
    disabled: {
      type: [Boolean],
      default: false,
    },
    oneRow: {
      type: [Boolean],
      default: false,
    },
    bottomLine: {
      type: [Boolean],
      default: false,
    },
    show_icon: {
      type: [Boolean],
      default: true,
    },
    placeholder: {
      type: String,
      default: '请选择',
    },
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  data() {
    return {
      tepm_current_value: '',
      show_customer_select: false,
    }
  },
  watch: {
    value(val) {
      if (this.multiple) {
        if (!val) {
          this.value = []
        }
      } else {
        this.value = val
      }
    },
    show_customer_select(val) {
      if (val) {
        if (this.multiple) {
          this.tepm_current_value = Array.from(this.value)
        } else {
          this.tepm_current_value = this.value
        }
      }
    },
  },
  computed: {
    index() {
      if (this.value || this.value === '0' || this.value === 0) {
        return this.range.findIndex((item) => item[this.value_name] === this.value)
      } else {
        return -1
      }
    },
    viewSelect() {
      if (!this.value || this.value.length === 0 || !this.range || this.range.length === 0) {
        return this.placeholder
      }
      let selected_option = ''
      if (Object.prototype.toString.call(this.value) === '[object Array]') {
        selected_option = this.range
          .filter((item) => this.value.includes(item[this.value_name]))
          .map((item) => item[this.label_name])
          .join(',')
      } else {
        selected_option = this.range.find((item) => this.value === item[this.value_name])[
          this.label_name
        ]
      }
      return selected_option || this.placeholder
    },
  },
  components: {
    icons,
    myPopup,
  },
  created() {
    if (!this.value && this.multiple) {
      this.value = []
      this.tepm_current_value = []
    }
  },
  filters: {
    viewSelect(val, placeholder) {
      if (!val || val.length === 0) {
        return placeholder
      }
      var selected_option = this.range
        .filter((item) => this.value.includes(item[this.value_name]))
        .join(',')
      return selected_option || placeholder
    },
  },
  methods: {
    pickerChange(e) {
      let index = parseInt(e.detail.value) || 0
      if (index < 0) {
        index = 0
      }
      var value = this.range[index][this.value_name]
      this.$emit('input', value) //兼容小程序的双向绑定
      this.$emit('change', value)
    },
    pickerClick() {
      if (this.multiple || this.customer) {
        this.show_customer_select = true
      }
      // #ifdef APP-PLUS
      // uni.hideKeyboard()
      plus.key.hideSoftKeybord()
      // #endif
    },
    onClickSelect(e) {
      if (this.multiple) {
        if (this.tepm_current_value.includes(e[this.value_name])) {
          this.tepm_current_value = this.tepm_current_value.filter(
            (item) => item !== e[this.value_name]
          )
        } else {
          this.tepm_current_value.push(e[this.value_name])
        }
      } else {
        this.tepm_current_value = e[this.value_name]
        this.onConfirmSelect()
      }
    },
    onConfirmSelect() {
      this.show_customer_select = false
      let value
      if (this.multiple) {
        value = Array.from(this.tepm_current_value)
      } else {
        value = this.tepm_current_value
      }
      this.$emit('input', value) //兼容小程序的双向绑定
      this.$emit('change', value)
    },
  },
}
</script>

<style lang="scss" scoped>
.option_list {
  // #ifdef H5
  // margin-bottom: 50px;
  // #endif
  &.has_bottombar {
    margin-bottom: 50px;
  }
  background-color: #fff;
  .header {
    padding: 24rpx;
    justify-content: space-between;
    border-top: 4rpx solid #2d84fb;
    background-image: linear-gradient(
      0deg,
      rgba(246, 246, 246, 0) 0%,
      rgba(#2d84fb, 0.1) 100%
    );
    .tip {
      flex: 1;
      text-align: center;
      color: #2e3c4e;
    }
    .cancel_btn {
      color: #888;
    }
    .ok_btn {
      color: #2d84fb;
    }
  }
  .content {
    max-height: 35vh;
    min-height: 320rpx;
    padding: 24rpx 48rpx;
    .item {
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 0;
      ::v-deep .icon {
        display: none;
      }
      &.active {
        color: #2d84fb;
        ::v-deep .icon {
          display: block;
          color: #2d84fb;
        }
      }
      .text {
        margin-right: 16rpx;
      }
    }
  }
}
.flex-row {
  flex-direction: row;
}
label {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 24rpx;
}
.select-box {
  // width: 100%;
  flex: 1;
  // padding: 24rpx 0;
}
.one_row {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  label {
    margin-bottom: 0;
    font-size: 32rpx;
    color: #666;
    margin-right: 24rpx;
  }
  .select-row {
    flex: 1;
    &.border {
      padding: 12rpx;
      border-radius: 12rpx;
      border: 1rpx solid #dde1e9;
    }
    &.disabled {
      background-color: #f8f8f8;
    }
    picker {
      view {
        text-align: right;
        font-size: 32rpx;
        // color: #999;
      }
    }
    &.big {
      border-radius: 8rpx;
      padding: 12rpx 16rpx;
      font-size: 36rpx;
      view {
        font-size: 36rpx;
      }
    }
    &.small {
      border-radius: 8rpx;
      padding: 8rpx 16rpx;
      font-size: 28rpx;
      view {
        font-size: 24rpx;
      }
    }
    &.mini {
      border-radius: 8rpx;
      padding: 6rpx 12rpx;
      view {
        font-size: 24rpx;
      }
    }
  }
}
.select-row {
  align-items: center;
  // line-height: 62upx;
  background-color: #fff;
  &.border {
    padding: 12rpx;
    border-radius: 12rpx;
    border: 1rpx solid #dde1e9;
  }
  &.disabled {
    background-color: #f8f8f8;
  }
  .select {
    flex: 1;
    font-size: 32rpx;
  }
  picker {
    flex: 1;
    // height: 62upx;
    view {
      font-size: 32rpx;
    }
  }
  .novalue {
    font-size: 32rpx;
    color: #8a929f;
  }
  .unit {
    margin-left: 10rpx;
    font-size: 24rpx;
    color: #8a929f;
  }
  uni-icon {
    line-height: 62rpx;
    margin-left: 10rpx;
  }
  &.big {
    border-radius: 8rpx;
    view {
      font-size: 36rpx;
    }
    &.border {
      padding: 12rpx;
    }
  }
  &.small {
    border-radius: 8rpx;
    view {
      font-size: 28rpx;
    }
    &.border {
      padding: 6rpx;
    }
  }
  &.mini {
    border-radius: 8rpx;
    view {
      font-size: 24rpx;
    }
    &.border {
      padding: 4rpx;
    }
  }
}
</style>
