<template>
    <view class="bar">
        <view class="title">
            <slot></slot>
        </view>
        <view class="right" v-if="$slots.right">
            <slot name="right"></slot>
        </view>
    </view>
</template>

<script>
export default {
    
}
</script>
<style lang="scss" scoped>
.bar{
    height: 96rpx;
    display: flex;
    flex-direction: row;
    align-items: center;
    .title{
        font-size: 32rpx;
        color: #131315;
        height: 40rpx;
        line-height: 40rpx;
        padding-left: 12rpx;
        border-left: 8rpx solid #488AF6;
        white-space: nowrap;
        padding-right: 12rpx;
    }
    .right{
        flex: 1
    }
}
</style>