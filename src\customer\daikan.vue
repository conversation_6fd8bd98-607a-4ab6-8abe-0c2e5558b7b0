<template>
  <view>
   <view class="reasonall">
    <view class="reason">

      <view class="block row" v-show="!isTrans">
        <view class="follow">
          带看人<text class="must">*</text>
        </view>
        <view class="follow_c  flex-row cont" @click ="showTakerPicker">
          <view class="value">
            <text v-if="follow_see_params.take_admin_id">{{follow_see_params.take_admin}}</text>
            <text class="empty" v-else>请选择带看人</text>
          </view>
          <image src="../static/icon/index/箭头 .png" style="width:32rpx;height:32rpx;"></image>
        </view>
      </view>

      <view class="block row">
          <view class="follow">
            带看日期<text class="must">*</text>
          </view>
          <view class="follow_c  flex-row cont">
            <tDatePicker v-model="follow_see_params.take_date" value-format="YYYY-MM-DD" :start="takDateRange[0]" :end="takDateRange[1]">
              <view class="follow_c  flex-row cont">
                <view class="value">
                  <text v-if="follow_see_params.take_date">{{follow_see_params.take_date}}</text>
                  <text class="empty" v-else>请选择带看日期</text>
                </view>
                <image src="../static/icon/index/箭头 .png" style="width:32rpx;height:32rpx;"></image>
              </view>
            </tDatePicker>
          <view >

  </view>
          </view>

 

        </view>
     
        <view class="block row">
          <view class="follow">
          带看时间
          </view>
          <view class="follow_c flex-1">
            <view class="follow_time row">
              <view class="follow_time_item flex-1 " :class ="{active:follow_see_params.take_time ==1}" @click = 'follow_see_params.take_time =1'>
                上午
              </view>

              <view class="follow_time_item flex-1 " :class ="{active:follow_see_params.take_time ==2}"  @click = 'follow_see_params.take_time =2'>
                下午
              </view>
              <view class="follow_time_item flex-1 " :class ="{active:follow_see_params.take_time ==3}"  @click = 'follow_see_params.take_time =3'>
                晚上
              </view>
            </view>
          </view>
        </view>

        <view class="block row">
          <view class="follow">带看项目</view>
          <view class="follow_c  flex-row cont" @click ="showProjectPicker">
            <view class="value">
              <template v-if ='seledProjectList && seledProjectList.length'>
                <view class="peikan_label flex-row" v-for ='(item,index) in seledProjectList' :key ="index">
                  <view class="name">
                    {{item.name}}
                  </view>
                  <view class="del" @click.prevent.stop = "delSeledProject(index)">
                    <my-icon type="guanbi" size="26rpx"></my-icon>
                  </view>
                </view>
              </template>
              <text class="empty" v-else>请选择带看项目</text>
            </view>
            <image src="../static/icon/index/箭头 .png" style="width:32rpx;height:32rpx;"></image>
          </view>
        </view>
      

        <view class="block row">
          <view class="follow fs">
            陪看人员
          </view>
          <view class="follow_c  flex-row cont" @click ="showPeikan">
            <template v-if ='selectPeikan&&selectPeikan.length'>
              <view class="peikan_label flex-row" v-for ='(item,index) in selectPeikan' :key ="index">
                <view class="name">
                  {{item.name}}
                </view>
                <view class="del" @click.prevent.stop = "delPeikan(index)">
                  <my-icon type="guanbi" size="26rpx">

                  </my-icon>
                </view>
              </view>
            </template>
            <template v-else >
              <view class="peikan_label empty" >
                请选择陪看人员
              </view>
            </template>
<template>
  <view>
    <image src="../static/icon/index/箭头 .png" style="width:32rpx;height:32rpx"></image>
  </view>
</template>
          </view>

        </view>


        

        <view class="block row">
          <view class="follow">
            带看单号
          </view>
          <view class="follow_c flex-row cont" style="text-align: right;padding-right: 42rpx;">
            <input type="text" placeholder-style="font-size:32rpx;" placeholder="请输入带看单号" v-model ='follow_see_params.take_no'>
          </view>
        </view>
        
       
      </view>
      <view >
          <view class="follow_call">
            <view class="follow_gj">
              {{genjin_title}}<text class="must">*</text>
            </view>
            <!-- <textarea  placeholder="请输入跟进内容 （企业内公开）" rows="3" v-model="follow_see_params.content" class="texttarea"></textarea> -->
          <view class='top-card-contenter'>
            <textarea v-model="follow_see_params.content" auto-height="true" :show-confirm-bar="false" style="min-height:80px"
              :focus="imMessageFocusBool" :cursor="imMessageFocusCursor" :cursor-spacing="14" maxlength='-1'
              @blur="imMessageBlur" @input="imMessageInput" @focus="imMessageFocus" @confirm="sendButtonConfirm"
                placeholder="请输入跟进内容 （企业内公开）" rows="3" class="im-message"></textarea>
              <view style="text-align: right;padding-bottom: 24rpx;">{{count|| follow_see_params.content ? follow_see_params.content.length : 0 }} / {{ maxLength }}</view>
          </view>

          <view class="flex-row items-center">
            <view class="add_friend" @click="addAt" v-if="!isTrans"> @同事 </view>
            <view class="add_friend" :class="{ to_pic: imageList.length }" @click="toPic">
              <view class="title"> +图片 </view>
            </view>
          </view>
          </view>
        </view>

        <view class="assign-wrapper">
          <uni-collapse ref="collapse">
            <uni-collapse-item title-border="none" :border="false" :open="false">
              <template #title>
                <view class="assign-title">
                  分边比例
                </view>
              </template>

              <view class="assign-content">
                <view class="assign-row" v-for="(item, index) in assignUserList" :key="index">
                  <view class="name">{{ item.name }}</view>
                  <view class="assign-input-wrapper">
                    <input class="assign-input" type="digit" v-model="item.percent">
                    <view class="sign">%</view>
                  </view>
                </view>
              </view>
            </uni-collapse-item>
          </uni-collapse>
        </view>

        <view class="btns" >
          <view class="btn" @click ="submit">确定</view>
        </view>
    </view>

    <!-- @好友-start -->
    <view v-if="showAtSelect" class="at-select-box">
      <view class="at-select-users">
        <view @click="hideAtUser" class="close-at-select">关闭</view>
        <scroll-view scroll-y="true" class="at-select-list">
          <view @click="insertAtUser(item)" class="at-select-item" v-for="(item, index) in atUsers" :key="index">
            <!-- <image :src="item.avatar" mode="widthFix"></image> -->
            <text>{{ item.nickname + (item.remark ? '(' + item.remark + ')' : '') }}</text>
          </view>
          <view v-if="!atUsers.length" class="fastim-data-none">没有更多了...</view>
        </scroll-view>
      </view>
    </view>
    <!-- mask -->
    <view v-if="maskShow" @click="maskClick" :style="maskStyle" class="mask"></view>
 
    <tMemberPicker :visible.sync="dialogs.takerPicker" @confirm="handleSeledTaker" v-model="follow_see_params.take_admin_id"></tMemberPicker>
    <tMemberPicker :visible.sync="dialogs.peikanPicker" multiple @confirm="handleSeledPeikan" v-model="follow_see_params.accompany"></tMemberPicker>
    <tCrmProjectPicker :visible.sync="dialogs.projectPicker" allowCreate @confirm="handleSeledProject" v-model="follow_see_params.project_id"></tCrmProjectPicker>
  </view>
</template>

<script>
import myIcon from "@/components/my-icon";
import tMemberPicker from "@/components/tplus/tMemberPicker";
import tCrmProjectPicker from "@/components/tplus/tCrmProjectPicker";
import tDatePicker from '@/components/tplus/tDatePicker';
let cursor = 0, defaultWriteHeight = 46, atUsersEd = [], _this = null
export default {
  components:{
    myIcon, tMemberPicker, tCrmProjectPicker,
    tDatePicker
  },
  data(){
    return {
      count:0,
      maxLength: 300,
      follow_see_params:{
         content:"",
          take_date:"",
          take_time:1,
          take_no:"",
          accompany:[],
          take_admin_id: 0,
          take_admin: '',
          project_id: [],
          remind: ''
      },
      peikanList:[],
      genjin_title:'带看跟进',
      show_peikan:false,
      selectPeikan:[],
      imageList: [],
      submiting: false,
      current: 'my',
      imMessageFocusBool: false,
      showAtSelect: false,
      maskShow: false,
      imMessageFocusCursor: 0,
      atUsers: [],
      imWriteHeight: 0,
      remindArray: [],

      takDateRange: [],   //带看日期范围
      assignUserList: [],
      seledProjectList: [],  // 选中的带看项目
      dialogs: {
        takerPicker: false,
        peikanPicker: false,
        projectPicker: false
      },
      photorequired:"",//带看照片是否必填
    }
  },
  computed: {
    isTrans(){
      return this.current === 'trans';
    }
  },
  watch: {
    'follow_see_params.take_admin_id'(){
      this.setAssignUserList();
    },
    'follow_see_params.accompany'(){
      this.setAssignUserList();
    }
  },
  onLoad(options){
    this.follow_see_params.client_id = options.id
    this.follow_see_params.type = options.type
    this.current = options.current || 'my'
    this.getPeikanList()
    if( options.type==2){
      this.genjin_title = "复看跟进"
    }
    uni.$on("uploadOk", res => {
      this.imageList = res
    })

    //带看日期范围
    let now = new Date().getTime(),
        minDate = new Date(now - 86400000 * 3),
        maxDate = new Date(now + 86400000 * 3),
        y1 = minDate.getFullYear(),
        m1 = minDate.getMonth() + 1,
        d1 = minDate.getDate(),
        y2 = maxDate.getFullYear(),
        m2 = maxDate.getMonth() + 1,
        d2 = maxDate.getDate();
    this.takDateRange = [
      y1+'-'+(m1>9?m1:'0'+m1)+'-'+(d1>9?d1:'0'+d1),
      y2+'-'+(m2>9?m2:'0'+m2)+'-'+(d2>9?d2:'0'+d2)
    ]

    this.getCurrentTaker();
    this.getUserList()
    this.getdaikan()
  },
  onUnload() {
    uni.$off("uploadOk")
    // uni.removeStorageSync("telInfo")
  },
  methods:{
    setAssignUserList(){
      let list = [];
      if(this.follow_see_params.take_admin_id){
        list.push({
          id: this.follow_see_params.take_admin_id,
          name: this.follow_see_params.take_admin,
          main: 1
        })
      }
      if(this.selectPeikan.length){
        for(const item of this.selectPeikan){
          if(!list.find(e=>e.id == item.values)){
            list.push({
              id: item.values,
              name: item.name,
            })
          }
        }
      }

      this.assignUserList = list.map((e,index) => {
        let rel = this.assignUserList.find(item => item.id === e.id);
        !rel && e.main == 1 && (rel = this.assignUserList.find(item => item.main==1));
        e.percent = rel ? rel.percent : '';
        return e;
      });

      this.$nextTick(()=>{
        this.$refs.collapse.resize(); 
      })
    },
    showProjectPicker(){
      this.dialogs.projectPicker = true;
    },
    handleSeledProject(data){
      this.seledProjectList = data || [];
    },
    //判断带看照片是否是必填
    getdaikan(){
      let name = "is_take_images"
      this.$ajax.get(`/admin/crm/config/get_crm_fixed_config`, {
        key:'is_take_images'
      }, res => {
        if (res.statusCode === 200) {
              this.photorequired = res.data
            }
      })
    },

    delSeledProject(index){
      this.seledProjectList.splice(index, 1);
      this.follow_see_params.project_id.splice(index, 1);
    },

    //获取当前带看人
    getCurrentTaker(){
      let temp = uni.getStorageSync("userInfo");
      const userInfo = temp ? JSON.parse(temp) : {};
      this.follow_see_params.take_admin_id = parseInt(userInfo.id || 0) || 0;
      this.follow_see_params.take_admin = userInfo.user_name || '';
    },
    //显示带看人选择
    showTakerPicker(){
      this.dialogs.takerPicker = true;
    },
    //选择带看人
    handleSeledTaker(data){
      this.dialogs.takerPicker = false;
      if(this.follow_see_params.take_admin_id){
        this.follow_see_params.take_admin = data.label ? data.label[0] :'';
      }else{
        this.$nextTick(()=>{
          this.getCurrentTaker();
        })
      }
    },
    handleSeledPeikan(data){
      this.dialogs.peikanPicker = false;
      this.selectPeikan = (data || []).map(e => {
        return {
          values: e.value[0],
          name: e.label[0]
        }
      })
    },
    addAt() {
      this.imMessageFocusBool = true
      this.follow_see_params.content += '@ '
      // this.count = this.content.length;
      this.imMessageInput({
        detail: { value: this.follow_see_params.content }
      })
      var that = this
      that.showAtSelect = true
      that.maskShow = true
      that.maskStyle = 'background:rgba(0, 0, 0, 0.1)';
      // setTimeout(() => {

      // }, 100)
    },
    maskClick() {
      this.showAtSelect = false
      this.maskShow = false
    },
    imMessageBlur: function () {
      this.imMessageFocusBool = false
      if (!this.showTool) {
        this.writeBottom = 0;
        this.writeHeight = defaultWriteHeight;
      }
      // this.inputStatus(false)
    },
    imMessageFocus(e) {
      // this.clickTool(false)

      let writeHeight = () => {
        this.writeBottom = e.detail.height || 0
        this.writeHeight = (parseInt(this.writeBottom) + defaultWriteHeight);
      }
      let userPlatform = uni.getSystemInfoSync.platform
      if (userPlatform == 'ios') {
        // #ifdef APP-PLUS
        // uni.onKeyboardHeightChange(res => {
        // 	this.writeBottom = res.height || e.detail.height || 0
        // 	this.writeHeight = (parseInt(this.writeBottom) + defaultWriteHeight);
        // 	uni.offKeyboardHeightChange(() => {})
        // })
        // #endif

        // #ifndef APP-PLUS
        writeHeight()
        // #endif
      } else {
        writeHeight()
      }

      // this.scrollIntoFooter(0, 99993)
    },
    imMessageInput: function (e) {
      console.log(e, '99999');
      cursor = e.detail.cursor || e.detail.value.length

      // 从光标位置向前搜索@和空格符号
      let atSearchIdx = -1;
      let beforeCursor = e.detail.value.substr(0, cursor)
      for (let i = (beforeCursor.length - 1); i >= 0; i--) {
        if (beforeCursor[i] == ' ' && beforeCursor[i + 1] == ' ') {
          break;
        } else if (beforeCursor[i] == '@') {
          atSearchIdx = i
        }
      }
      if (atSearchIdx !== -1) {
        this.atUser(beforeCursor.substr(atSearchIdx + 1));
      } else if (this.showAtSelect) {
        this.hideAtUser()
      }

      this.follow_see_params.content = e.detail.value;
      // this.imMessageChange()
      // this.inputStatus()
    },
    atUser(keywords = '') {
      var that = this
      that.showAtSelect = true
      that.maskShow = true
      that.maskStyle = 'background:rgba(0, 0, 0, 0.1)';

      // 获得输入框高度
      let imWrite = uni.createSelectorQuery().select('.im-message');
      imWrite.fields({
        size: true
      }, data => {
        that.imWriteHeight = data.height
      }).exec()


    },
    hideAtUser: function () {
      this.maskShow = false
      this.maskStyle = ''
      this.showAtSelect = false
    },
    insertAtUser(item) {
      // this.remind = item.id
      this.remindArray.push(item.values)
      // console.log(this.remindArray,'this.remind.push(item.values).splice(',')');
      // 将@用户传入id给后端
      this.follow_see_params.remind = this.remindArray.join(',')
      console.log(this.remind, 'this.remindthis.remind');
      item.nickname += '   '
      atUsersEd.push({
        id: item.id,
        nickname: item.nickname
      })
      this.hideAtUser()
    

      // 找到用户输入的搜索词，删除
      // 将当前@的用户全称输入至输入框
      let atSearchIdx = -1;
      let beforeCursor = this.follow_see_params.content.substr(0, cursor)

      for (let i = (beforeCursor.length - 1); i >= 0; i--) {
        if (beforeCursor[i] == ' ' && beforeCursor[i + 1] == ' ') {
          break;
        } else if (beforeCursor[i] == '@') {
          atSearchIdx = (i + 1)
        }
      }
      let delContent = beforeCursor.substr(atSearchIdx)
      this.follow_see_params.content = this.follow_see_params.content.substring(0, atSearchIdx) + item.nickname + this.follow_see_params.content.substring(atSearchIdx + delContent.length)

      // 聚焦到指定字符后
      this.imMessageFocusCursor = (cursor - delContent.length) + item.nickname.length

      setTimeout(() => {
        this.imMessageFocusBool = true
      }, 100)
    },
    getUserList() {
      this.$ajax.get("/admin/crm/client_follow/userListDepartment", {}, res => {

        if (res.statusCode == 200) {

          res.data.map(item => {
            item.id = item.values
            item.nickname = item.name
            return item
          })
          this.atUsers = res.data
        }
      })
    },

    // // 添加图片
    toPic() {
      if (this.imageList && this.imageList.length) {
        uni.setStorageSync("uploadImg", JSON.stringify(this.imageList || []))
      }

      this.$navigateTo("/customer/pics")
    },
    changeDate(e){
      this.follow_see_params.take_date = e.detail.value
    },
    changeSelect(e){
      this.selectMember = e
    },
    // 获取陪看人员列表
    getPeikanList(){
      this.$ajax.get('/admin/crm/client_follow/userListDepartment',{},res=>{
        if(res.statusCode ==200){
          this.peikanList =res.data
        }
      })
    },
    showPeikan(){
      this.dialogs.peikanPicker = true; 
    },
    updateCount() {
      this.count = this.follow_see_params.content.length; // 更新字数计数
      // this.value ? this.value.length :
    },
    delPeikan(index){
      this.selectPeikan.splice(index, 1)
      this.follow_see_params.accompany.splice(index, 1)
    },
    selectMemberOk(e){
      console.log(e);
      this.selectPeikan = e
      this.show_peikan =false
    },
    // 提交带看
    submit(){
      //console.log(this.follow_see_params);
      if(!this.follow_see_params.take_date){
        uni.showToast({
          title:'请选择带看日期',
          icon:"none"
        })
        return 
      }
      if(!this.follow_see_params.content){
        uni.showToast({
          title:'内容不能为空',
          icon:"none"
        })
        return 
      }
      if (!this.isTrans && this.photorequired === 1 && !this.imageList.length) {
              uni.showToast({
                title: "请上传带看图片",
                icon: "none"
              });
              return;
            }
      let params = Object.assign({},this.follow_see_params)
      let sel= []
      if(this.selectPeikan && this.selectPeikan.length){
        this.selectPeikan.map(item=>{
          sel.push(item.values)
          return item
        })
      }
      params.file_path = this.imageList && this.imageList.length ? this.imageList.join(",") : ''
      params.accompany =sel.join(",")

      params.proportion = this.assignUserList.map(e => {
        return {
          user_id: e.id,
          user_name: e.name,
          radio: e.percent||0
        }
      })
      
      if(this.submiting){
        return false
      }
      this.submiting = true
      try{
        let url = "/admin/crm/client_follow/follow_take";
        if(this.isTrans){
          url = '/admin/private_client_follow/follow_take';
        }
        this.$ajax.post(url,params,res=>{
          this.submiting = false
          if(res.statusCode==200){
            uni.showToast({
              title: res?.data?.msg || '添加成功'
            })
            setTimeout(() => {
              this.$emit("getDataAgain",{})
            }, 200);
            setTimeout(()=> {
              this.$navigateBack()
            }, 1500)
          }else{
            uni.showToast({
              title: res.data.message,
              icon: "none"
            })
          }
        })
      }catch(e){
        this.submiting = false
      }
    },
  }
}
</script>

<style lang ="scss" scoped>
.reasonall{
  display: flex;
  flex-direction: column;
  .follow_c.cont{
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-end;
    .value{
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      flex: 1;
      justify-content: flex-end;
      font-size: 32rpx;
      padding: 10rpx;
      margin-bottom: 0;
      .empty{
        color: rgba(41, 44, 57, 0.7);
      }
    }
  }
}


  .add_friend {
    padding: 16rpx 28rpx;
    background: #F8F8F8;
    color: #2e3c4e;
    border-radius: 8rpx;
    margin-top: 24rpx;

    ~.add_friend {
      margin-left: 20rpx;
    }

    &.to_pic {
      position: relative;

      &:after {
        content: '';
        width: 8rpx;
        height: 8rpx;
        position: absolute;
        top: 6rpx;
        right: 6rpx;
        background: #f63131;
        border-radius: 50%;
      }
    }
  }
  .top-card-contenter{
  position: relative;
  border-radius: 8rpx;
border: 2rpx solid #F0F1F5;
background: #F8F8F8;
padding: 24rpx 24rpx 0rpx 24rpx;
margin-top: 24rpx;
}
.texttarea{
  margin-top: 24rpx;
  padding: 12rpx 32rpx;
    // background: #f5f7fa;
    // border: 2rpx solid #e8e8e8;
    height: 220rpx;
    width: 100%;
    // margin-left: 42rpx;
}

.follow_call{
  margin-top: 32rpx;
  padding: 32rpx;
  background: #fff;
  // border-radius: 16rpx;
}
.follow_gj{
  flex-direction: row;
  color: rgba(41, 44, 57, 0.70);
font-size: 32rpx;
  .must{
    color: #f40;
    padding-left: 4rpx;
  }
}

page {
  background: #f6f6f6;
  // color: #2e3c4e;
}
.uni-input{
  color: rgba(41, 44, 57, 0.70);
font-size: 32rpx;
}

  .reason {
    padding: 24rpx 32rpx;
    background: #fff;
    // border-radius: 16rpx;
    textarea{
      border: 2rpx solid #f7f7f7;
    }
    .block{
      padding: 32rpx 0;
      &.row {
        align-items: center;
        justify-content: space-between;
        .follow {
          flex-direction: row;
          align-items: center;
          margin-right: 24rpx;
          margin-bottom:0;
          .must{
            color: #f40;
            padding-left: 4rpx;
          }
        }
        
      }
      .follow {
        margin-bottom: 24rpx;
        white-space: nowrap;
        color:rgba(41, 44, 57, 0.40);
font-size: 32rpx;
        &.fs {
          align-self: flex-start;
          padding: 10rpx 0;
        }
      }
      .follow_c {
        flex-wrap: wrap;
        
        textarea {
          padding: 6px 16px;
          background: #f5f7fa;
          border: 1px solid #e8e8e8;
          height: 153px;
        }
        .peikan_label {
          display: inline-flex;
          flex-direction: row;
          align-items: center;
          padding: 10rpx 16rpx;
          background: #eee;
          margin-right: 8rpx;
          margin-bottom: 4rpx;
          margin-top: 4rpx;
          border-radius: 8rpx;
          font-size: 30rpx;
          color: rgba(41, 44, 57, 0.70);
text-align: center;
font-size: 32rpx;

          &.empty {
              padding: 10rpx ;
              margin: 0;
              background: #fff;
               margin-bottom:0;
          }
          .del {
            margin-left: 8rpx;
            
          }
        }
      }
      .follow_time {
        background: #f0eff4;
        color: #666;
        padding: 4rpx ;
        border-radius: 10rpx;
        .follow_time_item {
          padding: 20rpx 0;
          text-align: center;
          &.active{
            background:#fff;
            border-radius: 10rpx;
          }
        }
      }
    }
  }
  .btns {
    display: flex;
    flex-direction: row;
    padding: 48rpx 32rpx;
    .btn{
     flex: 1;
     display: flex;
     flex-direction: row;
     justify-content: center;
     align-items: center;
     height: 88rpx;
     border-radius: 10rpx;
     background: #2d84fb;
     color:#fff;
     font-size: 32rpx;
    }
  }
  .p_con{
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    padding: 40rpx 48rpx;
    background: #fff;
  }


  
.at-select-box {
  // position: relative;
}

.at-select-users {
  position: absolute;
  background: #ffffff;
  width: 100vw;
  left: 0;
  bottom: 0;
  min-height: 300rpx;
  max-height: 70vh;
  overflow-y: auto;
  box-sizing: border-box;
  border-top-left-radius: 16rpx;
  border-top-right-radius: 16rpx;
  z-index: 9992;
}

.close-at-select {
  font-size: 26rpx;
  float: right;
  color: #999999;
  padding: 20rpx;
}

.at-select-list {
  clear: both;
  max-height: 700rpx;
}

.fastim-data-none {
  text-align: center;
  font-size: 30rpx;
  line-height: 80rpx;
  height: 80rpx;
  color: #999999;
}

.at-select-item {
  display: flex;
  align-items: center;
  height: 90rpx;
  padding-left: 20rpx;
}

.at-select-item image {
  height: 60rpx;
  width: 60rpx;
  border-radius: 16rpx;
}

.at-select-item text {
  padding-left: 10rpx;
}

.mask {
  z-index: 9990;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
}

.assign-wrapper{
  margin-top: 32rpx;
  padding: 12rpx 32rpx;
  background: #fff;
  .assign-title{
    color: rgba(41, 44, 57, 0.7);
    font-size: 32rpx;
    padding: 28rpx 0;
  }
  .assign-row{
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 32rpx;
    margin: 6rpx 0 24rpx;
    .name{
      flex: 1;
      text-align: right;
    }
    
    .assign-input-wrapper{
      width: 220rpx;
      height: 68rpx;
      margin-left: 20rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      border: 1px solid #e6e6e6;
      border-radius: 4rpx;
      padding: 12rpx;
      .assign-input{
        flex: 1;
        padding-left: 8rpx;
      }
      .sign{
        color: rgba(41, 44, 57, 0.4);
        padding-left: 4rpx;
      }
    }
  }
}

</style>