<template>
  <view class="list">
    <view class="house-nav">
      <view
        class="nav-item"
        v-for="(item, index) in navs"
        :key="index"
        :class="{ active: item.id == house_type_navs }"
        @click="onClickHouse(index)"
      >
        <text style="z-index:1">{{ item.name }}</text>
        <image
          src="../static/new_detail/<EMAIL>"
          v-if="item.id == house_type_navs"
          class="is-active"
        ></image>
      </view>
    </view>
    <swiper
      class="swiper"
      :indicator-dots="indicatorDots"
      :autoplay="autoplay"
      :interval="interval"
      :duration="duration"
    >
      <swiper-item v-for="item in arr" :key="item.id">
        <view class="swiper-item  ">
          <image
            mode="aspectFill"
            @click="$previewImage(item.img)"
            :src="item.img | imageFilter('w_220')"
          ></image>
          <view class="desc-row ">
            <view class="house-type _top row"
              >{{ item.total_room }}室{{ item.total_salloon }}厅{{
                item.total_washroom
              }}卫 <view class="label">在售</view></view
            >
            <view class="row area-type _top">
              <text>建面约</text>
              <view class="area">{{ item.area }}㎡</view>
              <view class="build-type _top">{{ item.name }}</view>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
export default {
  props: {
    indicatorDots: Boolean,
    autoplay: Boolean,
    interval: {
      type: [String, Number],
      default: 2000,
    },
    duration: {
      type: [Number, String],
      default: 500,
    },
    src: String,
    build_type: String,
    house_type: String,
    area: String,
    arr: Array,
  },
  data() {
    return {
      navs: [
        { id: 1, name: "全部", category: 0 },
        { id: 2, name: "二室", category: 2 },
        { id: 3, name: "三室", category: 3 },
      ],
      house_type_navs: 1,
    };
  },
  methods: {
    onClickHouse(index) {
      this.house_type_navs = this.navs[index].id;
      let category = this.navs[index].category;
      this.$emit("onClickNav", category);
    },
  },
};
</script>

<style lang="scss" scoped>
uni-swiper-item {
  width: 320rpx !important;
}
.list {
  ._top {
    margin-top: 12rpx;
  }
  margin-top: 24rpx;
  .swiper {
    height: 320rpx;
    .swiper-item {
      image {
        width: 280rpx;
        height: 200rpx;
      }
      .build-type {
        font-size: 24rpx;
        color: #333;
      }
      .house-type {
        font-size: 32rpx;
        color: #141414;
        align-items: center;
      }
      .area-type {
        font-size: 24rpx;
        align-items: flex-end;
      }
      .label {
        border-radius: 2px;
        background: #ecf2ff;
        font-size: 20rpx;
        color: #3172f6;
        margin-left: 12rpx;
        line-height: 30rpx;
        height: 30rpx;
        text-align: center;
      }
    }
  }
}
.house-nav {
  background: #fff;
  flex-direction: row;
  margin: 16rpx 0 32rpx;
  .nav-item {
    font-size: 28rpx;
    margin-right: 50rpx;
    color: #989898;
    position: relative;
    text-align: center;
    &.active {
      color: #141414;
    }
    .is-active {
      position: absolute;
      bottom: -8rpx;
      background-repeat: no-repeat;
      height: 16rpx;
      width: 92rpx;
    }
  }
}
</style>
