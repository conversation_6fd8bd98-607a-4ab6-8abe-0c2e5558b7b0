<template>
  <view class="i-input">
    <view class="full-out row" v-if="full_number == 1">
      <view class="text">+86</view>
      <view class="full-number ">
        <input
          class="full-number-input"
          type="number"
          maxlength="11"
          @input="oninput"
        />
        <view class="border-box row">
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
        </view>
      </view>
    </view>
    <view class="full-out row" v-if="full_number == 0">
      <view class="text">+86</view>
      <view class="full-number">
        <input
          type="number"
          class="full-number-input"
          maxlength="11"
          @input="oninput"
        />
        <view class="border-box row">
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
          <text class="border-bottom yinhao">*</text>
          <text class="border-bottom yinhao">*</text>
          <text class="border-bottom yinhao">*</text>
          <text class="border-bottom yinhao">*</text>
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
          <text class="border-bottom"></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    full_number: {
      type: [String, Number],
      default: 1,
    },
  },
  data() {
    return {};
  },
  methods: {
    oninput(e) {
      if (e.detail.value.length >= 11) {
        this.$emit("input", e.detail.value);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.i-input {
  font-size: 36rpx;
  background: #fff;
  .full-out {
    align-items: center;
    .full-number {
      position: relative;
      height: 20px;
      margin-left: 18rpx;
      width: 100%;
      .full-number-input {
        position: absolute;
        width: 100%;
        top: 0;
        left: 0;
        letter-spacing: 18.5px;
        border: none;
        background: transparent;
        outline: none;
      }
      .border-box {
        width: 100%;
        .border-bottom {
          display: inline-block;
          width: 17px;
          height: 20px;
          margin-right: 10px;
          border-bottom: 1rpx solid #999;
        }
        .yinhao {
          border-bottom: 1rpx solid #fff;
          width: 17px;
          height: 20px;
          z-index: 100;
          background: #fff;
          text-align: center;
          line-height: 20px;
          color: #999;
        }
      }
    }
  }
}
</style>
