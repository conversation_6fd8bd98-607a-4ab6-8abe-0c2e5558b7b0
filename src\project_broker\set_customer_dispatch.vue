<template>
  <view class="c-list">
    <view
      class="list-item row"
      v-for="item in user_list"
      :key="item.id"
      @click="onCheck(item)"
    >
      <view class="img-box row">
        <image :src="item.avatar"></image>
        <view class="name">{{ item.name || item.nickname || item.phone }}</view>
      </view>
      <view
        class="check"
        :class="form_data.user_id === item.id ? 'is_check' : ''"
      >
        <image
          v-if="form_data.user_id === item.id"
          src="@/static/check.png"
        ></image>
      </view>
    </view>
    <view class="btn" @click="onConfirm">确认</view>
    <load-more :status="load_status"></load-more>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore";
export default {
  components: {
    loadMore,
  },
  data() {
    return {
      form_data: {
        user_id: "",
        customer_reported_id: "",
      },
      params: {
        page: 1,
        //   keyword:"",
        is_case: 1,
      },
      load_status: "",
      user_list: [],
    };
  },
  onLoad(options) {
    if (options) {
      this.form_data.customer_reported_id = options.customer_id;
    }
    this.getUserList();
  },
  methods: {
    getUserList() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.user_list = [];
      }
      this.$ajax.get("/client/user/broker/search", this.params, (res) => {
        if (res.statusCode === 200) {
          this.load_status = "loadend";
          this.user_list = this.user_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
            uni.showToast({
              title: "没有更多数据了",
              icon: "none",
            });
          }
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
            success: () => {
              this.params.page = 1;
            },
          });
        }
      });
    },
    onCheck(item) {
      this.form_data.user_id = item.id;
    },
    onConfirm() {
      if (!this.form_data.user_id) {
        uni.showToast({
          title: "请选择置业顾问",
          icon: "none",
        });
        return;
      }
      var that = this;
      uni.showModal({
        title: "提示",
        content: "是否分配",
        confirmText: "确认",
        success: (res) => {
          if (res.confirm) {
            this.$ajax.post(
              "/client/customer/reported/dispatch",
              this.form_data,
              (res) => {
                if (res.statusCode === 200) {
                  uni.showToast({
                    title: "分配成功",
                  });
                  this.$navigateTo("/project_broker/all_customers");
                } else {
                  uni.showToast({
                    title: res.data.message,
                    icon: "none",
                  });
                }
              }
            );
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.getUserList();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getUserList();
  },
};
</script>

<style scoped lang="scss">
.c-list {
  padding: 48rpx;
  .list-item {
    align-items: center;
    justify-content: space-between;
    margin-bottom: 48rpx;
    .img-box {
      align-items: center;
      image {
        margin-right: 20rpx;
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }
    }
    .check {
      background: #f8f8f8;
      border: 1px solid #eee;
      border-radius: 50%;
      width: 40rpx;
      height: 40rpx;
      image {
        width: 30rpx;
        height: 30rpx;
      }
    }
    .is_check {
      background: #0174ff;
      align-items: center;
      justify-content: center;
      border-color: #fff;
    }
  }
  .btn {
    color: #fff;
    height: 72rpx;
    text-align: center;
    background: #0174ff;
    border-radius: 36rpx;
    line-height: 72rpx;
    position: fixed;
    bottom: 100rpx;
    left: 48rpx;
    right: 48rpx;
    font-size: 32rpx;
  }
}
</style>
