<template>
  <view>
    <button @click="getInfo">获取code</button>
    <view v-if="code" @click="copy"> {{ code }} -- {{ resInfo }} </view>
  </view>
</template>

<script>
export default {
  data () {
    return {
      code: '',
      website_id: "",
      resInfo: ''
    }
  },
  onLoad (options) {
    console.log(12312);
    if (options.code) {
      this.code = options.code
      this.website_id = options.website_id
      // alert(this.code)
      this.login()
      return
    }
  },
  methods: {
    getInfo () {
      // this.$ajax.get(
      //   `/common/config/query/wx_public_web_login/client/176`,
      //   {},
      //   (res) => {
      //     if (res.statusCode === 200) {
      window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx30363bce2ccc42ef&redirect_uri=${window.location.href}&response_type=code&scope=snsapi_userinfo&state=#wechat_redirect`;
      // } else {
      //   uni.showToast({
      //     title: res.data.message,
      //     icon: "none",
      //   });
      // }
      // }
      //   );
    }
    ,
    copy () {
      this.$copyText(this.code, () => {
        uni.showToast({
          title: "复制成功",
          icon: "none",
        });
      });
    },
    login () {
      this.$ajax.post("/common/poster/login", { website_id: this.website_id, code: this.code }, res => {
        console.log(res);
        if (res.status == 200) {
          this.resInfo = JSON.stringify(res.data)
        }
      })
    }
  }
}
</script>

<style>
</style>