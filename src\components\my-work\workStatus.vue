<template>
<view class="current-box" @click.stop="()=>{}" v-if="isShow">
    <view class="current-label" :class="{opened: showDialog, 'is-normal': curStatus==1}" @click="switchDialog">
        <view class="dot"></view>
        <view class="title">{{title}}</view>
        <view class="icon"></view>
    </view>
    <view class="poper-wrapper" v-if="showDialog">
        <view class="poper">
            <view class="poper-item" @click="setWorkStatus(1)">正常接单</view>
            <view class="poper-item" @click="setWorkStatus(0)">暂停接单</view>
        </view>
    </view>
</view>
</template>
<script>
import { mapState } from 'vuex';
export default {
    props: {
        status: { type: Number, default: 0 },
        dialogVisible: { type: Boolean, default: false },
    },
    data(){
        return {
            isShow: false,
            showDialog: false,
            switching: false,
            curStatus: 0,
        }
    },
    computed: {
        ...mapState(['crmConfig']),
        title(){
            if(this.switching){
                return '切换中'
            }
            return this.curStatus == 1 ? '正常' : '暂停';
        }
    },
    watch: {
        dialogVisible(val) {
            this.showDialog = val;
        },
        showDialog(val){
            this.$emit('update:dialogVisible', val);
        },
        status: {
            handler(val) {
                if(val == 1 || val == 0){
                    this.curStatus = val; 
                    //成员自主接单开关
                    if(this.crmConfig.is_auto_allocation){
                        this.isShow = true;
                    }
                }
            },
            immediate: true
        }
    },
    methods: {
        switchDialog(){
            if(this.switching){
                return;
            }
            this.showDialog = !this.showDialog
        },
        //设置接单状态
        setWorkStatus(status){
            this.showDialog = false;
            if(status === this.curStatus){
                return;
            }
            
            let content = status== 0? '更改为暂停接单状态，系统将不在分配客资信息, 是否继续?': '更改为正常接单状态，系统将正常分配客资信息, 是否继续?';
            uni.showModal({
                title: "提示",
                content: content,
                cancelText: "取消",
                confirmText: "确定",
                success: (e) => {
                  if(e.confirm){
                    this.switchWorkStatus(status);
                     
                  }else{
                  
                  }
                }
            })
        },
        switchWorkStatus(status){
            this.switching = true;
            try{
                this.$ajax.post('/admin/crm/config/update_allocation', {is_allocation: status}, res => {
                    this.switching = false;
                    if(res.statusCode == 200){
                        this.curStatus = status;
                        uni.showToast({
                            title: res.data?.msg || '设置成功',
                            icon: 'none'
                        })
                    }else{
                        uni.showToast({
                            title: res.data?.message || '设置失败',
                            icon: 'none'
                        })
                    }
                }, err => {
                    this.switching = false;
                })
            }catch(err){
                this.switching = false;
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.current-box{
    position: relative;
    .current-label{
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 56rpx;
    margin: 0 36rpx;
    &.opened{
        border-color: #aaa;
        .icon{
            margin-top: -5px;
            transform: rotate(180deg);
        }
    }
    &.is-normal{
        .dot{
            background-color: #488AF6;    
        }
        .title{
            color: #488AF6;
        }
    }
    .dot{
        width: 14rpx;
        height: 14rpx;
        border-radius: 50%;
        background-color: #ccc;
    }
    .title{
        color: #aaa;
        padding: 0 2rpx 0 10rpx;
        display: inline-block;
        white-space: nowrap;
        font-size: 28rpx;
    }
}
.poper-wrapper{
    position: absolute;
    z-index: 2;
    top: 60rpx;
    right: 0; 
    padding-top: 6rpx;
    .poper{
        background-color: #fff;
        box-shadow: 0px 0px 8px 0px #0000003f;
        border-radius: 16rpx;
        .poper-item{
            white-space: nowrap;
            padding: 28rpx 80rpx;
            text-align: center;
            font-size: 30rpx;
            +.poper-item{
                border-top: 1px solid #e9e9e9;
            }
        }
    }
    }
}
</style>