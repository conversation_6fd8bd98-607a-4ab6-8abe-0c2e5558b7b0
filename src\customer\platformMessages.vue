<template>
    <view style="padding-top: 40rpx;margin-bottom: 180rpx;">
        <view class="center" v-for="item in MessagesList" :key="item.id" v-if="Array.isArray(MessagesList)&&MessagesList.length">
            <view class="tiem">{{ time }}</view>
            <view class="characters">
                <text class="message-text">
                    {{ item.content }}
                </text>
            </view>
        </view>
        <view class="center message-text" style="margin: 0 auto;"  v-if="MessagesList.length == 0">暂无消息</view> 
    </view>
</template>
<script>
export default {
    data() {
        return {
            // 消息列表
            MessagesList: [],
            time: '',
        }
    },
    created() {
        this.getMessagesList()
        this.messageFn()
        uni.showLoading({
        title:"加载中",
         })
    },
    onUnload(){
        uni.$emit('getDataload')
    },
    methods: {
        // 消息列表
        getMessagesList() {
            this.$ajax.get('/qywx/message/letter/search', { page: 1, per_page: 10, platform: '1' }, (res) => {
                console.log(res.data.data, '消息列表');
                if (res.statusCode === 200) {
                this.MessagesList = res.data.data
                this.MessagesList.forEach(element => {
                    // console.log(element.created_at);
                    let time = element.created_at.split(" ")
                    //    console.log(time,'截取时间');
                    let ddd = time[1].split(':')
                    let aa = ddd[0] + ':' + ddd[1]
                    //    console.log(aa);

                    let day = time[0].split('-')
                    //    console.log(day,'年月');
                    let mm = day[1]
                    let mmm = day[1].split('')
                    let mmmm = mmm[1] + '月'
                    //    console.log(mmmm);
                    let dd = day[2] + '日'
                    let all = mmmm + dd
                    this.time = all + ' ' + aa
                });
                uni.hideLoading()
            }
            },
            ()=>{
                uni.hideLoading()
            })
        },
         // 修改消息为已读
         messageFn(id) {
            this.$ajax.get(`/qywx/message/letter/quick_read`, {platform:'1'}, (res) => {
                console.log(res, '点击已读');
            })
        },
    }
}

</script>
<style lang="scss">
page {
    background: #F6F6F6;
}

.text {
    margin-top: 100rpx;
    margin-bottom: 30px;
    color: #000;
    text-align: center;
    margin-top: 40rpx;
    font-family: PingFang SC;
    font-size: 36rpx;
    font-style: normal;
    font-weight: 700;
    line-height: 36rpx;
    /* 100% */
}

.system-text {
    width: 100%;
    height: 172rpx;
    margin-bottom: 90rpx;
    background-color: #fff;
}

.center {
    margin: 0 32rpx;
}

.tiem {
    color: rgba(41, 44, 57, 0.40);
    text-align: center;
    font-family: PingFang SC;
    font-size: 24rpx;

    font-style: normal;
    font-weight: 400;
    line-height: 36rpx;
    /* 150% */
}

.characters {
    padding: 32rpx;
    margin: 24rpx 0;
    background-color: #fff;
}

.message-text {
    color: #292C39;
    font-family: PingFang SC;
    font-size: 32rpx;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}
</style>