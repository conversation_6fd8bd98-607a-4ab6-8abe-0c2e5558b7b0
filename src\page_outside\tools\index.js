/**
 * <AUTHOR>
 * @date 2020-04-21 10:19:07
 * @desc 常用工具
 */

import config from "@/page_outside/config/index";
// 判断是不是数组
const isArray =
  Array.isArray ||
  function (obj) {
    return obj instanceof Array;
  };

// 判断资源是不是远程资源
const isHttp = function (val) {
  const httpArr = ["http", "https"];
  return httpArr.includes(val.split("://")[0]);
};

/**
 * 判断是否是视频
 * @param {String} url 资源地址
 */
const isVideo = function (url) {
  const videoArr = ["mp4", "avi"];
  const varr = url.split(".");
  const name = varr[varr.length - 1];
  return videoArr.includes(name);
};

/**
 * 图片处理
 * @param {String} url 图片或视频地址
 * @param {String} param 图片后面的参数
 */
const formatImg = function (url, param = "w_8601") {
  if (!url || typeof url !== "string") {
    return require("../../static/icon/none.png");
  }
  if (isHttp(url)) {
    // let reg = new RegExp(/\?.+\=/)
    let reg = new RegExp(/\?.+=/);
    if (reg.test(url)) {
      //如果链接中有参数则直接返回不需要再加参数
      return url;
    }
    if (isVideo(url)) {
      return url + config.ossSnapshot;
    }
    if (param) {
      if (url.split(".")[url.split(".").length - 1] === "gif") {
        return url + config.thumbParam + "w_1200";
      } else {
        return url + config.thumbParam + param;
      }
    }
    return url;
  } else {
    if (isVideo(url)) {
      return config.imgDomain + url + config.ossSnapshot;
    }
    if (param) {
      if (url.split(".")[url.split(".").length - 1] === "gif") {
        return config.imgDomain + url + config.thumbParam + "w_1200";
      } else {
        return config.imgDomain + url + config.thumbParam + param;
      }
    }
    return config.imgDomain + url;
  }
};

/**
 * 获取小程序码中的参数
 * @param {String} scene 页面后面的参数
 */
const getSceneParams = function (scene) {
  var sceneParams = {};
  var params = scene.split("&");
  for (var i = 0; i < params.length; i++) {
    sceneParams[params[i].split("=")[0]] = unescape(params[i].split("=")[1]);
  }
  return sceneParams;
};

/**
 * 路由跳转
 * @param {String} path 页面路径
 * @param {String} anim_type 动画效果（仅app支持）
 */

const navigateTo = function (path, anim_type = "pop-in") {
  if (!path) {
    return;
  }
  // 如果是超链接则打开webView页面
  if (isHttp(path)) {
    // #ifdef H5
    window.location.href = path;
    // #endif
    // #ifndef H5
    navigateTo("/pages/web_view/web_view?url=" + encodeURIComponent(path));
    // #endif
    return;
  }
  const reg = /\?.+=.{0,}/;
  // 如果用户清除了浏览器缓存，则将全局变量存储的siteID重新存储到缓存中
  // if (["wxwork", "com-wx-pc"].includes(isWxWork())) {
  //   if (reg.test(path)) {
  //     path += `&website_id=${uni.getStorageSync("wxwork_id") || 1}`;
  //   } else {
  //     path += `?website_id=${uni.getStorageSync("wxwork_id") || 1}`;
  //   }
  // } else {
    // if (reg.test(path)) {
    //   if (uni.getStorageSync("corp_id")) {
    //     // 如果存储corp_id链接拼接参数
    //     path += `&website_id=${getQueryString("website_id") ||
    //       1}&corp_id=${uni.getStorageSync("corp_id")}`;
    //   } else {
    //     path += `&website_id=${getQueryString("website_id") || 1}`;
    //   }
    // } else {
    //   if (uni.getStorageSync("corp_id")) {
    //     path += `?website_id=${getQueryString("website_id") ||
    //       1}&corp_id=${uni.getStorageSync("corp_id")}`;
    //   } else {
    //     path += `?website_id=${getQueryString("website_id") || 1}`;
    //   }
    // }
  // }
  console.log(path,'123');
  uni.navigateTo({
    url: path,
    animationType: anim_type,
    animationDuration: 320,
  });
};

/**
 * 提示框
 * @param {Object} obj
 */
const showModal = function (obj) {
  uni.showModal({
    title: obj.title || "提示",
    content: obj.content || "",
    showCancel: obj.showCancel || true,
    cancelText: obj.cancelText || "取消",
    cancelColor: obj.cancelColor || "#333333",
    confirmText: obj.confirmText || "确定",
    confirmColor: obj.confirmColor || config.modalBtnColor,
    success: (res) => {
      if (res.confirm) {
        obj.confirm();
      } else {
        if (obj.cancel) {
          obj.cancel();
        }
        console.log("用户点击取消");
      }
    },
  });
};

/**
 * 防抖
 * @param {*} fun 需要执行的方法
 * @param {Number} delay 延迟时间
 */
function debounce (fun, delay) {
  return function (args) {
    let that = this;
    let _args = args;
    clearTimeout(fun.id);
    fun.id = setTimeout(function () {
      fun.call(that, _args);
    }, delay);
  };
}

/**
 * <AUTHOR>
 * @date 2020-05-09 15:49:10
 * @desc 动态加载远程js文件
 */
const loadRemoteJs = function (src, id) {
  return new Promise((resolve) => {
    id && document.getElementById(id) && document.getElementById(id).remove();
    var script = document.createElement("script");
    script.src = src;
    if (id) {
      script.id = id;
    }
    var s = document.getElementsByTagName("script")[0];
    s.parentNode.insertBefore(script, s);
    if (script.readyState) {
      script.onreadystatechange(() => {
        if (
          script.readyState === "complete" ||
          script.readyState === "loaded"
        ) {
          resolve();
        }
      });
    } else {
      script.onload = function () {
        resolve();
      };
    }
  });
};

/**
 * <AUTHOR>
 * @date 2020-05-11 11:59:51
 * @desc 将16进制颜色转换成rgba
 */
const hexColorToRgba = function (hexColor, alphaMaxVal = 1) {
  if (hexColor.indexOf("#") !== 0) {
    return hexColor;
  }
  hexColor = hexColor.replace("#", "");

  // 用于分割16进制色彩通道
  const reg = new RegExp("\\w{1,2}", "g");
  // 分割颜色通道
  let rgbaArray255 = hexColor.match(reg);
  rgbaArray255 = rgbaArray255.map((channel, index) => {
    // 计算每个通道的10进制值
    const colorVal = parseInt(channel, 16);
    if (index === 3) {
      // 这是alpha通道
      return Math.round((colorVal / (255 / alphaMaxVal)) * 100) / 100;
    }
    return colorVal;
  });
  return rgbaArray255;
  // return 'rgba(' + rgbaArray255.join(',') + ')'
};

// 查看预览图
const previewImage = function (img) {
  this.previewImage(img);
};

//1. 通过value获取到数据列表中对应显示的字段
//用法示例：let list = [{id: 1, name: '深圳'}, {id: 2, name: '广州'}]
//getDataName({ dataList: arr, value: "id", label: "name", data: 1 }); // 深圳
//getDataName({ dataList: arr, value: "id", label: "name", data: 2 }); // 广州

/**
 * 通过value找到在列表中对应的名字
 * @param {Object} obj
 *  @param obj.dataList 数据列表
 *  @param obj.value    数据的值对应的字段名称   例如 'value'
 *  @param obj.label    数据的说明对应的字段名称 例如 'label'
 *  @param obj.data     当前传入的数据值
 * @return name        返回当前传入值在数组中对应的名字
 */
const getDataName = function (obj) {
  let name = obj.data;
  if (Array.isArray(obj.dataList) && obj.dataList.length > 0) {
    for (let i = 0; i < obj.dataList.length; i++) {
      if (obj.dataList[i][obj.value] == obj.data) {
        name = obj.dataList[i][obj.label];
      }
    }
  }
  return name;
};


// 数组分类  array=>数组   name=>对应属性key
const groupBy = function (array, name) {
  const groups = {};
  array.forEach(function (o) {
    const group = JSON.stringify(o[name]);
    groups[group] = groups[group] || [];
    groups[group].push(o);
  });
  return Object.keys(groups).map(function (group) {
    return groups[group];
  });
};

// 获取当前时间
const getTime = function (type) {
  var date = new Date(),
    year = date.getFullYear(),
    month = date.getMonth() + 1,
    day = date.getDate(),
    hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours(),
    minute =
      date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes(),
    second =
      date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  month >= 1 && month <= 9 ? (month = "0" + month) : "";
  day >= 0 && day <= 9 ? (day = "0" + day) : "";
  // + " " + hour + ":" + minute;
  if (type === "YMDhm") {
    return year + "-" + month + "-" + day + " " + hour + ":" + minute;
  } else if (type === "YMD") {
    return year + "-" + month + "-" + day;
  }
};

// 图片格式转base64
function getBase64 (src, callback, outputFormat) {
  var xhr = new XMLHttpRequest();
  xhr.open("GET", src, true);
  xhr.responseType = "arraybuffer";
  console.log(xhr);
  xhr.onload = function (e) {
    if (xhr.status == 200) {
      var uInt8Array = new Uint8Array(xhr.response);
      var i = uInt8Array.length;
      var binaryString = new Array(i);
      while (i--) {
        binaryString[i] = String.fromCharCode(uInt8Array[i]);
      }
      var data = binaryString.join("");
      var base64 = window.btoa(data);
      var dataUrl =
        "data:" + (outputFormat || "image/png") + ";base64," + base64;
      callback.call(this, dataUrl);
    }
  };

  xhr.send();
}

/**
 *  @title 后台接口请求字典数据
 *  @per_page {200} 一页显示的总数量
 *  @description 页面需要字典数据类型多的情况下使用，其他则使用 $getDictionaryList
 *  @method use this.$setDictionary(e=>{})
 * */

const setDictionary = function (callback) {
  let data = getApp().globalData.dictionary_data;
  const arr = [];
  if (data.length === 0) {
    this.$ajax.get("/common/dictionary/all", {}, (res) => {
      if (res.statusCode === 200) {
        const arr = [];
        getApp().globalData.dictionary_data = res.data; //全局变量不存在字典数据则进行赋值
        res.data.forEach((item) => {
          const parent = arr.find((cur) => cur.name === item.name);
          if (parent) {
            parent.childs.push(item);
          } else {
            const obj = {
              name: item.name,
              childs: [item],
            };
            arr.push(obj);
          }
        });
        callback(arr);
      }
    });
  } else {
    data.forEach((item) => {
      const parent = arr.find((cur) => cur.name === item.name);
      if (parent) {
        parent.childs.push(item);
      } else {
        const obj = {
          name: item.name,
          childs: [item],
        };
        arr.push(obj);
      }
    });
    callback(arr);
  }
};
/**
 * @date 2020/10/24
 * @description 文字识别
 * @content 粘贴内容
 * @arr 需要识别内容存入数组
 * @result 将处理好数据存入result
 * */

const IdentifyTools = function (content, arr, result) {
  var _arr = arr;
  var arr = content.split("\n");
  arr.map((item) => {
    var content = item.match(/】(\S*)/)[1];
    var label = item.match(/【(\S*)】/)[1];
    _arr.map((item2) => {
      if (item2.label === label) {
        item2.value = content;
        return result.push(item2);
      }
    });
  });
};

// 匹配楼盘属性标签
/**
 * @date 2020/10/27
 * @arr1 需要匹配的楼盘列表
 * @arr2 匹配楼盘属性的字典列表  期房排卡
 * @arr3 匹配楼盘类型的字典列表  住宅
 * */

const matchBuildType = function (arr1, arr2, arr3) {
  arr1.map((item, index) => {
    arr2.map((item2) => {
      if (item.build_status == item2.value) {
        return (item.build_status = item2.description);
      }
    });
  });
};

// 时间运算增加时间  /分钟
const addTime = function (time, min) {
  var time = time.replace(/-/g, "/");
  var update_time = new Date(time).getTime();
  var delay_time = (min *= 60) * 1000;
  var new_time = update_time + delay_time;
  return getTimeStr(new_time);
};

// 时间戳转换
function getTimeStr (timeStamp) {
  var now = new Date(timeStamp),
    y = now.getFullYear(),
    m = ("0" + (now.getMonth() + 1)).slice(-2),
    d = ("0" + now.getDate()).slice(-2);

  return y + "-" + m + "-" + d + " " + now.toTimeString().substr(0, 8);
}

// 验证辅助客户手机号
const handlePhoneArrVerification = function (arr) {
  let jsonArr = JSON.parse(arr);
  let result;
  jsonArr.map((item) => {
    if (!/^1[3456789]/.test(item.phone)) {
      uni.showToast({
        title: "客户" + item.name + "：" + item.phone + "手机号格式不正确",
        icon: "none",
      });
      result = false;
    }
  });
  return result;
};

// 提交访客记录
const createVisitorsRecord = function (data, callback) {
  this.$ajax.post("/client/user/visitor/record", data, (res) => {
    callback(res);
    if (res.statusCode === 200) {
      console.log("创建一条访客记录");
    } else {
      uni.showToast({
        title: res.data.message || "Visitor Record Error",
        icon: "none",
      });
    }
  });
};

const getUnreadMsg = function () {
  this.$ajax.get("/client/im/session/unread_total", {}, (res) => {
    if (res.statusCode === 200) {
      if (res.data.total > 0) {
        uni.setTabBarBadge({
          index: 2,
          text: res.data.total + "",
        });
      } else {
        uni.removeTabBarBadge({ index: 2 });
      }
    }
    this.$store.commit("setUnreadMsg", res.data.total + "");
  });
};


// 复制内容
const copyText = function (cont, callback) {
  // #ifdef H5
  let oInput = document.createElement("textarea");
  oInput.value = cont;
  document.body.appendChild(oInput);
  oInput.select(); // 选择对象;
  oInput.setSelectionRange(0, oInput.value.length);
  document.execCommand("Copy"); // 执行浏览器复制命令
  oInput.blur();
  oInput.remove();
  if (callback) callback();
  // #endif
  // #ifndef H5
  uni.setClipboardData({
    data: cont,
    success: () => {
      if (callback) callback();
    },
  });
  // #endif
};

const sortPro = function (data, keys = []) {
  //keys可以传一个数组
  var c = [];
  var d = {};
  for (var element of data) {
    let element_keyStr = "";
    let element_key = [];
    let element_keyObj = {};
    for (var key of keys) {
      element_key.push(element[key]);
      element_keyObj[key] = element[key];
    }
    element_keyStr = element_key.join("_");
    if (!d[element_keyStr]) {
      c.push({
        ...element_keyObj,
        children: [element],
      });
      d[element_keyStr] = element;
    } else {
      for (var ele of c) {
        let isTrue = keys.some((key) => {
          return ele[key] != element[key];
        });
        if (!isTrue) {
          ele.children.push(element);
        }
      }
    }
  }
  return c;
};



// 城市区域按照城市名称首字母进行排序
const pySegSort = function (arr, empty) {
  if (!String.prototype.localeCompare) return null;
  var letters = "#ABCDEFGHJKLMNOPQRSTWXYZ".split("");
  var en = "abcdefghjklmnopqrstwxyz".split("");
  var zh = "阿八嚓哒妸发旮哈讥咔垃痳拏噢妑七呥扨它穵夕丫帀".split("");
  // 先将其中的 字母找出来排序
  let segs2 = [];
  let curr2;
  letters.filter((i, v) => {
    curr2 = { letters: i, dat: [] };
    arr.filter((i2, v2) => {
      if (
        (!en[v - 1] || en[v - 1].localeCompare(i2.pinyin) <= 0) &&
        i2.pinyin.localeCompare(en[v]) == -1
      ) {
        curr2.dat.push(i2);
      }
      if (empty || curr2.dat.length) {
        segs2.push(curr2);
        curr2.dat.sort(function (a, b) {
          return a.pinyin.localeCompare(b);
        });
      }
    });
  });
  //将其中相同项合并  将中文其他字符取出 cnchar
  let arry2 = [];
  let cnChar = [];
  for (let i in segs2) {
    i = parseInt(i);
    if (segs2[i] && segs2[i + 1] && segs2[i].letters == segs2[i + 1].letters) {
    } else {
      if (segs2[i].letters == "#") {
        cnChar = cnChar.concat(segs2[i].dat);
      } else {
        arry2.push(segs2[i]);
      }
    }
  }
  // 对中文进行排序
  let segs = [];
  let curr;
  letters.filter((item, index) => {
    curr = { letters: item, dat: [] };
    cnChar.filter((item2, index2) => {
      if (
        (!zh[index - 1] || zh[index - 1].localeCompare(item2.name) <= 0) &&
        item2.name.localeCompare(zh[index]) == -1
      ) {
        curr.dat.push(item2);
      }
      if (empty || curr.dat.length) {
        segs.push(curr);
        curr.dat.sort(function (a, b) {
          return a.name.localeCompare(b);
        });
      }
    });
  });
  // 对相同项进行合并
  let arry = [];
  for (let i in segs) {
    i = parseInt(i);
    if (segs[i] && segs[i + 1] && segs[i].letters == segs[i + 1].letters) {
    } else {
      if (segs[i]) {
        arry.push(segs[i]);
      }
    }
  }
  //将中文排序数组英文排序数组合并
  let sel = arry.concat(arry2);
  for (let i = 0, l = sel.length; i < l; i++) {
    i = parseInt(i);
    for (let j = i + 1, l = sel.length; j < l; j++) {
      if (sel[i].letters == sel[j].letters) {
        sel[i].dat = sel[i].dat.concat(sel[j].dat);
        sel.splice(j, 1);
        l--;
        j--;
      }
    }
  }
  // 排序js里面字符#<a    a<b以此类推
  let len = sel.length;
  for (let i = 0; i < len - 1; i++) {
    for (let j = 0; j < len - 1 - i; j++) {
      // 相邻元素对比，元素交换，大的元素交换到后面
      if (sel[j].letters > sel[j + 1].letters) {
        let temp = sel[j];
        sel[j] = sel[j + 1];
        sel[j + 1] = temp;
      }
    }
  }
  return sel;
  // 返回排序集合
};

const getQueryString = function (name) {
  if(name == 'website_id') return uni.getStorageSync("wxwork_id") || '';
  return '';
};

module.exports = {
  isArray,
  isHttp,
  isVideo,
  formatImg,
  getSceneParams,
  navigateTo,
  showModal,
  debounce,
  loadRemoteJs,
  hexColorToRgba,
  previewImage,
  getDataName,
  getQueryString,
  groupBy,
  getTime,
  getBase64,
  setDictionary,
  IdentifyTools,
  matchBuildType,
  addTime,
  handlePhoneArrVerification,
  createVisitorsRecord,
  getUnreadMsg,
  // isWxWork,
  copyText,
  sortPro,
  // bindingPublic,
  pySegSort,
};
