<template>
  <view class="container">
    <!-- 顶部提示信息 -->
    <view v-if="top_tip" class="top-tip">
      <text class="tip-text">{{top_tip}}</text>
    </view>
    
    <view class="list">
      <!-- 资料列表 -->
      <view class="card" @click="handleDataList">
        <view class="left">
          <image class="icon" src="https://img.tfcs.cn/materials/collect_document/map_info.png" mode="aspectFit"></image>
          <view class="content">
            <text class="title">资料列表</text>
            <text class="desc">选择单个资料发送客户</text>
          </view>
        </view>
        <view class="right">
          <text class="arrow-text">></text>
        </view>
      </view>

      <!-- 资料包 -->
      <view class="card" @click="handleDataPackage">
        <view class="left">
          <image class="icon" src="https://img.tfcs.cn/materials/collect_document/map_package.png" mode="aspectFit"></image>
          <view class="content">
            <text class="title">资料包</text>
            <text class="desc">选择资料包发送客户</text>
          </view>
        </view>
        <view class="right">
          <text class="arrow-text">></text>
        </view>
      </view>

      <!-- 手机号授权验证组件 -->
      <view 
        v-if = "isShowPhoneAuth"
        class="card" 
        @click="handlePhoneAuth">
        <view class="left">
          <image class="icon" src="https://img.tfcs.cn/materials/collect_document/map_pay.png" mode="aspectFit"></image>
          <view class="content">
            <text class="title">手机号授权验证组件</text>
            <text class="desc">剩余次数1000</text>
          </view>
        </view>
        <view class="right">
          <text class="recharge">充值</text>
          <text class="arrow-text">></text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      isShowPhoneAuth:false,
      top_tip:''
    }
  },
  onLoad() {
    this.getTopTip()
  },
  methods: {
    getTopTip() {
      this.$ajax.get('/admin/map_plugin/tips', {}, res => {
        if(res.statusCode == 200) {
          this.top_tip = res.data
        }
      })
    },
    handleDataList() {
      this.$navigateTo("/customer/map_plugin_list")
    },
    handleDataPackage() {
      this.$navigateTo("/customer/map_package_list")
    },
    handlePhoneAuth() {
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #FFFFFF;
  padding: 24rpx;
}

.top-tip {
  background-color: #E6F7FF;
  border: 2rpx solid #BAE7FF;
  border-radius: 8rpx;
  padding: 20rpx 24rpx;
  margin-bottom: 24rpx;
  
  .tip-text {
    font-size: 28rpx;
    color: #1890FF;
    line-height: 1.5;
  }
}

.list {
  .card {
    background: #FFFFFF;
    border-radius: 8rpx;
    margin-bottom: 2rpx;
    padding: 32rpx;
    border: none;
    border-bottom: 2rpx solid #F5F5F5;
    
    /* 强制水平布局 */
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between;
    align-items: center;
    
    &:active {
      background-color: #F8F8F8;
    }
  }

  .left {
    display: flex !important;
    flex-direction: row !important;
    align-items: center;

    .icon {
      width: 80rpx;
      height: 80rpx;
      margin-right: 24rpx;
    }

    .content {
      display: flex !important;
      flex-direction: column !important;

      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        margin-bottom: 12rpx;
      }

      .desc {
        font-size: 28rpx;
        color: #999999;
      }
    }
  }

  .right {
    display: flex !important;
    flex-direction: row !important;
    align-items: center;

    .recharge {
      font-size: 28rpx;
      color: #0174FF;
      margin-right: 24rpx;
    }

    .arrow-text {
      color: #CCCCCC;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}
</style>
