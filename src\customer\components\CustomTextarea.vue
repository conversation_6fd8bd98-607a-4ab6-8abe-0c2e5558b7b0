<template>
    <view>
        <!-- 文本域 底部显示数字 -->
      <textarea v-model="text" @input="updateCount"></textarea>
      <view>{{ count }} / {{ max }}</view>
    </view>
  </template>
  
  <script>
  export default {
    props: {
      value: String, // 后端返回的数据
      max: Number, // 最大字数限制
    },
    data() {
      return {
        text: this.value, // 绑定输入的文本
        count: this.value ? this.value.length : 0, // 初始化字数计数
      };
    },
    methods: {
      updateCount() {
        this.count = this.text.length; // 更新字数计数
        this.$emit('input', this.text); // 发送文本数据到父组件
      },
    },
  };
  </script>