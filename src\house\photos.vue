<template>
  <view class="photos">
    <view class="filters flex-row items-center">
      <!-- <view class="filter_t"> 分类 </view> -->
      <view class="filter_list flex-1 flex-row items-center">
        <view
          class="filter_item"
          :class="{ active: currentCate == 'all' }"
          @click="filterCate({ values: 'all' })"
        >
          全部
        </view>
        <view
          class="filter_item"
          v-for="item in cateList"
          :key="item.values"
          :class="{ active: currentCate == item.values }"
          @click="filterCate(item)"
          >{{ item.name }}
        </view>
      </view>
      <view class="pop" @click="showPop"><my-icon type="caidan" size="52rpx"></my-icon> </view>
    </view>
    <view class="photoList">
      <view class="photo" v-for="(item, index) in cateList" :key="index">
        <template v-if="currentCate == 'all' || currentCate == item.values">
          <view class="title"> {{ item.name }}</view>
          <view class="photo_item">
            <upload
              :del="false"
              :upInfo="upInfo"
              @uploadDone="uploadSuccess($event, item.values)"
              :action="upload_api"
              :imgs="filterImgs(item.values)"
              :chooseType="1"
              :disabled="
                (detail.unilateral_agent == 1 && detail.unilateral_agent_auth == 0) ||
                detail.protect == 1
              "
              :disabledText="detail.protect == 1 ? '房源处于保护期内不能上传' : '请联系维护人'"
            ></upload>
            <!--:showAdd="!(detail.unilateral_agent == 1 && detail.unilateral_agent_auth == 0)" @uploadDon="uploadDon($event, item.value)"
            :imgs="filterImgs(item.value)" -->
          </view>
        </template>
      </view>
    </view>
    <my-popup position="top" top="0" top_0 :show="show_pop" @close="show_pop = false">
      <view class="filters pop">
        <view class="filter_list flex-1 flex-row items-center flex-wrap">
          <view
            class="filter_item"
            :class="{ active: currentCate == 'all' }"
            @click="filterCate({ values: 'all' })"
          >
            全部
          </view>
          <view
            class="filter_item"
            v-for="item in cateList"
            :key="item.values"
            :class="{ active: currentCate == item.values }"
            @click="filterCate(item)"
            >{{ item.name }}
          </view>
        </view>
        <view class="close flex-row items-center justify-center" @click="show_pop = false">
          <my-icon type="guanbi" size="30rpx" color="#999"></my-icon>
        </view>
      </view>
    </my-popup>
  </view>
</template>

<script>
import upload from "./components/upload.vue";
import myPopup from './components/myPopup'
import myIcon from '@/components/my-icon'
export default {
  components: {
    upload,
    myPopup,
    myIcon
  },
  data () {
    return {
      photoList: [],
      cateList: [],
      upInfo: {
        category: 6,
      },
      upload_api: '/common/file/upload/admin',
      currentCate: "all",
      show_pop: false,
      detail: {}

    }
  },
  onLoad (options) {
    if (options.id) {
      this.id = options.id
      uni.showLoading({
        title: '加载中',
        mask: true
      })
      this.getDetail()
    }
  },
  methods: {
    getPhotos () {
      this.$ajax.get(
        `/admin/house/housePhotoSk/${this.id}`,
        {}, res => {
          if (res.statusCode == 200) {
            // let photoList = res.data
            let photoList = res.data.photo_first.list
            if(res.data.photos&&res.data.photos.length){
              photoList = photoList.concat(res.data.photos)
            }
            this.photoList = photoList
            if(photoList && photoList.length) {
              this.photosImg = photoList.map(item => item.url)
            } else {
              this.photosImg = [];
            }
            // this.handleImgs(photoList)
          }
          uni.hideLoading()
        }, () => { uni.hideLoading() })
    },
    uploadSuccess (e, type) {
      console.log(e, type);
      let arr = []
      console.log(this.photosImg,"this.photosImg")
      let imgurl = e.filter(item => !this.photosImg.includes(item))
      console.log(imgurl);
      imgurl.map(item => {
        let obj = {
          url: item,
          descp: '',
          is_cover: 0,
          category_id: type
        }
        arr.push(obj)
      })
      this.uploadImg(JSON.stringify(arr))
    },
    uploadImg (pic) {
      this.$ajax.post(`/admin/house/privateHousesUpPic/${this.id}`, { pic }, (res) => {
        if (res.statusCode == 200) {
          uni.showToast({
            title: res.message || '上传成功',
            icon: 'none',
          })
          // this.getPhotos()
        }
      })
    },
    filterImgs (type) {
      let list = []
      let filters = null
      if(this.photoList) {
        filters = this.photoList.filter(item => item.category_id == type)
      }
      if (filters && filters.length) {
        list = filters.map(item => item.url)
      }
      return list
    },
    filterCate (item) {
      this.currentCate = item.values
      if (this.show_pop) {
        this.show_pop = false
      }
    },
    handleImgs (pics = this.photoList) {
      if (pics.length == 0) {
        return
      }
      this.cusBtn = []
      let pic = [],
        huxing = [],
        shiwai = []
      pics.map((item) => {
        if (item.category_id == 1) {
          pic.push({
            type: 1,
            name: "室内图",
            url: item.url
          })
        } else if (item.category_id == 2) {
          huxing.push({
            type: 2,
            name: "户型图",
            url: item.url
          })
        } else {
          shiwai.push({
            type: 3,
            name: "室外图",
            url: item.url
          })
        }
      })
      this.focus = pic.concat(huxing).concat(shiwai)
      this.focusLen = this.focus.length
      this.picLen = pic.length
      this.huxingLen = huxing.length
      this.shiwaiLen = shiwai.length
      if (this.picLen > 0) {
        this.cusBtn.push({
          name: '室内图',
          value: 1,
        })

      }

      if (this.huxingLen > 0) {
        this.cusBtn.push(
          {
            name: '户型图',
            value: 2,
          })

      }
      if (this.shiwaiLen > 0) {
        this.cusBtn.push({
          name: '室外图',
          value: 3,
        })
      }
      this.cateActive = this.cusBtn[0].value
    },
    showPop () {
      this.show_pop = true
    },
    getDetail () {
      this.$ajax.get(
        `/admin/house/privateHousesDetail/${this.id}`,
        {},
        (res) => {
          this.loading = false
          if (res.statusCode === 200) {
            let catList = [{
              name: "室内图",
              values: 1,
            }, {
              name: "户型图",
              values: 2,
            }, {
              name: "室外图",
              values: 3,
            }]
            this.detail = res.data
            this.cateList = catList.concat(res.data.photo_cat)

          }
          this.getPhotos()
        },
        (err) => {
          this.getPhotos()
        }
      )

    }
  }

}
</script>

<style scoped lang="scss">
.photos {
  ::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
  }
  background: #f7f7f7;
  .filters {
    padding: 24rpx;
    position: sticky;
    background: #f7f7f7;
    top: 0;
    z-index: 2;
    justify-content: space-between;
    &.pop {
      background: #f7f7f7;
      height: 100vh;
      display: block;
      .close {
        position: absolute;
        top: 20rpx;
        right: 24rpx;
        width: 40rpx;
        height: 40rpx;
        padding: 10rpx;
        border-radius: 50%;
        background: #d8d8d8;
        color: #666;
      }
      .filter_list {
        padding-top: 50rpx;
        .filter_item {
          min-width: calc((100% - 48rpx - 48rpx) / 4);
          margin-bottom: 24rpx;
        }
      }
    }
    .filter_t {
      // min-width: 115rpx;
      padding-top: 10rpx;
      margin-right: 10rpx;
    }
    .filter_list {
      overflow-y: auto;
      .filter_item {
        padding: 20rpx;
        background: #fff;
        font-size: 22rpx;
        // text-align-last: justify;
        margin-right: 16rpx;
        text-align: center;
        // margin-bottom: 10rpx;
        min-width: 136rpx;
        color: #666;
        border-radius: 64rpx;
        &.active {
          background: #3e81d6;
          color: #fff;
        }
      }
    }
  }
  .photoList {
    padding: 10rpx 24rpx;
    .photo {
      .title {
        font-size: 30rpx;
        padding: 24rpx 0;
        color: #666;
      }
      .photo_item {
        // overflow: hidden;
        ::v-deep .img-box {
          margin-right: 24rpx;
          width: calc((100% - 72rpx) / 4);
          height: 0;
          padding-bottom: calc((100% - 72rpx) / 4);
          text-align: center;
          box-sizing: border-box;
          margin-bottom: 24rpx;
          // overflow: hidden;
          &:nth-child(4n) {
            margin-right: 0;
          }
          image {
            width: 100%;
            height: 100%;
            margin-right: 0;
            margin-top: 0;
            position: absolute;
          }
        }
        ::v-deep .icon-box {
          margin-top: 0;
          width: calc((100% - 72rpx) / 4);
          height: calc((100vw - 72rpx - 24rpx) / 4);
        }
      }
    }
  }
}
</style>