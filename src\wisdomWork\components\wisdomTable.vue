<template>
    <view>
        <view class="table">
            <view class="header" :style="{top: top}"  v-show="headers.length">
                <scroll-view :scroll-left="tableScrollLeft" scroll-x @scroll="handlerTableScroll">
                    <view class="row">
                        <view v-for="(item, index) of headers" :key="index" class="row-cell row-th" :class="[item.field, {sticky:item.fixed}]" :style="{width: (item.width || 120)+'px'}">  
                            <slot name="header" v-bind="{column:item, index}"> {{ item.label }} </slot>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <scroll-view class="body" :scroll-left="tableScrollLeft" scroll-x @scroll="handlerTableScroll"  v-show="list.length">
                <view class="row" v-for="(row, rowIndex) of list" :key="rowIndex">
                    <view v-for="(item, index) of row" :key="index" class="row-cell" :class="[item.field, {sticky:item.fixed}]" :style="{width: (item.width || 120)+'px'}">  
                        <view class="row-cell-inner" :style="{'text-align': item.align}">
                            <wisdomTableCellOverflow v-if="item.showOverflowPopup" :content="item.value">
                                <slot v-bind="{row:item, index}"></slot>
                            </wisdomTableCellOverflow>
                            <template v-else>
                                <slot v-bind="{row:item, index}"> {{ item.value }} </slot>
                            </template>
                        </view>
                    </view>
                </view>
            </scroll-view>
            <view class="summary" v-if="summary && list.length">
                <scroll-view :scroll-left="tableScrollLeft" scroll-x @scroll="handlerTableScroll">
                    <view class="row">
                        <view v-for="(item, index) of summaries" :key="index" class="row-cell row-th" :class="[item.field, {sticky:item.fixed}]" :style="{width: (item.width || 120)+'px'}">  
                            {{ item.value }}
                        </view>
                    </view>
                </scroll-view>
            </view>
        </view>
        

        <loadMore :status="loadStatus" show-load-more @load-more="getList"></loadMore>
       
    </view>
</template>

<script>
import wisdomTableCellOverflow from "./wisdomTableCellOverflow.vue";
import loadMore from "@/components/loadMore.vue";
export default {
    props: {
        api: { type: Function, default(){} },
        headers: { type: Array, default: ()=>[] },
        top: { type: String, default: '88rpx' },
        summary: { type: Boolean, default: false },
    },
    components: {
        wisdomTableCellOverflow,
        loadMore
    },
    data(){
        return {
            loading: false,             //是否加载中
            isEmpty: false,             //是否空数据
            isNoMore: false,            //是否有更多
            tableScrollLeft: 0,
			page: 1,
            list: []
        }
    },
    computed: {
        loadStatus(){
            if(this.loading) return 'loading';
            if(this.isEmpty) return 'empty';
            if(this.isNoMore) return 'nomore';
            return '';
        },
        summaries(){
            return this.headers.map((header, index) => {
                let value = index === 0 ? '合计' : this.list.reduce((prev, cur) => {
                    let val = cur[index].value;
                    if(!isNaN(val)){
                        return val + (prev || 0)
                    }
                    return prev;
                }, '')
                return {...header, value}
            })
        }
    },
    created(){
        this.getList();
    },
    methods: {
        async getList(){
            if(this.isNoMore){
                return;
            }
            this.loading = true;
            const page = this.page;
            page === 1 && (this.list = []);
            const res =  await this.api(page) || {};



            const list = (res.list || []).map(e => {
                return this.headers.map(header => {
                    return {
                        field: header.field,
                        value: e[header.field] ?? '',
                        width: header.width || 120,
                        fixed: header.fixed || false,
                        align: header.align || 'center',
                        showOverflowPopup: header.showOverflowPopup || false,
                        info: e
                    }
                })
            });

            this.list = this.list.concat(list);
            this.page++;
            this.loading = false;

            if (list.length === 0) {
                if(page === 1){
                    this.isEmpty = true;
                }
                this.isNoMore = true;
            }
            if(res.pageSize && list.length < res.pageSize){
                this.isNoMore = true;
            }
        },
        search(){
            this.page = 1;
            this.isEmpty = false;
            this.isNoMore = false;
            this.getList();
        },
        handlerTableScroll(e){
            this.tableScrollLeft = e.detail.scrollLeft
        }
    } 
}
</script>
<style lang="scss" scoped>
.table{
    .header,.summary{
        position: sticky;
        top: 0;
        z-index: 2;
        &.summary{
            bottom: 0;
        }
        .row{
            height: 88rpx;
            color: #86909C;
        }
    }
    .row{
        display: inline-flex;
        white-space: nowrap;
        height: 108rpx;
        
        .row-cell{
            &:first-child{
                border-left: 1px solid #f2f3f5;
            }
            &.sticky{
                position: sticky;
                z-index: 1;
                left: 0;
                /* box-shadow: 0 0 10px rgba(0,0,0,.12); */
            }
            &.row-th{
                background-color: #F7F8FA;
                border-top: 1px solid #f2f3f5;
            }
            display: inline-flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            background-color: #fff;
            border-right: 1px solid #f2f3f5;
            border-bottom: 1px solid #f2f3f5;
            padding: 8rpx 12rpx;
            .row-cell-inner{
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                text-align: center;
                width: 100%;
                white-space: normal;
                line-height: 1.2;

                overflow:hidden;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
            }
        }
    }
}
</style>