<template>
  <view class="list">
    <view class="filter common_style">
      <view class="filter_list flex-row items-center space-between">
        <view
          class="filter_item"
          v-for="item in filterList"
          :key="item.id"
          :class="{ active: currentFilter == item.values }"
          @click="changeFilter(item)"
        >
          <view class="filter_item_con">
            {{ item.name }}
          </view>
        </view>
      </view>
    </view>
    <view class="approval_list">
      <view
        class="approval_item"
        v-for="item in approvalList"
        :key="item.openid"
        @click="toApproval(item)"
      >
        <approvalItem :item="item"></approvalItem>
      </view>
      <loadMore :status="load_status" @reload="getApprovalList()" />
    </view>
  </view>
</template>

<script>
// import loadMore from '@/components/ui/loadMore'
// import approvalItem from '../components/approvalItem'
export default {
  components: {
    // loadMore,
    // approvalItem,
  },
  data: () => {
    return {
      load_status: 'loading',
      params: {
        page: 1,
        rows: 20,
      },

      user: {},

      approvalList: [],
      filterList: [],
      currentFilter: '-1',
    }
  },
  onLoad() {
    uni.$on('loginSuccess', () => {
      this.params.page = 1
      this.getFilterList()
      this.getData()
    })
    // this.getFilterList()
    this.getData()
  },
  onUnload() {
    uni.$off('loginSuccess')
  },
  computed: {},
  methods: {
    getFilterList() {
      this.$ajax.get('/v1/wapLm/approveStatus', {}).then((res) => {
        if (res.data.status == 200) {
          let filterArr = [
            {
              name: '全部',
              values: -1,
            },
          ]
          this.filterList = filterArr.concat(res.data.data.status)
        } else {
          this.concatList = []
        }
      })
    },
    getData() {
      let params = {
        page: this.params.page,
        rows: this.params.rows,
        status: this.currentFilter,
      }
      if (params.status == -1) {
        delete params.status
      }
      if (params.status == 0) {
        params.status = '0'
      }
      this.$ajax.get('/v1/wapLm/dealApproveList', params).then((res) => {
        if (res.data.status == 200) {
          this.approvalList = res.data.data
          if (res.data.data.length < this.params.rows) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'loadend'
          }
        } else {
          this.load_status = 'nomore'
          this.concatList = []
        }
      })
    },
    changeFilter(item) {
      this.currentFilter = item.values
      this.getData()
    },

    toApproval(item) {
      this.$navigateTo({
        name: 'approval_detail',
        query: {
          id: item.id,
        },
      })
    },
  },
  onReachBottom() {
    if (this.load_status == 'loadend') {
      this.params.page++
      this.getData()
    }
  },
}
</script>

<style lang="scss" scoped>
.list {
  padding: 0 48rpx 24rpx;
  &.shenpi {
    padding: 0 0 24rpx;
    background: #f8f8f8;
    // .tabs {
    //   padding: 5rpx 158rpx;
    // }
    .common_style {
      background: #fff;
      margin: 0 48rpx;
    }
    .approval_list {
      margin: 0 48rpx;
    }
  }
}
.concat_list {
  .no_data {
    text-align: center;
    color: #999;
  }
}

::v-deep .search {
  padding: 0;
}
.tabs {
  padding: 5rpx 110rpx;
  // padding: 0 48rpx;
  background: #fff;
}

.oper {
  .btn {
    background-color: $color-primary;
    background-image: linear-gradient(
      135deg,
      mix($color-primary, #fff, 65%) 0%,
      $color-primary 100%
    );
    box-shadow: 0 6rpx 12rpx -6rpx rgba($color-primary, 0.4);
    height: 48rpx;
    line-height: 48rpx;
    color: #fff;
    padding: 0 24rpx;
    border-radius: 6rpx;
    font-size: 22rpx;
    border-radius: 24rpx;
  }
}
.filter {
  .filter_list {
    background: #f8f8f8;
    padding: 24rpx 0;
    overflow-x: auto;
    .filter_item {
      padding: 4rpx 36rpx;
      background: #f6f6f6;
      border-radius: 24rpx;
      font-size: 28rpx;
      color: #8a929f;
      white-space: nowrap;
      &.active {
        background: #fff;
        color: #2d84fb;
      }
    }
  }
}
.approval_item {
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}
.footer_btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0 48rpx;
  background: #fff;
  z-index: 2;
}
.footer_btns {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx 0;
  color: #2d84fb;
  background: #f8f8f8;
  .ft_btn {
    padding: 20rpx 0;
  }
}
.select_member {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background: #fff;
}
.select_department {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  background: #fff;
}
</style>
