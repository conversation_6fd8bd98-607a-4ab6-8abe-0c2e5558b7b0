.container {
  view,
  text,
  image {
    box-sizing: border-box;
  }
  scroll-view {
    width: 100%;
    white-space: nowrap;
    background-color: #fff;
    position: relative;
    padding-top: 10px;

    // margin-top:10px;
    &::after {
      background: #e5e5e5;
      content: "";
      display: block;
      width: 100%;
      height: 1px;
      position: absolute;
      bottom: 0;
      left: 0;
      transform: scaleY(0.5);
    }
    .flex-box {
      display: inline-block;
      width: 25%;
      margin: 0 7rpx 0 7rpx;
      box-sizing: border-box;

      &.active {
        .date-box {
          border: none;
          .days {
            font-weight: bold;
            color: #818181;
          }
          .date {
            font-weight: bold;
            color: #818181;
          }
        }
      }
      .date-box {
        border: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-around;
        color: rgba(129, 129, 129, 1);
        .date {
          color: #818181;
        }
      }
    }
  }
  .time-box {
    // padding:28upx 12upx 26upx;
    display: flex;
    flex-wrap: wrap;
    // margin-top:10px;
    background-color: #fff;
    margin-top: 20px;
    .item {
      padding: 0 9upx;
      margin-top: 24rpx;
      &-box {
        width: 100%;
        background: #f8f8f8;
        border-radius: 4rpx;
        color: #333;
        border: 1px solid #eeeeee;
        font-size: 22rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 8rpx 12rpx;
        &.disable {
          background: #f1f3f6 !important;
          color: #999 !important;
          // border: 1px solid #EEEEEE;
        }
        &.active {
          // background: #0094D7;
          border: 1px solid #2d84fb;
          font-weight: bold;
        }
        .all {
          font-size: 22upx;
          padding-top: 5px;
        }
      }
    }
  }
}
