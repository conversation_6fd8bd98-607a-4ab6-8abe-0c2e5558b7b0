<template>
  <view class="dealList">
    <scroll-view scroll-y="true" class="box">
      <view class="content row">
        <text class="left">成交编号：</text>
        <text class="right">{{ customer.sale_order_sn }}</text>
      </view>
      <view class="content row">
        <text class="left">合同编号：</text>
        <text class="right">{{ customer.contract_no }}</text>
      </view>
      <view class="content row">
        <text class="left">房源名称：</text>
        <text class="right">{{ customer.property_right_address }}</text>
      </view>
      <view class="content row">
        <text class="left">成交时间：</text>
        <text class="right">{{ customer.deal_at }}</text>
      </view>
      <view class="content row">
        <text class="left">成交价格：</text>
        <text class="right">{{ customer.deal_amount }}万元</text>
      </view>
      <view class="content row">
        <text class="left">会员佣金：</text>
        <text class="right">{{ customer.brokerage_amount }}元</text>
      </view>
      <view class="content row">
        <text class="left">报备会员：</text>
        <text class="right">{{ customer.s_name }} {{ customer.s_phone }}</text>
      </view>
      <view class="content row">
        <text class="left">成交备注：</text>
        <text class="right">{{ customer.remark }}</text>
      </view>
      <view class="content row">
        <text class="left">跟进案场：</text>
        <text class="right"
          >{{ customer.fu_name }} {{ customer.fu_phone }}</text
        >
      </view>
      <view class="content row">
        <text class="left">扫码案场：</text>
        <text class="right"
          >{{ customer.sc_name }} {{ customer.sc_phone }}</text
        >
      </view>
      <!-- <button class="copybtn" @click="copyBtn">复制</button> -->
    </scroll-view>
    <view class="close" @click="close">
      x
    </view>
  </view>
</template>

<script>
/**
 * @date 2020/10/15
 * @description 点击成交按钮弹出订单信息
 * @description 公司统计->点击经纪人->已成交->成交信息（调用）
 * @description clipboard
 * */
export default {
  props: {
    customer: [Object],
  },
  methods: {
    close() {
      this.$emit("close");
    },
    copyBtn() {
      var content = `成交编号：${this.customer.sale_order_sn}\n合同编号：${this.customer.contract_no}\n房源名称：${this.customer.property_right_address}\n成交时间：${this.customer.deal_at}\n成交价格：${this.customer.deal_amount}万元\n报备会员：${this.customer.s_name}\n成交备注：${this.customer.remark}\n跟进案场：${this.customer.fu_name}\n扫码案场：${this.customer.sc_name} `;
      this.$copyText(content, () => {
        uni.showToast({
          title: "复制成功",
          icon: "none",
        });
      });
    },
  },
};
</script>

<style scoped lang="scss">
.dealList {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  .box {
    padding: 24rpx;
    width: 600rpx;
    height: 718rpx;
    background: #fff;
    border-radius: 16rpx;
    font-size: 28rpx;
    overflow: hidden;
    .content {
      padding: 15rpx 0;
      border-bottom: 1rpx solid #eee;
      line-height: 50rpx;
      .right {
        color: #999;
        margin-left: 5rpx;
        flex: 1;
      }
    }

    .copybtn {
      margin-top: 10rpx;
      color: #fff;
      background: #0097fe;
    }
  }
  .close {
    color: #999;
    width: 50rpx;
    height: 50rpx;
    background: #fff;
    border-radius: 50%;
    font-size: 38rpx;
    align-items: center;
    margin: 50rpx auto 0;
  }
}
</style>
