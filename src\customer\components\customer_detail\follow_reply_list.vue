<template>
    <view>
        <view class="list">
            <view class="list-item" v-for="(item) in currentList" :key="item.id">
                <view class="title">
                    {{item.admin ? item.admin.user_name : ''}} 
                    {{ item.created_at }}
                </view>
                <view class="content">{{item.content}}</view>
            </view>
            <view class="bottom">
                <view class="btn" @click="toggleExpand" v-if="canExpanded">
                   <text v-if="isExpand">收起</text>
                   <text v-else>查看全部{{replyLength}}条回复</text>
                   <myIcon :type="isExpand ? 'shangla' : 'xiala'" size="24rpx" color="#488AF6" class="right-icon"></myIcon>
                </view>
                <view class="btn" @click="add" v-else>
                    <uni-icons type="plusempty" size="14" color="#488AF6" class="icon"></uni-icons>批注
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import myIcon from "@/components/my-icon.vue";
export default {
    props: {
        list: { type: Array, default: ()=>[] },
        followId: { type: [String, Number], default: 0 },
    },
    components: {
        myIcon
    },
    data() {
        return {
            isExpand: false,
            dialogs: {
                replyComment: false
            }
        }
    },
    computed: {
        replyLength(){
            return this.list.length;
        },
        canExpanded(){
            return this.replyLength > 2;
        },
        currentList(){
            return this.isExpand || !this.canExpanded ? this.list : this.list.slice(0, 2);
        }
    },
    watch: {
       
    },
    created() {
  
    },
    methods: {
        add(){
            this.$emit('add', this.followId)
        },
        toggleExpand(){
            this.isExpand = !this.isExpand;
        },
    }
        
}

</script>
    
<style scoped lang="scss"> 
.list{
    font-weight: 500;
    margin-top: 20rpx;
    padding: 0 30rpx;
    background-color: #f7f7f7;
    .list-item{
        padding: 26rpx 0;
        color: #86909C;
        .content{
            margin-top: 16rpx;
            line-height: 1.5;
        }
        border-bottom: 1rpx solid #e8e8e8;
    }
    .bottom{
        .btn{
            color: #488AF6;
            height: 80rpx;
            display: flex;
            flex-direction: row;
            align-items: center;
            .icon{
                margin-right: 6rpx;
            }
            .right-icon{
                margin-left: 10rpx;
            }
        }
        
        
    }
}

</style>