<template>
  <view class=""> </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  onLoad(options) {
    if (options.code) {
      this.getCodeLogin(options.code, options.website_id);
    }
  },
  methods: {
    getCodeLogin(code, id) {
      this.$navigateTo(`/customer/index?code=${code}&website_id=${id}`);
      // this.$ajax.get(
      //   "/common/wx_work/getClientUserInfo",
      //   { code: code },
      //   (res) => {
      //     if (res.statusCode === 200) {
      //       uni.showToast({
      //         title: "登录成功",
      //       });
      //       // uni.setStorageSync("token", res.data.token);
      //       uni.setStorageSync("token" + res.data.website_id, res.data.token);
      //       let url = `https://yun.tfcs.cn/fenxiao/index/mine?website_id=${res.data.website_id}`;
      //       window.location.href = url;
      //     } else {
      //       uni.showToast({
      //         title: res.data.message,
      //         icon: "none",
      //       });
      //     }
      //   }
      // );
    },
  },
};
</script>

<style></style>
