import Vue from "vue";
import Vuex from "vuex";

import ajax from "@/page_outside/tools/ajax";
Vue.use(Vuex);

const store = new Vuex.Store({
  state: {
    // 站点id
    website_id: "",
    token: "",
    user_info: {},
    im: {
      imToken: "",
      userToken: "",
      myChatInfo: {
        platform: "",
        from_id: "",
        to_id: "",
        dialog_id: "",
      },
      nowChat: {
        chatList: [],
      },
      socketOpen: false,
      friendList: [],
      unread_msg_total: 0,
      chatIndex: 0,
    },
    city: {
      name: "",
      region_0: 0,
      region_1: 0,
    },
    // 位置信息
    location: {
      latitude: "",
      longitude: "",
    },
    // 腾讯地图key
    qqmapkey: "GVZBZ-47RCP-D6LDP-LCA5V-PUQFQ-QMB2V",
    // qqmapkey: "QTNBZ-AWTK6-HBHSR-E7LZU-ZZ32E-3RBT2",
    mapcenter: {
      latitude: "",
      longitude: "",
    },
    siteConfig: {},
    // 佣金是否登录显示规则
    login_display_brokerage_rule: "",
    is_setting: {
      is_enable: false, // 判断是否全局获取到开启信息
      config_support_online_live: false, // 直播
      config_support_reported: false, // 报备
      config_support_im: false, // 即时通讯
    },
    unread_msg: "",
    in_mobile: true, // 判断页面宽度
    needRefreshWhenShow: false,  //back页面是否需要刷新
    backRefresh: false,
    isLoginBack: false,
    crmConfig: {},      //站点配置
    crmConfigProjectType: 0,   //CRM项目选项类型  1自建项目  2房源小区库
    scrollTop: 0,
  },
  mutations: {
    setUserInfo (state, provider) {
      state.user_info = provider;
    },
    setLocation (state, payload) {
      state.location = payload;
    },
    setCenter (state, payload) {
      state.mapcenter = payload;
    },
    setSiteConfig (state, provider) {
      state.siteConfig = provider;
      state.login_display_brokerage_rule =
        provider.login_display_brokerage_rule;
    },
    setWebsiteid (state, provider) {
      state.website_id = provider;
    },
    setToken (state, provider) {
      state.token = provider;
    },
    setIm (state, provider) {
      state.im.imToken = provider.token;
      state.im.myChatInfo.platform = provider.platform_id_prefix;
      state.im.myChatInfo.from_id = provider.user_id;
    },
    setUserIm (state, provider) {
      state.im.userToken = provider.token;
    },
    setUnreadMsg (state, provider) {
      state.unread_msg = provider;
    },
    setCityData (state, payload) {
      state.city = payload;
    },
    setScrollTop(state, payload){
      console.log('-------更新页面滚动值setScrollTop---------', payload);
      state.scrollTop = payload;
    },
    setNeedRefreshWhenShow(state, flag){
      state.needRefreshWhenShow = flag;
    },
    onBackRefresh(state, flag){
      state.backRefresh = flag ?? true;
    },
    //设置当前crm开启的项目类型
    setCrmConfigProjectType(state, payload) {
      state.crmConfigProjectType = payload;
    },
  },
  actions: {
    getSetting (ctx, call_back) {
      ajax.get(`/common/website/query/${this.state.website_id}`, {}, (res) => {
        if (res.statusCode === 200) {
          ctx.commit("setSiteConfig", res.data);
          call_back && call_back(res.data);
        }
      });
    },
    // 获取imtoken
    getImToken (ctx, options) {
      ajax.get("/client/my/tfy/im_token", {}, (res) => {
        if (res.statusCode === 200) {
          ctx.commit("setIm", res.data);
          ajax.get("/client/my/tfy/user_token", {}, (res) => {
            if (res.statusCode === 200) {
              ctx.commit("setUserIm", res.data);
              options(res.statusCode);
            }
          });
        }
      });
    },
    getUserInfo (ctx, options = {}) {
      ajax.get(
        "/client/my/query",
        {},
        (res) => {
          if (options.success) {
            options.success(res);
          }
          if (res.statusCode === 200) {
            ctx.commit("setUserInfo", res.data);
          } else {
            uni.showToast({
              title: res.data.message || "获取信息失败",
              icon: "none",
            });
          }
        },
        (err) => {
          if (options.fail) {
            options.fail(err);
          }
          console.log(err);
        },
        { disableAutoHandle: true }
      );
    },
    queryUserInfo(ctx, options = {}){
      ajax.get(
        "/qywx/common/query",
        {},
        (res) => {
          if (options.success) {
            options.success(res);
          }
          if (res.statusCode == 200) {
            uni.setStorageSync("userInfo", JSON.stringify(res.data));
          }
        },
        (err) => {
          if (options.fail) {
            options.fail(err);
          }
          console.log(err);
        },
        { disableAutoHandle: true }
      );
    },
    // 获取位置经纬度
    getLocation (ctx, options) {
      if (ctx.state.location.latitude && ctx.state.location.longitude) {
        if (options.success) {
          options.success(ctx.state.location);
        }
        return;
      }
      // 部分手机获取不到位置且不走失败回调，所以添加个计时器超时提示获取位置失败
      const timer = setTimeout(() => {
        if (options.fail) {
          options.fail({ msg: "获取位置超时" });
        }
        // uni.showToast({
        //   title: '获取位置失败',
        //   icon: 'none'
        // })
      }, 4500);
      uni.getLocation({
        type: "gcj02",
        success: (res) => {
          clearTimeout(timer);
          ctx.commit("setLocation", res);
          ctx.commit("setCenter", res);
          if (options.success) {
            options.success(res);
          }
        },
        fail: (err) => {
          clearTimeout(timer);
          console.log("获取位置失败：", err);
          // uni.showToast({
          //   title: '获取位置失败',
          //   icon: 'none'
          // })
          if (options.fail) {
            options.fail(err);
          }
        },
      });
    },
    //获取当前开启的crm项目配置
    async queryCrmConfigProjectType(ctx) {
        if (!ctx.state.crmConfigProjectType) {
          await new Promise((resolve, reject) => {
            ajax.get('/admin/crm/config/get_project_type', {}, res => {
                if (res.statusCode == 200) {
                    ctx.commit('setCrmConfigProjectType', res.data);
                    resolve();
                }else{
                    uni.showToast({
                        title: res?.data?.message || '获取项目配置失败',
                        icon: 'none'
                    });
                }
                reject();
            }, er => {
                reject();
            })
        });
      }
    },

    updateScrollTop({ commit }, value) {
      commit('setScrollTop', value);
    },


  },
});

module.exports = store;
