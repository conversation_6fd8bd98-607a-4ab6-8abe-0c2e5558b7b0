//获取客户状态
import ajax from "@/page_outside/tools/ajax";

let _promise = (api, params, cb) => {
    return new Promise((resolve, reject) => {
        ajax.get(api, params, res => {
            if (res.statusCode == 200) {
                resolve(res.data);
                cb && cb(res.data)
            }else{
                if(res?.data?.message){
                    uni.showToast({
                        title: res?.data?.message,
                        icon: 'none'
                    });
                }
            }
            reject();
        }, er => {
            reject();
        })
    })
};


function getStatusList(type = 4){
    return _promise("/admin/crm/tracking/list", { type }).then(data => 
        Promise.resolve(
            (data || []).map(e => ({
                value: e.id,
                label: e.title
            }))
        )
    );
}

function getLabelList(){
    return _promise("/qywx/tag/search", {}).then(data => 
        Promise.resolve(
            (data.qiwei_tag || []).map(item => {
                return {
                    id: item.id,
                    name: item.name,
                    label: item.taggroup,
                };
            }).concat(data.system_tag)
        )
    )
}

function getTypeList(){
    return _promise("/qywx/type/list", {}).then(data => 
        Promise.resolve(
            (data || []).map(e => ({
                value: e.id,
                label: e.title
            }))
        )
    );
}


function getLevelList(){
    return _promise("/qywx/level/list", {}).then(data => 
        Promise.resolve(
            (data || []).map(e => ({
                value: e.id,
                label: e.title
            }))
        )
    ); 
}


function getSourceList(){
    return _promise("/qywx/source/list", {}).then(data => 
        Promise.resolve(
            (data || []).map(e => ({
                value: e.id,
                label: e.title
            }))
        )
    ); 
}

//审批类型
function getApproveType(type = 1){
    return _promise("/admin/house/approveType/"+type, {})
}

export default {
    getStatusList,
    getLabelList,
    getTypeList,
    getLevelList,
    getSourceList,
    getApproveType
}