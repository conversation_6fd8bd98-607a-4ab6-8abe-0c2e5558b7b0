<template>
  <view>
    <view class="search_outsaide">
      <view class="search_outsaide_select">
        <view class="whole_input">
          <view class="whole_input_left" @click="iconFn">
            <view class="whole_input_left_name">{{ text }}</view>
            <view>
              <image src="../static/icon/index/xia.png" style="width:32rpx;height:32rpx;" v-if="searchshowfn"></image>
              <image src="../static/icon/index/箭头 (4).png" style="width:36rpx;height:30rpx" v-else></image>

            </view>
          </view>
          <view class="whole_shu">|</view>
          <view class="whole_bai">
            <!-- 请输入搜索内容 -->
            <view> <input placeholder="请输入搜索内容" placeholder-style="font-size:28rpx;color:#ACACAC;" type="text"
                v-model="keywords" @tap="tapBtn"></view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="searchshow">
      <view class="h">
        <image src="../static/icon/index/Polygon 1.png" style="width:36rpx;height:30rpx"></image>
      </view>
      <view class="search_box" @click="fousFn">
        <view v-for="(item, index) in range" :key="item.value" class="search_text"
          :class="{ 'active': item.value == value }" @tap="textFn(item.value, item.text)" >{{ item.text }}</view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      searchshow: false,
      searchshowfn: true,
      value: 0,
      range: [
        { value: 0, text: "篮球" },
        { value: 1, text: "足球" },
        { value: 2, text: "游泳" },
      ],
      text: '电话',
      keywords: ''
    };
  },
  watch: {
    keywords: {
      handler(naval) {
        uni.setStorageSync('keywords', naval);
      },
      immediate: true,
    }
  },
  onLoad() {
  },
  methods: {
    iconFn() {
      this.searchshow = !this.searchshow
      this.searchshowfn = !this.searchshowfn
    },
    tapBtn() {},
    fousFn() {
      this.searchshow = !this.searchshow
      this.searchshowfn = !this.searchshowfn
    },
    change(e) {
      console.log("e:", e);
    },
    textFn(id, text) {
      this.value = id
      this.text = text
    },
  },
};
</script>
<style lang="scss">
::v-deep .uni-input-placeholder {
  top: -6rpx !important;
}

::v-deep .uni-input-input {
  top: -6rpx !important;
}

.h {
  position: fixed;
  left: 138rpx;
  top: 124rpx;
}

.search_text {
  width: 100%;
  padding: 20rpx 32rpx;
}

.search_box {
  width: 180rpx;
  background: #fff;
  padding: 24rpx 0;
  margin-left: 24rpx;
  margin-top: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0px 0px 40rpx 0px rgba(0, 0, 0, 0.10);
}

.active {
  background: #007aff8c;
  color: #fff;
}

page {
  background: #F6F6F6;
}

.search_outsaide_select {
  padding: 24rpx;
}

.search_outsaide {
  // padding: 64rpx 32rpx 32rpx 32rpx;
  background: #fff;
}

.whole_shu {
  color: rgba(41, 44, 57, 0.40);
  font-size: 24rpx;
  font-weight: 400;
  line-height: 28rpx;

}

.whole_bai {
  width: 70%;
  height: 32rpx;
  line-height: 32rpx;
  color: rgba(41, 44, 57, 0.40);
}

.whole_input_left_name {
  color: rgba(41, 44, 57, 0.70);
  font-size: 28rpx;
  font-weight: 400;
  line-height: 32rpx;
  margin-right: 16rpx;
}

.whole_input_left {
  display: flex;
  flex-direction: row;

}

.whole_input {
  width: 100%;
  // height: 80rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  // margin: 0 16rpx;
  background: #F6F6F6;
  border-radius: 16rpx;
}</style>