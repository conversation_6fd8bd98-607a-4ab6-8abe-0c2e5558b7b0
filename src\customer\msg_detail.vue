<template>
  <view
    class="list"
    :style="{
      paddingBottom:
        show_face || show_statement ? '654rpx' : '194rpx',
    }"
  >
    <scroll-view
      class="scroll"
      scroll-y
      :scroll-top="scrollTop"
      :scroll-with-animation="scrollAnimation"
      @scrolltoupper="scrolltoupper"
    >
      <view id="chat-lists" v-if="loading" @click="shutdownPop">
        <view class="loadmore">{{ loadmore_chat }}</view>
        <chatItem
          :paly_voice="play_voice_index"
          @playvoice="playvoice($event)"
          :chat_list="im.nowChat.chatList"
          :user_info="user_info"
          @agreeSendWx="agreeSendWx"
        ></chatItem>
          <!-- 悬浮框点击发送消息 （项目/报备客户） -->
        <view
          class="float-send-box row"
          v-if="isClose"
          @click="toBuild(project_info)"
        >
          <view class="left">
            <image mode="aspectFill" :src="project_info.build_img"></image>
          </view>
          <view class="right">
            <view class="right-top row">
              <view class="build-name">
                {{ project_info.build_name }}
              </view>
              <view class="right-close" @click.stop="isClose = false"
                >X</view
              ></view
            >
            <view class="right-bottom row">
              <view class="right-bottom-end">
                {{ project_info.full_build_address }}
              </view>
              <view class="send" @click.stop="sendShareMsg(project_info)"
                >发送</view
              >
            </view>
          </view>
        </view>
        <view
          class="float-send-box row"
          v-if="show_build"
          @click="toERP(buildInfo.id)"
        >
          <view class="left">
            <image mode="aspectFill" :src="buildInfo.image"></image>
          </view>
          <view class="right">
            <view class="right-top row">
              <view class="build-name">
                {{ buildInfo.title }}
              </view>
              <view class="right-close" @click.stop="show_build = false"
                >X</view
              >
            </view>
            <view class="right-bottom row">
              <view class="right-bottom-end">
                {{ buildInfo.desc }}
              </view>
              <view class="send" @click.stop="sendShareMsg(buildInfo)"
                >发送</view
              >
            </view>
          </view>
        </view>
        <!-- 悬浮框点击发送客户信息 -->
        <view class="float-send-box-customer row" v-if="isCustomerClose">
            <view class="customer-left">
            客户姓名：{{ customer_info.customer_name }}</br> 联系方式：{{
              customer_info.customer_phone
            }}</br>
            报备项目：{{ customer_info.build_name }}
          </view>
          <view class="customer-right ">
            <view class="right-close" @click.stop="isCustomerClose = false">X</view>
            <view class="send" @click="sendCustomerMsg(customer_info)">发送</view>
          </view>
        </view>
      </view>
      <view id="chat-lists" v-else>正在加载...</view>
    </scroll-view>
    <view class="send-box top-line" v-if="loading">
      <view class="chat-menu row">
        <block v-for="(item, index) in chat_menu" :key="index">
          <view class="menu" @click="chooseMenu(item)">{{ item.value }}</view>
        </block>
      </view>
      <view class="send-content row">
        <myIcon
          v-if="isVoiceOrText"
          @click="isVoiceOrText = !isVoiceOrText"
          type="jianpan"
          size="56rpx"
          color="#333"
        ></myIcon>
        <myIcon
          v-else
          @click="isVoiceOrText = !isVoiceOrText"
          type="yuyin"
          size="56rpx"
          color="#333"
        ></myIcon>
        <!-- 文本输入框 -->
        <view class="msg-content">
          <input
            @confirm="handleSend(send_ctn)"
            v-if="isVoiceOrText === false"
            type="text"
            class="msg-input"
            v-model="send_ctn"
          />
          <!-- 点击长按录音 -->
          <view
            @longpress="onLongTapVoiceBtn"
            @touchmove="onTouchMoveVoicBtn"
            @touchend="onTouchEndVoiceBtn"
            v-else
            class="recording-box"
            >长按发送语音</view
          >
        </view>
        <myIcon
          @click="openExpression"
          type="biaoqing"
          size="56rpx"
          color="#333"
        ></myIcon>
        <view class="btn" @click="handleSend(send_ctn)" v-if="send_ctn"
          >发送</view
        >
        <myIcon
          v-else
          @click="sendMsgImg"
          type="tianjia"
          size="56rpx"
          color="#333"
        ></myIcon>
      </view>
      <!-- 表情 -->
      <view class="operate-box" v-show="show_face">
        <view class="face-list row">
          <view
            class="face"
            v-for="(item, index) in face_list"
            :key="index"
            @click="selectFace(item)"
          >
            <image
              :src="'https://img.tfcs.cn/static/img/face' + index + '.png'"
            ></image>
          </view>
        </view>
      </view>
      <!-- 常用语 -->
      <view class="operate-box" v-show="show_statement">
        <view class="statement-list">
          <view
            class="statement-item bottom-line row"
            v-for="(item, index) in statement_list"
            :key="index"
          >
            <text @click="handleSend(item.content)">{{ item.content }}</text>
            <view class="options row" v-if="manage_statement">
              <view
                class="icon-box"
                @click="
                  $navigateTo(`/only_build_im/add_statement?id=${item.id}`)
                "
              >
                <my-icon type="xiaoxi-bianji" color="#666"></my-icon>
              </view>
              <view class="icon-box" @click="handelDelStatement(item.id)">
                <my-icon type="xiaoxi-shanchu" color="#ff656b"></my-icon>
              </view>
            </view>
          </view>
          <load-more :status="load_status"></load-more>
        </view>
        <view class="add-statement row">
          <text
            class="text-center"
            v-if="manage_statement"
            @click="manage_statement = false"
            >完成</text
          >
          <template v-else>
            <text
              class="text-center"
              @click="$navigateTo('/only_build_im/add_statement')"
              >添加常用语</text
            >
            <text class="manage_btn" @click="manage_statement = true"
              >管理</text
            >
          </template>
        </view>
      </view>
    </view>
    <!-- 录音中提示 -->
    <view class="voiced" :class="{ show: show_voice }">
      <image
        class="voice_img"
        mode="widthFix"
        :src="
          show_voice
            ? '../static/voice/recorde.gif'
            : '../static/voice/none.png'
        "
      ></image>
      <view class="tip">手指上滑取消发送</view>
    </view>
    <view class="voiced" :class="{ show: show_cancel_voice }">
      <image
        class="voice_img"
        mode="widthFix"
        :src="'../static/voice/quxiao.png'"
      ></image>
      <view class="tip">松开手指取消发送</view>
    </view>
    <view class="socket_colse" v-if="im.socketOpen === false && isOpenInfo">
      <view class="err-tip row">
        <view class="tip-text">聊天连接已断开</view>
        <view class="tip-btn" @click="connectChatAgain()">{{
          reconnect_status
        }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import chat from "./chatmixin";
// import myIcon from "../only_build/components/my-icon";
import chatItem from "./chat-item";
import { mapState, mapActions } from "vuex";
import loadMore from "../components/loadMore";
import config from "../page_outside/config/index.js";
const recorderManager = uni.getRecorderManager(); // uniapp录音事件
const innerAudioContext = uni.createInnerAudioContext();
export default {
  components: { myIcon, chatItem, loadMore },
  mixins: [chat],
  data() {
    return {
      chat_menu: [
        { id: 1, value: "常用语" },
        { id: 2, value: "邀请致电" },
        { id: 3, value: "查看微信" },
        { id: 4, value: "发送位置" },
      ],
      send_ctn: "",
      now_time: new Date().toLocaleTimeString(), // 获取当前时间
      face_list: [
        "微笑",
        "大笑",
        "笑哭",
        "开心",
        "呲牙",
        "坏笑",
        "欣慰",
        "鄙视",
        "白眼",
        "飞吻",
        "鬼脸",
        "酷",
        "爱财",
        "调皮",
        "惊讶",
        "无表情",
        "思考",
        "亲亲",
        "喜欢",
        "低沉",
        "怒",
        "生气",
        "超爱",
        "大哭",
        "小声",
        "惊恐",
        "爱心",
        "心碎",
        "偷看",
        "OK",
        "耶",
        "大拇指",
        "握拳",
        "强壮",
      ],
      // 录音还是输入文本
      isVoiceOrText: false,
      isRecordering: false, // 正在录音
      recorder_max_time: 60000, //录音最大时长
      show_voice: false, // 录音中提示显示
      show_cancel_voice: false, //录音中取消提示
      show_face: false, //表情管理
      to_user_id: "",
      chatRecordList: [], // 聊天记录列表
      scrollTop: 100000,
      show_statement: false, // 常用语显示
      statement_list: [],
      statement_params: {
        page: 1,
      },
      scrollAnimation: false,
      load_status: "",
      manage_statement: false, // 常用语管理
      to_user_info: {}, //对方信息
      play_voice_index: -1, //播放音频的消息索引值
      chat_list_number: 0,
      loadmore_chat: "点击加载更多",
      is_list: "", // 判断进入
      reconnect_status: "点击重连", // 点击重新连接按钮
      loading: false, // 加载完后显示聊天内容
      isOpenInfo: false,
      project_id: "", // 地址拼接如果有项目id说明是从项目中进入
      project_info: {}, // 存入项目信息
      isClose: false, // 是否关闭显示
      customer_id: "", // 地址拼接如果有客户id说明是从我的客户列表进入
      customer_info: {}, // 客户信息
      isCustomerClose: false, // 客户信息是否显示发送
      show_build: false,
       initMsg: {},
      buildInfo: {}
    };
  },
  computed: {
    ...mapState(["im", "user_info"]),
  },
  onShow() {
    this.getStatementList();
  },
  onLoad(options) {
    if (options.to_id) {
      if (options.to_id == this.user_info.id) {
        uni.showModal({
          title: "提示",
          content: "不能和自己聊天！",
          showCancel: false,
          success: (res) => {
            if (res.confirm) {
              uni.navigateBack({
                delta: 1,
              });
            }
          },
        });
        return;
      }
      this.to_user_id = parseInt(options.to_id);
      // 创建一条访客记录
      this.$createVisitorsRecord(
        {
          user_id: this.to_user_id,
          visit_category: 3,
        },
        (res) => {}
      );
      this.initChat();
    }
    if (options.is_list == 1) {
      this.is_list = options.is_list;
    }
    // 触发页面滚动
    uni.$on("handleScroll", this.setScrollTop);
    // 监听语音播放
    innerAudioContext.onPlay(() => {
      this.play_voice_index = this.current_voice_index;
    });
    // 监听语音播放停止
    innerAudioContext.onStop(() => {
      this.play_voice_index = -1;
    });
    // 监听语音自然播放结束事件
    innerAudioContext.onEnded(() => {
      this.play_voice_index = -1;
    });
    // 监听语音播放失败事件
    innerAudioContext.onError(() => {
      uni.showToast({
        title: "播放失败，请重试",
        icon: "none",
      });
      this.play_voice_index = -1;
    });
    // if (options.project_id) {
    //   this.project_id = options.project_id;
    // }

     if (options.project_id && (options.type == 0 || !options.type)) {
      this.project_id = options.project_id;
    }
    console.log(this.$store.state.buildInfo.id, 123123123321321);
    if (this.$store.state.buildInfo && this.$store.state.buildInfo.id) {
      this.buildInfo = JSON.parse(JSON.stringify(this.$store.state.buildInfo))
      this.show_build = true;
      this.$store.state.buildInfo = {}
    }
    if (options.customer_id) {
      this.customer_id = options.customer_id;
    }
  },
  methods: {
    ...mapActions(["getImToken", "getUserInfo"]),
    // 获取项目信息
    getProjectInfo(project_id) {
      this.$ajax.get(`/common/project/query/${project_id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.project_info = res.data;
          this.isClose = true;
        } else {
          this.isClose = false;
          uni.showToast({
            title: res.data.message || "获取项目信息失败",
            icon: "none",
          });
        }
      });
    },
    // 获取客户信息
    getCustomerInfo(customer_id) {
      this.$ajax.get(
        `/client/customer/reported/query/project/id/${customer_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.customer_info = res.data;
            this.isCustomerClose = true;
          } else {
            this.isCustomerClose = false;
            uni.showToast({
              title: res.data.message || "获取客户信息失败",
              icon: "none",
            });
          }
        }
      );
    },
    // 获取常用语列表
    getStatementList() {
      this.load_status = "loading";
      if (this.statement_params.page === 1) {
        this.statement_list = [];
      }
      this.$ajax.get(
        "/client/im/everyday_language/search?is_page=1",
        this.statement_list,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.statement_list = this.statement_list.concat(res.data.data);
            if (res.data.data.length == 0) {
              this.load_status = "nomore";
            }
          } else {
            uni.showToast({
              title: res.data.message || "获取常用语失败",
              icon: "none",
            });
          }
        }
      );
    },
    // 获取聊天记录
    getChatRecord(user_id, info) {
      this.loadmore_chat = "正在加载...";
      this.$ajax.get(
        `/client/im/msg/search?start_id=${this.chat_list_number}&to_user_id=${user_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            res.data.map((item, index) => {
              res.data[index].to_headimage = info.avatar;
              res.data[index].from_headimage = this.user_info.avatar;
            });
            this.chatRecordList = res.data.reverse();
            this.concatChat(this.chatRecordList);
            this.loading = true;
            this.isOpenInfo = true;
          } else {
            uni.showToast({
              title: res.data.message || "获取聊天信息失败",
              icon: "none",
            });
          }
        }
      );
    },
    // 合并聊天记录
    concatChat(chatList) {
      this.loadmore_chat = "加载更多";
      this.im.nowChat.chatList = chatList.concat(this.im.nowChat.chatList);
      this.$nextTick(() => {
        this.setScrollTop(false);
      });
    },
    /**
     * 使页面滚动
     */
    setScrollTop(scrollAnimation = true) {
      if (this.chat_list_number === 0) {
        this.scrollAnimation = scrollAnimation;
        let query = wx.createSelectorQuery();
        query
          .select("#chat-lists")
          .boundingClientRect((rect) => {
            this.scrollTop = rect.height;
          })
          .exec();
      }
    },
    // 获取聊天对象的头像
    getChatUserInfo(user_id) {
      this.$ajax.get(`/common/user/query/${user_id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.im.myChatInfo.to_id = res.data.id;
          // 动态设置标题为客户昵称
          uni.setNavigationBarTitle({
            title: `正在和${res.data.name || res.data.nickname}聊天`,
          });
          this.haveReadMsgAll(user_id);
          this.to_user_info = res.data;
          this.getChatRecord(user_id, res.data);
          this.im.nowChat.chatList = [];
        }
      });
    },
    // 创建会话
    createSession() {
      this.$ajax.post(
        "/client/im/session/put",
        {
          to_user_id: this.to_user_id,
          chat_category: 0,
        },
        (res) => {
          if (res.statusCode === 200) {
              // 判断会话窗口内容
            if(this.project_id){
              this.getProjectInfo(this.project_id);
            }else if(this.customer_id){
              this.getCustomerInfo(this.customer_id);
            }else if(res.data.issue_category === 1){
              this.getProjectInfo(res.data.issue_id)
            }else if(res.data.issue_category === 2){
              this.getCustomerInfo(res.data.issue_id)
            }
            this.im.myChatInfo.dialog_id = res.data.id;
            this.getChatUserInfo(this.to_user_id);
          }
        }
      );
    },
    // 初始化聊天
    initChat() {
      if (this.im.myChatInfo.from_id) {
        if (!this.im.socketOpen) {
          console.log("聊天初始化");
          this.getImToken((options) => {
            if (options === 200) {
              this.initMsg = {
                flag: "init",
                from_id:
                  this.im.myChatInfo.platform + this.im.myChatInfo.from_id,
                dialog_id: this.im.myChatInfo.dialog_id,
              };
              this.handleConnectSocket();
              // this.onMessage()
              this.onClose();
              this.onSocketError();
            }
          });
        } else {
          console.log("聊天已经是连接状态");
        }
        this.createSession();
      } else {
        uni.showToast({
          title: "请手动发起聊天",
          icon: "none",
        });
      }
    },
    onLongTapVoiceBtn(e) {
      // 如果正在录音停止
      if (this.isRecordering) {
        return;
      }
      recorderManager.start({
        duration: this.recorder_max_time,
      });
      this.touch_voice_start_position_y = e.changedTouches[0].clientY;
      recorderManager.onStart(() => {
        console.log("录音开始");
        this.onRecordering();
      });
      this.onRecorderStop();
    },
    // 播放语音
    playvoice(e) {
      this.current_voice_index = e.index;
      if (e.index === this.play_voice_index) {
        innerAudioContext.stop();
        return;
      }
      // innerAudioContext.destroy()
      innerAudioContext.src = e.content.content;
      innerAudioContext.play();
    },
    // 录音中状态
    onRecordering() {
      this.isRecordering = true;
      this.recorderCountDowm();
      this.show_voice = true;
    },
    // 监听录音结束事件
    onRecorderStop() {
      recorderManager.onStop((e) => {
        if (this.recorder_timer) {
          uni.showToast({
            title: "录音已自动终止",
            icon: "none",
          });
        }
        this.onRecordered();
        console.log("已经停止录音");
        // 如果大于100则是取消录音,否则获取录音文件
        if (
          this.touch_voice_start_position_y - this.touch_voice_end_position_y <
          70
        ) {
          this.uploadVoice(e.tempFilePath, e.duration);
        }
        this.touch_voice_start_position_y = null;
      });
    },
    // 录音倒计时
    recorderCountDowm() {
      if (this.recorder_timer) {
        clearInterval(this.recorder_timer);
        this.recorder_timer = null;
      }
      let max_time = this.recorder_max_time / 1000;
      this.recorder_timer = setInterval(() => {
        max_time--;
        if (max_time >= 1 && max_time <= 3) {
          uni.showToast({
            title: `${max_time}秒后停止录音`,
            icon: "none",
          });
          if (max_time <= 0) {
            // 记录结束触摸的位置
            this.touch_voice_end_position_y = this.touch_voice_start_position_y;
            // 执行停止录音
            this.stopRecorder();
          }
        }
      }, 1000);
    },
    // 录音结束状态
    onRecordered() {
      this.isRecordering = false;
      if (this.recorder_timer) {
        clearInterval(this.recorder_timer);
        this.recorder_timer = null;
      }
      this.show_voice = false;
      this.show_cancel_voice = false;
      if (this.timer) clearInterval(this.timer);
    },
    //停止录音
    stopRecorder() {
      if (!this.touch_voice_start_position_y) {
        return;
      }
      // 判断垂直滑动距离到某个值松开提示取消发送录音
      if (this.recorder_timer) {
        clearInterval(this.recorder_timer);
        this.recorder_timer = null;
      }
      recorderManager.stop && recorderManager.stop();
    },
    // 滑动发送语音

    onTouchMoveVoicBtn(e) {
      if (!this.touch_voice_start_position_y) {
        return;
      }
      // 判断垂直滑动距离达到某个值则提示松开取消发送录音
      if (
        this.touch_voice_start_position_y &&
        this.touch_voice_start_position_y - e.changedTouches[0].clientY >= 70
      ) {
        this.show_cancel_voice = true;
        this.show_voice = false;
      } else {
        this.show_cancel_voice = false;
        this.show_voice = true;
      }
    },
    /**
     * @desc 松开发送语音按钮
     */
    onTouchEndVoiceBtn(e) {
      // 记录结束触摸的位置
      this.touch_voice_end_position_y = e.changedTouches[0].clientY;
      // 执行停止录音
      this.stopRecorder();
    },
    toERP(id){
      let url =`/fenxiao/ershou/detail?id=${id}`
      if (this.$store.state.configInfo.version>1){
        this.$navigateTo(url.replace("/fenxiao/","/build/"))
      }else {
        this.$navigateTo(url)
      }
      
    },
    // 上传录音
    uploadVoice(file, duration) {
      if (duration < 1000) {
        uni.showToast({
          title: "录制时间太短了",
          icon: "none",
        });
        return;
      }
      uni.showLoading({ title: "正在发送" });
      let baseURL = config.appApi;
      var that = this;
      uni.uploadFile({
        url: baseURL + "/common/file/upload/client",
        filePath: file,
        header: { Authorization: "Bearer " + uni.getStorageSync("token") },
        name: "file",
        formData: { category: "104" },
        success: (res) => {
          if (res.statusCode === 200) {
            uni.hideLoading();
            let data = JSON.parse(res.data);
            let msg = {
              type: "voice",
              content: data.url,
              duration: duration,
              dialog_id: this.im.myChatInfo.dialog_id,
            };
            this.chat_list_number = 0;
            that.sendMessage(msg, "voice");
          }
        },
      });
    },
    // 点击表情管理
    openExpression() {
      this.show_face = !this.show_face;
      this.show_statement = false;
      this.isVoiceOrText = false;
    },
    // 点击快捷菜单
    chooseMenu(item) {
      if (item.id === 1) {
        this.show_face = false;
        this.show_statement = !this.show_statement;
      }
      if (item.id === 2) {
        uni.makePhoneCall({
          phoneNumber: this.to_user_info.phone,
        });
      }
      if (item.id === 3) {
        let msg = {
          type: "apply_wx",
          dialog_id: this.im.myChatInfo.dialog_id,
        };
        this.sendMessage(msg, "apply_wx");
      }
      if (item.id === 4) {
        uni.chooseLocation({
          keyword: "",
          success: (res) => {
            let msg = {
              name: res.name,
              address: res.address,
              lat: res.latitude,
              lng: res.longitude,
              type: "map",
              dialog_id: this.im.myChatInfo.dialog_id,
            };
            this.sendMessage(msg, "map");
          },
        });
      }
    },
    sendMsgImg() {
      let baseURL = config.appApi;
      uni.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"],
        success: (res) => {
          const uploadTask = uni.uploadFile({
            url: baseURL + "/common/file/upload/client",
            filePath: res.tempFilePaths[0],
            header: { Authorization: "Bearer " + uni.getStorageSync("token") },
            name: "file",
            formData: { category: "101" },
            success: (res) => {
              if (res.statusCode === 200) {
                let data = JSON.parse(res.data);
                let send_msg = {
                  type: "img",
                  content: data.url,
                  dialog_id: this.im.myChatInfo.dialog_id,
                };
                this.chat_list_number = 0;
                this.sendMessage(send_msg, "img");
              } else {
                uni.showToast({
                  title: res.data.message || "选择失败",
                  icon: "none",
                });
              }
            },
          });
        },
      });
      // console.log("发送" + this.send_ctn);
    },
    // 发送信息
    handleSend(content) {
      if (!content) {
        uni.showToast({
          title: "请输入内容",
          icon: "none",
        });
        return;
      }
      this.show_face = false;
      this.show_statement = false;
      let send_msg = {
        type: "text",
        content: content,
        dialog_id: this.im.myChatInfo.dialog_id,
      };
      this.sendMessage(send_msg, "text");
      this.send_ctn = "";
      this.chat_list_number = 0;
      // // 加一秒延迟发送
      // setTimeout(() => {
      //   this.getChatRecord(this.to_user_id, this.user_info);
      // }, 1000);
    },
    // 选择表情
    selectFace(face) {
      this.send_ctn += "[" + face + "]";
    },
    // 删除某个常用语
    handelDelStatement(id) {
      this.$ajax.get(`/client/im/everyday_language/delete/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "删除成功",
          });
          this.getStatementList();
        } else {
          uni.showToast({
            title: res.data.message || "删除失败",
            icon: "none",
          });
        }
      });
    },
    // 同意发送微信
    agreeSendWx(item) {
      let msg = {
        type: "agreeSendWx",
        content: {
          wechatName: this.user_info.name,
          wechatNumber: this.user_info.wx_user_name,
          wechatQrcode: this.user_info.wx_user_qrcode,
          dialog_id: this.im.myChatInfo.dialog_id,
        },
      };
      uni.showToast({
        title: "发送成功",
      });
      this.sendMessage(msg, "agreeSendWx");
    },
    connectChatAgain() {
      this.reconnect_status = "正在连接...";
      this.handleConnectSocket();
      return;
    },
    // 关闭弹窗
    shutdownPop() {
      this.show_face = false;
      this.show_statement = false;
    },
    // 加载更多
    scrolltoupper(e) {
      if (e.type === "scrolltoupper") {
        this.chat_list_number = this.im.nowChat.chatList[0].id;
        this.scrollAnimation = true;
        this.getChatRecord(this.to_user_id, this.to_user_info);
      }
    },
    toBuild(build){
      if(this.$store.state.configInfo.mode ==3){
        this.$navigateTo(`/build/single_detail?buildID=${project_info.build_id}`)
        return 
      }
      this.$navigateTo(`/build/detail?buildID=${project_info.build_id}`)
    },
     // 发送分享的链接
    sendShareMsg(project_info) {
      let send_msg = {
        type: "issue",
        category: this.project_id ? 1 : 1000,
        id: project_info.project_id || project_info.id,
        content: {
          build_name: project_info.build_name || project_info.title,
          dialog_id: this.im.myChatInfo.dialog_id,
          build_img: project_info.build_img || project_info.image,
          build_id: this.project_info.build_id || project_info.id,
          build_address: project_info.full_build_address || project_info.desc,
        },
      }
      this.isClose = false
      this.show_build = false
      this.sendMessage(send_msg, "issue");
      uni.showToast({
        title: "已发送",
        icon: "none",
      });
    },
    sendCustomerMsg(customer_info){
      let send_msg = {
        type:"issue",
        category:2,
        id:customer_info.id,
        content:{
          customer_name:customer_info.customer_name,
          customer_phone:customer_info.customer_phone,
          build_name:customer_info.build_name,
          build_id:customer_info.build_id,
          customer_id:customer_info.id,
        }
      }
      this.isCustomerClose = false
      this.sendMessage(send_msg,'issue')
      uni.showToast({
        title:"已发送",
        icon:"none"
      })
    }
  },
  onUnload() {
    // if (!this.is_list) {
    //   if (this.recorder_timer) {
    //     clearInterval(this.recorder_timer);
    //     this.recorder_timer = null;
    //   }
    //   if (this.timer) clearInterval(this.timer);
    //   this.im.nowChat.chatList = [];
    //   // 如果是当前页创建的连接则返回时断开连接
    //   this.closeSocket();
    //   if (this.timer) {
    //     clearInterval(this.timer);
    //   }
    // }
    if (this.recorder_timer) {
      clearInterval(this.recorder_timer);
      this.recorder_timer = null;
    }
    if (this.timer) clearInterval(this.timer);
    this.im.nowChat.chatList = [];
    // 如果是当前页创建的连接则返回时断开连接
    this.closeSocket();
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
};
</script>

<style lang="scss">
page {
  background: #f3f3f3;
}
.list {
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 0;
  .loadmore {
    text-align: center;
    color: #999;
    margin-top: 30rpx;
  }
  .scroll {
    height: 100%;
    width: 100%;
    background-color: #f5f5f5;
    box-sizing: border-box;
  }
  .time {
    margin: 48rpx auto;
    font-size: 22rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: #999999;
  }
  .send-box {
    width: 100%;
    background: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    justify-content: center;
    .chat-menu {
      padding: 22rpx 48rpx;
      color: #333;
      font-size: 24rpx;
      justify-content: space-around;
      .menu {
        height: 48rpx;
        line-height: 47rpx;
        padding: 0 24rpx;
        border: 1rpx solid #999;
        border-radius: 24rpx;
      }
    }
    .send-content {
      align-items: center;
      justify-content: space-between;
      margin: 11rpx 20rpx 20rpx;
      .msg-content {
        width: 474rpx;
        height: 72rpx;
        margin: 0 10rpx;
        .msg-input {
          height: 72rpx;
          background: #fff;
          border: 1rpx solid #999;
          border-radius: 8rpx;
          padding: 0 20rpx;
        }
        .recording-box {
          height: 72rpx;
          background: #fff;
          border: 1rpx solid #999;
          border-radius: 8rpx;
          align-items: center;
          line-height: 72rpx;
          color: #666;
        }
      }
      .btn {
        width: 110upx;
        height: 56upx;
        margin-left: 20upx;
        margin-bottom: 5upx;
        padding: 0;
        font-size: 28upx;
        line-height: 56upx;
        text-align: center;
        border-radius: 10upx;
        background-color: #2e8cef;
        color: #fff;
        &.left {
          margin-left: 0;
          margin-right: 20upx;
        }
      }
    }
    .operate-box {
      border-top: 20rpx solid #f3f3f3;
      height: 460rpx;
      box-sizing: border-box;
      padding: 10rpx 20rpx 14rpx 30rpx;
      background-color: #fff;
      .face-list {
        padding: 20rpx;
        justify-content: space-between;
        flex-wrap: wrap;
        height: 100%;
        overflow-x: hidden;
        .face {
          width: 50rpx;
          min-width: 50rpx;
          height: 50rpx;
          margin: 20rpx;
          image {
            width: 100%;
            height: 100%;
          }
        }
      }
      .statement-list {
        height: 360upx;
        overflow-x: hidden;
        .statement-item {
          justify-content: space-between;
          align-items: center;
          > text {
            padding: 24upx 20upx;
          }
          .icon-box {
            padding: 10rpx;
          }
        }
      }
      .add-statement {
        align-items: center;
        color: #666;
        > text {
          padding: 24upx 20upx;
        }
        .text-center {
          color: $uni-color-primary;
          text-align: center;
        }
        .manage_btn {
          // color: $uni-color-primary;
        }
      }
    }
  }
  // 录音中
  .voiced {
    width: 240rpx;
    padding: 24rpx;
    text-align: center;
    box-sizing: border-box;
    height: 240rpx;
    position: fixed;
    z-index: -1;
    left: 0;
    right: 0;
    bottom: 360rpx;
    margin: auto;
    border-radius: 20rpx;
    background-color: rgba($color: #000000, $alpha: 0.5);
    opacity: 0;
    transition: 0.26s;
    &.show {
      opacity: 1;
      z-index: 10;
    }
    .tip {
      margin-top: 20 rpx;
      height: 30rpx;
      line-height: 30rpx;
      text-align: center;
      font-size: 22rpx;
      color: #fff;
    }

    .voice_img {
      width: 140rpx;
    }
  }
  .socket_colse {
    position: fixed;
    top: 20rpx;
    width: 100%;
    .err-tip {
      align-items: center;
      justify-content: space-between;
      padding: 30upx 24upx;
      background-color: #2e8cef;
      color: #fff;
      .tip-btn {
        padding: 6upx 12upx;
        border: 1upx solid #2e8cef;
        border-radius: 6upx;
        font-size: 26upx;
      }
    }
  }
}
// 漂浮发送消息
.float-send-box {
  background: #fff;
  border-radius: 10rpx;
  margin: 20rpx;
  padding: 10rpx 20rpx;
  align-items: center;
  .left {
    image {
      width: 120rpx;
      height: 120rpx;
    }
  }
  .right {
    justify-content: space-around;
    margin-left: 30rpx;
    width: 100%;
    .right-top {
      justify-content: space-between;
      .build-name {
        font-size: 32rpx;
      }
      .right-close {
        color: #333;
        font-size: 32rpx;
        margin-right: 30rpx;
      }
    }
    .right-bottom {
      justify-content: space-between;
      align-items: center;
      .right-bottom-end {
        font-size: 24rpx;
        flex: 1;
        margin-right: 10rpx;
      }
      .send {
        padding: 10rpx 20rpx;
        background: #2e8cef;
        color: #fff;
        border-radius: 20rpx;
        margin-top: 20rpx;
      }
    }
  }
  
}
.float-send-box-customer{
    background: #fff;
    border-radius: 10rpx;
    margin: 20rpx;
    padding: 10rpx 20rpx;
    align-items: center;
    justify-content: space-between;
    .customer-left{
      justify-content: space-around;
      line-height: 38rpx;
    }
    .customer-right{
      align-items: center;
      .right-close{
        color: #333;
        font-size: 34rpx;
      }
      .send{
        margin-top: 20rpx ;
          padding: 10rpx 20rpx;
          background: #2e8cef;
          color: #fff;
          border-radius: 20rpx;
      }
    }
  }

  .float-send-box-customer {
  background: #fff;
  border-radius: 10rpx;
  margin: 20rpx;
  padding: 10rpx 20rpx;
  align-items: center;
  justify-content: space-between;
  .customer-left {
    justify-content: space-around;
    line-height: 38rpx;
  }
  .customer-right {
    align-items: center;
    .right-close {
      color: #333;
      font-size: 34rpx;
    }
    .send {
      margin-top: 20rpx;
      padding: 10rpx 20rpx;
      background: #2e8cef;
      color: #fff;
      border-radius: 20rpx;
    }
  }
}
.chat_list {
  text-align: center;
  padding: 20rpx 0;
}
</style>
