<template>
  <view class="body mine_c">
    <view class="mine">
      <view class="admin row">
        <view class="row">
          <view class="img-box">
            <image
              mode="aspectFill"
              @click="$navigateTo(`/user/user_card?id=${user_info.id}`)"
              :src="
                user_info.avatar
                  ? user_info.avatar
                  : 'https://dss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=2561659095,299912888&fm=26&gp=0.jpg'
              "
            />
          </view>
          <view class="admin-ctn-box">
            <view class="admin-ctn row">
              <view class="admin-label row">
                <text class="admin-name">
                  {{ user_info.name || user_info.nickname }}
                </text>
                <view class="label" v-if="user_info.id">
                  {{ user_info.category_name }}
                </view>
              </view>
              <view v-if="user_info.company_id === 0" class="company-name" @click="bindCompany"
                >暂未绑定公司</view
              >
              <view
                @click="bindCompany(user_info.company_store_code)"
                v-else
                class="company-name"
                >{{ user_info.company_name }}</view
              >
            </view>
          </view>
        </view>
        <!-- v-if="!user_info.id" -->
        <!-- @click="$navigateTo('/user/login')" -->
        <view class="login-out" @click="$navigateTo('/user/phone_login')" v-if="!user_info.id"
          >点击登录</view
        >
        <view
          v-else
          class="business-card"
          @click="$navigateTo(`/user/user_edit?id=${user_info.id}`)"
          >名片设置</view
        >
      </view>
      <view class="data_detail row" v-if="user_info.id">
        <text class="baobeo-icon baobeo-icon-ic_shuju3x"></text>
        <view class="data_title">数据明细</view>
      </view>
      <view class="data_table" v-if="user_info.id">
        <view class="nav">
          <view
            v-for="(nav, index) in navs"
            :key="nav.type"
            class="nav-item"
            :class="{ active: nav.type === data_type }"
            @click="onClickNav(index, nav)"
            >{{ nav.name }}</view
          >
        </view>
        <view class="data-list row">
          <view class="data-item" @click="$navigateTo(`/client/list?type=${1}`)">
            <view>
              <view class="top">{{ date_num.reported }}</view>
              <view class="bottom">报备</view>
            </view>
          </view>
          <view class="data-item" @click="$navigateTo(`/client/list?type=${2}`)">
            <view>
              <view class="top">{{ date_num.reception }}</view>
              <view class="bottom">带看</view>
            </view>
          </view>
          <view class="data-item" @click="$navigateTo(`/client/list?type=${4}`)">
            <view>
              <view class="top">{{ date_num.subscribe }}</view>
              <view class="bottom">认购</view>
            </view>
          </view>
          <view class="data-item" @click="$navigateTo(`/client/list?type=${5}`)">
            <view>
              <view class="top">{{ date_num.deal }}</view>
              <view class="bottom">成交</view>
            </view>
          </view>
        </view>
      </view>
      <view class="top_line row">
        <!-- <text class="line-content"
          >经纪人【尹刚晶】刚刚成交一套成刚刚成交一套成交一套大</text
        >-->
        <text class="baobeo-icon baobeo-icon-toutiao1x"></text>
        <view class="line-content">
          <swiper class="swiper" autoplay circular vertical>
            <swiper-item v-for="(item, index) in deal_list" :key="index">
              <text class="swiper-item">{{ item }}</text>
            </swiper-item>
          </swiper>
        </view>
      </view>
    </view>
    <!-- 会员 -->
    <view class="bottom_line" v-if="user_info.id">
      <view class="bottom_list tool_box" style="height: auto">
        <view class="label-top row">
          <text class="baobeo-icon baobeo-icon-yingyong"></text>
          <text class="label-title">会员应用</text>
        </view>
        <view class="content-bottom row">
          <view
            @click="
              openNewTab(
                item,
                user_info.audit_status,
                user_info.id,
                user_info.company_id,
                user_info
              )
            "
            class="ctn-box"
            v-for="item in vip_list"
            :key="item.id"
          >
            <image :src="item.icon_path | imageFilter('w_80')" />
            <view class="ctn-text">{{ item.desc }}</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 店长 -->
    <view
      class="bottom_line"
      v-if="
        (user_info.company_store_category === 0 && user_info.is_store_manager) ||
        user_info.company_store_category === 1
      "
    >
      <view class="bottom_list tool_box" style="height: auto">
        <view class="label-top row">
          <text class="icon-baobei-css icon-baobei-css-ic_guanli"></text>
          <text class="label-title">渠道管理</text>
        </view>
        <view class="content-bottom row">
          <view
            @click="openCompany(user_info, item)"
            class="ctn-box"
            v-for="item in company_list"
            :key="item.id"
          >
            <image :src="item.icon_path | imageFilter('w_80')" />
            <view class="ctn-text">{{ item.desc }}</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 案场助理 -->
    <view class="bottom_line" v-if="user_info.category == 3">
      <view class="bottom_list tool_box">
        <view class="label-top row">
          <text class="baobeo-icon baobeo-icon-anchang"></text>
          <text class="label-title">案场工作台</text>
        </view>
        <view class="content-bottom row">
          <view
            class="ctn-box"
            v-for="item in anchang_list"
            :key="item.id"
            @click="anClick(item, user_info)"
          >
            <!-- @click="$navigateTo(item.link_path + '?id=' + user_info.id)" -->
            <image :src="item.icon_path | imageFilter('w_80')" />
            <view class="ctn-text">{{ item.desc }}</view>
          </view>
        </view>
      </view>
    </view>

    <view class="bottom_line">
      <view class="bottom_list row" @click="handlePublic">
        <view class="icon-left row">
          <text class="baobeo-icon baobeo-icon-ic_gongzhonghao"></text>
          <view>关注公众号</view>
        </view>
        <view class="icon-right">
          <myIcon type="you"></myIcon>
        </view>
      </view>
      <!-- <view
        class="bottom_list row"
        @click="handleTfy(tfy_site_info)"
        v-if="tfy_site_info.id"
      >
        <view class="icon-left row">
          <image
            src="@/static/lianjie.png"
            style="margin-right:10rpx;width:20px;height:20px"
          ></image>
          <view>{{ tfy_site_info.name }}</view>
        </view>
        <view class="icon-right">
          <myIcon type="you"></myIcon>
        </view>
      </view> -->
      <view class="bottom_list row" @click="$navigateTo('/user/about_us')">
        <view class="icon-left row">
          <text class="baobeo-icon baobeo-icon-ic_guanyu3x1"></text>
          <view>关于我们</view>
        </view>
        <view class="icon-right">
          <myIcon type="you"></myIcon>
        </view>
      </view>
      <view class="bottom_list row" @click="$navigateTo('/user/advisory')">
        <view class="icon-left row">
          <text class="baobeo-icon baobeo-icon-ic_kefu3x1"></text>
          <view>咨询客服</view>
        </view>
        <view class="icon-right">
          <myIcon type="you"></myIcon>
        </view>
      </view>
      <view v-if="outLogin" class="bottom_list row" @click="logOut">
        <view class="icon-left row">
          <myIcon type="tuichu" size="40rpx"></myIcon>
          <view>退出登录</view>
        </view>
        <view class="icon-right">
          <myIcon type="you"></myIcon>
        </view>
      </view>
    </view>
    <shareTip :show="recommend_friends" @hide="recommend_friends = false"></shareTip>
    <myPopup :show="show_qrcode_popup" position="center" @hide="show_qrcode_popup = false">
      <invitePop :image="siteConfig.wx_pub_qr_code" @close="show_qrcode_popup = false"></invitePop>
    </myPopup>
    <myPopup :show="is_allow_popup" position="center" @hide="is_allow_popup = false">
      <phonePop @close="is_allow_popup = false"></phonePop>
    </myPopup>

    <BottomBar @click="switchTabBottom" :current="currentTabIndex"></BottomBar>
  </view>
</template>

<script>
import myIcon from "@/components/my-icon";
import { mapActions, mapState, mapMutations } from "vuex";
import myLoading from "@/components/my-loading";
import shareTip from "@/components/shareTip";
import invitePop from "@/components/invitePop";
import myPopup from "@/components/myPopup";
import phonePop from "@/components/phonePop";
export default {
  components: { myIcon, myLoading, shareTip, invitePop, myPopup, phonePop },
  filters: {},
  data () {
    return {
      navs: [
        // day yesterday week month
        { type: "day", name: "今日", value: 0 },
        { type: "yesterday", name: "昨日", value: 1 },
        { type: "week", name: "本周", value: 2 },
        { type: "month", name: "本月", value: 3 },
      ],
      anchang_list: [
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: 1,
          desc: "全部客户",
          link_path: `/project_broker/all_customers`,
        },
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: 2,
          desc: "我的客户",
          link_path: `/project_broker/my_customers?type=-1`,
        },
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: 3,
          desc: "我的楼盘",
          link_path: "/project_broker/my_build",
        },
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: 4,
          desc: "扫码报备",
        },
        // {
        //   icon_path:
        //     "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/img/huokezhushou.png",
        //   id: 5,
        //   desc: "获客助手",
        //   link_path: "/project_broker/get_customer",
        // },
      ],
      vip_list: [
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: "1",
          desc: "推荐好友",
          isShow: true,
        },
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: "2",
          desc: "我的收藏",
          link_path: "/commission/my_collection",
          isShow: true,
        },
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: "3",
          desc: "帮助手册",
          link_path: "/commission/help_manual",
          isShow: true,
        },
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: "4",
          desc: "推广码",
          isShow: true,
        },
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: "5",
          desc: "银行卡",
          link_path: "/commission/bank_card",
          isShow: true,
        },
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: "6",
          desc: "实名认证",
          link_path: "/user/add_verified",
          isShow: true,
        },
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: "7",
          desc: "佣金明细",
          link_path: "/commission/commission_detail",
          isShow: true,
        },
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: "8",
          desc: "我的客户",
          link_path: "/client/list?type=-1",
          isShow: true,
        },
        {
          icon_path: "https://img.tfcs.cn/static/img/ziliaochaxun.png",
          id: "9",
          link_path: "/client/data_query",
          isShow: true,
          desc: "资料查询",
        },
      ],
      company_list: [
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: "1",
          desc: "渠道入口",
          link_path: "/company/channel_list",
        },
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: "2",
          desc: "公司入口",
          link_path: "/company/management",
        },
        {
          icon_path: "https://img.tfcs.cn/static/img/<EMAIL>",
          id: "3",
          desc: "推荐员工",
          link_path: "/company/management",
        },
      ],
      data_type: "day",
      date_num: {},
      deal_list: [],
      outLogin: true,
      link_path: "",
      recommend_friends: false,
      show_qrcode_popup: false,
      is_allow_popup: false,
      ms_number: "",
      company_name_label: "门店",
      tfy_site_info: {},
      currentTabIndex: 3,
    };
  },
  onReady () { },
  onHide () {
    // this.is_allow_popup = false;
  },
  onShow () {
    setTimeout(() => {
      let token = uni.getStorageSync("token" + this.$store.state.website_id);
      if (!token) {
        this.outLogin = false;
        this.setUserInfo({});
      } else {
        this.outLogin = true;
        this.getConfigUser();
      }
    }, 500);
    // 绑定真实姓名
    // this.getConfigUser();
    // this.getUserInfo();
    // this.getSetting();
    this.onClickNav();
    this.setUrlWebsiteId();
  },
  onLoad (options) {
    if (options.recommend_code || uni.getStorageSync("recommend_code")) {
      var recommend_code =
        options.recommend_code || uni.getStorageSync("recommend_code");
      uni.setStorageSync("recommend_code", recommend_code);
      this.$ajax.get(
        `/client/my/bind/recommend_user/${recommend_code}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            uni.showToast({
              title: "已推荐",
            });
            uni.removeStorageSync("recommend_code");
          } else {
            uni.showToast({
              title: res.data.message || "推荐失败",
              icon: "none",
            });
            uni.removeStorageSync("recommend_code");
          }
        }
      );
    }
    // 判断分享邀请公司员工（未登录）
    if (options.company_code || uni.getStorageSync("company_code")) {
      var company_code =
        options.company_code || uni.getStorageSync("company_code");
      uni.setStorageSync("company_code", company_code);
      this.$ajax.get(`/client/my/bind/company/${company_code}`, {}, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "绑定成功",
          });
          uni.removeStorageSync("company_code");
        } else {
          uni.showToast({
            title: res.data.message || "绑定失败",
            icon: "none",
          });
          uni.removeStorageSync("company_code");
        }
      });
    }
    this.getTfySiteInfo();
  },
  computed: {
    ...mapState(["user_info", "siteConfig", "city"]),
  },
  methods: {
    ...mapActions(["getUserInfo", "getSetting"]),
    ...mapMutations(["setUserInfo"]),
    switchTabBottom (index, item) {
      if (this.currentTabIndex == index) {
        return;
      }
      uni.switchTab({
        url: item.path,
      });
    },
    // 获取跳转腾房云站点信息
    getTfySiteInfo () {
      this.$ajax.get("/client/website/queryTfyWebsite", {}, (res) => {
        if (res.statusCode === 200) {
          this.tfy_site_info = res.data;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    handleTfy (site) {
      window.location.href = site.url;
    },
    // 获取配置和用户信息
    getConfigUser () {
      this.getUserInfo({
        success: (res) => {
          if (res.statusCode === 200) {
            if (res.data.is_searcher !== 1) {
              this.vip_list = this.vip_list.filter((item) => {
                return item.id !== "9";
              });
            }
            if (res.data.is_channel_manager !== 1) {
              this.company_list = this.company_list.filter((item) => {
                return item.id !== "1";
              });
            }
            this.ms_number += 1;
            if (!res.data.phone) {
              // this.is_allow_popup = true;
            } else {
              this.is_allow_popup = false;
              this.isInvite();
            }
            this.getSetting((e) => {
              this.ms_number++;
              this.bindingUserName();
              if (e.open_real_name === 1) {
                return;
              } else {
                this.vip_list = this.vip_list.filter((item) => {
                  return item.id !== "6";
                });
              }
            });
            this.getDealBigNews();
          } else {
            uni.showToast({
              title: res.data.message || "获取信息失败",
              icon: "none",
            });
          }
        },
      });
    },
    // 判断是否关注公众号
    isInvite () {
      this.$ajax.get("/client/my/is_subscribe/wx_pub", {}, (res) => {
        if (res.statusCode === 200) {
          var result = res.data.result;
          const isQrcode = () => {
            return !this.user_info.wx_open_id || result === -1 || !result;
          };
          this.show_qrcode_popup = isQrcode() ? true : false;
          // isQrcode()
          //   ? (this.show_qrcode_popup = true)
          //   : (this.show_qrcode_popup = false);
        }
      });
    },
    // 获取成交头条内容
    getDealBigNews () {
      this.$ajax.get(
        "/common/my/dealBigNews",
        {
          company_id: this.user_info.company_id || 0,
          region_0: this.city.region_0 || 0,
          region_1: this.city.region_1 || 0,
        },
        (res) => {
          if (res.statusCode === 200) {
            res.data.length > 0
              ? (this.deal_list = res.data)
              : (this.deal_list = this.siteConfig.default_deal_big_news.split(
                "|"
              ));
          }
        }
      );
    },
    onClickNav (index, nav) {
      let token = uni.getStorageSync("token" + this.$store.state.website_id);
      if (!token) {
        return;
      }
      this.data_type = nav ? this.navs[index].type : "day";
      this.$ajax.get(
        `/client/my/statistic`,
        { type: this.data_type },
        (res) => {
          if (res.statusCode === 200) {
            this.date_num = res.data;
          }
        }
      );
    },
    // 退出登录
    logOut () {
      uni.showModal({
        title: "提示",
        content: "是否退出登录",
        success: (res) => {
          if (res.confirm) {
            this.logOutToken();
          }
        },
      });
    },
    logOutToken () {
      this.$ajax.get("/auth/client/logout", {}, (res) => {
        if (res.statusCode === 200) {
          this.setUserInfo({});
          uni.removeStorageSync("token" + this.$store.state.website_id);
          uni.removeTabBarBadge({
            index: 2,
          });
          uni.showToast({
            title: "退出登录",
          });
          this.outLogin = false;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    // 判断是否绑定真实姓名
    bindingUserName (ms) {
      // 添加判断站点是否开启需要填写真实姓名，以及用户是否填写真实姓名
      setTimeout(() => {
        if (
          this.siteConfig.client_user_force_setting_name === 1 &&
          this.user_info.name === "" &&
          this.ms_number >= 2
        ) {
          uni.showModal({
            title: "提示",
            content: "请绑定真实姓名",
            showCancel: false,
            success: (e) => {
              this.$navigateTo(`/user/user_edit?id=${this.user_info.id}`);
            },
          });
        }
      }, ms);
    },
    // 判断是否实名认证如果认证跳转到信息列表，没有就去认证
    openNewTab (item, status, id, company_id, user_info) {
      var obj = {
        item: item,
        status: status,
        id: id,
        company_id: company_id,
        user_info: user_info,
      };
      this.$debounce(this.openNewTabdebounce, 500)(obj);
    },
    openNewTabdebounce (obj) {
      switch (obj.item.id) {
        case "6":
          this.$ajax.get("/client/user/real_name/query", {}, (res) => {
            if (res.statusCode === 200) {
              let user = res.data;
              var actions = {
                1: ["已实名认证", "/user/user_verified"],
                2: ["审核未通过，请重新上传认证", obj.item.link_path],
                0: ["审核中请稍等"],
              };
              if (!user) {
                uni.showToast({
                  title: "未实名认证",
                  icon: "none",
                });
                setTimeout(() => {
                  this.$navigateTo(obj.item.link_path);
                }, 500);
              } else {
                this.checkStatus(user.audit_status, actions);
              }
            }
          });
          break;
        case "1":
          this.getSetting((e) => {
            this.share = {
              forward_title: e.name,
              forward_desc: e.invite_description,
              forward_pic: e.logo,
              forward_recommend_code: this.user_info.recommend_code,
            };
            this.getWxConfig();
            this.recommend_friends = true;
            if (
              this.$isWxWork() === "wxwork" ||
              this.$isWxWork() === "com-wx-pc"
            ) {
              this.share = {
                forward_title: e.name,
                forward_desc: e.invite_description,
                forward_pic: e.logo,
                forward_recommend_code: this.user_info.recommend_code,
                forward_config: "recommend",
              };
              // this.getWxQyWxConfig();
            }
          });
          break;
        case "4":
          this.$navigateTo(`/user/recommend_code?id=${obj.id}`);
          break;
        case "7":
          if (obj.company_id) {
            this.$ajax.get(
              `/client/company/query/${obj.company_id}`,
              {},
              (res) => {
                if (res.statusCode === 200) {
                  res.data.allow_withdraw === 0 &&
                    obj.user_info.is_store_manager === false
                    ? uni.showToast({
                      title: "请联系公司进行结佣",
                      icon: "none",
                    })
                    : this.$navigateTo(obj.item.link_path);
                }
              }
            );
          } else {
            this.$navigateTo(obj.item.link_path);
          }
          break;
        default:
          this.$navigateTo(obj.item.link_path);
          break;
      }
    },
    // 优化多个if的方法(案场管理)
    checkStatus (status, actions) {
      let action = actions[status];
      if (action[1]) {
        setTimeout(() => {
          this.$navigateTo(action[1]);
        }, 500);
      } else {
        const isWork = () => {
          return (
            this.$isWxWork() === "wxwork" || this.$isWxWork() === "com-wx-pc"
          );
        };
        if (isWork()) {
          // 企业微信
          this.openScan = 1;
          // this.getWxQyWxConfig();
        } else {
          this.openScan = 1;
          this.getWxConfig();
        }
      }
    },
    handlePublic () {
      this.show_qrcode_popup = true;
    },
    anClick (item, user_info) {
      this.$debounce(
        this.anClickdebounce,
        500
      )({ item: item, user_info: user_info });
    },
    anClickdebounce (obj) {
      var actions = {
        2: ["", obj.item.link_path + "&id=" + obj.user_info.id],
        4: ["请返回微信进行扫码"],
        1: ["", obj.item.link_path + "?id=" + obj.user_info.id],
        3: ["", obj.item.link_path + "?id=" + obj.user_info.id],
        5: ["", obj.item.link_path + "?id=" + obj.user_info.id],
      };
      this.checkStatus(obj.item.id, actions);
    },
    // 绑定公司
    bindCompany (company_store_code) {
      if (typeof company_store_code !== "object") {
        this.$navigateTo(
          `/company/bind_company?company_store_code=${company_store_code}`
        );
      } else {
        this.$navigateTo(`/company/bind_company`);
      }
    },
    openCompany (user, item) {
      this.$debounce(this.openCompanydebounce, 500)({ user: user, item: item });
    },
    openCompanydebounce (obj) {
      if (obj.item.id == 2) {
        if (obj.user.company_store_category === 1) {
          this.$navigateTo(`/company/company_list?id=${obj.user.company_id}`);
        } else {
          this.$navigateTo(`/company/management?id=${obj.user.company_id}`);
        }
      } else if (obj.item.id == 3) {
        // if (obj.user.company_store_category === 1) {
        //   uni.showToast({
        //     title: "仅支持门店用户邀请员工加入",
        //     icon: "none",
        //   });
        //   return;
        // }
        var url = window.location.href;
        this.getSetting((e) => {
          this.share = {
            forward_title: this.user_info.company_name,
            forward_desc: this.user_info.company_name + `邀请您加入团队`,
            forward_pic: e.logo,
            link: url + `&company_code=${obj.user.company_store_code}`,
          };
          this.getWxConfig();
          this.recommend_friends = true;
          if (
            this.$isWxWork() === "wxwork" ||
            this.$isWxWork() === "com-wx-pc"
          ) {
            this.share = {
              forward_title: this.user_info.company_name,
              forward_desc: this.user_info.company_name + `邀请您加入团队`,
              forward_pic: e.logo,
              link: url + `&company_code=${obj.user.company_store_code}`,
              forward_config: "company",
            };
            // this.getWxQyWxConfig();
          }
        });
      } else if (obj.item.id == 1) {
        this.$navigateTo(obj.item.link_path + `?id=${obj.user.company_id}`);
      }
    },
    setUrlWebsiteId () {
      let nowlink = window.location.href;
      const reg = /\?.+=.{0,}/;
      if (
        nowlink.indexOf("?website_id=") === -1 &&
        nowlink.indexOf("&website_id=") === -1
      ) {
        if (reg.test(nowlink)) {
          nowlink += `&website_id=${this.$store.state.website_id || 1}`;
        } else {
          nowlink += `?website_id=${this.$store.state.website_id || 1}`;
        }
        history.replaceState(null, " ", nowlink);
      }
    },
  },
  onPullDownRefresh: function () {
    this.getUserInfo();
    this.getSetting();
    this.onClickNav();
    uni.stopPullDownRefresh();
  },
  onTabItemTap (e) {
    this.setUrlWebsiteId();
    uni.$emit("closeChat");
  },
};
</script>
<style lang="scss" scoped>
// 彩色图标
.icon-baobei-css-ic_guanli {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22icon-baobei-css-ic_guanli%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M967.111111%2056.888889v682.666667H56.888889V56.888889h910.222222z%20m-113.777778%20113.777778H170.666667v455.111111h682.666666V170.666667z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M227.555556%20227.555556m56.888888%200l0%200q56.888889%200%2056.888889%2056.888888l0%20227.555556q0%2056.888889-56.888889%2056.888889l0%200q-56.888889%200-56.888888-56.888889l0-227.555556q0-56.888889%2056.888888-56.888888Z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M455.111111%20341.333333m56.888889%200l0%200q56.888889%200%2056.888889%2056.888889l0%20113.777778q0%2056.888889-56.888889%2056.888889l0%200q-56.888889%200-56.888889-56.888889l0-113.777778q0-56.888889%2056.888889-56.888889Z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M682.666667%20284.444444m56.888889%200l0%200q56.888889%200%2056.888888%2056.888889l0%20170.666667q0%2056.888889-56.888888%2056.888889l0%200q-56.888889%200-56.888889-56.888889l0-170.666667q0-56.888889%2056.888889-56.888889Z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M56.888889%20796.444444h910.222222v113.777778H56.888889z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
.baobeo-icon-ic_shuju3x {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22baobeo-icon-ic_shuju3x%22%20viewBox%3D%220%200%201077%201024%22%3E%3Cpath%20d%3D%22M53.894737%20377.263158h215.578947v592.842105H53.894737zM808.421053%200h215.578947v970.105263h-215.578947zM431.157895%20161.684211h215.578947v808.421052H431.157895z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M0%20916.210526h1077.894737v107.789474H0z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
.baobeo-icon-toutiao1x {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22baobeo-icon-toutiao1x%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M1023.6928%20177.5616A185.2416%20185.2416%200%200%200%20839.1168%200H184.832A184.8832%20184.8832%200%200%200%200%20184.8832v54.4256l1023.6928-61.7472z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M0%20853.8112a184.576%20184.576%200%200%200%20184.8832%20169.8816h653.8752a184.8832%20184.8832%200%200%200%20184.9344-184.576v-47.36L0%20853.8112z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M566.8864%20499.0464c-5.7856%202.56-17.92%200-23.3984%202.56a69.12%2069.12%200%200%200-16.896%200c-1.792%2017.408-1.2288%2034.9184%201.536%2052.1216l54.7328-2.56c5.8368-2.6624%2011.4688-5.632%2016.9472-8.96%2024.32-10.5472%2046.3872-22.3744%2069.12-33.8944%2020.4288-7.5776%2040.0384-17.2032%2058.5216-28.7744%2016.384%203.328%2032.256%209.0624%2047.0528%2016.9472%2019.8144%207.68%2038.7072%2011.2128%2058.5216%2019.5072%2014.4896%206.8608%2029.3376%2012.9536%2044.4416%2018.2272l57.2928-3.84v-1.5872c2.4064-17.3056%202.4064-34.816%200-52.1216l-39.0144%203.1744a469.6064%20469.6064%200%200%201-45.7728-18.2272%20206.6432%20206.6432%200%200%201-46.6944-16.64l128-64a479.8464%20479.8464%200%200%200%200-91.4944c-64%205.12-124.8256%207.68-192%2011.8784a270.6432%20270.6432%200%200%201-71.68%202.56l-61.0816-30.1056c-17.2544%2028.8256-37.0688%2056.6272-54.6816%2084.7872-6.7584%2015.104-15.616%2029.184-26.2144%2041.9328%202.8672%207.9872%2012.8%207.3216%2020.7872%2010.24%2014.9504%204.0448%2029.44%209.728%2043.1616%2016.896%208.96-1.536%2010.24-11.4688%2014.3872-18.176%2010.5472-16.64%2023.3472-33.9456%2033.9456-50.8928%2023.9104%201.1776%2047.9232-0.1024%2071.68-3.84h25.856c12.8-1.8944%2030.4128%200%2043.2128-3.84%2022.6816-5.12%2050.8416%202.8672%2071.68-4.1472v1.5872a17.6128%2017.6128%200%200%201%200%208.96c-4.5056%208.6528-21.504%2011.2128-30.1056%2015.6672l-62.3616%2032a112.2816%20112.2816%200%200%200-24.9856%2011.8272%20223.9488%20223.9488%200%200%201-57.2416-21.0944%20118.0672%20118.0672%200%200%201-35.84-11.8272c-10.24%202.56-25.6%2037.0688-33.8944%2045.7216%201.8944%208.3456%2042.24%2020.1728%2052.1216%2023.3472v1.5872c-30.3616%2014.4384-59.4944%2032-90.2144%2044.4928h-0.9216z%20m-109.4144%2099.1744c7.4752%200.512%2015.0016%200%2022.3744-1.5872a199.6288%20199.6288%200%200%200%200-52.7872l-95.9488%205.12V393.472a311.9104%20311.9104%200%200%200%200-71.68l-35.2256%202.56c-7.0144%200-17.2544-1.8944-22.016%200a95.9488%2095.9488%200%200%200-22.4256%200v156.4672c2.7648%2023.808%202.7648%2047.8208%200%2071.68l-230.9632%2013.1072v52.1216c30.5664%200.9216%2061.184-0.768%2091.4944-5.12%2014.336-2.8672%2032%203.5328%2042.8544-2.56h26.2144c-5.632%205.376-10.496%2011.4688-14.336%2018.2272-10.24%2014.3872-21.8112%2028.4672-32.0512%2043.2128-6.4%208.6016-17.2544%2016.64-21.0944%2027.4944-12.8%203.1744-64%200-72.96%206.4a69.12%2069.12%200%200%200-16.896%200v52.1216c23.552%200.6144%2047.104-1.0752%2070.3488-5.12%208.96-1.8944%2021.76%200%2028.7744%200%2013.4656-2.8672%2037.4272%203.2256%2047.0528-2.56%209.5744-5.7344%2026.8288-31.9488%2033.8944-43.1616%2024.9344-34.56%2057.856-68.4544%2080.896-104.2944a466.7392%20466.7392%200%200%200%2076.8-5.12c13.1072-3.1744%2037.12%202.8672%2046.6944-2.8672l-3.4816%203.84z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M167.936%20432.4864c6.8096%202.0992%2014.0288%202.0992%2020.7872%200l67.1744-2.56c0-12.4416-12.1344-17.92-18.2272-25.9072-13.7216-20.1216-31.6416-36.1472-43.52-55.9616l-63.9488%203.84h-24.9344c3.84%2010.5472%2014.6944%2017.5616%2021.0944%2025.9072%2012.288%2018.944%2026.2144%2036.864%2041.5744%2053.4016v1.28z%20m2.8672%20101.7344c8.6016%201.4336%2017.3568%201.4336%2025.9584%200l59.136-4.1472c-2.2016-9.2672-10.8544-14.08-15.6672-20.7872-10.24-13.7728-20.1216-26.5728-30.0544-40.2944-4.8128-6.4-12.8-11.52-15.6672-19.8656a199.3216%20199.3216%200%200%200-54.7328%202.8672c-7.68%200-18.2272-2.2016-23.6544%200a39.3728%2039.3728%200%200%200-11.52%200c12.4928%2019.8656%2032%2036.1984%2044.1344%2054.7328%205.7856%207.68%2011.52%2021.4528%2021.1456%2024.9344l0.9216%202.56z%22%20fill%3D%22%23FF7401%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M934.7584%20598.2208v-51.2c-59.4944%200-106.8544%208.3456-164.096%207.9872v-24.9344c-16.9984-0.8192-34.048%200-50.8928%202.56-7.9872%201.8944-20.1728-2.2528-26.2144%200h-2.56c1.3312%208.704%201.3312%2017.5616%200%2026.2144l-162.816%208.96v52.1728a275.456%20275.456%200%200%200%2054.6816-3.84c16.3328-3.5328%2033.8944%200%2048.2816-2.56%2014.4384-2.56%2038.0928%203.4816%2048.0256-2.56a50.5344%2050.5344%200%200%201%2013.1072%200v77.1072a150.3744%20150.3744%200%200%200%200%2036.4544c20.48-1.9456%2052.736-3.84%2077.1072-5.12v-78.336c0-11.264%201.3824-22.4256%204.096-33.28%2054.4256%200%20107.52-5.7856%20160-9.0112l1.28-0.6144z%20m-507.0336%2016.64l-87.3472%204.1472c3.84%2013.5168%2010.0352%2026.3168%2018.2272%2037.7344%2014.6944%2028.8256%2029.7472%2056.6272%2042.8544%2086.0672l54.7328-2.56c10.752%201.28%2021.6064%200.4096%2032-2.56%202.8672-7.68-11.8784-25.2928-15.7184-32-15.6672-30.3616-29.0816-61.44-45.4144-90.2144l0.6656-0.6144z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M892.8256%20614.8608h-5.12c-3.84%202.56-13.4144%200-19.456%201.5872a184.8832%20184.8832%200%200%200-53.4528%205.12c8.448%2010.8032%2015.5136%2022.6304%2021.0944%2035.1744%2011.8272%2019.5072%2025.2928%2038.4%2036.4544%2058.88%2021.4528%200.4608%2042.8544-0.9216%2064-4.1472h18.2272a1129.5744%201129.5744%200%200%200-58.5216-97.8944l-3.2256%201.28z%20m-235.776%2013.1072c-4.7616%203.1744-14.0288%200-20.7872%201.5872-15.36%203.2256-45.056%200-54.9888%205.12-9.9328%205.12-8.6528%2016.3328-12.8%2023.3472-9.6256%2015.6672-18.8928%2032-27.5456%2049.6128-4.4544%207.9872-6.4%2018.8416-13.1072%2024.9344a17.92%2017.92%200%200%201%200%203.84l79.9744-3.84c12.4928-24.32%2026.2656-49.2544%2039.0656-73.216%203.4304-9.1136%207.8336-17.7664%2013.1072-25.9584%200-4.1472%200-3.1744-2.56-5.4272h-0.3584z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
.baobeo-icon-ic_guanyu3x1 {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22baobeo-icon-ic_guanyu3x1%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M512%2056.888889a455.111111%20455.111111%200%201%201%200%20910.222222A455.111111%20455.111111%200%200%201%20512%2056.888889z%20m0%20113.777778a341.333333%20341.333333%200%201%200%200%20682.666666A341.333333%20341.333333%200%200%200%20512%20170.666667z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M512%20341.333333m-56.888889%200a56.888889%2056.888889%200%201%200%20113.777778%200%2056.888889%2056.888889%200%201%200-113.777778%200Z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M455.111111%20455.111111h113.777778v284.444445H455.111111z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
.baobeo-icon-ic_gongzhonghao {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22baobeo-icon-ic_gongzhonghao%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M56.888889%2056.888889h796.444444v910.222222H56.888889V56.888889z%20m227.555555%20284.444444a56.888889%2056.888889%200%201%200%200-113.777777%2056.888889%2056.888889%200%200%200%200%20113.777777zM227.555556%20398.222222h227.555555v56.888889H227.555556V398.222222zM170.666667%20170.666667v682.666666h568.888889V170.666667H170.666667z%22%20fill%3D%22%23333333%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M711.111111%20711.111111m-256%200a256%20256%200%201%200%20512%200%20256%20256%200%201%200-512%200Z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M711.111111%20455.111111a256%20256%200%201%201%200%20512%20256%20256%200%200%201%200-512z%20m0%2056.888889a199.111111%20199.111111%200%201%200%200%20398.222222%20199.111111%20199.111111%200%200%200%200-398.222222z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M573.326222%20710.485333l40.220445-40.220444%20120.661333%20120.661333-40.220444%2040.220445z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M653.767111%20790.926222l160.881778-160.881778%2040.220444%2040.220445L694.044444%20831.146667z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
.baobeo-icon-anchang {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22baobeo-icon-anchang%22%20viewBox%3D%220%200%201137%201024%22%3E%3Cpath%20d%3D%22M758.499556%20195.925333c0-32.426667-24.405333-59.107556-55.808-62.748444l-7.395556-0.455111H442.481778c-32.426667%200-59.164444%2024.462222-62.805334%2055.864889l-0.398222%207.395555v63.146667H252.814222v-63.146667A189.610667%20189.610667%200%200%201%20442.481778%206.257778h252.814222a189.610667%20189.610667%200%200%201%20189.667556%20189.610666v63.203556h-126.464v-63.146667zM1074.574222%20259.185778c34.929778%200%2063.203556%2028.273778%2063.203556%2063.146666v632.149334c0%2034.872889-28.273778%2063.146667-63.203556%2063.146666H63.146667A63.203556%2063.203556%200%200%201%200%20954.538667V322.389333c0-34.929778%2028.273778-63.203556%2063.203556-63.203555H1074.631111z%20m-63.203555%20126.407111H126.407111v505.685333h884.963556v-505.742222z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M1074.574222%20259.185778c34.929778%200%2063.203556%2028.273778%2063.203556%2063.146666V512l-568.888889%20316.074667L0%20512V322.389333c0-34.929778%2028.273778-63.203556%2063.203556-63.203555H1074.631111z%20m-63.203555%20126.407111H126.407111v51.996444L568.888889%20683.349333l442.481778-245.76v-51.996444z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
.baobeo-icon-ic_fankui3x1 {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22baobeo-icon-ic_fankui3x1%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M341.333333%20853.333333h625.777778v113.777778H341.333333z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M56.888889%20695.637333L632.490667%2051.996444l295.480889%20285.639112L555.235556%20773.12l-86.471112-74.012444%20303.104-354.133334-133.461333-129.024L170.666667%20739.043556v114.232888L341.333333%20853.333333v113.777778H56.888889z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
.baobeo-icon-yingyong {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22baobeo-icon-yingyong%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M425.175579%2059.877053V479.124211H5.928421V59.823158H425.229474zM305.421474%20179.631158H125.736421v179.685053h179.685053V179.631158zM425.175579%20598.824421v419.193263H5.928421V598.770526H425.229474z%20m-119.754105%20119.754105H125.736421v179.685053h179.685053v-179.685053z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M1011.226947%20121.263158L902.736842%20526.174316%20497.825684%20417.684211%20606.315789%2012.773053l404.911158%20108.490105zM864.471579%20205.931789l-173.541053-46.457263-46.457263%20173.541053%20173.541053%2046.457263%2046.457263-173.541053z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M964.122947%20598.824421v419.193263H544.875789V598.770526h419.193264z%20m-119.754105%20119.754105h-179.685053v179.685053h179.685053v-179.685053z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
.baobeo-icon-ic_kefu3x1 {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22baobeo-icon-ic_kefu3x1%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M796.444444%20455.111111a284.444444%20284.444444%200%200%200-568.604444-12.344889L227.555556%20455.111111v170.666667H113.777778V455.111111a398.222222%20398.222222%200%201%201%20796.444444%200v170.666667h-113.777778V455.111111z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M56.888889%20512h227.555555v284.444444H56.888889z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M113.777778%20796.444444h113.777778v170.666667H113.777778z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M170.666667%20910.222222h398.222222v56.888889H170.666667zM568.888889%20853.333333h227.555555v113.777778h-227.555555z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M739.555556%20512h227.555555v284.444444h-227.555555z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
page {
  background: #f3f3f3;
}
.icon-shuju {
  width: 40rpx;
  height: 40rpx;
  fill: currentColor;
  overflow: hidden;
}
.icon-font {
  width: 32rpx;
  height: 32rpx;
  fill: currentColor;
  overflow: hidden;
  margin-right: 10rpx;
}
.mine {
  padding: 0 48rpx;
  background: #fff;
}
.admin {
  margin-top: 24rpx;
  align-items: center;
  justify-content: space-between;
  .img-box {
    width: 128rpx;
    height: 128rpx;
    image {
      width: 128rpx;
      height: 128rpx;
      border-radius: 50%;
    }
  }
  .admin-ctn-box {
    width: 360rpx;
    justify-content: center;
    .admin-ctn {
      justify-content: center;
      margin-left: 24rpx;
      flex-direction: column;
      width: auto;
      .admin-name {
        font-size: 32rpx;
        font-weight: bold;
        // margin-right: 16rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .company-name {
        margin-top: 30rpx;
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .label {
    margin-left: 12rpx;
    font-size: 24rpx;
    color: #fff;
    padding: 6rpx;
    background: #0174ff;
    border-radius: 10px 0 10px 0;
  }
  .login-out {
    height: 60rpx;
    line-height: 60rpx;
    width: 140rpx;
    border-radius: 8rpx;
    color: #fff;
    align-items: center;
    background: #0174ff;
  }
  .business-card {
    font-size: 32rpx;
    color: #0174ff;
  }
}
.data_detail {
  margin-top: 48rpx;
  align-items: flex-end;
  .data_title {
    font-size: 28rpx;
    font-weight: bold;
    margin-left: 10rpx;
  }
}
.data_table {
  width: 654rpx;
  height: 248rpx;
  background: #0174ff;
  margin: 48rpx 0;
  box-shadow: 0 8rpx 16rpx 0 rgba(1, 116, 255, 0.4);
  border-radius: 24rpx;
  color: #ffffff;
  .data-list {
    line-height: 48rpx;
    justify-content: space-around;
    margin: 48rpx 0;
    text-align: center;
    .top {
      font-size: 48rpx;
    }
    .bottom {
      font-size: 24rpx;
    }
  }
}
.top_line {
  justify-content: space-evenly;
  align-items: center;
  margin-bottom: 20rpx;
  .line {
    height: 80rpx;
    width: 8rpx;
    background: #0174ff;
  }
  .line-title {
    font-weight: bold;
    width: 80rpx;
    font-size: 40rpx;
    color: #333333;
    text-align: center;
    margin: 0 16rpx;
  }
  .line-content {
    height: 40rpx;
    line-height: 40rpx;
    flex: 1;
    uni-swiper {
      height: 40rpx;
      background: #f5f9fb;
      padding: 0 24rpx;
      margin-left: 16rpx;
      .swiper-item {
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }
  }
}
.bottom_line {
  margin-top: 16rpx;
  padding: 0 48rpx;
  background: #fff;
  .bottom_list {
    justify-content: space-between;
    padding: 30rpx 0;
    align-items: center;
    font-size: 32rpx;
    .icon-left {
      align-items: center;
      text {
        margin-right: 10rpx;
      }
    }
  }
  .tool_box {
    align-items: flex-start;
    .label-top {
      align-items: center;
      .label-title {
        margin-left: 16rpx;
        font-weight: bold;
        font-size: 28rpx;
        color: #333;
      }
    }
    .content-bottom {
      width: 100%;
      justify-content: flex-start;
      flex-wrap: wrap;
      .ctn-box {
        margin-top: 30rpx;
        align-items: center;
        width: 160rpx;

        image {
          width: 56rpx;
          height: 56rpx;
        }
        .ctn-text {
          font-size: 22rpx;
          margin-top: 12rpx;
          color: #333;
        }
      }
      &::after {
        content: '';
        width: 160rpx;
      }
    }
  }
}
// 导航
.nav {
  border-bottom: 1rpx solid #0068e6;
  flex-direction: row;
  padding-top: 28rpx;
  // justify-content: space-evenly;
  justify-content: space-between;
  font-size: 24rpx;
  .nav-item {
    color: #fff;
    padding: 14rpx 5rpx;
    text-align: center;
    margin: 0 50rpx;
    &.active {
      font-size: 28rpx;
      font-weight: bold;
      border-bottom: 8rpx solid #ffffff;
    }
  }
}
.data-item {
  margin: 0 50rpx;
}
.mine_c {
  padding-bottom: 100rpx;
}
.mine_c ::v-deep .border-row {
  position: fixed;
}
</style>
