<template>
  <view class="list">
    <view class="top-title">+添加银行卡</view>
    <view class="form">
      <view class="input-box">
        <input
          @input="onInput"
          type="text"
          class="uni-input"
          v-model="form.card_no"
          placeholder="卡号"
          placeholder-class="input-placeholder"
        />
      </view>
      <view class="input-box">
        <input
          type="text"
          class="uni-input"
          v-model="form.real_name"
          placeholder="持卡人姓名"
          placeholder-class="input-placeholder"
        />
      </view>
      <view class="input-box row input-bank" @click="showBankType">
        <input
          disabled="true"
          type="text"
          class="uni-input"
          v-model="bank_name"
          placeholder="开户银行"
          placeholder-class="input-placeholder"
        />
        <myIcon type="you" size="20rpx"></myIcon>
      </view>
      <view :class="{ submit: disabled }" class="btn" @click="onSubmit"
        >提交</view
      >
    </view>
    <!-- 弹出选择框 -->
    <VuePicker
      :data="pickDataBank"
      title="请选择银行卡类型"
      cancelText="取消"
      confirmText="确认"
      :showToolbar="true"
      @cancel="cancelBank"
      @confirm="confirmBank"
      :visible.sync="pickerVisibleBank"
    />
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import VuePicker from "vue-pickers";
import myIcon from "@/components/my-icon";
export default {
  components: { myIcon, VuePicker },
  data() {
    return {
      form: {
        card_no: "",
        real_name: "",
        bank_name: "",
      },
      pickDataBank: [],
      pickerVisibleBank: false,
      bank_name: "",
      params: {
        per_page: "20",
      },
      disabled: false,
    };
  },
  onLoad() {
    this.init();
  },
  watch: {
    ["form.card_no"](val) {
      this.$nextTick(() => {
        this.form.card_no = val.replace(/\s/g, "").replace(/....(?!$)/g, "$& ");
      });
    },
  },
  methods: {
    init() {
      this.$getDictionaryList("BANK_NAME", this.params, (res) => {
        if (res.statusCode === 200) {
          var arr = res.data.data.map((item) => {
            return {
              value: item.value,
              label: item.description,
            };
          });
          this.pickDataBank.push(arr);
          this.bank_name = arr[0].label;
        }
      });
    },
    cancelBank() {},
    confirmBank(res) {
      res.map((item) => {
        this.bank_name = item.label;
      });
    },
    showBankType() {
      this.pickerVisibleBank = true;
    },
    // 银行卡效验
    validBankNum(bankNum) {
      var pattern = /^\d{16}|\d{19}$/;
      if (pattern.test(bankNum)) {
        return true;
      } else {
        return false;
      }
    },
    onInput(e) {
      let value = e.detail.value;
      this.disabled = value ? true : false;
    },
    onSubmit() {
      this.form.card_no = this.form.card_no.replace(/\s/g, "");
      let rel_code = this.validBankNum(this.form.card_no);
      if (rel_code === false) {
        uni.showToast({
          title: "请输入正确卡号",
          icon: "none",
        });
        return false;
      } else {
        this.$ajax.post(
          "/client/user/bank_card/create",
          {
            card_no: this.form.card_no,
            real_name: this.form.real_name,
            bank_name: this.bank_name,
          },
          (res) => {
            if (res.statusCode === 200) {
              uni.showToast({
                title: "提交成功",
              });
              setTimeout(() => {
                uni.switchTab({
                  url: "/index/mine",
                });
              }, 1000);
            } else {
            }
          }
        );
      }
    },
  },
};
</script>

<style scoped lang="scss">
.list {
  padding: 24rpx 48rpx;
  .top-title {
    margin-top: 24rpx;
    font-size: 40rpx;
    color: #333;
  }
  .form {
    width: 100%;
    margin-top: 24rpx;
    .input-box {
      height: 80rpx;
      border-radius: 8rpx;
      padding: 0 24rpx;
      margin: 10rpx 0;
      background: #f3f3f3;
      .uni-input {
        font-size: 28rpx;
        color: #333;
        height: 80rpx;
        line-height: 80rpx;
      }
    }
    .input-bank {
      align-items: center;
      margin-bottom: 64rpx;
      justify-content: space-between;
    }
    .btn {
      align-items: center;
      opacity: 0.6;
      background: #0174ff;
      padding: 30rpx;
      border-radius: 22px;
      color: #fff;
    }
  }
  .input-placeholder {
    color: #999;
  }
}
.submit {
  background: #0174ff;
  opacity: 1 !important;
  box-shadow: 0 4px 16px 0 rgba(1, 116, 255, 0.4);
}
</style>
