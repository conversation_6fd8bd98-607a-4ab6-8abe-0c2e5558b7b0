<template>
  <view class=""> </view>
</template>

<script>
export default {
  onReady() {},
  onLoad(options) {
    if (location.href.indexOf("#reloaded") == -1) {
      // 进入页面刷新一次且不重复刷新
      location.href = location.href + "#reloaded";
      location.reload();
    }
    if (options.corp_id && options.app_id) {
      this.qywxInitCorpid(options);
      // 如果链接存在corp_id说明是在企业微信
      uni.setStorageSync("corp_id", options.corp_id);
      uni.setStorageSync("qy_app_id", options.app_id);
      uni.showLoading({
        title: "正在加载...",
      });
    }
  },
  methods: {
    // 根据企业微信获取站点id
    qywxInitCorpid(options) {
      this.$ajax.get(
        `/common/wx_work/auth/corp_id_to_website_id?app_id=${options.app_id}&corp_id=${options.corp_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.$store.commit("setWebsiteid", res.data.website_id || 1);
            uni.hideLoading();
            let link_v =
              window.location.origin +
              "/fenxiao/?website_id=" +
              res.data.website_id;
            window.location.href = link_v;
          } else {
            uni.showToast({
              title: "应用未绑定站点",
              icon: "none",
            });
          }
        }
      );
    },
  },
};
</script>

<style></style>
