<template>
  <view class="checkbox-box">
    <view class="checkbox-row">
      <view class="checkbox-list flex-row">
        <!-- #ifdef H5 -->
        <view
          v-for="(item, index) in range"
          :key="index"
          class="checkbox-item"
          :class="judge(item[value_name])"
          @click="handelSelect(item[value_name], index)"
          >{{ item[label_name] }}</view
        >
        <!-- #endif -->
        <!-- #ifndef H5 -->
        <view
          v-for="(item, index) in range"
          :key="index"
          class="checkbox-item"
          :class="
            selects.includes(item[value_name]) ||
            JSON.stringify(selects[0]) == JSON.stringify(item.value)
              ? 'active'
              : ''
          "
          @click="handelSelect(item[value_name], index)"
          >{{ item[label_name] }}</view
        >
        <!-- #endif -->
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'myCheckbox',
  props: {
    value: Array,
    value_name: {
      type: String,
      default: 'value',
    },
    label_name: {
      type: String,
      default: 'name',
    },
    maxnum: {
      type: Number,
      default: 0,
    },
    range: Array,
    onlayOne: {
      //是否是单选
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  model: {
    prop: 'value',
    event: 'change',
  },
  watch: {
    value(val) {
      this.selects = val || []
      console.log(this.selects)
    },
  },
  data() {
    return {
      selects: this.value || [],
    }
  },
  methods: {
    judge(val) {
      if (this.selects.includes(val) || JSON.stringify(this.selects[0]) == JSON.stringify(val)) {
        return 'active'
      } else {
        return ''
      }
    },
    handelSelect(value) {
      if (this.disabled) {
        uni.showToast({
          title: '当前信息不能修改',
          icon: 'none',
        })
        return
      }
      if (this.onlayOne) {
        this.selects = [value]
      } else {
        if (!this.selects.includes(value)) {
          if (this.maxnum && this.selects.length >= this.maxnum) {
            uni.showToast({
              title: `最多只能选择${this.maxnum}个`,
              icon: 'none',
            })
            return
          }
          this.selects.push(value)
        } else {
          for (let i = 0; i < this.selects.length; i++) {
            if (this.selects[i] == value) {
              this.selects.splice(i, 1)
            }
          }
        }
      }
      this.$emit('input', this.selects)
      this.$emit('change', this.selects)
    },
  },
}
</script>

<style lang="scss">
.checkbox-row {
  // display: flex;
  // align-items: center;
  background-color: #fff;
}
.checkbox-row .checkbox-list {
  flex: 1;
  flex-wrap: wrap;
  min-height: 56rpx;
  margin-right: 10rpx;
}
.checkbox-row label {
  min-width: 130rpx;
  max-width: 220rpx;
  font-size: $uni-font-size-lg;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 38rpx;
  text-align-last: justify;
}
.checkbox-row .checkbox-list .checkbox-item {
  min-width: 15%;
  margin-right: 8rpx;
  box-sizing: border-box;
  line-height: 1;
  padding: 12rpx 16rpx;
  margin-bottom: 8rpx;
  border-radius: 4rpx;
  text-align: center;
  font-size: 28rpx;
  border: 1rpx solid #d8d8d8;
  color: #666;
  // ~ .checkbox-item {
  //   margin-left: 8rpx;
  // }
}
.checkbox-row .checkbox-list .checkbox-item.active {
  border-color: $uni-color-primary;
  background-color: $uni-color-primary;
  color: #fff;
}
</style>
