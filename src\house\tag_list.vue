<template>
  <view class="tag-box">
    <block v-for="(item, index) in tag_list" :key="index">
      <view class="l-title">{{ item.name }}</view>
      <view class="tag_input row">
        <!-- <input
                type="text"
                class="input c2"
                maxlength="50"
                placeholder="请在这里输入"
              /> -->
        <block v-for="(item1, index1) in item.sub" :key="index1">
          <view
            class="tag_item"
            @click="onClickTags(item1.values)"
            :class="{ checked: choose.includes(item1.values) || choose.includes(item1.values + '') }"
            :key="item1.values"
          >
            <view class="checked">
              <image
                v-show="choose.includes(item1.values) || choose.includes(item1.values + '')"
                src="@/static/img/checked.png"
              ></image>
            </view>
            {{ item1.name }}
          </view>
        </block>
      </view>
    </block>
    <view v-if="!from" style="margin: 24px 12px" class="claim" @click="onCreateLabels">确认</view>
    <view v-else class="foot row">
      <view class="c2" @click="$navigateBack()">取消</view>
      <view @click="onCreateLabels">确定</view>
    </view>
  </view>
</template>

<script>
export default {
  data () {
    return {
      tag_list: [],
      form_info: {
        id: "",
        label: ""
      },
      detail: {},
      choose: [],
      from: ''
    };
  },
  onLoad (options) {
    if (options.from) {
      this.from = options.from
    }
    this.form_info.id = options.id
    console.log(212322);
    uni.$on("teseArr",(res)=>{
      console.log(res);
      let tese = res.tese,teseArr = res.teseArr
      this.tag_list = teseArr
      this.choose = tese
      console.log(tese);
    })
  },
  onUnload(){
    uni.$off("teseArr")
  },
  methods: {
    onClickTags (id) {
      if (this.choose.includes(id)) {
        let indexex = this.choose.indexOf(id);
        this.choose.splice(indexex, 1);
      } else {
        this.choose.push(id);
      }
    },
    onCreateLabels () {
      this.form_info.label = this.choose.join(",");
      this.$ajax.post("/admin/house/editPrivateHouse", this.form_info, (res) => {
        if (res.statusCode === 200) {
          uni.$emit("getDataAgain")
          setTimeout(() => {
            this.$navigateBack()
          }, 300);
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      }, (err) => {
        uni.showToast({
          title: err?.data?.message || '提交失败',
          icon: "none",
        });
      });
    },
    findArrDiff (oldValue, newValue) {
      let set;
      let diff;
      let type;
      if (newValue.length > oldValue.length) {
        console.log("add");
        type = "add";
        set = new Set(oldValue);
        diff = newValue.filter((v) => !set.has(v));
      } else {
        console.log("delete");
        type = "delete";
        set = new Set(newValue);
        diff = oldValue.filter((v) => !set.has(v));
      }
      return { diff, type };
    },
  },
};
</script>

<style scoped lang="scss">
.tag-box {
  padding: 0 24px 100px;
  margin-top: 24px;
  .loadmore {
    text-align: center;
    color: #2d84fb;
  }
}
.l-title {
  font-size: 16px;
  color: #2e3c4e;
}
.tag_input {
  margin-top: 12px;
  // border: 1px solid #dde1e9;
  border-radius: 4px;
  align-items: center;
  padding: 9px 12px;
  flex-wrap: wrap;
  .tag_item {
    padding: 10px;
    text-align: center;
    margin-right: 10rpx;
    margin-left: 10px;
    margin-bottom: 24px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #eee;
    min-width: 80px;
    color: #8d9099;
    position: relative;
    overflow: hidden;
    &.checked {
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
      background: #fff;
    }
    .checked {
      position: absolute;
      right: 0;
      top: 0;
      width: 32rpx;
      height: 24rpx;
      image {
        width: 100%;
        height: 100%;
      }
    }
  }
  input {
    width: 100%;
    font-size: 14px;
  }
}
.claim {
  text-align: center;
  height: 40px;
  line-height: 40px;
  font-weight: 500;
  margin: 12px 0;
  border-radius: 6px;
  background: #3172f6;
  color: #fff;
}
.foot {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 12px 12px 40px 12px;
  justify-content: space-between;
  view {
    width: 48%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 6px;
    border: 1px solid #2d84fb;
    color: #fff;
    background: #2d84fb;
    font-weight: 500;
    &.c2 {
      border: 1px solid #dde1e9;
      background: #fff;
      color: #8a929f;
    }
  }
}
</style>
