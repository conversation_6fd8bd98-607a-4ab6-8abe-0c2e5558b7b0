<template>
  <view class="list">
    <view class="title-tips row">
      <text class="icon-baobei-ic_guanyu3x1 icon-baobei"></text>
      <text>点击进入相应详细内容</text>
    </view>
    <!-- 测试帮助手册显示 -->
    <view class="article-content-box" v-for="item in content" :key="item.id">
      <view
        class="content-box row"
        @click="$navigateTo(`/commission/help_desc?id=${item.id}`)"
      >
        <view class="label">描述：{{ item.title }}</view>
        <myIcon type="you"></myIcon>
      </view>
    </view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import myIcon from "@/components/my-icon";
export default {
  components: { myIcon },
  data() {
    return {
      content: [],
      params: {
        page: 1,
      },
    };
  },
  onLoad() {
    this.getData();
  },
  methods: {
    getData() {
      if (this.params.page === 1) {
        this.content = [];
      }
      this.$ajax.get("/common/help_center/search", this.params, (res) => {
        if (res.statusCode === 200) {
          this.content = this.content.concat(res.data.data);
        } else {
          uni.showToast({
            title: res.data.message || "请求出错" + res.statusCode,
            icon: "none",
          });
        }
      });
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.getData();
    uni.stopPullDownRefresh();
  },
};
</script>

<style lang="scss" scoped>
.icon-baobei-ic_guanyu3x1 {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22icon-baobei-ic_guanyu3x1%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M512%2056.888889a455.111111%20455.111111%200%201%201%200%20910.222222A455.111111%20455.111111%200%200%201%20512%2056.888889z%20m0%20113.777778a341.333333%20341.333333%200%201%200%200%20682.666666A341.333333%20341.333333%200%200%200%20512%20170.666667z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M512%20341.333333m-56.888889%200a56.888889%2056.888889%200%201%200%20113.777778%200%2056.888889%2056.888889%200%201%200-113.777778%200Z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M455.111111%20455.111111h113.777778v284.444445H455.111111z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
page {
  background: #eee;
}
.list {
  .title-tips {
    align-items: center;
    padding: 24rpx 48rpx;
    color: #0174ff;
    background: #fff;
    text {
      margin-left: 8rpx;
    }
  }
}
.article-content-box {
  background: #fff;
  margin: 12rpx 0;
  .content-box {
    padding: 24rpx 48rpx;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
