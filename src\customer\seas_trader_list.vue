<template>
    <index current="seas_trader" ref="customerList"></index>
</template>
<script>
import index from '@/customer/components/customer_list/index.vue';
import customerListMixins from '../common/mixis/customer_list.js';
export default {
	components: {
		index
	},
	mixins: [customerListMixins],
};
</script>

<style scoped lang="scss">
page {
    background: #f6f6f6;
    color: #2e3c4e;
}
</style>