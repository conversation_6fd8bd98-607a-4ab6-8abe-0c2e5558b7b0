import ajax from "@/page_outside/tools/ajax";
let 
_ajax = (type, api, params, errMsg, cb) => {
    return new Promise((resolve, reject) => {
        ajax[type](api, params, res => {
            if (res.statusCode == 200) {
                resolve(res.data);
                cb && cb(res.data)
            }else{
                if(errMsg || res?.data?.message){
                    uni.showToast({
                        title: res?.data?.message || errMsg,
                        icon: 'none'
                    });
                }
            }
            reject();
        }, er => {
            reject();
        })
    })
},
_promise = {
    get(api, params, errMsg = '', cb){
        return _ajax('get', api, params, errMsg, cb)
    },
    post(api, params, errMsg = '', cb){
        return _ajax('post', api, params, errMsg, cb)
    }
};


module.exports = {
    ajax: _ajax,
    promise: _promise
};