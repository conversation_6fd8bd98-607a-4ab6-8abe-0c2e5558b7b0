/**
 * 智慧经营 api
 */
import { promise } from "./promise-ajax.js";


//我的拨打统计
export function myCallStatistics( params = {} ){
    return promise.get('/admin/call_clue/my_statistics', params, '获取我的外呼统计失败');
}
//个人crm数据统计
export function personalCrmData( params = {} ){
    return promise.get('/admin/crm/smart_management/personal_crm_data', params, '获取个人crm数据统计失败');
}

//团队拨打统计
export function teamCallStatistics( params = {} ){
    return promise.get('/admin/call_phone/dataKanbanTopNew', params, '获取团队外呼统计失败');
}
//团队crm数据统计
export function teamCrmData( params = {} ){
    return promise.get('/admin/crm/smart_management/team_crm_data', params, '获取团队crm数据统计失败');
}


//分客数据
export function getSortedUserData( params = {} ){
    return promise.get('/admin/crm/smart_management/search', params, '获取分客数据失败');
}

//带看数据
export function getSeedUserData( params = {} ){
    return promise.get('/admin/crm/smart_management/take_statistics', params, '获取带看数据失败');
}

//回访数据
export function getVisitUserData( params = {} ){
    return promise.get('/admin/crm/smart_management/follow_up_statistics', params, '获取回访数据失败');
}

//维护数据
export function getMaintainUserData( params = {} ){
    return promise.get('/admin/crm/smart_management/service_statistics', params, '获取回访数据失败');
}

//获取主播账号
export function getAnchorAccounts( params = {} ){
    return promise.get('/admin/crm/live_anchor/accounts_member', params, '获取主播账号失败');
}

//获取主播平台
export function getAnchorPlatforms( params = {} ){
    return promise.get('/admin/crm/live_anchor/live_platform', params, '获取主播平台失败');
}

//主播账号数据
export function getAnchorAccountData( params = {} ){
    return promise.get('/admin/crm/live_anchor/live_account_statistics', params, '获取主播账号数据失败');
}

//主播客资分布数据
export function getAnchorGuestData( params = {} ){
    return promise.get('/admin/crm/live_anchor/member_statistics', params, '获取主播客资分布数据失败');
}

//流转数据
export function getTransUserData( params = {} ){
    return promise.get('/admin/private_client_statistics/assign_list', params, '获取流转数据失败');
}


export async function checkPerms(){
    const admin = await promise.get("/admin/my/query", {});
    if(admin){
        const roles = admin.roles || [];
        if(roles[0].name == '站长'){
            return true;
        }

        const conf = await promise.get("/admin/website/get_fixed_conf", { key: 'config_auth_uid' });
        if(conf.split(',').includes(String(admin.id))){
            return true;
        }
    }
    return false;
}