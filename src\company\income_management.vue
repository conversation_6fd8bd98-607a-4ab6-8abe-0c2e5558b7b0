<template>
  <view class="income">
    <view class="content-box" v-if="company_income.all">
      <view class="title row">
        <view class="title-left">项目应收</view>
        <view
          class="order row"
          @click="
            $navigateTo(`/company/subscribe_list?company_id=${company_id}`)
          "
        >
          <text>认购订单 {{ company_income.all.order_total }} 笔</text>
          <myIcon
            type="you"
            size="32rpx"
            color="#999"
            fontWeight="bold"
          ></myIcon>
        </view>
      </view>
      <view class="all-income"
        >总应收{{
          FloatAdd(
            company_income.all.customer_amount,
            company_income.all.project_amount
          )
        }}元</view
      >
      <view class="all-income-tips">
        应收客户 {{ company_income.all.customer_amount }} 元，应收开发商
        {{ company_income.all.project_amount }} 元
      </view>
      <view class="bottom-content top-line">
        <view class="top"
          >财务已确认 {{ company_income.all.confirmed_amount }} 元</view
        >
        <view class="top"
          >财务未确认 {{ company_income.all.unconfirmed_amount }} 元</view
        >
      </view>
    </view>
    <!-- <view class="content-box">
      <view class="title row">
        <view class="title-left">财务收入驳回</view>
        <view class="order row">
          <myIcon
            type="you"
            size="32rpx"
            color="#999"
            fontWeight="bold"
          ></myIcon>
        </view>
      </view>
    </view> -->
    <view
      class="content-box"
      v-if="company_income.customer"
      @click="clickCompany"
    >
      <view class="title"><view class="title-left">应收客户</view></view>
      <view class="desc-content row">
        <view class="label">前佣收款：</view>
        <view class="bottom-content">
          <view class="top">
            前佣已收款 {{ company_income.customer.paid_amount }} 元
          </view>
          <view class="top">
            前佣未收款 {{ company_income.customer.unpaid_amount }} 元
          </view>
        </view>
        <view class="order row">
          <myIcon
            type="you"
            size="32rpx"
            color="#999"
            fontWeight="bold"
          ></myIcon>
        </view>
      </view>
    </view>
    <view
      class="content-box"
      @click="clickCompany"
      v-if="company_income.project"
    >
      <view class="title"><view class="title-left">应收开发商</view></view>
      <view style="padding-bottom:20rpx" class="desc-content row bottom-line">
        <view class="label">业绩确认：</view>
        <view class="bottom-content">
          <view class="top">
            有确认单金额
            {{ company_income.project.performance.confirmed_amount }} 元
          </view>
          <view class="top">
            无确认单金额
            {{ company_income.project.performance.unconfirmed_amount }} 元
          </view>
        </view>
        <view class="order row"> </view>
      </view>
      <view style="padding-bottom:20rpx" class="desc-content row bottom-line">
        <view class="label">预计回款：</view>
        <view class="bottom-content">
          <view class="top">
            已达到回款条件
            {{ company_income.project.prediction.in_payment_date }} 元
          </view>
          <view class="top">
            未达到回款条件
            {{ company_income.project.prediction.out_payment_dae }} 元
          </view>
        </view>
        <view class="order row">
          <myIcon
            type="you"
            size="32rpx"
            color="#999"
            fontWeight="bold"
          ></myIcon>
        </view>
      </view>
      <view style="padding-bottom:20rpx" class="desc-content row bottom-line">
        <view class="label">后佣开票：</view>
        <view class="bottom-content">
          <view class="top">
            已开票金额
            {{ company_income.project.invoice.make_amount }} 元
          </view>
          <view class="top">
            未开票金额
            {{ company_income.project.invoice.not_make_amount }} 元
          </view>
        </view>
        <view class="order row">
          <myIcon
            type="you"
            size="32rpx"
            color="#999"
            fontWeight="bold"
          ></myIcon>
        </view>
      </view>
      <view style="padding-bottom:20rpx" class="desc-content row bottom-line">
        <view class="label">后佣回款：</view>
        <view class="bottom-content">
          <view class="top">
            已回款金额
            {{ company_income.project.cash.cashed }} 元
          </view>
          <view class="top">
            未回款金额
            {{ company_income.project.cash.not_yet_cashed }} 元
          </view>
        </view>
        <view class="order row">
          <myIcon
            type="you"
            size="32rpx"
            color="#999"
            fontWeight="bold"
          ></myIcon>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from "@/components/my-icon";
export default {
  components: { myIcon },
  data() {
    return {
      company_income: {},
      company_id: "",
      all: "",
    };
  },
  onLoad(options) {
    this.company_id = options.company_id;
    this.all = options.all || "";
    this.getDataDetail(options.company_id);
  },
  methods: {
    getDataDetail(company_id) {
      // 获取该公司项目收入详情
      this.$ajax.get(
        `/client/sale_order/statistics/earning?company_id=${company_id}&all=${this.all}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.company_income = res.data;
          } else {
            uni.showToast({
              title: res.data.message || "获取收入管理数据失败",
              icon: "none",
            });
          }
        }
      );
    },
    //加法
    FloatAdd(arg1, arg2) {
      var r1, r2, m;
      try {
        r1 = arg1.toString().split(".")[1].length;
      } catch (e) {
        r1 = 0;
      }
      try {
        r2 = arg2.toString().split(".")[1].length;
      } catch (e) {
        r2 = 0;
      }
      m = Math.pow(10, Math.max(r1, r2));
      return (arg1 * m + arg2 * m) / m;
    },
    // 点击跳转公司项目收入管理
    clickCompany() {
      this.$navigateTo(
        `/company/income_management_project?company_id=${this.company_id}`
      );
    },
  },
};
</script>

<style scoped lang="scss">
page {
  background: #eee;
}
.income {
  padding: 24rpx;
  .content-box {
    padding: 24rpx;
    background: #fff;
    border-radius: 10rpx;
    margin-bottom: 24rpx;
    .title {
      justify-content: space-between;
      .title-left {
        font-size: 32rpx;
        font-weight: 600;
      }
      .order {
        align-items: center;
        color: #333;
      }
    }
    .all-income {
      margin-top: 24rpx;
      font-size: 28rpx;
    }
    .all-income-tips {
      font-size: 24rpx;
      color: #999;
      margin: 24rpx 0;
    }
    .bottom-content {
      .top {
        margin-top: 24rpx;
      }
    }
    .desc-content {
      margin: 24rpx 0;
      justify-content: space-between;
      align-items: center;
      .label {
        font-weight: 600;
      }
    }
  }
}
</style>
