<template>
  <view class="msgs">
    <view class="tips"> 
      <view class ="tip">
        当前短信签名：{{sign}}
      </view>
      <view class ="tip">
        每天只能发送一条短信提醒
      </view>
    </view>
    <view class="msgs-cont">
      <view class="msg" :class="{ active: item.check }" v-for="item in msgList" :key="item.id">
        <view class="msg_title flex-row items-center">
          <view class="msg_uncheck" v-if="!item.check" @click="selectMsg(item)"> </view>
          <view class="msg_check" v-if="item.check" @click="selectMsg(item)">
            <image
              mode="widthFix"
              :src="'/static/admin/customer/follow/<EMAIL>' | imageFilter('w_80')"
            >
            </image>
          </view>
          <view class="msg_title_con">
            {{ item.title }}
          </view>
        </view>
        <view class="msg_con flex-row items-center">
          <!-- <view class="content1"> -->
            {{ item.content1 }}
          <!-- </view> -->
          <view class="inp">
            <input type="text" v-model ="item.name">
          </view>
          <!-- <view class="content1"> -->
            {{ item.content2 }}
          <!-- </view> -->
          <view class="inp">
            <input type="text" v-model ="item.mobile">
          </view>
          <!-- <view class="content1"> -->
            {{ item.content3 || '' }}
          <!-- </view> -->
          <!-- {{ item.content }} -->
        </view>
      </view>
      <view class="submit"> <view class="submit_btn" @click="submit">确定</view> </view>
    </view>
  </view>
</template>

<script>
export default {
  data () {
    return {
      msgList: [
        {
          id: 1,
          check: false,
          title: "模板一",
          name:"",
          mobile:"",
          content1:'你好，我是',
          content2:'您预约的信息已经收到，在您方便的时候可以随时联系我'
          // content: "实客求租琶洲新村 11栋12栋 100到300方 用途办公，可长租，有意出租请回电158899***** "
        },
        {
          id: 2,
          check: false,
          title: "模板2",
          name:"",
          mobile:"",
          content1:'你好，我是',
          content2:'您预约的信息已经收到，在您方便的时候可以随时联系我',
          content3:"为你发送资料并分析解答。"
        },
        
      ],
      checked:{},
      sign:''
    }
  },
  onLoad () {

    this.getSign()
    let userInfo = uni.getStorageSync("userInfo")?JSON.parse(uni.getStorageSync("userInfo")):null
    if(userInfo){
      this.msgList.map(item=>{
        item.name = userInfo.user_name
        item.mobile = userInfo.phone
        return item
      })
    }
    
    uni.$once("giveUp", res => {
      console.log(res,123);
      if(res){
        this.checked = res
        this.msgList.map((item,i)=>{
          if(item.id == res.id) {
            // if(i!=0){
            //   this.$set(this.msgList[0],checked,false)
            // } 
            this.msgList.splice(i,1,res)
          }
        })
      }else  {
        this.msgList[0].check =true
        this.checked =  this.msgList[0]
        this.msgList.splice(0,1,this.checked)
      }

    })
  },
  methods: {
    selectMsg (item) {
      this.checedId = item.id
      this.msgList.map(i => {
        i.check = false
        if (i.id == item.id) {
          i.check = true
          this.checked = item
        }
        return item
      })
    },
    getList () {
      // 获取列表如果this.checedId 存在 显示选中
    },
    getSign (){
      this.$ajax.get("/admin/marketing_sms/getSMSConfig",{},res=>{
        if(res.statusCode ==200){
         this.sign= res.data.value 
        }
      })
    },
    submit () {
      // let 
      if(!this.checked.id) {
        uni.showToast({
          title:"请选择模板",
          icon:"none"
        })
        return 
      }
      let reg = /^[\u4e00-\u9fa5]{2,12}$/
      console.log(reg.test(this.checked.name),1111234);
      if(!reg.test(this.checked.name)){
        uni.showToast({
          title:"姓名只能是纯汉字且不能大于12个",
          icon:'none'
        })
        return
      }
      let regphone = /(^1[3456789]\d{9}$)/
      if(!regphone.test(this.checked.mobile)){
        uni.showToast({
          title:"手机号格式不正确",
          icon:'none'
        })
        return
      }
      this.$navigateBack()
        setTimeout(() => {
          uni.$emit("selectedOk", this.checked)
        }, 200);
      }
      
  }
}
</script>

<style lang ="scss" scoped>
.msgs {
  background: #f7f7f7;
  .msgs-cont{
    padding: 48rpx 48rpx 128rpx;
  }
  .msg {
    padding: 36rpx;
    border-radius: 20rpx;
    line-height: 1.5;
    background: #ffffff;
    margin-bottom: 20rpx;
    &.active {
      border: 2rpx solid #3172f6;
    }
    /* border: 2rpx solid #2d84fb; */
    .msg_title {
      .msg_uncheck {
        width: 40rpx;
        height: 40rpx;
        border-radius: 50%;
        border: 2rpx solid #8a929f;
      }
      .msg_check {
        width: 40rpx;
        height: 40rpx;
        overflow: hidden;
        image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .msg_title_con {
        margin-left: 20rpx;
        font-weight: 600;
        font-size: 32rpx;
      }
    }
    .content1{
      display: inline-block;
      vertical-align: middle;
    }
    .msg_con {
      margin-top: 24rpx;
      display: block;
      line-height: 1.4em;
      word-break: break-all;
      /* flex-wrap: wrap; */
      color: #8a929f;
      font-size: 28rpx;
      .inp {
        display: inline-block;
        /* width: 200px; */
        border-bottom: 2rpx solid #8a929f;
        input{
          display: inline-block;
          margin-bottom: -3px;
        }
      }
    }
  }
  .submit {
    position: fixed;
    bottom: 0;
    left: 48rpx;
    right: 48rpx;
    .submit_btn {
      padding: 24rpx 0;
      margin-bottom: 60rpx;
      font-size: 30rpx;
      text-align: center;
      border-radius: 10rpx;
      background: #3172f6;
      color: #ffffff;
    }

    /* height: 80rpx; */
  }
}
.tips {
  padding:24rpx 48rpx;
  background: #3172f6;
  color: #ffffff;
  .tip{
    ~.tip
    {
      margin-top: 12rpx;
    }
  }
}
</style>