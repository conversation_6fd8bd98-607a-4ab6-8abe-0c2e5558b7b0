<template>
    <view>
        <view class="Search">
            <view class="search_for">
                <image src="./photo/Group 284.png"></image>
                <input type="text" value="" placeholder="搜索">
            </view>
        </view>
        <view class="whole">
            <view class="on_line">
                <view class="on_line_top">
                    <view class="count_down">活动倒计时</view>
                    <view class="line_top_time">
                        <view>5</view>
                        <text>天</text>
                        <view>17</view>
                        <text>时</text>
                        <view>17</view>
                        <text>分</text>
                    </view>
                </view>
                <view class="on_line_bottom">
                    <view class="line_bottom_top">绿城618线上章三直播说房（石家庄···</view>
                        <view class="line_bottom_center">9.9元订房0元抽车位1万元定商铺，华语集团 10周年庆</view>
                        <view class="line_bottom">
                            <view class="line_bottom_bottom">详情</view>
                        </view>
                </view>
            </view>
            <view class="on_line">
                <view class="on_line_top">
                    <view class="count_down">活动倒计时</view>
                    <view class="line_top_time">
                        <view>5</view>
                        <text>天</text>
                        <view>17</view>
                        <text>时</text>
                        <view>17</view>
                        <text>分</text>
                    </view>
                </view>
                <view class="on_line_bottom">
                    <view class="line_bottom_top">绿城618线上章三直播说房（石家庄···</view>
                        <view class="line_bottom_center">9.9元订房0元抽车位1万元定商铺，华语集团 10周年庆</view>
                        <view class="line_bottom">
                            <view class="line_bottom_bottom">详情</view>
                        </view>
                </view>
            </view>
            <view class="on_line">
                <view class="on_line_top">
                    <view class="count_down">活动倒计时</view>
                    <view class="line_top_time">
                        <view>5</view>
                        <text>天</text>
                        <view>17</view>
                        <text>时</text>
                        <view>17</view>
                        <text>分</text>
                    </view>
                </view>
                <view class="on_line_bottom">
                    <view class="line_bottom_top">绿城618线上章三直播说房（石家庄···</view>
                        <view class="line_bottom_center">9.9元订房0元抽车位1万元定商铺，华语集团 10周年庆</view>
                        <view class="line_bottom">
                            <view class="line_bottom_bottom">详情</view>
                        </view>
                </view>
            </view>
            <view class="on_line">
                <view class="on_line_top">
                    <view class="count_down">活动倒计时</view>
                    <view class="line_top_time">
                        <view>5</view>
                        <text>天</text>
                        <view>17</view>
                        <text>时</text>
                        <view>17</view>
                        <text>分</text>
                    </view>
                </view>
                <view class="on_line_bottom">
                    <view class="line_bottom_top">绿城618线上章三直播说房（石家庄···</view>
                        <view class="line_bottom_center">9.9元订房0元抽车位1万元定商铺，华语集团 10周年庆</view>
                        <view class="line_bottom">
                            <view class="line_bottom_bottom">详情</view>
                        </view>
                </view>
            </view>
        </view>

    </view>
</template>
<script>
export default {
    data() {
        return {

        }
    },
}
</script>
<style scoped lang="scss">
.Search {
    width: 100%;
    height: 150rpx;

    // background-color: #1c49a4;
    .search_for {
        width: 90%;
        height: 70rpx;
        background-color: #F5F6F8;
        color: #D1D1D1;
        margin: 0 auto;
        border-radius: 50px;
        text-align: center;
        margin-top: 30rpx;
        display: flex;
        flex-direction: row;
        align-content: center;
        justify-content: center;
        flex-wrap: wrap;

        image {
            width: 50rpx;
            height: 50rpx;
            margin-left: 150rpx;
        }

    }

}

::v-deep.uni-input-wrapper {
    width: 45%;
}

.whole {
    width: 100%;
    height: 1390rpx;
    overflow: hidden;
    background-color: #F5F6F8;

    .on_line {
        width: 90%;
        height: 300rpx;
        background-color: #fff;
        margin: 0 auto;
        margin-top: 40rpx;
        border-radius: 7px;

        .on_line_top {
            width: 90%;
            height: 90rpx;
            border: 1px solid #fff;
            border-bottom-color: #D1D1D1;
            margin: 0 auto;
            display: flex;
            flex-direction: row;
            .count_down{
                margin-top: 30rpx;
            }
            .line_top_time{
                display: flex;
                flex-direction: row;
                margin-left: 20rpx;
                margin-top: 30rpx;
                view{
                    width:40rpx;
                    height: 35rpx;
                    background-color: #EB4836;
                    text-align: center;
                    line-height: 35rpx;
                    border-radius: 2px;
                    color: #fff;
                    margin-left: 5rpx;
                }
                text{
                    color: #EB4836;
                    margin-left: 5rpx;
                    font-size: 15px;
                }

            }
        }
        .on_line_bottom{
            width: 90%;
            height: 170rpx;
            // background-color: palevioletred;
            margin: 0 auto;
            margin-top: 20rpx;
            .line_bottom_top{
                color:#2E3C4E;
                font-size: 16px
            }
            .line_bottom_center{
                width: 90%;
                color: #8A929F;
                font-size: 14px;
                margin-top: 30rpx;
            }
            .line_bottom{
                width: 100%;
                height: 50rpx;
                // background-color: aquamarine;
                display: flex;
                flex-direction: row-reverse;
            }
            .line_bottom_bottom{
                width: 120rpx;
                height: 50rpx;
                background-color: #2D84FB;
                color: #fff;
                text-align: center;
                line-height: 45rpx;
                border-radius: 15px;
            }
        }
    }
}
</style>