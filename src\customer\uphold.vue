<template>
  <view class="container">
    <view class="all">
      <addForm :customer="c_detail" :current.sync="current" :isEdit="type==2" ref="addForm"></addForm>
    </view>
    <view class="footer">
        <view class="btn-cancle" @click="$navigateBack()">取消</view>
        <button class="btn flex-1" @click="onCreateData" :loading="submiting">提交</button>
    </view>
  </view>
</template>

<script>
import addForm from '@/customer/components/customer/addForm'
export default {
  components: {
    addForm
  },
  data() {
    return {
      type: 1, //1:录入客户 2：维护资料
      push_form: {
        cname: "",
        source_id: 0,
        source2_id: 0,
        source_ids: [],
        level_id: 0,
        type: "",
        sex: 1,
        subsidiary_mobile: "",
        intention_community: "",
        add_type: 1,
        remark: "",
        label: "", // 客户标签
      },
      c_detail: {},
      id: '',
      submiting: false,
      current: 'my',
      customerCurrents: [
        { label: '私客', value: 'my' },
        { label: '公海', value: 'seas' },
        { label: '流转客', value: 'trans' },
      ],
      dialogs: {
        current: false
      }
    };
  },
  computed: {
    isTrans(){
      return this.current === 'trans'
    },
  },
  onLoad(options) {
    if (options.current) {
      let isExist = this.customerCurrents.some(e => e.value === options.current);
      this.current = isExist ? options.current : this.customerCurrents[0].value;
    }


    if (options.wxuserid) {
      this.wxuserid = options.wxuserid;
      this.getQwUserDetail();
    }
    this.push_form.wxqy_id = options.wxuserid;
    if (options.type) {
      this.type = options.type;
    }
    if (options.id) {
      this.push_form.id = options.id;
      this.getUserDetail();
    }
    let name;
    if (this.type == 1) {
      name = "录入客户";
    } else {
      name = "维护资料";
    }
    wx.setNavigationBarTitle({
      title: name,
    });
  },
  methods: {
    // 客户详情
    getUserDetail() {
      uni.showLoading({
        title: '加载中',
        mask: true
      });
      let api = '/qywx/client/info/';
      if(this.isTrans){
        api = '/admin/private_client/info/';
      }
      this.$ajax.get(`${api}${this.push_form.id}`, {}, (res) => {
        if (res.statusCode === 200) {
          let c_detail = res.data;
          this.c_detail = res.data;
          this.push_form = {
            id: c_detail.id,
            cname: c_detail.cname,
            source_id:"" ,  //来源最后一级id
            source2_id: 0,  //来源次最后一级id
            source_ids: [],
            sex:"",
            level_id:"",
            type: c_detail.client_type.id,
            intention_community: c_detail.intention_community,
            remark: c_detail.remark,
          };
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    
    // 获取企业微信用户信息并赋值这里点录入的时候， 姓名， 来源，添加方式，  可以传进去。 客户来源——客户意向，   添加方式——备注
    getQwUserDetail() {
      this.$ajax.get(`/qywx/client/qws_info/${this.wxuserid}`, {}, (res) => {
        if (res.statusCode === 200) {
          let c_detail = res.data.wx_info;
          this.c_detail = res.data;
          this.push_form = {
            cname: c_detail.name,
            sex: c_detail.gender || 1,
            intention_community: c_detail.type == 1 ? '微信用户' : '企业微信',
            remark: c_detail.add_way_title,
          };
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },

    verifyCrmCustomerCreate(mobile){
      return new Promise((resolve, reject) => {
        this.$ajax.get('/admin/crm/client/verify_create', {mobile}, (res) => {
          if(res.statusCode == 200){
            resolve(res.data)
          }else{
            reject(res);
            uni.showToast({
              title: res.data?.message || '请求失败',
              icon: 'none'
            })
          }
        }, err=> {
          console.log(err);
          reject(err);
        })
      })
    },
    async onCreateData() {
      const push_form = this.$refs.addForm.checkSubmit();
      if(!push_form){
        return;
      }
  
      if(this.push_form.id){
        push_form.id = this.push_form.id;
      }
      //当前客户企微id
      if(this.push_form.wxqy_id){
        push_form.wxqy_id = this.push_form.wxqy_id;
      }


      let url = this.push_form.id
        ? "/qywx/client/updates"
        : "/qywx/client/creates";

      
      if(this.submiting){
        return;
      }
      this.submiting = true;
      let isCreate =  this.type == 1,
          isTrans = this.isTrans;
      if(isCreate && !isTrans){
        //录入客户时验证
        const res = await this.verifyCrmCustomerCreate(push_form.mobile).catch(()=>{})
        if(!res){
          this.submiting = false;
          return;
        }
        //客户已存在，不去重， 走添加流转客接口
        if(res.number > 0 && res.add_client_repeat == 1){
          isTrans = true;
        }
      }

      if(isTrans){
        url = !isCreate ? '/admin/private_client/update' : '/admin/private_client/create'
      }else{
        if(!this.push_form.id){
          push_form.add_type = 1;
          if(this.current === 'seas'){
            push_form.add_type = 2;
          }
        }
      }


      this.$ajax.post(url, push_form, (res) => {
        if(res.statusCode != 200){
          this.submiting = false;
        }
        if (res.statusCode === 200) {
          uni.$emit("getDataAgain")
          if(isCreate){
            uni.showModal({
                title: "提示",
                content: res.data?.msg || '录入成功，请前往我的客户查看',
                cancelText: "知道了",
                confirmText: "客户详情",
                success: (e) => {
                  console.log(e, res);
                  if(e.confirm){
                    let websiteId = this.$getQueryString("website_id") || uni.getStorageSync("wxwork_id"),
                    url = '';
                    if(isTrans){
                      url = `/customer/trans_detail?id=${res.data.id}&form=2&website_id=${websiteId}`;
                    }else{
                      url = `/customer/detail?id=${res.data.id}&form=2&website_id=${websiteId}`;
                    }
                    uni.redirectTo({ url });
                  }else{
                    this.$navigateBack(1, true)
                  }
                }
            })
            this.submiting = false;
            return;
          }else{
            uni.showToast({
              title: "操作成功",
            });
            setTimeout(() => {
              this.submiting = false;
              this.$navigateBack(1, true)
            }, 300);
          }
        } else if (res.statusCode === 422) {
          let that = this;
          const cus_id = res.data.data && (res.data.data.id != '' && res.data.data.id != undefined) ? res.data.data.id : 0; // 赋值客户id
          this.customerID = cus_id; // 赋值客户id
          // 当客户手机号重复录入时，返回维护跟进人follow_id，如果有就提示已有维护人立即查看。如果没有就提示是否认领
          if ((res.data.data && res.data.data.follow_id) && (res.data.data.follow_id != undefined && res.data.data.follow_id != 0)) {
            this.view_hide = true; // 显示查看客户模态框
            uni.showModal({
              title: "提示",
              cancelText: "取消",
              content: res.data.message,
              confirmText: "立即查看",
              success: (res) => {
	            if(res.confirm) {  
                if(isTrans){
                  that.$navigateTo(`/customer/trans_detail?id=${that.customerID}&form=2`);
                  return;
                }
                that.$navigateTo(`/customer/detail?id=${that.customerID}&form=2`);//点击确定之后执行的代码
	            } else {  
	            	console.log('cancel') //点击取消之后执行的代码
	            	}  
	            } 
            });
          } else {
            if (this.push_form.id) {
              uni.showToast({
                title: res.data.message || '编辑失败',
                icon: "none",
              })
              return
            }
            if(that.customerID){
              uni.showModal({
              title: "提示",
              cancelText: "取消",
              content: res.data.message,
              confirmText: "立即认领",
              success: function (res) {
              if(res.confirm){
                let form = {
                  ids: that.customerID + "",
                };
                that.$ajax.post("/qywx/client/get", form, (res) => {
                  console.log(res);
                  if (res.statusCode === 200) {
                    uni.showToast({
                      title: "领取成功",
                      icon: "none",
                    });
                    that.$navigateTo(`/customer/detail?id=${that.customerID}&form=2`);
                  } else {
                    uni.showToast({
                      title: res.data.message,
                      icon: "none",
                    });
                  }
                });
              }else{
                console.log('cancel') //点击取消之后执行的代码
              }
              },
            });
            }else{
              uni.showModal({
              title: "提示",
              cancelText: "取消",
              content: res.data.message,
              confirmText: "确定",
              success: (res) => {
	            if(res.confirm) {  
	            	console.log('comfirm') 
	            } else {  
	            	console.log('cancel') //点击取消之后执行的代码
	            	}  
	            } 
            });
            }
        
          }
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      }, err => {
        this.submiting = false;
      });
    },
  },
};
</script>
<style scoped lang="scss">
.container{
  display: flex;
  flex-direction: column;
  height: 100vh;
  .all{
    flex: 1;
    overflow: auto;
    background-color: #fff;
  }
  .footer{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    z-index: 1;
    padding: 24rpx 24rpx;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    border-top: 1rpx solid #F0F1F2;
    padding-bottom: calc(constant(safe-area-inset-bottom) + 24rpx);
    padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
     .btn{
        flex: 1;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        height: 80rpx;
        margin: 0 12rpx;
        font-size: 32rpx;
        color: #fff;
        background-color: #2d84fb;
        border-radius: 8rpx;
    }
    .btn-cancle{
      color: #4E5969;
      font-size: 32rpx;
      padding: 0 32rpx;
      min-width: 30%;
      text-align: center;
    }
  }
}

page {
  background: #f6f6f6;
  color: #2e3c4e;
}
</style>