<template>
  <view class="index">
    <view class="sticky" id="i_search" ref="i_search">
      <view class="tab-box">
        <tabs
          :options="tab_list"
          :format="{ name: 'name', value: 'type' }"
          :equispaced="tab_list.length > 3"
          :showAnimation="false"
          v-model="current_tab"
          @change="onTabChange"
        ></tabs>
      </view>
      <view class="i_search">
        <!-- <search placeholder="小区" v-model="params.keyword" @confirm="initData"> -->
        <view class="flex-row align-center">
          <view class="search_comm search_loudong flex-1 flex-row" @click="showCommSearch">
            <view class="loudong_text">小区/编号</view>
            <view class="xiasanjiao" v-if="!show_comm_search"></view>
            <view class="shangsanjiao" v-if="show_comm_search"></view>
          </view>
          <view class="search_loudong flex-row" @click="showTelSearch">
            <view class="loudong_text">电话</view>
            <view class="xiasanjiao" v-if="!show_tel_search"></view>
            <view class="shangsanjiao" v-if="show_tel_search"></view>
          </view>
          <view class="search_loudong flex-row" @click="showLoudongSearch">
            <view class="loudong_text">楼栋门牌</view>
            <view class="xiasanjiao" v-if="!show_loudong_search"></view>
            <view class="shangsanjiao" v-if="show_loudong_search"></view>
          </view>
        </view>
        <!-- </search> -->
      </view>
      <view
        class="search_container"
        :class="{ show: show_comm_search }"
        :style="{ top: loudong_search }"
        @touchmove.prevent.stop="stopMove"
      >
        <view class="search_con">
          <!-- <view class="search_title"> 搜索 </view> -->
          <view class="serch_info">
            <view class="search_inp">
              <input
                type="text"
                v-model="otherParams.keyword"
                placeholder="输入小区"
                placeholder-class="pls"
              />
            </view>
            <view class="search_inp mat10 mal0">
              <input
                type="text"
                v-model="otherParams.hid"
                placeholder="输入房源编号"
                placeholder-class="pls"
              />
            </view>
          </view>
          <view class="flex-row btn_group">
            <view class="btn" @click="resetCommData">重置</view>
            <view class="btn bg_highlight" @click="filterCommData">确定</view>
          </view>
        </view>

        <view class="mask" @click="show_loudong_search = false" @touchmove="stopMove"></view>
      </view>
      <view
        class="search_container"
        :class="{ show: show_loudong_search }"
        :style="{ top: loudong_search }"
        @touchmove.prevent.stop="stopMove"
      >
        <view class="search_con">
          <view class="search_title"> 搜索 </view>
          <view class="serch_info flex-row items-center space-between">
            <view class="search_inp">
              <input
                type="text"
                v-model="otherParams.loudong"
                placeholder="输入楼栋"
                placeholder-class="pls"
              />
            </view>
            <view class="search_inp">
              <input
                type="text"
                v-model="otherParams.danyuan"
                placeholder="输入单元"
                placeholder-class="pls"
              />
            </view>
            <view class="search_inp">
              <input
                type="text"
                v-model="otherParams.fanghao"
                placeholder="输入房号"
                placeholder-class="pls"
              />
            </view>
          </view>
          <view class="flex-row btn_group">
            <view class="btn" @click="resetLoudongData">重置</view>
            <view class="btn bg_highlight" @click="filterLoudongData">确定</view>
          </view>
        </view>

        <view class="mask" @click="show_loudong_search = false" @touchmove="stopMove"></view>
      </view>
      <view
        class="search_container"
        :class="{ show: show_tel_search }"
        :style="{ top: loudong_search }"
        @touchmove.prevent.stop="stopMove"
      >
        <view class="search_con">
          <view class="search_title"> 搜索 </view>
          <view class="serch_info flex-row items-center space-between">
            <view class="search_inp">
              <input
                type="text"
                v-model="otherParams.tel"
                placeholder="输入业主电话"
                placeholder-class="pls"
              />
            </view>
          </view>
          <view class="flex-row btn_group">
            <!-- <view class="btn" @click="resetTelData">重置</view> -->
            <view class="btn bg_highlight" @click="filterTelData">确定</view>
          </view>
        </view>

        <view class="mask" @click="show_tel_search = false" @touchmove="stopMove"></view>
      </view>
    </view>
    <!-- 二级分类 -->
    <view class="second_tab flex-row flex-1 align-center js-between" id="second_tab">
      <view
        class="second_tab_item flex-1 flex-row justify-center items-center"
        @click="changeSecondTab(1)"
      >
        <view class="item_name">
          {{ currentType }}
        </view>
        <view class="sanjiao"></view>
      </view>
      <view
        class="second_tab_item flex-1 flex-row justify-center items-center"
        @click="changeSecondTab(2)"
      >
        <view class="item_name">
          {{ currentRegion }}
        </view>
        <view class="sanjiao"></view>
      </view>
      <view
        class="second_tab_item flex-1 flex-row justify-center items-center"
        @click="changeSecondTab(3)"
      >
        <view class="item_name">
          {{ currentPrice }}
        </view>
        <view class="sanjiao"></view>
      </view>
      <view
        class="second_tab_item flex-1 flex-row justify-center items-center"
        @click="changeSecondTab(4)"
      >
        <view class="item_name">
          {{ currentArea }}
        </view>
        <view class="sanjiao"></view>
      </view>
      <view
        class="second_tab_item flex-1 flex-row justify-center items-center"
        @click="changeSecondTab(5)"
      >
        <view class="item_name"> 更多 </view>
        <view class="sanjiao"></view>
      </view>
      <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 1">
        <view class="second_tab_con_i">
          <view
            class="second_tab_con_item"
            v-for="item in typeList"
            :key="item.id"
            @click="changeType(item)"
          >
            {{ item.name }}
          </view>
        </view>
        <view class="mask" @click="hideErjiSearch"></view>
        <!-- <view class="mask1" v-show="showMask"> </view> -->
      </view>

      <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 2">
        <view class="second_tab_con_i">
          <view
            class="second_tab_con_item"
            v-for="item in regionList"
            :key="item.id"
            @click="changeType(item)"
          >
            {{ item.name }}
          </view>
        </view>
        <view class="mask" @click="hideErjiSearch"></view>
        <!-- <view class="mask1" v-show="showMask"> </view> -->
      </view>

      <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 3">
        <view class="second_tab_con_i">
          <view
            class="second_tab_con_item"
            v-for="item in priceList"
            :key="item.id"
            @click="changeType(item)"
          >
            {{ item.name }}
          </view>
        </view>
        <view class="mask" @click="hideErjiSearch"></view>
        <!-- <view class="mask1" v-show="showMask"> </view> -->
      </view>
      <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 4">
        <view class="second_tab_con_i">
          <view
            class="second_tab_con_item"
            v-for="item in areaList"
            :key="item.id"
            @click="changeType(item)"
          >
            {{ item.name }}
          </view>
        </view>
        <view class="mask" @click="hideErjiSearch"></view>
        <!-- <view class="mask1" v-show="showMask"> </view> -->
      </view>
    </view>
    <view
      class="filter_container"
      :class="{ show: current_second_tab == 5 }"
      @touchmove.stop.prevent="disMove"
    >
      <view class="filter_box" :style="{ top: filterTop }">
        <view class="filter_list flex-row">
          <scroll-view class="filter_labels" scroll-y>
            <view
              class="item"
              v-for="(item, index) in filtersNew"
              :key="index"
              @click="checkFilter(index, item)"
              :class="{ active: index == scroll_into_view_index }"
              >{{ item.label }}</view
            >
          </scroll-view>
          <scroll-view
            class="filter_options"
            id="filter_options"
            scroll-y
            scroll-with-animation
            :scroll-into-view="scroll_into_view"
            @scroll="onFilterOptionsScroll"
          >
            <view
              class="item"
              v-for="(filter_item, index) in filtersNew"
              :key="index"
              :id="filter_item.id"
            >
              <view class="label flex-row"
                >{{ filter_item.label }}
                <text v-if="filter_item.unit">({{ filter_item.unit }})</text>
              </view>
              <view class="value_list" v-if="!filter_item.multiple">
                <view
                  class="value"
                  v-for="(option, idx) in filter_item.item"
                  :key="idx"
                  :class="{ active: erjiParams[filter_item.type] === option.values }"
                  @click="setFilterItem(option, filter_item.type)"
                  >{{ option.name }}</view
                >
                <view class="empty_value" v-if="filter_item.item.length % 3 === 2"></view>
                <view
                  class="input_range flex-row"
                  v-if="filter_item.customizale === 1 && filter_item.customizale"
                >
                  <input
                    class="input"
                    v-model="filter_item.custom_value.min"
                    @input="onCustomFilterChange(index, 'min')"
                    type="number"
                    placeholder="最低"
                  />
                  <view class="mark">~</view>
                  <input
                    class="input"
                    v-model="filter_item.custom_value.max"
                    @input="onCustomFilterChange(index, 'max')"
                    type="number"
                    placeholder="最高"
                  />
                </view>
              </view>
              <view class="value_list" v-else>
                <view
                  class="value"
                  v-for="(option, idx) in filter_item.item"
                  :key="idx"
                  :class="{ active: erjiParams[option.type] === option.values }"
                  @click="setFilterItem(option)"
                  >{{ option.name }}</view
                >
                <view class="empty_value"></view>
              </view>
            </view>
            <view style="height: 40vh"></view>
          </scroll-view>
        </view>
        <view class="flex-row btn_group">
          <view class="btn" @click="resetData()">重置</view>
          <view class="btn bg_highlight" @click="filterData()">确定</view>
        </view>
      </view>
      <view class="mask" @click="showMask = false"></view>
    </view>
    <!-- 三级分类 -->
    <view class="third_tab flex-row flex-1 align-center j-between">
      <view class="nav-box">
        <scroll-view
          scroll-x
          scroll-with-animation
          :scroll-into-view="
            'i' + (current_third_index > 0 ? current_third_index - 1 : current_third_index)
          "
          class="nav-list"
        >
          <view
            class="nav-item"
            :class="{ active: current_third_index == index }"
            :id="'i' + index"
            v-for="(item, index) in thirdTabs"
            :key="item.values"
            @click="changThirdTab(item, index)"
          >
            <text>{{ item.name }} </text>
            <!-- <image
              v-if="item.type == 'img'"
              mode="heightFix"
              :src="current_third_index == index ? item.active_src : item.src"
            ></image> -->
          </view>
        </scroll-view>
      </view>
    </view>

    <view class="house_list">
      <view class="house" v-for="house in house_list" :key="house.id">
        <privateItem
          class="flex-1"
          :house="house"
          @toDetail="checkPerssion($event, toPrivateDetail)"
          @copy="checkPerssion($event, copyPrivate)"
          @follow="checkPerssion($event, followPrivate)"
          @addLianmai="checkPerssion($event, addToLianmai)"
          @addShowing="checkPerssion($event, addToShowing)"
          @houseGroup="checkPerssion($event, houseGroup)"
          @setLevels="checkPerssion($event, setLevels)"
          @setLevel="checkPerssion($event, setLevels)"
          @tixing="checkPerssion($event, tixing)"
          @houseTop="checkPerssion($event, houseTop)"
          @houseDel="checkPerssion($event, houseDel)"
        ></privateItem>
      </view>
      <loadMore :status="load_status" @reload="getData()" />
    </view>
    <view class="add flex-box items-center justify-center" @click="toAdd">
      <view class="icon">
        <image :src="'/static/house/img/<EMAIL>' | imageFilter('w_80')"></image>
      </view>
      <view class="add_name"> 录房 </view>
    </view>
    <my-popup :show="show_set_showing" @close="show_set_showing = false">
      <view>
        <setShowing :is_status="true" :house="current_house" @success="setShowingSuccess" />
      </view>
    </my-popup>
  </view>
</template>

<script>
import Tabs from '@/components/Tabs'
import privateItem from '@/components/privateItem'
import loadMore from '@/components/loadMore'
import myPopup from '@/components/myPopup.vue'
import setShowing from '@/components/setShowing'
import search from '@/components/search'
import { copyPrivate } from '@/page_outside/private.js'
export default {
  components: {
    Tabs,
    privateItem,
    loadMore,
    myPopup,
    setShowing,
    search
  },
  data () {
    return {
      params: {
        page: 1,
        rows: 20,
        trade_type: '',
      },
      current_tab: '',
      tab_list: [


      ],
      load_status: 'loading',
      show_edit: false,
      house_list: [],
      is_collect: false,
      type: 0,
      current_house: {},
      show_set_showing: false,
      show_loudong_search: false,
      loudong_search: '110rpx',
      otherParams: {
        loudong: '',
        danyuan: '',
        fanghao: '',
        tel: ''
      },
      erjiParams: {
        region_id: '',
        mianji: '',
        sale_price: '',
        rent_price: '',
        trade_status: 9,
      },
      sanjiSimiParams: {
        exclusive: '',
        has_key: '',
        youxiao: '',
        wuxiao: '',
        yichengjiao: '',
        fenyong: '',
      },
      show_tel_search: false,
      current_second_tab: '',
      typeList: [
        // {
        //   id: 1,
        //   name: '出售',
        // },
        // {
        //   id: 2,
        //   name: '出租',
        // },
        // {
        //   id: 3,
        //   name: '租售',
        // },
      ],
      regionList: [
      ],
      priceList: [

      ],
      areaList: [

      ],
      // thirdTabs: [
      //   {
      //     id: 0,
      //     name: '全部',
      //     type: 'text',
      //   },
      //   {
      //     id: 1,
      //     name: '独家',
      //     type: 'img',
      //     value: 1,
      //     value_name: 'exclusive',
      //     src: '/static/icon/index/<EMAIL>',
      //     active_src: '/static/icon/index/<EMAIL>',
      //   },
      //   {
      //     id: 2,
      //     name: '钥匙',
      //     type: 'img',
      //     value: 1,
      //     value_name: 'has_key',
      //     src: '/static/icon/index/<EMAIL>',
      //     active_src: '/static/icon/index/<EMAIL>',
      //   },
      //   {
      //     id: 3,
      //     name: '有效房源',
      //     type: 'text',
      //     value: 1,
      //     value_name: 'youxiao',
      //   },
      //   {
      //     id: 4,
      //     name: '无效房源',
      //     type: 'text',
      //     value: 0,
      //     value_name: 'youxiao',
      //   },
      //   {
      //     id: 5,
      //     name: '已成交',
      //     type: 'text',
      //     value: 1,
      //     value_name: 'chengjiao',
      //   },
      //   {
      //     id: 6,
      //     name: '分佣联卖',
      //     type: 'text',
      //     value: 1,
      //     value_name: 'fenyong',
      //   },
      //],
      current_third_index: 0,
      showMask: false,
      filterTop: '220rpx',
      scroll_into_view: '',
      scroll_into_view_index: '',
      filters: [],
      show_comm_search: false,
      unfollowInfo: {
      }
    }
  },
  onLoad (options) {
    if (options.trade_type >= 0) {
      this.type = options.trade_type
      this.params.trade_type = options.trade_type
    }
    if (options.is_owner) {
      this.type=0
      this.params.is_owner = options.is_owner
    }
    if (options.trade_status) {
      this.params.trade_status = options.trade_status
    }
    this.getUnfollow()
    // let index = this.tab_list.find(item => item.index == this.params.trade_type)
    // this.current_tab = index.type
    this.getPerminsion()

    uni.$once("getDataAgain", (res) => {
      this.params.page = 1
      this.getPerminsion()
    })
    const value = uni.getStorageSync('mobiles')
    const text = uni.getStorageSync('keywords')
    console.log(text,'搜索内容');
    if(value == '电话'){
      this.otherParams.tel = text
      // this.filterData()
    }else if (value == '编号'){
      this.otherParams.hid = text
      // this.filterData()
    }else if (value == '小区'){
      this.otherParams.keyword = text
      // this.filterData()
    }
  },
  computed: {
    currentType () {
      let type = '交易'
      if (this.erjiParams.trade_status && this.typeList.length > 0) {
        let res = this.typeList.find((item) => item.values == this.erjiParams.trade_status)
        if (res && res.name) {
          type = res.name
        }
      }
      // if (this.erjiParams.trade_type && this.typeList.length > 0) {
      //   let res = this.typeList.find((item) => item.id == this.erjiParams.trade_type)
      //   if (res && res.name) {
      //     type = res.name
      //   }
      //   if ((res && (res.id == 1 || res.id == 3)) || !res) {
      //     this.filters.map((item) => {
      //       if (item.type == 'sale_price') {
      //         this.priceList = item.item.map((price) => {
      //           price.id = price.values
      //           return price
      //         })
      //       }
      //     })
      //   } else {
      //     this.filters.map((item) => {
      //       if (item.type == 'rent_price') {
      //         this.priceList = item.item.map((price) => {
      //           price.id = price.values
      //           return price
      //         })
      //       }
      //     })
      //   }
      // }
      return type
    },
    currentArea () {
      let area = '面积'
      if (this.erjiParams.mianji && this.areaList.length > 0) {
        let res = this.areaList.find((item) => item.id == this.erjiParams.mianji)
        if (res && res.name) {
          area = res.name
        }
      }
      return area
    },
    currentRegion () {
      let region = '商圈'
      if (this.erjiParams.region_id && this.regionList.length > 0) {
        let res = this.regionList.find((item) => item.id == this.erjiParams.region_id)
        if (res && res.name) {
          region = res.name
        }
      }
      return region
    },
    currentPrice () {
      let price = '价格'
      console.log(this.erjiParams.rent_price, this.erjiParams.sale_price);
      if ((this.erjiParams.rent_price || this.erjiParams.sale_price) && this.priceList.length > 0) {
        let res = this.priceList.find(
          (item) => item.id == (this.erjiParams.rent_price || this.erjiParams.sale_price)
        )
        if (res && res.name) {
          price = res.name
        }
      }
      return price
    },
    filtersNew () {
      return this.filters.filter((item) => {
        return item.type !== 'yongtu_id'
        // return this.filters
      })
    },
    thirdTabs () {
      let f = this.filters.filter((item) => {
        return item.type == 'label_type'
        // return this.filters
      })
      console.log(f);
      if (f.length) {
        return f[0].item
      }
      return []
    }
  },
  mounted () {
    this.$nextTick(() => {
      this.query = uni.createSelectorQuery().in(this)
      // this.setActiveBar()
      this.setFilterTop()
    })
  },
  methods: {
    del(){
      uni.removeStorageSync('mobiles');
      uni.removeStorageSync('keywords');
    },
    getUnfollow () {
      this.$ajax.get("/admin/house/remindPhoneFollow", {}, res => {
        if (res.statusCode == 200) {
          this.unfollowInfo = res.data
        }
      })
    },
    checkPerssion (e, callback) {
      if (this.unfollowInfo.remindPhoneFollow && this.unfollowInfo.count > 0) {
        uni.showModal({
          title: "提示",
          content: `您有${this.unfollowInfo.count}条未跟进记录需要跟进,立即跟进？`,
          confirmText: "立即跟进",
          success: (res) => {
            if (res.confirm) {
              this.$navigateTo(`/house/detail?id=${this.unfollowInfo.info.info_id}`)
            }
          }
        })
      } else {
        callback && callback(e)
      }
    },
    getPerminsion () {
      this.$ajax.get('/admin/my/query/permissions/list', {}, (res) => {

        if (res.statusCode == 200) {
          let tabList = res.data.filter(item => item.is_show && item.id == 3)
          let tabL = [], tabIds = []
          if (tabList && tabList.length) {
            tabL = tabList[0].children
            if (tabL && tabL.length) {
              tabL = tabL.filter(item => item.id == 186)
            }
            console.log(tabL);
            if (tabL && tabL.length && tabL[0].children && tabL[0].children.length) {
              tabL[0].children.map(item => {
                tabIds.push(item.id)
              })
            }
          }
          this.tab_list = []
          let type = {
            187: {
              type: '',
              name: '公盘',
              index: 0,
            },
            195: {
              name: '出售',
              type: '1',
              index: 1,
            },
            196: {
              name: '出租',
              type: '2',
              index: 2,
            },
            197: {
              name: '租售',
              type: '3',
              index: 3,
            }
          }
          for (const key in type) {
            console.log(key, tabIds, tabIds.includes(key));
            if (tabIds.includes(+key)) {
              this.tab_list.push(
                type[key]
              )
            }
          }
          this.tab_list.push({
            name: '我的房源',
            type: 0,
            index: 4,
          })
          if (this.type > 0) {
            this.params.trade_type = this.type
          }else if (this.type === 0){
            this.params.is_owner = 1
          } else {
            this.type = this.tab_list[0].type
            this.params.trade_type = this.type
          }
          let index = this.tab_list.find(item => item.index == this.params.trade_type)
          if(index){
            this.current_tab = index.type
          }else {
            this.current_tab =''
          }
          if(this.params.is_owner){
            this.current_tab = 0
          }
          this.getData()
          this.getOptions()
        }
      })
    },
    getData () {
      if (this.params.page == 1) {
        this.house_list = []
      }
      let params = Object.assign({}, this.params, this.erjiParams, this.sanjiSimiParams, this.otherParams)
      // if (this.current_tab == 1111) {
      //   params.is_owner = 1
      //   delete params.trade_type
      // } else {
      //   delete params.is_owner
      // }
      // if (this.erjiParams.trade_status == 99999) {
      //   params.department_id = this.department_id
      //   params.trade_status = ''
      // }
      if( params.is_owner){
        delete params.trade_type
      }
      if(params.trade_type){
        delete params.is_owner
      }
      this.load_status = 'loading'
      this.$ajax.get('/admin/house/privateHouses', params, (res) => {
        if (res.statusCode == 200) {
          // let department_id = res.data?.current_login_user?.department_id
          // if (department_id) {
          //   department_id = (department_id + '').split(",")
          //   this.department_id = department_id[department_id.length - 1]
          // }
          if (res.data.data.length < this.params.rows) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'loadend'
          }
          let current_login_user = res.data.current_login_user || {}
          res.data.data.map(item => {
            item.current_login_user = current_login_user
            return item
          })
          this.house_list = this.house_list.concat(res.data.data)
        } else {
          this.load_status = 'nomore'
        }
      }, () => {
        this.load_status = 'nomore'
      })

    },
    getOptions () {
      this.$ajax.get("/admin/house/houseConditionByLm", {}, res => {
        console.log(res);
        if (res.statusCode == 200) {
          let _filters = res.data.second.concat(res.data.first)
          let status = _filters.find(item => item.type == 'trade_status')
          if (status) {
            this.typeList = status.item
          }
          // this.typeList.push({
          //   name: '本部',
          //   values: "99999"
          // })

          this.filters = _filters.map((e, index) => {
            // 区域传参是传的汉字，需要把valus的值设置为其name的值
            if (e.type === 'districtName') {
              e.item.map((chil) => (chil.values = chil.name))
            }
            if (e.type == 'region_id') {
              this.regionList = e.item.map((region) => {
                region.id = region.values
                return region
              })
              this.regionList.unshift({
                id: '',
                name: "全部"
              })
            }
            if (e.type == 'sale_price') {
              this.priceList = e.item.map((price) => {
                price.id = price.values
                return price
              })
              this.priceList.unshift({
                id: '',
                name: "全部"
              })
            }
            if (e.type == 'mianji') {
              this.areaList = e.item.map((mianji) => {
                mianji.id = mianji.values
                return mianji
              })
              this.areaList.unshift({
                id: '',
                name: "全部"
              })
            }
            // 如果支持自定义数值
            if (e.customizale) {
              e.custom_value = {
                min: '',
                max: '',
              }
            }
            if (e.unit) {
              var unit_reg = new RegExp(e.unit, 'g')
              e.item.map((chil) => {
                chil.name = chil.name.replace(unit_reg, '')
              })
            }
            if (!e.multiple && e.item.findIndex((chil) => chil.values === '') === -1) {
              // 追加一个不限
              e.item.unshift({
                name: '不限',
                values: '',
              })
            }
            e.id = `filter_${index}`
            // let obj = {
            //   id: e.id,
            //   name: e.label,
            // }
            // if (
            //   e.type == 'trade_type' ||
            //   e.type == 'region_id' ||
            //   e.type == 'mianji' ||
            //   e.type == 'sale_price' ||
            //   e.type == 'rent_price'
            // ) {
            //   this.secondTabs.push(obj)
            // }
            return e
          })
          this.scroll_into_view = this.filters[0].id
        }
      })
    },
    onTabChange (e) {
      // this.params = {
      //   page: 1,
      //   rows: 20
      // }
      console.log(e);
      
      this.params.page = 1
      this.erjiParams = {
        region_id: '',
        mianji: '',
        sale_price: '',
        rent_price: '',
        trade_status: 9
        // trade_type: '',
      }
      this.sanjiSimiParams = {
        exclusive: '',
        has_key: '',
        youxiao: '',
        wuxiao: '',
        yichengjiao: '',
        fenyong: '',
      }
      this.otherParams = {
        loudong: '',
        danyuan: '',
        fanghao: '',
        tel: ''
      }
      this.current_tab = e
      this.params.trade_type=e
      // this.erjiParams.trade_type = e
      if(e===0){
        this.params.is_owner=1
        delete this.params.trade_type
      }else {
        delete this.params.is_owner
      }
      
      this.getData()
    },
    toPrivateDetail (e) {
      this.$navigateTo(`/house/detail?id=${e.id}`)
    },
    copyPrivate (e) {
      this.$ajax.get(`/admin/house/shareHouse/${e.id}`, {}, res => {
        if (res.statusCode == 200) {
          this.$set(e, 'current_login_user', res.data)
          copyPrivate(e)
        } else {
          uni.showToast({
            title: res.data.message || '请求失败',
            icon: 'none'
          })
        }
      })

      //       let price = ''
      //       if (e.trade_type == 1 || e.trade_type == 3) {
      //         if (e.sale_price) {
      //           price = '售价：' + Number(e.sale_price) / 10000 + '万元'
      //         } else {
      //           price = '售价：' + '面议'
      //         }
      //       }
      //       if (e.trade_type == 2) {
      //         if (e.rent_price) {
      //           price = '租金：' + e.rent_price + '元/月'
      //         } else {
      //           price = '租金：' + '面议'
      //         }
      //       }
      //       let content = `房源名称：${e.title}
      // 户型：${e.shi}室${e.ting}厅${e.wei}卫
      // 商圈：${e.area_name}- ${e.region_name}
      // 楼层：${e.sz_floor || ''}/${e.total_floor || ''}层
      // 楼栋：${e.loudong}- ${e.danyuan}-${e.fanghao}
      // ${price}
      // 电话：${e.owner_tel || ''}`

      //       this.$copyText(content, () => {
      //         uni.showToast({
      //           title: '复制成功',
      //           icon: 'none',
      //         })
      //       })
    },
    followPrivate (e) {
      this.$navigateTo(`/house/follow_up?follow_id=${e.id}&from=private`)
    },
    houseGroup (house) {
      this.$navigateTo("/house/house_send?house_id=" + house.id + "&protect=" + house.protect)
    },
    tixing (e) {
      this.$navigateTo(`/house/remind?remind_id=${e.id}`)
    },
    setLevels (e) {
      if (e.unilateral_agent == 1 && e.unilateral_agent_auth == 0) {
        uni.showToast({
          title: '请联系房源维护人',
          icon: 'none',
        })
        return
      }

      this.$ajax.post('/admin/house/editPrivateHouse', { id: e.house.id, level: e.level }, (res) => {
        if (res.statusCode == 200) {
          uni.showToast({
            title: '等级更改成功',
            icon: 'none',
          })
          this.params.page = 1
          this.getData()
        } else {
          uni.showToast({
            title: '等级更改失败',
            icon: 'none',
          })
        }
      }, () => {
        uni.showToast({
          title: '等级更改失败',
          icon: 'none',
        })
      })
    },
    addToLianmai (house) {
      this.showModal({
        title: '确定发布到联卖房源吗？',
        confirm: () => {
          this.$ajax.get(`/v1/wapLm/releaseToLm/${house.id}`).then((res) => {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
            })
          })
        },
      })
    },
    toAdd () {
      this.$navigateTo('/house/add?tradeStatus=' + (this.current_tab || 1))
    },
    toPage (name, query = {}) {
      this.$navigateTo({
        name,
        query,
      })
    },
    addToShowing (e) {
      this.current_house = e
      this.show_set_showing = true
    },
    setShowingSuccess () {
      this.show_set_showing = false
      this.getData()
    },
    houseTop (e) {
      uni.showModal({
        title: '是否聚焦该房源？',
        success: (result) => {
          if (result.confirm) {
            this.$ajax.post('/v1/wapLm/editPrivateHouse', { id: e.id, is_top: 1 }, (res) => {
              uni.showToast({
                title: res.data.message,
                icon: 'none',
              })
              if (res.data.status == 200) {
                this.params.page = 1
                this.initData()
              }
            })
          } else if (result.cancel) {
            console.log('用户点击取消');
          }

        },
      })
    },
    houseDel (e) {
      uni.showModal({
        title: '是否删除该房源？',
        success: (result) => {
          if (result.confirm) {
            this.$ajax.post('/v1/wapLm/editPrivateHouse', { id: e.id, is_del: 1 }, (res) => {
              uni.showToast({
                title: res.data.message,
                icon: 'none',
              })
              if (res.data.status == 200) {
                this.params.page = 1
                this.initData()
              }
            })
          } else if (result.cancel) {
            console.log('用户点击取消');
          }

        },
      })
    },
    showCommSearch () {
      this.show_tel_search = false
      this.show_loudong_search = false
      this.setLoudongTop()

      this.show_comm_search = !this.show_comm_search
    },
    showLoudongSearch () {
      this.show_tel_search = false
      this.show_comm_search = false
      this.setLoudongTop()
      this.show_loudong_search = !this.show_loudong_search
    },
    initData () {
      this.params.page = 1
      this.getData()
    },
    setLoudongTop () {
      const current_node_shaixuan = this.query.select('#i_search')
      let top = 0,
        i_height = 0
      current_node_shaixuan
        .fields({ rect: true, size: true }, (res) => {
          top = res.top
          i_height = res.height
        })
        .exec()
      this.loudong_search = top + i_height + 'px'
    },
    showTelSearch () {
      this.show_loudong_search = false
      this.show_comm_search = false
      this.setLoudongTop()
      this.show_tel_search = !this.show_tel_search
    },
    filterTelData () {
      this.show_tel_search = false
      this.initData()
    },
    resetLoudongData () {
      this.otherParams.loudong = ''
      this.otherParams.danyuan = ''
      this.otherParams.fanghao = ''
    },
    filterLoudongData () {
      this.show_loudong_search = false
      this.initData()
    },
    resetCommData () {
this.del()
      this.otherParams.keyword = ''
      this.otherParams.hid = ''

    },
    filterCommData () {
      this.show_comm_search = false
      this.initData()
    },
    setFilterTop () {
      const current_node_shaixuan = this.query.select('#second_tab')
      current_node_shaixuan
        .fields({ rect: true, size: true }, (res) => {
          this.filterTop = +res.top + +res.height + 'px'
          console.log(this.filterTop)
        })
        .exec()
    },
    changeType (item) {
      this.showMask = false
      switch (this.current_second_tab) {
        case 1:
          this.erjiParams.trade_status = item.values
          break
        case 2:
          this.erjiParams.region_id = item.id
          break
        case 3:
          if (
            this.erjiParams.trade_type == 1 ||
            this.erjiParams.trade_type == 3 ||
            !this.erjiParams.trade_type
          ) {
            delete this.erjiParams.rent_price
            this.erjiParams.sale_price = item.values
          } else {
            delete this.erjiParams.sale_price
            this.erjiParams.rent_price = item.values
          }
          break
        case 4:
          this.erjiParams.mianji = item.id
          break
        default:
          break
      }
      this.current_second_tab = ''
      this.initData()
    },
    changeSecondTab (index) {
      if (this.current_second_tab == index) return (this.current_second_tab = '')
      this.current_second_tab = index
      this.showMask = true
    },
    changThirdTab (item, index) {
      this.current_third_index = index
      this.$set(this.sanjiSimiParams, 'label_type', item.values)
      this.params.page = 1
      this.initData()
    },
    resetData () {
      // 切换清空min，max参数
      this.resetFilterArr()
      this.erjiParams = {
        rent_price: '',
        sale_price: '',
      }
      this.initData()
      this.current_second_tab = ''
      this.showMask = false
    },
    // 清空筛选中输入的min和max
    resetFilterArr () {
      // 切换清空min，max参数
      this.filters.map((item) => {
        if (item.customizale == 1 && (item.custom_value.max || item.custom_value.min)) {
          item.custom_value.max = ''
          item.custom_value.min = ''
        }
      })
    },


    filterData () {
      this.initData()
      this.current_second_tab = ''
      this.showMask = false
    },
    hideErjiSearch () {
      this.current_second_tab = ''
      this.showMask = false
    },
    setFilterItem (option, type) {
      if (type) {
        this.$set(this.erjiParams, type, option.values)
      } else if (option.type) {
        if (this.erjiParams[option.type]) {
          this.$set(this.erjiParams, option.type, '')
        } else {
          this.$set(this.erjiParams, option.type, option.values)
        }
      }
    },
    checkFilter (index, item) {
      this.scroll_into_view = item.id
      this.scroll_into_view_index = index
    },
    onFilterOptionsScroll (e) {
      console.log(e, this.filters[1]);
      for (let i = 0; i < this.filters.length; i++) {
        var scroll_difference = e.detail.scrollTop - this.filters[i].offsetTop
        if (scroll_difference >= -5 && scroll_difference <= this.filters[i].height) {
          this.scroll_into_view = ''
          this.scroll_into_view_index = i
          continue
        }
      }
    },

  },
  onReachBottom () {
    if (this.load_status === 'loadend') {
      this.params.page++
      this.getData()
    }
  },
  onNavigationBarButtonTap (e) {
    if (e.index === 0) {
      this.show_edit = !this.show_edit
    }
  },
}
</script>

<style lang="scss">
.index {
  ::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
  }
  .sticky {
    // padding: 0 48rpx;
    // #ifdef H5
    top: 0;
    // #ifdef H5-WEIXIN
    top: 0;
    // #endif
    // #endif
    // #ifndef H5
    top: 0;
    // #endif
    position: sticky;
    z-index: 9;
    background-color: #fff;
  }
  .tab-box {
    width: 100%;
    padding: 0 48rpx;
    z-index: 9;
    background: #fff;
  }
  .house_list {
    // padding: 0 48rpx;
    width: 100vw;
    // overflow: hidden;
    &.padtop64 {
      padding-top: 64rpx;
    }
    .house {
      padding: 24rpx 0;
      // overflow: hidden;
      display: flex;
      flex-direction: row;
      align-items: flex-start;
    }
  }
}
.add {
  position: fixed;
  right: 24rpx;
  bottom: 150rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-image: linear-gradient(135deg, #4daaff 0%, #2671ff 100%);
  box-shadow: 0px 4px 10px 0px rgba(46, 124, 255, 0.4);
  z-index: 3;
  .icon {
    width: 48rpx;
    height: 48rpx;
    image {
      width: 100%;
      height: 100%;
    }
  }
  .add_name {
    color: #fff;
    font-size: 24rpx;
  }
}
.i_search {
  z-index: 9;
  padding: 0 48rpx;
  background: #fff;
  position: relative;
  ::v-deep .input-box {
    border-radius: 10rpx;
    // background: #fff;
    // border: 1rpx solid #2d84fb;
  }
  .search_loudong {
    background: #f8f8f8;
    height: 76rpx;
    justify-content: center;
    align-items: center;
    padding: 0 20rpx;
    margin-left: 10rpx;
    font-size: 24rpx;
    color: #999;
    &.search_comm {
      justify-content: space-between;
    }
    .xiasanjiao {
      height: 0;
      width: 0;
      border: 10rpx solid transparent;
      border-top-color: #999;
      margin-left: 10rpx;
      margin-top: 10rpx;
    }
    .shangsanjiao {
      height: 0;
      width: 0;
      border: 10rpx solid transparent;
      border-bottom-color: #999;
      margin-left: 10rpx;
      margin-bottom: 10rpx;
    }
  }
}
.search_container {
  position: fixed;
  left: 0;
  right: 0;
  top: 110rpx;

  background: #fff;
  z-index: 8;
  transform: translateY(-300%);
  transition: 0.26s;
  .search_con {
    background: #fff;
    padding: 24rpx 48rpx 48rpx;
    z-index: 5;
  }
  .search_title {
    color: #333;
    font-weight: 700;
    padding-bottom: 20rpx;
  }
  .search_inp {
    border: 2rpx solid #f0f0f0;
    border-radius: 4rpx;
    padding: 10rpx;
    ~ .search_inp {
      margin-left: 20rpx;
    }
    &.mat10 {
      margin-top: 10rpx;
    }
    &.mal0 {
      margin-left: 0;
    }
    input {
      color: #666;
    }
    .pls {
      color: #d6d6d6;
    }
  }
  .btn_group {
    // min-height: 100rpx;
    box-shadow: 0 -6rpx 10rpx 0 #f1f1f1;
    margin-top: 40rpx;
    .btn {
      padding: 12rpx 24rpx;
      flex: 1;
      text-align: center;
      &.bg_highlight {
        background-color: $color-primary;
        color: #fff;
      }
    }
  }
  .mask {
    position: fixed;
    z-index: -1;
    width: 100%;
    height: 100vh;
    left: 0;
    background-color: #000;
    display: none;
    transition: 0.26s;
  }
  &.show {
    // .filter_box {
    // z-index: 5;
    transform: translateY(0);

    // }
    .mask {
      display: flex;
      z-index: 1;
      opacity: 0.3;
    }
  }
}
.second_tab {
  padding: 24rpx;
  position: sticky;
  top: 150rpx;
  z-index: 6;
  background: #fff;

  .second_tab_item {
    max-width: 20%;
    .item_name {
      font-size: 28rpx;
      margin-right: 8rpx;
      color: #8a929f;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
    }
    .sanjiao {
      margin-top: 10rpx;
      width: 0;
      height: 0;
      border: 13rpx solid;
      border-color: #d8d8d8 transparent transparent transparent;
    }
  }
  .second_tab_con {
    position: absolute;
    top: 80rpx;
    left: 0;
    right: 0;
    z-index: 5;
    // padding: 0 48rpx;
    background: #fff;
    .second_tab_con_i {
      background: #fff;
      padding: 0 48rpx;
      max-height: 60vh;
      overflow-y: auto;
    }
    .second_tab_con_item {
      padding: 20rpx 0;
    }
    .mask {
      position: fixed;
      z-index: -1;
      width: 100%;
      height: 100vh;
      left: 0;
      background-color: #000;
      opacity: 0.5;
      transition: 0.26s;
    }
  }
}
.nav-box {
  width: 100%;
  // background-color: #fff;
  // border-bottom: 1upx solid $uni-border-color;
  // box-shadow: 0 4upx 10upx #e6e6e6;
}
.third_tab {
  padding: 28rpx 0 28rpx 48rpx;
  background: #f7f7f7;
}
.filter_container {
  touch-action: none;
  .filter_box {
    padding-top: 24rpx;
    position: fixed;
    z-index: 5;
    width: 100%;
    left: 0;
    top: 200rpx;
    overflow: hidden;
    background-color: #fff;
    max-height: calc(100vh - 200rpx - 200px);
    transform: translateY(-150%);
    transition: 0.26s;
    .filter_list {
      flex: 1;
      overflow: hidden;
    }
    .filter_labels {
      width: 200rpx;
      text-align: center;
      background-color: $bg-color-grey;
      .item {
        padding: 24rpx 12rpx;
        &.active {
          background-color: #fff;
          color: $color-primary;
        }
      }
    }
    .filter_options {
      padding: 0 24rpx;
      .item {
        .label {
          padding: 24rpx 0;
        }
        .value_list {
          flex-direction: row;
          flex-wrap: wrap;
          justify-content: space-between;
        }
        .value {
          width: 172rpx;
          margin-bottom: 24rpx;
          text-align: center;
          padding: 16rpx 6rpx;
          border-radius: 4rpx;
          font-size: 24rpx;
          // flex: 1;
          background-color: $bg-color-grey;
          &.active {
            background-color: rgba($color: $color-primary, $alpha: 0.2);
            color: $color-primary;
          }
        }
        .empty_value {
          width: 172rpx;
        }
        .input_range {
          margin-bottom: 24rpx;
          align-items: center;
          .input {
            padding: 12rpx;
            height: 64rpx;
            width: 172rpx;
            font-size: 24rpx;
            background-color: $bg-color-grey;
          }
          .mark {
            width: 24rpx;
            text-align: center;
          }
        }
      }
    }
  }
  .btn_group {
    min-height: 100rpx;
    box-shadow: 0 -6rpx 10rpx 0 #f1f1f1;
    .btn {
      padding: 24rpx;
      flex: 1;
      text-align: center;
      &.bg_highlight {
        background-color: $color-primary;
        color: #fff;
      }
    }
  }
  .mask {
    position: fixed;
    z-index: -1;
    width: 100%;
    height: 100vh;
    left: 0;
    background-color: #000;
    opacity: 0;
    transition: 0.26s;
  }
  &.show {
    .filter_box {
      // z-index: 5;
      transform: translateY(0);
    }
    .mask {
      z-index: 4;
      opacity: 0.5;
    }
  }
}
.nav-list {
  width: 100%;
  white-space: nowrap;
}
.nav-item {
  display: inline-block;
  padding: 12rpx 16rpx;
  border-radius: 4rpx;
  // line-height: 1;
  // box-sizing: border-box;
  margin-right: 24rpx;
  font-size: 22rpx;
  background: #fff;
  color: #8a929f;
  image {
    // width: 40rpx;
    height: 18rpx;
    object-fit: cover;
  }
}

.nav-item.active {
  color: #fff;
  position: relative;
  background: #2d84fb;
}
</style>
