<template>
  <view class="list">
    <view class="title-bar">
      <my-search placeholder="请输入客户手机号码" @input="searchTel">
        <template v-slot:left>
          <my-icon type="ic_sousuo3x1" color="#999"></my-icon>
        </template>
        <template v-slot:right>
          <text class="xinzeng" @click="newAdd">新增</text>
        </template>
      </my-search>
      <!-- 导航 -->
      <view class="nav row">
        <view
          v-for="(item, index) in report_status"
          :key="index"
          class="nav-item"
          :class="{ active: parseInt(item.value) === data_type }"
          @click="onClickNav(index)"
          >{{ item.description }}</view
        >
      </view>
    </view>
    <view class="report-list">
      <report-item
        class="report-item"
        @open="reportDetail(item.id)"
        v-for="item in report_list"
        :report_item="item"
        :key="item.id"
        :status="item.status"
        @createFillPhone="createFillPhone"
      ></report-item>
    </view>
    <load-more :status="load_status"></load-more>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import mySearch from "@/components/my-search";
import myIcon from "@/components/my-icon";
import reportItem from "@/components/report-item";
import loadMore from "@/components/loadMore";
export default {
  components: { mySearch, myIcon, reportItem, loadMore },
  data() {
    return {
      data_type: 0,
      report_list: [],
      build_category_list: [],
      report_status: [
        { value: "0", description: "报备未接收" },
        { value: "1", description: "报备有效" },
        { value: "10", description: "报备无效" },
      ],
      params: {
        page: 1,
        status: "",
        project_id: "",
        customer_phone: "",
      },
      full_num_reported: "",
      load_status: "",
    };
  },
  onLoad(options) {
    if (options) {
      this.params.project_id = parseInt(options.id);
      this.params.status = this.data_type;
      this.full_num_reported = options.full_num_reported;
    }
    this.init();
    this.getListData();
  },

  methods: {
    onClickNav(index) {
      this.params.page = 1;
      this.data_type = parseInt(this.report_status[index].value);
      this.params.status = this.data_type;
      this.getListData();
    },
    // 匹配楼盘数据
    init() {
      this.$getDictionaryList("BUILD_CATEGORY", {}, (res) => {
        if (res.statusCode === 200) {
          this.build_category_list = res.data.data;
        }
      });
    },
    getListData() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.report_list = [];
      }
      this.$ajax.get(
        `/client/customer/reported/search/broker`,
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.report_list = this.report_list.concat(res.data.data);
            for (var i = 0; i < this.report_list.length; i++) {
              this.report_list[i].build_category_str = [];
              this.build_category_list.map((item) => {
                if (this.report_list[i].build_category.includes(item.value)) {
                  this.report_list[i].build_category_str.push(item.description);
                }
              });
            }
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        }
      );
    },

    searchTel(tel) {
      this.$debounce(this.searchTelDebounce, 500)(tel);
    },
    searchTelDebounce(tel) {
      let params = {
        page: 1,
        customer_phone: tel,
      };
      this.load_status = "loading";
      this.$ajax.get(
        "/client/customer/reported/search/broker",
        params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.report_list = res.data.data;
          }
        }
      );
    },
    reportDetail(id) {
      this.$navigateTo(`/report/report_detail?id=${id}`);
    },
    newAdd() {
      this.$navigateTo(`/report/report_client?currentTel=1`);
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.getListData();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getListData();
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #eee;
}
.title-bar {
  background: #fff;
}
// 导航
.nav {
  width: 100%;
  background: #fff;
  padding-bottom: 0;
  justify-content: space-around;
  .nav-item {
    padding: 20rpx 5rpx;
    text-align: center;
    font-weight: bold;
    color: #666;
    &.active {
      color: #0174ff;
      border-bottom: 2rpx solid #0174ff;
    }
  }
}
.xinzeng {
  position: absolute;
  right: 48rpx;
  color: #0174ff;
}
.report-item:last-child {
  margin-bottom: 20rpx;
}
</style>
