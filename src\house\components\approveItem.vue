<template>
  <view class="apply_item">
    <view class="title flex-row items-center">
      <view class="title_con flex-1">
        {{ item.title }}
      </view>
      <view class="time">
        {{ item.time }}
      </view>
    </view>
    <view class="info">
      <view class="info_name flex-row" v-for="(te, index) in item.template" :key="index"
        ><text class="name"> {{ te.label }}：</text>{{ te.value }}
      </view>
    </view>
    <view class="apply_info flex-row items-center">
      <view class="prelogo">
        <image :src="item.agent ? item.agent.head_image : ''"> </image>
      </view>
      <view class="apply_name flex-1 flex-row">
        由<text>{{ item.agent ? item.agent.cname : '' }}</text
        >提交
      </view>
      <view class="apply_status" :class="'apply_status' + item.status">
        {{ item.status_name }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    item: Object,
  },
  data() {
    return {}
  },
  methods: {},
}
</script>

<style scoped lang="scss">
.apply_item {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  .title {
    .title_con {
      font-size: 32rpx;
      font-weight: bolder;
      color: #2e3c4e;
    }
    .time {
      font-size: 22rpx;
      color: #8a929f;
    }
  }
  .info {
    margin-top: 24rpx;
    .info_name {
      margin-bottom: 24rpx;
      font-size: 22rpx;
      color: #8a929f;
      .name {
        width: 120rpx;
        margin-right: 24rpx;
      }
    }
  }
  .apply_info {
    .prelogo {
      height: 48rpx;
      width: 48rpx;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 10rpx;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .apply_name {
      font-size: 22rpx;
      color: #8a929f;
      text {
        color: #2d84fb;
        margin: 0 10rpx;
      }
    }
    .apply_status {
      padding: 8rpx 36rpx;
      border-radius: 8rpx;
      font-size: 22rpx;

      &.apply_status2 {
        //待同意 rgba(45,132,251,1)
        background: rgba(45, 132, 251, 0.15);
        color: rgba(45, 132, 251, 1);
      }
      &.apply_status0 {
        //审批中 rgba(245,111,107,0.15);
        background: rgba(245, 111, 107, 0.15);
        color: rgba(245, 111, 107, 1);
      }
      &.apply_status3 {
        //已拒绝 rgba(167, 201, 237,0.15);
        background: rgba(167, 201, 237, 0.15);
        color: rgba(167, 201, 237, 1);
      }
      &.apply_status1 {
        //已同意
        background: rgba(12, 217, 103, 0.15);
        color: rgba(12, 217, 103, 1);
      }
    }
  }
}
</style>
