<template>
  <view class="page">
    <view
      class="info"
      :class="[
        status_value == 1 ? 'green' : '',
        status_value == 0 ? 'primary' : '',
        status_value == 2 ? 'gray' : '',
      ]"
    >
      <view class="top flex-row item-center">
        <view class="top_item flex-1"> 未拨/已拨/总量 </view>
        <view class="status"> {{ status_value | filterStatus }} </view>
      </view>
      <view class="center">
        {{ detail.unCallCount }}/{{ detail.callCount }}/{{ detail.totalCallCount }}
      </view>
      <view class="bottom"> 通话时长 ：{{ totalDuration | formatTime}}</view>
    </view>
    <view class="tab-box">
      <tabs
        :options="tab_list"
        :format="{ name: 'name', value: 'type' }"
        equispaced
        :showAnimation="false"
        v-model="status"
        @change="onTabChange"
      ></tabs>
    </view>
    <view class="list">
      <view class="list_item flex-row" v-for="item in list" :key="item.id" @click="makePhone(item)">
        <!-- <view class="left"> -->
        <view class="left_img">
          <image
            v-if="item.is_call == 0"
            mode="widthFix"
            :src="`/static/h5detail/outbound/on2.png` | imageFilter('w_80')"
          ></image>
          <image
            v-else-if="item.is_call == 1 && item.is_on == 0"
            mode="widthFix"
            :src="`/static/h5detail/outbound/on3.png` | imageFilter('w_80')"
          ></image>
          <image
            v-else-if="item.is_call == 1 && item.is_on == 1"
            mode="widthFix"
            :src="`/static/h5detail/outbound/on1.png` | imageFilter('w_80')"
          ></image>
        </view>
        <view class="left_center">
          <view class="left_title">{{ item.phone }} </view>
          <view class="left_bottom un_call" v-if="item.is_call == 0"> <text> 未拨</text> </view>
          <view class="left_bottom un_tong" v-else-if="item.is_call == 1 && item.is_on == 0">
            <text>未接通</text>
          </view>
          <view class="left_bottom tong" v-else-if="item.is_call == 1 && item.is_on == 1">
            <text>已接通</text>
          </view>
        </view>
        <!-- </view> -->
      </view>
      <loadMore :status="load_status" @reload="getData()" />
    </view>
    <myPopup
      ref="showPhone"
      :show="show_phone_pop"
      position="center"
      width="80%"
      @close="show_phone_pop = false"
    >
      <view class="p_con">
        <view class="title"> 拨打电话 </view>
        <view class="p_content">
          <view class="p_item flex-row items-center">
            <view class="label"> 选择外显号码 </view>
            <view class="value flex-1">
              <selectDown
                valueName="phone"
                :multiple="false"
                v-model="show_id"
                :localdata="phoneList"
                @change="changeSelect"
                defaultValue=""
                placeholder="请选择外显号码"
              >
              </selectDown>
            </view>
          </view>
          <view class="btns flex-row items-center">
            <view class="btn flex-1" @click="conMakePhone"> 确认拨打 </view>
          </view>
        </view>
      </view>
    </myPopup>
  </view>
</template>

<script>
import Tabs from './components/Tabs'
import myPopup from "./components/myPopup"
import selectDown from './components/uni-data-select'
import loadMore from '@/components/loadMore'
export default {
  components: { Tabs, loadMore, myPopup, selectDown },
  filters: {
    filterStatus (val) {
      let name = ''
      switch (+val) {
        case 0:
          name = '未进行'
          break;
        case 1:
          name = '进行中'
          break;
        case 2:
          name = '已完成'
          break;

        default:
          name = '未进行'
          break;
      }
      return name
    }
  },
  data () {
    return {
      tab_list: [
        {
          type: '',
          name: '全部',
          index: 0,
        },
        {
          name: '已拨打',
          type: 'is_call-1',
          index: 1,
        },
        {
          name: '未拨打',
          type: 'is_call-0',
          index: 2,
        },
        {
          name: '已接通',
          type: 'is_on-1',
          index: 3,
        },
        {
          name: '未接通',
          type: 'is_on-0',
          index: 4,
        }
      ],
      status: '',
      list: [],
      detail: {
        unCallCount: 0,
        callCount: 0,
        totalCallCount: 0,
        totalDuration: 0
      },
      params: {
        member_task_id: '',
        is_call: "",
        is_on: "",
        page: 1,
        rows: 20,
      },
      show_id: "",
      phoneList: [],
      show_phone_pop: false,
      load_status: "loading",
      totalDuration:0
    }
  },
  onLoad (options) {
    this.params.member_task_id = options.id
    this.status_value = options.status
    this.getInfo()
    this.getData()
this.formatTime()
  },
  filters: { 
    formatTime(time) {
    console.log(11111);
  let hours = Math.floor(time / 3600);
  let minutes = Math.floor((time % 3600) / 60);
  let seconds = time % 60;

  return hours + ":" + minutes + ":" + seconds;
}
  },
  methods: {        // 将通话时长转化成0:0:0的形式
    
    onTabChange (e) {
      console.log(e);
      if (e === '') {
        this.params.is_on = ''
        this.params.is_call = ''
      } else {
        let arr = e.split("-")
        this.params[arr[0]] = arr[1]
        if (e == 'is_on-0' || e == 'is_on-1') {
          this.params.is_call = ''
        } else {
          this.params.is_on = ''
        }
      }
      this.params.page = 1
      this.getData()

    },
    getInfo () {
      this.$ajax.get('/admin/call_clue/myCallTaskStatistics', { member_task_id: this.params.member_task_id }, res => {
        console.log(res);
        if (res.statusCode == 200) {
          this.detail = res.data
          console.log(res.data.totalDuration,'res.data.totalDuration');
this.totalDuration =res.data.totalDuration;
console.log(this.totalDuration,'this.detail.totalDuration');
        }
      })
    },
    changeSelect (e) {
      console.log(e);


    },
    makePhone (item) {
      this.current = item
      if (!this.phoneList.length) {
        this.getphoneList()
      }
      this.show_id = ''
      this.show_phone_pop = true
      // this.$refs.showPhone.show()


      // uni.showModal({
      //   title: '确定拨打电话嘛？',
      //   success: (res) => {
      //     console.log(res);
      //     if (res.confirm) {
      //       console.log(item, 111);
      //       this.conMakePhone(item)
      //     }
      //   }
      // })
    },
    getphoneList () {
      this.$ajax.get('/qywx/call_phone/getSeatsPhone', {}, res => {
        if (res.statusCode == 200) {
          this.phoneList = res.data.map(item => {
            item.value = item.show_id
            item.text = item.phone
            return item
          })
          // this.phoneList = this.phoneList.concat(this.phoneList).concat(this.phoneList)
          //   .concat(this.phoneList)
          //   .concat(this.phoneList)

        }
      })
    },
    conMakePhone (item) {
      if (!this.show_id) {
        uni.showToast({
          title: "请选择外显号码",
          icon: "none"
        })
        return
      }
      this.$ajax.get(`/qywx/call_phone/memberCallCluePhone/${this.current.id}`, { show_id: this.show_id }, res => {
        console.log(res.data.telX,'-------');
        if (res.statusCode == 200) {
          // uni.showToast({
          //   title: '正在跳转拨号页面 请稍后',
          //   icon: "none",
          //   duration: 2000
          // })
          uni.makePhoneCall({
        phoneNumber: res.data.telX,
        success: () => {
          console.log("正在拨打中 请稍后");
        },
      });
          this.show_phone_pop = false
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
            duration: 2000
          })
        }
      })
    },
    getData () {
      if (this.params.page == 1) {
        this.list = []
      }
      this.load_status = "loading"
      this.$ajax.get("/qywx/call_phone/myCallTaskList", this.params, res => {
        console.log(res,'999');
        if (res.statusCode == 200) {
          this.list = this.list.concat(res.data.data)
          if (res.data.data.length < this.params.rows) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'loadend'
          }
        } else {
          this.load_status = 'nomore'
        }
      }, () => {
        this.load_status = 'nomore'
      })
    }
  },
  onReachBottom () {
    if (this.load_status === 'loadend') {
      this.params.page++
      this.getData()
    }
  },
}
</script>

<style lang ="scss" scoped>
.page {
  min-height: 100vh;
  padding: 0 24rpx;
  background: #f6f6f6;
}
.info {
  &.green {
    background: #11d060;
  }
  &.primary {
    background: #2d84fb;
  }
  &.gray {
    background: #e3e8f3;
  }
  margin-top: 24rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  .top {
    font-size: 22rpx;
    color: #ffffff;
    margin-bottom: 24rpx;
  }
  .center {
    font-size: 48rpx;
    color: #ffffff;
    margin-bottom: 48rpx;
  }
  .bottom {
    font-size: 22rpx;
    color: #ffffff;
    margin-bottom: 24rpx;
  }
}
.list {
  margin-top: 24rpx;
  .list_item {
    padding: 24rpx;
    background: #fff;
    margin-bottom: 24rpx;
    border-radius: 12rpx;
    .left_img {
      width: 56rpx;
      height: 56rpx;
      margin-top: 8rpx;
      margin-right: 24rpx;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .left_center {
      flex: 1;
      .left_title {
        font-size: 40rpx;
        padding: 8rpx 0;
        color: #2e3c4e;
        font-weight: 400;
      }
      .left_bottom {
        font-size: 22rpx;
        margin-top: 24rpx;
        padding: 4rpx 0;

        /* flex-shrink: 0; */
        display: inline-block;
        border-radius: 4rpx;
        text {
          padding: 0 16rpx;
          border-radius: 4rpx;
        }
        &.tong {
          text {
            background: rgba(17, 208, 96, 0.3);
            color: #08c657;
          }
        }
        &.un_tong {
          text {
            color: #f53d39;
            background: rgba(245, 61, 57, 0.3);
          }
        }
        &.un_call {
          text {
            background: rgba(45, 132, 251, 0.3);
            color: #2d84fb;
          }
        }
      }
    }
  }
}
.p_con {
  padding: 40rpx;
  background: #fff;
  border-radius: 8rpx;
  position: relative;
  padding-top: 140rpx;
  width: 80vw;
  /* height: 50vh; */
  .title {
    font-size: 40rpx;
    font-weight: 600;
    padding: 40rpx 0;
    text-align: center;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    text-align: center;
  }
  .p_content {
    .p_item {
      .label {
        margin-right: 5rpx;
      }
    }
    .btns {
      margin-top: 200rpx;
      .btn {
        background: #2d84fb;
        padding: 30rpx 0;
        color: #fff;
        border-radius: 10rpx;
      }
    }
  }
}
::v-deep .uni-select__selector-scroll {
  max-height: 200rpx;
}
</style>