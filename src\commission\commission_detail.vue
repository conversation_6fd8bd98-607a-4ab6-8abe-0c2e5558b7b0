<template>
  <view class="list">
    <view class="card row">
      <view class="left com">
        <text class="title">成交</text>
        <text class="number">{{ user_withdraw.brokerage_total }}</text>
      </view>
      <view class="line"></view>
      <view class="right com">
        <text class="title">我的佣金</text>
        <text class="number">{{ user_withdraw.brokerage_balance_amount }}</text>
      </view>
    </view>
    <view class="btn" @click="$navigateTo('/commission/apply_take')">提现</view>
    <view class="cash_records" @click="cashRecoeds">提现记录</view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
export default {
  data() {
    return {
      user_withdraw: {},
    };
  },
  onLoad() {
    this.getWithdrawData();
  },
  methods: {
    cashRecoeds() {
      this.$navigateTo("/commission/cash_records");
    },
    getWithdrawData() {
      this.$ajax.get("/client/my/query/brokerage", {}, (res) => {
        if (res.statusCode === 200) {
          this.user_withdraw = res.data;
        } else {
          uni.showToast({
            title: res.data.message || "获取失败",
            icon: "none",
          });
        }
      });
    },
  },
  onPullDownRefresh() {
    this.getWithdrawData();
    uni.stopPullDownRefresh();
  },
};
</script>

<style scoped lang="scss">
.list {
  color: #fff;
  padding: 24rpx 48rpx;
  .card {
    align-items: center;
    justify-content: space-around;
    background: #0174ff;
    background-image: url("https://img.tfcs.cn/static/img/<EMAIL>");
    background-size: contain;
    background-repeat: no-repeat;
    box-shadow: 0 0 8px 0 rgba(1, 116, 255, 0.4);
    border-radius: 14px;
    height: 280rpx;
    .com {
      align-items: center;
      width: 200rpx;
      .title {
        font-size: 22rpx;
      }
      .number {
        margin-top: 20rpx;
        font-size: 64rpx;
      }
    }
    .line {
      height: 110rpx;
      width: 2rpx;
      background: #4c9dff;
    }
  }
  .btn {
    align-items: center;
    margin-top: 52rpx;
    padding: 30rpx;
    background: #0174ff;
    box-shadow: 0 4px 16px 0 rgba(1, 116, 255, 0.4);
    border-radius: 22px;
  }
  .cash_records {
    margin-top: 48rpx;
    align-items: center;
    font-size: 32rpx;
    color: #666;
  }
}
</style>
