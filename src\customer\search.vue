<template>
<view class="container">
	<view class="top">
		<view class="search-input-wrapper">
			<view class="search-input">
				<myIcon class="icon" type="ic_sousuo3x1" color="#bbb3b3" size="32rpx"></myIcon>
				<view class="search-input-view">
					<input type="text" confirm-type="search" v-model="keywords" @confirm="handelInputConfirm" focus placeholder-style="font-size:28rpx;color: rgba(41, 44, 57, 0.4)"
						placeholder="如：部门 成员 手机号 客户编号等" />
				</view>
			</view>
			<view class="search-cancle" @click="cancleSearch">取消</view>
		</view>
		<tabs v-model="searchType" :current="current" @change="handleTabChange"></tabs>
	</view>

	<view class="content">
		<view class="selector-btn-wrapper" v-if="isDepartmentSearch">
			<view class="selector-btn" @click="showDepartmentPicker">
				<view class="title">{{deptSelectedLable || '请选择部门'}}</view>
				<view @click.stop="cancleDeptSeled" v-if="department_id">
					<myIcon class="icon-close" type="guanbi" color="#bbb3b3" size="28rpx"></myIcon>
				</view>
				<view class="icon" v-else></view>
			</view>
		</view>
		<view class="selector-btn-wrapper" v-else-if="isAdminSearch">
			<view class="selector-btn" @click="showAdminPicker">
				<view class="title">{{adminSelectedLable || '请选择成员'}}</view>
				<view @click.stop="cancleAdminSeled" v-if="admin_id">
					<myIcon class="icon-close" type="guanbi" color="#bbb3b3" size="28rpx"></myIcon>
				</view>
				<view class="icon" v-else></view>
			</view>
		</view>

		<list :current="current" :params="params" :auto-load="false" ref="customerList" v-show="isSearched">
			<template #empty>
				<view class="search-tip">
					<image :src="'/yidongduan/customer/customer_search.png' | imgDomain" class="empty-icon"></image>
					<view class="empty-text">
						<text>{{ emptyText }}</text>
					</view>
				</view>
			</template>
		</list>
		
		<view class="search-tip" v-if="!isSearched">
			<image :src="'/yidongduan/customer/customer_search.png' | imgDomain" class="empty-icon"></image>
			<view class="empty-text">
				<text>{{ emptyText }}</text>
			</view>
		</view>
	</view>

	<tDepartmentPicker :visible.sync="dialogs.deptPicker" v-model="department_id" @confirm="confirmSeledDept"></tDepartmentPicker>
	<tMemberPicker :visible.sync="dialogs.adminPicker" :adminType.sync="admin_type" adminTypeSelect v-model="admin_id" @confirm="confirmSeledAdmin"></tMemberPicker>
</view>
</template>
<script>
import myIcon from "@/components/my-icon";
import tabs from './components/customer_search/tabs.vue';
import tDepartmentPicker from '@/components/tplus/tDepartmentPicker.vue';
import tMemberPicker from '@/components/tplus/tMemberPicker.vue';
import list from '@/customer/components/customer_list/list.vue';
export default {
	components: {
        myIcon, tabs, tDepartmentPicker, tMemberPicker, list
    },
	data() {
		return {
			current: 'my',
			keywords: '',				//搜索关键词
			searchType: '',  	//搜索类型
			searchValue: '',			//搜索值
			department_id: '',			//部门ID
			admin_type: 0,				//成员类型
			admin_id: '',				//成员ID
			deptSelectedLable: '',		//已选中的部门label
			adminSelectedLable: '',		//已选中的成员label
			params: {

			},
			extendParams: {},
			dialogs: {
				deptPicker: false,
				adminPicker: false
			}
		};
	},
	computed: {
		//是否input搜索
		isInputSearch(){
			return !this.isDepartmentSearch && !this.isAdminSearch;
		},
		//是否搜索部门
		isDepartmentSearch(){
			return this.searchType === 'department_id'
		},
		//是否搜索成员
		isAdminSearch(){
			return this.searchType === 'admin_id'
		},
		//是否搜索
		isSearched(){
			return !!this.searchValue;
		},
		emptyText(){
			if(this.isSearched){
				return '暂无数据';
			}
			if(this.isDepartmentSearch){
				return '请选择部门';
			}
			if(this.isAdminSearch){
				return '请选择成员';
			}
			return '请输入搜索内容';
		}
	},
	onLoad(options) {
		if(options.from == 'list'){
			let params = uni.getStorageSync('crmSearchParams');
			if(params){
				this.extendParams =  JSON.parse(params) || {};
				
			}
		}
		

		this.current = options.current || 'my';
		for(const name of ['mobile', 'cname', 'keywords', 'number']){
			let keywords = options[name] || '';
			if(keywords){
				this.searchType = name;
				this.searchValue = keywords;
				this.keywords = keywords;
				this.search();
				break;
			}
		}


		uni.$on("getDataAgain", () => {
			this.$refs.customerList && this.$refs.customerList.search();
		})
	},
	onUnload () {
		//uni.$off("getDataAgain")
	},
	methods: {
		search(){
			if(this.isSearched){
				const params = {};
				params[this.searchType] = this.searchValue;
				if(this.searchType == 'admin_id'){
					params.admin_type = this.admin_type
				}

				this.params = Object.assign(params, this.extendParams);
				this.$nextTick(()=>{
					this.$refs.customerList.search();
				})
			}
		},
		//input搜索
		handelInputConfirm(e){
			this.keywords = e.detail.value;
			if(this.isInputSearch){
				this.searchValue = this.keywords;
				this.search();
			}
		},
		//tab 切换
		handleTabChange(){
			if(this.isInputSearch){
				this.searchValue = this.keywords;
			}else if(this.isDepartmentSearch){
				this.searchValue = this.department_id || '';
			}else if(this.isAdminSearch){
				this.searchValue = this.admin_id || '';
			}
			this.search();
		},
		//取消搜索
		cancleSearch(){
			this.$navigateBack();
		},
		//显示部门选择
		showDepartmentPicker(){
			this.dialogs.deptPicker = true;
		},
		//显示成员选择
		showAdminPicker(){
			this.dialogs.adminPicker = true;
		},
		confirmSeledDept(data){
			this.searchValue = this.department_id;
			if(this.searchValue){
				this.deptSelectedLable = data.label.join('/');
				this.search();
			}else{
				this.deptSelectedLable = '';
			}
		},
		confirmSeledAdmin(data, adminTypeData){
			this.dialogs.adminPicker = false;
			this.searchValue = this.admin_id;
			if(this.searchValue){
				this.search();
				console.log(adminTypeData,data)
				this.adminSelectedLable = adminTypeData.label[0] + '/' + data.label[0];
			}else{
				this.adminSelectedLable = '';
			}
		},
		cancleDeptSeled(){
			this.department_id = '';
			this.deptSelectedLable = '';
			this.searchValue = '';
		},
		cancleAdminSeled(){
			this.admin_id = '';
			this.adminSelectedLable = '';
			this.searchValue = '';
		}
	},
	onReachBottom () {
		this.$refs.customerList.getList();
	},
    onBackRefresh(){
		this.$refs.customerList.search();
		this.$store.commit('onBackRefresh', true);
    }
};
</script>
<style lang="scss" scoped>
page {
	background: #f6f6f6;
}
.container{
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	.top{
		position: sticky;
		top: 0;
		z-index: 2;
	}
	.content{
		flex: 1;
		background: #f6f6f6;
		.selector-btn-wrapper{
			flex-direction: row;
			padding: 24rpx 36rpx 0;
			.selector-btn{
				display: inline-flex;
				flex-direction: row;
				align-items: center;
				height: 64rpx;
				padding: 0 24rpx;
				background-color: #fff;
				border-radius: 8rpx;
				max-width: 100%;
				.title{
					display: inline-block;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					font-size: 14px;
					color: rgba(41, 44, 57, 0.7);
				}
				.icon{
					width: 0;
					height: 0;
					margin: 4px 0 0 8px;
					border: 4px solid transparent;
					border-top-color: #b8b9ba;
				}
				.icon-close{
					padding-left: 16rpx;
				}
			}
		}
		.search-tip{
			padding-top: 20%;
			.empty-icon{
				width: 240rpx;
				height: 240rpx;
				margin: 0 auto;
			}
			.empty-text{
				color: rgba(41, 44, 57, 0.4);
				font-size: 32rpx;
				text-align: center;
			}
		}
	}
}

.search-input-wrapper{
	display: flex;
	flex-direction: row;
    height: 110rpx;
    padding: 24rpx 32rpx 8rpx;
    background-color: #fff;
    .search-input{
		flex: 1;
        height: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        background-color: #F6F6F6;
        border-radius: 16rpx;
        padding: 0 24rpx;
        .search-input-view{
			flex: 1;
			display: inline-block;
			padding-left: 16rpx;
		}
    }
	.search-cancle{
		display: flex;
		flex-direction: row;
		align-items: center;
		width: 80rpx;
		font-size: 32rpx;
		color: #2d84fb;
		margin-left: 20rpx;
	}
}
</style>