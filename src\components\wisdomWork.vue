<template>
    <view>
        <view class="whole"></view>
        <!-- 智慧经营 -->
        <!-- 本周 -->
        <view class="whole_con">
            <scroll-view class="weektop" scroll-x="true" @scroll="scroll">
                <view class="weekeds">
                    <view class="weeked" v-for="(tab, index) in navs" :key="tab.id" :id="tab.id"
                        :class="navIndex == index ? 'activite' : ''" @click="checkIndex(index)">{{ tab.name }}</view>
                </view>
            </scroll-view>
            <view class="change">
                <!-- 比例模块 -->
                <scroll-view class="weekbili" scroll-x="true" @scroll="scroll">
                    <view class="proportion_whole flex-row items-center">
                        <view class="proportion" v-for="item in censusList" :key="item.id">
                            <view>
                                <view class="header">
                                    <view>
                                        <image :src="item.app_images" style="width:64rpx;height:64rpx"></image>
                                    </view>
                                    <view class="name">{{ item.desc }}</view>
                                </view>
                                <view class="middle">{{ item.num }}</view>
                                <view class="foot">
                                    <view class="proportion_foot_left">占比：</view>
                                    <view class="proportion_foot_right">{{ (item.ratio * 100).toFixed(2) + "%" }}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <view class="trantions_top">
                <view class="whole_middle">团队成员</view>
                <view class="whole_input" @click="getElementScollTop">
                    <view class="whole_input_left">
                        <view class="whole_input_left_name">按部门</view>
                        <view>
                            <image src="../static/icon/index/xia.png" style="width:32rpx;height:32rpx;"></image>
                        </view>
                    </view>
                    <view class="whole_shu">|</view>
                    <view class="whole_bai">
                        请输入搜索内容
                        <!-- <view> <input placeholder="请输入搜索内容" placeholder-style="font-size:28rpx;color:#ACACAC;" type="text"></view> -->
                    </view>
                </view>
                <view style="margin-bottom: 200rpx;" class="index-con">
                    <view class="whole_list" v-for="item in dataList " :key="item.id">
                        <view class="whole_list_header">
                            <view>
                                <!-- <image src="../static/icon/index/客户-我录入的.png" style="width: 48rpx; height: 48rpx;"></image> -->
                                <view class="names">{{ item.user_name.split('')[0] }}</view>
                            </view>
                            <view class="whole_list_name">{{ item.user_name }}</view>
                            <view class="whole_list_department">{{ item.department }}</view>
                        </view>
                        <view class="whole_list_flex">
                            <view class="whole_list_footer">
                                <view class="whole_list_names">客户量</view>
                                <view class="whole_list_num">{{ item.khzl_num }}</view>
                            </view>
                            <view class="whole_list_footer">
                                <view class="whole_list_names">认领</view>
                                <view class="whole_list_num">{{ item.rl_num }}</view>
                            </view>
                            <view class="whole_list_footer">
                                <view class="whole_list_names">录入</view>
                                <view class="whole_list_num">{{ item.lr_num }}</view>
                            </view>
                            <view class="whole_list_footer">
                                <view class="whole_list_names">分配</view>
                                <view class="whole_list_num">{{ item.auto_rl_num }}</view>
                            </view>
                            <view class="whole_list_footer">
                                <view class="whole_list_names">回访</view>
                                <view class="whole_list_num">{{ item.hf_num }}</view>
                            </view>
                        </view>
                    </view>
                    <load-more :status="load_status"></load-more>
                    <!-- <view class="loading" >--加载到底--</view> -->
                    <!-- <view class="loading">加载中...</view> -->
                </view>
            </view>
            <!-- 部门弹框 -->
            <uni-popup ref="popup" type="bottom" class="popup">
                <uni-collapse ref="collapse" v-model="values" @change="change" accordion>
                    <uni-search-bar @input="search" v-model="keywords" placeholder="请输入部门名称">
                    </uni-search-bar>
                    <uni-collapse-item v-if="resValuie == ''" :title="item.name" v-for="item in departmentList"
                        :key="item.id">
                        <view class="content" v-for="son in item.subs" :key="son.id" @click="chengs(item.id, son.id)">
                            <text class="text">{{ son.name }}</text>
                        </view>
                    </uni-collapse-item>
                    <uni-list v-if="resValuie != ''">
                        <uni-list-item @click="idfn(item.id)" :clickable="true" :title="item.name" v-for="item in vagueList"
                            :key="item.id" />
                    </uni-list>
                </uni-collapse>
                <view style="height: 400rpx; background-color: #ffff;"></view>
            </uni-popup>
        </view>
    </view>
</template>
<script>
import loadMore from "@/components/loadMore.vue";
export default {
    components: {
        loadMore,
    },
    data() {
        return {
            keywords: '',
            values: [''],
            load_status: "",
            navIndex: 2, // 本周
            navs: [
                { id: 1, name: '全部' },
                { id: 2, name: "今天" },
                { id: 3, name: "昨天" },
                { id: 4, name: "本周" },
                { id: 5, name: "上周" },
                { id: 6, name: "本月" },
                { id: 7, name: "上月" },
            ],
            departmentList: [],
            proportionList: {
                page: 1,
                per_page: 10,
                date_type: '1',
                department_id: 0
            },
            censusList: [],// 客户卡片
            dataList: [],
            names: '',
            vagueList: [],
            icon: [
                { id: 1, url: '../static/icon/index/客户总量.png' },
                { id: 2, url: '../static/icon/index/有效客户.png' },
                { id: 3, url: '../static/icon/index/A级客户.png' },
                { id: 4, url: '../static/icon/index/B级客户.png' },
                { id: 5, url: '../static/icon/index/C级客户.png' },
            ],
            resValuie: ''
        };
    },
    watch: {
        navIndex: {
            handler(nval) {
                // console.log(nval+1,'tepy');
                this.proportionList.date_type = nval + 1
                this.getworkList()
                this.getworkLister()
            },
            immediate: true
        }
    },
    created() {
        uni.showLoading({
            title: "加载中",
        })
        //  this.getworkLister()
    },
    methods: {
        chengs(item, son) {
            console.log(item, son, '222');
            if (item) {
                this.proportionList.department_id = son
                this.getworkList()
                this.getworkLister()
            }
            this.$refs.popup.close()

        },
        // 搜索id自动检索
        idfn(id) {
            console.log(1111);
            this.proportionList.department_id = id
            this.getworkList()
            this.getworkLister()
            this.$refs.popup.close()
            if (this.keywords != '') {
                this.keywords = ''
            }
        },
        // 按部门
        search(res) {
            console.log(res, 'ss');
            this.$ajax.get('/admin/personnelMatters/departmentByName', { keywords: this.keywords }, (res) => {
                console.log(res.data, '部门2222');
                if (res.statusCode === 200) {
                    this.vagueList = res.data
                }

            })
            this.resValuie = res
            console.log(this.resValuie, 'this.resValuie');
        },
        change(e) {
            console.log(e, 'eeee');
        },
        //获取元素离页面顶部的距离
        getElementScollTop() {

            // 通过组件定义的ref调用uni-popup方法 ,如果传入参数 ，type 属性将失效 ，仅支持 ['top','left','bottom','right','center']
            this.$refs.popup.open('bottom')
            this.$ajax.get('/qywx/department/list', {}, (res) => {
                console.log(res.data, '部门');
                this.departmentList = res.data

            })
            const query = uni.createSelectorQuery()
            query
                .select('.index-con')
                .boundingClientRect((data) => {
                    //   console.log(data, '1111')
                    let pageScrollTop = Math.round(data.top)
                    console.log(pageScrollTop, '头部');
                    uni.pageScrollTo({
                        scrollTop: 360, //滚动的距离
                        duration: 0, //过渡时间
                    })
                })
                .exec()
        },
        // tab互斥效果
        changeAct(item) {
            // console.log(item.id);
            // 激活样式是当前点击的对应下标--list中对应id
            this.act = item.id;
        },
        scroll: function (e) {
            // console.log(e)
            // this.old.scrollTop = e.detail.scrollTop
        },
        checkIndex(index) {
            // console.log(index, '000')
            this.navIndex = index;
            // this.getworkLister()
        },
        // 客户卡片
        getworkList() {
            this.load_status = "loading";
            this.$ajax.get('/qywx/operate/census', { date_type: this.proportionList.date_type }, (res) => {
                this.load_status = "loadend";
                // console.log(res.data.census, '比例');
                console.log(res.data, 'data12345');
                // console.log(res.data.header, 'header');
                if (res.statusCode === 200) {
                    this.censusList = res.data
                    // this.dataList = res.data.data
                    this.load_status = 'nomore'
                    // console.log(this.censusList, 'push');
                    uni.hideLoading()
                } else {
                    uni.hideLoading()
                }
            },
                () => {
                    uni.hideLoading()
                }
            )
        },
        // 客户列表
        getworkLister() {
            this.load_status = "loading";
            this.$ajax.get('/qywx/operate/search', this.proportionList, (res) => {
                this.load_status = "loadend";
                // console.log(res.data.census, '比例');
                console.log(res.data.data, 'data99999');
                // console.log(res.data.header, 'header');
                if (res.statusCode === 200) {
                    // this.censusList = res.data
                    this.dataList = res.data.data
                    this.load_status = 'nomore'
                    // console.log(this.censusList, 'push');
                    uni.hideLoading()
                } else {
                    uni.hideLoading()
                }
            },
                () => {
                    uni.hideLoading()
                }
            )
        }
    },
};
</script>
<style lang="scss">
.loading {
    text-align: center;
    margin-top: 30rpx;
    color: rgb(138, 138, 138);
    font-size: 25rpx;
}

.popup {
    background: #fff;
}

.example-body {
    flex-direction: column;
    flex: 1;
}

.content {
    padding: 15px;
    background-color: #F6F6F6;
}

.text {
    font-size: 14px;
    color: #666;
    line-height: 20px;
}

@mixin flex {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
}

@mixin height {
    /* #ifndef APP-NVUE */
    height: 100%;
    /* #endif */
    /* #ifdef APP-NVUE */
    flex: 1;
    /* #endif */
}

.box {
    @include flex;
}

.button {
    @include flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    height: 35px;
    margin: 0 5px;
    border-radius: 5px;
}

.example-body {
    background-color: #fff;
    padding: 10px 0;
}

.button-text {
    color: #fff;
    font-size: 12px;
}

.popup-content {
    @include flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    height: 50px;
    background-color: #fff;
}

.popup-height {
    @include height;
    width: 200px;
}

.text {
    font-size: 12px;
    color: #333;
}

.popup-success {
    color: #fff;
    background-color: #e1f3d8;
}

.popup-warn {
    color: #fff;
    background-color: #faecd8;
}

.popup-error {
    color: #fff;
    background-color: #fde2e2;
}

.popup-info {
    color: #fff;
    background-color: #f2f6fc;
}

.success-text {
    color: #09bb07;
}

.warn-text {
    color: #e6a23c;
}

.error-text {
    color: #f56c6c;
}

.info-text {
    color: #909399;
}

.dialog,
.share {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: column;
}

.dialog-box {
    padding: 10px;
}

.dialog .button,
.share .button {
    /* #ifndef APP-NVUE */
    width: 100%;
    /* #endif */
    margin: 0;
    margin-top: 10px;
    padding: 3px 0;
    flex: 1;
}

.dialog-text {
    font-size: 14px;
    color: #333;
}

.trantions_top {
    height: 100%;
    padding: 0 32rpx;
}

.whole_shu {
    color: rgba(41, 44, 57, 0.70);
    font-size: 24rpx;
    font-weight: 400;
    line-height: 28rpx;

}

.whole_bai {
    width: 70%;
    height: 32rpx;
    line-height: 32rpx;
    color: rgba(41, 44, 57, 0.40);
}

.whole_input_left_name {
    color: rgba(41, 44, 57, 0.70);
    font-size: 28rpx;
    font-weight: 400;
    line-height: 32rpx;
    margin-right: 16rpx;
}

.whole_input_left {
    display: flex;
    flex-direction: row;

}

.whole_con {
    // padding: 32rpx;
    padding-top: 32rpx;
    margin-top: -312rpx;
    // padding-left: 32rpx;
}

.names {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
    text-align: center;
    line-height: 48rpx;
    color: #fff;
    font-size: 20rpx;
    background: #488AF6;
}

.whole_list_flex {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.whole_list_num {
    color: #000;
    margin-top: 16rpx;
    font-size: 32rpx;
    text-align: center;
    font-weight: 500;
}

.whole_list_names {
    color: rgba(41, 44, 57, 0.40);
    font-size: 24rpx;
    font-weight: 400;
}

.whole_list_department {
    padding: 8rpx 16rpx;
    background: rgba(72, 138, 246, 0.20);
    color: #488AF6;
    text-align: center;
    border-radius: 8rpx;
    font-size: 24rpx;
    font-weight: 400;
}

.whole_list {
    width: 100%;
    padding: 32rpx;
    margin: 32rpx 0 0;
    background: #fff;
    border-radius: 16rpx;
}

.whole_list_name {
    color: #292C39;
    font-size: 32rpx;
    font-weight: 500;
    margin: 0 24rpx;
}

.whole_list_header {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
    margin-bottom: 32rpx;
}

.whole_input {
    width: 100%s;
    // height: 80rpx;
    padding: 24rpx;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    // margin: 0 16rpx;
    background-color: #fff;
    border-radius: 16rpx;
}

.whole_middle {
    color: rgba(41, 44, 57, 0.70);
    font-size: 28rpx;
    font-weight: 400;
    line-height: 36rpx;
    margin: 32rpx 0;
}

.proportion_whole {
    width: 1722rpx;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding-left: 32rpx;
}

page {
    background: #F6F6F6;
}

.proportion_foot_right {
    color: #292C39;
    font-size: 24rpx;
    font-weight: 400;
}

.foot {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
}

.proportion_foot_left {
    color: rgba(41, 44, 57, 0.70);
    font-size: 22rpx;
    font-weight: 400;
    line-height: 32rpx;
}

.middle {
    color: #292C39;
    font-size: 40rpx;
    font-weight: 500;
    margin: 28rpx 0;
}

.proportion {
    padding: 24rpx;
    width: 300rpx;
    background: #fff;
    border-radius: 16rpx;
    margin-right: 32rpx;
    margin-top: 32rpx;

    &.proportion:last-child {
        margin-right: 0;
    }
}

.name {
    margin-left: 16rpx;
    color: rgba(41, 44, 57, 0.70);
    font-size: 24rpx;
    font-weight: 400;
}

.header {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
}

.whole {
    width: 100%;
    height: 286rpx;
    background: linear-gradient(180deg, rgba(99, 165, 255, 0.40) 0%, rgba(255, 255, 255, 0.00) 100%);
}

.weektop {
    // margin-left: 10rpx;
    // width: 25%;
    margin-top: 30rpx;
    white-space: nowrap;
    text-align: center;
    // padding-left:  32rpx;
}

.weekbili {
    white-space: nowrap;
}

// 本周
.weeked {
    box-sizing: border-box;
    padding: 14rpx 24rpx;
    border-radius: 16rpx;
    margin-right: 24rpx;
    font-size: 24rpx;
    color: rgba(41, 44, 57, 0.40);
    background: #f8f8f8;
    display: inline-block;

    // padding-left: 32rpx;
    /* 必要，导航栏才能横向*/
    &.weeked:last-child {
        margin-right: 0;
    }
}

.weekeds {
    padding-left: 32rpx;
    // background: #f8f8f8;
    display: inline-block;
}

.activite {
    box-sizing: border-box;
    padding: 14rpx 24rpx;
    border-radius: 16rpx;
    color: #fff;
    font-size: 24rpx;
    background: #488AF6
}
</style>