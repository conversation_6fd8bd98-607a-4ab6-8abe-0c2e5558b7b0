<template>
  <view class="list">
    <svg
      class="iconfont"
      :aria-hidden="hidden"
      :style="{
        width: width,
        height: height,
        color: color,
      }"
      @click="$emit('click')"
    >
      <use :xlink:href="'#icon-' + type"></use>
    </svg>
  </view>
</template>

<script>
export default {
  props: {
    hidden: {
      type: Boolean,
      default: true,
    },
    type: {
      type: String,
      default: "",
    },
    width: {
      type: String,
      default: "32rpx",
    },
    height: {
      type: String,
      default: "32rpx",
    },
    color: {
      type: String,
      default: "#000",
    },
  },
};
</script>

<style></style>
