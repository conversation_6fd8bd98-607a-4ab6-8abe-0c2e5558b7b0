<template>
    <tMemberPicker v-model="admin_id" multiple 
        :visible.sync="show" :submiting="submiting" :filterId="selfUid"
        @confirm="handleConfirm">
    </tMemberPicker>
</template>
<script>
import tMemberPicker from '@/components/tplus/tMemberPicker';
export default {
    props: {
        visible: { type: Boolean, default: false },
        customerId: { type: [String, Number], default: '' },
    },
    components: {
        tMemberPicker
    },
    data(){
        return {
            show: false,
            submiting: false,
            selfUid: 0,
            admin_id: []
        }
    },
    watch: {
        visible(val){
            this.show = val;
        },
        show(val){
            val != this.visible && this.$emit('update:visible', val)
        }
    },
    created(){
        const userInfo = uni.getStorageSync("userInfo") ? JSON.parse(uni.getStorageSync("userInfo")) : {};
        this.selfUid = userInfo.id || 0;
    },
    methods: {
        async handleConfirm(data){
            if (process.env.NODE_ENV === 'development') {
                this.show = false;
                uni.showToast({
                    title: '开发模式下流转客复制给同事测试',
                    icon: 'none',
                });
                this.$emit('success');
                return;
            }
            if(this.admin_id.length == 0){
                uni.showToast({
                    title: '请选择要复制的同事',
                    icon: 'none',
                });
                return;
            }

            this.submiting = true;
            try{
                const data = await this.postCopyToMember(this.customerId, this.admin_id.join(','))
                this.show = false;
                uni.showToast({
                    title: data && data.msg ? data.msg : '复制给同事成功',
                    icon: 'none',
                });
                this.$emit('success');
            }catch(e){}
            this.submiting = false;
        },
        postCopyToMember(id, user_ids){
            return new Promise((resolve, reject) => {
                this.$ajax.post('/admin/private_client/copy_to_colleague', {ids: String(id), user_ids}, res => {
                    if (res.statusCode == 200) {
                        resolve(res.data);
                    }else{
                        uni.showToast({
                            title: res?.data?.message || '复制给同事失败',
                            icon: 'none'
                        });
                    }
                    reject();
                }, er => {
                    reject();
                })
            })
        }
    }
}


</script>
<style lang="scss" scoped>
</style>