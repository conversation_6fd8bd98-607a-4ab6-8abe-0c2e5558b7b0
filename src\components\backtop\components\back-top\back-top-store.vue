<template>
  <view
      v-if="scrollTop > 400"
      class="backTop"
      :class="{ 'mescroll-fade-in': isShowToTop }"
      @click="toTopClick"
      :style="{ zIndex: zIndex }"
  >
  <image :src="src ? src : defaultImage" mode="widthFix" />
  </view>
</template>

<script>
import { mapState } from 'vuex';
const requireContext = require.context('../../static/back-top', false, /\.png$/);
const defaultImage = requireContext('./top.png');
export default {
  name: "backTopStore",
  props: {
    src: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
    tab: {
      type: Boolean,
      default: false,
    },
    zIndex: {
      type: Number,
      default: 999,
    },
  },
  computed: {
    ...mapState(['scrollTop']),
  },
  data() {
    return {
      isShowToTop: true,
      defaultImage
    };
  },
  mounted() {
    console.log('Default image path:', this.defaultImage);
  },
  methods: {
    toTopClick() {
      this.isShowToTop = false; // 回到顶部按钮需要先隐藏,再执行回到顶部,避免闪动
      if (this.tab) {
        this.$emit("setScrollTop");
      } else {
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 300,
        });
      }
    },
  },
};
</script>

<style>
.mescroll-lazy-in,
.mescroll-fade-in {
  -webkit-animation: mescrollFadeIn 0.3s linear forwards;
  animation: mescrollFadeIn 0.3s linear forwards;
}

.backTop {
  position: fixed;
  right: 63upx;
  bottom: 120upx;
  /* #ifdef H5 */
  bottom: 220upx;
  /* #endif */
  width: 72upx;
  height: 72upx;
  border-radius: 50%;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

.backTop image {
  width: 100%;
  height: 100%;
}
</style>
