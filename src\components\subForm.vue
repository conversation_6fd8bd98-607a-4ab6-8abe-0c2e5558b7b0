<template>
  <view>
    <view class="sub_box" id="sub_box">
      <view class="sub_header">
        <!-- <view class="sub_title">{{ sub_title }}</view>
          <view class="sub_tip" v-if="groupCount"
            >已有<text>{{ groupCount }}</text
            >人订阅该楼盘</view
          > -->
        <image
          class="icon"
          mode="widthFix"
          src="https://img.tfcs.cn/static/img/baoming_kp.png"
        ></image>
      </view>
      <view class="form_box">
        <!-- <view class="sub_content">{{ sub_content }}</view> -->
        <view class="sub_form">
          <input
            class="sub_tel"
            v-if="sub_mode !== 1"
            maxlength="10"
            type="text"
            placeholder="称呼"
            v-model="form_customer.name"
          />
          <input
            class="sub_tel"
            maxlength="11"
            type="number"
            placeholder="手机号"
            v-model="form_customer.phone"
          />
          <view class="btn-box">
            <view class="button" @click="subData()">提交预约</view>
            <view class="close_btn" @click="closeSub()">取消</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from "./my-icon.vue";
export default {
  data() {
    return {
      sub_box_height: "initial",
      form_customer: {
        build_id: "",
        name: "",
        phone: "",
      },
    };
  },
  props: {
    sub_type: {
      type: [Number],
      default: 0,
    },
    sub_mode: {
      // 0:系统默认姓名和手机号，不需要引导登录；1:简约模式，仅需要手机号，不需要引导登录； 2:引导登录模式，姓名手机号，需要验证手机号
      type: [Number],
      default: 0,
    },
    build_id: [String, Number],
  },
  computed: {},
  components: {
    myIcon,
  },
  methods: {
    closeSub() {
      this.$emit("close");
    },
    subData() {
      this.form_customer.build_id = this.build_id;
      this.$emit("subdata", this.form_customer);
    },
  },
};
</script>

<style scoped lang="scss">
.sub_box {
  background-color: #fff;
  margin: 0 40rpx;
  border-radius: 16rpx;
  position: relative;
  margin-bottom: 400rpx;
  //   overflow-y: hidden;
  //   margin-top: 400rpx;
  .sub_header {
    padding: 24rpx 48rpx;
    color: #fff;
    background-image: linear-gradient(-41deg, #f7918f 0%, #fb656a 100%);
    position: relative;
    border-top-left-radius: 16rpx;
    border-top-right-radius: 16rpx;
    .sub_title {
      margin-bottom: 16rpx;
      font-size: 40rpx;
      font-weight: bold;
    }
    .sub_tip {
      font-size: 24rpx;
    }
    .icon {
      width: 150rpx;
      height: 150rpx;
      position: absolute;
      top: -8rpx;
      right: 48rpx;
    }
  }
  .form_box {
    padding: 30rpx 48rpx;
  }
  .sub_content {
    font-size: 32rpx;
    line-height: 1.5;
    color: #333;
  }
  .sub_form {
    margin-top: 25rpx;
    .sms_code_inp {
      align-items: center;
    }
    .sub_tel {
      margin-bottom: 20rpx;
      height: 80rpx;
    }
    input {
      padding: 20rpx;
      font-size: 28rpx;
      background-color: #f5f5f5;
    }
    .send-code {
      margin-left: 10rpx;
      color: $uni-color-primary;
      &.disable {
        color: #888;
      }
    }
    .btn-box {
      padding: 10px 0 0 0;
      .button {
        align-items: center;
        color: #fff;
        font-size: 34rpx;
        font-weight: bold;
        height: 88rpx;
        line-height: 88rpx;
        background: #fb656a;
        box-shadow: 0 4px 16px 0 rgba(251, 101, 106, 0.4);
        border-radius: 44rpx;
      }
      .close_btn {
        padding: 24rpx;
        text-align: center;
        color: #999;
      }
    }
  }
  .verify_block {
    position: absolute;
    left: 0;
    right: 0;
    top: 160rpx;
    bottom: 40rpx;
    background-color: #fff;
    z-index: 2;
  }
}
</style>
