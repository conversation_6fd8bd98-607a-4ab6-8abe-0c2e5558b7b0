<template>
    <view>
        <view class="bar-filter" @click="openFilter">
            <view class="bar-filter-title">{{ seledTitle }}</view>
            <image class="bar-filter-icon" :src="'/icons/mini-arrow.png' | imgDomain"></image>
        </view>

        <myPopup :show="show" @close="show = false">
            <view class="container">
                <view class="header">
                    <view class="action cancle" @click.stop="cancle">取消</view>
                    <view class="title"></view>
                    <view class="action confirm" @click.stop="confirm">确认</view>
                </view>
                <view class="body">
                    <view class="filter-list">
                        <view class="filter-list-item" :class="{active: seledValue===item.value}" v-for="(item, index) in options" :key="index" @click="onItemClick(item)">{{ item.title }}</view>
                    </view>
                </view>
            </view>
        </myPopup>
    </view>
</template>
<script>
import myPopup from '@/components/myPopup';
import moment from "moment";
export default {
    props: {
        value: { type: String, default: ''},
        options: { type: Array, default: ()=>[
            { title: '全部', value: '' },
            { title: '今天', value: 'today' },
            { title: '昨天', value: 'yestoday' },
            { title: '本周', value: 'now_week' },
            { title: '上周', value: 'last_week' },
            { title: '本月', value: 'now_month' },
            { title: '上月', value: 'last_month' },
            /* { title: '自定义', value: 'custom' }, */
        ]},
        dates : { type: Array, default: ()=> [] },
        format: { type: String, default: 'YYYY-MM-DD HH:ii:ss'},
    },
    components: {
        myPopup
    },
    data(){
        return {
            show: false,
            seledValue: '',
            datetimerange: []
        }
    },
    computed: {
        seledTitle(){
            const item = this.options.find(e => e.value === this.value)
            return item ? item.title : '全部'
        }
    },
    watch: {
        show(val){
            if(val){
                this.seledValue = this.value
            }
        },
        value: {
            handler(val){
                if(val != 'custom'){
                    this.$emit('update:dates', this.$Utils.getDateRange(val));
                }
            },
            immediate: true
        }
    },
    methods: {
        openFilter(){
            this.show = true
        },
        onItemClick({ value }){
            this.seledValue = value;
        },
        cancle(){
            this.show = false;
        },
        confirm(){
            this.show = false;
            this.$emit('input', this.seledValue);
            this.$nextTick(()=>{
                this.$emit('filter');
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.bar-filter{
    display: flex;
    flex-direction: row;
    align-items: center;
    color: #488AF6;
    font-size: 24rpx;
    .bar-filter-title{
        flex: 1;
        padding-right: 16rpx;
        text-align: right;
        word-break: break-all;
    }
    .bar-filter-icon{
        height: 6px;
        width: 4px;
    }
}

.container{
    background-color: #fff;
    
    line-height: 1;
    
    .header{
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 46px;
        :after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            height: 1px;
            clear: both;
            border-bottom: 1px solid #e5e5e5;
            color: #e5e5e5;
            -webkit-transform-origin: 0 100%;
            transform-origin: 0 100%;
            -webkit-transform: scaleY(.5);
            transform: scaleY(.5);
        }
        .title{
            flex: 1;
            color: #999;
            text-align: center;
            display: inline-block;
            max-width: 50%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .action{
            padding: 0 14px;
            font-size: 17px;
            &.cancle{
                color: #888;
            }
            &.confirm{
                color: #007aff;
            }
        }
    }
    .body{
        padding: 32rpx 8rpx 32rpx 32rpx;
    }
}
.filter-list{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .filter-list-item{
        display: flex;
        align-items: center;
        justify-content: center;
        height: 76rpx;
        font-size: 28rpx;
        color: #4E5969;
        background-color: #f6f6f6;
        min-width: calc( (100vw - 112rpx) / 3);
        margin: 0 24rpx 24rpx 0;
        padding: 0 4rpx;
        &.active{
            color: #2d84fb;
            background-color: #e5eeff;
        }
    }
}
</style>