<template>
    <view>
        <view class="search-select" @click="open">
            <view class="select-label" :class="{placeholder: !isSeled}">{{ title }}</view>

            <view class="icon"  @click.stop="clear" v-if="isSeled && clearable">
                <icons class="icon-icon" type="guanbi" size="24rpx" color="#696a6c"></icons>
            </view>
            <view class="icon" v-else>
                <icons class="icon-icon icon-select" type="jinrujiantou" size="24rpx" color="#696a6c"></icons>
            </view>
        </view>

        <wisdomSelectPopup ref="popup" v-if="autoPopup" :datas="datas" :params.sync="dataParams" :visible.sync="visible" @confirm="confirm"></wisdomSelectPopup>
    </view>
</template>

<script>
import icons from '@/components/my-icon';
import wisdomSelectPopup from './wisdomSelectPopup';
export default {
    props: {
        value: { type: [ String, Number, Boolean ], default: false },
        placeholder: { type: String, default: '请选择' },
        label: { type: String, default: '' },
        datas: { type: Array, default: () => [] },
        params: { type: Object, default: () => ({}) },
        autoPopup: { type: Boolean, default: false },
        clearable: { type: Boolean, default: true },
    },
    components: {
        icons,
        wisdomSelectPopup
    },
    data(){
        return {
            visible: false,
            dataParams: {}
        }
    },
    computed: {
        isSeled(){
            return !!this.value;
        },
        seledLabel(){
            for(const key in this.dataParams){
                let value = this.dataParams[key];
                if( value!== ''){
                    let group = this.datas.find(item => item.field === key);
                    if(group && Array.isArray(group.options)){
                        let option = group.options.find(item => item.value === value);
                        if(option && option.label){
                            return option.label;
                        }
                    }
                }
            }
            return '';
        },
        title(){
            return this.seledLabel || (this.isSeled ? this.label : this.placeholder) || this.placeholder
        }
    },
    watch: {
        datas:{
            handler(val){
                this.groups = val.map(e => {
                    let value = this.params[e.field] ?? '';
                    return {...e, value };
                })
            },
            immediate: true
        },
        params: {
            handler(val){
                const params = {};
                for(const item of this.datas){
                    params[item.field] = val[item.field] ?? '';
                }
                if(JSON.stringify(this.dataParams) !== JSON.stringify(params)){
                    this.dataParams = params;
                }
            },
            immediate: true,
            deep: true
        },
        dataParams(val){
            this.$emit('update:params', {...this.params, ...val})
        }
    },
    methods: {
        open(){
            this.visible = true;
            this.$emit('open');
        },
        clear(){
            if(this.autoPopup){
                const params = {};
                for(const key in this.dataParams){
                    let item = this.datas.find(group=> group.field === key);
                    if(item && item.default !== undefined){
                        params[key] = item.default;
                    }else{
                        params[key] = '';
                    }
                }
                this.dataParams = params;
                this.$refs.popup.clear();
            }
            this.$nextTick(()=>{
                this.$emit('clear')
            })
        },
        confirm(data){
            this.$nextTick(()=>{
                this.$emit('confirm', data)
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.search-select{
    display: flex;
    flex-direction: row;
    align-items: baseline;
    .placeholder{
        color: rgba(41, 44, 57, 0.7);
    }
    .icon{
        padding-left: 12rpx;
        justify-content: center;
        align-items: center;
        .icon-icon{
            font-weight: 600!important;
        }
        .icon-select{
            transform: rotate(90deg);
        }
    }
    .select-label{
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
}

.popup-container{
    background-color: #fff;
    line-height: 1;
    .header{
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 46px;
        :after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            height: 1px;
            clear: both;
            border-bottom: 1px solid #e5e5e5;
            color: #e5e5e5;
            -webkit-transform-origin: 0 100%;
            transform-origin: 0 100%;
            -webkit-transform: scaleY(.5);
            transform: scaleY(.5);
        }
        .title{
            flex: 1;
            color: #999;
            text-align: center;
            display: inline-block;
            max-width: 50%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .action{
            padding: 0 14px;
            font-size: 17px;
            &.cancle{
                color: #888;
            }
            &.confirm{
                color: #007aff;
            }
        }
    }
    .body{
        padding: 32rpx 8rpx 32rpx 32rpx;
        .filter-list{
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            .filter-list-item{
                display: flex;
                align-items: center;
                justify-content: center;
                height: 76rpx;
                font-size: 28rpx;
                color: #4E5969;
                background-color: #f6f6f6;
                min-width: calc( (100vw - 112rpx) / 3);
                margin: 0 24rpx 24rpx 0;
                padding: 0 4rpx;
                &.active{
                    color: #2d84fb;
                    background-color: #e5eeff;
                }
            }
        }
    }
}
</style>