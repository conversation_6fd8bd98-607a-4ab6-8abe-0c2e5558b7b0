<template>
<view class="cont" :style="{'--rows':rows}" :class="{'ai-call': isAiCall}">
    <view v-if="isAiCall" class="ai-header">
        <view class="ai-icon-container">
            <view class="ai-icon">AI</view>
            <text class="ai-title">智能总结</text>
        </view>
    </view>
    <view class="overflow-container" :class="{'ai-container': isAiCall}">
        <view class="overflow" :class="{expand: isExpand, 'ai-content': isAiCall}">
            <text class="text">
                <slot></slot>
            </text>
        </view>
    </view>
    <view v-if="isAiCall && (!isOvered || (isOvered && isExpand))" class="ai-footer">
        以上内容由AI大模型智能生成
    </view>
    <view v-if="isSession" class="session-btn" @click="viewSession">
        查看会话
    </view>
    <view class="btn" @click="toggleExpand" v-if="isOvered && !isSession">
        <template v-if="isExpand">收起</template>
        <template v-else>展开</template>
        <myIcon :type="isExpand ? 'shangla' : 'xiala'" size="24rpx" color="#488AF6" class="right-icon"></myIcon>
    </view>
</view>
</template>
<script>
import myIcon from "@/components/my-icon";
export default {
    props: {
        rows: { type: Number, default: 3 },
        isAiCall: { type: Boolean, default: false },
        isSession: { type: Boolean, default: false },
    },
    components: {
        myIcon
    },
    data(){
        return {
            isOvered: false,
            isExpand: false
        }
    },
    mounted(){
        this.checkOverflow();
    },
    updated() {
        this.checkOverflow();
    },
    methods: {
        checkOverflow() {
            // 如果已经展开，则保持展开/收起按钮可见
            if (this.isExpand) {
                this.isOvered = true;
                return;
            }

            const queryCont = uni.createSelectorQuery().in(this);
            const queryText = uni.createSelectorQuery().in(this);
            setTimeout(()=>{
                queryCont.select('.overflow').boundingClientRect(e => {
                    if (!e) return;
                    const height = e.height;
                    queryText.select('.text').boundingClientRect(e => {
                        if (!e) return;
                        if(e.height > height){
                            this.isOvered = true;
                        } else {
                            this.isOvered = false;
                        }
                    }).exec();
                }).exec();
            }, 100)
        },
        toggleExpand(){
            this.isExpand = !this.isExpand;
            // 确保展开/收起按钮始终可见
            this.isOvered = true;
        },
        viewSession() {
            this.$emit('viewSession');
        }
    }
}
</script>
<style lang="scss" scoped>
$rows: var(--rows, 3);
.overflow-container {
    &.ai-container {
        background-color: #f9f9f9;
        border-radius: 8rpx;
        margin-top: 10rpx;
        padding: 20rpx;
    }
}

.overflow{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $rows;
    line-clamp: $rows; /* 标准属性，增加兼容性 */

    &.expand{
        overflow: visible;
        display: inline-block;
        -webkit-line-clamp: initial;
        line-clamp: initial;
    }

    &.ai-content {
        font-size: 28rpx;
        line-height: 1.8;
        word-break: break-all;
    }
}
.btn{
    color: #488AF6;
    font-size: 30rpx;
    display: inline-block;
    .right-icon{
        margin-left: 10rpx;
    }
}

.session-btn {
    color: #488AF6;
    font-size: 30rpx;
    display: inline-block;
    margin-top: 10rpx;
}

.ai-call {
    .ai-header {
        margin-bottom: 10rpx;
    }

    .ai-icon-container {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-direction: row;
    }

    .ai-icon {
        width: 40rpx;
        height: 40rpx;
        border: 2px solid #F85D02;
        border-radius: 50%;
        color: #F85D02;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        font-weight: bold;
        margin-right: 10rpx;
    }

    .ai-title {
        color: #333;
        font-size: 30rpx;
        font-weight: bold;
    }

    .ai-footer {
        font-size: 24rpx;
        color: #999;
        text-align: left;
        margin-top: 10rpx;
        padding-left: 10rpx;
        font-style: italic;
    }
}
</style>