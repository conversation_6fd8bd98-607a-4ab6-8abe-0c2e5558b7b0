<template>
  <view class="list">
    <view class="title-box row">
      <view class="left row">
        <view class="left-txt row">
          <view class="txt">客户名称：{{ client_detail.customer_name }}</view>
          <view class="txt">{{ client_detail.customer_phone }}</view
          ><view
            class="right"
            @click="callTel"
            v-if="isPhone"
            style="margin-left:30rpx"
          >
            <myIcon type="zixun" color="#fff" size="50rpx"> </myIcon>
          </view>
        </view>
        <view class="txt">项目名称：{{ client_detail.build_name }}</view>
        <view class="left-txt row">
          <view class="txt">领取助理：{{ client_detail.pu_name }}</view>
          <view class="txt">{{ client_detail.pu_phone }}</view>
          <view
            class="right"
            @click="callTelProject(client_detail.pu_phone)"
            v-if="client_detail.pu_phone"
            style="margin-left:30rpx"
          >
            <myIcon type="zixun" color="#fff" size="50rpx"> </myIcon>
          </view>
        </view>
      </view>
    </view>
    <view class="report-status">
      <mySteps
        :options="steps_list"
        :active="client_detail.status"
        active-color="#6783f7"
      ></mySteps>
    </view>
    <view class="report-list">
      <!-- 导航按钮 -->
      <view class="nav">
        <view
          class="nav-item"
          v-for="(nav, index) in click_navs"
          :key="nav.type"
          :class="{ active: nav.type == data_type }"
          @click="onClickNav(index)"
          >{{ nav.name }}</view
        >
      </view>
      <view v-if="data_type == 1" class="detail top">
        <view class="box row">
          <text class="label">预期来访</text>
          <text class="label-ctn">{{
            client_detail.visit_time ? client_detail.visit_time : "无"
          }}</text>
        </view>
        <view class="box row">
          <text class="label">来访方式</text>
          <text class="label-ctn">{{
            client_detail.visit_category ? client_detail.visit_category : "无"
          }}</text>
        </view>
        <view class="box row">
          <text class="label">来访人数</text>
          <text class="label-ctn">{{
            client_detail.visit_people ? client_detail.visit_people : "无"
          }}</text>
        </view>
        <view class="box row">
          <text class="label">报备备注</text>
          <text class="label-ctn">{{
            client_detail.remark ? client_detail.remark : "无"
          }}</text>
        </view>
        <view class="box row" v-if="client_detail.cancel_reason">
          <text class="label">无效原因</text>
          <text class="label-ctn">{{ client_detail.cancel_reason }}</text>
        </view>
      </view>
      <view v-if="data_type == 2">
        <colStep
          @scrollToLower="scrollToLower"
          :stepList="steps_recording"
          :loadStatus="load_status"
        ></colStep>
      </view>
      <view v-if="data_type == 3">
        <view class="img_row row" v-if="img_list.length > 0">
          <image
            v-for="item in img_list"
            :key="item.id"
            :src="item.file"
            @click="$previewImage(item.file)"
          ></image>
        </view>
        <upload
          :imgShow="false"
          :del="del_img"
          :imageList="info_imgs"
          :upload_category="9"
        ></upload>
      </view>
    </view>
    <view class="btn-box">
      <view class="btn" @click="addSchedule">{{ btn_txt }}</view>
    </view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import mySteps from "@/components/uni-steps/uni-steps/uni-steps";
import myIcon from "@/components/my-icon";
import upload from "@/components/upload";
import colStep from "@/components/my-step";
import { mapState, mapActions } from "vuex";
export default {
  components: {
    mySteps,
    myIcon,
    upload,
    colStep,
  },
  data() {
    return {
      client_id: "",
      client_detail: {},
      steps_list: [],
      click_navs: [
        { type: 1, name: "报备详情" },
        { type: 2, name: "跟进列表" },
        { type: 3, name: "资料照片" },
      ],
      follow_up_list: [],
      data_type: 1,
      btn_txt: "添加跟进",
      visit_category_list: [],
      steps_recording: [],
      info_imgs: [],
      del_img: true,
      isPhone: true,
      img_list: [],
      load_status: "",
      params: {
        page: 1,
        is_page: 1,
      },
    };
  },
  onLoad(options) {
    if (options) {
      this.client_id = options.id;
    }
    if (options.data_type) {
      this.data_type = options.data_type;
    }
    // 获取带访方式
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "REPORTED_VISIT_CATEGORY":
            this.visit_category_list = item.childs;
            this.getData();
            break;
          case "REPORTED_STATUS":
            this.steps_list = item.childs;
          default:
            break;
        }
      });
    });
  },
  computed: {
    ...mapState(["user_info"]),
  },
  methods: {
    ...mapActions(["getUserInfo"]),
    addSchedule() {
      if (this.data_type == 1 || this.data_type == 2) {
        this.$navigateTo(`/report/add_schedule?id=${this.client_detail.id}`);
      } else if (this.data_type == 3) {
        // 上传资料附件
        if (this.info_imgs.length === 0) {
          uni.showToast({
            title: "请选择图片上传",
            icon: "none",
          });
          return;
        }
        this.$ajax.post(
          "/client/customer/reported/attached_file/multi/create",
          {
            customer_reported_id: this.client_detail.id,
            files: this.info_imgs,
          },
          (res) => {
            if (res.statusCode === 200 && res.data) {
              uni.showToast({
                title: "上传成功",
              });
              this.info_imgs = [];
              this.getDataImgs();
            } else {
              uni.showToast({
                title: res.data.message || "上传失败",
                icon: "none",
              });
            }
          }
        );
      }
    },
    getData() {
      this.$ajax.get(
        `/client/customer/reported/query/project/id/${this.client_id}?all=1`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.client_detail = res.data;
            if (this.client_detail.status == 5) {
              this.steps_list.pop();
            }
            this.isPhone = this.client_detail.customer_phone.includes("*")
              ? false
              : true;
            this.getStepDetail();
            this.visit_category_list.map((item) => {
              if (this.client_detail.visit_category === Number(item.value)) {
                this.client_detail.visit_category = item.description;
              }
            });
          } else {
            uni.showToast({
              title: res.data.message || "数据请求出错",
              icon: "none",
            });
          }
        }
      );
    },
    getStepDetail() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.steps_recording = [];
      }
      this.$ajax.get(
        `/client/customer/reported/audit/record/all/${this.client_detail.project_id}/${this.client_detail.id}`,
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.steps_recording = this.steps_recording.concat(res.data.data);
            if (res.data.data.length == 0) {
              this.load_status = "nomore";
            }
          } else {
            uni.showToast({
              title: res.data.message || "获取跟进记录失败",
              icon: "none",
            });
            this.load_status = "loadend";
          }
        }
      );
    },
    // 到底时加载下一页
    scrollToLower() {
      if (this.load_status === "nomore") {
        return;
      }
      this.params.page++;
      this.getStepDetail();
    },
    callTel() {
      let tel = this.client_detail.customer_phone;
      if (tel) {
        uni.makePhoneCall({
          phoneNumber: tel,
          success: (e) => {},
        });
      }
    },
    // 拨打项目助理
    callTelProject(phone) {
      if (phone) {
        uni.makePhoneCall({
          phoneNumber: phone,
        });
      }
    },
    getDataImgs() {
      this.$ajax.get(
        `/client/customer/reported/audit/attached_file/all/${this.client_detail.project_id}/${this.client_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.img_list = res.data;
          } else {
            uni.showToast({
              title: res.data.message || "请求数据失败",
              icon: "none",
            });
          }
        }
      );
    },
    onClickNav(index) {
      this.data_type = this.click_navs[index].type;
      if (this.data_type == 2 || this.data_type == 1) {
        this.btn_txt = "添加跟进";
      } else if (this.data_type == 3) {
        this.btn_txt = "上传资料";
        this.getDataImgs();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  .title-box {
    padding: 24rpx 48rpx;
    justify-content: space-between;
    align-items: center;
    color: #fff;
    .left {
      font-size: 30rpx;
      flex-direction: column;
      justify-content: space-evenly;
      .txt {
        padding: 8rpx;
      }
    }
    margin: 30rpx auto 0;
    border-radius: 10rpx;
    height: 200rpx;
    width: 700rpx;
    background-image: linear-gradient(
      to right,
      rgba(87, 118, 245, 0.7) 0%,
      rgba(87, 118, 245, 1) 100%
    );
  }
  .report-status {
    padding: 50rpx 0;
  }
  .report-list {
    margin-bottom: 140rpx;
    padding: 0 48rpx;
    .nav {
      flex-direction: row;
      padding-top: 28rpx;
      justify-content: flex-start;
      .nav-item {
        font-size: 28rpx;
        align-items: center;
        margin-right: 40rpx;
        padding-bottom: 30rpx;
        &.active {
          color: #0068e6;
          border-bottom: 4rpx solid #0068e6;
        }
      }
    }
    .top {
      margin-top: 40rpx;
      color: #999;
    }
    .detail {
      .box {
        margin-top: 20rpx;
        .label-ctn {
          margin-left: 40rpx;
        }
      }
    }
  }
  .img_row {
    width: 100%;
    flex-wrap: wrap;
    justify-content: space-between;
    image {
      margin: 6rpx;
      width: 30%;
      height: 180rpx;
    }
    &::after {
      content: "";
      width: 30%;
    }
  }
  .btn-box {
    color: #fff;
    position: fixed;
    bottom: 0;
    height: 120rpx;
    box-shadow: 0 0 6px #ddd;
    width: 100%;
    padding: 20rpx;
    background: #fff;
    .btn {
      width: 700rpx;
      background-image: linear-gradient(
        to right,
        rgba(87, 118, 245, 0.8) 0%,
        rgba(87, 118, 245, 1) 100%
      );
      height: 100%;
      padding: 25rpx;
      border-radius: 10rpx;
      align-items: center;
    }
  }
}
</style>
