<template>
	<view class="container">
		<view class="form-row">
			<view class="label">客户姓名</view>
			<view class="cont">
				<input type="text" v-model="params.cname" placeholder="请填写客户姓名" placeholder-class="placeholder"/>
			</view>
		</view>

		<view :class="{'mobile-form-group':params.mobile.length>1}">
			<view class="form-row" v-for="(item,index) in params.mobile" :key="index">
				<view class="label">手机号</view>
				<view class="cont">
					<view class="flex-row">
						<input type="text" v-model="params.mobile[index]" placeholder="请填写手机号" placeholder-class="placeholder"/>
						<view class="icon-btn">
							<uni-icons type="plus" size="30" color="#2d84fb" @click="addMobileItem()" v-if="index==0"></uni-icons>
							<uni-icons type="minus" size="30" color="#2d84fb" @click="removeMobileItem(index)" v-else></uni-icons>
						</view>
					</view>
				</view>
				
			</view>
		</view>
	
		<view class="form-row">
			<view class="label">报备项目</view>
			<view class="cont" @click="dialogs.projectPicker = true">
				<view class="select-wrapper">
					<view class="select-text">
						<template v-if="params.report_project.length">
							<view v-for="(item,index) in params.report_project" :key="index" class="tag-item">
								<view>{{item.name}}</view>
								<view class="icon-remove" @click.stop="removeProjectItem(index)"><icons type="guanbi" color="#aaa" size="28rpx"></icons></view>
							</view>
						</template>
						<view class="placeholder" v-else>请选择</view>
					</view>
					<icons type="jinrujiantou" color="#aaa" size="28rpx"></icons>
				</view>
			</view>
		</view>
		<view class="form-row">
			<view class="label">报备日期</view>
			<view class="cont">
				<tDatePicker v-model="params.visit_time">
					<template>
						<view class="select-wrapper">
							<text class="select-text">
								<text v-if="params.visit_time">{{params.visit_time}}</text>
								<text class="placeholder" v-else>请选择</text>
							</text>
							<icons type="jinrujiantou" color="#aaa" size="28rpx"></icons>
						</view>
					</template>
				</tDatePicker>
			</view>
		</view>

		<view class="form-row">
			<view class="label">带访时间</view>
			<view class="cont">
				<view class="follow_time">
					<view class="follow_time_item" v-for="(item, index) in visitTypeList" :key="index" :class ="{active:params.visit_type ==item.value}" @click="params.visit_type = item.value">
						{{item.label}}
					</view>
				</view>
			</view>
		</view>

		<view class="form-row">
			<view class="label">报备专员</view>
			<view class="cont" @click="dialogs.reportUserPicker = true">
				<view class="select-wrapper">
					<view class="select-text">
						<text v-if="params.channel_uid.length">
							{{params.channel_user_name}}
						</text>
						<text class="placeholder" v-else>请选择</text>
					</view>
					<icons type="jinrujiantou" color="#aaa" size="28rpx"></icons>
				</view>
			</view>
		</view>

		<view class="footer">
			<button type="primary" :loading="submiting" @click="handleSubmit">确定</button>
		</view>

		<tCrmProjectPicker :visible.sync="dialogs.projectPicker" @confirm="handleSeledProject" v-model="params.project_id"></tCrmProjectPicker>
		<tReportUserPicker :visible.sync="dialogs.reportUserPicker" multiple v-model="params.channel_uid" @confirm="handleSeledReportUser"/>
	</view>
</template>
<script>
import icons from '@/components/my-icon';
import tDatePicker from '@/components/tplus/tDatePicker';
import tCrmProjectPicker from "@/components/tplus/tCrmProjectPicker";
import tReportUserPicker from "@/components/tplus/tReportUserPicker";
export default {
	components: {
		icons, tDatePicker, tCrmProjectPicker, tReportUserPicker
	},
	data(){
		return {
			submiting: false,
			params: {
				client_id: 0,
				cname: '',
				mobile: [],
				project_id: [],
				report_project: [],
				visit_time: '',
				visit_type: 1,
				channel_uid: [],
				channel_user_name: ''
			},
			visitTypeList: [
				{ label: '上午', value: 1 },
				{ label: '下午', value: 2 },
				{ label: '晚上', value: 3 },
			],
			dialogs: {
				projectPicker: false,
				reportUserPicker: false,
			},
		}
	},
	onLoad(options){
		this.params.client_id = options.client_id;
		this.params.cname = options.cname || '';
		this.params.mobile = options.tel ? decodeURIComponent(options.tel).split(',') : [''];

		//当前时间
		const d = new Date();
		let m = d.getMonth() + 1,
			day = d.getDate(),
			h = d.getHours(),
			i = d.getMinutes(),
			s = d.getSeconds();
		this.params.visit_time = d.getFullYear() + '-' + (m < 10 ? '0' + m : m)  + '-' + (day < 10 ? '0' + day : day) 
			+ ' '+ (h < 10 ? '0' + h : h) + ':' + (i < 10 ? '0' + i : i) + ':' + (s < 10 ? '0' + s : s);
	},
	methods: {
		//增加手机号
		addMobileItem(){
			this.params.mobile.push('');
		},
		//移除手机号
		removeMobileItem(index){
			this.params.mobile.splice(index, 1);
		},
		handleSeledProject(e){
			console.log(e);
			this.params.report_project = e.map(e => ({id:e.id, name:e.name}))
		},
		handleChangeDate(e){
			this.params.visit_time = e.detail.value;
		},
		handleSeledReportUser(e){
			this.params.channel_user_name = e.map(e => e.label).join('、')
		},
		removeProjectItem(index){
			this.params.report_project.splice(index, 1);
			this.params.project_id.splice(index, 1);
		},
		async handleSubmit(){
			const params = { ...this.params };
			if(!this.params.cname){
				uni.showToast({title:'请填写客户名称',icon:'none'})
				return
			}
			params.mobile = this.params.mobile.filter(e => e.trim() !== '');
			if(!params.mobile.length){
				uni.showToast({title:'请填写手机号',icon:'none'})
				return
			}
			let regphone = /(^1[3456789]\d{9}$)/
			for(const item of params.mobile){
				if(!regphone.test(item)){
					uni.showToast({
						title:"手机号格式不正确",
						icon:'none'
					})
					return
				}
			}

			if(!this.params.visit_time){
				uni.showToast({title:'请填写报备日期',icon:'none'})
				return
			}
			if(!this.params.project_id.length){
				uni.showToast({title:'请选择报备项目',icon:'none'})
				return
			}
			if(!this.params.channel_uid.length){
				uni.showToast({title:'请选择报备专员',icon:'none'})
				return
			}

			
			params.project_id = params.project_id.join(',');
			params.report_project = params.report_project.map(e => e.name).join(',');
			params.channel_uid = params.channel_uid.join(',');
			params.mobile = params.mobile.join(',');
			delete params.channel_user_name;
			this.submiting = true;
			this.$ajax.post('/admin/crm/report/create',params, (res)=>{
				if(res.statusCode == 200){
					uni.showToast({
						title: res.data?.msg || "提交成功",
						icon: "success",
					});
					setTimeout(()=>{
						this.submiting = false;
						this.$navigateBack(2, true)
					}, 300)
				}else{
					this.submiting = false;
					uni.showToast({
						title: res.data.message,
						icon: "none",
					});
				}
				
			}, er=>{
				this.submiting = false;
			})
		}
	},
}
</script>

<style lang="scss" scoped>
.container{
	padding: 32rpx;
	.form-row{
		display: flex;
		flex-direction: row;
		align-items: baseline;
		.label{
			white-space: nowrap;
			color: rgba(41, 44, 57, 0.4);
			font-size: 32rpx;
			display: flex;
			height: 80rpx;
			align-items: center;
		}
		.cont{
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: center;
			font-size: 32rpx;
			text-align: right;
			color: rgba(41, 44, 57, 0.8);
			min-height: 120rpx;
			padding: 0 0 0 24rpx;
			word-break: break-all;
			.flex-row{
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: flex-end;
			}
			.select-wrapper{
				display: flex;
				flex-direction: row;
				align-items: center;
				min-height: 80rpx;
				.select-text{
					flex: 1;
					display: inline-block;
					padding-right: 6rpx;
					.placeholder{
						color: rgba(41, 44, 57, 0.4);
					}
				}
			}
			.icon-btn{
				margin-left: 12rpx;
			}
			.tag-item{
				display: inline-flex;
				flex-direction: row;
				align-items: center;
				line-height: 1;
				padding: 12rpx 24rpx;
				margin: 8rpx 0;
				border-radius: 4rpx;
				background-color: #f0f0f0;
				&+.tag-item{
					margin-left: 16rpx;
				}
				.icon-remove{
					margin-left: 8rpx;
				}
			}
		}
		
	}
	.mobile-form-group{
		padding: 26rpx 0 5rpx;
		.form-row{
			.cont{
				min-height: 68rpx;
			}
		}
	}
	.footer{
		padding: 32rpx 0;
		button{
			width: 100%;
			background-color: #2d84fb;
		}
	}
}

.follow_time{
	display: flex;
	flex-direction: row;
	align-items: center;
	height: 76rpx;
	background: #f0eff4;
    color: #666;
    padding: 4rpx;
    border-radius: 10rpx;
	.follow_time_item{
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
		&.active{
			background: #fff;
    		border-radius: 10rpx;
		}
	}
}

::v-deep .placeholder{
	color: rgba(41, 44, 57, 0.4);
}
</style>