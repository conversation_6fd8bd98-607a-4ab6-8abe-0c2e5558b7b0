<template>
    <view>
        <view class="background">
            <image src="./photo/picture.png"></image>
        </view>
        <view class="foot">
            <view class="foot_left">
                <view class="publicity_Icon">
                    <view class="publicity">
                        <image src="./photo/publicity.png"></image>
                    </view>
                    <text>关注</text>
                    <view class="publicity">
                        <image src="./photo/billboard.png"></image>
                    </view>
                    <text>榜单</text>
                    <view class="publicity">
                        <image src="./photo/share.png"></image>
                    </view>
                    <text>分享</text>
                    <view class="publicity">
                        <image src="./photo/billfold.png"></image>
                    </view>
                    <text>钱包</text>
                </view>
            </view>
            <view class="foot_right">
                <button type="primary">拨打电话</button>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {
            
        }
    },
}
</script>
<style scoped lang="scss">
.background {
    width: 100%;
    height: 1350rpx;
    background-color: aqua;

    image {
        width: 100%;
        height: 1350rpx;
    }
}

.foot {
    width: 100%;
    height: 190rpx;
    // background-color: rgb(27, 198, 198);
    display: flex;
    flex-wrap: wrap;

    .foot_left {
        width: 50%;
        height: 190rpx;
        // background-color: crimson;

        .publicity_Icon {
            width: 320rpx;
            height: 100rpx;
            // background-color: chocolate;
            margin: 0 auto;
            display: flex;
            flex-wrap: wrap;
            align-content: space-between;
            margin-top: 45rpx;
            text{
                color: #324157;
                font-size: 11px;
                margin-left: 10rpx;
                margin-top: 10rpx;
            }
        }

        .publicity {
            width: 60rpx;
            height: 60rpx;
            // border: 1px dashed #7c8084;
            // background-color: aquamarine;
            image{
                width: 100%;
                height: 100%;
            }
        }
    }

    .foot_right {
        width: 50%;
        height: 190rpx;
        // background-color: rgb(50, 220, 20);
        button{
            width: 300rpx;
            height: 100rpx;
            margin: 50rpx auto;
        }
    }
}
</style>