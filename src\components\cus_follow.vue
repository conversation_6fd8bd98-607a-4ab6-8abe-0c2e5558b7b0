<template>
  <view>
    <tab-bar
      v-if="from == 'mark'"
      class="tab"
      :equispaced="false"
      :fixedTop="false"
      :nowIndex="tab_type"
      :tabs="mark_tab"
      @click="onClickTab"
    >
    </tab-bar>
    <tab-bar
      v-else
      class="tab"
      :equispaced="false"
      :fixedTop="false"
      :nowIndex="tab_type"
      :tabs="tab"
      @click="onClickTab"
    >
    </tab-bar>
    <!-- 跟进 -->
    <view class="follow" v-if="tab_type == 0">
      <view class="tit row">
        <text>客户跟进</text>
        <view class="row" @click="follow">
          <text>写跟进</text>
          <myIcon :type="followShow ? 'open' : 'guanbi'" size="24rpx" color="#2d84fb"></myIcon>
        </view>
      </view>
      <view class="form" v-if="!followShow">
        <view class="level row">
          <view
            class="v"
            v-for="item in level_list"
            :key="item.id"
            :class="{ on: item.id === udetail.level_id }"
            @click="onClickLevel(item.id, udetail.id)"
          >
            <text>{{ item.title }}级</text>
            <text>{{ item.desc }}</text>
          </view>
        </view>
        <view class="title">跟进类型</view>
        <picker
          @change="onTypeChange"
          :range="follow_status_list"
          range-key="title"
          :value="is_follow_index"
        >
          <view :class="follow_id ? '' : 'novalue'" class="row type_val">
            {{ follow_status_list[is_follow_index].title || '请选择' }}
            <myIcon
              style="margin-top: 4rpx"
              :type="typeChange ? 'xiala' : 'shangla'"
              size="30rpx"
              color="#b0b0b0"
            ></myIcon>
          </view>
        </picker>
        <view class="title">备注</view>
        <view class="remark">
          <textarea
            placeholder="请在这里输入"
            maxlength="40"
            v-model="remark"
            @input="getNumber"
          ></textarea>
          <view>{{ reciprocal }}/40</view>
        </view>
        <view class="claim" @click="onConfirmRemark">提交</view>
      </view>
      <timeLine :lineData="follow_list"></timeLine>
      <view class="claim" @click="onLoadMoreFollow">查看更多</view>
    </view>
    <!-- 画像 -->
    <view class="pic_box" v-if="tab_type == 1">
      <view class="list">
        <view class="info">
          <view class="time row">
            <text>接待录入人</text>
            <text class="c2">{{ udetail.created_at }}</text>
          </view>
          <view class="pic row">
            <image
              src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png"
              mode="aspectFill"
            />
            <text class="c2" v-if="udetail.create_user_name || udetail.create_user">{{
              udetail.create_user_name || udetail.create_user.user_name
            }}</text>
          </view>
        </view>
        <view class="info">
          <view class="time row">
            <text>跟进维护人</text>
            <text class="c2">{{ udetail.operation_at }}</text>
          </view>
          <view class="pic row">
            <image
              src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png"
              mode="aspectFill"
            />
            <text class="c2" v-if="udetail.follow_user_name || udetail.follow_user">{{
              udetail.follow_user_name || udetail.follow_user.user_name
            }}</text>
          </view>
        </view>
        <view class="info" v-if="udetail.deal_user_name">
          <view class="time row">
            <text>客源成交人</text>
            <text class="c2">{{ udetail.deal_at }}</text>
          </view>
          <view class="pic row">
            <image
              src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png"
              mode="aspectFill"
            />
            <text class="c2" v-if="udetail.deal_user_name || udetail.deal_user">{{
              udetail.deal_user_name || udetail.deal_user.user_name
            }}</text>
          </view>
        </view>
      </view>
      <view class="list" v-if="udetail.label_name">
        <view class="tag-tit-box row">
          <text class="tag_tit">用户标签</text>
          <text v-if="istag" class="tag_tit tag-more" @click="onClickTag(udetail)">更多</text>
        </view>
        <view class="tag row c2">
          <block v-for="(item, index) in udetail.label_name" :key="index">
            <text v-if="item">{{ item }}</text>
          </block>
        </view>
      </view>
      <view v-if="false" class="list">
        <view class="region row">
          <text>意向区域</text>
          <myIcon class="ic_guanyu" type="ic_guanyu3x1" size="24rpx" color="#8a929f"></myIcon>
          <text class="c2">根据客户7日内浏览产生</text>
        </view>
        <view class="charts-box" :style="{ width: cWidth1 + 'px', height: cHeight1 + 'px' }">
          <qiun-data-charts type="ring" :chartData="chartData2" />
        </view>
      </view>
      <view v-if="false" class="list">
        <text class="tag_tit">意向小区TOP5</text>
        <view class="rank_list">
          <view class="rank row c2">
            <text class="level v1">1</text>
            <text>城建善国盛景</text>
            <text>城南</text>
            <text>均价9000元/m²</text>
          </view>
          <view class="rank row c2">
            <text class="level v2">2</text>
            <text>城建善国盛景</text>
            <text>城南</text>
            <text>均价9000元/m²</text>
          </view>
          <view class="rank row c2">
            <text class="level v3">3</text>
            <text>城建善国盛景</text>
            <text>城南</text>
            <text>均价9000元/m²</text>
          </view>
          <view class="rank row c2">
            <text class="level">4</text>
            <text>城建善国盛景</text>
            <text>城南</text>
            <text>均价9000元/m²</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 线索 -->
    <view class="clue" v-if="tab_type == 2">
      <timeLine :lineData="logs_list"></timeLine>
      <view style="margin: 24px 12px" class="claim" @click="onLoadMoreLogs">查看更多</view>
    </view>
    <!-- 任务 -->
    <view class="task" v-if="tab_type == 3">
      <view class="type row">
        <view v-for="(item, index) in tab1" :key="index">
          <text @click="tabChange(index)" :class="{ active: current == index }">
            {{ item.name }}
          </text>
        </view>
      </view>
      <view class="list">
        <view class="info">
          <view class="top row" @click="taskSwitch">
            <view class="left row">
              <image class="pic" src="../static/customer/rw.png" mode="widthFix" />
              <text>任务名称任务名称任务名称任务名称任务名称任务名称任务名称</text>
            </view>
            <myIcon :type="cancelShow ? 'xiala' : 'shangla'" size="24rpx" color="#2D84FB"></myIcon>
          </view>
          <view class="detail" v-if="cancelShow">
            <view class="sex-box-row labelr row" v-for="item in list" :key="item.value">
              <image class="xz" :src="taskShow ? xz : wxz" mode="widthFix" />
              <view class="info1 row">
                <view class="con c2">
                  <text>{{ item.name }}</text>
                  <view>{{ item.info }}</view>
                </view>
                <view class="row push">
                  <image src="../static/customer/fs.png" mode="widthFix" />
                  <text>发送</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import tabBar from "@/components/tabBar";
import myIcon from "@/components/my-icon";
import timeLine from "@/components/cusTimeLine";
export default {
  components: {
    tabBar,
    timeLine,
    myIcon,
  },
  props: {
    from: {
      type: String,
      default: "",
    },
    udetail: {
      type: Object,
      default: () => { },
    },
    f_page: {
      type: Number,
      default: 1,
    },
    follow_list: {
      type: Array,
      default: () => [],
    },
    mark_tab: {
      type: Array,
      default: () => [],
    },
    logs_list: {
      type: Array,
      default: () => [],
    },
    l_page: {
      type: Number,
      default: 1,
    },
    istag: {
      type: Boolean,
      default: false,
    },
  },
  data () {
    return {
      do_type: 1, //1:客户详情 2：营销素材
      tab: [
        { description: "跟进(5)", type: 0 },
        { description: "用户画像", type: 1 },
        { description: "客户线索(12)", type: 2 },
      ],
      // mark_tab: [
      //   { description: "跟进(5)", type: 0 },
      //   { description: "用户画像", type: 1 },
      //   { description: "客户线索(12)", type: 2 },
      //   // { description: "任务", type: 3 },
      // ],
      tab_type: 0,
      types: [
        {
          name: "个人",
          id: 1,
        },
        {
          name: "机构",
          id: 2,
        },
      ],
      is_follow_index: 0,
      followShow: true,
      typeChange: true,
      remark: "",
      reciprocal: 0,
      tab1: [{ name: "全部" }, { name: "已完成" }, { name: "未完成" }],
      current: 0,
      cancelShow: false,
      list: [
        {
          value: "1",
          name: "标题标题标题标题标题标题标题标题标题标题",
          info:
            "详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情详情",
        },
        { value: "2", name: "标题", info: "详情" },
      ],
      current: "",
      taskShow: "true",
      xz: "../static/customer/xz.png",
      wxz: "../static/customer/wxz.png",
      chartData2: {
        series: [
          {
            data: [
              {
                name: "渠道报备",
                value: 50,
              },
              {
                name: "现场接待",
                value: 30,
              },
              {
                name: "自渠拓客",
                value: 20,
              },
              {
                name: "私客录入",
                value: 18,
              },
              {
                name: "自定义来源",
                value: 8,
              },
              {
                name: "合作平台",
                value: 8,
              },
            ],
          },
        ],
      },
      // #ifdef H5
      cWidth1: "",
      cHeight1: "",
      // #endif
      level_list: [],
      follow_status_list: [],
      follow_id: "",
    };
  },
  created () {
    
    this.getFollowList();
    this.getLevelList();
  },
  onLoad () {
    this.cWidth1 = uni.upx2px(650);
    this.cHeight1 = uni.upx2px(500);
  },
  methods: {
    getLevelList () {
      this.$ajax.get("/qywx/level/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.level_list = res.data;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    getFollowList () {
      this.$ajax.get("/qywx/follow/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.follow_status_list = res.data;
          res.data.map((item) => {
            if (item.is_default) {
              this.follow_id = item.id;
            }
          });
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    taskSwitch () {
      this.cancelShow = !this.cancelShow;
    },
    onClickTab (e) {
      //tab
      this.tab_type = e.type;
    },
    onTypeChange (e) {
      //客户类型
      this.is_follow_index = e.detail.value;
      this.follow_id = this.follow_status_list[e.detail.value].id;
    },
    getNumber () {
      //备注字数
      this.reciprocal = this.remark.length;
    },
    follow () {
      //写跟进
      this.followShow = !this.followShow;
    },
    tabChange (index) {
      //任务tab
      this.current = index;
    },
    radioChange (e) {
      this.current = e.detail.value;
      // console.log(this.current);
    },
    onLoadMoreFollow () {
      this.$emit("loadmoreFollow", this.f_page);
    },
    onLoadMoreLogs () {
      this.$emit("LoadMoreLogs", this.l_page);
    },
    onClickLevel (id, uid) {
      let form = {
        id: uid,
        level_id: id,
      };
      var _this = this;
      uni.showModal({
        title: "提示",
        content: `是否修改客户等级？`,
        success: function (res) {
          if (res.confirm) {
            _this.udetail.level_id = id;
            _this.$ajax.post("/qywx/client/update_level", form, (res) => {
              if (res.statusCode === 200) {
                uni.showToast({
                  title: "修改成功",
                  icon: "none",
                });
                _this.$emit("onClickLevel");
              } else {
                uni.showToast({
                  title: res.data.message,
                  icon: "none",
                });
              }
            });
          }
        },
      });
    },
    onConfirmRemark () {
      let form = {
        type: this.follow_id,
        client_id: this.udetail.id,
        content: this.remark,
      };
      this.$ajax.post("/qywx/client_follow/create", form, (res) => {
        if (res.statusCode === 200) {
          this.$emit("onClickFollow");
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    onClickTag (e) {
      this.$emit("onClickTag", e);
    },
  },
};
</script>
<style lang="scss">
.task {
  .list {
    margin: 12px;
    .info {
      padding: 12px;
      background: #fff;
      border-radius: 6px;
      margin-bottom: 12px;
      .detail {
        .labelr {
          display: flex;
          justify-content: space-between;
          margin: 12px 0;
          .xz {
            width: 16px;
          }
          .info1 {
            align-items: center;
            flex: 1;
            .con {
              flex: 1;
              margin: 0 12px;
              view {
                margin-top: 12px;
              }
            }
            .push {
              image {
                width: 10px;
              }
            }
          }
        }
      }
      .top {
        align-items: center;
        justify-content: space-between;
        .left {
          flex: 1;
          align-items: center;
          font-weight: 500;
          text {
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            word-break: break-all;
          }
          .pic {
            width: 20px;
            margin-right: 12px;
          }
        }
      }
    }
  }
  .type {
    margin: 0 12px;
    view {
      margin-right: 6px;
      text {
        color: #8a929f;
        border-radius: 12px;
        padding: 5px 18px;
        &.active {
          background: #fff;
          color: #2d84fb;
        }
      }
    }
  }
}

page {
  background: #f6f6f6;
  color: #2e3c4e;
}
.c2 {
  color: #8a929f;
}
.clue {
  margin: 0 12px;
  background: #fff;
  padding: 3px 2px;
  border-radius: 6px;
  margin-bottom: 100px;
}
.pic_box {
  margin-bottom: 100px;
  .list {
    margin: 0 12px 12px 12px;
    padding: 12px;
    background: #fff;
    border-radius: 6px;
    .rank_list {
      .rank {
        align-items: center;
        justify-content: space-between;
        margin-top: 12px;
        .level {
          background: #d8d8d8;
          border-radius: 2px;
          color: #fff;
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          &.v1 {
            background: #fb656a;
          }
          &.v2 {
            background: #fb8968;
          }
          &.v3 {
            background: #fbc365;
          }
        }
      }
    }
    .charts-box {
      background: #f8f8f8;
    }
    .region {
      align-items: center;
      font-size: 16px;
      margin-bottom: 24rpx;
      .ic_guanyu {
        margin: 0 8px 0 14px;
      }
      .c2 {
        font-size: 11px;
      }
    }
    .tag-tit-box {
      align-items: center;
      justify-content: space-between;
      .tag-more {
        font-size: 12px;
      }
    }
    .tag_tit {
      font-size: 16px;
    }
    .tag {
      flex-wrap: wrap;
      text {
        background: #f1f4fa;
        border-radius: 12px;
        margin-right: 12px;
        font-size: 11px;
        padding: 6px 18px;
        margin-top: 12px;
      }
    }
    .tite {
      font-size: 16px;
    }
    .info:first-child {
      margin-top: 0;
    }
    .info {
      font-size: 16px;
      margin-top: 25px;
      .time {
        justify-content: space-between;
        align-items: center;
      }
      .pic {
        align-items: center;
        margin-top: 12px;
        image {
          width: 45px;
          height: 45px;
          border-radius: 50%;
          margin-right: 12px;
        }
      }
    }
  }
}
.follow {
  margin: 0 12px;
  background: #fff;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 100px;

  .form {
    margin: 12px 0;
    .remark {
      position: relative;
      textarea {
        border: 2rpx solid #dde1e9;
        border-radius: 4px;
        width: 100%;
        padding: 10px 12px;
        font-size: 14px;
        height: 80px;
      }
      view {
        position: absolute;
        bottom: 12px;
        right: 12px;
        font-size: 11px;
      }
    }
    .type_val {
      justify-content: space-between;
      border: 1px solid #dde1e9;
      border-radius: 4px;
      align-items: center;
      padding: 10px 12px;
    }
    .title {
      margin: 12px 0;
      font-size: 16px;
    }
    .level {
      .v {
        padding: 16px 12px;
        background: #f8f8f8;
        color: #8a929f;
        margin-right: 12px;
        text-align: center;
        border: 1px solid #f8f8f8;
        text:last-child {
          font-size: 12px;
          margin-top: 5px;
        }
        &.on {
          color: #2d84fb;
          background: #fff;
          border: 1px solid #2d84fb;
          border-radius: 4px;
        }
      }
    }
  }
  .tit {
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    margin-bottom: 3px;
    > view {
      color: #2d84fb;
      align-items: center;
      text {
        margin-right: 5px;
      }
    }
  }
}
.tab {
  background-color: rgba(255, 255, 255, 0);
  padding: 0 12px;
  margin: 0 12px 12px 12px;
  ::v-deep.col4 .nav-item {
    width: auto;
    margin-right: 20px;
  }
}
.claim {
  text-align: center;
  height: 40px;
  line-height: 40px;
  font-weight: 500;
  margin: 12px 0;
  border-radius: 6px;
  background: #eaf3ff;
  color: #2d84fb;
}
</style>
