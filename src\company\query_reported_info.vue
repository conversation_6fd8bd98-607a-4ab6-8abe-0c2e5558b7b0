<template>
  <view class="">
    <view class="topbox">
      <view class="time-box row">
        <text class="datestr">
          {{ date_name }}
        </text>
        <view class="input-box row">
          <input
            id="start"
            class="input"
            disabled="true"
            type="text"
            v-model="deal_at_date_start"
            placeholder="开始时间"
            @click="$refs.picker_start.show()"
          />
          <text>至</text>
          <input
            id="end"
            class="input"
            type="text"
            disabled="true"
            v-model="deal_at_date_end"
            placeholder="结束时间"
            @click="$refs.picker_end.show()"
          />
        </view>
        <view class="search" @click="searchTime">
          搜索
        </view>
      </view>
      <tab-bar
        ref="tab_bar"
        :tabs="report_cates"
        :nowIndex="
          report_cates.findIndex((item) => Number(item.value) == params.type)
        "
        fixed_top
        top="100"
        @click="onClickCate"
      ></tab-bar>
      <!-- <view class="filter row">
        <picker
          class="picker-box "
          @change="bindPickerChange"
          :value="index"
          :range="array"
          range-key="desc"
        >
          <view class="uni-input">{{ array[index].desc }}</view>
          <view class="filter-btn">筛选</view>
        </picker>
      </view> -->
    </view>
    <view class="infoList">
      <view class="list" v-for="item in reported_list" :key="item.id">
        <view class="title-top row">
          <text class="left">{{ item.customer_name }}</text>
          <text class="right">{{ item.customer_phone }}</text>
        </view>
        <view class="">
          <view class="ctn-box row">
            <view class="label">报备楼盘</view>
            <view class="content">{{ item.build_name }}</view>
          </view>
          <view class="ctn-box row">
            <view class="label">客户性别</view>
            <view class="content">{{ item.customer_sex | formatSex }}</view>
          </view>
          <view class="ctn-box row">
            <view class="label">报备会员</view>
            <view class="content"
              ><text style="line-height:60rpx"
                >{{ item.u_name || item.u_nickname }} {{ item.u_phone }}</text
              ></view
            >
          </view>
          <view class="ctn-box row">
            <view class="label">报备时间</view>
            <view class="content">{{ item.created_at }}</view>
          </view>
          <view class="ctn-box row">
            <view class="label">最后更新</view>
            <view class="content">{{ item.updated_at }}</view>
          </view>
          <view class="ctn-box row">
            <view class="label">
              报备状态
            </view>
            <view
              class="content txt row"
              :class="{
                report: item.status == 0,
                visite: item.status == 1,
                subscribe: item.status == 2,
                isBuy: item.status == 3,
                isbuy4: item.status == 4,
                isDeal: item.status == 5,
                failure: item.status == 10,
              }"
              >{{ changeStr(item.status) }}
              <text
                v-if="item.status == 5"
                class="dealBill"
                @click="dealBill(item.id)"
                >成交信息</text
              >
            </view>
          </view>
        </view>
      </view>
      <load-more :status="load_status"></load-more>
    </view>
    <myPopup position="center" :show="isShow" @hide="isShow = false">
      <dealBill :customer="customer_info" @close="isShow = false"></dealBill>
    </myPopup>
    <!-- 弹出列表 -->
    <!-- 开始 -->
    <timePicker
      end="2030-12-30"
      mode="YMD"
      ref="picker_start"
      @confirm="confirmTimeStart"
    ></timePicker>
    <!-- 结束 -->
    <timePicker
      end="2030-12-30"
      mode="YMD"
      ref="picker_end"
      @confirm="confirmTimeEnd"
    ></timePicker>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import timePicker from "@/components/time-picker";
import tabBar from "@/components/tabBar";
import loadMore from "@/components/loadMore";
import myPopup from "@/components/myPopup";
import dealBill from "@/components/deal_bill";
export default {
  components: { loadMore, tabBar, myPopup, dealBill, timePicker },
  data() {
    return {
      user_id: "",
      reported_list: [],
      load_status: "",
      params: {
        page: 1,
        type: -1,
        status: "",
      },
      report_cates: [],
      statistics: [],
      array: [
        { desc: "今天", value: "day", id: 1 },
        { desc: "昨天", value: "yesterday", id: 2 },
        { desc: "本周", value: "week", id: 3 },
        { desc: "本月", value: "month", id: 4 },
        { desc: "上月", value: "last_month", id: 5 },
        { desc: "季度", value: "quarter", id: 6 },
        { desc: "今年", value: "year", id: 7 },
      ],
      index: 0,
      isShow: false,
      customer_info: {},
      deal_at_date_start: "",
      deal_at_date_end: "",
      // 获取上个页面地址传递的当天参数
      date_str: "",
      date_name: "",
      load_text: [],
    };
  },
  onLoad(options) {
    this.user_id = options.user_id;
    this.date_str = options.date_str || "year";
    this.getDataInfo();
    this.changeDateStr();
  },
  methods: {
    init() {
      this.$getDictionaryList("REPORTED_STATUS", {}, (res) => {
        if (res.statusCode === 200) {
          this.report_cates = [
            { value: -1, description: "全部" },
            ...res.data.data,
          ];
          this.load_text = res.data.data;
          // for (var i = 0; i < this.report_cates.length; i++) {
          //   this.report_cates[i].statistics = "";
          //   this.statistics.map((e) => {
          //     if (this.report_cates[i].value == e.status) {
          //       this.report_cates[i].statistics = e.total;
          //     }
          //   });
          // }
        }
      });
    },
    bindPickerChange(e) {
      console.log(e);
    },
    changeStr(e) {
      let txt = "";
      this.load_text.find((item) => {
        if (parseInt(item.value) === e) {
          txt = item.description;
        }
      });
      return txt;
    },
    getDataInfo() {
      this.load_status = "loading";
      this.params.status === -1 || this.params.type === -1
        ? delete this.params.status
        : (this.params.status = this.params.type);
      if (this.params.page === 1) {
        this.reported_list = [];
      }
      /**
       * @geturl 设置请求地址
       * @urlJump 声明变量存放一样的地址
       *
       */
      var geturl = "";
      var urlJump = `/client/customer/reported/search/broker?user_id=${this.user_id}`;
      /**
       * @desc 判断是否选中日期如果是判断类型 -1,0用created查，其他状态用status查
       * @desc 点击状态查询条件同上
       * @date 2020/10/15
       * */

      if (this.deal_at_date_start && this.deal_at_date_end) {
        if (this.params.type === -1 || this.params.type === 0) {
          geturl = `${urlJump}&created_date[start]=${this.deal_at_date_start}&created_date[end]=${this.deal_at_date_end}`;
        } else {
          geturl = `${urlJump}&status_date[start]=${this.deal_at_date_start}&status_date[end]=${this.deal_at_date_end}`;
        }
      } else {
        if (this.params.type === -1 || this.params.type === 0) {
          geturl = `${urlJump}&created_date_type=${this.date_str}`;
        } else {
          geturl = `${urlJump}&status_date_type=${this.date_str}`;
        }
      }
      this.$ajax.get(geturl, this.params, (res) => {
        if (res.statusCode === 200) {
          this.load_status = "loadend";
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
          this.statistics = res.data.statistics;
          this.init();
          this.reported_list = this.reported_list.concat(res.data.data);
        } else {
          uni.showToast({
            title: res.data.message || "获取失败",
            icon: "none",
          });
        }
      });
    },
    onClickCate(e) {
      this.params.type = Number(e.value);
      this.params.page = 1;
      this.params.status = this.params.type;
      this.getDataInfo();
    },
    dealBill(id) {
      // 请求客户成交信息
      this.$ajax.get(`/client/sale_order/query/reported/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.customer_info = res.data;
        } else {
          uni.showToast({
            title: res.data.message || "获取失败",
            icon: "none",
          });
        }
      });
      this.isShow = true;
    },
    // 选择开始时间
    confirmTimeStart(e) {
      this.deal_at_date_start = e.result;
      if (this.deal_at_date_start) {
        this.date_name = "筛选";
      }
    },
    //选择结束时间
    confirmTimeEnd(e) {
      this.deal_at_date_end = e.result;
    },
    // 根据时间搜索
    searchTime() {
      this.getDataInfo();
    },
    changeDateStr() {
      this.array.map((item) => {
        if (item.value === this.date_str) {
          this.date_name = item.desc;
        }
      });
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.getDataInfo();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataInfo();
  },
};
</script>

<style scoped lang="scss">
.infoList {
  padding: 24rpx 48rpx;
  margin-top: 160rpx;
  .list {
    padding: 48rpx 24rpx 30rpx;
    background: #fff;
    margin: 30rpx 0;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.04);
    .title-top {
      font-size: 32rpx;
      margin-bottom: 30rpx;
      .left {
        margin-right: 10rpx;
      }
    }
    .ctn-box {
      font-size: 28rpx;
      line-height: 44rpx;
      color: #999999;
      align-items: center;
      .label {
        margin-right: 30rpx;
      }
      .content {
        text {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .dealBill {
          margin-left: 30rpx;
        }
      }
      .txt {
        &.report {
          color: #ff0000;
        }
        &.visite {
          color: #638ff9;
        }
        &.subscribe {
          color: #39becd;
        }
        &.isBuy {
          color: #ff8062;
        }
        &.isBuy4 {
          color: #ffa53a;
        }
        &.isDeal {
          color: #33be85;
        }
        &.failure {
          color: #808080;
        }
      }
    }
  }
}
.topbox {
  background: #fff;
  position: fixed;
  width: 100%;
  top: 0;
  .time-box {
    padding: 0 48rpx;
    line-height: 100rpx;
    height: 100rpx;
    justify-content: space-between;
    .input-box {
      width: 250px;
      margin: 20rpx 0;
      border-radius: 4px;
      align-items: center;
      background-color: #eee;
      padding: 0 16rpx;
      input {
        font-size: 28rpx;
        text-align: center;
        background-color: #eee;
        border: 1rpx solid #f3f3f3;
        height: 32rpx;
        width: 284rpx;
        border: none;
      }
    }
    .search {
      color: #0174ff;
    }
  }
  .filter {
    margin-top: 90rpx;
    line-height: 80rpx;
    font-size: 32rpx;
    width: 100%;
    height: 80rpx;
    padding: 0 48rpx;
    background: #fff;
    .picker-box {
      display: flex;
      width: 100%;
      height: 80rpx;
      position: relative;
      .uni-input {
        align-items: center;
        position: absolute;
        left: 0;
        width: 80%;
        background: #eee;
      }
      .filter-btn {
        margin-left: 20rpx;
        background: #eee;
        align-items: center;
        width: 18%;
        position: absolute;
        right: 0;
      }
    }
  }
}
</style>
