<template>
  <view class="deal">
    <myTabbar :fixedTop="false" custom :tabs="tabs" class="istabs">
      <view class="tabs-list row">
        <view
          class="tabs-item"
          :class="{ isactive: params.type == item.id }"
          v-for="item in tabs"
          @click="onClickType(item)"
          :key="item.id"
          >{{ item.name }}</view
        >
      </view>
    </myTabbar>
    <view class="data-box row" :class="'back' + params.type">
      <view class="data-left">
        <text class="top">3213412</text>
        <text class="bottom">今日收入费用/元</text>
      </view>
      <view class="data-right">
        <image
          src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/zht.png"
        ></image>
      </view>
    </view>
    <myTabbar :fixedTop="false" custom :tabs="tabs2" class="istabs">
      <view class="tabs-list row">
        <view
          class="tabs-item"
          :class="{ isactive: params.status == item.id }"
          v-for="item in tabs2"
          @click="onClickType2(item)"
          :key="item.id"
          >{{ item.name }}</view
        >
      </view>
    </myTabbar>
    <view class="list">
      <menlist :arr="tableData"></menlist>
    </view>
    <view class="btn-bottom">
      <view class="btn-box row">
        <view class="btn">收支账单</view>
        <view class="btn blue">添加</view>
      </view>
    </view>
  </view>
</template>

<script>
import myTabbar from "@/components/tabBar.vue";
import menlist from "./components/men_list";
export default {
  components: {
    myTabbar,
    menlist,
  },
  data() {
    return {
      params: {
        type: 0,
        status: 0,
      },
      tabs: [
        { id: 0, name: "今日" },
        { id: 1, name: "本月" },
        { id: 2, name: "累计" },
      ],
      tabs2: [
        { id: 0, name: "全部" },
        { id: 1, name: "今天" },
        { id: 2, name: "昨天" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
      ],
      tableData: [
        {
          id: 1,
          type_name: "佣金分成",
          type: 0,
          tracking_name: "合作分成",
          cname: "张先生",
          remark:
            "合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/default.jpg?x-oss-process=style/w_80",
          user_name: "申请人",
          created_at: "2022-01-01 12:12:12",
        },
        {
          id: 2,
          type_name: "佣金分成",
          type: 1,
          tracking_name: "合作分成",
          cname: "张先生",
          remark:
            "合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/default.jpg?x-oss-process=style/w_80",
          user_name: "申请人",
          created_at: "2022-01-01 12:12:12",
        },
        {
          id: 3,
          type_name: "佣金分成",
          type: 2,
          tracking_name: "合作分成",
          cname: "张先生",
          remark:
            "合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/default.jpg?x-oss-process=style/w_80",
          user_name: "申请人",
          created_at: "2022-01-01 12:12:12",
        },
        {
          id: 4,
          type_name: "佣金分成",
          type: 3,
          tracking_name: "合作分成",
          cname: "张先生",
          remark:
            "合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/default.jpg?x-oss-process=style/w_80",
          user_name: "申请人",
          created_at: "2022-01-01 12:12:12",
        },
      ],
    };
  },
  methods: {
    onClickType(item) {
      this.params.type = item.id;
    },
    onClickType2(item) {
      this.params.status = item.id;
    },
  },
};
</script>
<style scoped lang="scss">
page {
  background: #f6f6f6;
}
.istabs {
  background: none;
  padding: 12px;
  .tabs-list {
    .tabs-item {
      font-size: 14px;
      color: #8a929f;
      padding: 8px 18px;
      line-height: 1;
      &.isactive {
        border-radius: 12px;
        color: #2d84fb;
        background: #fff;
      }
    }
  }
}
.data-box {
  margin: -12px 12px 12px;
  height: 133px;
  padding: 24px;
  align-items: center;
  border-radius: 6px;
  justify-content: space-between;
  &.back0 {
    background-image: linear-gradient(270deg, #48b6fa 0%, #3e8ff3 100%);
  }
  &.back1 {
    background-image: linear-gradient(90deg, #52c286 0%, #78dbaa 100%);
  }
  &.back2 {
    background-image: linear-gradient(87deg, #9f70f5 0%, #9187ff 100%);
  }
  .data-left {
    color: #fff;
    .top {
      margin-bottom: 12px;
      font-size: 32px;
    }
    .bottom {
      font-size: 14px;
    }
  }
  .data-right {
    height: 60px;
    width: 80px;
    image {
      height: 100%;
      width: 100%;
    }
  }
}
.list {
  padding: 0 12px;
}
.btn-bottom {
  height: 100px;
  .btn-box {
    background: #fff;
    justify-content: space-between;
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 24px;
    .btn {
      height: 42px;
      width: 157px;
      line-height: 42px;
      text-align: center;
      color: #8a929f;
      border: 1px solid rgba(221, 225, 233, 1);
      border-radius: 6px;
      &.blue {
        background: #2d84fb;
        color: #fff;
      }
    }
  }
}
</style>
