<template>
  <view class="list">
    <view v-if="isImg" class="TableImg">
      <image
        class="shareImg"
        style="width:432rpx;height:620rpx"
        :src="imgUrl"
      ></image>
    </view>
    <view v-else class="TableImg" ref="TableImg">
      <view class="img-box">
        <image
          mode="aspectFill"
          :src="img ? img : 'https://img.tfcs.cn/static/img/20200829154656.jpg'"
          class="image"
          crossorigin="anonymous"
        ></image>
      </view>
      <view class="content">
        <view class="top row">
          <view class="left">{{ build_name }}</view>
          <view class="right">{{ build_label }}</view>
        </view>
        <view class="ctn">
          <view class="ctn-box row">
            <view class="ctn-box-left">住宅</view>
            <view class="ctn-box-right red">{{ price }}元/㎡起</view>
          </view>
          <view class="ctn-box row">
            <view class="ctn-box-left">开盘</view>
            <view class="ctn-box-right ">{{ open_time }}</view>
          </view>
        </view>
        <view class="bottom row">
          <view class="bottom-left">
            <view class="build_address">楼盘地址</view>
            <view class="address">{{ address }}</view>
          </view>
          <view class="bottom-right">
            <template>
              <canvas canvas-id="qrcode" class="qrcode" />
            </template>
          </view>
        </view>
      </view>
    </view>
    <view class="btn-box row" v-if="isImg === false">
      <button hover-class="none" class="btn-l btn" @click="downloadImg">
        生成图片
      </button>
      <!-- #ifndef H5 -->
      <button hover-class="none" class="btn-r btn">分享好友</button>
      <!-- #endif -->
    </view>
  </view>
</template>

<script>
import uQRCode from "@/common/uqrcode.js";
import html2canvas from "html2canvas";
export default {
  components: { uQRCode },
  props: {
    img: {
      type: String,
      default:
        "https://img.tfcs.cn/1/1/build/155/house_type/1597993921624510.jpg",
    },
    build_name: String,
    build_label: String,
    price: [String, Number],
    open_time: String,
    address: String,
    url: String,
  },
  data() {
    return {
      imgUrl: "",
      isImg: false,
      qrcodeSize: uni.upx2px(130),
    };
  },
  mounted() {
    this.make();
  },
  methods: {
    make() {
      uQRCode.make({
        canvasId: "qrcode",
        componentInstance: this,
        text: this.url,
        size: this.qrcodeSize,
        margin: 0,
        backgroundColor: "#ffffff",
        foregroundColor: "#000000",
        fileType: "png",
        correctLevel: 3,
        success: (res) => {},
      });
    },
    downloadImg() {
      // 获取待转换对象
      /// 转换下载

      let copyDom = document.querySelector(".TableImg");
      html2canvas(copyDom, {
        useCORS: false,
      }).then((canvas) => {
        //1.0.0版本的有then方法
        //返回图片dataURL，参数：图片格式和清晰度(0-1)
        this.imgUrl = canvas.toDataURL("image/png");
        this.isImg = true;
      });
      uni.showToast({
        title: "长按图片保存",
        icon: "none",
      });
    },
  },
};
</script>

<style scoped lang="scss">
.qrcode {
  width: 130rpx;
  height: 130rpx;
}
.list {
  border-radius: 14rpx;
  width: 432rpx;
  height: 670rpx;
  background: #ffff;
  margin: 0 auto 50%;
  .img-box {
    width: 432rpx;
    height: 240rpx;
    .image {
      width: 100%;
      height: 240rpx;
      border-radius: 14rpx 14rpx 0 0;
    }
  }
  .content {
    padding: 24rpx;
    line-height: 48rpx;
    .top {
      align-items: center;
      .left {
        font-size: 28rpx;
      }
      .right {
        line-height: normal;
        margin-left: 16rpx;
        color: #fff;
        padding: 0 16rpx;
        font-size: 22rpx;
        background-image: linear-gradient(135deg, #69d4bb 0%, #00caa7 100%);
      }
    }
    .ctn {
      .ctn-box {
        font-size: 22rpx;
        .ctn-box-left {
          color: #999;
          margin-right: 16rpx;
        }
        .red {
          color: #fb656a;
        }
      }
      &::after {
        content: "";
        margin: 16rpx 0;
        border-bottom: 2rpx solid #d8d8d8;
      }
    }
    .bottom {
      font-size: 22rpx;
      color: #999;
      align-items: center;
      line-height: normal;
      .bottom-left {
        padding-right: 24rpx;
        width: 280rpx;
      }
      .bottom-right {
        background: #fff;
        width: 130rpx;
        height: 130rpx;
      }
    }
  }
}
.btn-box {
  position: relative;
  left: 50%;
  transform: translate(-50%);
  .btn {
    font-size: 28rpx;
    border-radius: 4px;
    width: 180rpx;
    line-height: 64rpx;
    height: 64rpx;
  }
  .btn-l {
    background: #fff;
    color: #0174ff;
    border: 1rpx solid #0174ff;
  }
  .btn-r {
    background: #0174ff;
    color: #fff;
  }
}
button::after {
  border: none;
}
</style>
