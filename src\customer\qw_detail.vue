<template>
  <view class="">
    <view v-if="is_show_content">
      <mySkeleton
        v-if="is_show_skeleton"
        :showAvatar="false"
        :row="9"
        :showTitle="true"
      ></mySkeleton>
      <!--  v-if="!is_show_skeleton" -->
      <view class="top" v-if="!is_show_skeleton">
        <view class="top_img">
          <view class="top_img_main">
            <view class="top_img_follow">
              <!-- 客户数据 -->
              <template v-if="user_detail.follow_id > 0">
                <view class="top_img_cus row">
                  <span v-if="user_detail.get_time != '' && user_detail.get_time != undefined">
                    跟客{{ user_detail.get_time | getFollowDay(user_detail.get_time) }}
                  </span>
                  <span
                    v-if="
                      user_detail.take_num != '' &&
                      user_detail.take_num != undefined &&
                      user_detail.get_time != '' &&
                      user_detail.get_time != undefined
                    "
                    style="margin: 0 5px"
                  >
                    |
                  </span>
                  <span v-if="user_detail.take_num != '' && user_detail.take_num != undefined">
                    {{ user_detail.take_num | getTakeLook(user_detail.take_num) }}
                  </span>
                </view>
                <!-- 时间数据 -->
                <view class="top_img_time">
                  {{ user_detail.last_follow_time }}
                </view>
              </template>
              <template v-if="user_detail.follow_id == 0">
                <view class="top_img_cus row">
                  <span class="un_follow"> 待认领 </span>
                </view>
                <!-- 掉工 -->
                <view class="top_img_time drop_to_sea">
                  {{
                    user_detail.public2_status == 1
                      ? '已转公'
                      : user_detail.public2_status == 2
                      ? '已掉公'
                      : ''
                  }}
                </view>
              </template>
            </view>
          </view>
        </view>
        <view class="top-card" style="margin-top: -100rpx">
          <view class="row">
            <view class="top-card-left">
              <image
                class="pic"
                :src="`https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/${
                  user_detail.sex == 1 ? 'nan2' : 'nv2'
                }.png`"
                mode="aspectFill"
              />
            </view>
            <view class="top-card-right">
              <view class="top-card-right-top row">
                <text class="top-card-right-top_name">{{ user_detail.cname }}</text>
                <image
                  v-if="user_detail.wxqy_id"
                  class="top-card-right-top_qw"
                  src="/static/customer/qw.png"
                  mode="widthFix"
                />
                <image
                  class="pic top-card-right-top_sex"
                  :src="`https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/${
                    user_detail.sex == 1 ? 'nan' : 'nv3'
                  }.png`"
                  mode="aspectFill"
                />
                <!-- https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan.png -->
                <!-- <span class="top-card-right-top_tracking">{{
                  user_detail.tracking ? user_detail.tracking.title : ''
                }}</span> -->
                <!-- <text
                  :style="{
                    backgroundColor: user_detail.level.color,
                  }"
                  class="top-card-right-top_level"
                  v-if="user_detail.level"
                  >{{ user_detail.level ? user_detail.level.title : '--' }}</text
                > -->
              </view>
              <image
                @click="showAction"
                v-if="user_detail.level && user_detail.level.title"
                class="setting_level"
                mode="widthFix"
                :src="
                  ('/yidongduan/customer/level/' + user_detail.level.title + '_d.png')
                    | imageFilter('w_80')
                "
              ></image>
              <image
                @click="showAction"
                v-else
                class="setting_level"
                mode="widthFix"
                :src="'/yidongduan/customer/level/un_d.png' | imageFilter('w_80')"
              ></image>
              <!-- <span v-else   @click="showAction"  class ='un_level'>
                未设置
              </span> -->
              <!-- <image
                @click="showAction"
                v-if ='user_detail.level && ser_detail.level.title'
                class="setting"
                :src="'/yidongduan/customer/level/'+ user_detail.level.title+'.png' | imageFilter('w_80')"
              ></image> -->
              <!-- <view class="top-card-right-bottom">
                客户性别：{{ user_detail.sex == 1 ? '男' : user_detail.sex == 2 ? '女' : '--' }}
              </view> -->
              <span class="top-card-right-top_tracking">{{
                user_detail.tracking ? user_detail.tracking.title : ''
              }}</span>
            </view>
          </view>
          <view class="top-card-bot-bottom row">
            <span>跟进{{ user_detail.follow_num > 0 ? '（已跟进）' : '（未跟进）' }}</span>
            <span>创建时间：{{ user_detail.created_at }}</span>
          </view>
        </view>

        <view class="top-card">
          <salesProgress :list="progress" @clickItem="clickProgress"></salesProgress>
        </view>

        <view class="top-card">
          <view class="top-card-title row"
            >基本信息
            <!-- <myIcon type="you"></myIcon> -->
          </view>
          <view class="top-card-content tel row">
            <view class="top-card-content-label top-card-content-label_top">客户电话</view>
            <view>
              <view
                class="top-card-content-right"
                :class="{
                  has_follow:
                    user_detail.last_call_follow && user_detail.last_call_follow.client_id > 0,
                }"
                v-if="!is_view_tel"
                @click="show_last_call_follow_content = !show_last_call_follow_content"
              >
                <span
                  class="un_tong"
                  :class="{
                    tong:
                      user_detail.last_call_follow && user_detail.last_call_follow.call_status > 0,
                  }"
                  >{{ user_detail.mobile | mobileFilter }}</span
                ></view
              >
              <!-- <view class="top-card-content-right" :class ='{has_follow:user_detail.last_call_follow && user_detail.last_call_follow.client_id>0}' v-if="is_view_tel">{{ user_detail.mobile }}</view> -->
              <view
                class="top-card-content-act"
                v-if="!user_detail.mobile_place"
                @click="searchAddress"
                >归属地查询</view
              >
              <view class="top-card-content-act address" v-else>{{
                user_detail.mobile_place
              }}</view>
              <view
                class="top-card-content-last_follow"
                v-if="
                  user_detail.last_call_follow &&
                  user_detail.last_call_follow.content &&
                  show_last_call_follow_content
                "
                >{{ user_detail.last_call_follow.content }}</view
              >
            </view>
            <view class="chakan" @click="setViewTel">{{
              user_detail.call_open_crm > 1 ? '外呼' : '查看'
            }}</view>
          </view>
          <view class="top-card-content row" v-if="user_detail.source">
            <view class="top-card-content-label">客户来源</view>
            <view class="top-card-content-right">{{
              user_detail.source ? user_detail.source.title : '--'
            }}</view>
          </view>
          <view class="top-card-content row" v-if="user_detail.type">
            <view class="top-card-content-label">客户来源</view>
            <view class="top-card-content-right">{{ typeFilter(user_detail.type) }}</view>
          </view>
          <view class="top-card-content row" v-if="user_detail.intention_community">
            <view class="top-card-content-label">客户意向</view>
            <view class="top-card-content-right">{{ user_detail.intention_community }}</view>
          </view>
          <view class="top-card-content">
            <view class="top-card-content-label">备注信息</view>
            <textarea
              placeholder-class="placeholderClass"
              placeholder="一句话描述客户需求"
              class="textarea"
              :disabled="!has_roles"
              @blur="onConfirmRemark"
            ></textarea>
          </view>
        </view>
        <view class="top-card" v-if="has_roles">
          <view class="top-card-title row" @click="onClickTag"
            >客户标签
            <myIcon type="you"></myIcon>
          </view>

          <view
            class="top-card-label-list row"
            v-if="user_detail.label_name && user_detail.label_name.length > 0"
          >
            <!-- <view class=""> -->
            <!-- <view class="l-title">{{ item.name }}</view> -->
            <!-- class="tag_input row" -->
            <block v-for="(item, index) in user_detail.label_name" :key="index">
              <block v-for="(item1, index1) in item.son" :key="index1">
                <view class="tag_item" :key="item1.id">
                  <!-- <view class="checked">
                    <image src="@/static/img/checked.png"></image>
                  </view> -->
                  <!-- <view class="tag_t">
                    {{ item.name }}
                  </view> -->
                  <view class="tag_b">
                    {{ item1.name }}
                  </view>
                </view>
              </block>
            </block>
            <!-- </view> -->
          </view>
        </view>
        <!-- <view class="top-card" v-if="has_roles" @click="showAction">
          <view class="top-card-title">客户等级</view>
          <text class="level" v-if="user_detail.level">{{ user_detail.level.title }}</text>
        </view> -->
        <!-- <view
          :class="{ isborder: is_view_tel_desc }"
          class="top-card"
          @click="onGoDemand"
          v-if="user_detail.follow_id > 0 || has_roles"
        >
          <view class="top-card-title row"
            >填写跟进内容
            <myIcon type="you"></myIcon>
          </view>
        </view> -->
        <view class="top-card fixed">
          <tabBar
            :fixedTop="false"
            :nowIndex="nowIndex"
            :tabs="tabs_list"
            @click="onClickTabs"
          ></tabBar>
          <!-- <view class="top-card-tabs row">
            <view
              class="top-card-tabs-item"
              v-for="item in tabs_list"
              :key="item.id"
              @click="onClickTabs(item)"
              :class="{ isactive: item.id == is_tabs }"
              >{{ item.name }}</view
            >
          </view> -->
        </view>
        <!-- 动态 -->
        <view class="top-card" v-if="is_tabs == 1" style="margin-bottom: 100px">
          <view class="top-card-title card-border-line row">2022年01月01日 </view>
        </view>
        <view class="pic_box top-card" v-if="is_tabs == 2">
          <view class="basic-info-box">
            <view class="basic-info-title">基本信息</view>
            <!-- 客户行为 -->
            <view class="basic-info-content">
              <text class="content-title">客户行为</text>
              <text
                :class="
                  user_detail.last_behavior_day != '' && user_detail.last_behavior_day != undefined
                    ? 'blueInfo'
                    : ''
                "
              >
                {{
                  user_detail.last_behavior_day != '' && user_detail.last_behavior_day != undefined
                    ? user_detail.last_behavior_day
                    : '--'
                }}
              </text>
              <text
                v-if="
                  user_detail.last_behavior_day != '' && user_detail.last_behavior_day != undefined
                "
                >天内</text
              >
            </view>
            <!-- 活跃时间 -->
            <view class="basic-info-content">
              <text class="content-title">活跃时间</text>
              <template v-if="user_detail.start_date || user_detail.end_date">
                <text style="color: #3d91ff" v-if="user_detail.start_date">{{
                  user_detail.start_date
                }}</text>
                <text style="color: #3d91ff" v-if="user_detail.start_date && user_detail.end_date"
                  >/</text
                >
                <text style="color: #3d91ff" v-if="user_detail.end_date">{{
                  user_detail.end_date
                }}</text>
              </template>
              <template v-else> -- </template>
              <text v-if="user_detail.start_date || user_detail.end_date">点</text>
            </view>
            <!-- 客户偏好 -->
            <view class="basic-info-content">
              <text class="content-title">客户偏好</text>
              <text>{{ user_detail.like || '--' }}</text>
            </view>
            <!-- 线索 -->
            <view class="basic-info-content">
              <text class="content-title">线 索</text>
              <text
                :class="
                  user_detail.operation_log != '' && user_detail.operation_log != undefined
                    ? 'redInfo'
                    : ''
                "
              >
                {{ user_detail.operation_log || 0 }}
              </text>
              <text v-if="user_detail.operation_log != '' && user_detail.operation_log != undefined"
                >条</text
              >
            </view>
            <!-- 社群 -->
            <view class="basic-info-content">
              <text class="content-title">社 群</text>
              <text>{{ user_detail.group_list || '0' }}</text
              >个
            </view>
            <!-- 好友 -->
            <view class="basic-info-content">
              <text class="content-title">好 友</text>
              <text>
                {{
                  user_detail.qywx_info && user_detail.qywx_info.name
                    ? user_detail.qywx_info.name
                    : '--'
                }}
              </text>
            </view>
            <!-- 报备 -->
            <view class="basic-info-content">
              <text class="content-title">报 备</text>
              <text
                :class="
                  user_detail.customer_list != '' && user_detail.customer_list != undefined
                    ? redInfo
                    : ''
                "
              >
                {{ user_detail.customer_list || '--' }}
              </text>
              <text v-if="user_detail.customer_list != '' && user_detail.customer_list != undefined"
                >条</text
              >
            </view>
            <!-- 报备状态 -->
            <view class="basic-info-content">
              <text class="content-title">报备状态</text>
              {{ user_detail.customer_status || '--' }}
              <template v-if="user_detail.customer_day">
                ({{ user_detail.customer_day }}天前)
              </template>
            </view>
            <!-- 最近报备 -->
            <view class="basic-info-content">
              <text class="content-title">最近报备</text>
              {{ user_detail.customer_build_name || '--' }}
            </view>
          </view>
          <view class="list" style="margin-bottom: 100px">
            <view class="info">
              <view class="time row">
                <text>接待录入人</text>
              </view>
              <view class="pic row">
                <image
                  src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png"
                  mode="aspectFill"
                />
                <view class="">
                  <view class="c2 flex_start">{{
                    user_detail.create_user_name ||
                    (user_detail.create_user && user_detail.create_user.user_name) ||
                    '--'
                  }}</view>
                  <view class="c2 depart"
                    >{{
                      (user_detail.create_user && user_detail.create_user.department_name) || '--'
                    }}
                  </view>
                </view>
              </view>
              <text class="c2">创建时间：{{ user_detail.created_at || '--' }}</text>
            </view>
            <view class="info">
              <view class="time row">
                <text>跟进维护人</text>
              </view>
              <view class="pic row">
                <image
                  src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png"
                  mode="aspectFill"
                />
                <view>
                  <view class="c2 flex_start">{{
                    user_detail.follow_user_name ||
                    (user_detail.follow_user && user_detail.follow_user.user_name) ||
                    '--'
                  }}</view>
                  <view class="c2 depart">{{
                    (user_detail.follow_user && user_detail.follow_user.department_name) || '--'
                  }}</view>
                </view>
              </view>
              <text class="c2">创建时间：{{ user_detail.get_time || '--' }}</text>
            </view>
            <view class="info">
              <view class="time row">
                <text>首次带看人</text>
              </view>
              <view class="pic row">
                <image
                  src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png"
                  mode="aspectFill"
                />
                <view>
                  <view class="c2 flex_start"
                    >{{
                      (user_detail.first_look_user && user_detail.first_look_user.user_name) || '--'
                    }}
                  </view>
                  <view class="c2 depart">{{
                    (user_detail.first_look_user && user_detail.first_look_user.department_name) ||
                    '--'
                  }}</view>
                </view>
              </view>
              <text class="c2"
                >首次带看时间：{{
                  (user_detail.first_look_user && user_detail.first_look_user.first_look_at) || '--'
                }}</text
              >
            </view>
            <!-- v-if="user_detail.deal_user_name" -->
            <view class="info">
              <view class="time row">
                <text>客源成交人</text>
              </view>
              <view class="pic row">
                <image
                  src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png"
                  mode="aspectFill"
                />
                <view>
                  <view class="c2 flex_start">{{
                    user_detail.deal_user_name ||
                    (user_detail.deal_user && user_detail.deal_user.user_name) ||
                    '--'
                  }}</view>
                  <view class="c2 depart">{{
                    (user_detail.deal_user && user_detail.deal_user.department_name) || '--'
                  }}</view>
                </view>
              </view>
              <text class="c2">创建时间：{{ user_detail.deal_at || '--' }}</text>
            </view>
          </view>
        </view>
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 3">
          <view class="follow-card" style="margin-bottom: 100px" @click="hideShow">
            <view
              class="follow-card-item"
              v-for="(item, index) in follow_list_new"
              :key="item.id + '_' + index"
            >
              {{ item.created_at | captureTime1 }}
              <view class="follow-card-content row" style="padding-bottom: 0">
                <view class="left" style="margin-right: 10px">
                  {{ item.created_at | captureTime }}
                </view>
                <view class="right">
                  <view class="row">
                    <view class="order" v-if="item.order"><view class="order_c"> 顶</view></view>
                    <text v-if="!item.url" v-html="item.content"> </text>
                    <view v-else class="voice row" @click.prevent.stop="playVoice(item, index)">
                      <image
                        v-if="!item.playing"
                        :src="'/static/icon/voice/voice_icon.png' | imageFilter('w_80')"
                      ></image>
                      <image
                        v-if="item.playing"
                        :src="'/static/icon/voice/play_voice.gif' | imageFilter('w_80')"
                      ></image>
                      <text>{{ parseInt(item.time / 1000) }}"</text>
                    </view>
                  </view>

                  <text class="username" v-if="item.admin"
                    >由「{{ item.admin.user_name }}」跟进</text
                  >
                </view>
              </view>
              <view class="follow-card-picture">
                <view class="follow-picture-box">
                  <image
                    v-for="(img, i) in item.file_path_list"
                    :key="i"
                    @click.prevent.stop="preFollowImgs(item.file_path_list, i)"
                    :src="img"
                    mode="aspectFill"
                  />
                </view>
                <view class="follow-giveLike-box">
                  <view class="follow-giveLike-main" v-if="item.top_list && item.top_list.length">
                    <view class="follow-giveLike-zan">
                      <image
                        src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/<EMAIL>"
                      />
                    </view>
                    <template v-if="item.top_list && item.top_list.length">
                      <view class="follow-giveLike-user" v-for="(it, i) in item.top_list" :key="it">
                        <template v-if="i < 2">
                          {{ it }}
                        </template>
                        <template v-if="i >= 2">
                          {{ '等' + item.top_list.length + '人' }}
                        </template>
                      </view>
                    </template>
                  </view>

                  <view class="follow_giveLike-controls">
                    <!-- 点赞 -->
                    <view
                      class="follow_giveLike_icon"
                      @click.prevent.stop="followGiveLike(item.id)"
                    >
                      <image :src="'/static/admin/customer/<EMAIL>' | imageFilter('w_80')" />
                    </view>
                    <!-- 复制 -->
                    <view
                      style="margin: 0 20px; box-sizing: content-box"
                      class="follow_giveLike_icon"
                      @click.prevent.stop="followCopy(item)"
                    >
                      <image
                        src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/<EMAIL>"
                      />
                    </view>
                    <!-- 置顶 -->
                    <view
                      class="follow_giveLike_more"
                      @click.prevent.stop="showPinned(item, index)"
                    >
                      <image :src="'/static/admin/customer/<EMAIL>' | imageFilter('w_80')" />
                    </view>
                    <view class="follow-Pinned-box" ref="tops" v-if="item.show">
                      <text class="Pinned-text" v-if="item.order == 0" @click.stop="setTop(item)"
                        >设置置顶</text
                      >
                      <text class="Pinned-text" v-if="item.order == 1" @click.stop="setTop(item)"
                        >取消置顶</text
                      >
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <loadMore :status="is_f_loading_new"></loadMore>
          </view>
        </view>
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 7">
          <view class="follow-card" style="margin-bottom: 100px" @click="hideShow">
            <view
              class="follow-card-item"
              v-for="(item, index) in follow_list"
              :key="item.id + '_' + index"
            >
              {{ item.created_at | captureTime1 }}
              <view class="follow-card-content row" style="padding-bottom: 0">
                <view class="left" style="margin-right: 10px">
                  {{ item.created_at | captureTime }}
                </view>
                <view class="right">
                  <view class="row">
                    <view class="order" v-if="item.order"><view class="order_c"> 顶</view></view>
                    <text v-if="!item.url" v-html="item.content"> </text>
                    <view v-else class="voice row" @click.prevent.stop="playVoice(item, index)">
                      <image
                        v-if="!item.playing"
                        :src="'/static/icon/voice/voice_icon.png' | imageFilter('w_80')"
                      ></image>
                      <image
                        v-if="item.playing"
                        :src="'/static/icon/voice/play_voice.gif' | imageFilter('w_80')"
                      ></image>
                      <text>{{ parseInt(item.time / 1000) }}"</text>
                    </view>
                  </view>

                  <text class="username" v-if="item.admin"
                    >由「{{ item.admin.user_name }}」跟进</text
                  >
                </view>
              </view>
              <view class="follow-card-picture">
                <view class="follow-picture-box">
                  <image
                    v-for="(img, i) in item.file_path_list"
                    :key="i"
                    @click.prevent.stop="preFollowImgs(item.file_path_list, i)"
                    :src="img"
                    mode="aspectFill"
                  />
                </view>
                <view class="follow-giveLike-box">
                  <view class="follow-giveLike-main" v-if="item.top_list && item.top_list.length">
                    <view class="follow-giveLike-zan">
                      <image
                        src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/<EMAIL>"
                      />
                    </view>
                    <template v-if="item.top_list && item.top_list.length">
                      <view class="follow-giveLike-user" v-for="(it, i) in item.top_list" :key="it">
                        <template v-if="i < 2">
                          {{ it }}
                        </template>
                        <template v-if="i >= 2">
                          {{ '等' + item.top_list.length + '人' }}
                        </template>
                      </view>
                    </template>
                  </view>

                  <view class="follow_giveLike-controls">
                    <!-- 点赞 -->
                    <view
                      class="follow_giveLike_icon"
                      @click.prevent.stop="followGiveLike(item.id)"
                    >
                      <image :src="'/static/admin/customer/<EMAIL>' | imageFilter('w_80')" />
                    </view>
                    <!-- 复制 -->
                    <view
                      style="margin: 0 20px; box-sizing: content-box"
                      class="follow_giveLike_icon"
                      @click.prevent.stop="followCopy(item)"
                    >
                      <image
                        src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/<EMAIL>"
                      />
                    </view>
                    <!-- 置顶 -->
                    <view
                      class="follow_giveLike_more"
                      @click.prevent.stop="showPinned(item, index)"
                    >
                      <image :src="'/static/admin/customer/<EMAIL>' | imageFilter('w_80')" />
                    </view>
                    <view class="follow-Pinned-box" ref="tops" v-if="item.show">
                      <text class="Pinned-text" v-if="item.order == 0" @click.stop="setTop(item)"
                        >设置置顶</text
                      >
                      <text class="Pinned-text" v-if="item.order == 1" @click.stop="setTop(item)"
                        >取消置顶</text
                      >
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <loadMore :status="is_f_loading"></loadMore>
          </view>
        </view>
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 4">
          <view class="follow-card" style="margin-bottom: 100px">
            <view class="follow-card-item" v-for="(item, i) in logs_list" :key="item.id">
              <text v-if="item.created_at">
                {{ item.created_at | captureTime1 }}
              </text>
              <view class="follow-card-content row">
                <view class="left" v-if="item.created_at" style="margin-right: 10px">
                  {{ item.created_at | captureTime }}
                </view>
                <view class="right" style="width: 100%">
                  <view class="follow-config-main">
                    <view class="follow-config-titel">
                      客户跟进
                      <text
                        v-if="item.type_title && item.type_title.title"
                        style="margin: 0 5px; line-height: 16px"
                        >|</text
                      >
                      <text v-if="item.type_title && item.type_title.title">
                        {{ item.type_title.title }}
                      </text>
                    </view>
                    <!-- <view class="InstalledTop">已置顶</view> -->
                    <view class="follow-config-box">
                      <!-- <text class="follow_config-Pinned">已置顶</text> -->
                      <!-- <view class="follow-config-like" @click="">点赞</view>
                      <view class="follow-config-copy" @click="followCopy(item)">复制</view>
                      <view class="follow-config-top" @click="followSetTop(item)">设为置顶</view> -->
                    </view>
                  </view>
                  <text class="follow-box-content">
                    {{ item.content }}
                  </text>
                  <text class="username" v-if="item.admin"
                    >由「{{ item.admin.user_name }}」跟进</text
                  >
                </view>
              </view>
            </view>
            <loadMore :status="is_l_loading"></loadMore>
          </view>
        </view>
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 5">
          <view class="follow-card" style="margin-bottom: 100px">
            <view class="follow-card-item" v-for="item in guiji_list" :key="item.id">
              <text v-if="item.ctime">
                {{ item.ctime | captureTime1 }}
              </text>
              <view class="follow-card-content row">
                <view class="left" v-if="item.ctime" style="margin-right: 10px">
                  {{ item.ctime | captureTime }}
                </view>
                <view class="right">
                  <text>
                    {{ item.content || '' }}
                  </text>
                </view>
              </view>
            </view>
            <loadMore :status="is_guiji_loading"></loadMore>
          </view>
        </view>
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 6">
          <view class="follow-card" style="margin-bottom: 100px">
            <view class="follow-card-item" v-for="item in tel_list" :key="item.id">
              <tel-item
                @clickvoice="onClickVoice"
                @voiceEnded="voice_playing_index = -1"
                @voiceError="voice_playing_index = -1"
                :share="item"
                :voice_playing="item.voice_playing_index == voice_playing_index"
              >
              </tel-item>
            </view>
            <loadMore :status="is_tel_loading"></loadMore>
          </view>
        </view>
      </view>
      <!-- 底部菜单 -->
      <view class="container top-line flex-row footer_btn_group">
        <view class="option_btn flex-1" v-if="user_detail.follow_id > 0">
          <my-button @click="concatOwner" type="primary" :round="false" size="big">电话</my-button>
        </view>
        <view
          class="option_btn flex-1"
          :class="{ disabled: un_renling_status }"
          v-if="user_detail.follow_id == 0"
        >
          <my-button @click="getCustomer" type="primary" :round="false" size="big">认领</my-button>
        </view>
        <view class="option_btn flex-1">
          <my-button @click="onGoDemand" type="primary" :round="false" size="big" :plain="true"
            >跟进</my-button
          >
        </view>
        <view class="option_btn flex-1" @click="isShowSetting = true">
          <my-button type="primary" :round="false" size="big" :plain="true">
            <view class="more_con row">更多 ⋮ </view></my-button
          >
        </view>
        <!-- 底部菜单 -->
      </view>
      <!-- <view v-if="is_from == 3" class="container top-line flex-row footer_btn_group">
        <view class="option_btn flex-3">
          <my-button @click="getCustomer" type="primary" :round="false" size="big">认领</my-button>
        </view>
        <view class="option_btn flex-3">
          <my-button @click="onGoDemand" type="primary" :round="false" size="big" :plain="true"
            >跟进</my-button
          >
        </view>
        <view class="option_btn flex-3" @click="isShowSetting3 = true">
          <my-button type="primary" :round="false" size="big" :plain="true">
            <view class="more_con row">更多 ⋮ </view></my-button
          >
        </view>
      </view> -->
    </view>
    <myPopup :show="isShowSetting" @hide="isShowSetting = false">
      <view class="setting-pop">
        <view class="setting-pop-top row">
          <view
            @click="onClickSetting1(item)"
            class="item"
            v-for="item in setting_1"
            :key="item.id"
          >
            <image class="item-img" :src="item.icon"></image>
            <span>{{ item.name }}</span>
          </view>
        </view>
        <view class="setting-pop-bottom">
          <view
            class="item row"
            v-for="item in setting_2"
            :key="item.id"
            @click="onClickSetting2(item)"
          >
            <view class="left row">
              <image class="item-img" :src="item.icon"></image>
              <span>{{ item.name }}</span>
            </view>
            <myIcon type="you"></myIcon>
          </view>
        </view>
      </view>
    </myPopup>
    <my-popup ref="remind" :show="isShow" @hide="isShow = false">
      <remind
        :id="user_detail.id"
        class="remind"
        v-if="isShow"
        :temp_disable="temp_disable"
        @onClick="onClickRemind"
        @onClick1="onClickRemind1"
        @cancel="isShow = false"
      ></remind>
    </my-popup>

    <my-popup ref="reason" :show="giveup_reason" @hide="giveup_reason = false" :touch_hide="true">
      <view class="reason" v-if="giveup_reason">
        <view class="close" @click="giveup_reason = false">×</view>
        <view class="label"> 请输入转到公海的原因 </view>
        <textarea v-model="giveup_content" placeholder="请输入转到公海的原因"></textarea>
        <view class="btn" @click="toSeas"> 转到公海 </view>
      </view>
    </my-popup>

    <!-- <my-popup ref="reason" :show="show_follow_see" @hide="show_follow_see = false" :touch_hide="true">
      
    </my-popup> -->
    <image
      v-if="false"
      class="istotop"
      src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/hdb.png"
      @click="ongoTop"
    ></image>

    <myPop
      ref="showPhone"
      :show="show_phone_pop"
      position="center"
      width="80%"
      @close="show_phone_pop = false"
    >
      <view class="p_con">
        <view class="title"> 拨打电话 </view>
        <view class="p_content">
          <view class="p_item flex-row items-center">
            <view class="label"> 选择外显号码 </view>
            <view class="value flex-1">
              <selectDown
                valueName="phone"
                :multiple="false"
                v-model="show_id"
                :localdata="phoneList"
                @change="changeSelect"
                defaultValue="请选择外显号码"
                placeholder="请选择外显号码"
              >
              </selectDown>
            </view>
          </view>
          <view class="btns flex-row items-center">
            <view class="btn flex-1" @click="conMakePhone"> 确认拨打 </view>
          </view>
        </view>
      </view>
    </myPop>

    <!-- 公海详情进入显示立即认领 -->
    <!-- <view
      class="lijibtn"
      v-if="is_from == 3 && !user_detail.follow_id && is_load_end"
      @click="getCustomer"
      >立即认领</view
    > -->
  </view>
</template>
<script>
import myIcon from "@/components/my-icon.vue";
import myPopup from "@/components/myPopup.vue";
import remind from "@/components/remind";
import Dropdown from "../house/components/Dropdown";
import DropdownItem from "../house/components/DropdownItem";
import myButton from "../house/components/myButton";
import loadMore from "@/components/loadMore"
import tabBar from "@/components/tabBar"
import salesProgress from "./components/salesProgress"
import selectDown from '@/outbound/components/uni-data-select'
import telItem from './components/telItem.vue';
import myPop from "@/outbound/components/myPopup";
const innerAudioContext = uni.createInnerAudioContext();
export default {
  components: { myIcon, myPopup, remind, Dropdown, DropdownItem, myButton, loadMore, selectDown, salesProgress, telItem, myPop, selectDown, tabBar },
  data () {
    return {
      client_id: "",
      user_detail: {},
      follow_params: {
        page: 1,
        per_page: 10,
        client_id: "",
      },
      follow_params_new: {
        page: 1,
        per_page: 10,
        client_id: "",
      },
      log_params: {
        page: 1,
        per_page: 10,
        client_id: "",
      },
      is_guiji_loading: false,
      guiji_params: {
        page: 1,
        per_page: 10,
        client_id: "",
      },
      guiji_list: [],
      follow_list: [],
      is_f_loading: '',
      is_f_loading_new: '',
      logs_list: [],
      is_l_loading: '',
      // 查看电话
      is_view_tel: false,
      view_tel: "",
      is_from: "",
      is_show_skeleton: false,
      is_show_content: false, // 用于从侧边栏进入隐藏内容
      type_list: [],
      tabs_list: [
        // { id: 1, name: "动态" },
        { id: 3, name: "跟进", description: "跟进" },
        { id: 2, name: "画像", description: "画像" },
        { id: 7, name: "维护", description: "维护" },
        { id: 4, name: "线索", description: "线索" },
        { id: 5, name: "轨迹", description: "轨迹" },
        { id: 6, name: "外呼", description: "外呼" },
      ],
      is_tabs: 3,
      isShowSetting: false,
      isShowSetting3: false,
      setting_1: [
        {
          id: 1,
          name: "维护资料",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/whzl.png",
        },
        {
          id: 2,
          name: "提醒跟进",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/txtz.png",
        },
      ],
      setting_2: [
        {
          id: 1,
          name: "标无效",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/bwx.png",
        },
        {
          id: 2,
          name: "转交到同事",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/zzl.png",
        },
        {
          id: 5,
          name: "转交到公海",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/zzl.png",
        },
        {
          id: 4,
          name: "绑定企业微信",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/zzl.png",
        },
        {
          id: 3,
          name: "更改客户状态",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/sp.png",
        },
      ],

      isShow: false,
      is_view_tel_desc: false,
      from: '',    //进入来源  默认为空  如果侧边栏进入不携带id  from=‘fromOther’
      login_user_info: {},//当前登录用户的信息
      has_roles: false, //是否有操作权限 也就是 当前的客户还是不是登陆者的客户 如果不是只能查看不能操作
      is_load_end: false, //定义变量 加载完成以后显示认领按钮
      token: "",
      giveup_content: "",
      giveup_reason: false,
      stateList: [],
      progress: [],
      show_follow_see: false,
      follow_see_type: 0,
      follow_see_params: {
        content: "",
        take_date: "",
        take_time: "",
        take_no: "",
        accompany: []
      },
      voice_playing_index: -1,
      tel_params: {
        page: 1,
        per_page: 10,
        status: 19,
        call_phone: '',
        call_show_phone: ""
      },
      tel_list: [],
      is_tel_loading: "",
      show_phone_pop: false,
      phoneList: [],
      show_id: "",
      nowIndex: 0,
      show_last_call_follow_content: false,
      temp_disable: false,
      un_renling_status: false  //false  可以认领 true 不可认领 
    };
  },

  onLoad (options) {
    uni.$on("getDataAgain", () => {
      // this.is_show_content = true
      this.getDataDetail();
      this.getTypeData();
      this.setProgress()
    })
    // if (options . website_id) {
    //   if (options.website_id == 176) {
    this.is_show_skeleton = true;

    if (this.$isWxWork() == 'wxwork' || this.$isWxWork() == 'com-wx-pc') {
      this.token = localStorage.getItem("wxwork_token");
      this.website_id = localStorage.getItem("wxwork_id");
      if (!this.token) {
        return
      }

    } else {
      let website_id = options.website_id
      this.token = localStorage.getItem("token" + website_id)
      console.log(this.token);
      if (!this.token) {
        localStorage.setItem('backUrl', location.href)
        this.$router.push("https://yun.tfcs.cn")
      }

    }
    // 未登录中断请求

    this.is_from = options.form || 2; // 来源公海/我的
    uni.showLoading({
      title: "加载中",
      mask: true
    })
    this.source = options.source || 1
    this.un_renling_status = (options.un_renling_status === "false" || !options.un_renling_status) ? false : true
    if (options.id) {
      this.client_id = options.id;

      this.getDataDetail();
      this.getTypeData();
    } else {
      this.from = "fromOther"
      this.getWxQyWxConfig(["agentConfig", 'getCurExternalContact'], wx => {
        this.wx = wx
        this.getDataDetail();
        this.getTypeData();
      })
    }
    innerAudioContext.onPlay(() => {
      // item.playing = true
    })
    // 监听语音播放停止
    innerAudioContext.onStop(() => {
      if (this.follow_list) {
        this.follow_list.map(item => {
          item.playing = false
          return item
        })
      }
      // item.playing = false
    })
    // 监听语音自然播放结束事件
    innerAudioContext.onEnded(() => {
      if (this.follow_list) {
        this.follow_list.map(item => {
          item.playing = false
          return item
        })
      }
    })
    // 监听语音播放失败事件
    innerAudioContext.onError(() => {

      uni.showToast({
        title: '播放失败，请重试',
        icon: 'none'
      })
      if (this.follow_list) {
        this.follow_list.map(item => {
          item.playing = false
          return item
        })
      }

    })
  },
  onShow () {
    if (this.is_refresh) {
      uni.$on('refresh', (data) => {
        if (data.refreh) {
          this.follow_params.page == 1; // 重置页码
          this.follow_list = []; // 清空跟进记录列表
          this.getFollowData(); // 获取跟进记录
        }
      })
      this.is_refresh = false;
    }
  },
  onUnload () {
    uni.$off("getDataAgain")
    uni.$off("refresh"); // 解绑自定义事件
  },

  filters: {
    mobileFilter (val) {
      let reg = /^(.{3}).*(.{3})$/;
      if (val) {
        return val.replace(reg, "$1*****$2");
      }
    },

    captureTime1 (fullTime) {
      var timeArr = fullTime
        .replace(" ", ":")
        .replace(/\:/g, "-")
        .split("-");
      var nian = timeArr[0];
      var yue = timeArr[1];
      var ri = timeArr[2];
      var shi = timeArr[3];
      var fen = timeArr[4];
      var miao = timeArr[5];

      return yue + "月" + ri + "日";
    },
    captureTime (fullTime) {
      var timeArr = fullTime
        .replace(" ", ":")
        .replace(/\:/g, "-")
        .split("-");
      var nian = timeArr[0];
      var yue = timeArr[1];
      var ri = timeArr[2];
      var shi = timeArr[3];
      var fen = timeArr[4];
      var miao = timeArr[5];

      return shi + ":" + fen;
    },
    // 计算过去时间天数
    getPastDay (val) {
      if (val == 0) {
        return "今天";
      } else {
        return val + "天前";
      }
    },
    // 计算跟客时间
    getFollowDay (val) {
      const currentDate = new Date();
      const specifiedDate = new Date(val);
      // 计算时间差
      const timeDiff = currentDate.getTime() - specifiedDate.getTime();
      // 将时间差转换为天数
      const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      if (daysDiff == 0) {
        return "今天";
      } else {
        return daysDiff + "天";
      }
    },
    // 计算带看次数
    getTakeLook (val) {
      switch (val) {
        case 1:
          val = "首看";
          break;
        case 2:
          val = "二看";
          break;
        default:
          if (val > 3) {
            return "多次带看";
          }
          break;
      }
      return val;
    },
    // 是否是新客留资(24小时内新增的)
    getNewCustomer (val) {
      const currentDate = new Date();
      const specifiedDate = new Date(val);
      // 计算时间差
      const timeDiff = currentDate.getTime() - specifiedDate.getTime();
      // 将时间差转换为天数
      const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      if (daysDiff == 0) {
        return "新客留资";
      } else {
        return null;
      }
    }
  },
  methods: {
    getTypeData () {
      this.$ajax.get("/qywx/type/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.type_list = res.data;
        }
      });
    },
    onClickVoice (src) {
      var voice_playing_index = this.tel_list.findIndex(item => item.record_url == src)
      if (this.voice_playing_index == voice_playing_index) {
        this.voice_playing_index = -1
      } else {
        this.voice_playing_index = voice_playing_index
      }
    },
    typeFilter (val) {
      let arr = this.type_list.filter((item) => {
        if (item.id == val) {
          return item;
        }
      })[0];
      if (arr) {
        return arr.title;
      }
    },
    onClickTabs (e) {
      console.log(e);
      this.nowIndex = e.index
      this.is_tabs = e.id;
      if (e.id == 5) {
        this.guiji_params.page = 1
        this.getGuijiList()
      }
      if (e.id == 7) {
        this.follow_params.page = 1
        this.getFollowData()
      }
      if (e.id == 4) {
        this.log_params.page = 1;
        this.getLogsData();
        // this.follow_params.page =1
        // this.getFollowData()
      }
      if (e.id == 6) {
        this.tel_params.page = 1
        this.getTelList()
      }
    },
    getTelList () {
      this.tel_params.client_id = this.client_id
      if (this.tel_params.page == 1) {
        this.tel_list = []
      }

      this.$ajax.get("/admin/crm/client_follow/search", this.tel_params, res => {
        console.log(res);
        if (res.statusCode == 200) {
          res.data.data.map((item, index) => item.voice_playing_index = index)
          this.tel_list = this.tel_list.concat(res.data.data)
          if (res.data.data.length < this.tel_params.per_page) {
            this.is_tel_loading = "nomore";
          } else {
            this.is_tel_loading = "loadend"
          }
        }


      })
    },
    getGuijiList () {
      if (this.guiji_params.page === 1) {
        this.guiji_list = [];
      }
      this.guiji_params.id = this.client_id
      this.is_guiji_loading = "loading"
      this.$ajax.get("/qywx/tfy_client_track/search", this.guiji_params, (res) => {
        if (res.statusCode === 200) {
          this.guiji_list = this.guiji_list.concat(res.data.list);
          if (res.data.list.length === 0 || res.data.list.length < this.guiji_list.per_page) {
            this.is_guiji_loading = "nomore";
          } else {
            this.is_guiji_loading = "loadend"
          }
        }
      });
    },
    getDataDetail () {
      var url = "";
      if (this.client_id) {
        url = `/admin/crm/client/info/${this.client_id}`;
        this.getUserData(url);
      } else {
        var _this = this
        this.wx.invoke("getCurExternalContact", {}, function (res) {
          // this.user_id = 'wm-VQJYQAABaPDlf4UPTMNqm40Rq5WXw'
          if (res.err_msg == "getCurExternalContact:ok") {
            _this.user_id = res.userId;
            url = `/qywx/client/qw_info/${_this.user_id}`;
            _this.getUserData(url);
          } else {
            console.log(res);
          }
        });
      }
    },
    searchAddress (e) {
      this.$ajax.get(`/admin/crm/client/query_mobile_place/${this.user_detail.id}`, {}, res => {
        if (res.statusCode == 200) {
          // console.log(res);
          this.$set(this.user_detail, 'mobile_place', res.data)
          // let index = this.client_list.findIndex(item => item.id == e.id)
          // this.$set(this.client_list[index], 'mobile_place', res.data)
        }
      })
    },
    getUserData (url) {
      this.is_show_skeleton = false;
      this.$ajax.get(url, { type: this.source }, (res) => {
        console.log(res, 123213);
        if (res.statusCode === 200) {
          this.user_detail = res.data;
          this.follow_params.client_id = res.data.id;
          this.log_params.client_id = res.data.id;
          this.client_id = res.data.id;

          let send_template = uni.getStorageSync("send_template1") ? JSON.parse(uni.getStorageSync("send_template1")) : {}
          if (send_template && send_template.client_id) {
            if (this.client_id == send_template.client_id) {
              let sed_tem = new Date(send_template.date),
                now_tem = new Date()
              let oy = sed_tem.getFullYear(),
                om = sed_tem.getMonth(),
                od = sed_tem.getDate(),
                ny = now_tem.getFullYear(),
                nm = now_tem.getMonth(),
                nd = now_tem.getDate()
              console.log(oy, om, od, ny, nm, nd);
              if (oy = ny && od == nd && om == nm) {
                this.temp_disable = true
              }
            }
          }
          this.setProgress()
          this.getFollowDataNew()
          // this.getFollowData();
          this.getUserInfo()
          // this.getTelList()
          // 第三方来源
          // this.tabs_list = [
          //   { id: 3, name: "跟进", description: "跟进" },
          //   { id: 2, name: "画像", description: "画像" },
          //   { id: 7, name: "维护", description: "维护" },
          //   { id: 4, name: "线索", description: "线索" },
          //   { id: 5, name: "轨迹", description: "轨迹" },
          //   { id: 6, name: "外呼", description: "外呼" },
          // ]
          // if (this.user_detail.access_id) {
          //   if (this.user_detail.follow_id == 0) {
          //     // 公海客户详情
          //     this.tabs_list = this.tabs_list.filter((item) => item.id == 4 || item.id == 6);
          //     this.tabs_list.push(
          //       { id: 5, name: "轨迹", description: "轨迹" },
          //     )
          //     // this.tabs_list.push(
          //     //   { id: 6, name: "电话记录" },
          //     // )
          //     this.is_tabs = 4;
          //   } else {
          //     this.tabs_list = [
          //       { id: 3, name: "跟进", description: "跟进" },
          //       { id: 2, name: "画像", description: "画像" },
          //       { id: 7, name: "维护", description: "维护" },
          //       { id: 4, name: "线索", description: "线索" },
          //       { id: 5, name: "轨迹", description: "轨迹" },
          //       { id: 6, name: "外呼", description: "外呼" },
          //     ]
          //   }

          // }
          if (this.user_detail.wxqy_id) {
            this.setting_2 = this.setting_2.filter(item => item.id != 4)
          }
          uni.hideLoading()
          this.is_load_end = true
          uni.setStorageSync("crm_client_id", res.data.id);
          this.$ajax.get('/admin/crm/client/verify_follow', {}, res => {
            if (res.statusCode == 200) {
              if (res.data && res.data.id > 0 && res.data.client_id > 0) {
                if (res.data.client_id == this.client_id) {
                  this.toForceFollow(res.data)
                } else {
                  let website_id = this.$getQueryString('website_id')
                  let url = `/customer/detail?id=${res.data.client_id}&form=${this.is_from}&source=${this.source}&website_id=${website_id || this.website_id}`
                  uni.redirectTo({
                    url
                  })
                }
              }
            }
          })
          this.is_show_content = true
        }
        else {
          uni.hideLoading()
          let website_id = this.$getQueryString('website_id')
          uni.redirectTo({
            url: '/customer/default_crm?website_id=' + (website_id || this.website_id)
          })
          // this.$navigateTo(`/customer/default_crm`);
        }
      });
    },
    toForceFollow (data) {
      let telInfo = { tel: [] }
      telInfo.tel.push(this.user_detail.mobile)
      if (this.user_detail.subsidiary_mobile_list && this.user_detail.subsidiary_mobile_list.length) {
        telInfo.tel.push(...this.user_detail.subsidiary_mobile_list)
      }
      uni.setStorageSync("telInfo", JSON.stringify(telInfo))
      let website_id = this.$getQueryString('website_id')
      let url = `/customer/demand?id=${data.client_id}&from=${this.from}`
      // if (this.view_tel && this.view_tel.length) {
      url += "&tel=1&name=" + encodeURIComponent(this.user_detail.cname) + '&telType=' + this.user_detail.call_open_crm + "&tracking=" + (this.user_detail.tracking ? this.user_detail.tracking.id : '') + "&has_roles=" + (this.has_roles ? 1 : 0) + "&tel_log_id=" + data.id + "&website_id=" + (website_id || this.website_id)
      uni.redirectTo({
        url: url
      })
      // }
      // this.$navigateTo(url)
    },
    followSee (type) {
      if (!this.has_roles) {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: 'none'
        })
        return
      }
      this.$navigateTo("/customer/daikan?type=" + type + "&id=" + this.user_detail.id)
      // this.follow_see_type = type
      // this.show_follow_see =true
    },
    setProgress () {
      this.progress = []
      let arr = [
        //   {
        //   name: "level_id",
        //   title: this.user_detail.level_id ? this.user_detail.level.title : "未设置",
        //   events: 'showAction'
        // },
        {
          name: "see_tel_num",
          title: "邀约",
          events: 'setViewTel'
        },
        {
          name: "is_first_look",
          title: '带看',
          events: 'followSee'
        },
        {
          name: "is_repeat_look",
          title: '复看',
          events: 'followSee'
        },
        {
          name: "deal_id",
          title: '成交',
          events: 'toShenpi'
        }]
      for (let index = arr.length - 1; index >= 0; index--) {
        const element = arr[index];
        let icon = '', icon_right = '', status = 0, icon_left = ''
        if (element.name == 'level_id') {
          if (this.user_detail.level_id) {
            icon = this.user_detail.level.title + '-1'
            icon_right = this.user_detail.level.title + '-2'
            status = 1

            icon_left = ''
          } else {
            icon = "5=2"
            icon_right = "7-1"
            status = 0
            icon_left = ''
          }

        } else {
          if (this.user_detail[element.name] > 0) {
            status = 1
            icon = index == arr.length - 1 ? '4-2' : '6-2'
            icon_right = index == arr.length - 1 ? '' : '6-1'
            icon_left = "6-3"
          } else {
            status = 0
            icon = index == arr.length - 1 ? '7-2' : '02'
            icon_right = index == arr.length - 1 ? '' : '7-1'
            icon_left = "7-3"
          }
        }
        console.log(this.user_detail[arr[index + 1]], 1112);
        // let icon = element.name=='level_id'?this.user_detail.level.title+'-1':()
        let obj = {
          name: element.title,
          status: status,
          icon,
          icon_right,
          icon_left,
          value: element.name,
          events: element.events
        }
        this.progress.unshift(obj)
        console.log(this.progress);

      }
      // 如果后边的已完成 前边的就直接完成  所以从后往前追加  
      // if (this.user_detail.deal_id > 0) {
      //   // 成交状态
      //   this.progress.unshift({
      //     title: "已成交",
      //     icon: "4-1",
      //     status: 1,
      //     id: 6
      //   })
      // } else {
      //   this.progress.unshift({
      //     title: "成交",
      //     icon: "7-2",
      //     status: 0,
      //     id: 6
      //   })
      // }
      // // 复看状态
      // if (this.user_detail.deal_id > 0) {

      //   this.progress.unshift({
      //     title: "复看",
      //     icon: "4-1",
      //     status: 1,
      //     id: 5
      //   })
      // } else {
      //   this.progress.unshift({
      //     title: "复看",
      //     icon: "7-2",
      //     status: 0,
      //     id: 5
      //   })
      // }

      // if(this.user_detail.)
      // if (this.user_detail.level_id){
      //   this.progress.push({
      //     title:A,

      //   })
      // }
    },
    clickProgress (e) {
      if (e.value == "is_first_look") {
        let type = 1
        if (this.user_detail.is_first_look > 0) {
          type = 2
        }
        this[e.events](type)
      } else if (e.value == "is_repeat_look") {
        this[e.events](2)
      } else {
        this[e.events]()
      }
      // let arr = [
      //   {
      //     value: ''
      //   }
      // ]
    },
    getUserInfo () {
      var that = this;
      this.getLevelList()
      this.getStateList()
      this.$ajax.get("/qywx/common/query", {}, (res) => {
        if (res.statusCode === 200) {
          this.login_user_info = res.data
          if (!Array.isArray(this.user_detail.admin_list) && this.user_detail.admin_list == '') {
            this.user_detail.admin_list = []
          }
          let mangers = this.user_detail.admin_list
          let keyuanManger = 0, whr = 0
          if (mangers.includes(this.login_user_info.id + "")) {
            keyuanManger = 1
          }
          console.log(keyuanManger);
          if (this.login_user_info.id == this.user_detail.follow_id) {
            whr = 1
          }
          if (keyuanManger == 1 || whr == 1 || (this.user_detail.follow_id > 0 && this.user_detail.follow_id == this.login_user_info.id)) {
            this.has_roles = true
          }
          // if (this.user_detail.follow_id != this.login_user_info.id) {
          //   this.has_roles = false
          // }
        }
      });
    },
    getStateList () {
      this.$ajax.get("/admin/crm/tracking/list", { type: 4 }, res => {
        if (res.statusCode == 200) {
          this.stateList = res.data
        }
      }, () => {

      })
    },
    postData (data) {
      let params = {
        call_phone_id: data.call_id,
        call_name: this.user_detail.cname,
        call_phone: data.callee,
        call_show_phone: data.caller,
        type: 1,
        client_id: this.client_id
      }
      this.$ajax.post("/common/call_module/addCallOutRecord", params, res => {
        console.log(res);
      })
    },
    getphoneList (callBack) {
      this.$ajax.get('/admin/call_clue/getSeatsPhone', {}, res => {
        if (res.statusCode == 200) {
          this.phoneList = res.data.map(item => {
            item.value = item.show_id
            item.text = item.phone
            return item
          })
          callBack && callBack()
        }
      })
    },
    changeSelect () {

    },

    conMakePhone (item) {
      if (!this.show_id) {
        uni.showToast({
          title: "请选择外显号码",
          icon: "none"
        })
        return
      }
      let api = `/admin/call_clue/directCallPhone`
      let params = { show_id: this.show_id, phone: this.user_detail.mobile, client_id: this.client_id }
      if (this.user_detail.call_open_crm == 3) {
        api = '/admin/call_clue/anyOneCallByCrm'
      }
      this.$ajax.post(api, params, res => {
        if (res.statusCode == 200) {
          if (res.data.call_id) {
            this.postData(res.data)
          }
          uni.showToast({
            title: '正在拨打中 请稍后',
            icon: "none",
            duration: 2000
          })
          this.show_phone_pop = false
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
            duration: 2000
          })
        }
      })
    },
    // getStateList1 () {
    //   this.$ajax.get("/admin/crm/tracking/list", { type: 2 }, res => {
    //     this.getStateList1()
    //     if (res.statusCode == 200) {
    //       this.stateList = res.data
    //     }
    //   }, () => {
    //     this.getStateList1()
    //   })
    // },
    getFollowDataNew () {
      if (this.follow_params_new.page === 1) {
        this.follow_list_new = [];
      }
      this.is_f_loading_new = "loading"
      let params = Object.assign({}, this.follow_params_new, { client_id: this.follow_params.client_id })
      this.$ajax.get(
        "/admin/crm/client_follow/follow_search",
        params,
        (res) => {
          if (res.statusCode === 200) {
            if (res.data.data.length < this.follow_params_new.per_page) {
              this.is_f_loading_new = 'nomore';
            } else {
              this.is_f_loading_new = ''
            }
            res.data.data.map(i => {
              i.playing = false
              return i
            })
            this.follow_list_new = this.follow_list_new.concat(res.data.data);
            // this.getLogsData();
          }
        }
      );
    },
    getFollowData () {
      if (this.follow_params.page === 1) {
        this.follow_list = [];
      }
      this.is_f_loading = "loading"
      this.$ajax.get(
        "/admin/crm/client_follow/search",
        this.follow_params,
        (res) => {
          if (res.statusCode === 200) {
            if (res.data.data.length === 0 || res.data.data.length < this.follow_params.per_page) {
              this.is_f_loading = 'nomore';
            } else {
              this.is_f_loading = ''
            }
            res.data.data.map(i => {
              i.playing = false
              return i
            })
            this.follow_list = this.follow_list.concat(res.data.data);
            // this.getLogsData();
          }
        }
      );
    },
    setViewTel () {
      // if(this.user_detail.call_open_crm>1){
      //   if(this.user_detail.call_open_crm==2){
      //     if (!this.has_roles) {
      //       uni.showToast({
      //         title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
      //         icon: "none",
      //       });
      //       return
      //     }
      //   }
      //   if (this.phoneList&& this.phoneList.length){
      //       this.show_phone_pop =true
      //     }else  {

      //       this.getphoneList(()=>{
      //         this.show_phone_pop =true
      //       })
      //     }
      // }else  {
      //   if (!this.has_roles) {
      //       uni.showToast({
      //         title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
      //         icon: "none",
      //       });
      //       return
      //     }
      //   this.setViewTels()
      // }

      if (this.user_detail.call_open_crm == 3) {
        let actionArr = ['查看电话', '外呼']
        uni.showActionSheet({
          itemList: actionArr,
          success: (result) => {
            if (result.tapIndex == 0) {
              // 查看电话
              if (!this.has_roles) {
                uni.showToast({
                  title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
                  icon: "none",
                });
                return
              }
              this.setViewTels()

            } else if (result.tapIndex == 1) {
              // 直接吊起外呼电话

              if (this.phoneList && this.phoneList.length) {
                this.show_phone_pop = true
              } else {
                this.getphoneList(() => {
                  this.show_phone_pop = true
                })
              }

            }
          },
          fail: (res) => {
            console.log(res.errMsg);
          }
        })
      } else {
        // 如果没开外呼 查看电话
        if (!this.has_roles) {
          uni.showToast({
            title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
            icon: "none",
          });
          return
        }
        this.setViewTels()
      }

    },
    setViewTels () {
      let api = `/admin/crm/client/see_tel/${this.client_id}`
      this.$ajax.get(api, {}, (res) => {
        if (res.statusCode === 200) {
          // this.is_view_tel = true;
          // uni.showToast({
          //   title: "不要忘记写跟进哦",
          //   icon: "none",
          // });
          // console.log(res);
          this.view_tel = res.data;
          uni.setStorageSync("telInfo", JSON.stringify(this.view_tel))
          let url = `/customer/demand?id=${this.user_detail.id}&from=${this.from}`
          // if (this.view_tel && this.view_tel.length) {
          url += "&tel=1&name=" + encodeURIComponent(this.user_detail.cname) + '&telType=' + this.user_detail.call_open_crm + "&tracking=" + (this.user_detail.tracking ? this.user_detail.tracking.id : '') + "&has_roles=" + (this.has_roles ? 1 : 0)
          // }
          this.$navigateTo(url)
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    getLogsData () {
      if (this.log_params.page === 1) {
        this.logs_list = [];
      }
      this.is_l_loading = "loading"
      this.$ajax.get("/qywx/client_clue/search", this.log_params, (res) => {
        if (res.statusCode === 200) {
          this.logs_list = this.logs_list.concat(res.data.data);
          if (res.data.data.length === 0 || res.data.data.length < this.log_params.per_page) {
            this.is_l_loading = "nomore";
          } else {
            this.is_l_loading = "loadend"
          }
        }
      });
    },
    loadmoreFollow () {
      if (this.is_f_loading == "nomore" || this.is_f_loading == "loading") {
        // uni.showToast({
        //   title: "没有更多了",
        //   icon: "none",
        // });
        return;
      }
      this.follow_params.page++;
      this.getFollowData();
    },
    loadmoreFollowNew () {
      console.log(this.is_f_loading_new);
      if (this.is_f_loading_new == "nomore" || this.is_f_loading_new == "loading") {
        // uni.showToast({
        //   title: "没有更多了",
        //   icon: "none",
        // });
        return;
      }
      this.follow_params_new.page++;
      this.getFollowDataNew();
    },
    LoadMoreLogs () {
      if (this.is_l_loading == 'nomore' || this.is_l_loading == 'loading') {
        return;
      }
      this.log_params.page++;
      this.getLogsData();
    },
    loadMoreGuiji () {
      if (this.is_guiji_loading == 'nomore') {
        return;
      }
      this.guiji_params.page++;
      this.getGuijiList();
    },
    loadMoreTel () {
      if (this.is_tel_loading == 'nomore' || this.is_tel_loading == 'loading') {
        return;
      }
      this.tel_params.page++;
      this.getTelList();
    },
    onClickTag () {
      if (!this.has_roles) {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: 'none'
        })
        return
      }
      this.$navigateTo(`/customer/tag_list?id=${this.user_detail.id}&from=${this.from}`);
    },
    onConfirmRemark (e) {
      if (!e.detail.value) {
        // uni.showToast({
        //   title: "请检查备注内容",
        //   icon: "none",
        // });
        return
      }
      let form = {
        id: this.user_detail.id,
        remark: e.detail.value,
      };
      this.$ajax.post("/qywx/client/update_remark", form, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "操作成功",
            icon: "none",
          });
          this.log_params.page = 1;
          this.getLogsData();
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    onClickSetting1 (e) {
      if (!this.has_roles) {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: 'none'
        })
        return
      }
      if (e.id == 1) {
        this.$navigateTo(`uphold?type=2&id=${this.user_detail.id}`);
      } else {
        this.isShowSetting = false;
        this.isShow = true;
      }
    },
    onClickSetting2 (e) {
      if (!this.has_roles) {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: 'none'
        })
        return
      }
      if (e.id == 1) {
        if (this.user_detail.is_state == 1) {
          uni.showToast({
            title: "该客户正在审核中",
            icon: 'none'
          })
          return
        }
        let shenPiInfo = {
          is_del: this.user_detail.is_del,
          is_state: this.user_detail.is_state,
          state_list: this.user_detail.state_list,
          stateList: this.stateList
        }
        uni.setStorageSync("shenpi", JSON.stringify(shenPiInfo))
        let type = '19_2'
        this.$navigateTo("/house/applyApprove?id=1&house_id=" + this.user_detail.id + "&type=" + type)
        // uni .showModal({
        //   title: "提示",
        //   content: `是否设置为无效？ `, 
        //   succes s: fuction (res) {
        //     if (res.confirm) {
        //       let form = {
        //          content: "",
        //         client_id: that.user_detail.id,
        //       };
        //       that.$ajax.post("/qywx/client/update_invalid", form, (res) => {
        //         if (res.statusCode === 200) {
        //           that.getDataDetail();
        //         } else {
        //           uni.showToast({
        //             title: res.data.message,
        //             icon: "none",
        //           });
        //         }
        //       });
        //     }
        //   },
        // });
      }
      if (e.id == 2) {
        // this.$navigateTo(`/customer/choose_friend?id=${this.user_detail.id}`)
        this.$navigateTo(
          `/customer/transfer_customer?id=${this.user_detail.id}`
        );
      }
      if (e.id == 3) {
        this.toShenpi()
        // this.$navigateTo(`/customer/create_audit?id=${this.user_detail.id}`);
      }
      if (e.id == 4) {
        this.$navigateTo(`/customer/qw_friend_list?id=${this.user_detail.id}`);
      }
      if (e.id == 5) {
        this.giveup_reason = true
      }
      this.isShowSetting = false;
    },
    toShenpi () {
      if (!this.has_roles) {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: 'none'
        })
        return
      }
      if (this.user_detail.is_state == 1) {
        uni.showToast({
          title: "该客户正在审核中",
          icon: 'none'
        })
        return
      }
      let shenPiInfo = {
        is_del: this.user_detail.is_del,
        is_state: this.user_detail.is_state,
        state_list: this.user_detail.state_list,
        stateList: this.stateList
      }
      uni.setStorageSync("shenpi", JSON.stringify(shenPiInfo))
      this.$navigateTo("/house/applyApprove?id=1&house_id=" + this.user_detail.id)
    },
    // 客户转公海
    toSeas () {
      this.$ajax.post("/qywx/client/discard", { ids: this.user_detail.id + '', content: this.giveup_content }, (res) => {
        console.log(res);
        if (res.statusCode == 200) {
          uni.showToast({
            title: res.message || '操作成功',
            icon: "none"
          })
          this.giveup_reason = false
          this.getDataDetail()
        } else {
          uni.showToast({
            title: res.data.message || '操作失败',
            icon: "none"
          })
        }
      })
    },
    getLevelList () {
      this.$ajax.get("/admin/crm/level/list", {}, res => {
        if (res.statusCode == 200) {
          this.levelList = res.data
        }

      })
    },
    showAction () {
      if (!this.has_roles) {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: 'none'
        })
        return
      }
      let arr = ['A', "B", "C"]
      uni.showActionSheet({
        itemList: arr,
        success: (result) => {
          uni.showModal({
            title: '提示',
            content: '确认更改客户等级吗？',
            success: (res) => {
              if (res.confirm) {
                this.setCustomerLevel(arr[result.tapIndex])
              } else if (res.cancel) {
                console.log('用户点击取消');
              }
            }
          })
        },
        fail: (res) => {
          console.log(res.errMsg);
        }
      })
    },
    setCustomerLevel (level) {
      let level_id = ''
      this.levelList.map(item => {
        if (item.title == level) {
          level_id = item.id
        }
      })
      this.$ajax.post("/admin/crm/client/update_level", {
        id: this.user_detail.id,
        level_id
      }, res => {
        console.log(res);
        if (res.statusCode == 200) {
          uni.showToast({
            title: res.message || '操作成功',
            icon: "none"
          })
          this.getDataDetail()
        }
      })
    },
    onGoDemand () {
      if ((this.user_detail.all_follow_status == 0 && this.has_roles) || this.user_detail.all_follow_status == 1) {
        let url = `/customer/demand?id=${this.user_detail.id}&from=${this.from}&tracking=${this.user_detail.tracking ? this.user_detail.tracking.id : ''}`
        this.$navigateTo(url);
      } else {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: "none"
        })
      }

    },
    onClickRemind (e) {
      this.$ajax.post("/qywx/client_remind/create", e, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "操作成功",
            icon: "none",
          });
          this.getDataDetail();
          this.isShow = false;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    onClickRemind1 (e) {
      e.client_id = this.user_detail.id
      this.$ajax.post("/admin/crm/client/sms_remind", e, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "操作成功",
            icon: "none",
          });
          uni.setStorageSync("send_template1", JSON.stringify({ date: +new Date(), client_id: this.user_detail.id }))
          // this.getDataDetail();
          this.isShow = false;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    ongoTop () {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300,
      });
    },
    getCustomer () {
      var that = this;
      if (this.un_renling_status) {
        uni.showToast({
          title: "不可认领",
          icon: "none"
        })
        return
      }
      uni.showModal({
        title: "提示",
        content: `是否认领该客户？`,
        success: function (res) {
          if (res.confirm) {
            let form = {
              ids: that.user_detail.id + "",
            };
            that.$ajax.post("/qywx/client/get", form, (res) => {
              if (res.statusCode === 200) {
                uni.showToast({
                  title: "领取成功",
                  icon: "none",
                });
                that.$navigateTo(
                  `/customer/detail?id=${that.client_id}&form=2`
                );
                // that.getDataDetail();
              } else {
                uni.showToast({
                  title: res.data.message,
                  icon: "none",
                });
              }
            });
          }
        },
      });
    },
    goBack () {
      this.$navigateBack();
    },
    concatOwner () {
      this.setViewTel()
      // if (this.from == "fromOther") return
      // uni.makePhoneCall({
      //   phoneNumber: this.user_detail.mobile,
      //   success: () => {
      //     console.log("拨打经纪人电话");
      //   }, //仅为示例
      // });
    },
    // 预览图片
    preFollowImgs (filePath, index) {
      uni.previewImage({
        current: index,
        urls: filePath,
        indicator: 'number',
      })
    },
    // 跟进列表点赞功能
    followGiveLike (id) {
      this.$ajax.get(`/qywx/client_follow/click/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.follow_params.page = 1
          this.getFollowData(); // 获取最新数据
        }
      })
    },
    // 跟进列表赋值文本功能
    followCopy (item) {
      console.log(item, "复制");
      let contents = item.content;
      this.$copyText(contents, () => {
        uni.showToast({
          title: '复制成功',
          icon: "none",
        });
      })
    },
    setTop (item) {
      this.followSetTop(item)
    },
    hideShow () {
      console.log(123);
      for (let index = 0; index < this.follow_list.length; index++) {
        const element = this.follow_list[index];
        this.$set(element, "show", false)
        this.$forceUpdate()
      }
      // this.follow_list.map(item => item.show = false)
    },

    // 跟进列表设置为置顶
    followSetTop (item) {
      this.$ajax.get(`/qywx/client_follow/top/${item.id}`, {}, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: '置顶成功',
            icon: "none",
          });
          this.follow_params.page = 1;
          this.getFollowData(); // 获取最新数据
        }
      })
    },
    setHide () {
      this.follow_list.map(item => {
        item.show = false
        return item
      })
      this.$forceUpdate()
    },
    playVoice (item, index) {
      innerAudioContext && innerAudioContext.stop()
      // if (item.playing) {
      //   innerAudioContext.stop()
      //   return
      // }
      this.follow_list.map((i, idx) => {
        if (idx != index) {
          i.playing = false
        }
        return i
      })

      // setTimeout(() => {
      this.$set(this.follow_list[index], 'playing', true)

      // item.playing = true
      innerAudioContext.src = item.url
      console.log(this.follow_list, 11123, item.url);
      innerAudioContext.play()
      this.$forceUpdate()
      // }, 100);
    },
    // 显示置顶操作框
    showPinned (item, index) {
      console.log(item, index);
      this.follow_list.map(item => {
        item.show = false
        return item
      })
      item.show = true
      this.$forceUpdate()
      // this.$set(this.follow_list[index])
      // 如果id不同就全部隐藏操作框
      // if(this.PinnedStyle.id != item.id) {
      //   this.$refs.tops.map((item) => {
      //     item.$el.style.display = "none";
      //   })
      //   this.PinnedStyle.is_top = true;
      //   this.PinnedStyle.id = item.id; // 复制id
      // }
      // if(this.PinnedStyle.is_top) {
      //   this.$refs.tops[index].$el.style.display = "flex";
      //   this.PinnedStyle.is_top = false;
      //   document.body.addEventListener('click', this.eventPinned);
      // } else {
      //   this.$refs.tops[index].$el.style.display = "none";
      //   this.PinnedStyle.is_top = true;
      //   document.body.removeEventListener('click', this.eventPinned);
      // }
    },
    eventPinned () {
      console.log("123");
    }
  },
  onReachBottom () {
    if (this.is_tabs == 7) {
      this.loadmoreFollow();
    }
    if (this.is_tabs == 3) {
      this.loadmoreFollowNew();
    }
    if (this.is_tabs == 4) {
      this.LoadMoreLogs();
    }
    if (this.is_tabs == 5) {
      this.loadMoreGuiji();
    }
    if (this.is_tabs == 6) {
      this.loadMoreTel();
    }
  },
};
</script>
<style scoped lang="scss">
page {
  background: #f6f6f6;
  color: #2e3c4e;
}

.top {
  display: block;
  .top_img {
    background: #2f6aff;
    height: 274rpx;
    position: relative;
    .top_img_main {
      width: 100%;
      height: 41px;
      padding: 0 18px;
      box-sizing: border-box;
      position: absolute;
      bottom: 60px;
      left: 0px;
      .top_img_follow {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 100%;
        background-color: #5f8cff;
        border-radius: 5px;
        padding: 0 14px;
        box-sizing: border-box;
        .top_img_cus,
        .top_img_time {
          font-size: 14px;
          color: #ffffff;
        }
        .top_img_cus {
          .un_follow {
            color: #e15762;
          }
        }
        .top_img_time.drop_to_sea {
          color: #dcc05e;
        }
      }
    }
  }
  &-card {
    padding: 14px;
    background: #fff;
    border-radius: 5px;
    margin: 0 18px 10px;
    display: block;
    position: relative;
    &-title {
      justify-content: space-between;
      font-size: 14px;
    }
    &-left {
      .pic {
        width: 40px;
        height: 40px;
      }
    }
    &-right {
      margin-left: 12px;
      .setting {
        width: 20px;
        height: 20px;
        position: absolute;
        right: 14px;
      }
      &-top {
        align-items: center;
        &_name {
          font-size: 14px;
          color: #282a2f;
        }
        &_qw {
          height: 18px;
          width: 18px;
          margin-left: 4px;
        }
        &_sex {
          height: 16px;
          width: 16px;
          margin-left: 4px;
        }
        &_tracking {
          color: #3e8afd;
          margin-top: 20rpx;
          // margin: 0 6px;
          font-size: 12px;
        }
        &_level {
          color: #fff;
          padding: 2px 12px;
          border-radius: 2px;
        }
      }
      &-bottom {
        margin-top: 10px;
        color: #828488;
        font-size: 12px;
      }
    }
    &-bot-bottom {
      margin-top: 16px;
      width: 100%;
      border-top: 1px solid #eeeeee;
      padding-top: 12px;
      font-size: 12px;
      justify-content: space-between;
      color: #828488;
    }
    &-content {
      margin-top: 16px;
      &.tel {
        align-items: center;
      }
      &-label {
        color: #737373;
        &.top-card-content-label_top {
          min-width: 112rpx;
          align-self: flex-start;
        }
      }
      &-right {
        margin-left: 20px;
        &.has_follow {
          display: inline-block;
          .un_tong {
            position: relative;
            &.tong {
              &::after {
                background: #9edf2e;
              }
            }
            &::after {
              content: '';
              position: absolute;
              right: -10rpx;
              top: -2rpx;
              background: #f56c6c;
              width: 10rpx;
              height: 10rpx;
              border-radius: 50%;
            }
          }
        }
      }
      .textarea {
        margin-top: 8px;
        padding: 6px 16px;
        background: #f5f7fa;
        border: 1px solid #e8e8e8;
        height: 63px;
        width: 100%;
      }
      .placeholderClass {
        font-size: 13px;
        color: #bcbcbc;
      }
    }
    &-label-list {
      margin-top: 8px;
      flex-wrap: wrap;
      .item {
        margin-bottom: 4px;
        padding: 4px 12px;
        border-radius: 4px;
        background: #e8f1ff;
        color: #2f6aff;
        margin-right: 10px;
      }
    }
    .chakan {
      align-self: flex-start;
      font-size: 13px;
      color: #2f6aff;
      position: absolute;
      right: 14px;
    }
    .level {
      width: 60px;
      display: inline-block;
      margin-top: 10px;
      text-align: center;
      border-radius: 4px;
      background: #f1f4fa;
      color: #737373;
      height: 28px;
      line-height: 28px;
      font-size: 14px;
    }
    &-tabs {
      font-size: 14px;
      justify-content: space-around;
      &-item {
        position: relative;
        &.isactive {
          color: #2f6aff;
          &::after {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            content: '';
            height: 4px;
            background: #2d84fb;
            width: 30px;
            display: block;
            margin-top: 18px;
          }
        }
      }
    }
  }
}
.setting-pop {
  height: 400px;
  border-radius: 20px 20px 0px 0px;
  background: #f6f6f6;
  padding: 14px 18px 42px;
  &-top {
    align-items: center;
    background: #fff;
    justify-content: space-around;
    border-radius: 5px;
    padding: 22px 0;
    .item {
      align-items: center;
      font-size: 16px;
    }
    .item-img {
      width: 20px;
      margin-bottom: 6px;
      height: 20px;
    }
  }
  &-bottom {
    background: #fff;
    border-radius: 5px;
    padding: 22px 20px;
    margin-top: 16px;
    .item {
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      .left {
        align-items: center;
        font-size: 16px;
        .item-img {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
.istotop {
  width: 42px;
  height: 42px;
  position: fixed;
  right: 24px;
  bottom: 100px;
}
.content-box {
  background: #fff;
  padding: 14px;
  border-radius: 5px;
}
.card-border-line {
  padding-bottom: 14px;
  border-bottom: 1px solid #eeeeee;
}

.pic_box {
  display: flex;
  flex-direction: column;
  margin-bottom: 100px;
  background: none;
  padding: 0;
  .basic-info-box {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 14px;
    box-sizing: border-box;
    background-color: #ffffff;
    margin-top: 4px;
    margin-bottom: 20px;
    .basic-info-title {
      color: #2e3c4e;
      font-weight: bold;
      font-size: 14px;
    }
    .basic-info-content {
      display: flex;
      flex-direction: row;
      margin-top: 20px;
      .content-title {
        font-size: 13px;
        color: #737373;
        margin-right: 20px;
        white-space: pre;
      }
    }
  }

  .list {
    background: #fff;
    border-radius: 6px;
    padding: 14px;
    box-sizing: border-box;
    .info {
      font-size: 16px;
      margin-top: 25px;
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;
      .c2 {
        text-align: end;
        font-size: 12px;
        color: #828488;
        &.depart {
          align-items: flex-start;
          text-align: left;
          margin-top: 20rpx;
        }
        &.flex_start {
          text-align: left;
          align-items: flex-start;
        }
      }
      .time {
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
        color: #737373;
      }
      .pic {
        align-items: center;
        margin-top: 12px;
        image {
          width: 90rpx;
          height: 90rpx;
          min-width: 90rpx;
          border-radius: 50%;
          margin-right: 12px;
        }
      }
    }
  }
}
.follow-card {
  &-item {
    margin-bottom: 24px;
  }
  &-content {
    background: #fff;
    border-radius: 5px;
    margin-top: 14px;
    padding: 14px;
    font-size: 14px;
    color: #828488;
    line-height: 20px;
    span {
      color: #000;
    }

    .order {
      display: block;
      margin-right: 4rpx;
      .order_c {
        display: inline-block;
        padding: 0 8rpx;
        font-size: 24rpx;
        background: #e15762;
        color: #fff;
        border-radius: 2rpx;
      }
    }
    .username {
      font-size: 12px;
    }
  }
  .follow-card-picture {
    padding: 20rpx 28rpx 28rpx 120rpx;
    background-color: #fff;
    position: relative;
    .follow-picture-box {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      image {
        width: 113rpx;
        height: 113rpx;
        border-radius: 4rpx;
        margin-right: 16rpx;
        margin-bottom: 16rpx;
      }
    }
    .follow-giveLike-box {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-top: 6px;
      .follow-giveLike-main {
        display: flex;
        flex-direction: row;
        align-items: center;
        border-radius: 12px;
        padding: 5px 14px;
        box-sizing: border-box;
        background-color: #ebebeb;
        .follow-giveLike-zan {
          width: 14px;
          height: 14px;
          image {
            width: 100%;
            height: 100%;
          }
        }
        .follow-giveLike-user {
          display: flex;
          flex-direction: row;
          padding-left: 16rpx;
          margin-left: 16rpx;
          color: #8a929f;
          font-size: 24rpx;
          border-left: 1px solid #8a929f;
        }
      }
      .follow_giveLike-controls {
        display: flex;
        flex-direction: row;
        flex: 1;
        justify-content: flex-end;
        .follow_giveLike_icon {
          width: 28rpx;
          height: 28rpx;
          image {
            width: 100%;
            height: 100%;
          }
        }
        .follow_giveLike_more {
          width: 28rpx;
          height: 8rpx;
          margin-top: 10rpx;
          // line-height: 16rpx;
          image {
            width: 100%;
            height: 100%;
          }
        }
        .follow-Pinned-box {
          // display: none;
          padding: 16rpx;
          border-radius: 5px;
          background: #ffffff;
          box-shadow: 0px 0px 8px 0px #0000003f;
          position: absolute;
          bottom: -45px;
          right: 10px;
          .Pinned-text {
            color: #6c6f74;
            font-size: 28rpx;
            &:first-child {
              padding-bottom: 5px;
              // border-bottom: 1px solid #6c6f74;
              margin-bottom: 5px;
            }
          }
        }
      }
    }
  }
}
.lijibtn {
  border-radius: 5px;
  background: #3172f6;
  box-shadow: 0px 4px 10px 0px #3172f67f;
  position: fixed;
  color: #fff;
  line-height: 40px;
  text-align: center;
  left: 50%;
  bottom: 10%;
  transform: translate(-50%, 0);
  width: 300px;
}

.isborder {
  animation: glow 800ms ease-out infinite alternate;
}
@keyframes glow {
  0% {
    border-color: #2d84fb;
    box-shadow: 0 0 5px #2d84fb, inset 0 0 5px rgba(221, 225, 233, 1), 0 1px 0 #2d84fb;
  }
  100% {
    border-color: rgba(221, 225, 233, 1);
    box-shadow: 0 0 20px #2d84fb, inset 0 0 10px rgba(221, 225, 233, 1), 0 1px 0 #2d84fb;
  }
}

.footer_btn_group {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9;
  background-color: #fff;
  padding: 12px 24px;
  .option_btn {
    display: block;
    text-align: center;
    .more_con {
      align-items: center;
      padding: 0 20rpx;
    }
    .more {
      display: block;
      width: 6rpx;
      font-size: 44rpx;
      transform: rotate(90deg);
      // line-height: 10rpx;
      // overflow: hidden;
      // word-wrap: break-word;
      // line-height: 18rpx;
      // padding: 6rpx 0;
    }
    &.option_btn_cloumn {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 32rpx;
      color: #8a929f;
      image {
        width: 45rpx;
        height: 34rpx;
      }
    }

    ~ .option_btn {
      margin-left: 24rpx;
    }
    ::v-deep .my-btn.big {
      padding: 0 16rpx;
    }
  }
}
.l-title {
  font-size: 16px;
  color: #2e3c4e;
}
.top-card.fixed {
  position: sticky;
  z-index: 8;
  top: 0;
}
.tag_input,
.top-card-label-list {
  margin-top: 20px;
  // border: 1px solid #dde1e9;
  border-radius: 4px;
  align-items: center;
  // padding: 9px 12px;
  flex-wrap: wrap;
  .tag_item {
    margin-bottom: 4px;
    padding: 4px 12px;
    border-radius: 4px;
    background: #e8f1ff;
    color: #2f6aff;
    margin-right: 10px;
    // padding: 8px 20px;
    // text-align: center;
    // margin-right: 10px;
    // // margin-left: 10px;
    // margin-bottom: 12px;
    // background: #fff;
    // border-radius: 4px;
    // border: 1px solid #eee;
    // min-width: 80px;
    // color: #8d9099;
    position: relative;
    overflow: hidden;
    &.checked {
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
      background: #fff;
    }
    .tag_t {
      font-size: 14px;
      margin-bottom: 10rpx;
    }
    .tag_b {
      font-size: 14px;
    }

    .checked {
      position: absolute;
      right: 0;
      top: 0;
      width: 32rpx;
      height: 24rpx;
      image {
        width: 100%;
        height: 100%;
      }
    }
  }
  input {
    width: 100%;
    font-size: 14px;
  }
}
.reason {
  width: 80vw;
  height: 400rpx;
  margin: calc(100% - 200rpx) auto;
  padding: 40rpx;
  border-radius: 20rpx;
  background: #fff;
  position: relative;
  .close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    font-size: 48rpx;
  }
  textarea {
    margin-top: 8px;
    padding: 6px 16px;
    box-sizing: border-box;
    background: #f5f7fa;
    border: 1px solid #e8e8e8;
    height: 280rpx;
    width: 100%;
  }
  .btn {
    display: inline-block;
    padding: 20rpx 20rpx;
    background: #2d84fb;
    color: #fff;
    margin: 20rpx auto 0;
  }
}
.follow-config-main {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
  justify-content: space-between;
  .InstalledTop {
    padding: 0px 4px;
    border-radius: 2px;
    background: #2d84fb;
    color: #ffffff;
    font-size: 12px;
  }
  .follow-config-titel {
    display: flex;
    flex-direction: row;
    color: #2e3c4e;
    font-size: 14px;
    font-weight: bold;
  }
}
.follow-box-content {
  margin-bottom: 10px;
}
.redInfo {
  color: #ff0000;
}
.blueInfo {
  color: #3d91ff;
}
.voice {
  background: #2d84fb;
  width: 300rpx;
  image {
    width: 40rpx;
    height: 40rpx;
  }
  text {
    color: #fff;
    margin-left: 10rpx;
  }
}
.setting_level {
  width: 80rpx;
  height: 40rpx;
  position: absolute;
  right: 28rpx;
}
.un_level {
  background: #bcbcbc;
  color: #8a929f;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  position: absolute;
  right: 28rpx;
}
.top-card-content-act {
  width: 140rpx;
  margin-left: 40rpx;
  font-size: 22rpx;
  margin-top: 10rpx;
  padding: 4rpx 8rpx;
  border: 1rpx solid #2d84fb;
  color: #2d84fb;
  &.address {
    color: #828488;
    border: none;
    width: auto;
  }
}
.top-card-content-last_follow {
  color: #828488;
  margin-top: 10rpx;
  margin-left: 40rpx;
  line-height: 1.5;
}

.p_con {
  padding: 40rpx;
  background: #fff;
  border-radius: 8rpx;
  position: relative;
  padding-top: 140rpx;
  width: 80vw;
  /* height: 50vh; */
  .title {
    font-size: 40rpx;
    font-weight: 600;
    padding: 40rpx 0;
    text-align: center;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    text-align: center;
  }
  .p_content {
    .p_item {
      .label {
        margin-right: 5rpx;
      }
    }
    .btns {
      margin-top: 200rpx;
      .btn {
        background: #2d84fb;
        padding: 30rpx 0;
        color: #fff;
        border-radius: 10rpx;
      }
    }
  }
}
.option_btn.disabled {
  ::v-deep .my-btn.primary {
    background: rgba(45, 132, 251, 0.3);
    color: rgba(45, 132, 251, 0.8);
  }
}
</style>
