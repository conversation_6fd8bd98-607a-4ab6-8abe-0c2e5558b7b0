<template>
  <view class="tixing">
    <view class="pat48 palr48">
      <!-- <view class="flex-row justify-center items-center">
        <text class="title">添加提醒</text>
      </view> -->
      <!-- <view class="check">
        <radio-group @change="radioChange" class="group">
          <view class="radio-item flex-row" v-for="(item, index) in range" :key="index">
            <radio
              :value="item.value"
              :checked="item.value + '' === params.type + ''"
              color="#2d84fb"
            />
            <view class="radio_text">{{ item.name }}</view>
          </view>
        </radio-group>
      </view> -->
      <view class="flex-row">
        <view class="flex-1 con">
          <my-input
            v-model="params.content"
            size="small"
            type="textarea"
            height="160rpx"
            border
            placeholder="请输入"
            @input="handleInput"
            :disabled="params.type == 2"
          ></my-input>
          <view class="con_length"> {{ params.content.length }}/{{ textLen }} </view>
        </view>
      </view>
    </view>
    <view class="date">
      <scroll-view
        scroll-x
        class=""
        scroll-with-animation
        :scroll-into-view="'i' + (current_index > 0 ? current_index - 1 : current_index)"
      >
        <!-- <view class="date_box flex-row"> -->
        <view
          class="date_item"
          :class="{ active: currentDate == date.fullDate }"
          v-for="(date, index) in date_range"
          :key="index"
          :id="'i' + index"
          @click="changeDate(date, index)"
        >
          <view class="date_name"> {{ index == 0 ? '今天' : date.weekDate }} </view>
          <view class="date_value"> {{ date.shortDate }} </view>
        </view>
        <!-- <view class="date_item">
            <view class="date_name"> 周四 </view>
            <view class="date_value"> 6月16日 </view>
          </view>
          <view class="date_item">
            <view class="date_name"> 周四 </view>
            <view class="date_value"> 6月16日 </view>
          </view>
          <view class="date_item">
            <view class="date_name"> 周四 </view>
            <view class="date_value"> 6月16日 </view>
          </view>
          <view class="date_item">
            <view class="date_name"> 周四 </view>
            <view class="date_value"> 6月16日 </view>
          </view>
          <view class="date_item">
            <view class="date_name"> 周四 </view>
            <view class="date_value"> 6月16日 </view>
          </view>
          <view class="date_item">
            <view class="date_name"> 周四 </view>
            <view class="date_value"> 6月16日 </view>
          </view> -->
        <!-- </view> -->
      </scroll-view>
    </view>
    <view class="pab48 palr48">
      <view class="t_time flex-row items-center">
        <view
          class="t_time_item"
          :class="{ active: currentTimeIndex == index, disabled: item.disabled }"
          v-for="(item, index) in timeArr"
          :key="index"
          @click="changeCurrentTime(index)"
        >
          {{ item.value}}
        </view>
      </view>

      <myButton type="primary" :round="false" :loading="loading" @click="submitTixing" class="onbtn"
        >提交提醒</myButton
      >
    </view>
  </view>
</template>

<script>
import myInput from './components/myInput'
import myButton from './components/myButton'
// import tabs from '@/components/ui/Tabs'
export default {
  name: 'follow',
  components: {
    myInput,
    myButton,
    // tabs,
  },
  data() {
    return {
      loading: false,
      params: {
        id: '',
        content: '',
        type: '1',
        ptime: '',
      },
      range: [
        { value: '1', name: '公众号提醒' },
        { value: '2', name: '短信提醒' },
      ],
      date_range: [],
      currentDate: '',
      currentTimeIndex: 1,
      currentTime: '',
      timeArr: [],
      current_index: 1,
      textLen: 40, // 字数限制
    }
  },
  onLoad(options) {
    this.params.id = options.remind_id
    this.getRemindData(options.remind_id)
    this.getDateArray()
  },
  beforeDestroy() {},
  methods: {
    // 获取提醒数据
    getRemindData(id) {
      this.$ajax.get(`/admin/house/privateHouseMsgTimeAndSms/${id}`,{},(res) => {
        if (res.statusCode === 200) {
          this.params.content = res.data.sms_content
          let timeArr =res.data.list
          this.timeArr =[]
          if (timeArr && timeArr.length) {
             timeArr.map(item=>{
               let obj ={
                 name:item,
                 value:item
               }
               this.timeArr.push(obj)
             })
            if (this.currentTimeIndex < 0) {
              this.currentTimeIndex = 1
            }
            this.currentTime = this.timeArr[this.currentTimeIndex].value
          }
          if (res.data.last_msg.id) {
            uni.showToast({
              title:"您有已设置的提醒 不能重复设置",
              icon:'none'
            })
            // this.params.type = res.data.last_msg.type
            // this.currentDate = res.data.last_msg.ptime.substring(0, 10)
            // let time = res.data.last_msg.ptime.substring(10)
            // res.data.list.map((item, index) => {
            //   if (time.indexOf(item) != -1) {
            //     this.currentTimeIndex = index
            //   }
            // })
            // if (res.data.last_msg.type == 1) {
            //   this.params.content = res.data.last_msg.content
            // }
          }
        }
      })
    },
    // textArea 设置字数限制
    handleInput(e) {
      // /[a-zA-Z0-9_\u4e00-\u9fa5]{0,40}/g
      // let reg = new RegExp('[a-zA-Z0-9\s\S\u4e00-\u9fa5]{0,40}', 'g')
      let reg = new RegExp('.{0,40}', 'g')
      let text = e.match(reg)[0]
      this.$nextTick(() => {
        this.$set(this.params, 'content', text)
      })
    },
    // 组合最近7天的日期
    getDateArray() {
      this.date_range = []
      let arr = [0, 1, 2, 3, 4, 5, 6]
      arr.map((item) => {
        this.date_range.push(this.getDay(item))
      })
      if (this.current_index < 0) {
        this.current_index = 1
      }
      this.currentDate = this.date_range[this.current_index].fullDate
    },
    // 获取几天前 几天后 的日期 传递正数时是几天后的 参数为整数 返回对象  年月日   月日 和周几 因为传递给后端需要年所以加了个fullDate 带年的日期
    getDay(day) {
      var today = new Date()
      var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day
      today.setTime(targetday_milliseconds) //注意，这行是关键代码

      var tYear = today.getFullYear()

      var tMonth = today.getMonth()
      var tDate = today.getDate()
      let weekDay = this.doHandleWeek(today.getDay())
      tMonth = this.doHandleMonth(tMonth + 1)
      tDate = this.doHandleMonth(tDate)

      return {
        fullDate: tYear + '-' + tMonth + '-' + tDate,
        shortDate: tMonth + '月' + tDate + '日',
        weekDate: weekDay,
      }
    },
    // 不足十 补0
    doHandleMonth(month) {
      var m = month

      if (month.toString().length == 1) {
        m = '0' + month
      }
      return m
    },
    // 设置星期
    doHandleWeek(num) {
      let week = ''
      switch (num) {
        case 0:
          week = '周日'

          break
        case 1:
          week = '周一'

          break
        case 2:
          week = '周二'

          break
        case 3:
          week = '周三'

          break
        case 4:
          week = '周四'

          break
        case 5:
          week = '周五'

          break
        case 6:
          week = '周六'

          break
        default:
          week = ''
          break
      }
      return week
    },
    // 更改日期
    changeDate(item, index) {
      this.current_index = index
      if (index == 0) {
        // 当前几点
        let nowTime = new Date().getHours()
        let curIndex = 0
        this.timeArr.map((item, idx) => {
          if (item.value.split(':')[0] <= nowTime) {
            item.disabled = true
            curIndex = idx
          }
          return item
        })
        this.currentTimeIndex = curIndex == this.timeArr.length - 1 ? -1 : curIndex + 1
      } else {
        this.timeArr.map((item) => (item.disabled = false))
      }

      this.currentDate = item.fullDate
    },
    changeCurrentTime(index) {
      if (this.timeArr[index].disabled) return
      this.currentTimeIndex = index
      this.currentTime = this.timeArr[index].value
    },
    
    // 提交提醒
    /**
     * 组合提醒时间
     * id:房源id
     * ptime:提醒时间 年-月-日 小时：分钟
     * type:提醒方式 1 公众号 2 短信
     * content:提醒内容
     */
    submitTixing() {
      if (!this.params.content) {
        uni.showToast({
          title: '请输入提醒内容',
          icon: 'none',
        })
        return
      }
      let params = Object.assign({}, this.params)
      params.ptime = this.currentDate + ' ' + this.currentTime
      this.loading = true
      let api = '/admin/house/addPrivateHouseMsg'
      this.$ajax
        .post(api, params,(res) => {
          this.loading = false
          if (res.data.status === 200) {
            uni.showToast({
              title: '提交成功',
              icon: 'success',
            })
          } else {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
            })
          }
        })
        .catch((err) => {
          console.log(err)
          this.loading = false
        })
    },
  },
}
</script>

<style scoped lang="scss">
.tixing {
  .pat48 {
    padding-top: 48rpx;
  }
  .pab48 {
    padding-bottom: 48rpx;
  }
  .palr48 {
    padding-left: 48rpx;
    padding-right: 48rpx;
  }
  width: 100vw;
  // padding: 48rpx;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  background-color: #fff;
  position: relative;
  .close {
    position: absolute;
    top: 36rpx;
    font-size: 28rpx;
    left: 20rpx;
    color: #8a929f;
    padding: 20rpx;
  }
  .con {
    text-align: left;
    position: relative;
    font-size: 28rpx;
    color: #8a929f;
    .con_length {
      position: absolute;
      right: 15rpx;
      bottom: 6rpx;
      color: #999;
    }
  }
  .check {
    margin-bottom: 24rpx;
  }
  .tips {
    color: #fb656a;
    font-size: 22rpx;
    padding: 24rpx 48rpx;
  }
  > .flex-row {
    // align-items: center;
    // margin-bottom: 24rpx;
    font-size: 24rpx;
  }
  .title {
    font-size: 40rpx;
    font-weight: bold;
    margin-bottom: 48rpx;
    // padding-left: 12rpx;
    line-height: 1;
    // border-left: 6rpx solid $color-primary;
  }
  .label {
    flex-shrink: 0;
    width: 120rpx;
    margin-right: 12rpx;
  }
}
.group {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
::v-deep .uni-radio-input {
  width: 32rpx;
  height: 32rpx;
}
.radio_text {
  font-size: 28rpx;
  color: #999;
}
.date {
  padding-left: 48rpx;
  background: #ffffff;
  box-shadow: 0px 2px 8px -3px rgba(0, 0, 0, 0.08);
  white-space: nowrap;

  // margin-top: 24rpx;
  // .date_box {
  //   display: inline-block;
  //   white-space: nowrap;
  .date_item {
    display: inline-block;
    color: #999;
    text-align: center;
    padding: 24rpx 5rpx;
    margin-right: 40rpx;
    &.active {
      position: relative;
      .date_name {
        font-weight: 600;
      }
      .date_value,
      .date_name {
        color: #2d84fb;
      }
      &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 20rpx;
        right: 20rpx;
        height: 4rpx;
        background: #2d84fb;
      }
    }

    .date_value {
      margin-top: 8rpx;
      font-size: 22rpx;
      color: #8a929f;
    }
    // }
  }
}
.t_time {
  flex-wrap: wrap;
  margin-top: 48rpx;
  .t_time_item {
    padding: 8rpx 12rpx;
    background: #f8f8f8;
    border-radius: 4rpx;
    font-size: 22rpx;
    color: #8a929f;
    margin-right: 16rpx;
    margin-bottom: 24rpx;
    &.active {
      color: #2d84fb;
    }
    &.disabled {
      background: #f1f1f1;
    }
  }
}
.onbtn {
  position: fixed;
  bottom: 200rpx;
  left: 50%;
  transform: translateX(-50%);
}
</style>
