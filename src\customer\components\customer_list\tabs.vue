<template>
<view class="container">
    <view class="tabs">
	    <tabBar :tabs="list" :fixed-top="false" :equispaced="false" :now-index="curTabIndex" @click="handleTabClick" @scrolltolower="handleScrolltolower"></tabBar>
    </view>
    <view class="tabs-btn">
        <image src="/static/img/desc_order.png" class="tabs-btn-img"></image>
    </view>
</view>
</template>

<script>
import tabBar from '@/components/tabBar.vue';
export default {
    components: {
        tabBar
    },
    props: {
        current: { type: String, default: 'my' }
    },
    data() {
        return {
            curTabIndex: 0,
            list: []
        }
    },
    created() {
        this.list = this.getTabs();
    },
    methods: {
        getTabs(){
            return [
                {index:0,description:'流转客'}
            ]
        },
		handleTabClick(e){
            this.curTabIndex = e.index;
			console.log(e)
		},
        handleScrolltolower(){
            console.log('scrolltolower')
        }
    }
}

</script>

<style scoped lang="scss"> 
.container{
    display: flex;
    flex-direction: row;
    height: 96rpx;
    background-color: #fff;
    .tabs{
        position: relative;
        flex: 1;
        overflow: hidden;
        &::after{
            content: " ";
            position: absolute;
            z-index: 1;
            top: 0;
            right: 0;
            width: 40rpx;
            height: 100%;
            background: linear-gradient(270deg, rgba(246, 246, 246, 0.70) 0%, rgba(255, 255, 255, 0.00) 100%);
            /* visibility: hidden; */
        }
    }   
    .tabs-btn{
        width: 96rpx;
        padding-left: 16rpx;
        display: flex;
        flex-direction: row;
        align-items: center;
        .tabs-btn-img{
            width: 48rpx;
            height: 48rpx;
        }
    }
}
::v-deep .nav-box{
    .nav-list{
        height: 96rpx;
        .nav-item {
            width: auto;
            padding: 0 32rpx;
            margin: 0;
            font-size: 32rpx;
            height: 100%;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            &.active {
                color: #007aff;
                &:before{
                    height: 6rpx;
                    width: 32rpx;
                }
            }
        }
    }
}
</style>