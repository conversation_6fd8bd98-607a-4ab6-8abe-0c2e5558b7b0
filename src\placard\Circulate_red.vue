<template>
    <view class="masking">
        <view class="background">
            <view class="backcloth">
                <view>
                    <text>T+楼盘线上派单系统</text>
                    <text style="font-size: 44rpx;margin-left: -65rpx;">恭喜你获得阅读红包</text>
                </view>
                <image src="./photo/red.png">

                </image>
            </view>
        </view>
        <view class="foot">
            <view class="foot_left">
                <view class="publicity_Icon">
                    <view class="publicity">
                        <image src="./photo/publicity.png"></image>
                    </view>
                    <text>关注</text>
                    <view class="publicity">
                        <image src="./photo/billboard.png"></image>
                    </view>
                    <text>榜单</text>
                    <view class="publicity">
                        <image src="./photo/share.png"></image>
                    </view>
                    <text>分享</text>
                    <view class="publicity">
                        <image src="./photo/billfold.png"></image>
                    </view>
                    <text>钱包</text>
                </view>
            </view>
            <view class="foot_right">
                <!-- <view class="call_up">
                        拨打电话
                    </view> -->
                <button type="primary">拨打电话</button>
            </view>
        </view>
        <view>
            12345678855
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {

        }
    },
}
</script>
<style scoped lang="scss">
.masking {
    width: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    position: fixed;
}

.background {
    width: 100%;
    height: 1350rpx;
    background-image: url(./photo/picture.png);
    background-repeat: no-repeat;
    background-size: cover;
    // background-color: aqua;
    position: fixed;

    .backcloth {
        width: 100%;
        // height: 1000rpx;
        height: 1350rpx;
        background-color: rgba(0, 0, 0, 0.6);
        position: relative;

        view {
            color: #E5CD9F;
            position: absolute;
            margin-top: 300rpx;
            margin-left: 250rpx;
            z-index: 99;
            line-height: 90rpx;
        }

        image {
            width: 80%;
            height: 1000rpx;
            margin: 0 auto;
            margin-top: 220rpx;
        }
    }
}

.foot {
    width: 100%;
    height: 190rpx;
    // background-color: rgba(163, 153, 43, 0.6);
    display: flex;
    flex-wrap: wrap;
    margin-top: 1350rpx;

    .foot_left {
        width: 50%;
        height: 190rpx;
        // background-color: crimson;

        .publicity_Icon {
            width: 320rpx;
            height: 100rpx;
            // background-color: chocolate;
            margin: 0 auto;
            display: flex;
            flex-wrap: wrap;
            align-content: space-between;
            margin-top: 45rpx;

            text {
                color: #324157;
                font-size: 11px;
                margin-left: 10rpx;
                margin-top: 10rpx;
            }
        }

        .publicity {
            width: 60rpx;
            height: 60rpx;

            // border: 1px dashed #7c8084;
            // background-color: aquamarine;
            image {
                width: 100%;
                height: 100%;
            }
        }
    }

    .foot_right {
        width: 50%;
        height: 190rpx;

        // background-color: rgb(50, 220, 20);
        // .call_up {
        //     width: 300rpx;
        //     height: 100rpx;
        //     margin: 50rpx auto;
        //     background-color: #2D84FB;
        //     border-radius: 10rpx;
        //     text-align: center;
        //     color: #fff;
        //     font-size: 35rpx;
        //     line-height: 85rpx;
        //     z-index: 1;
        // }

        button {
            width: 300rpx;
            height: 100rpx;
            margin: 50rpx auto;
            z-index: 1;
        }
    }
}
</style>