<template>
  <view class="search flex-row">
    <view class="input-box flex-row flex-1" @click="$emit('click', $event.detail.value)">
      <icons type="ic_sousuo" color="#8a929f" size="32"></icons>
      <input
        :value="value"
        :focus="focus"
        @focus="is_focus = true"
        @blur="onBlur"
        @input="onInput"
        type="text"
        :placeholder="placeholder"
        @confirm="$emit('confirm', $event.detail.value)"
      />
      <view @click="$emit('input', '')">
        <icons v-if="clearable && value && is_focus" type="qingchu" size="32"></icons>
      </view>
    </view>
    <slot></slot>
  </view>
</template>

<script>
import icons from '@/components/my-icon'
export default {
  name: 'Search',
  components: {
    icons,
  },
  props: {
    value: String,
    placeholder: String,
    focus: Boolean,
    clearable: {
      type: Boolean,
      default: false,
    },
    delay: {
      type: Number,
      default: 300,
    },
  },
  data () {
    return {
      is_focus: false,
    }
  },
  watch: {
    focus (val) {
      this.is_focus = val
    },
  },
  methods: {
    onInput (e) {
      if (this.handleInput) {
        clearTimeout(this.handleInput)
      }
      this.handleInput = setTimeout(() => {
        this.$emit('input', e.detail.value)
      }, this.delay)
    },
    onBlur () {
      setTimeout(() => {
        this.is_focus = false
      }, 320)
    },
  },
}
</script>

<style scoped lang="scss">
.search {
  align-items: center;
  padding: 24rpx 0;
  .input-box {
    align-items: center;
    height: 76rpx;
    border-radius: 40rpx;
    padding: 0 32rpx;
    background-color: #f8f8f8;
    input {
      height: 100%;
      width: 100%;
      padding: 20rpx;
      font-size: 28rpx;
    }
  }
}
</style>
