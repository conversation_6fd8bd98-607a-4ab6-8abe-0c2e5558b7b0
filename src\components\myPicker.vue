<template>
  <view class="picker-box">
    <view class="selecter flex-row" @click="showPicker">
      <view class="selecter-info">
        <view class="labels">
          <text
            class="selecter_label"
            v-for="(option, index) in options"
            :key="index"
            >{{ option.label }}</text
          >
        </view>
        <view class="contents" v-if="selected && selected.length > 0">
          <text
            class="selecter_content"
            v-for="(item, index) in selected"
            :key="index"
            >{{ item | formatSelectedOk }}</text
          >
        </view>
        <view class="contents" v-else>
          <text class="selecter_placeholder">请选择</text>
        </view>
      </view>
      <my-icon type="ic_into" color="#888" size="32rpx"></my-icon>
    </view>
    <my-popup ref="picker">
      <view class="my-picker">
        <view class="label-list flex-row">
          <view
            class="label"
            v-for="(option, index) in options"
            :key="index"
            :class="{ active: current === index }"
            @click="handleCheck(index)"
          >
            <text class="label_text">{{ option.label }}</text>
            <text class="value_name">{{ option | formatSelected }}</text>
          </view>
        </view>
        <view class="operation flex-row">
          <view class="tip">请选择{{ options[current].label }}</view>
          <view class="btn" @click="handleSelectted">确定</view>
        </view>
        <picker-view :value="options[current].value" @change="onChange">
          <picker-view-column
            v-for="(column, index) in options[current].range"
            :key="index"
          >
            <view
              class="picker-view-column-item"
              v-for="(item, idx) in column.list"
              :key="idx"
              >{{ item.name }}</view
            >
          </picker-view-column>
        </picker-view>
      </view>
    </my-popup>
  </view>
</template>

<script>
import myIcon from "../components/my-icon";
import myPopup from "../components/myPopup";
export default {
  components: {
    myIcon,
    myPopup,
  },
  data() {
    let options = [
      {
        label: "户型",
        value: [0, 0, 0],
        required: true,
        range: [
          {
            unit: "室",
            identifier: "shi",
            list: [
              {
                id: 1,
                name: "1室",
              },
              {
                id: 2,
                name: "2室",
              },
              {
                id: 3,
                name: "3室",
              },
              {
                id: 4,
                name: "4室",
              },
              {
                id: 5,
                name: "5室",
              },
            ],
          },
          {
            unit: "厅",
            identifier: "ting",
            list: [
              {
                id: 1,
                name: "1厅",
              },
              {
                id: 2,
                name: "2厅",
              },
              {
                id: 3,
                name: "3厅",
              },
              {
                id: 4,
                name: "4厅",
              },
            ],
          },
          {
            unit: "卫",
            identifier: "wei",
            list: [
              {
                id: 1,
                name: "1卫",
              },
              {
                id: 2,
                name: "2卫",
              },
              {
                id: 3,
                name: "3卫",
              },
            ],
          },
        ],
      },
      {
        label: "朝向",
        value: [],
        required: true,
        range: [
          {
            unit: "",
            identifier: "caoxiang",
            list: [
              {
                id: 1,
                name: "东",
              },
              {
                id: 2,
                name: "西",
              },
              {
                id: 3,
                name: "南",
              },
              {
                id: 3,
                name: "北",
              },
            ],
          },
        ],
      },
      {
        label: "楼层",
        value: [],
        required: true,
        range: [
          {
            unit: "",
            identifier: "louceng",
            list: [
              {
                id: 1,
                name: "-1层",
              },
              {
                id: 2,
                name: "1层",
              },
              {
                id: 3,
                name: "2层",
              },
              {
                id: 3,
                name: "3层",
              },
            ],
          },
          {
            unit: "",
            identifier: "floor",
            list: [
              {
                id: 1,
                name: "共1层",
              },
              {
                id: 2,
                name: "共2层",
              },
              {
                id: 3,
                name: "共3层",
              },
              {
                id: 3,
                name: "共4层",
              },
            ],
          },
        ],
      },
    ];
    return {
      selected: [],
      current: 0,
      options: options,
    };
  },
  filters: {
    formatSelected(option) {
      let value = "";
      option.value.forEach((item, index) => {
        value += option.range[index].list[item].name;
      });
      if (value) {
        return value;
      } else {
        return "请选择";
      }
    },
    formatSelectedOk(selected) {
      const res = selected.map((item) => item.name).join("");
      return res || "请选择";
    },
  },
  methods: {
    showPicker() {
      this.$refs.picker.show();
      // 初始化第一项
      this.handleCheck(0);
    },
    /**
     * 选择器选择后
     */
    onChange(e) {
      // 获取选中的索引
      this.options[this.current].value = e.detail.value;
      // 获取选中的值
      let selected = e.detail.value.map((item, index) => {
        let select_item = this.options[this.current].range[index].list[item];
        select_item.identifier = this.options[this.current].range[
          index
        ].identifier;
        return select_item;
      });
      this.options[this.current].selected = selected;
    },
    /**
     * 切换选择器选项
     */
    handleCheck(index) {
      this.current = index;
      // 初始化选中的索引为第一项
      if (this.options[index].value.length === 0) {
        let value_len = this.options[index].range.length;
        while (value_len) {
          this.options[index].value.push(0);
          value_len--;
        }
      }
      // 初始化选中项的value后执行一次onChange事件用来初始化选中的值
      this.onChange({ detail: { value: this.options[index].value } });
    },
    /**
     * 点击确定
     */
    handleSelectted() {
      // 判断必选项是否必需选择且没有选择
      let no_val = this.options.find(
        (item) => item.required && item.value.length === 0
      );
      if (no_val) {
        uni.showToast({
          title: `请选择${no_val.label}`,
          icon: "none",
        });
        return;
      }
      const selected = this.options.map((item) => item.selected);
      this.selected = selected;
      // const value = this.options.map(item=>item.value)
      // console.log(value)
      this.$refs.picker.hide();
      this.$emit("change", selected);
    },
  },
};
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}

.selecter {
  padding: 20rpx 0;
  justify-content: space-between;
  align-items: center;
  .labels {
    margin-bottom: 24rpx;
    display: block;
    .selecter_label {
      line-height: 1;
      font-size: 22rpx;
      padding: 0 6rpx;
      color: #666;
      ~ .selecter_label {
        border-left: 2rpx solid #666;
      }
    }
  }
  .contents {
    display: block;
    .selecter_placeholder {
      line-height: 1;
      padding: 0 10rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #999;
    }
    .selecter_content {
      line-height: 1;
      padding: 0 10rpx;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      ~ .selecter_content {
        border-left: 4rpx solid #333;
      }
    }
  }
}

.my-picker {
  background-color: #fff;
}
.label-list {
  .label {
    flex: 1;
    text-align: center;
    padding: 20rpx;
    border-top: 4rpx solid #fff;
    .label_text {
      font-size: 22rpx;
      margin-bottom: 6rpx;
      color: #666;
    }
    &.active {
      border-top: 4rpx solid $uni-color-primary;
      background-image: linear-gradient(
        0deg,
        rgba(246, 246, 246, 0) 0%,
        rgba(251, 101, 106, 0.1) 100%
      );
      .value_name {
        color: $uni-color-primary;
      }
    }
  }
}

.operation {
  padding: 24rpx 32rpx;
  align-items: center;
  background-color: #f5f5f5;
  .tip {
    flex: 1;
    text-align: center;
    font-size: 22rpx;
    color: #999;
  }
  .btn {
    color: $uni-color-primary;
  }
}
picker-view {
  height: 400rpx;
}
.picker-view-column-item {
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
}
</style>
