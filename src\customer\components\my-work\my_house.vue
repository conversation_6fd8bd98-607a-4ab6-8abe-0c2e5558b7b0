<template>
  <view style="width: 100%">
    <!-- 客户-->
    <view style="
        padding:0 32rpx;
        width: 100%;
        background-color: #fff;
        border-radius: 16rpx;
      ">
      <view class="search-box" @click="gotoSearch">
        <myIcon class="icon" type="ic_sousuo3x1" color="#bbb3b3" size="32rpx"></myIcon>
        <text style="text-wrap: nowrap;overflow-x: auto;padding-left: 16rpx;">如: 小区名称 / 业主电话 / 房源编号等</text>
      </view>
      <view style="
          display: flex;
          flex-direction: row;
          margin-top: 32rpx;
        ">
        <!-- tabs切换 -->
        <view v-for="(item, index) in houseLIst" :key="item.id" @tap="changeAct(item)" class="my_style_all">
          <view class="my-style-one" :class="{ 'active': act === item.id }">
            {{ item.name }}
          </view>
        </view>
        <!-- 本周 -->
        <view class="data_all" @click="weekerBtn">
          <view>
            <image src="../../../static/icon/index/data.png" style="width:32rpx;height:32rpx"></image>
          </view>
          <view class="weeker_data">
            {{ weekrName }}
          </view>
          <view>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path
                d="M3.52864 5.52864C3.78899 5.26829 4.2111 5.26829 4.47145 5.52864L8.00004 9.05723L11.5286 5.52864C11.789 5.26829 12.2111 5.26829 12.4714 5.52864C12.7318 5.78899 12.7318 6.2111 12.4714 6.47145L8.47145 10.4714C8.2111 10.7318 7.78899 10.7318 7.52864 10.4714L3.52864 6.47145C3.26829 6.2111 3.26829 5.78899 3.52864 5.52864Z"
                fill="#488AF6" />
            </svg>
          </view>
        </view>
      </view>
      <!-- 列表 -->
      <view>
        <view class="myhouser" v-for="item in houseLists" :key="item.id" @click="locationFn(item.location)">
          <view style="display: flex; flex-direction: row; align-items: center">
            <view>
              <image :src="item.icon" style="width: 100rpx; height: 100rpx"></image>
            </view>
            <view style="margin-left: 24rpx">{{ item.name }}</view>
          </view>
          <view style="display: flex; flex-direction: row">
            <view>{{ item.number }}</view>
            <view>
              <image src="../../../static/icon/index/箭头 .png" style="width: 32rpx; height: 32rpx"></image>
            </view>
          </view>
        </view>
      </view>
      <view v-if="searchshow">
        <view>
          <image src="../../../static/icon/index/Polygon 1.png" class="search_box_image"></image>
        </view>
        <view class="search_box">
          <view v-for="(item, index) in range" :key="item.value" class="search_text"
            :class="{ 'activee': item.value == value }" @tap="textFn(item.value, item.text)">{{ item.text }}</view>
        </view>
      </view>
    </view>
    <!-- 本周下拉框 -->
    <view class="zhezao" v-if="weekerShow">
      <view class="popbox">
        <view class="popbox_item" v-for="(item, index ) in navs" :key="item.id"
          :class="{ 'popbox_item_active': navIndex == item.id }" @click="checkIndex(index, item.name)">{{ item.name }}
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
import getDate from "./getDate .js";
export default {
  components: {
    myIcon
  },
  props: {
    houseLIst: { Array },
  },
  data() {
    return {
      searchshow: false,
      navIndex: 2, // 本周
      weekrName: '今天',
      weekerShow: false,// 控制本周的气泡框
      act: 17,
      navs: [
        { id: 1, name: '全部' },
        { id: 2, name: "今天" },
        { id: 3, name: "昨天" },
        { id: 4, name: "本周" },
        { id: 5, name: "上周" },
        { id: 6, name: "本月" },
        { id: 7, name: "上月" },
      ],
      houseLists: {},
      range: [
        { value: 0, text: "电话" },
        { value: 1, text: "编号" },
        { value: 2, text: "小区" },
      ],
      // 房源列表
      dataList: {
        parent_id: "",
        start_date: "",
        end_date: "",
      },
      myList: {},
      value: "0",
      mobiles: '',
      keywords: ''
    };
  },
  watch: {
    keywords: {
      handler(naval) {
        uni.setStorageSync('keywords', naval);
      },
      immediate: true,
    },
    mobiles: {
      handler(naval) {
        if (naval == '') {
          naval = '电话'
        }
        uni.setStorageSync('mobiles', naval);
      },
      immediate: true,
    },
    act: {
      handler(naval) {
        // 昨天
        // 昨天开始时间
        let endtime = getDate.getToday().endtime;
        //昨天结束时间
        let starttime = getDate.getToday().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        this.getUserHouse();
      },
      immediate: true,
    },
  },
  created() {
    uni.showLoading({
      title: "加载中",
    });

  },
  methods: {
    // 跳转房源列表
    gotoSearch() {
      this.$navigateTo(`/house/search?is_owner=2`)
    },
    // 搜索框
    inputFn() {
      this.searchshow = !this.searchshow
    },
    // 下拉框内容
    textFn(item, text) {
      this.searchshow = !this.searchshow
      this.value = item
      this.mobiles = text
      uni.setStorageSync('mobiles', this.mobiles);
      if (text != '') {
        this.keywords = ''
      }
    },
    // tab互斥效果
    changeAct(item) {
      console.log(item.id, '11111');
      // 激活样式是当前点击的对应下标--list中对应id
      this.act = item.id;
      if (this.act == item.id) {
        this.weekrName = '今天'
        this.navIndex = 2
      }
    },
    // 本周时间传入
    checkIndex(index, name) {
      // console.log(index+1, "000");
      this.navIndex = index + 1;
      this.weekrName = name
      this.weekerShow = !this.weekerShow
      if (index === 0) {
        this.dataList.start_date = "";
        this.dataList.end_date = "";
        this.getUserHouse();
      } else if (index === 1) {
        // 今天
        // 今天开始时间
        let endtime = getDate.getToday().endtime;
        //今天结束时间
        let starttime = getDate.getToday().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        // console.log(this.dataList.start_date, '昨天开始时间');
        this.getUserHouse();
      } else if (index === 2) {
        // 昨天
        // 昨天开始时间
        let endtime = getDate.getYesterday().endtime;
        //昨天结束时间
        let starttime = getDate.getYesterday().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        // console.log(this.dataList.start_date, '昨天开始时间');
        this.getUserHouse();
      } else if (index === 3) {
        // 本周
        // 本周开始时间
        let endtime = getDate.getCurrWeekDays().endtime;
        //本周结束时间
        let starttime = getDate.getCurrWeekDays().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        // console.log(this.dataList.start_date, '本周开始时间');
        this.getUserHouse();
      } else if (index === 4) {

        // 上周
        // 上周开始时间
        let endtime = getDate.getLastWeekDays().endtime;
        //上周结束时间
        let starttime = getDate.getLastWeekDays().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        // console.log(this.dataList.start_date, '上周开始时间');
        this.getUserHouse();
      } else if (index === 5) {
        // 本月
        // 本月开始时间
        let endtime = getDate.getCurrMonthDays().endtime;
        //本月结束时间
        let starttime = getDate.getCurrMonthDays().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        // console.log(this.dataList.start_date, '本月开始时间');
        this.getUserHouse();
      } else if (index === 6) {
        // 上月
        // 上月开始时间
        let endtime = getDate.getLastMonthDays().endtime;
        //上月结束时间
        let starttime = getDate.getLastMonthDays().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        console.log(this.dataList.start_date, "上月开始时间");
        this.getUserHouse();
      }
    },
    // scroll: function (e) {
    //   // console.log(e)
    //   // this.old.scrollTop = e.detail.scrollTop
    // },
    onConfirm() {
      this.getUserHouse();
    },
    // 点击跳转·1
    locationFn(item) {
      // console.log(111);
      this.$navigateTo(item);
    },
    itemFn(item) {
      this.$navigateTo(item);
    },
    // 控制本周气泡框
    weekerBtn() {
      this.weekerShow = !this.weekerShow
    },
    getUserHouse() {
      this.dataList.mobile = this.mobiles
      this.dataList.keywords = this.keyword
      // console.log(this.act,'99999');
      this.dataList.parent_id = this.act
      let params = Object.assign({}, this.dataList);
      if (!params.start_date) {
        delete params.start_date;
      }
      if (!params.end_date) {
        delete params.end_date;
      }
      this.$ajax.get(
        `/qywx/welcome/get_fixed_menu`,
        params,
        (res) => {
          console.log(res.data, "11列表");
          if (res.statusCode === 200) {
            // 列表
            this.houseLists = res.data;
            uni.hideLoading();
          } else {
            uni.hideLoading();
          }
        },
        () => {
          uni.hideLoading();
        }
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.search-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 80rpx;
  border-radius: 16rpx;
  padding: 0 24rpx;
  background-color: #f3f3f3;

  text {
    color: rgba(41, 44, 57, 0.4);
    margin-left: 3px;
  }
}

.zhezao {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  // z-index: 777;
}

.myhouser {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  // margin-top: 32rpx;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #F6F6F6;

  &.myhouser:last-child {
    border: none;
  }

}

.my-style-one {
  box-sizing: border-box;
  // padding: 16rpx 20rpx;
  width: 176rpx;
  height: 64rpx;
  text-align: center;
  line-height: 64rpx;
  background: #F6F6F6;
  font-size: 28rpx;
  border-radius: 8rpx;
  border: none;

}

// 我的房客
.active {
  box-sizing: border-box;
  // padding: 16rpx 20rpx;
  width: 176rpx;
  height: 64rpx;
  text-align: center;
  line-height: 64rpx;
  color: #488AF6;
  font-size: 28rpx;
  border-radius: 8rpx;
  background: #FFF;
  border: none;
}

.my_style_all {
  text-align: center;
  line-height: 64rpx;
  padding: 8rpx;
  border-radius: 8rpx;
  background: #F6F6F6;
  font-size: 28rpx;
  color: rgba(41, 44, 57, 0.40);
}

.popbox {
  position: absolute;
  top: 490rpx;
  left: 466rpx;
  background: #fff;
  width: 260rpx;
  // padding: 0 32rpx;
  border-radius: 16rpx;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.05);
}

.popbox_item {
  padding: 24rpx 0;
  background: #ffff;
  text-align: center;
  // border-bottom: 2rpx solid #F6F6F6;
}

.popbox_item_active {
  background: #e4e3e3;
}

.mywork_box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #F6F6F6;

  &.mywork_box:last-child {
    border: none;
  }
}

.weeker_data {
  margin: 0 10rpx;
  color: #488AF6;
}

.data_all {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  margin-left: 120rpx;
  width: 50%;
  position: relative;
}

.search_box_image {
  width: 36rpx;
  height: 30rpx;
  position: absolute;
  top: 360rpx;
  left: 150rpx;
}

.search_text {
  padding: 20rpx 32rpx;
  width: 100%
}

.search_box {
  position: absolute;

  top: 366rpx;
  left: 54rpx;
  background: #fff;
  width: 180rpx;
  margin-top: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0px 0px 40rpx 0px rgba(0, 0, 0, 0.10);
}

::v-deep .uni-input-placeholder {
  top: -6rpx !important;
}

::v-deep .uni-input-input {
  top: -6rpx !important;
}

.whole_shu {
  color: rgba(41, 44, 57, 0.40);
  font-size: 24rpx;
  font-weight: 400;
  line-height: 28rpx;

}

.whole_bai {
  width: 70%;
  height: 32rpx;
  line-height: 32rpx;
  color: rgba(41, 44, 57, 0.40);
}

.whole_input_left_name {
  color: rgba(41, 44, 57, 0.70);
  font-size: 28rpx;
  font-weight: 400;
  line-height: 32rpx;
  margin-right: 16rpx;
}

.whole_input_left {
  display: flex;
  flex-direction: row;

}

.whole_input {
  position: relative;
  overflow: auto;
  width: 100%;
  // height: 80rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  // margin: 0 16rpx;
  background: #F6F6F6;
  border-radius: 16rpx;
}

.activee {
  background: rgba(0, 0, 0, 0.03);
}


// 本周
.weeked {
  box-sizing: border-box;
  // padding: 6rpx 24rpx;
  width: 96rpx;
  height: 48rpx;
  border-radius: 8rpx;
  margin: 0 6rpx;
  font-size: 24rpx;
  line-height: 48rpx;
  background: #f8f8f8;
  display: inline-block;
  /* 必要，导航栏才能横向*/
}

.activite {
  box-sizing: border-box;
  // padding: 6rpx 24rpx;
  width: 96rpx;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  background: rgba(72, 138, 246, 0.2);
}

// 下拉
.updata {
  width: 160rpx;
  background-color: #fff;
}
</style>
