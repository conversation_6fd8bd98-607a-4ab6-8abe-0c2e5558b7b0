<template>
  <view class="list">
    <view class="title-bar">
      <view class="search-tab">
        <my-search
          width="287px"
          :placeholder="
            is_select_index == 0 ? '请输入客户手机号码' : '请输入楼盘名称'
          "
          @input="onInput"
        >
          <template v-slot:left>
            <picker
              style="font-size:24rpx;color:#0174ff;margin-right:20rpx"
              @change="bindPickerChange"
              @cancel="is_select = false"
              :value="is_select_index"
              :range="is_select_arr"
              range-key="description"
            >
              <view class="uni-input row" @click="is_select = true">
                {{ is_select_arr[is_select_index].description }}
                <myIcon
                  :type="is_select ? 'xiala' : 'shangla'"
                  size="24rpx"
                  color="#0173ff"
                  style="margin-left:10rpx"
                ></myIcon>
              </view>
            </picker>
            <my-icon type="ic_sousuo3x1" color="#999"></my-icon>
          </template>
          <template v-slot:right>
            <text class="xinzeng" @click="newAdd">新增</text>
          </template>
        </my-search>
        <!-- 导航 -->
      </view>
    </view>
    <tab-bar
      ref="tab_bar"
      :tabs="report_cates"
      :nowIndex="
        report_cates.findIndex((item) => Number(item.value) === params.type)
      "
      fixed_top
      top="100"
      @click="onClickCate"
    ></tab-bar>

    <view class="report-list">
      <report-item
        @open="reportDetail(item.id)"
        v-for="(item, index) in report_list"
        :report_item="item"
        :key="index"
        :status="item.status"
        @createFillPhone="createFillPhone"
        @sendMsg="sendMsg"
        @status1="followStatus"
        :is_case="is_case"
      ></report-item>
    </view>
    <load-more :status="load_status"></load-more>
    <backTop :scrollTop="scrollTop"></backTop>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import mySearch from "@/components/my-search";
import myIcon from "@/components/my-icon";
import reportItem from "@/components/report-item";
import myTag from "@/components/my-tag";
import tabBar from "@/components/tabBar";
import loadMore from "@/components/loadMore";
import backTop from "../components/backtop/components/back-top/back-top";
import { mapActions } from "vuex";
export default {
  components: {
    mySearch,
    myIcon,
    reportItem,
    myTag,
    tabBar,
    loadMore,
    backTop,
  },
  data() {
    return {
      report_list: [],
      build_category_list: [],
      reception_status: [],
      params: {
        type: "",
        page: 1,
        customer_phone: "",
        total: "",
        status: 1,
      },
      report_cates: [],
      load_status: "",
      statistics: [],
      report_tatol: 0,
      scrollTop: 0,
      is_select_index: 0,
      is_select: false,
      is_select_arr: [
        { value: 0, description: "手机号码" },
        { value: 1, description: "楼盘名称" },
      ],
      is_case: "",
      report_status_list: [],
    };
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  onLoad(options) {
    uni.setStorageSync("page_from", "broker");
    if (options) {
      this.params.type = parseInt(options.type);
      this.params.status = parseInt(options.type);
    }
    this.init();
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          // 匹配楼盘类型
          case "BUILD_CATEGORY":
            this.build_category_list = item.childs;
            break;
          case "CUSTOMER_RECEPTION_STATUS":
            this.reception_status = item.childs;
            break;
          case "REPORTED_STATUS":
            this.report_status_list = item.childs;
            break;
          case "CUSTOMER_REPORTED_STATUS":
            this.report_status = item.childs;
            break;
        }
      });
    });
  },
  methods: {
    ...mapActions(["getImToken", "getUserInfo"]),
    init() {
      this.getDataList();
      // 报备状态信息
    },
    getCates() {
      if (this.is_case === 1) {
        this.report_status_list = this.report_status_list.filter((item) => {
          return item.value != 1;
        });
      }
      this.report_cates = [
        { value: -1, description: "全部" },
        ...this.report_status_list,
      ];
      for (var i = 0; i < this.report_cates.length; i++) {
        this.report_cates[i].statistics = "";
        this.statistics.map((e) => {
          if (this.report_cates[i].value == e.status) {
            this.report_cates[i].statistics = e.total;
          }
        });
      }
    },
    getDataList() {
      this.load_status = "loading";
      this.params.status === -1 || this.params.type === -1
        ? delete this.params.status
        : (this.params.status = this.params.type);
      // if (this.params.status === -1 || this.params.type === -1) {
      //   delete this.params.status;
      // } else {
      //   this.params.status = this.params.type;
      // }
      if (this.params.page === 1) {
        this.report_list = [];
      }
      this.$ajax.get(
        "/client/customer/reported/search/broker",
        this.params,
        (res) => {
          this.load_status = "loadend";
          if (res.statusCode === 200) {
            this.statistics = res.data.statistics;
            // 计算客户总数
            this.statistics.forEach((item) => {
              this.report_tatol = this.report_tatol + parseInt(item.total);
            });
            this.getUserInfo({
              success: (res) => {
                this.is_case = res.data.is_case; // 是否是案场方
                this.getCates();
              },
            });
            this.report_list = this.report_list.concat(res.data.data);
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
              uni.showToast({
                title: "没有更多数据了",
                icon: "none",
              });
            }
          } else {
            uni.showToast({
              title: res.data.message || "没有更多数据了",
              icon: "none",
              success: () => {
                this.params.page = 1;
              },
            });
          }
        }
      );
    },
    reportDetail(id) {
      this.$navigateTo(`/report/report_detail?id=${id}`);
    },
    newAdd() {
      this.$navigateTo("/report/report_client?currentTel=1");
    },
    onClickCate(e) {
      // this.params.customer_phone = "";
      this.params.type = Number(e.value);
      this.params.status = this.params.type;
      this.params.page = 1;
      this.init();
    },
    onInput(e) {
      this.$debounce(this.oninputDebounce, 500)(e);
      // this.init();
    },
    oninputDebounce(e) {
      let params = {};
      this.is_select_index === 0
        ? (params = { customer_phone: e, build_name: "", page: 1 })
        : (params = { customer_phone: "", build_name: e, page: 1 });
      this.load_status = "loading";
      this.$ajax.get(
        "/client/customer/reported/search/broker",
        params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.report_list = res.data.data;
            this.statistics = res.data.statistics;
            // 计算客户总数
            this.getCates();
            if (res.data.data.length == 0) {
              this.load_status = "nomore";
              uni.showToast({
                title: "没有更多数据了",
                icon: "none",
              });
            }
          }
        }
      );
    },
    createFillPhone(e) {
      this.$ajax.post(
        "/client/customer/reported/fill/customer_phone",
        e,
        (res) => {
          if (res.statusCode === 200) {
            uni.showToast({
              title: "已补全",
            });
            this.params.page = 1;
            this.getDataList();
          } else {
            uni.showToast({
              title: res.data.message || "补全失败",
              icon: "none",
            });
          }
        }
      );
    },
    sendMsg(item) {
      this.getImToken();
      this.$navigateTo(`/im_list/msg_detail?to_id=${item.pu_id}`);
    },
    bindPickerChange(e) {
      this.is_select_index = e.detail.value;
      this.is_select = false;
    },
    followStatus(status, id) {
      var that = this;
      uni.showModal({
        title: "提示",
        content: "如报备有误可将该客户设置为无效，请谨慎操作！",
        confirmText: "确认",
        success: (res) => {
          if (res.confirm) {
            that.$ajax.post(
              "/client/customer/reported/audit/brokerCancel",
              { customer_reported_id: id },
              (res) => {
                if (res.statusCode === 200) {
                  uni.showToast({
                    title: "已无效",
                    icon: "none",
                  });
                  this.getDataList();
                } else {
                  uni.showToast({
                    title: res.data.message,
                    icon: "none",
                  });
                }
              }
            );
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.init();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #eee;
}
.title-bar {
  background: #fff;
}
.search-tab {
  background: #fff;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 9;
}
// 导航

.screen-tab {
  flex-direction: row;
  width: 100%;
  height: 80rpx;
  background-color: #fff;
  .screen-tab-item {
    flex: 1;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    text {
      margin-right: 10rpx;
      transition: 0.3s;
    }
  }
}

.report-list {
  padding-top: 168rpx;
}
.nodata-center {
}
.xinzeng {
  position: absolute;
  right: 48rpx;
  color: #0174ff;
}
</style>
