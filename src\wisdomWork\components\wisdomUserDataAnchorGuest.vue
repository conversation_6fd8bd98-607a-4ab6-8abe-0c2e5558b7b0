<template>
    <view>
        <view class="top">
            <view class="filters">
                <!-- <view class="filters-item">
                    <wisdomSearchSelect placeholder="日期" v-model="params.date_value" :datas="dateGroups" :params.sync="params" auto-popup @confirm="search" @clear="search"></wisdomSearchSelect>
                </view> -->
                <view class="filters-item">
                    <wisdomMemberSelect placeholder="成员" v-model="params.admin_id" @select="search"></wisdomMemberSelect>
                </view>
            </view>
        </view>

        <wisdomTable :api="getList" :headers="headers" ref="table"></wisdomTable>
    </view>
</template>

<script>
import wisdomSearchSelect from './wisdomSearchSelect';
import wisdomAnchorAccountSelect from './wisdomAnchorAccountSelect';
import wisdomAnchorPlatformSelect from './wisdomAnchorPlatformSelect';
import wisdomMemberSelect from './wisdomMemberSelect';
import wisdomTable from './wisdomTable';
import { getAnchorGuestData } from '@/common/utils/wisdom-work.js';
export default {
    props: {
        dateValue: { type: String, default: '' }
    },
    components: {
        wisdomSearchSelect,
        wisdomAnchorAccountSelect,
        wisdomAnchorPlatformSelect,
        wisdomMemberSelect,
        wisdomTable
    },
    data(){
        return {
            dateGroups: [{
                title: ' ', field: 'date_value', options: [
                    { label: '全部', value: '' },
                    { label: '今天', value: 'today' },
                    { label: '昨天', value: 'yestoday' },
                    { label: '本周', value: 'now_week' },
                    { label: '上周', value: 'last_week' },
                    { label: '本月', value: 'now_month' },
                    { label: '上月', value: 'last_month' },
                ]
            }],
            params: {
                date_value: '',
                start_date: '',
                end_date: '',
                admin_id: ''
            },
            headers: [
                { label: '主播', field: 'user_name', width: 130, fixed: true },
                { label: '留资总量', field: 'custom_num', width: 110 },
                { label: '公海客户', field: 'public_custom_num', width: 110 },
                { label: '私客', field: 'private_custom_num', width: 110 },
                { label: '潜客', field: 'potential_custom_num', width: 110 },
                { label: '废客', field: 'discard_custom_num', width: 110 },
                { label: '成交量', field: 'deal_custom_num', width: 110 },
                { label: '掉公客户', field: 'discard_custom_public', width: 110 },
                { label: '转公客户', field: 'transmit_custom_public', width: 110 },
                { label: '有效客户', field: 'effective_num', width: 110 },
                { label: '无效客户', field: 'invalid_num', width: 110 },
                { label: '暂缓客户', field: 'suspend_num', width: 110 },
                { label: 'A级客户', field: 'a_num', width: 110 },
                { label: 'B级客户', field: 'b_num', width: 110 },
                { label: 'C级客户', field: 'c_num', width: 110 },
            ],
            list: [],
        }
    },
    watch: {
        dateValue: {
            handler(val){
                this.params.date_value = val;
            },
            immediate: true
        }
    },
    methods: {
        async getList(page){
            const [start_date, end_date ] = this.$Utils.getDateRange(this.params.date_value, 'YYYY-MM-DD');
            const params = { ...this.params, start_date, end_date, page };
            delete params.date_value;
            const res = await getAnchorGuestData(params);
            return {
                list: (res.data || []),
                pageSize: res.per_page || 0
            }
        },
        loadData(){
            this.$refs.table.getList();
        },
        async search(){
            await this.$refs.table.search();
        }
    },
}
</script>

<style lang="scss" scoped>
@import "@/common/style/wisdom_work/wisdom_work_top_filters.scss";
</style>