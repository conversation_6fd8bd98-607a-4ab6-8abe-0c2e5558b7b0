<template>
    <myPopup :show="show"  @close="show = false">
        <view class="container">
            <view class="header">
                <view class="action cancle" @click.stop="cancle">取消</view>
                <view class="title">选择共享维护人</view>
                <view class="action confirm" @click.stop="confirm">确认</view>
            </view>
            <view class="body">
                <tCustomPickerView v-model="params.shared_admin_id" filterable :loading="loading" height="40vh" :datas="memberList" :map="{value:'id', label: 'user_name'}"></tCustomPickerView>
            </view>
        </view>
    </myPopup>
</template>
<script>
import myPopup from '@/components/myPopup';
import tCustomPickerView from '@/components/tplus/tCustomPickerView';
export default {
    name: 'addShareFollower',
    components: {
        myPopup, tCustomPickerView
    },
    data(){
        return {
            show: false,
            loading: false,
            params: {
                client_id: 0,
                shared_admin_id: ''
            },
            memberList: [],
            successFn: null,
            submiting: false
        }
    },
    created(){
        this.getMemberList();
    },
    methods: {
        getMemberList(){
            this.loading = true;
            this.$ajax.get("/qywx/home/<USER>", {}, (res) => {
                this.loading = false;
                if (res.statusCode === 200) {
                    this.memberList = res.data;
                } else {
                    uni.showToast({
                        title: res.data.message,
                        icon: "none",
                    });
                }
            });
        },
        open(client_id){
            this.params.client_id = client_id;
            this.params.shared_admin_id = '';
            this.show = true;
            return this;
        },
        onSuccess(fn){
            this.successFn = fn;
            return this;
        },
        cancle(){
            this.show = false;
        },
        confirm(){
            if(this.submiting){
                return;
            }
            if(!this.params.shared_admin_id){
                uni.showToast({
                    title: '请选择共享维护人',
                    icon: "none",
                });
                return false;
            }

            this.submiting = true;
            try{
                this.$ajax.post("/admin/crm/share_follow/add_share_follow", this.params, (res) => {
                    this.submiting = false;
                    if (res.statusCode === 200) {
                        if(res.data?.code == 0 && res.data?.msg){
                            uni.showToast({
                                title: res.data?.msg,
                                icon: "none",
                            });
                        }else{
                            uni.showToast({
                                title: res.data?.msg || "添加成功",
                            });
                        }
                        this.show = false;
                        this.successFn && this.successFn();
                    } else {
                        uni.showToast({
                            title: res.data.message,
                            icon: "none",
                        });
                    }
                });
            }catch(e){
                console.error(e);
                this.show = false;
            }
        }
    }
}


</script>
<style lang="scss" scoped>
.container{
    background: #fff;
    line-height: 1;
    
    .header{
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 46px;
        :after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            height: 1px;
            clear: both;
            border-bottom: 1px solid #e5e5e5;
            color: #e5e5e5;
            -webkit-transform-origin: 0 100%;
            transform-origin: 0 100%;
            -webkit-transform: scaleY(.5);
            transform: scaleY(.5);
        }
        .title{
            flex: 1;
            color: #999;
            text-align: center;
            display: inline-block;
            max-width: 50%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .action{
            padding: 0 14px;
            font-size: 17px;
            &.cancle{
                color: #888;
            }
            &.confirm{
                color: #007aff;
            }
        }
    }
}

</style>