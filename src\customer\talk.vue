<template>
  <view>
    <view style="height: 100vh" v-if="show">
      <view class="top">
        <tabBar class="tab" :fixedTop="false" :nowIndex="tab_type" :tabs="tab" @click="onClickTab">
        </tabBar>
        <view class="search_box row">
          <view class="search row">
            <view class="row input">
              <myIcon class="icon" type="ic_sousuo3x1" size="32rpx" color="#b0b0b0"></myIcon>
              <input
                class="c2"
                v-model="params.keywords"
                @confirm="onSearch"
                type="text"
                placeholder="请在这里输入"
              />
            </view>
          </view>
          <view class="add row" @click="addTalk"><text>+</text>新增</view>
        </view>
      </view>
      <view class="info">
        <view class="title c2 row" v-if="false">为您推荐最近检测到的关键词：<text>3</text>个</view>
        <view class="tab row" v-if="false">
          <view v-for="(item, index) in tabs" :key="index">
            <text @click="tabChange(index)" :class="{ active: current == index }">
              {{ item.name }}
            </text>
          </view>
        </view>
        <view class="list">
          <view class="deatil" v-for="item in talk_list" :key="item.id">
            <text>{{ item.title }}</text>
            <view class="val row">
              <text class="c2">{{ item.content }}</text>
              <view class="row push" @click="onSendMsg(item)">
                <image src="../static/customer/fs.png" mode="widthFix" />
                <text>发送</text>
              </view>
            </view>
            <view v-if="false" class="open row c2">
              展开1条
              <myIcon
                type="xiala"
                size="22rpx"
                color="#737373"
                style="margin: 4rpx 0 0 10rpx"
              ></myIcon>
            </view>
          </view>
          <load-more :status="load_status"></load-more>
          <!-- <view class="deatil">
            <text>12345</text>
            <view class="val row">
              <view class="pic row">
                <image src="../static/customer/xlsx.png" mode="aspectFill" />
                <view class="tit c2">
                  <view>图片.jpg</view>
                  <text>123.4k</text>
                </view>
              </view>
              <view class="row push">
                <image src="../static/customer/fs.png" mode="widthFix" />
                <text>发送</text>
              </view>
            </view>
            <view class="open row c2">
              展开1条
              <myIcon
                type="xiala"
                size="22rpx"
                color="#737373"
                style="margin: 4rpx 0 0 10rpx"
              ></myIcon>
            </view>
          </view> -->
        </view>
      </view>
    </view>
    <!-- <BottomBar @click="switchTab" :current="currentTabIndex"></BottomBar> -->
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
import tabBar from "@/components/tabBar";
import BottomBar from "./components/tabbar";
import loadMore from "@/components/loadMore.vue";
export default {
  components: {
    myIcon,
    tabBar,
    loadMore,
    BottomBar,
  },
  data () {
    return {
      tab: [
        // { description: "智能推荐", type: 0 },
        { description: "企业话术库", type: 1 },
        { description: "个人话术库", type: 2 },
      ],
      tab_type: 0,
      tabs: [{ name: "SCRM销售" }, { name: "销售管理" }, { name: "客户管理" }],
      current: 0,
      currentTabIndex: 1,
      params: {
        page: 1,
        type: 1,
        keywords: "",
        work_user_id: "",
      },
      talk_list: [],
      load_status: "",
      show: false
    };
  },
  onLoad (options) {
    console.log(options);
    if (options.website_id) {
      if (options.website_id == 176) {
        this.show = true
      } else {
        return
      }
    } else {
      return;
    }

    let token = uni.getStorageSync("wxwork_token");
    if (!token) {
      if (this.$isWxWork() == 'wxwork') {
        return
      } else {
        localStorage.setItem('backUrl', location.href)
        this.$router.push("https://yun.tfcs.cn")
      }
      // 未登录中断请求
    }
    if (["wxwork", "com-wx-pc"].includes(this.$isWxWork())) {
      console.log("企业微信环境");
      // 企业微信弹窗解决
      this.getWxQyWxConfig(["agentConfig", "sendChatMessage"], (wx) => {
        this.wx = wx;
      });
    }
    this.params.work_user_id = uni.getStorageSync("crm_client_id");
    this.getDetailList();
  },
  methods: {
    getDetailList () {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.talk_list = [];
      }
      this.$ajax.get("/qywx/words/search", this.params, (res) => {
        this.load_status = "loadend";
        if (res.statusCode === 200) {
          this.talk_list = this.talk_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        }
      }, () => {
        this.load_status = "nomore";
      })
    },
    onClickTab (e) {
      this.tab_type = e.index;
      this.params.type = e.type;
      this.params.page = 1;
      this.getDetailList();
    },
    tabChange (index) {
      this.current = index;
    },
    addTalk () {
      this.$navigateTo("add");
    },
    onSearch () {
      this.params.page = 1;
      this.getDetailList();
    },
    onSendMsg (e) {
      this.wx.invoke(
        "sendChatMessage",
        {
          msgtype: "text", //消息类型，必填
          enterChat: true, //为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
          text: {
            content: e.content, //文本内容
          },
        },
        function (res) {
          if (res.err_msg == "sendChatMessage:ok") {
            //发送成功
            uni.showToast({
              title: "发送成功",
            });
          }
        }
      );
    },
    switchTab (index, item) {
      if (this.currentTabIndex == index) {
        return;
      }
      this.$navigateTo(item.path);
    },
  },
  onReachBottom () {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDetailList();
  },
};
</script>
<style lang="scss" scoped>
.info {
  padding: 12px;
  .list {
    margin-bottom: 75px;
    .deatil {
      background: #fff;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 12px;
      .open {
        justify-content: center;
        border-top: 1px solid #d8d8d8;
        padding-top: 12px;
        margin-top: 10px;
      }
      > text {
        font-weight: 500;
      }
      .val {
        margin-top: 12px;
        align-items: center;
        .pic {
          flex: 1;
          image {
            width: 65px;
            height: 65px;
            border-radius: 4px;
          }
          .tit {
            margin-left: 12px;
            line-height: 24px;
            text {
              font-size: 11px;
            }
          }
        }
        .c2 {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          word-break: break-all;
          flex: 1;
          line-height: 18px;
          &.d1 {
            -webkit-line-clamp: 3;
          }
        }
        .push {
          color: #2d84fb;
          margin-left: 10px;
          image {
            width: 15px;
            margin-right: 5px;
          }
        }
      }
    }
  }
  .tab {
    margin: 12px 0;
    view {
      margin-right: 15px;
      text {
        color: #8a929f;
        border-radius: 12px;
        padding: 5px 18px;
        &.active {
          background: #fff;
          color: #2d84fb;
        }
      }
    }
  }
  .title {
    font-size: 16px;
    align-items: center;
    text {
      color: #2d84fb;
    }
  }
}
page {
  background: #f6f6f6;
  color: #2e3c4e;
}
.c2 {
  color: #8a929f;
}
.top {
  background: #fff;
  padding: 12px;
  .search_box {
    align-items: center;
    margin-top: 8px;
    .add {
      color: #2d84fb;
      font-weight: 500;
      margin-left: 12px;
      align-items: center;
      text {
        font-size: 22px;
        margin: -1px 2px 0 0;
      }
    }
  }
  .search {
    flex: 1;
    align-items: center;
    border-radius: 4px;
    background: #f6f6f6;
    padding: 10px 12px;
    .input {
      flex: 1;
      .c2 {
        margin-left: 5px;
        font-size: 14px;
        flex: 1;
      }
    }
    > view {
      align-items: center;
    }
  }
}
</style>
