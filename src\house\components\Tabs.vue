<template>
  <view class="nav-box" :class="{ small: small, theme2: theme == 2 }">
    <scroll-view class="scroll-view" scroll-x scroll-with-animation
      :scroll-into-view="'i' + (current_index > 0 ? current_index - 1 : current_index)"
      :style="{ 'line-height': lineHeight, height: height }">
      <view class="nav-list flex-row" :class="{ equispaced }">
        <slot>
          <view class="nav-item" :class="{
            active: value === item[format.value],
            active_customer: value === item[format.value] && !showAnimation,
          }" :id="'i' + index" v-for="(item, index) in options" :key="item.index" @click="onClick(index)">{{
  item[format.name] }}</view>
        </slot>
        <view v-if="showAnimation" class="active_bar" :style="{ transform: `translateX(${bar_left}px)` }"></view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  props: {
    options: Array,
    format: {
      type: Object,
      default: () => {
        return { name: 'name', value: 'value' }
      },
    },
    lineHeight: {
      type: [String],
      default: '',
    },
    height: {
      type: [String],
      default: '',
    },
    value: {
      type: [Number, String],
      default: '',
    },
    maxNum: {
      // 一屏最大显示数量
      type: [Number],
      default: 4,
    },
    equispaced: {
      // maxNum小于4时是否平均分布
      type: [Boolean],
      default: true,
    },
    small: {
      // 是否时缩小版的
      type: [Boolean],
      default: false,
    },
    theme: {
      type: [Number],
      default: 1,
    },
    showAnimation: {
      type: [Boolean],
      default: true,
    },
  },
  // model: {
  //   event: 'change',
  //   prop: 'current',
  // },
  watch: {
    options: {
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.setActiveBar()
          })
        }
      },
      deep: true,
    },
    value() {
      this.$nextTick(() => {
        this.setActiveBar()
      })
    },
  },
  data() {
    return {
      list_left: 0,
      bar_left: 0,
    }
  },
  // created() {
  //   console.log(this.value)
  // },
  computed: {
    current_index() {
      return this.options.findIndex((item) => item[this.format.value] === this.value) || 0
    },
  },
  mounted() {
    this.query = uni.createSelectorQuery().in(this)
    this.setActiveBar()
  },
  methods: {
    onClick(index) {
      this.$emit('input', this.options[index][this.format.value])
      this.$emit('change', this.options[index][this.format.value])
      // this.setActiveBar(index)
    },
    setActiveBar(index = this.current_index) {
      if (index === undefined || index < 0) {
        return
      }
      // 获取当前节点信息
      const list_node_info = this.query.select('.nav-list')
      list_node_info
        .fields({ rect: true }, (res) => {
          if (res.left) {
            this.list_left = res.left
            // this.setActiveBar()
          }
        })
        .exec()
      const current_node_info = this.query.select(`#i${index}`)
      current_node_info
        .fields({ rect: true, size: true }, (res) => {
          this.bar_left =
            res.width + res.left - this.list_left - uni.upx2px(48) - uni.upx2px(24)
        })
        .exec()
    },
  },
}
</script>

<style lang="scss">
.nav-box {
  width: 50% !important;
}

.nav-list {
  display: flex;
  min-width: 100%;
  white-space: nowrap;
  font-size: 32rpx;
  position: relative;

  &.equispaced {
    justify-content: space-between;
  }

  .active_bar {
    position: absolute;
    bottom: 12rpx;
    width: 48rpx;
    height: 18rpx;
    border-radius: 9rpx;
    background-image: linear-gradient(90deg,
        rgba($uni-color-primary, 0.85),
        rgba($uni-color-primary, 0.02));
    transition: 0.26s;
  }
}

.small .nav-list {
  height: 60rpx;
  line-height: 56rpx;
  white-space: nowrap;
  font-size: 28rpx;
}

.nav-item {
  flex-shrink: 0;
  text-align: center;
  padding: 0 24rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  // text-shadow: 0 0 1rpx #8a929f;
  transition: 0.26s;
  color: #8a929f;
}

.nav-item.active {
  // color: $uni-color-primary;
  text-shadow: 0 0 1rpx #2e3c4e;
  color: #2e3c4e;
  position: relative;

  &.active_customer {
    color: #007aff;

    // &::before {
    //   position: absolute;
    //   content: '';
    //   height: 3px;
    //   width: 60%;
    //   left: 0;
    //   right: 0;
    //   margin: auto;
    //   bottom: 0;
    //   background-color: #007aff;
    // }
  }

  // border-bottom: 4rpx solid $uni-color-primary;
}

// .nav-item.active:before {
//   position: absolute;
//   content: '';
//   height: 8rpx;
//   max-width: 18vw;
//   left: 32%;
//   right: 32%;
//   border-radius: 4rpx;
//   margin: auto;
//   bottom: 0;
//   background-color: $uni-color-primary;
// }
.theme2 {
  padding: 15rpx 0;

  .nav-item {
    border: 1rpx solid $uni-color-primary;
    color: $uni-color-primary;
    border-radius: 3px;

    &.active {
      background-color: $uni-color-primary;
      color: #fff;
      position: relative;
    }
  }
}
</style>
