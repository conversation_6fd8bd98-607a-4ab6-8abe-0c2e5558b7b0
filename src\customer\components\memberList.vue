<template>
  <view class="lists">
    <!-- <checkbox-group @change="checkboxChange"> -->
    <!-- <view v-for="i in memberList" :key="i.id"> -->
    <view class="flex-row items-center deaprt" @click.prevent.stop="changeOpen(member)">
      <view class="deaprt_name flex-1">
        {{ member.name }}
      </view>
      <view class="icon">
        <myIcon
          v-if="member.open"
          class="icon"
          type="shangla"
          color="#D1D1D1"
          size="36rpx"
        ></myIcon>
        <myIcon v-else class="icon" type="xiala" color="#D1D1D1" size="36rpx"></myIcon>
      </view>
    </view>
    <template v-if="member.children && member.children.length && member.open">
      <view
        class="uni-list-cell uni-list-cell-pd flex-1 flex-row"
        v-for="item in member.children"
        :key="item.id"
        @click.prevent.stop="changeOpen(item)"
      >
        <view class="check_box">
          <myIcon type="ic_queren3x" color="#2d84fb" v-if="item.checked"></myIcon>
          <!-- <radio :value="'' + item.id" :checked="item.id === current" /> -->
        </view>
        <view class="prelogo">
          <view class="name_first">
            {{ item.name && item.name[0] }}
          </view>
        </view>
        <view class="cname flex-1">
          <view class="cname_top"> {{ item.name }} </view>
        </view>
      </view>
    </template>
    <template v-if="member.subs && member.subs.length">
      <view
        class="deaprt flex-1 flex-row"
        v-for="item in member.subs"
        :key="item.id"
        @click.prevent.stop="changeOpen(item)"
      >
        <view class="deaprt_name flex-1">
          {{ item.name }}
        </view>
        <view class="icon">
          <myIcon
            v-if="item.open"
            class="icon"
            type="shangla"
            color="#D1D1D1"
            size="36rpx"
          ></myIcon>
          <myIcon v-else class="icon" type="xiala" color="#D1D1D1" size="36rpx"></myIcon>
        </view>
      </view>
    </template>
  </view>
  <!-- </view> -->
</template>

<script>
import myIcon from "@/components/my-icon";
export default {
  components: { myIcon },
  name: 'memberList',
  props: {
    memberList: {
      type: Object,
      default: () => { }
    }
  },
  watch: {
    memberList: {
      handler (val) {
        // handle(val){
        this.member = JSON.parse(JSON.stringify(val))
        console.log(this.member, 123233333333333333333)
      },
      immediate: true

      // }
      // ;
    },

  },
  data () {
    return {
      current: '',
      member: {}
    }
  },
  methods: {
    changeOpen (member) {
      this.$emit('changeOpen', member)
    },
    changeRadio (item) {
      console.log(item);
      this.$emit("changeRadio", item)
    }
  }

}
</script>

<style lang="scss" scoped>
.lists {
  flex: 1;
  padding-left: 24px;
  // padding-top: 60px;
  checkbox-group {
    // padding-left: 24rpx;
    ::v-deep uni-label {
      width: 100% !important;
    }
    uni-checkbox {
      ::v-deep .uni-checkbox-input {
        margin-right: 0;
        border-radius: 50%;
      }
    }
  }
  radio-group {
    // padding-left: 24rpx;
    ::v-deep uni-label {
      width: 100% !important;
    }
    uni-radio {
      ::v-deep .uni-radio-input {
        margin-right: 0;
        border-radius: 50%;
      }
    }
  }
  .deaprt {
    padding: 10px 0;
    ~ .deaprt {
      padding-left: 20px;
    }
  }
  .uni-list-cell {
    padding: 10px 0;
    .check_box {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      border: 1px solid #d1d1d1;
    }
  }
  .prelogo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin: 0 10px;
    background: #2d84fb;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    .name_first {
      color: #fff;
      font-size: 16px;
    }
    image {
      width: 100%;
      height: 100%;
    }
  }
  .cname {
    font-size: 16px;
    // font-weight: bolder;
    color: 414346;
    .cname_bottom {
      margin-top: 5px;
    }
    .c_label {
      background: #f4f4f4;
      padding: 3px 5px;
      border-radius: 2px;
      color: #959a9d;
      font-size: 10px;
      &:not(last-child) {
        margin-right: 5px;
      }
    }
  }
  .souce {
    font-size: 14px;
    color: #868686;
  }

  .uni-list-cell {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 20rpx 0;
  }
  .footer {
    padding: 20rpx;
    background: #f8f8f8;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    max-width: 1500rpx;
    .confirm {
      font-size: 28rpx;
      padding: 10rpx 20rpx;
      color: #fff;
      background: #2d84fb;
      border-radius: 8rpx;
    }
  }
}
</style>