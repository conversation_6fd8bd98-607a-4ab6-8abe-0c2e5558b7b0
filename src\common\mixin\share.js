/*
 * @Description:全局分享配置 
 * @Author: lei
 * @Date: 2020-11-13 13:39:41
 */
import {
    formatImg
} from "@/page_outside/tools/index";
export default ({
    onLoad () {

    },
    onShareAppMessage () {
        if (!this.share) this.share = {}
        console.log(this.share);
        var pages = getCurrentPages() //获取加载的页面
        var currentPage = pages[pages.length - 1] //获取当前页面的对象
        var url = currentPage.$page.fullPath //当前页面url
        let share_id=''
   
        if (url.indexOf("shareId=") == -1) {
            if (url.indexOf("?") >= 0) {
                url += '&shareId=' + share_id
            } else {
                url += '?shareId=' + share_id
            }
        }
        if (this.share && this.share.path && this.share.path.indexOf("shareId=") == -1) {
            if (this.share.path.indexOf("?") >= 0) {
                this.share.path += '&shareId=' + share_id
            } else {
                this.share.path += '?shareId=' + share_id
            }
        }
        if (this.share && this.share.path && this.share.path.indexOf("ftime=") == -1) {
            if (this.share.path.indexOf("?") >= 0) {
                this.share.path += '&ftime=' + (+new Date())
            } else {
                this.share.path += '?ftime='  + (+new Date())
            } 
        }
        console.log(this.share.path,"12313");
        // if (this.$store.state.user_info.id) {
        //     if (url.indexOf("shareId=") == -1) {
        //         if (url.indexOf("?") >= 0) {
        //             url += '&shareId=' + this.$store.state.user_info.id
        //         } else {
        //             url += '?shareId=' + this.$store.state.user_info.id
        //         }
        //     }
 
        //     if (this.share && this.share.path) {
        //         if (this.share.path.indexOf("?") >= 0) {
        //             this.share.path += '&shareId=' + this.$store.state.user_info.id
        //         } else {
        //             this.share.path += '?shareId=' + this.$store.state.user_info.id
        //         }
        //     }
        // }
        if (this.share) {
            return {
                title: this.share.title || "",
                desc: this.share.content || '',
                imageUrl: (this.share.pic || this.share.imageUrl) ? formatImg((this.share.pic || this.share.imageUrl), 'w_6401') : "", //分享图片
                path: this.share.path || url
            }
        }
    },
    onShareTimeline () {
        if (!this.share) this.share = {}
        var pages = getCurrentPages() //获取加载的页面
        var currentPage = pages[pages.length - 1] //获取当前页面的对象
        var url = currentPage.$page.fullPath //当前页面url
        
        if (url.indexOf("shareId=") == -1) {
            if (url.indexOf("?") >= 0) {
                url += '&shareId=' + share_id
            } else {
                url += '?shareId=' + share_id
            }
        }
        if (this.share && this.share.path) {
            if (this.share.path.indexOf("?") >= 0) {
                this.share.path += '&shareId=' + share_id
            } else {
                this.share.path += '?shareId=' + share_id
            }
        }
        

        return {
          title:this.share.title||'',
          query:(this.share.path || url).split("?").length>1?(this.share.path || url).split("?")[1]:'',
          imageUrl: this.share.pic ? formatImg(this.share.pic, "w_6401") : "",
        }
      },
})
