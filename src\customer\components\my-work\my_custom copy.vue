<template>
  <view>
    <!-- 客户-->
    <view
      style="
        padding: 32rpx;
        width: 100%;
        background-color: #fff;
        border-radius: 16rpx;
      "
    >
    <view class="whole_input">
                <view class="whole_input_left" @click="inputFn">
                    <view class="whole_input_left_name">{{ mobiles || '电话'}}</view>
                    <view>
                        <image src="../../../static/icon/index/xia.png" style="width:32rpx;height:32rpx;"></image>
                    </view>
                </view>
                <view class="whole_shu">|</view>
                <view class="whole_bai">
            <!-- 请输入搜索内容 -->
            <view> <input placeholder="请输入搜索内容" placeholder-style="font-size:28rpx;color:#ACACAC;" type="text"
                v-model="keywords" @confirm="keywordsBtn"></view>
          </view>
            </view>
            <!-- 我的客户公海客户的统计 -->
         <view class="rectangle_image">
          <view style="width: 352rpx; height:168rpx ;margin-top: 20rpx;" class="rectangle_image_left">
              <image src="../../../static/icon/index/Rectangle 4952.png" style="width:100%;height:100%"></image>
            </view>
            <view style="width: 352rpx; height:168rpx ;" class="rectangle_image_right">
              <image src="../../../static/icon/index/Rectangle 4951.png" style="width:100%;height:100%"></image>
            </view>
         </view>
   
          <image src="../../../static/icon/index/huojian.png"  class="huojian_image"></image>
<view class="rectangle_text">
<view class="rectangle_text_header">
  <view style="margin-right: 16rpx;">我的客户</view>
  <view style="margin-right: 8rpx;">  +11</view>
  <view>
    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
<path d="M7.5 6V10H4.5V6H2L6 2L10 6H7.5Z" fill="white"/>
</svg>
  </view>
</view>
<view class="rectangle_text_center">13666</view>
</view>
<view class="rectangle_texts">
<view class="rectangle_text_header">
  <view>
    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
<path d="M7.5 6V10H4.5V6H2L6 2L10 6H7.5Z" fill="white"/>
</svg>
  </view>
  <view style="margin-right: 8rpx;">  +11</view>
  <view>公海客户</view>
</view>
<view class="rectangle_text_center">13666</view>
</view>
     
      <view
        style="
          display: flex;
          flex-direction: row;
          margin-top: 32rpx;
          width: 200%;
        "
      >
          <!-- tabs切换 -->
        <view
          v-for="(item, index) in myhouseList"
          :key="item.id"
          @tap="changeAct(item)"
          class="my_style_all"
        >
          <view class="my-style-one" :class="{ 'active': act === item.id }">
            {{ item.name }}
          </view>
        </view>
        <!-- 本周 -->
        <!-- <scroll-view
          style="
            margin-left: 10rpx;
            width: 25%;
            margin-top: 8rpx;
            white-space: nowrap;
            text-align: center;
          "
          scroll-x="true"
          @scroll="scroll"
        >
          <view
            class="weeked"
            v-for="(tab, index) in navs"
            :key="tab.id"
            :id="tab.id"
            :class="navIndex == index ? 'activite' : ''"
            @click="checkIndex(index)"
            >{{ tab.name }}</view
          >
        </scroll-view> -->
        <!-- 本周 -->
        <view class="data_all" @click="weekerBtn">
          <view>
            <image src="../../../static/icon/index/data.png" style="width:32rpx;height:32rpx"></image>
          </view>
          <view class="weeker_data">
            {{weekrName}}
          </view>
          <view>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
<path d="M3.52864 5.52864C3.78899 5.26829 4.2111 5.26829 4.47145 5.52864L8.00004 9.05723L11.5286 5.52864C11.789 5.26829 12.2111 5.26829 12.4714 5.52864C12.7318 5.78899 12.7318 6.2111 12.4714 6.47145L8.47145 10.4714C8.2111 10.7318 7.78899 10.7318 7.52864 10.4714L3.52864 6.47145C3.26829 6.2111 3.26829 5.78899 3.52864 5.52864Z" fill="#488AF6"/>
</svg>
          </view>
        </view>
      </view>
      <!-- 我的客户 -->
      <view v-if="act == 2">
        <view
        class="mywork_box"
          v-for="item in houseLists"
          :key="item.id"
          @click="itemFn(item.location)"
        >
          <view style="display: flex; flex-direction: row; align-items: center">
            <view>
              <image
                :src="item.icon"
                style="width: 100rpx; height: 100rpx"
              ></image>
            </view>
            <view style="margin-left: 24rpx">{{ item.name }}</view>
          </view>
          <view style="display: flex; flex-direction: row">
            <view>{{ item.number }}</view>
            <view>
              <image
                src="../../../static/icon/index/箭头 .png"
                style="width: 32rpx; height: 32rpx"
              ></image>
            </view>
          </view>
        </view>
      </view>
      <!-- 我参与的 -->
      <view v-if="act == 3">
        <view
        class="mywork_box"
          v-for="item in myList"
          :key="item.id"
          @click="locationFn(item.location)"
        >
          <view style="display: flex; flex-direction: row; align-items: center">
            <view>
              <image
                :src="item.icon"
                style="width: 100rpx; height: 100rpx"
              ></image>
            </view>
            <view style="margin-left: 24rpx">{{ item.name }}</view>
          </view>
          <view style="display: flex; flex-direction: row">
            <view>{{ item.number }}</view>
            <view>
              <image
                src="../../../static/icon/index/箭头 .png"
                style="width: 32rpx; height: 32rpx"
              ></image>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="searchshow">
    <view>
      <image src="../../../static/icon/index/Polygon 1.png" class="search_box_image"></image>
    </view>
    <view class="search_box" >
        <view v-for="(item, index) in range" :key="item.value" class="search_text"
          :class="{ 'activee': item.value == value }" @tap="textFn(item.value,item.text)">{{ item.text }}</view>
      </view>
  </view>
<!-- 本周下拉框 -->
  <view class="popbox" v-if="weekerShow">
    <view class="popbox_item" v-for="(item,index ) in navs" :key="item.id" :class="{'popbox_item_active' :navIndex == item.id}" @click="checkIndex(index,item.name)">{{ item.name }}</view>
</view>
  </view>
</template>
<script>
import getDate from "./getDate .js";
export default {
  props: {
    myhouseList: { Array },
  },
  data() {
    return {
      searchshow:false,
      navIndex: 3, // 本周
      weekrName:'昨天',
      weekerShow:false,// 控制本周的气泡框
      act: 2,
      navs: [
        {id:1,name:'全部'},
        { id: 2, name: "今天" },
        { id: 3, name: "昨天" },
        { id: 4, name: "本周" },
        { id: 5, name: "上周" },
        { id: 6, name: "本月" },
        { id: 7, name: "上月" },
      ],
      houseLists: {},
      // 我的客户
      dataList: {
        parent_id: "2",
        start_date: "",
        end_date: "",
        mobile: "1",
        keywords: "",
      },
      //  我参与的
      dataLists: {
        parent_id: "3",
        start_date: "",
        end_date: "",
        mobile: "1",
        keywords: "",
      },
      myList: {},
      value: "0",
      mobiles:'',
      keywords:'',
      range:[
        {  value:0,text:"电话"},
        {  value:1,text:"姓名"},
        {  value:2,text:"线索"},
        {  value:3,text:"编号"},
      ],
    };
  },
  created() {
    uni.showLoading({
      title: "加载中",
    });
    if (this.dataList.parent_id === "2") {
          // 今天
          // 今天开始时间
          let endtime = getDate.getToday().endtime;
          //今天结束时间
          let starttime = getDate.getToday().starttime;
          this.dataList.start_date = starttime;
          this.dataList.end_date = endtime;
          // console.log(this.dataList.start_date, '昨天开始时间');
          this.getUserHouse();
        }
        if (this.dataLists.parent_id === "3") {
          // 今天开始时间
          let endtime = getDate.getToday().endtime;
          //今天结束时间
          let starttime = getDate.getToday().starttime;
          this.dataLists.start_date = starttime;
          this.dataLists.end_date = endtime;
          this.getMyHouse();
        }
  },
  watch: {
    keywords: {
      handler(naval) {
        uni.setStorageSync('keywords', naval);
      },
      immediate: true,
    },
    mobiles:{
      handler(naval) {
        if(naval == ''){
          naval ='电话'
        }
        uni.setStorageSync('mobiles', naval);
      },
      immediate: true,
    }
  },
  methods: {
    deldate(){
      this.mobiles = ''
      this.keywords = ''
    },
    keywordsBtn(){
      // this.searchshow = !this.searchshow
      if(this.keywords != ''){
        this.$navigateTo('/customer/myLists')
      }
    },
    inputFn(){
this.searchshow = !this.searchshow
    },
    textFn(item,text){
      this.searchshow = !this.searchshow
      this.value = item
      this.mobiles= text
      uni.setStorageSync('mobiles', this.mobiles);
      if(text != ''){
        this.keywords = ''
      }
    },
    change(e) {
      console.log("e:", e);
    },
    // tab互斥效果
    changeAct(item) {
      // console.log(item.id);
      // 激活样式是当前点击的对应下标--list中对应id
      this.act = item.id;
    },
    // 控制本周气泡框
    weekerBtn(){
      this.weekerShow = !this.weekerShow
    },
    checkIndex(index,name) {
      if (this.dataList.parent_id === "2") {
        this.navIndex = index+1;
        this.weekrName = name
        this.weekerShow = !this.weekerShow
      }
      if (this.dataLists.parent_id === "3") {
        this.navIndex = index+1;
      }
      if (index === 0) {
        if (this.dataList.parent_id === "2") {
          this.dataList.start_date = "";
          this.dataList.end_date = "";
          this.getUserHouse();
        }
        if (this.dataLists.parent_id === "3") {
          this.dataLists.start_date = "";
          this.dataLists.end_date = "";
          this.getMyHouse();
        }
      } else if (index === 1) {
        if (this.dataList.parent_id === "2") {
          // 今天
          // 今天开始时间
          let endtime = getDate.getToday().endtime;
          //今天结束时间
          let starttime = getDate.getToday().starttime;
          this.dataList.start_date = starttime;
          this.dataList.end_date = endtime;
          // console.log(this.dataList.start_date, '昨天开始时间');
          this.getUserHouse();
        }
        if (this.dataLists.parent_id === "3") {
          // 今天开始时间
          let endtime = getDate.getToday().endtime;
          //今天结束时间
          let starttime = getDate.getToday().starttime;
          this.dataLists.start_date = starttime;
          this.dataLists.end_date = endtime;
          this.getMyHouse();
        }
      } else if (index === 2) {
        if (this.dataList.parent_id === "2") {
          // 昨天
          // 昨天开始时间
          let endtime = getDate.getYesterday().endtime;
          //昨天结束时间
          let starttime = getDate.getYesterday().starttime;
          this.dataList.start_date = starttime;
          this.dataList.end_date = endtime;
          // console.log(this.dataList.start_date, '昨天开始时间');
          this.getUserHouse();
        }
        if (this.dataLists.parent_id === "3") {
          // 昨天开始时间
          let endtime = getDate.getYesterday().endtime;
          //昨天结束时间
          let starttime = getDate.getYesterday().starttime;
          this.dataLists.start_date = starttime;
          this.dataLists.end_date = endtime;
          this.getMyHouse();
        }
      } else if (index === 3) {
        if (this.dataList.parent_id === "2") {
          // 本周
          // 本周开始时间
          let endtime = getDate.getCurrWeekDays().endtime;
          //本周结束时间
          let starttime = getDate.getCurrWeekDays().starttime;
          this.dataList.start_date = starttime;
          this.dataList.end_date = endtime;
          // console.log(this.dataList.start_date, '本周开始时间');
          this.getUserHouse();
        }
        if (this.dataLists.parent_id === "3") {
          // 本周
          // 本周开始时间
          let endtime = getDate.getCurrWeekDays().endtime;
          //本周结束时间
          let starttime = getDate.getCurrWeekDays().starttime;
          this.dataLists.start_date = starttime;
          this.dataLists.end_date = endtime;
          this.getMyHouse();
        }
      } else if (index === 4) {
        if (this.dataList.parent_id === "2") {
          // 上周
          // 上周开始时间
          let endtime = getDate.getLastWeekDays().endtime;
          //上周结束时间
          let starttime = getDate.getLastWeekDays().starttime;
          this.dataList.start_date = starttime;
          this.dataList.end_date = endtime;
          // console.log(this.dataList.start_date, '上周开始时间');
          this.getUserHouse();
        }
        if (this.dataLists.parent_id === "3") {
          // 上周
          // 上周开始时间
          let endtime = getDate.getLastWeekDays().endtime;
          //上周结束时间
          let starttime = getDate.getLastWeekDays().starttime;
          this.dataLists.start_date = starttime;
          this.dataLists.end_date = endtime;
          this.getMyHouse();
        }
      } else if (index === 5) {
        if (this.dataList.parent_id === "2") {
          // 本月
          // 本月开始时间
          let endtime = getDate.getCurrMonthDays().endtime;
          //本月结束时间
          let starttime = getDate.getCurrMonthDays().starttime;
          this.dataList.start_date = starttime;
          this.dataList.end_date = endtime;
          // console.log(this.dataList.start_date, '本月开始时间');
          this.getUserHouse();
        }
        if (this.dataLists.parent_id === "3") {
          // 本月
          // 本月开始时间
          let endtime = getDate.getCurrMonthDays().endtime;
          //本月结束时间
          let starttime = getDate.getCurrMonthDays().starttime;
          this.dataLists.start_date = starttime;
          this.dataLists.end_date = endtime;
          this.getMyHouse();
        }
      } else if (index === 6) {
        if (this.dataList.parent_id === "2") {
          // 上月
          // 上月开始时间
          let endtime = getDate.getLastMonthDays().endtime;
          //上月结束时间
          let starttime = getDate.getLastMonthDays().starttime;
          this.dataList.start_date = starttime;
          this.dataList.end_date = endtime;
          console.log(this.dataList.start_date, "上月开始时间");
          this.getUserHouse();
        }
        if (this.dataLists.parent_id === "3") {
          // 上月
          // 上月开始时间
          let endtime = getDate.getLastMonthDays().endtime;
          //上月结束时间
          let starttime = getDate.getLastMonthDays().starttime;
          this.dataLists.start_date = starttime;
          this.dataLists.end_date = endtime;
          this.getMyHouse();
        }
      }
    },
    scroll: function (e) {
      // console.log(e)
      // this.old.scrollTop = e.detail.scrollTop
    },
    onConfirm() {
      // 我的客户
      if (this.dataList.parent_id === "2") {
        this.getUserHouse();
      }
      // 我参与的
      if (this.dataLists.parent_id === "3") {
        this.getMyHouse();
      }
    },
    // 我的客户
    getUserHouse() {
      let params = Object.assign({}, this.dataList);
      if (!params.start_date) {
        delete params.start_date;
      }
      if (!params.end_date) {
        delete params.end_date;
      }
      this.$ajax.get(
        `/qywx/welcome/get_fixed_menu`,
        params,
        (res) => {
          console.log(res.data, "11");
          if (res.statusCode === 200) {
            this.houseLists = res.data;
            uni.hideLoading();
          } else {
            uni.hideLoading();
          }
        },
        () => {
          uni.hideLoading();
        }
      );
    },
    // 我参与的
    getMyHouse() {
      let params = Object.assign({}, this.dataLists);
      if (!params.start_date) {
        delete params.start_date;
      }
      if (!params.end_date) {
        delete params.end_date;
      }
      this.$ajax.get(
        `/qywx/welcome/get_fixed_menu`,
        params,
        (res) => {
          console.log(res.data, "22");

          if (res.statusCode === 200) {
            this.myList = res.data;
            uni.hideLoading();
          } else {
            uni.hideLoading();
          }
        },
        () => {
          uni.hideLoading();
        }
      );
    },
    // 点击跳转·1
    locationFn(item) {
      console.log(111);
      this.$navigateTo(item);
    },
    itemFn(item) {
      this.$navigateTo(item);
    },
  },
};
</script>
<style lang="scss" scoped>
.popbox{
  position: absolute;
  top: 720rpx;
  left: 466rpx;
  background: #fff;
  width: 260rpx;
  // padding: 0 32rpx;
  border-radius: 16rpx;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.05);
}
.popbox_item{
  padding: 24rpx 0; 
  background: #ffff;
text-align: center;
  // border-bottom: 2rpx solid #F6F6F6;
}
.popbox_item_active{
   background: #e4e3e3;
}
.mywork_box{
  display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding: 32rpx 0;
            border-bottom: 2rpx solid #F6F6F6;
            &.mywork_box:last-child {
    border: none;
  }
}
.weeker_data{
  margin: 0 16rpx;
  color: #488AF6;
}
.data_all{
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  margin-left: 120rpx;
  position: relative;
}
.my_style_all{
  text-align: center;
 line-height: 64rpx;
  padding: 8rpx;
  border-radius: 8rpx;
background: #F6F6F6;
font-size: 28rpx;
color: rgba(41, 44, 57, 0.40);
}
.rectangle_text_center{
  color: #FFF;
font-family: PingFang SC;
font-size: 48rpx;
margin-top: 36rpx;
}
.rectangle_image_left{
  position: relative;
}
.rectangle_text{
position: absolute;
top: 456rpx;
left: 32rpx;
  // background: rgb(249, 183, 183);
  width: 352rpx; 
  height:168rpx;
  padding-top: 32rpx;
  padding-left: 24rpx;
}
.rectangle_texts{
  position: absolute;
  top: 436rpx;
  left: 366rpx;
  // background: rgb(249, 183, 183);
  width: 352rpx; 
  height:168rpx;
  padding-top: 32rpx;
  padding-right: 24rpx;
  align-items: flex-end
}
.rectangle_text_header{
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  color: #FFF;
font-size: 28rpx;
}
.huojian{
  width: 104rpx;
  height: 104rpx;
}
.huojian_image{
  width: 104rpx;
  height: 104rpx;
  // z-index: 999;
  position: absolute;
  top: 486rpx;
  left: 326rpx;
}
.rectangle_image{
  position: relative;
display: flex;
flex-direction: row;
flex-wrap: nowrap;
margin-top: 40rpx;
}
.activee {
  background:rgba(0, 0, 0, 0.03);
}
.search_box_image{
  width: 36rpx;
  height: 30rpx;
  position: absolute;
  top: 366rpx;
  left: 150rpx;
}
.search_text {
  padding: 20rpx 32rpx;
width: 100%
}
.search_box {
  position: absolute;
  top: 372rpx;
  left: 54rpx;
  background: #fff;
  width: 180rpx;
  margin-top: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0px 0px 40rpx 0px rgba(0, 0, 0, 0.10);
}
.whole_shu {
  color: rgba(41, 44, 57, 0.40);
    font-size: 24rpx;
    font-weight: 400;
    line-height: 28rpx;

}

.whole_bai {
    width: 70%;
    height: 32rpx;
    line-height: 32rpx;
    color: rgba(41, 44, 57, 0.40);
}

.whole_input_left_name {
    color: rgba(41, 44, 57, 0.70);
    font-size: 28rpx;
    font-weight: 400;
    line-height: 32rpx;
    margin-right: 16rpx;
}

.whole_input_left {
    display: flex;
    flex-direction: row;

}

.whole_input {
  position: relative;
  overflow: auto;
    width: 100%;
    // height: 80rpx;
    padding: 24rpx;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    // margin: 0 16rpx;
    background: #F6F6F6;
    border-radius: 16rpx;
}
.my-style-one {
  box-sizing: border-box;
  // padding: 16rpx 20rpx;
width: 176rpx;
height: 64rpx;
  text-align: center;
  line-height: 64rpx;
  background: #F6F6F6;
font-size: 28rpx;
  border-radius: 8rpx;
  border: none;

}

// 我的房客
.active {
  box-sizing: border-box;
  // padding: 16rpx 20rpx;
width: 176rpx;
height: 64rpx;
  text-align: center;
  line-height: 64rpx;
color: #488AF6;
font-size: 28rpx;
  border-radius: 8rpx;
background:  #FFF;
  border: none;
}

// 本周
.weeked {
  box-sizing: border-box;
  // padding: 6rpx 24rpx;
  width: 96rpx;
  height: 48rpx;
  border-radius: 8rpx;
  margin: 0 6rpx;
  font-size: 24rpx;
  line-height: 48rpx;
  background: #f8f8f8;
  display: inline-block;
  /* 必要，导航栏才能横向*/
}

.activite {
  box-sizing: border-box;
  // padding: 6rpx 24rpx;
  width: 96rpx;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  background: rgba(72, 138, 246, 0.2);
}
::v-deep .uni-input-placeholder {
  top: -6rpx !important;
}

::v-deep .uni-input-input {
  top: -6rpx !important;
}
// 下拉
.updata {
  width: 160rpx;
  background-color: #fff;
}
</style>
