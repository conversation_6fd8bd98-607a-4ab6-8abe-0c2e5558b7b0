<template>
  <view class="time_line">
    <!-- #ifdef H5 -->
    <view class="item" v-for="(item, index) in list" :key="index">
      <slot name="dot">
        <view class="dot"></view>
      </slot>
      <slot :_index="index" :prop="item">
        <view class="time">{{ item[time] }}</view>
        <view class="content">{{ item[content] }}</view>
      </slot>
    </view>
    <!-- #endif -->
    <!-- #ifndef H5 -->
    <slot></slot>
    <!-- #endif -->
  </view>
</template>

<script>
export default {
  props: {
    list: Array,
    content: {
      type: String,
      default: 'content',
    },
    time: {
      type: String,
      default: 'time',
    },
  },
  data() {
    return {}
  },
}
</script>

<style lang="scss" scope>
.time_line {
  padding: 20rpx 30rpx;
  .item {
    position: relative;
    padding: 0 20rpx 36rpx 32rpx;
    border-left: 2rpx solid #dde1e9;
    .time {
      margin-bottom: 15rpx;
      font-size: 24rpx;
      font-weight: bold;
      color: #999;
    }
    .content {
      font-size: 28rpx;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
    }
  }
  // .item::after {
  //   content: '';
  //   height: 36rpx;
  //   width: 36rpx;
  //   box-sizing: border-box;
  //   border-radius: 50%;
  //   position: absolute;
  //   border: 4rpx solid #3399ff;
  //   background-color: #fff;
  //   left: -20rpx;
  //   top: -8rpx;
  // }
  .dot {
    content: '';
    height: 14rpx;
    width: 14rpx;
    border-radius: 50%;
    background-color: #3399ff;
    position: absolute;
    left: -8rpx;
    top: 0;
    z-index: 2;
  }
  // .active::before {
  //   content: '';
  //   height: 14rpx;
  //   width: 14rpx;
  //   border-radius: 50%;
  //   background-color: #3399ff;
  //   position: absolute;
  //   left: -8rpx;
  //   top: 0;
  //   z-index: 2;
  // }
}
</style>
