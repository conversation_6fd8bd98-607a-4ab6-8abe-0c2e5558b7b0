<template>
  <view class="item">
    <view class="item_row flex-row">
      <view class="item_value flex-1"> 资料名称</view>
      <view class="item_name">{{ item.map_plugin }}</view>
    </view>
    <view class="item_row flex-row">
      <view class="item_value flex-1">客户手机号</view>
      <view class="item_name"> {{ item.phone }}</view>
    </view>
    <view class="item_row flex-row">
      <view class="item_value flex-1">浏览次数</view>
      <view class="item_name">{{ item.browse_count }}</view>
    </view>
    <view class="item_row flex-row">
      <view class="item_value flex-1">领取状态</view>
      <view class="item_name">{{ item.is_receive == 0 ? '未领取' : '已领取' }}</view>
    </view>
    <view class="item_row flex-row">
      <view class="item_value flex-1">访问时间</view>
      <view class="item_name">{{ item.access_time }}</view>
    </view>
    <view class="item_row flex-row">
      <view class="item_value flex-1">停留时长</view>
      <view class="item_name"> {{ item.browse_time }}秒</view>
    </view>
    <!-- <view class="item_row flex-row">
      <view class="item_value flex-1"> 离开时间</view>
      <view class="item_name">{{ item.login_out_time }}</view>
    </view> -->
    <view class="item_row flex-row">
      <view class="item_value flex-1"> 添加时间</view>
      <view class="item_name">{{ item.ctime }}</view>
    </view>
    <!-- <view class="item_row flex-row">
      <view class="item_value flex-1"> 分享者</view>
      <view class="item_name">{{ item.share_name }}</view>
    </view> -->
    <!-- <view class="item_row flex-row">
      <view class="item_value flex-1"> 内部客户分享</view>
      <view class="item_name">{{ item.inner_user_id > 0 ? '是' : '否' }}</view>
    </view> -->

    <view class="item_row flex-row">
      <view class="item_value flex-1"> 锚点名称</view>
      <view class="item_name">{{ item.map_title }}</view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: [Object],
      default: () => { }
    }
  }
}
</script>

<style lang="scss" scoped>
.item_row {
  margin-bottom: 30rpx;
}
</style>