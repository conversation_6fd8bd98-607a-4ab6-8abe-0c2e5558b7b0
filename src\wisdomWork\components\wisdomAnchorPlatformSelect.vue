<template>
    <view class="search-select">
        <wisdomSearchSelect :placeholder="placeholder" :label="label" v-model="isSeled" @open="open" @clear="clear"></wisdomSearchSelect>
        <tPickerPopup :visible.sync="visible" v-model="admin_id" @confirm="confirm" :api="getAnchorPlatforms" :map="{label: 'platform_name', value: 'platform'}"></tPickerPopup>
    </view>
</template>

<script>
import wisdomSearchSelect from './wisdomSearchSelect';
import tPickerPopup from '@/components/tplus/tPickerPopup';
import { getAnchorPlatforms } from '@/common/utils/wisdom-work.js';
export default {
    props: {
        value: { type: [ String, Number ], default: false },
        placeholder: { type: String, default: '请选择' },
    },
    components: {
        wisdomSearchSelect,
        tPickerPopup
    },
    data(){
        return {
            visible: false,
            label: '',
            admin_id: ''
        }
    },
    computed: {
        isSeled(){
            return this.admin_id !== '';
        }
    },
    watch: {
        admin_id(val){
            this.$emit('input', val)
        }
    },
    methods: {
        async getAnchorPlatforms(){
            const res = await getAnchorPlatforms();
            return res || [];
        },
        confirm(e){
            this.visible = false;
            this.label = e? e.label[e.label.length - 1] : '';
            this.$nextTick(()=>{
                this.$emit('select')
            })
        },
        open(){
            this.visible = true;
        },
        clear(){
            this.admin_id = '';
            this.$nextTick(()=>{
                this.$emit('select')
            })
        }
    }
}
</script>
<style lang="scss" scoped>

</style>