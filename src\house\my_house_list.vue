<template>
  <view>
    <view class="index">
      <view class="sticky" id="i_search" ref="i_search">
        <view class="i_search">
          <view class="search">
            <view class="search-box" @click="gotoSearch">
              <myIcon class="icon" type="ic_sousuo3x1" color="#bbb3b3" size="32rpx"></myIcon>
              <text style="text-wrap: nowrap;overflow-x: auto;padding-left: 16rpx;">如: 小区名称 / 业主电话 / 房源编号</text>
            </view>
            <view class="sort" @click="sortChange">
              <uni-icons :type="sortIcon" size="20" color="#488AF6"></uni-icons>
              <text>{{ sortText }}</text>
            </view>
          </view>
        </view>
        <view class="second_tab flex-row flex-1 align-center js-between" id="second_tab">
          <view class="second_tab_item flex-1 flex-row justify-center items-center" @click="changeSecondTab(6)">
            <view class="item_name">{{ typeFilter }}</view>
            <view class="sanjiao"></view>
          </view>
          <view class="second_tab_item flex-1 flex-row justify-center items-center" @click="changeSecondTab(5, '状态')">
            <view class="item_name">
              {{ currentType }}
            </view>
            <view class="sanjiao"></view>
          </view>
          <view class="second_tab_item flex-1 flex-row justify-center items-center" @click="changeSecondTab(5, '售价')">
            <view class="item_name">
              {{ currentPrice }}
            </view>
            <view class="sanjiao"></view>
          </view>
          <view class="second_tab_item flex-1 flex-row justify-center items-center" @click="changeSecondTab(5, '面积')">
            <view class="item_name">
              {{ currentArea }}
            </view>
            <view class="sanjiao"></view>
          </view>
          <view class="second_tab_item flex-1 flex-row justify-center items-center" @click="changeSecondTab(5)">
            <view class="item_name"> 更多 </view>
            <view class="sanjiao"></view>
          </view>
          <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 6">
            <view class="sort-type-box">
              <view class="box-l">
                <text :class="sortSelected == index ? 'selected-l' : ''" v-for="(item, index) in sortType" :key="index"
                  @click="sortTypeChange(index)">{{
                    item.name
                  }}</text>
              </view>
              <view class="box-r">
                <view class="box-r-type" v-if="sortSelected === 0">
                  <text :class="sortTypeSelected == index ? 'selected-r' : ''" v-for="(item, index) in sortTypeSub"
                    :key="index" @click="sortTypeSubChange(item, 'type', index)">{{ item.name }}</text>
                </view>
                <view class="box-r-time" v-if="sortSelected === 1">
                  <text :class="sortTimeSelected == index ? 'selected-r' : ''" v-for="(item, index) in sortTimeSub"
                    :key="index" @click="sortTypeSubChange(item, 'time', index)">{{ item.name }}</text>
                </view>
              </view>
            </view>
            <view class="mask" @click="hideErjiSearch"></view>
          </view>
          <!-- <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 1">
            <view class="second_tab_con_i">
              <view class="second_tab_con_item" v-for="item in typeList" :key="item.id" @click="changeType(item)">
                {{ item.name }}
              </view>
            </view>
            <view class="mask" @click="hideErjiSearch"></view>
          </view> -->

          <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 2">
            <view class="second_tab_con_i">
              <view class="second_tab_con_item" v-for="item in regionList" :key="item.id" @click="changeType(item)">
                {{ item.name }}
              </view>
            </view>
            <view class="mask" @click="hideErjiSearch"></view>
          </view>

          <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 3">
            <view class="second_tab_con_i">
              <view class="second_tab_con_item" v-for="item in priceList" :key="item.id" @click="changeType(item)">
                {{ item.name }}
              </view>
            </view>
            <view class="mask" @click="hideErjiSearch"></view>
          </view>
          <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 4">
            <view class="second_tab_con_i">
              <view class="second_tab_con_item" v-for="item in areaList" :key="item.id" @click="changeType(item)">
                {{ item.name }}
              </view>
            </view>
            <view class="mask" @click="hideErjiSearch"></view>
          </view>
        </view>
      </view>
      <view class="filter_container" :class="{ show: current_second_tab == 5 }" @touchmove.stop.prevent="disMove">
        <view class="filter_box" :style="{ top: filterTop }">
          <view class="filter_list flex-row">
            <scroll-view class="filter_labels" scroll-y>
              <view class="item" v-for="(item, index) in filtersNew" :key="index" @click="checkFilter(index, item)"
                :class="{ active: index == scroll_into_view_index }">{{ item.label }}</view>
            </scroll-view>
            <scroll-view class="filter_options" id="filter_options" scroll-y scroll-with-animation
              :scroll-into-view="scroll_into_view" @scroll="onFilterOptionsScroll">
              <view class="item" v-for="(filter_item, index) in filtersNew" :key="index" :id="filter_item.id">
                <view class="label flex-row">{{ filter_item.label }}
                  <text v-if="filter_item.unit">({{ filter_item.unit }})</text>
                </view>
                <view class="value_list" v-if="!filter_item.multiple">
                  <view class="value" v-for="(option, idx) in filter_item.item" :key="idx"
                    :class="{ active: erjiParams[filter_item.type] === option.values }"
                    @click="setFilterItem(option, filter_item.type)">{{ option.name }}</view>
                  <view class="empty_value" v-if="filter_item.item.length % 3 === 2"></view>
                  <view class="input_range flex-row" v-if="filter_item.customizale === 1 && filter_item.customizale">
                    <input class="input" v-model="filter_item.custom_value.min"
                      @input="onCustomFilterChange(index, 'min')" type="number" placeholder="最低" />
                    <view class="mark">~</view>
                    <input class="input" v-model="filter_item.custom_value.max"
                      @input="onCustomFilterChange(index, 'max')" type="number" placeholder="最高" />
                  </view>
                </view>
                <view class="value_list" v-else>
                  <view class="value" v-for="(option, idx) in filter_item.item" :key="idx"
                    :class="{ active: erjiParams[option.type] === option.values }" @click="setFilterItem(option)">{{
                      option.name }}</view>
                  <view class="empty_value"></view>
                </view>
              </view>
              <view style="height: 40vh"></view>
            </scroll-view>
          </view>
          <view class="flex-row btn_group">
            <view class="btn" @click="resetData()">清空</view>
            <view class="btn bg_highlight" @click="filterData()">确定</view>
          </view>
        </view>
        <view class="mask" @click="showMask = false"></view>
      </view>
      <!-- 三级分类 -->
      <view class="third_tab flex-row flex-1 align-center j-between">
        <view class="nav-box">
          <scroll-view scroll-x scroll-with-animation :scroll-into-view="'i' + (current_third_index > 0 ? current_third_index - 1 : current_third_index)
            " class="nav-list">
            <view class="nav-item" :class="{ active: current_third_index == index }" :id="'i' + index"
              v-for="(item, index) in thirdTabs" :key="item.values" @click="changThirdTab(item, index)">
              <text>{{ item.name }} </text>
            </view>
          </scroll-view>
        </view>
      </view>

      <view class="house_list">
        <view class="house" v-for="house in house_list" :key="house.id">
          <privateItem class="flex-1" :house="house" @toDetail="checkPerssion($event, toPrivateDetail)"
            @copy="checkPerssion($event, copyPrivate)" @follow="checkPerssion($event, followPrivate)"
            @addLianmai="checkPerssion($event, addToLianmai)" @addShowing="checkPerssion($event, addToShowing)"
            @houseGroup="checkPerssion($event, houseGroup)" @setLevels="checkPerssion($event, setLevels)"
            @setLevel="checkPerssion($event, setLevels)" @tixing="checkPerssion($event, tixing)"
            @houseTop="checkPerssion($event, houseTop)" @houseDel="checkPerssion($event, houseDel)"></privateItem>
        </view>
        <loadMore :status="load_status" @reload="getData()" />
      </view>
      <view class="add flex-box items-center justify-center" @click="toAdd">
        <view class="icon">
          <image :src="'/static/house/img/<EMAIL>' | imageFilter('w_80')"></image>
        </view>
        <view class="add_name"> 录房 </view>
      </view>
      <my-popup :show="show_set_showing" @close="show_set_showing = false">
        <view>
          <setShowing :is_status="true" :house="current_house" @success="setShowingSuccess" />
        </view>
      </my-popup>
    </view>
  </view>
</template>

<script>
import myIcon from "@/components/my-icon";
import Tabs from './components/Tabs'
import privateItem from './components/privateItem.vue'
import loadMore from '@/components/loadMore'
import myPopup from '@/components/myPopup.vue'
import setShowing from './components/setShowing.vue'
import search from './components/search.vue'
import { copyPrivate } from '@/page_outside/private.js'
export default {
  components: {
    Tabs,
    privateItem,
    loadMore,
    myPopup,
    setShowing,
    search,
    myIcon
  },
  data() {
    return {
      sortSelected: 0,
      sortType: [
        { name: '类型', value: 0 },
        { name: '时间', value: 1 },
      ],
      sortTypeSelected: 0,
      sortTypeSub: [
        { name: '全部', value: 0 },
        { name: '出租', value: 2 },
        { name: '出售', value: 1 },
      ],
      sortTimeSelected: 0,
      sortTimeSub: [
        { name: '录入时间', value: 2 },
        { name: '跟进时间', value: 1 }
      ],
      sortText: '降序',
      sortIcon: 'arrow-down',
      // rentSaleValue: 0,
      // rentSaleRange: [
      //   { value: 0, text: "全部" },
      //   { value: 1, text: "出租" },
      //   { value: 2, text: "出售" },
      // ],
      // sortValue: 0,
      // sortRange: [
      //   { value: 0, text: "降序" },
      //   { value: 1, text: "升序" }
      // ],
      params: {
        page: 1,
        rows: 20,
        trade_type: '',
        order_type: 2,
        is_owner: 2
      },
      current_tab: 0,
      tab_list: [],
      load_status: 'loading',
      show_edit: false,
      house_list: [],
      is_collect: false,
      type: 0,
      current_house: {},
      show_set_showing: false,
      show_loudong_search: false,
      loudong_search: '110rpx',
      otherParams: {
        loudong: '',
        danyuan: '',
        fanghao: '',
        tel: ''
      },
      erjiParams: {
        region_id: '',
        mianji: '',
        sale_price: '',
        rent_price: '',
        trade_status: 9,
        order_sort: 'desc'
      },
      sanjiSimiParams: {
        exclusive: '',
        has_key: '',
        youxiao: '',
        wuxiao: '',
        yichengjiao: '',
        fenyong: '',
      },
      show_tel_search: false,
      current_second_tab: '',
      typeFilter: '类型',
      typeList: [],
      regionList: [],
      priceList: [],
      areaList: [],
      current_third_index: 0,
      showMask: false,
      filterTop: '220rpx',
      scroll_into_view: '',
      scroll_into_view_index: '',
      filters: [],
      show_comm_search: false,
      unfollowInfo: {
      }
    }
  },
  onLoad(options) {
    if (options.trade_type >= 0) {
      this.type = options.trade_type
      this.params.trade_type = options.trade_type
    }
    if (options.trade_status) {
      this.params.trade_status = options.trade_status
    }
    this.getData()
    this.getOptions()
    uni.$once("getDataAgain", (res) => {
      this.params.page = 1
      this.getData()
      this.getOptions()
    })
  },
  computed: {
    currentType() {
      let type = '状态'
      if (this.erjiParams.trade_status && this.typeList.length > 0) {
        let res = this.typeList.find((item) => item.values == this.erjiParams.trade_status)
        if (res && res.name) {
          type = res.name
        }
      }
      return type
    },
    currentArea() {
      let area = '面积'
      if (this.erjiParams.mianji && this.areaList.length > 0) {
        let res = this.areaList.find((item) => item.id == this.erjiParams.mianji)
        if (res && res.name) {
          area = res.name
        }
      }
      return area
    },
    currentRegion() {
      let region = '商圈'
      if (this.erjiParams.region_id && this.regionList.length > 0) {
        let res = this.regionList.find((item) => item.id == this.erjiParams.region_id)
        if (res && res.name) {
          region = res.name
        }
      }
      return region
    },
    currentPrice() {
      let price = '价格'
      if ((this.erjiParams.rent_price || this.erjiParams.sale_price) && this.priceList.length > 0) {
        let res = this.priceList.find(
          (item) => item.id == (this.erjiParams.rent_price || this.erjiParams.sale_price)
        )
        if (res && res.name) {
          price = res.name
        }
      }
      return price
    },
    filtersNew() {
      return this.filters.filter((item) => {
        return item.type !== 'yongtu_id'
        // return this.filters
      })
    },
    thirdTabs() {
      let f = this.filters.filter((item) => {
        return item.type == 'label_type'
        // return this.filters
      })
      if (f.length) {
        return f[0].item
      }
      return []
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.query = uni.createSelectorQuery().in(this)
      this.setFilterTop()
    })
  },
  methods: {
    sortTypeSubChange(item, type, index) {
      if (index == 0) {
        this.params.trade_type = ''
        this.params.order_type = ''
        this.sortTypeSelected = 0
        this.sortTimeSelected = 0
      }
      if (type == 'type' && index != 0) {
        this.params.trade_type = item.value
        this.sortTypeSelected = index
      } else if (type == 'time') {
        this.params.order_type = item.value
        this.sortTimeSelected = index
      }
      this.getData()
    },
    sortTypeChange(index) {
      this.sortSelected = index
    },
    sortChange() {
      if (this.sortText == '降序') {
        this.sortText = '升序'
        this.sortIcon = 'arrow-up'
        this.erjiParams.order_sort = 'asc'
      } else {
        this.sortText = '降序'
        this.sortIcon = 'arrow-down'
        this.erjiParams.order_sort = 'desc'
      }
      this.params.page = 1
      this.house_list = []
      this.getData()
    },
    gotoSearch() {
      this.$navigateTo(`/house/search?is_owner=2`)
    },
    getUnfollow() {
      this.$ajax.get("/admin/house/remindPhoneFollow", {}, res => {
        if (res.statusCode == 200) {
          this.unfollowInfo = res.data
        }
      })
    },
    checkPerssion(e, callback) {
      if (this.unfollowInfo.remindPhoneFollow && this.unfollowInfo.count > 0) {
        uni.showModal({
          title: "提示",
          content: `您有${this.unfollowInfo.count}条未跟进记录需要跟进,立即跟进？`,
          confirmText: "立即跟进",
          success: (res) => {
            if (res.confirm) {
              this.$navigateTo(`/house/detail?id=${this.unfollowInfo.info.info_id}`)
            }
          }
        })
      } else {
        callback && callback(e)
      }
    },
    getData() {
      if (this.params.page == 1) {
        this.house_list = []
      }
      let params = Object.assign({}, this.params, this.erjiParams, this.sanjiSimiParams, this.otherParams)
      this.load_status = 'loading'
      this.$ajax.get('/admin/house/privateHouses', params, (res) => {
        if (res.statusCode == 200) {
          if (res.data.data.length < this.params.rows) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'loadend'
          }
          let current_login_user = res.data.current_login_user || {}
          res.data.data.map(item => {
            item.current_login_user = current_login_user
            return item
          })
          this.house_list = this.house_list.concat(res.data.data)
        } else {
          this.load_status = 'nomore'
        }
      }, () => {
        this.load_status = 'nomore'
      })

    },
    getOptions() {
      this.$ajax.get("/admin/house/houseConditionByLm", {}, res => {
        if (res.statusCode == 200) {
          let _filters = res.data.second.concat(res.data.first)
          let status = _filters.find(item => item.type == 'trade_status')
          if (status) {
            this.typeList = status.item
          }
          this.filters = _filters.map((e, index) => {
            // 区域传参是传的汉字，需要把valus的值设置为其name的值
            if (e.type === 'districtName') {
              e.item.map((chil) => (chil.values = chil.name))
            }
            if (e.type == 'region_id') {
              this.regionList = e.item.map((region) => {
                region.id = region.values
                return region
              })
              this.regionList.unshift({
                id: '',
                name: "全部"
              })
            }
            if (e.type == 'sale_price') {
              this.priceList = e.item.map((price) => {
                price.id = price.values
                return price
              })
              this.priceList.unshift({
                id: '',
                name: "全部"
              })
            }
            if (e.type == 'mianji') {
              this.areaList = e.item.map((mianji) => {
                mianji.id = mianji.values
                return mianji
              })
              this.areaList.unshift({
                id: '',
                name: "全部"
              })
            }
            // 如果支持自定义数值
            if (e.customizale) {
              e.custom_value = {
                min: '',
                max: '',
              }
            }
            if (e.unit) {
              var unit_reg = new RegExp(e.unit, 'g')
              e.item.map((chil) => {
                chil.name = chil.name.replace(unit_reg, '')
              })
            }
            if (!e.multiple && e.item.findIndex((chil) => chil.values === '') === -1) {
              // 追加一个不限
              e.item.unshift({
                name: '不限',
                values: '',
              })
            }
            e.id = `filter_${index}`
            return e
          })
          let state = {
            customizale: 0,
            id: `filter_${this.filters.length + 1}`,
            label: '状态',
            item: [
              { name: '不限', values: '' },
              { name: '有效', values: 9 },
              { name: '无效', values: 8 },
              { name: '暂缓', values: 5 },
              { name: '待定', values: 102 },
              { name: '我司成交', values: 100 },
              { name: '他司成交', values: 101 }
            ],
            multiple: 0,
            type: 'trade_status',
            unit: ''
          }
          this.filters.unshift(state)
          console.log(this.filters, 'filters----')
          this.scroll_into_view = this.filters[0].id
        }
      })
    },
    toPrivateDetail(e) {
      this.$navigateTo(`/house/detail?id=${e.id}`)
    },
    copyPrivate(e) {
      this.$ajax.get(`/admin/house/shareHouse/${e.id}`, {}, res => {
        if (res.statusCode == 200) {
          this.$set(e, 'current_login_user', res.data)
          copyPrivate(e)
        } else {
          uni.showToast({
            title: res.data.message || '请求失败',
            icon: 'none'
          })
        }
      })
    },
    followPrivate(e) {
      this.$navigateTo(`/house/follow_up?follow_id=${e.id}&from=private`)
    },
    houseGroup(house) {
      this.$navigateTo("/house/house_send?house_id=" + house.id + "&protect=" + house.protect)
    },
    tixing(e) {
      this.$navigateTo(`/house/remind?remind_id=${e.id}`)
    },
    setLevels(e) {
      if (e.unilateral_agent == 1 && e.unilateral_agent_auth == 0) {
        uni.showToast({
          title: '请联系房源维护人',
          icon: 'none',
        })
        return
      }

      // /editPrivateHouse/{id}
      this.$ajax.post('/admin/house/editPrivateHouse', { id: e.house.id, level: e.level }, (res) => {
        if (res.statusCode == 200) {
          uni.showToast({
            title: '等级更改成功',
            icon: 'none',
          })
          this.params.page = 1
          this.getData()
        } else {
          uni.showToast({
            title: '等级更改失败',
            icon: 'none',
          })
        }
      }, () => {
        uni.showToast({
          title: '等级更改失败',
          icon: 'none',
        })
      })
    },
    addToLianmai(house) {
      this.showModal({
        title: '确定发布到联卖房源吗？',
        confirm: () => {
          this.$ajax.get(`/v1/wapLm/releaseToLm/${house.id}`).then((res) => {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
            })
          })
        },
      })
    },
    toAdd() {
      this.$navigateTo('/house/add?tradeStatus=' + (this.current_tab || 1))
    },
    toPage(name, query = {}) {
      this.$navigateTo({
        name,
        query,
      })
    },
    addToShowing(e) {
      this.current_house = e
      this.show_set_showing = true
    },
    setShowingSuccess() {
      this.show_set_showing = false
      this.getData()
    },
    houseTop(e) {
      uni.showModal({
        title: '是否聚焦该房源？',
        success: (result) => {
          if (result.confirm) {
            this.$ajax.post('/v1/wapLm/editPrivateHouse', { id: e.id, is_top: 1 }, (res) => {
              uni.showToast({
                title: res.data.message,
                icon: 'none',
              })
              if (res.data.status == 200) {
                this.params.page = 1
                this.initData()
              }
            })
          } else if (result.cancel) {
            console.log('用户点击取消');
          }

        },
      })
    },
    houseDel(e) {
      uni.showModal({
        title: '是否删除该房源？',
        success: (result) => {
          if (result.confirm) {
            this.$ajax.post('/v1/wapLm/editPrivateHouse', { id: e.id, is_del: 1 }, (res) => {
              uni.showToast({
                title: res.data.message,
                icon: 'none',
              })
              if (res.data.status == 200) {
                this.params.page = 1
                this.initData()
              }
            })
          } else if (result.cancel) {
            console.log('用户点击取消');
          }

        },
      })
    },
    showCommSearch() {
      this.show_tel_search = false
      this.show_loudong_search = false
      this.setLoudongTop()

      this.show_comm_search = !this.show_comm_search
    },
    showLoudongSearch() {
      this.show_tel_search = false
      this.show_comm_search = false
      this.setLoudongTop()
      this.show_loudong_search = !this.show_loudong_search
    },
    initData() {
      this.params.page = 1
      this.getData()
    },
    setLoudongTop() {
      const current_node_shaixuan = this.query.select('#i_search')
      let top = 0,
        i_height = 0
      current_node_shaixuan
        .fields({ rect: true, size: true }, (res) => {
          top = res.top
          i_height = res.height
        })
        .exec()
      this.loudong_search = top + i_height + 110 + 'px'
    },
    showTelSearch() {
      this.show_loudong_search = false
      this.show_comm_search = false
      this.setLoudongTop()
      this.show_tel_search = !this.show_tel_search
    },
    filterTelData() {
      this.show_tel_search = false
      this.initData()
    },
    resetLoudongData() {
      this.otherParams.loudong = ''
      this.otherParams.danyuan = ''
      this.otherParams.fanghao = ''
    },
    filterLoudongData() {
      this.show_loudong_search = false
      this.initData()
    },
    resetCommData() {
      this.otherParams.keyword = ''
      this.otherParams.hid = ''
    },
    filterCommData() {
      this.show_comm_search = false
      this.initData()
    },
    setFilterTop() {
      const current_node_shaixuan = this.query.select('#second_tab')
      current_node_shaixuan
        .fields({ rect: true, size: true }, (res) => {
          this.filterTop = +res.top + +res.height + 'px'
        })
        .exec()
    },
    changeType(item) {
      this.showMask = false
      switch (this.current_second_tab) {
        case 1:
          this.erjiParams.trade_status = item.values
          break
        case 2:
          this.erjiParams.region_id = item.id
          break
        case 3:
          if (
            this.erjiParams.trade_type == 1 ||
            this.erjiParams.trade_type == 3 ||
            !this.erjiParams.trade_type
          ) {
            delete this.erjiParams.rent_price
            this.erjiParams.sale_price = item.values
          } else {
            delete this.erjiParams.sale_price
            this.erjiParams.rent_price = item.values
          }
          // this.erjiParams.price = item.id
          break
        case 4:
          this.erjiParams.mianji = item.id
          break
        case 6:
          this.erjiParams.order_type = item.value
          break
        case 7:
          this.erjiParams.order_sort = item.value
          break
        default:
          break
      }
      this.current_second_tab = ''
      this.initData()
    },
    changeSecondTab(index, type) {
      if (this.current_second_tab == index) return (this.current_second_tab = '')
      if (index == 5 && type) {
        this.scroll_into_view = ''
        this.scroll_into_view_index = ''
        this.dataExchange(type)
      }
      this.current_second_tab = index
      this.showMask = true
    },
    dataExchange(type) {
      let newArr = [...this.filters]
      let isSort = newArr.some((item, index) => item.label == type && index != 0)
      if (isSort) {
        let _index = newArr.findIndex((item, index) => item.label == type)
        newArr.unshift(newArr[_index]);
        newArr.splice(_index += 1, 1)
        this.filters = [...newArr]
        return this.filters
      }
    },
    changThirdTab(item, index) {
      this.current_third_index = index
      this.$set(this.sanjiSimiParams, 'label_type', item.values)
      this.params.page = 1
      this.initData()
    },
    resetData() {
      // 切换清空min，max参数
      this.resetFilterArr()
      this.erjiParams = {
        rent_price: '',
        sale_price: '',
      }
      this.initData()
      this.current_second_tab = ''
      this.showMask = false
    },
    // 清空筛选中输入的min和max
    resetFilterArr() {
      // 切换清空min，max参数
      this.filters.map((item) => {
        if (item.customizale == 1 && (item.custom_value.max || item.custom_value.min)) {
          item.custom_value.max = ''
          item.custom_value.min = ''
        }
      })
    },


    filterData() {
      this.initData()
      this.current_second_tab = ''
      this.showMask = false
    },
    hideErjiSearch() {
      this.current_second_tab = ''
      this.showMask = false
    },
    setFilterItem(option, type) {
      // this.params[type] = option.values
      if (type) {
        this.$set(this.erjiParams, type, option.values)
      } else if (option.type) {
        if (this.erjiParams[option.type]) {
          this.$set(this.erjiParams, option.type, '')
        } else {
          this.$set(this.erjiParams, option.type, option.values)
        }
      }
    },
    checkFilter(index, item) {
      console.log(index, item)
      this.scroll_into_view = item.id
      this.scroll_into_view_index = index
    },
    onFilterOptionsScroll(e) {
      for (let i = 0; i < this.filters.length; i++) {
        var scroll_difference = e.detail.scrollTop - this.filters[i].offsetTop
        if (scroll_difference >= -5 && scroll_difference <= this.filters[i].height) {
          this.scroll_into_view = ''
          this.scroll_into_view_index = i
          continue
        }
      }
    },

  },
  onReachBottom() {
    if (this.load_status === 'loadend') {
      this.params.page++
      this.getData()
    }
  },
  onNavigationBarButtonTap(e) {
    if (e.index === 0) {
      this.show_edit = !this.show_edit
    }
  },
}
</script>

<style lang="scss">
.selected-l {
  color: #2671ff !important;
}

.selected-r {
  color: #2671ff !important;
  border: 1px solid #2671ff !important;
}

.sort-type-box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  padding: 20rpx 20rpx 40rpx;
  background-color: #fff;

  .box-l {
    display: flex;
    width: 25%;

    text {
      padding: 20rpx;
      color: #666;
      font-size: 24rpx;
    }
  }

  .box-r {
    width: 100%;
    margin-top: 5rpx;
    padding-left: 20rpx;
    border-left: 1px solid #f3f3f3;

    text {
      padding: 10rpx 20rpx;
      margin-right: 20rpx;
      font-size: 24rpx;
      border: 1px solid #f3f3f3;
      color: #666;
      border-radius: 3px;
    }

    view {
      display: flex;
      flex-direction: row;
    }
  }
}

/deep/.uni-select {
  padding: 0 !important;
  border: 0 !important;
}

.uni-select__selector {
  width: 120rpx !important;
}

.uni-select__input-text {
  color: #666 !important;
}

.active {
  position: relative;
  color: #2671ff !important;
}

.index {
  ::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
  }

  .sticky {
    position: sticky;
    top: 0;
    z-index: 9;
    box-shadow: 0 -1px #fff;
    box-shadow: 0 1px 8px 0 rgba(100, 100, 100, 0.1);
    background-color: #fff;
  }

  .tab-box {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 20rpx;
    z-index: 9;
    background: #fff;
  }

  .house_list {
    // padding: 0 48rpx;
    width: 100vw;

    // overflow: hidden;
    &.padtop64 {
      padding-top: 64rpx;
    }

    .house {
      // padding: 24rpx 0;
      // overflow: hidden;
      display: flex;
      flex-direction: row;
      align-items: flex-start;
    }
  }
}

.add {
  position: fixed;
  right: 24rpx;
  bottom: 150rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-image: linear-gradient(135deg, #4daaff 0%, #2671ff 100%);
  box-shadow: 0px 4px 10px 0px rgba(46, 124, 255, 0.4);
  z-index: 3;

  .icon {
    width: 48rpx;
    height: 48rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .add_name {
    color: #fff;
    font-size: 24rpx;
  }
}

.i_search {
  z-index: 9;
  background: #fff;
  position: relative;
  padding: 24rpx 32rpx;

  ::v-deep .input-box {
    border-radius: 10rpx;
  }

  .search {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .search-box {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      height: 80rpx;
      border-radius: 16rpx;
      padding: 0 24rpx;
      background-color: #f3f3f3;

      text {
        color: rgba(41, 44, 57, 0.4);
        margin-left: 3px;
      }
    }

    .sort {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 74rpx *2;
      height: 100%;
      color: #488AF6;
      font-size: 28rpx;
      padding-left: 20rpx;
    }
  }

  .search_loudong {
    background: #f8f8f8;
    height: 76rpx;
    justify-content: center;
    align-items: center;
    padding: 0 20rpx;
    margin-left: 10rpx;
    font-size: 24rpx;
    color: #999;

    &.search_comm {
      justify-content: space-between;
    }

    .xiasanjiao {
      height: 0;
      width: 0;
      border: 10rpx solid transparent;
      border-top-color: #999;
      margin-left: 10rpx;
      margin-top: 10rpx;
    }

    .shangsanjiao {
      height: 0;
      width: 0;
      border: 10rpx solid transparent;
      border-bottom-color: #999;
      margin-left: 10rpx;
      margin-bottom: 10rpx;
    }
  }

  .doorplate {
    height: 100%;
    padding: 0;
    background-color: #fff;
  }
}

.search_container {
  position: fixed;
  left: 0;
  right: 0;
  top: 110rpx;

  background: #fff;
  z-index: 8;
  transform: translateY(-300%);
  transition: 0.26s;

  .search_con {
    background: #fff;
    padding: 24rpx 48rpx 48rpx;
    z-index: 5;
  }

  .search_title {
    color: #333;
    font-weight: 700;
    padding-bottom: 20rpx;
  }

  .search_inp {
    border: 2rpx solid #f0f0f0;
    border-radius: 4rpx;
    padding: 10rpx;

    ~.search_inp {
      margin-left: 20rpx;
    }

    &.mat10 {
      margin-top: 10rpx;
    }

    &.mal0 {
      margin-left: 0;
    }

    input {
      color: #666;
    }

    .pls {
      color: #d6d6d6;
    }
  }

  .btn_group {
    // min-height: 100rpx;
    box-shadow: 0 -6rpx 10rpx 0 #f1f1f1;
    margin-top: 40rpx;

    .btn {
      padding: 12rpx 24rpx;
      flex: 1;
      text-align: center;

      &.bg_highlight {
        background-color: $color-primary;
        color: #fff;
      }
    }
  }

  .mask {
    position: fixed;
    z-index: -1;
    width: 100%;
    height: 100vh;
    left: 0;
    background-color: #000;
    display: none;
    transition: 0.26s;
  }

  &.show {
    // .filter_box {
    // z-index: 5;
    transform: translateY(0);

    // }
    .mask {
      display: flex;
      z-index: 1;
      opacity: 0.3;
    }
  }
}

.second_tab {
  position: relative;
  display: flex;
  justify-content: space-between;
  padding: 30rpx 0;
  background: #fff;

  .second_tab_item {
    max-width: 20%;

    .item_name {
      font-size: 24rpx;
      margin-right: 8rpx;
      color: #8a929f;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
    }

    .sanjiao {
      margin-top: 10rpx;
      width: 0;
      height: 0;
      border: 8rpx solid;
      border-color: #aaabb0 transparent transparent transparent;
    }
  }

  .second_tab_con {
    position: absolute;
    top: 75rpx;
    left: 0;
    right: 0;
    z-index: 5;
    // padding: 0 48rpx;
    background: #fff;

    .second_tab_con_i {
      background: #fff;
      padding: 0 48rpx;
      max-height: 60vh;
      overflow-y: auto;
    }

    .second_tab_con_item {
      padding: 20rpx 0;
    }

    .mask {
      position: fixed;
      z-index: -1;
      width: 100%;
      height: 100vh;
      left: 0;
      background-color: #000;
      opacity: 0.5;
      transition: 0.26s;
    }
  }
}

.nav-box {
  width: 100%;
  // background-color: #fff;
  // border-bottom: 1upx solid $uni-border-color;
  // box-shadow: 0 4upx 10upx #e6e6e6;
}

.third_tab {
  width: 100%;
  padding: 40rpx 32rpx 0;
}

.filter_container {
  touch-action: none;

  .filter_box {
    padding-top: 24rpx;
    position: fixed;
    z-index: 5;
    width: 100%;
    left: 0;
    top: 200rpx;
    overflow: hidden;
    background-color: #fff;
    max-height: calc(100vh - 200rpx - 200px);
    transform: translateY(-150%);
    transition: 0.26s;

    .filter_list {
      flex: 1;
      overflow: hidden;
    }

    .filter_labels {
      width: 200rpx;
      text-align: center;
      background-color: $bg-color-grey;

      .item {
        padding: 24rpx 12rpx;

        &.active {
          background-color: #fff;
          color: $color-primary;
        }
      }
    }

    .filter_options {
      padding: 0 24rpx;

      .item {
        .label {
          padding: 24rpx 0;
        }

        .value_list {
          flex-direction: row;
          flex-wrap: wrap;
          justify-content: space-between;
        }

        .value {
          width: 172rpx;
          margin-bottom: 24rpx;
          text-align: center;
          padding: 16rpx 6rpx;
          border-radius: 4rpx;
          font-size: 24rpx;
          // flex: 1;
          background-color: $bg-color-grey;

          &.active {
            background-color: rgba($color: $color-primary, $alpha: 0.2);
            color: $color-primary;
          }
        }

        .empty_value {
          width: 172rpx;
        }

        .input_range {
          margin-bottom: 24rpx;
          align-items: center;

          .input {
            padding: 12rpx;
            height: 64rpx;
            width: 172rpx;
            font-size: 24rpx;
            background-color: $bg-color-grey;
          }

          .mark {
            width: 24rpx;
            text-align: center;
          }
        }
      }
    }
  }

  .btn_group {
    // min-height: 100rpx;
    box-shadow: 0 -6rpx 10rpx 0 #f1f1f1;

    .btn {
      padding: 24rpx;
      flex: 1;
      text-align: center;

      &.bg_highlight {
        background-color: $color-primary;
        color: #fff;
      }
    }
  }

  .mask {
    position: fixed;
    z-index: -1;
    width: 100%;
    height: 100vh;
    left: 0;
    background-color: #000;
    opacity: 0;
    transition: 0.26s;
  }

  &.show {
    .filter_box {
      // z-index: 5;
      transform: translateY(0);
    }

    .mask {
      z-index: 4;
      opacity: 0.5;
    }
  }
}

.nav-list {
  width: 100%;
  white-space: nowrap;
}

.nav-item {
  display: inline-block;
  padding: 12rpx 16rpx;
  border-radius: 4rpx;
  margin-right: 24rpx;
  font-size: 22rpx;
  background: #f3f3f3;
  color: #8a929f;

  image {
    // width: 40rpx;
    height: 18rpx;
    object-fit: cover;
  }
}

.nav-item .active {
  color: #fff;
  position: relative;
  // background: #2d84fb;
}
</style>
