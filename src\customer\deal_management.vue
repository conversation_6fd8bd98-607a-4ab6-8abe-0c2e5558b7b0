<template>
  <view class="deal">
    <myTabbar :fixedTop="false" custom :tabs="tabs" class="istabs">
      <view class="tabs-list row">
        <view
          class="tabs-item"
          :class="{ isactive: params.type == item.id }"
          v-for="item in tabs"
          @click="onClickType(item)"
          :key="item.id"
          >{{ item.name }}</view
        >
      </view>
    </myTabbar>
    <view class="list">
      <menlist :arr="tableData"></menlist>
    </view>
  </view>
</template>

<script>
import myTabbar from "@/components/tabBar.vue";
import menlist from "./components/men_list";
export default {
  components: {
    myTabbar,
    menlist,
  },
  data() {
    return {
      params: {
        type: 0,
      },
      tabs: [
        { id: 0, name: "全部" },
        { id: 1, name: "最新" },
        { id: 2, name: "审批中" },
        { id: 3, name: "已同意" },
        { id: 4, name: "已拒绝" },
      ],
      tableData: [
        {
          id: 1,
          type_name: "佣金分成",
          type: 0,
          tracking_name: "合作分成",
          cname: "张先生",
          remark:
            "合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/default.jpg?x-oss-process=style/w_80",
          user_name: "申请人",
          created_at: "2022-01-01 12:12:12",
        },
        {
          id: 2,
          type_name: "佣金分成",
          type: 1,
          tracking_name: "合作分成",
          cname: "张先生",
          remark:
            "合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/default.jpg?x-oss-process=style/w_80",
          user_name: "申请人",
          created_at: "2022-01-01 12:12:12",
        },
        {
          id: 3,
          type_name: "佣金分成",
          type: 2,
          tracking_name: "合作分成",
          cname: "张先生",
          remark:
            "合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/default.jpg?x-oss-process=style/w_80",
          user_name: "申请人",
          created_at: "2022-01-01 12:12:12",
        },
        {
          id: 4,
          type_name: "佣金分成",
          type: 3,
          tracking_name: "合作分成",
          cname: "张先生",
          remark:
            "合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容合作内容",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/default.jpg?x-oss-process=style/w_80",
          user_name: "申请人",
          created_at: "2022-01-01 12:12:12",
        },
      ],
    };
  },
  methods: {
    onClickType(item) {
      this.params.type = item.id;
    },
  },
};
</script>
<style scoped lang="scss">
page {
  background: #f6f6f6;
}
.istabs {
  background: none;
  padding: 12px;
  .tabs-list {
    .tabs-item {
      font-size: 14px;
      color: #8a929f;
      padding: 8px 18px;
      line-height: 1;
      &.isactive {
        border-radius: 12px;
        color: #2d84fb;
        background: #fff;
      }
    }
  }
}
.list {
  margin-top: -12px;
  padding: 0 12px;
}
</style>
