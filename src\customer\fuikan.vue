<template>
  <view>
   <view class="reasonall">
    <view class="reason">
      <view class="block row">
          <view class="follow">
            带看单号
          </view>
          <view class="follow_c ">
            <input type="text" placeholder-style="font-size:32rpx;text-align: end;padding-right: 50rpx;" placeholder="请输入带看单号" v-model ='follow_see_params.take_no'>
          </view>
        </view>
        <view class="block row">
          <view class="follow fs">
            陪看人员
          </view>
          <view class="follow_c  flex-row" @click ="showPeikan">
            <template v-if ='selectPeikan&&selectPeikan.length'>
              <view class="peikan_label flex-row" v-for ='(item,index) in selectPeikan' :key ="index">
                <view class="name">
                  {{item.name}}
                </view>
                <view class="del" @click.prevent.stop = "delPeikan(item)">
                  <my-icon type="guanbi" size="26rpx">

                  </my-icon>
                </view>
              </view>
            </template>
            <template v-else >
              <view class="peikan_label empty" >
                请选择陪看人员
              </view>
            </template>
<template>
  <view style="margin-top: 14rpx;">
    <image src="../static/icon/index/箭头 .png" style="width:32rpx;height:32rpx"></image>
  </view>
</template>
            <!-- <selectDown
              valueName="name"
              :list="peikanList"
              :multiple="true"
              @input="changeSelect"
              defaultValue="请选择陪看人员"
              placeholder="请选择陪看人员"
            >
            </selectDown> -->
          </view>

        </view>
        <view class="block row">
          <view class="follow">
          带看日期
          </view>
          <view class="follow_c" style="display: flex;flex-direction: row;flex-wrap: nowrap;justify-content: space-between;">
            <picker
                mode="date"
                value=""
                @change="changeDate"
              >
                <view class="uni-input">{{ follow_see_params.take_date || '请选择带看日期'  }}</view>
              </picker>
              <view >
    <image src="../static/icon/index/箭头 .png" style="width:32rpx;height:32rpx;margin-left: 20rpx;"></image>
  </view>
          </view>

 

        </view>
     
        <view class="block row">
          <view class="follow">
          带看时间
          </view>
          <view class="follow_c flex-1">
            <view class="follow_time row">
              <view class="follow_time_item flex-1 " :class ="{active:follow_see_params.take_time ==1}" @click = 'follow_see_params.take_time =1'>
                上午
              </view>

              <view class="follow_time_item flex-1 " :class ="{active:follow_see_params.take_time ==2}"  @click = 'follow_see_params.take_time =2'>
                下午
              </view>
              <view class="follow_time_item flex-1 " :class ="{active:follow_see_params.take_time ==3}"  @click = 'follow_see_params.take_time =3'>
                晚上
              </view>
            </view>
          </view>
        </view>
       
      </view>
      <view >
          <view class="follow_call">
            <view class="follow_gj">跟进内容</view>
            <textarea  placeholder="请输入跟进内容 （企业内公开）" rows="3" v-model="follow_see_params.content" class="texttarea"></textarea>
            <!-- <view class="add_friend" :class="{ to_pic: imageList.length }" @click="toPic">
              <view class="title"> +图片 </view>
            </view> -->
          </view>
        </view>
        <view class="btns" >
          <view class="btn" @click ="submit">确定</view>
        </view>
    <myPopup
      ref="showMember"
      :show="show_peikan"
      position="center"
      @close="show_peikan = false"
    >
      <view class="p_con" v-if ='show_peikan'>
        <memberSelect :admin_list='peikanList' :selectPeikan="selectPeikan"   @cancel ="show_peikan=false" @selectedOk = 'selectMemberOk' defaultName="name" :multiple="true" defaultValue="values"></memberSelect>
      </view>
    </myPopup>
   </view>
  </view>
  
</template>

<script>
import myPopup from "@/outbound/components/myPopup";
import myIcon from "@/components/my-icon";
import memberSelect from "@/customer/components/memberSelect";
// import selectDown from '@/house/components/w-select'
export default {
  components:{
    myPopup,
    memberSelect,
    myIcon
  },
  data(){
    return {
      follow_see_params:{
         content:"",
          take_date:"",
          take_time:1,
          take_no:"",
          accompany:[]
      },
      peikanList:[],
      genjin_title:'带看跟进',
      show_peikan:false,
      selectPeikan:[]

    }
  },
  onLoad(options){
    this.follow_see_params.client_id = options.id
    this.follow_see_params.type = options.type
    this.getPeikanList()
    if( options.type==2){
      this.genjin_title = "复看跟进"
    }
  },
  methods:{
    // // 添加图片
    // toPic() {
    //   if (this.imageList && this.imageList.length) {
    //     uni.setStorageSync("uploadImg", JSON.stringify(this.imageList || []))
    //   }

    //   this.$navigateTo("/customer/pics")
    // },
    changeDate(e){
      this.follow_see_params.take_date = e.detail.value
    },
    changeSelect(e){
      this.selectMember = e
    },
    // 获取陪看人员列表
    getPeikanList(){
      this.$ajax.get('/admin/crm/client_follow/userListDepartment',{},res=>{
        if(res.statusCode ==200){
          this.peikanList =res.data
        }
      })
    },
    showPeikan(){
      this.show_peikan = true
    },
    delPeikan(item){
      this.selectPeikan= this.selectPeikan.filter((i)=>i.values!=item.values)
    
    },
    selectMemberOk(e){
      console.log(e);
      this.selectPeikan = e
      this.show_peikan =false
    },
    submit(){
      if(!this.follow_see_params.content){
        uni.showToast({
          title:'内容不能为空',
          icon:"none"
        })
        return 
      }
      let params = Object.assign({},this.follow_see_params)
      let sel= []
      if(this.selectPeikan && this.selectPeikan.length){
        this.selectPeikan.map(item=>{
          sel.push(item.values)
          return item
        })
      }
      params.accompany =sel.join(",")
      this.$ajax.post("/admin/crm/client_follow/follow_take",params,res=>{
        console.log(res);
        if(res.statusCode==200){
          setTimeout(() => {
            this.$emit("getDataAgain",{})
          }, 200);
          this.$navigateBack()
          
        }
      })
    },
  }
}
</script>

<style lang ="scss" scoped>
  .add_friend {
    padding: 10rpx 20rpx;
    background: #F8F8F8;
    color: #2e3c4e;
    border-radius: 8rpx;

    ~.add_friend {
      margin-left: 20rpx;
    }

    &.to_pic {
      position: relative;

      &:after {
        content: '';
        width: 8rpx;
        height: 8rpx;
        position: absolute;
        top: 6rpx;
        right: 6rpx;
        background: #f63131;
        border-radius: 50%;
      }
    }
  }
.texttarea{
  margin-top: 24rpx;
  padding: 12rpx 32rpx;
    background: #f5f7fa;
    border: 2rpx solid #e8e8e8;
    height: 220rpx;
    width: 100%;
    // margin-left: 42rpx;
}

.follow_call{
  margin-top: 32rpx;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
}
.follow_gj{
  color: rgba(41, 44, 57, 0.70);
font-size: 32rpx;
}

page {
  background: #f6f6f6;
  // color: #2e3c4e;
}
.uni-input{
  color: rgba(41, 44, 57, 0.70);
font-size: 32rpx;
}
.reasonall{
  padding: 32rpx;
}
  .reason {
    padding:0 32rpx;
    background: #fff;
    border-radius: 16rpx;
    textarea{
      border: 2rpx solid #f7f7f7;
    }
    .block{
      margin: 24rpx 0;
      &.row {
        align-items: center;
        justify-content: space-between;
        .follow {
          margin-right: 24rpx;
          margin-bottom:0
        }
        
      }
      .follow {
        margin-bottom: 24rpx;
        white-space: nowrap;
        color:rgba(41, 44, 57, 0.40);
font-size: 32rpx;
        &.fs {
          align-self: flex-start;
          padding: 10rpx 0;
        }
      }
      .follow_c {
        flex-wrap: wrap;
        
        textarea {
          padding: 6px 16px;
          background: #f5f7fa;
          border: 1px solid #e8e8e8;
          height: 153px;
        }
        .peikan_label {
          position: relative;
          padding: 10rpx 40rpx 10rpx 10rpx;
          background: #e8e8e8;
          margin-right: 8rpx;
          margin-bottom: 8rpx;
          border-radius: 8rpx;
          color: rgba(41, 44, 57, 0.70);
text-align: center;
font-size: 32rpx;

          &.empty {
              padding: 10rpx ;
              background: #fff;
               margin-bottom:0;
          }
          .del {
            position: absolute;
            right: 4rpx;
            top: 12rpx;
          }
        }
      }
      .follow_time {
        background: #f0eff4;
        color: #666;
        padding: 4rpx ;
        border-radius: 10rpx;
        .follow_time_item {
          padding: 20rpx 0;
          text-align: center;
          &.active{
            background:#fff;
            border-radius: 10rpx;
          }
        }
      }
    }
  }
  .btns {
    display: flex;
    flex-direction: row;
    margin-top:40rpx;
    .btn{
     flex: 1;
     text-align: center;
     padding: 20rpx;
     border-radius: 10rpx;
     background: #2d84fb;
     color:#fff;
    }
  }
  .p_con{
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    padding: 40rpx 48rpx;
    background: #fff;
  }
</style>