<template>
  <view class="case">
    <counselor-list
      v-for="item in consultant_list"
      :key="item.id"
      :headImg="item.avatar"
      :phone="item.phone"
      :name="item.name || item.user_name || item.nickname"
      :grade="item.grade"
      :number="item.number_people"
      @clickMsg="clickMsg(item)"
      @clickTel="clickTel(item.phone)"
    ></counselor-list>
    <load-more :status="load_status"></load-more>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import counselorList from "@/components/counselor-list";
import loadMore from "@/components/loadMore";
import { mapState, mapActions } from "vuex";
export default {
  components: { counselorList, loadMore },
  data() {
    return {
      consultant_list: [],
      project_id: "",
      load_status: "",
      customer_id: "",
    };
  },
  computed: {
    ...mapState(["im", "is_setting"]),
  },
  onLoad(options) {
    this.customer_id = options.customer_id;
    this.project_id = options.project_id;
    this.getDataList();
  },
  methods: {
    ...mapActions(["getImToken"]),
    getDataList() {
      this.load_status = "loading";
      this.getImToken();
      this.$ajax.get(
        `/common/project/all/manager/${this.project_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.consultant_list = res.data;
            if (res.data.length === 0) {
              this.load_status = "nomore";
              uni.showToast({
                title: "没有更多数据了",
                icon: "none",
              });
            }
          } else {
            uni.showToast({
              title: res.data.message || "获取置业顾问列表失败",
              icon: "none",
            });
          }
        }
      );
    },
    // 置业顾问电话
    clickTel(e) {
      if (e) {
        uni.makePhoneCall({
          phoneNumber: e,
          success: () => {
            console.log("拨打置业顾问电话电话");
          }, //仅为示例
        });
      }
    },
    clickMsg(item) {
      if (this.im.imToken) {
        this.$navigateTo(
          `/im_list/msg_detail?to_id=${item.user_id}&customer_id=${this.customer_id}`
        );
      }
    },
  },
  onPullDownRefresh: function() {
    this.getDataList();
    uni.stopPullDownRefresh();
  },
};
</script>

<style scoped lang="scss">
.case {
  padding: 24rpx 48rpx;
}
</style>
