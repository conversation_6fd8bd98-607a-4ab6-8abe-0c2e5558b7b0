<template>
    <view class="t-voice-player-container" :class="{'is-ai-call': isAiCall, 'has-health': healthdata}">
        <view class="t-voice-player" @click="onClick" :style="{width: healthdata ? 'calc(100% - 80px)' : videoWidth}">
            <image :src="currentPlayIcon" class="play-icon"></image>
            <view class="duration">{{ durationFormat }}</view>
            <view class="play-process" :class="{'paly-done': playDone}" :style="{width: playProcessPct}"></view>
        </view>
        <view v-if="healthdata" class="health-quality" @click="showHealthDesc">
            <view :class="[
                healthdata.value === '健康' ? 'green' : '',
                healthdata.value === '违规' ? 'red' : ''
            ]"></view>
            <text class="health-quality-text">
                {{healthdata.value}}
            </text>
            <view class="health-icon" :class="[
                healthdata.value === '健康' ? 'greyicon' : '',
                healthdata.value === '违规' ? 'redicon' : ''
            ]">!</view>
        </view>
        <view v-else-if="isAiCall" class="ai-info" @click.stop="openAiAnalysis">
            <image class="ai-icon" src="https://img.tfcs.cn/backup/static/admin/customer/AIhuise.png"></image>
        </view>

        <!-- 健康度描述弹出层 -->
        <view v-if="showHealthTip && healthdata" class="health-desc-popup" @click.stop="showHealthTip = false">
            <view class="health-desc-content" :class="healthdata.value === '健康' ? 'health-green' : 'health-red'">
                {{ healthdata.desc }}
            </view>
        </view>
    </view>
</template>
<script>
export default {
    name: 'tVoicePlayer',
    props: {
        path: {type: String, default: ''},
        duration: { type: Number, default: 0},
        name: {type: [String, Number], default: ''},
        isAiCall: { type: Boolean, default: false },
        healthdata: { type: Object, default: null },
    },
    data(){
        return {
            playing: false,
            isPause: false,
            innerAudioContext: null,
            playCurrentTime: 0,
            playDone: false,
            playingKey: '',
            hasPlayed: false, // 标记是否已经播放过
            playedToEnd: false, // 标记是否播放到了结束
            showHealthTip: false // 是否显示健康度描述弹窗
        }
    },
    created(){

    },
    computed: {
        currentPlayIcon(){
            return this.playing ? 'https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/icon/voice/play_voice.gif' : 'https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/icon/voice/voice_icon.png';
        },
        playProcessPct(){
            // 如果已经播放到结束，则显示100%
            if (this.playedToEnd) {
                return '100%';
            }

            const duration = this.innerAudioContext?.duration || this.duration;
            let pct = duration ? this.playCurrentTime / duration : 0;
            if(pct > 1){
                pct = 1;
            }
            return Math.floor(pct*100) + '%'
        },
        durationFormat(){
            return this.formatDuration(this.duration)
        },
        videoWidth(){
            let pct = (this.duration / 60)*100;
            if(pct < 25){
                pct = 25
            }else if(pct > 100){
                pct = 100
            }
            return pct+'%'
        }
    },
    beforeDestroy(){
        this.innerAudioContext && this.innerAudioContext.destroy();
    },
    methods: {
        formatDuration(duration){
            if(duration < 60){
                return duration + '\'\'';
            }else if(duration < 3600){
                let m = Math.floor(duration/60),
                    s = duration - m*60;
                return m+'\''+(s?s+'\'\'':'');
            }else{
                let h = Math.floor(duration/3600),
                    m = Math.floor((duration-h*3600)/60),
                    s = duration - h*3600 - m*60;
                return h+':'+(m || s?m+'\'':'')+(s?s+'\'\'':'');
            }
        },

        onClick(){
            this.playing = !this.playing;
            console.log(this.playing);

            // 如果开始播放
            if(this.playing){
                // 如果之前已经播放到结束，则重置状态
                if (this.playedToEnd) {
                    this.playedToEnd = false;
                    this.playCurrentTime = 0;
                }
                this.play();
            } else {
                this.pause();
            }
        },
        initAudioEvent(){
            this.innerAudioContext.onPlay((e) => {
                console.log(e);
            })
            this.innerAudioContext.onEnded(() => {
                this.playing = false;
                this.playedToEnd = true; // 标记已播放到结束
                this.hasPlayed = true;
                this.$emit('stop');
            })
            this.innerAudioContext.onError(() => {
                uni.showToast({
                    title: '播放失败，请重试',
                    icon: 'none'
                })
            })
            this.innerAudioContext.onTimeUpdate(()=>{
                this.playCurrentTime = this.innerAudioContext.currentTime;

            })
        },
        play(){
            if(!this.innerAudioContext){
                this.innerAudioContext = uni.createInnerAudioContext();
                this.initAudioEvent();
                this.innerAudioContext.src = this.path;

            }
            uni.$emit('tVideoPlayer', this.name);

            this.innerAudioContext.play();

            uni.$once('tVideoPlayer', (name)=>{
                if(name != this.name){
                    this.stop();
                }
            })
        },
        pause(){
            this.innerAudioContext && this.innerAudioContext.pause();
            this.isPause = true;
        },
        stop(){
            if(this.innerAudioContext){
                this.innerAudioContext.stop();
                this.innerAudioContext.startTime = 0
            }
            this.playing = false;

            // 只有在非播放到结束的情况下才重置进度
            if (!this.playedToEnd) {
                this.playDone = true;
                this.playCurrentTime = 0;

                setTimeout(()=> {
                    this.playDone = false;
                },100)
            }

            this.$emit('stop');
        },

        // 打开AI分析弹窗
        openAiAnalysis() {
            console.warn("-------openAiAnalysis--------");
            this.$emit('openAiAnalysis');
        },

        // 显示健康度描述
        showHealthDesc(e) {
            e.stopPropagation(); // 阻止事件冒泡
            this.showHealthTip = !this.showHealthTip;
        }
    }
}
</script>
<style lang="scss" scoped>
.t-voice-player-container {
    display: flex;
    flex-direction: row !important; /* 强制水平排列 */
    align-items: center;
    position: relative;
    width: 100%;
    flex-wrap: nowrap; /* 禁止换行 */
}

.t-voice-player{
    height: 32px;
    background: #e0e0e0; /* 未播放时的浅灰色背景 */
    border-radius: 16px; /* 两边半圆形样式 */
    display: flex;
    flex-direction: row;
    align-items: center;
    line-height: 1;
    padding: 0 5px;
    position: relative;
    overflow: hidden;
    flex: 1; /* 让播放器占据容器的大部分空间 */

    .play-icon{
        width: 22px;
        height: 22px;
        z-index: 2;
    }

    .duration{
        padding-left: 6px;
        font-size: 13px;
        font-weight: 400;
        color: #fff; /* 改回白色文本 */
        z-index: 2;
    }

    .play-process{
        position: absolute;
        z-index: 1;
        left: 0;
        top: 0;
        height: 100%;
        width: 0;
        background: linear-gradient(90deg, #4776E6, #8E54E9, #4776E6); /* 渐变色背景 */
        background-size: 200% 100%;
        transition: width .2s;
        animation: gradientAnimation 2s linear infinite;
        border-radius: 16px; /* 确保进度条也是圆角 */

        &.paly-done{
            transition: none;
        }
    }
}

.ai-info {
    display: flex;
    align-items: center;
    margin-left: 8px;

    .ai-icon {
        width: 20px;
        height: 20px;
    }
}

.health-quality {
    display: flex;
    flex-direction: row;
    align-items: center; /* 确保垂直居中 */
    margin-left: 8px;
    background-color: #f9f9f9;
    border-radius: 16px;
    padding: 0 10px;
    height: 32px;
    min-width: 60px;
    justify-content: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    white-space: nowrap;

    .green {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #52c41a;
        flex-shrink: 0;
        display: flex; /* 确保垂直居中 */
        align-items: center; /* 确保垂直居中 */
    }

    .red {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: #f5222d;
        flex-shrink: 0;
        display: flex; /* 确保垂直居中 */
        align-items: center; /* 确保垂直居中 */
    }

    .health-quality-text {
        margin: 0 4px;
        font-size: 12px;
        color: #333;
        font-weight: 500;
        flex-shrink: 0;
        line-height: 1; /* 确保文字垂直居中 */
        display: flex; /* 确保垂直居中 */
        align-items: center; /* 确保垂直居中 */
    }

    .health-icon {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        display: flex;
        align-items: center; /* 确保垂直居中 */
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        flex-shrink: 0;
        line-height: 1; /* 确保文字垂直居中 */

        &.greyicon {
            color: #999;
            border: 1px solid #d9d9d9;
        }

        &.redicon {
            color: #f5222d;
            border: 1px solid #f5222d;
        }
    }
}

.t-voice-player-container.is-ai-call .ai-info {
    margin-left: 8px;
    flex-shrink: 0; /* 防止AI图标被压缩 */
}

.t-voice-player-container.has-health {
    justify-content: space-between;
    flex-direction: row !important;
    display: flex !important;
    flex-wrap: nowrap !important;

    .t-voice-player {
        flex: 0 1 auto; /* 允许播放器收缩但不扩展 */
    }

    .health-quality {
        flex: 0 0 auto; /* 不允许健康度收缩或扩展 */
        display: inline-flex !important;
    }
}

/* 健康度描述弹出层 */
.health-desc-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;

    .health-desc-content {
        max-width: 80%;
        padding: 16px;
        border-radius: 8px;
        background-color: #fff;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
        font-size: 14px;
        line-height: 1.5;
        text-align: left;
        word-break: break-word;

        &.health-green {
            border-left: 4px solid #52c41a;
        }

        &.health-red {
            border-left: 4px solid #f5222d;
        }
    }
}

@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}
</style>