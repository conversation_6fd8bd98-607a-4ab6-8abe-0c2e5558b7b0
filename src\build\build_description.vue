<template>
  <view class="">
    <view class="_list">
      <view class="title _line">楼盘详情</view>
      <view class="_line row">
        <view class="label">楼盘名称：</view>
        <view class="content _ctn" style="color:#ff6c0d">{{
          build_detail.build_name
        }}</view>
      </view>
      <view class="_line row">
        <view class="label">楼盘地址：</view>
        <view class="content ">{{ build_detail.full_build_address }}</view>
      </view>
      <view class="_line row" v-if="build_detail.total_build_area">
        <view class="label">楼盘面积：</view>
        <view class="content row"
          >{{ build_detail.total_build_area }}<text>㎡</text></view
        >
      </view>
      <view class="_line row" v-if="build_attr.land_occupancy_area">
        <view class="label">占地面积：</view>
        <view class="content row"
          >{{ build_attr.land_occupancy_area }}<text>㎡</text></view
        >
      </view>
      <view class="_line row" v-if="build_attr.property_right_years">
        <view class="label">房屋产权：</view>
        <view class="content row"
          >{{ build_attr.property_right_years }}<text>年</text></view
        >
      </view>
      <view class="_line row" v-if="build_attr.plot_ratio">
        <view class="label">容积率：</view>
        <view class="content ">{{ build_attr.plot_ratio }}</view>
      </view>
      <view class="_line row" v-if="build_attr.greening_rate">
        <view class="label">绿化率：</view>
        <view class="content ">{{ build_attr.greening_rate }}</view>
      </view>
      <view class="_line row" v-if="build_attr.total_parking_space">
        <view class="label">停车位：</view>
        <view class="content ">{{ build_attr.total_parking_space }}</view>
      </view>
      <view class="_line row" v-if="build_detail.build_avg_price">
        <view class="label">楼盘均价：</view>
        <view class="content row"
          >{{ build_detail.build_avg_price }} <text>元/㎡</text>
        </view>
      </view>
      <view class="_line row" v-if="build_detail.b_price_description">
        <view class="label">价格补充：</view>
        <view class="content ">{{ build_detail.b_price_description }}</view>
      </view>
      <view class="_line row">
        <view class="label">销售许可证：</view>
        <view class="content ">{{ build_attr.sales_license }}</view>
      </view>
      <view class="_line row">
        <view class="label">物业公司：</view>
        <view class="content ">{{ build_attr.real_estate_company }}</view>
      </view>
      <view class="_line row" v-if="build_attr.real_estate_price">
        <view class="label">物业费：</view>
        <view class="content ">{{ build_attr.real_estate_price }}元/㎡/月</view>
      </view>
      <view class="_line row" v-if="build_attr.elevator">
        <view class="label">电梯：</view>
        <view class="content ">{{ build_attr.elevator.replace(",", "") }}</view>
      </view>
      <view class="_line row">
        <view class="label">楼间距：</view>
        <view class="content ">{{ build_attr.distance }}</view>
      </view>
    </view>
    <view class="_list" v-if="build_detail.project_id && display_brokerage">
      <view class="title _line">分佣规则</view>
      <view class="_line row">
        <view class="label">佣金分成：</view>
        <view class="content ">{{ build_detail.brokerage_rule }}</view>
      </view>
      <view class="_line row">
        <view class="label">奖励说明：</view>
        <view class="content ">{{ build_detail.brokerage_description }}</view>
      </view>
    </view>
    <view class="_list">
      <view class="title _line">房地产信息</view>
      <view class="_line row" v-if="build_detail.developers_company_name">
        <view class="label">开 发 商：</view>
        <view class="content ">{{ build_detail.developers_company_name }}</view>
      </view>
      <view class="_line row" v-if="build_detail.completion_house_time">
        <view class="label">竣工日期：</view>
        <view class="content ">{{ build_detail.completion_house_time }}</view>
      </view>
      <view class="_line row" v-if="build_attr.delivery_date">
        <view class="label">交房日期：</view>
        <view class="content ">{{ build_attr.delivery_date }}</view>
      </view>
    </view>
    <view class="_list">
      <view class="title _line">售楼处信息</view>
      <view class="_line row" v-if="build_detail.sales_office_address">
        <view class="label">售楼地址：</view>
        <view class="content ">{{ build_detail.sales_office_address }}</view>
      </view>
      <view class="_line row" v-if="build_detail.sales_office_phone">
        <view class="label">售楼电话：</view>
        <view class="content ">{{ build_detail.sales_office_phone }}</view>
      </view>
    </view>
    <view class="_list">
      <view class="title _line">项目介绍</view>
      <view class="pro_info" v-html="build_detail.feature"> </view>
    </view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import { mapActions, mapState } from "vuex";
export default {
  data() {
    return {
      params: {},
      build_detail: {},
      build_attr: {},
      display_brokerage: false,
    };
  },
  components: {},
  onLoad(options) {
    this.params.build_id = options.build_id;
    this.getData();
  },
  methods: {
    ...mapActions(["getSetting"]),
    getData() {
      this.$ajax.get(
        `/common/project/query/build/${this.params.build_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.build_detail = res.data;
            this.getattrList(this.params.build_id);
            this.getSetting((e) => {
              let display = e.login_display_brokerage_rule;
              let token = uni.getStorageSync(
                "token" + this.$store.state.website_id
              );
              if (display == 1 || token) {
                this.display_brokerage = true;
              } else if (display == 0) {
                this.display_brokerage = false;
              }
            });
          } else {
            uni.showToast({
              title: res.data.message || "获取失败",
              icon: "none",
            });
          }
        }
      );
    },

    getattrList(id) {
      this.$ajax.get(`/common/project/query/build/attr/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.build_attr = res.data;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #eee;
}
._list {
  margin-bottom: 20rpx;
  background: #fff;
  font-size: 28rpx;
  ._line {
    margin: 0 30rpx;
    padding: 30rpx 0;
    color: #999;
    border-bottom: 2rpx solid #eee;
    line-height: 40rpx;
    .label {
      width: 170rpx;
    }
    &:last-child {
      border: none;
    }
    .content {
      flex: 1;
      color: #333;
    }
  }
  .border_none {
    border: none;
  }
  .pro_info {
    padding: 48rpx 48rpx 80rpx;
  }
}
</style>
<style>
/*防止图片宽度超出*/
.pro_info >>> img {
  width: 100%;
}
</style>
