<template>
  <view class="add-member">
    <text class="title">添加成员</text>
    <view class="add-member-item" v-for="(item, index) in addUserInfo" :key="index">
      <text class="item-l">{{ item.text }}</text>
      <view class="item-r">
        <input type="text" v-model="item.value" :placeholder="item.placeholder" />
      </view>
    </view>
    <view class="add-member-item">
      <text class="item-l">所属部门</text>
      <text class="item-r" style="color: #808080;font-size: 28rpx;line-height: 1.2;" @click="openDepartment">{{
        param.all_department_id ? deptSelectedLable : '请选择部门' }}</text>
      <myIcon style="padding-left: 20rpx;" type="xiala" color="#808080" size="24rpx" @click="openDepartment">
      </myIcon>
    </view>
    <view class="add-member-item">
      <text class="item-l">状态</text>
      <view class="status" @click="statusChange">
        <text>{{ param.status ? '已启用' : '已禁用' }}</text>
        <switch style="transform:scale(0.7)" :checked="param.status == 1" />
      </view>
    </view>
    <view class="detail-btm">
      <button type="default" @click="reset">重置</button>
      <button type="primary" @click="addMember">添加</button>
    </view>
    <tDepartmentPicker multiple :visible.sync="dialogs.deptPicker" v-model="department_id" @confirm="confirmSeledDept">
    </tDepartmentPicker>
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
import tDepartmentPicker from '@/components/tplus/tDepartmentPicker.vue';
export default {
  components: {
    myIcon,
    tDepartmentPicker
  },
  data() {
    return {
      department_id: [],
      status: 1,
      deptSelectedLable: '',
      dialogs: {
        deptPicker: false
      },
      param: {
        all_department_id: '',
        wx_work_department_id: '',
        password: '',
        password_confirmation: '',
        phone: '',
        status: 1,
        syn_wx: 0,
        user_name: '',
        post: ''
      },
      addUserInfo: [
        { text: '姓名', placeholder: '请输入姓名', value: '' },
        { text: '手机号', placeholder: '请输入手机号', value: '' },
        { text: '职位', placeholder: '请输入职位', value: '' },
        { text: '设置密码', placeholder: '请设置密码', value: '' },
        { text: '确认密码', placeholder: '请再次输入密码', value: '' }
      ]
    }
  },
  methods: {
    addMember() {
      const item = this.addUserInfo.find((item) => item.value == '')
      if (item) {
        uni.showToast({
          title: item.placeholder,
          icon: 'none'
        })
      } else {
        this.addUserInfo.forEach((item) => {
          if (item.text == '姓名') return this.param.user_name = item.value
          if (item.text == '手机号') return this.param.phone = item.value
          if (item.text == '职位') return this.param.post = item.value
          if (item.text == '设置密码') return this.param.password = item.value
          if (item.text == '确认密码') return this.param.password_confirmation = item.value
        })
        if (!this.param.all_department_id) {
          uni.showToast({
            title: '请选择部门',
            icon: 'none'
          })
          return
        }
        this.$ajax.post('/admin/personnelMatters/createMember', this.param, (res) => {
          if (res.statusCode != 200) {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
              duration: 2000
            })
          } else {
            uni.$emit('refreshData');
            uni.showToast({
              title: '添加成功',
              icon: 'success',
              duration: 2000
            })
            uni.navigateBack()
          }
        })
      }
    },
    statusChange() {
      this.param.status == 1 ? this.param.status = 0 : this.param.status = 1
    },
    openDepartment() {
      this.dialogs.deptPicker = true
    },
    confirmSeledDept(data) {

      if (data.length) {
        let id = [], name = []
        data.forEach(item => {
          id = [...id, ...item.value]
          name = [...name, ...item.label.slice(-1)]
        })
        this.param.all_department_id = id.join(',')
        this.param.wx_work_department_id = this.department_id.join(',')
        this.deptSelectedLable = name.join(' ')
      } else {

      }
      this.dialogs.deptPicker = false
    },
    reset() {
      this.param = {
        all_department_id: '',
        wx_work_department_id: '',
        password: '',
        password_confirmation: '',
        phone: '',
        status: 1,
        syn_wx: 0,
        user_name: '',
        post: ''
      }
      this.editUserInfo = [
        { text: '姓名', placeholder: '请输入姓名', value: '' },
        { text: '手机号', placeholder: '请输入手机号', value: '' },
        { text: '职位', placeholder: '请输入职位', value: '' },
        { text: '设置密码', placeholder: '请设置密码', value: '' },
        { text: '确认密码', placeholder: '请再次输入密码', value: '' }
      ]
      this.department_id = []
      this.deptSelectedLable = ''
    }
  },
}
</script>
<style lang="scss" scoped>
.add-member {
  padding: 32rpx;

  .title {
    font-size: 36rpx;
    font-weight: 500;
    color: #292C39;
    margin-bottom: 40rpx;
  }

  .title::after {
    content: '（请完善资料）';
    font-size: 28rpx;
    color: #a1a1a1;
  }

  .add-member-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 0;

    .item-l {
      min-width: 25%;
      font-size: 32rpx;
      color: #292C3966;
    }

    .item-r {
      width: 100%;
      font-size: 32rpx;
      color: #292C39;
      text-align: right;
    }

    .status {
      display: flex;
      flex-direction: row;
      align-items: center;

      text {
        color: #808080;
        font-size: 32rpx;
        margin-right: 10rpx;
      }
    }
  }

  .detail-btm {
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
    padding: 30rpx 20rpx 40rpx;
    border-top: 1px solid #f6f6f6;
    background-color: #fff;

    button {
      width: 50%;
    }

    &>button:nth-child(1) {
      margin-right: 30rpx;
    }
  }
}
</style>