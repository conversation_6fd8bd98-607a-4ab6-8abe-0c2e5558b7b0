<template>
  <view class="add-member">
    <!-- <text class="title">编辑成员</text> -->
    <view class="add-member-item" v-for="(item, index) in editUserInfo" :key="index">
      <text class="item-l">{{ item.text }}</text>
      <view class="item-r">
        <input type="text" v-model="item.value" :placeholder="item.placeholder" />
      </view>
    </view>
    <view class="add-member-item">
      <text class="item-l">部门</text>
      <text class="item-r" style="color: #808080;font-size: 28rpx;line-height: 1.2;" @click="openDepartment">{{
      department_id.length ? deptSelectedLable : '请选择部门' }}</text>
      <myIcon style="padding-left: 20rpx;" type="xiala" color="#808080" size="24rpx" @click="openDepartment">
      </myIcon>
    </view>
    <view class="add-member-item" v-if='isAdmin'>
      <text class="item-l">状态</text>
      <view class="status" @click="statusChange">
        <text>{{ status ? '已启用' : '已禁用' }}</text>
        <switch style="transform:scale(0.7)" color="#488AF6" :checked="status == 1" />
      </view>
    </view>
    <view class="add-member-item" style="align-items: flex-start;" v-if='isAdmin'>
      <text class="item-l" style="margin-top: 10rpx;">角色管理</text>
      <view class="item-r"
        style="display: flex;flex-direction: row;align-items: center;justify-content: flex-end; flex-wrap: wrap; width: auto;"
        @click="openRolePopup">
        <text class="role-item" v-for="(item, index) in roleSelectedLable" :key="index">{{ item.name }}</text>
        <myIcon style="padding-left: 20rpx;" type="xiala" color="#808080" size="24rpx" @click="openDepartment">
        </myIcon>
      </view>
    </view>
    <view class="detail-btm">
      <button type="default" @click="reset">重置</button>
      <button type="primary" @click="updateMember">编辑</button>
    </view>
    <tDepartmentPicker multiple :visible.sync="dialogs.deptPicker" :type="1" v-model="department_id"
      @confirm="confirmSeledDept">
    </tDepartmentPicker>
    <tDepartmentPicker multiple :visible.sync="dialogs.isRolePopup" :type="2" v-model="role_id" @confirm="roleChange">
    </tDepartmentPicker>
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
import tDepartmentPicker from '@/components/tplus/tDepartmentPicker.vue';
export default {
  components: {
    myIcon,
    tDepartmentPicker
  },
  data() {
    return {
      userData: {},
      phone: "",
      department_id: [],
      role_id: [],
      status: 1,
      deptSelectedLable: '',
      roleSelectedLable: [],
      dialogs: {
        deptPicker: false,
        isRolePopup: false
      },
      param: {
        id: '',
        all_department_id: '',
        wx_work_department_id: '',
        password: '',
        password_confirmation: '',
        phone: '',
        status: 1,
        syn_wx: 0,
        user_name: '',
        post: ''
      },
      editUserInfo: [
        { text: '姓名', placeholder: '请输入姓名', value: '' },
        { text: '手机号', placeholder: '请输入手机号', value: '' },
        { text: '职位', placeholder: '请输入职位', value: '' },
        { text: '设置密码', placeholder: '请设置密码', value: '' },
        { text: '确认密码', placeholder: '请再次输入密码', value: '' }
      ]
    }
  },
  computed: {
    isAdmin() {
      let userInfo = JSON.parse(uni.getStorageSync('userInfo'))
      return userInfo.roles.some(item => item.name == '站长')
    },
    isMe() {
      let userInfo = JSON.parse(uni.getStorageSync('userInfo'))
      return userInfo.id == this.userData.id
    }
  },
  onLoad(options) {
    if (options.phone) {
      this.param.phone = options.phone
    }
    this.getUserDetail()
  },
  methods: {
    getUserRole() {
      this.roleSelectedLable = []
      this.$ajax.get(`/admin/admin_user/role/all/${this.userData.id}`, {}, (res) => {
        if (res.statusCode == 200) {
          this.roleSelectedLable = res.data.roles
          if (res.data.roles.length) {
            res.data.roles.forEach(item => {
              this.role_id.push(item.id)
            })
          }
        }
      })
    },
    getUserDetail() {
      this.$ajax.get('/admin/personnelMatters/memberList', this.param, (res) => {
        if (res.statusCode == 200) {
          this.userData = res.data.data[0]
          this.editUserInfo.forEach(item => {
            if (item.text == '姓名') return item.value = res.data.data[0].user_name
            if (item.text == '手机号') return item.value = res.data.data[0].phone
            if (item.text == '职位') return item.value = res.data.data[0].post
          })
          let name = []
          res.data.data[0].department.forEach(item => {
            this.department_id.push(item.id)
            name.push(item.name)
          })
          this.param.id = res.data.data[0].id
          this.deptSelectedLable = name.join(' | ')
          this.status = res.data.data[0].status
          this.getUserRole()
        }
      })
    },
    updateMember() {
      const item = this.editUserInfo.find((item) => item.value == '')
      if (item) {
        uni.showToast({
          title: item.placeholder,
          icon: 'none'
        })
      } else {
        this.editUserInfo.forEach((item) => {
          if (item.text == '姓名') return this.param.user_name = item.value
          if (item.text == '手机号') return this.param.phone = item.value
          if (item.text == '职位') return this.param.post = item.value
          if (item.text == '设置密码') return this.param.password = item.value
          if (item.text == '确认密码') return this.param.password_confirmation = item.value
        })
        // 检测用户是否重选部门
        let arr = []
        this.userData.department.forEach(item => {
          arr.push(item.id)
        })
        if (this.arrayEqual(arr, this.department_id)) {
          this.param.all_department_id = this.userData.all_department_id
          this.param.wx_work_department_id = this.department_id.join(',')
        }
        if (!this.param.all_department_id) {
          uni.showToast({
            title: '请选择部门',
            icon: 'none'
          })
          return
        }
        this.$ajax.post('/admin/personnelMatters/updateMember', this.param, (res) => {
          if (res.statusCode != 200) {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
              duration: 2000
            })
          } else {
            uni.$emit('refreshData');
            uni.showToast({
              title: '编辑成功',
              icon: 'success',
              duration: 2000
            })
            uni.navigateBack()
          }
        })
      }
    },
    arrayEqual(a, b) {
      a = a.sort();
      b = b.sort();
      if (a.length != b.length)
        return false;
      for (var i = 0; i < a.length; ++i) {
        if (a[i] !== b[i])
          return false;
      }
      return true;
    },
    statusChange() {
      this.status == 1 ? this.status = 0 : this.status = 1
    },
    openDepartment() {
      this.dialogs.isRolePopup = false
      this.dialogs.deptPicker = true
    },
    openRolePopup() {
      this.dialogs.deptPicker = false
      this.dialogs.isRolePopup = true
    },
    confirmSeledDept(data) {
      if (data.length) {
        let id = [], name = []
        data.forEach(item => {
          id = [...id, ...item.value]
          name = [...name, ...item.label.slice(-1)]
        })
        this.param.all_department_id = id.join(',')
        this.param.wx_work_department_id = this.department_id.join(',')
        this.deptSelectedLable = name.join(' ')
      }
      this.dialogs.deptPicker = false
    },
    roleChange(data) {
      if (data.length) {
        let param = {
          id: this.userData.id + '',
          role_names: []
        }
        this.roleSelectedLable = []
        data.forEach(item => {
          let obj = {
            id: item.value[0],
            name: item.label[0],
          }
          param.role_names.push(item.label[0])
          this.roleSelectedLable.push(obj)
        })
        this.$ajax.post('/admin/admin_user/role/reset/all', param, (res) => {
          if (res.statusCode == 200) {
            this.getUserRole()
          }
        })
      }
    },
    reset() {
      this.param = {
        all_department_id: '',
        wx_work_department_id: '',
        password: '',
        password_confirmation: '',
        phone: '',
        status: 1,
        syn_wx: 0,
        user_name: '',
        post: ''
      }
      this.editUserInfo = [
        { text: '姓名', placeholder: '请输入姓名', value: '' },
        { text: '手机号', placeholder: '请输入手机号', value: '' },
        { text: '职位', placeholder: '请输入职位', value: '' },
        { text: '设置密码', placeholder: '请设置密码', value: '' },
        { text: '确认密码', placeholder: '请再次输入密码', value: '' }
      ]
      this.status = 1
      this.department_id = []
      this.deptSelectedLable = ''
    }
  },
}
</script>
<style lang="scss" scoped>
.add-member {
  padding: 32rpx;

  .title {
    font-size: 36rpx;
    font-weight: 500;
    color: #292C39;
    margin-bottom: 40rpx;
  }

  // .title::after {
  //   content: '（请完善资料）';
  //   font-size: 28rpx;
  //   color: #a1a1a1;
  // }

  .add-member-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 0;

    .item-l {
      min-width: 25%;
      font-size: 32rpx;
      color: #292C3966;
    }

    .item-r {
      width: 100%;
      font-size: 32rpx;
      color: #292C39;
      text-align: right;

      .role-item {
        border-radius: 4px;
        padding: 8rpx 16rpx;
        font-size: 24rpx;
        background: #488AF633;
        color: #488AF6;
        margin: 10rpx 0;
        margin-left: 20rpx;
        white-space: nowrap;
      }
    }

    .status {
      display: flex;
      flex-direction: row;
      align-items: center;

      text {
        color: #808080;
        font-size: 32rpx;
        margin-right: 10rpx;
      }
    }
  }

  .detail-btm {
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
    padding: 30rpx 20rpx 40rpx;
    border-top: 1px solid #f6f6f6;
    background-color: #fff;

    button {
      width: 50%;
    }

    &>button:nth-child(1) {
      margin-right: 30rpx;
    }

    &>button:nth-child(2) {
      background-color: #488AF6;
    }
  }
}
</style>