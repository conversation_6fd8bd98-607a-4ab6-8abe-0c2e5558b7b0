// 获取地址栏参数  输入string
const getQueryString = function (name) {
  var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)')
  var r = window.location.search.substr(1).match(reg)
  if (r != null) return unescape(r[2])
  return null
}

const config = {
  projectName: '腾房云报备',
  // h5Api: "https://fenxiao.zaodaoxiao.com/api", // h5结构基础路径
  h5Api: '/api',
  appApi: 'https://yun.tfcs.cn/api', // 小程序或app的api接口域名
  imSocket: 'wss://imcloud.tengfangyun.com:9002',
  // imSocket: "ws://localhost:9090" // 用于测试的聊天本地服务器,
  imgDomain: 'https://img.tfcs.cn/backup', // 图片域名
  // #ifdef H5
  // aliPayUrl: '/wap/member/aliPayByWap', // 支付宝支付
  // wxPayUrl: '/wap/member/wxPayByWap', // 微信支付
  // // #endif
  uploadApi: '/common/file/upload/client', // 上传文件api
  // imSocket: 'wss://imcloud.tengfangyun.com:9002',
  thumbParam: '?x-oss-process=style/', // oss图片缩放参数
  // ossSnapshot: '?x-oss-process=video/snapshot,t_1000,f_jpg,m_fast', // 视频截帧参数
  // modalBtnColor: '#ff6735' // 弹窗确定按钮颜色\
  website_id: getQueryString('website_id'),
  appid_tplus_mini: 'wx52247a78fb9ca990', // T+领资料 置业顾问小程序appid
}
if (process.env.NODE_ENV === 'development') {
  console.log('开发环境')
  // #ifdef H5
  // config.h5Api = "https://fenxiao.zaodaoxiao.com/api/";
  // #endif
  // #ifdef H5
  // config.h5Api = "http://localhost:81/fenxiao/";
  // #endif
}
module.exports = config
