<template>
  <view>
    <view class="sort row">
      <view class="info">
        <view class="num row">
          <text>342</text>
        </view>
        <text>公海客户</text>
      </view>
      <view class="info">
        <view class="num row">
          <text>342</text>
        </view>
        <text>有效客户</text>
      </view>
      <view class="info">
        <view class="num row">
          <text>342</text>
        </view>
        <text>我的客户</text>
      </view>
      <view class="info">
        <view class="num row">
          <text>342</text>
        </view>
        <text>团队人员</text>
      </view>
    </view>
    <view class="title c2">工作日历</view>
    <view class="calendar_box">
      <!-- 仿钉钉打卡日历组件 -->
      <view class="calendar_container">
        <zsyCalendar @change="change" />
      </view>
      <view>
        <view class="remind row">
          <text>跟进提醒（1/3）</text>
          <view @click="toRemind">全部提醒</view>
        </view>
        <view class="remind_info">
          <view>您的客户【大同天下】需要跟进,请及时处理。</view>
        </view>
      </view>
      <view>
        <view class="remind row">
          <text>待处理事项</text>
        </view>
        <view class="pending">
          <view class="row color1">
            <view class="row tit"><text></text>即将掉公海客户</view>
            <text>10</text>
          </view>
          <view class="row color2">
            <view class="row tit"><text></text>7日未跟进客户</view>
            <text>10</text>
          </view>
        </view>
      </view>
    </view>
    <view>
      <tabBar
        class="tab"
        :fixedTop="false"
        :nowIndex="tab_type"
        :tabs="tab"
        @click="onClickTab"
      >
      </tabBar>
    </view>
    <view class="state_box">
      <view
        class="charts-box"
        v-if="this.tab_type == 0"
        :style="{ width: cWidth1 + 'px', height: cHeight1 + 'px' }"
      >
        <qiun-data-charts type="funnel" :chartData="chartData" />
      </view>
      <view
        class="charts-box"
        v-if="this.tab_type == 1"
        :style="{ width: cWidth1 + 'px', height: cHeight1 + 'px' }"
      >
        <qiun-data-charts type="ring" :chartData="chartData2" />
      </view>
      <view
        class="charts-box"
        v-if="this.tab_type == 2"
        :style="{ width: cWidth1 + 'px', height: cHeight1 + 'px' }"
      >
        <qiun-data-charts type="ring" :chartData="chartData3" />
      </view>
      <view class="client_list" v-if="this.tab_type == 0">
        <view>
          <view class="client row c2">
            <text></text>
            <text>新增客户</text>
            <text>7日</text>
            <text>本月</text>
            <text>累计</text>
          </view>
          <view class="client client_color row">
            <text>公海客户</text>
            <text>0</text>
            <text>0</text>
            <text>0</text>
            <text>0</text>
          </view>
        </view>
      </view>
    </view>
    <view class="title c2">荣誉榜</view>
    <view class="honor_list">
      <view class="h_sort row">
        <text
          class="c2"
          :class="{ active: honor_type == index }"
          @click="clickHonor(index)"
          v-for="(item, index) in honor_list"
          :key="index"
          >{{ item.name }}</text
        >
      </view>
      <view class="h_date row">
        <text
          class="c2"
          :class="{ active: date_type == index }"
          @click="clickDate(index)"
          v-for="(item, index) in honor_date"
          :key="index"
          >{{ item.name }}</text
        >
      </view>
      <view class="h_list">
        <view class="info row">
          <view class="left row">
            <text class="on1">1</text>
            <image
              src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png"
              mode="aspectFill"
            />
            <view>姓名</view>
          </view>
          <view class="right">跟进：105</view>
        </view>
      </view>
    </view>
  </view>
</template>
<style lang="scss" scoped>
page {
  background: #f6f6f6;
  padding: 12px;
  color: #2e3c4e;
}
.c2 {
  color: #8a929f;
}
.honor_list {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  .h_list {
    .info {
      align-items: center;
      margin-bottom: 24rpx;
      .left {
        flex: 1;
        align-items: center;
        text {
          color: #2e3c4e;
          background: #f1f4fa;
          border-radius: 4rpx;
          width: 32rpx;
          height: 32rpx;
          line-height: 32rpx;
          text-align: center;
          font-size: 24rpx;
          &.on1 {
            background: #f95742;
            color: #fff;
          }
          &.on2 {
            background: #ff9a06;
            color: #fff;
          }
          &.on3 {
            background: #ffd26e;
            color: #fff;
          }
        }
        image {
          width: 64rpx;
          height: 64rpx;
          margin: 0 24rpx;
        }
      }
    }
  }
  .h_date {
    justify-content: left;
    margin: 24rpx 0;
    text {
      margin-right: 24rpx;
      padding: 10rpx 16rpx;
      background: #f6f6f6;
      border-radius: 8rpx;
      &.active {
        color: #2d84fb;
        font-weight: 500;
        background: #c0dafe;
      }
    }
  }
  .h_sort {
    justify-content: left;
    text {
      margin-right: 50rpx;
      &.active {
        color: #2d84fb;
        font-weight: 500;
      }
    }
  }
}
.client_list {
  margin: 24rpx 0 0 0;
  .client {
    align-items: center;
    justify-content: space-between;
    margin: 24rpx;
    font-size: 22rpx;
    &.client_color {
      text:first-child {
        color: #8a929f;
      }
    }
    text {
      width: 24%;
      text-align: center;
    }
  }
}
.state_box {
  background: #fff;
  border-radius: 12rpx;
  margin-top: 24rpx;
  padding: 24rpx 0;
  .charts-box {
    margin: 0 auto;
  }
}
.tab {
  background: rgba(255, 255, 255, 0);
}
.calendar_box {
  min-height: auto;
  background-color: #fff;
  padding: 24rpx;
  box-sizing: border-box;
  border-radius: 12rpx;
  .pending {
    margin-top: 24rpx;
    > .row {
      justify-content: space-between;
      padding: 16rpx 24rpx;
      margin-bottom: 20rpx;
      border-radius: 8rpx;
      align-items: center;
      .tit {
        align-items: center;
        text {
          width: 10rpx;
          height: 10rpx;
          border-radius: 50%;
          margin-right: 6rpx;
        }
      }
      text {
        font-weight: 500;
      }
      &.color1 {
        background: #fef0e7;
        color: #fe6c17;
        .tit {
          text {
            background: #fe6c17;
          }
        }
      }
      &.color2 {
        background: #fef8e5;
        color: #f5bd04;
        .tit {
          text {
            background: #f5bd04;
          }
        }
      }
    }
  }
  .remind_info {
    background: #f6f6f6;
    border-radius: 12rpx;
    padding: 24rpx;
    margin: 24rpx 0;
    min-height: 180rpx;
  }
  .remind {
    font-weight: 500;
    margin-top: 24rpx;
    text {
      flex: 1;
      font-size: 32rpx;
    }
    view {
      color: #2d84fb;
    }
  }
}
.title {
  margin: 12px 0;
  font-size: 16px;
}
.sort {
  background: #fff;
  justify-content: space-between;
  padding: 35px 0;
  border-radius: 6px;
  .info {
    width: 33%;
    align-items: center;
    border-right: 1px solid #fff6f6f6;
    > text {
      font-size: 11px;
      color: #8a929f;
    }
    .num {
      margin-bottom: 12px;
      font-size: 24px;
      font-weight: 500;
      align-items: center;
    }
  }
  .info:last-child {
    border: none;
  }
}
</style>
<script>
import tabBar from "@/components/tabBar";
import zsyCalendar from "@/components/zsy-calendar/zsy-calendar.vue";
export default {
  components: {
    zsyCalendar,
    tabBar,
  },
  data() {
    return {
      tab: [
        { description: "客户状态", type: 0 },
        { description: "客户来源统计", type: 1 },
        { description: "跟进数据统计", type: 2 },
      ],
      tab_type: 0,
      honor_list: [
        { name: "认领最多", type: 0 },
        { name: "跟进最多", type: 1 },
        { name: "成交最多", type: 2 },
      ],
      honor_type: 0,
      honor_date: [
        { name: "昨日", type: 0 },
        { name: "今天", type: 1 },
        { name: "7日", type: 2 },
      ],
      date_type: 0,
      // #ifdef H5
      cWidth1: "",
      cHeight1: "",
      // #endif
      chartData: {
        series: [
          {
            data: [
              { name: "一班", value: 50 },
              { name: "二班", value: 30 },
              { name: "三班", value: 20 },
              { name: "四班", value: 18 },
              { name: "五班", value: 8 },
            ],
          },
        ],
      },
      chartData2: {
        series: [
          {
            data: [
              {
                name: "渠道报备",
                value: 50,
              },
              {
                name: "现场接待",
                value: 30,
              },
              {
                name: "自渠拓客",
                value: 20,
              },
              {
                name: "私客录入",
                value: 18,
              },
              {
                name: "自定义来源",
                value: 8,
              },
              {
                name: "合作平台",
                value: 8,
              },
            ],
          },
        ],
      },
      chartData3: {
        series: [
          {
            data: [
              {
                name: "一班",
                value: 50,
              },
              {
                name: "二班",
                value: 30,
              },
              {
                name: "三班",
                value: 20,
              },
              {
                name: "四班",
                value: 18,
              },
              {
                name: "五班",
                value: 8,
              },
            ],
          },
        ],
      },
    };
  },
  computed: {
    is_open_im() {
      return this.$store.state.im.ischat;
    },
  },
  onLoad() {
    this.cWidth1 = uni.upx2px(650);
    this.cHeight1 = uni.upx2px(500);
    this.getData();
  },
  methods: {
    getData() {},
    // 日历选中日期改变事件回调
    change(e) {
      console.log(e);
    },
    onClickTab(e) {
      this.tab_type = e.index;
    },
    clickHonor(index) {
      this.honor_type = index;
    },
    clickDate(index) {
      this.date_type = index;
    },
    toRemind() {
      this.$navigateTo("remind");
    },
  },
};
</script>
