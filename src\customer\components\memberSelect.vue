<template>
  <view class="transfer">
    <!-- <view class="transfer-header">
        <uni-search-bar class="uni-mt-10" radius="100" placeholder="请输入同事姓名" clearButton="none" cancelButton="none" />

    </view> -->

    <scroll-view scroll-y="true" class="transfer-body">
      <view class="client-list">
        <view
          class="client-item row"
          :class="{ isactive: item.checked }"
          v-for="item in admin_list"
          :key="item[defaultValue]"
          @click="selectMember(item)"
        >
          <view class="left">
            {{ item[defaultName][0] }}
          </view>
          <view class="right">
            <view class="t">{{item[defaultName] }}</view>
          </view>
        </view>

        
      </view>
    </scroll-view>
    <view class="btn-bottom">
      <view class="btn-box row">
        <view class="btn plain" @click="$emit('cancel')">取消</view>
        <view class="btn" @click="onCreateData">确认</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props:{
    admin_list:{
      type:Array,
      default:()=>[]
    },
    multiple:{
      type:Boolean,
      default:false
    },
    defaultValue:{
      type:String,
      default:"id"
    },
    defaultName:{
      type:String,
      default:"user_name"
    },
    selectPeikan:{
      type:Array,
      default:()=>[]
    }
  },
  data(){
    return {
      selectedArr:[],
    }
  },
  created(){
   
    this.selectedArr = this.selectPeikan
      this.init()
  },
  methods:{
    onSearchInput(val){
        console.log(val);
    },  
    selectMember(item){
        if (this.multiple){
          item.checked =!item.checked
          this.$forceUpdate()
        }else {
          this.admin_list.map(i=>{
             i.checked =false
            if(i[this.defaultValue]== item[this.defaultValue]) {
              i.checked =true
            }
          })
          this.$forceUpdate()
        }
    },
    init(){
      this.admin_list.map(item=>{
        item.checked =false
        this.selectedArr.map(it=>{
          if(item[this.defaultValue] == it[this.defaultValue]) {
            item.checked =true
          }
          return it
        })
        return item
      })
    },
    onCreateData(){
      let checkedArray = []
      this.admin_list.map(item=>{
        if(item.checked){
          checkedArray.push(item)
        }
      })
      this.$emit("selectedOk",checkedArray )
    }
  }
}
</script>

<style lang ="scss" scoped>

.transfer{
  display: flex;
  flex-direction: column;
  height: 100vh;
  .transfer-header{
    .search-bar{
      .uni-input-form, .uni-input-wrapper{
        justify-content: flex-start;
      }

    }
  }

  .transfer-body{
    flex: 1;
    height: calc( 100% - 100px);
  }
}

.transfer {
  .client-list {
    .client-item {
      text-align: center;
      align-items: center;
      padding: 10px 12px;
      border-bottom: 1px solid #f6f6f6;
      .left {
        width: 30px;
        line-height: 30px;
        height: 30px;
        border-radius: 50%;
        color: #fff;
        background: #2d84fb;
        margin-right: 12px;
      }
      .right {
        align-items: flex-start;
        line-height: 20px;
      }
      &.isactive {
        background: #f6f6f6;
      }
    }
  }
  .btn-bottom {
    height: 100px;
    .btn-box {
      background: #fff;
      justify-content: space-between;
      position: fixed;
      bottom: 0;
      right: 0;
      width: 100%;
      padding: 24px;
      .btn {
        height: 42px;
        width: 100%;
        line-height: 42px;
        text-align: center;
        color: #fff;
        background: #2d84fb;
        border-radius: 6px;
      }
      .plain {
        background: #fff;
        color: #333;
        border: 1px solid #999;
        margin-right: 20px;
      }
    }
  }
}
</style>