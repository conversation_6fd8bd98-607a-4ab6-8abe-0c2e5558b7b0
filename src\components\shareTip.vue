<template>
  <div
    class="tip_mask"
    :class="{ show: show }"
    @click="handleHide()"
    @touchmove.stop.prevent="() => {}"
  >
    <div class="tip_box">
      <img
        src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/icons/share_tip.png?x-oss-process=style/w_240"
        alt=""
      />
      <p>{{ tip_text }}</p>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {}
  },
  props: {
    show: Boolean,
    tip_text: {
      type: String,
      default: '点击右上角，即可分享'
    }
  },
  methods: {
    handleHide () {
      this.$emit('hide')
    }
  }
}
</script>

<style scoped lang="scss">
.tip_mask {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: rgba($color: #000000, $alpha: 0);
  opacity: 0;
  z-index: -1;
  display: none;
  transition: 0.26s;
  &.show {
    display: block;
    background-color: rgba($color: #000000, $alpha: 0.5);
    z-index: 1005;
    opacity: 1;
  }
  .tip_box {
    position: absolute;
    top: 30rpx;
    right: 30rpx;
    width: 50%;
    text-align: right;
    img {
      display: inline-block;
      width: 26%;
    }
    p {
      margin-top: 20rpx;
      color: #fff;
    }
  }
}
</style>
