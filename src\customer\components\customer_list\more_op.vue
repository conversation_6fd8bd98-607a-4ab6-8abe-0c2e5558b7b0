<template>
<view>
    <myPopup :show="show" @hide="show = false">
        <view class="more_box">
            <view class="more_title row"> 更多操作 </view>
            <view class="more_list row">
                <view class="more_item" v-for="item in currentList" :key="item.name" @click="handleItemClick(item)">
                    <view class="icon">
                        <image :src="item.icon | imageFilter('w_80')"></image>
                    </view>
                    <view class="name"> {{item.title}} </view>
                </view>
            </view>
        </view>
    </myPopup>

    <copyToMember :visible.sync="dialogs.copyToMember" :customerId="customerId" @success="handleCopyToMemberSuccess"></copyToMember>
    <transToMember :visible.sync="dialogs.transToMember" :customerId="customerId" @success="handleTransToMemberSuccess" :current="current"></transToMember>
    <transToSeas :visible.sync="dialogs.transToSeas" :customerId="customerId" @success="handleTransToSeasSuccess"></transToSeas>
</view>
</template>

<script>
import utils from '@/common/utils/customer-options.js';
import { transToPrivate, setCrmCustomerTop, setTransCustomerTop,delTransCustomer } from '@/common/utils/customer.js';
import myPopup from '@/components/myPopup';
import copyToMember from '@/components/customer/copyToMember';
import transToMember from '@/components/customer/transToMember';
import transToSeas from '@/components/customer/transToSeas';  
export default {
    props: {
        current: { type: String, default: 'my' },
        visible: { type: Boolean, default: false },
        customer: { type: Object, default: ()=>({}) },
        customerId: { type: Number, default: 0 },
        //新增list列表中客户索引
        customerIndex: { type: Number, default: -1 },
    },
    components: {
        myPopup, copyToMember, transToMember, transToSeas
    },
    data() {
        return {
            show: false,
            list: [],
            customerStatusList: null,
            dialogs: {
                copyToMember: false,
                transToMember: false,
                transToSeas: false
            },
            setToping: false,
        }
    },
    computed: {
        isTrans(){
            return this.current === 'trans';
        },
        isTrader(){
            return this.current === 'my_trader' || this.current === 'seas_trader';
        },
        isListTop(){
            return this.customer ? this.customer.order == 1 : false;
        },
        currentList(){
            return this.list.map( e => {
                if(e.name === 'setTop'){
                    e.title = this.isListTop ? '取消置顶' : '置顶';   
                }
                return e;
            });
        },
    },
    watch: {
        visible(val){
            this.show = val;
        },
        show(val){
            this.$emit('update:visible', val)
        },
        customerIndex(val) {
            console.log('最新的 customerIndex:', val);
        }
    },
    mounted() {
        this.list = this.getList()
    },
    filters: {
       
    },
    methods: {
        getList(){
            const maintain = { title: '维护资料', name: 'maintain', icon: '/yidongduan/customer/<EMAIL>' }
            //const approve = { title: '审批', name: 'approve', icon: '/yidongduan/customer/<EMAIL>' }
            const follow = { title: '跟进', name: 'follow', icon: '/yidongduan/customer/<EMAIL>' }
            const transToMember = { title: '转交到同事', name: 'transToMember', icon: '/yidongduan/customer/<EMAIL>' }
            const transToSeas = { title: '转交到公海', name: 'transToSeas', icon: '/yidongduan/customer/<EMAIL>' }
            const setTop  = { title: '置顶', name: 'setTop', icon: '/icons/top-set.png' }
            switch (this.current) {
                case 'my':
                    return [ maintain, transToSeas, transToMember, follow, setTop]
                case 'seas':
                case 'potential':
                case 'my_trader':
                case 'seas_trader':
                case 'useless':
                    return [ maintain, transToSeas, transToMember, follow]
                case 'trans':
                    const transToPrivate = { title: '转为私客', name: 'transToPrivate', icon: '/yidongduan/customer/<EMAIL>' }
                    const copyToMember = { title: '复制给同事', name: 'copyToMember', icon: '/yidongduan/customer/<EMAIL>' }
                    const delCustomer = { title: '删除', name: 'delCustomer', icon: '/yidongduan/customer/<EMAIL>' }
                    return [ maintain, transToPrivate, copyToMember, transToMember, follow, delCustomer,setTop];
            }
            return [];
        },
        handleItemClick(item){
            this[item.name]();
        },
        /**
         * 维护资料
         */
         maintain () {
            this.show = false;
            this.$navigateTo(`uphold?type=2&id=${this.customerId}&current=${this.current}`);
        },
        /**
         * 审批
         */
        async approve(){
            this.show = false;
            if(this.isTrans){
                this.$navigateTo("/house/applyApprove?id=5&house_id=" + this.customerId)
            }else{
                if(!this.customerStatusList){
                    this.customerStatusList = await utils.getStatusList();
                }
                //审批页读的这些存储数据。。。
                if(this.isTrader){
                    uni.setStorageSync("shenpi", JSON.stringify({
                        is_trader: 1,
                        stateList: this.customerStatusList
                    }))
                }else{
                    uni.setStorageSync("shenpi", JSON.stringify({
                        is_del: this.customer.is_del,
                        is_state: this.customer.is_state,
                        state_list: this.customer.state_list,
                        stateList: this.customerStatusList
                    }))
                }
                this.$navigateTo("/house/applyApprove?id=1&house_id=" + this.customerId)
            }
        },
        /**
         * 转为私客
         */
        async transToPrivate(){
            try{
                if (process.env.NODE_ENV === 'development') {
                    this.show = false;
                    uni.showToast({
                        title: '开发模式下流转客转为私客模拟操作',
                        icon: 'none',
                    });
                    if(parseInt(this.customerIndex) > -1){
                        this.$emit('remove-customer', {customer: this.customer, index: this.customerIndex});
                    }else{
                        //兼容之前的调用
                        uni.$emit('getDataAgain');
                    }
                    return;
                }

                await transToPrivate(this.customerId);
                this.show = false;
                if(parseInt(this.customerIndex) > -1){
                    this.$emit('remove-customer', {customer: this.customer, index: this.customerIndex});
                }else{
                    //兼容之前的调用
                    uni.$emit('getDataAgain');
                }
                
                uni.showToast({
                    title: '转为私客成功',
                    icon: 'success'
                })
            }catch(e){}
        },
        /**
         * 转交到同事
         */
        transToMember () {
            this.show = false;
            this.dialogs.transToMember = true;
        },
        handleTransToMemberSuccess(item){
            if(parseInt(this.customerIndex) > -1){
                console.log(this.customer,this.customerIndex,'----转交给同事 操作成功---');
                console.log(item,'----转交给同事成功后获取到的参数---');
                const name = item?.name || '';//获取新的维护人
                this.$emit('remove-customer', {customer: this.customer, index: this.customerIndex, follow_user_name:name});
            }else{
                //兼容之前的调用
                uni.$emit('getDataAgain');
            } 
        },
        /**
         * 转交到公海
         */
        transToSeas(){
            this.show = false;
            this.dialogs.transToSeas = true;
        },
        handleTransToSeasSuccess(){
            if(parseInt(this.customerIndex) > -1){
                console.log(this.customer,this.customerIndex,'----转入公海 操作成功---');
                this.$emit('remove-customer', {customer: this.customer, index: this.customerIndex});
            }else{
                //兼容之前的调用
                uni.$emit('getDataAgain');
            } 
        },
        /**
         * 复制给同事
         */
        async copyToMember(){
            this.show = false;
            this.dialogs.copyToMember = true;
        },

        handleCopyToMemberSuccess(){
            if(parseInt(this.customerIndex) > -1){
                console.log(this.customer,this.customerIndex,'----流转客复制给同事 操作成功 ---');
                this.$emit('remove-customer', {customer: this.customer, index: this.customerIndex,is_del:0});
            }else{
                //兼容之前的调用
                uni.$emit('getDataAgain');
            }
        },

        /**
         * 跟进
         */
        follow(){
            this.show = false;
            this.$navigateTo(`/customer/demand?id=${this.customerId}&current=${this.current}&sys_source=customer_list&customer_index=${this.customerIndex}`);
        },
        //置顶
        async setTop(){
            if(this.setToping){
                return;
            }
            this.setToping = true;

            if (process.env.NODE_ENV === 'development' && 1==2) {
                this.show = false;
                this.setToping = false;
                uni.showToast({
                    title: '开发模式下模拟操作置顶',
                    icon: 'none',
                });
                if(parseInt(this.customerIndex) > -1){
                    this.$emit('remove-customer', {customer: this.customer, index: this.customerIndex,position:'top'});
                }else{
                    //兼容之前的调用
                    uni.$emit('getDataAgain');
                }
                return;
            }
            
            try{
                const res = this.isTrans ? await setTransCustomerTop(this.customerId) : await setCrmCustomerTop(this.customerId);
                this.show = false;
                if(parseInt(this.customerIndex) > -1){
                    this.$emit('remove-customer', {customer: this.customer, index: this.customerIndex,position:'top'});
                }else{
                    //兼容之前的调用
                    uni.$emit('getDataAgain');
                }
                uni.showToast({
                    title: res && res.msg ? res.msg : '设置成功',
                    icon: 'success',
                });
            }catch(e){}
            this.setToping = false;
        },
        //删除客户
        delCustomer(){
            uni.showModal({
                title: '提示',
                content: '确定删除该客户吗？',
                success: async (res) => {
                    if (res.confirm) {
                        try{
                            if (process.env.NODE_ENV === 'development') {
                                this.show = false;
                                uni.showToast({
                                    title: '开发模式下模拟操作删除流转客',
                                    icon: 'none',
                                });
                                if(parseInt(this.customerIndex) > -1){
                                    this.$emit('remove-customer', {customer: this.customer, index: this.customerIndex});
                                }else{
                                    //兼容之前的调用
                                    uni.$emit('getDataAgain');
                                }
                                return;
                            }

                            const res = await delTransCustomer(this.customerId);
                            this.show = false;
                            if(parseInt(this.customerIndex) > -1){
                                this.$emit('remove-customer', {customer: this.customer, index: this.customerIndex});
                            }else{
                                //兼容之前的调用
                                uni.$emit('getDataAgain');
                            }
                            uni.showToast({
                                title: res && res.msg ? res.msg : '删除成功',
                                icon: 'success',
                            });
                        }catch(e){}
                    }
                }
            })
        }
    }
        
}

</script>

<style scoped lang="scss"> 
.more_box {
  border-radius: 40rpx 40rpx 0px 0px;
  background: #ffffff;

  .more_title {
    justify-content: center;
    color: #8a929f;
    font-size: 32rpx;
    padding: 24rpx;
    border-bottom: 2rpx solid #f2f2f2;
  }
  .more_list {
    padding: 24rpx;
    flex-wrap: wrap;
    .more_item {
      width: 25%;
      justify-content: center;
      align-items: center;
      padding: 24rpx 0;
      .icon {
        width: 40rpx;
        height: 40rpx;
        overflow: hidden;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .name {
        color: #8a929f;
        margin-top: 24rpx;
        font-size: 24rpx;
      }
    }
  }
}

.reason {
  width: 80vw;
  height: 400rpx;
  margin: calc(100% - 200rpx) auto;
  padding: 40rpx;
  border-radius: 20rpx;
  background: #fff;
  position: relative;
  .close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    font-size: 48rpx;
  }
  textarea {
    margin-top: 16rpx;
    padding: 12rpx 32rpx;
    box-sizing: border-box;
    background: #f5f7fa;
    border: 2rpx solid #e8e8e8;
    height: 280rpx;
    width: 100%;
  }
  .btn {
    display: inline-block;
    padding: 20rpx 20rpx;
    background: #2d84fb;
    color: #fff;
    margin: 20rpx auto 0;
  }
}
</style>