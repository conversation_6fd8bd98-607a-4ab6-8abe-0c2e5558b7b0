<template>
<view class="search-input-wrapper">
    <view class="search-input" @click="goSearchPage">
        <view class="search-input-inner">
            <myIcon class="icon" type="ic_sousuo3x1" color="#bbb3b3" size="32rpx"></myIcon>
            <text class="placeholder">如：部门 成员 手机号 客户编号 跟进内容等</text>
        </view>
	</view>
</view>
</template>

<script>
import myIcon from "@/components/my-icon";
export default {
    props: {
        current: { type: String, default:'my' },
        params: { type: Object, default:{} },
    },
    components: {
        myIcon
    },
    data() {
        return {
          
        }
    },
    methods: {
        goSearchPage() {
            uni.setStorageSync('crmSearchParams', JSON.stringify(this.params));
            this.$navigateTo('/customer/search?current=' + this.current+'&from=list')
        }
    }
}
</script>

<style scoped lang="scss"> 
.search-input-wrapper{
    height: 128rpx;
    padding: 24rpx 32rpx;
    background-color: #fff;
    .search-input{
        height: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        background-color: #F6F6F6;
        border-radius: 16rpx;
        padding: 0 24rpx;
        .search-input-inner{
            display: inline-block;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: rgba(41, 44, 57, 0.4);
            .placeholder{
                padding-left: 16rpx;
            }
        }
    }
}
</style>