<template>
  <view class="img-list row">
    <canvas canvas-id="myCanvas" style="width:200px;height:200px;display:none"></canvas>
    <block v-if="customer">
      <view @click="chooseVideoImage">
        <slot></slot>
      </view>
    </block>
    <block v-else>
      <view class="img-box" v-for="(image, index) in imageList" :key="index">
        <video :src="image" v-if="$isVideoReg(image)"></video>
        <image
          mode="aspectFill"
          @click="$previewImage(image)"
          :src="image"
          :v-if="imgShow && !$isVideoReg(image)"
        ></image>
        <view v-if="del" class="del-img" @click="delImg(index)">x</view>
      </view>
      <view
        class="icon-box"
        v-if="!$isVideoReg(imageList[0]) && imageList.length < maxCount && showAdd"
      >
        <myIcon @click="chooseVideoImage" type="icons01" color="#d8d8d8" size="100rpx"></myIcon>
      </view>
    </block>
  </view>
</template>

<script>
import myIcon from '@/components/my-icon'
import config from '@/page_outside/config/index'
export default {
  components: {
    myIcon,
  },
  props: {
    // 图片存放
    imgs: {
      type: Array,
      default: function () {
        return []
      },
    },
    del: {
      type: Boolean,
      default: true,
    },
    action: {
      type: [String],
      default: '',
    },
    imgShow: {
      type: Boolean,
      default: true,
    },
    time_watermark: {
      type: [Number, String],
      default: 0,
    },
    upInfo: {
      type: [Object],
      default: () => { },
    },
    upload_category: [String, Number],
    is_visit: [Boolean],
    is_video: {
      type: Boolean,
      default: false,
    },
    maxCount: {
      type: [Number, String],
      default: 100,
    },
    customer: {
      type: [Boolean],
      default: false
    },
    showAdd: {
      type: [Boolean || String],
      default: true
    },
    disabled: {
      type: [Boolean || String],
      default: false
    },
    disabledText: {
      type: [String],
      default: ''
    }
  },
  watch: {
    imgs (val) {
      this.imageList = Array.from(val)
    },
  },
  created () {
    let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
    let curRoute = routes[routes.length - 1].$mp?.query // 获取当前页面路由，也就是最后一个打开的页面路由
    if (curRoute) {
      this.website_id = curRoute.website_id
    }else{
      this.website_id = this.$store.state.website_id
    }
    // #ifdef H5
    const ua = window.navigator.userAgent.toLowerCase()
    if (ua.match(/WxWork/i) == 'wxwork') {
      this.isWorkWechat = true
    }
    // #endif
    this.imageList = Array.from(this.imgs)
  },
  data () {
    return {
      // 视频存放
      src: '',
      sourceTypeIndex: 2,
      sourceType: ['拍摄', '相册', '拍摄或相册'],
      VideoOfImagesShow: true,
      per: 0,
      imageList: [],
      image:''
    }
  },
  methods: {
    addImg () {
      if (this.isWorkWechat) {
        let _this = this
        this.getWxQyWxConfig(
          ['agentConfig', 'chooseImage', 'uploadImage', 'getLocalImgData'],
          (wx) => {
            _this.wx = wx
            _this.wechatChooseImg()

          }
          ,
          this.upInfo && this.upInfo.website_id ? this.upInfo.website_id : '',
          1
        )
        return
      }
      uni.chooseImage({
        count: 9, // 选择数量  默认9
        sizeType: ['compressed '],
        sourceType: this.is_visit ? ['camera'] : ['camera', 'album'], //从相册选择
        success: (value) => {
          let maxLen = this.maxCount - this.imageList.length
          if (value.tempFilePaths.length > maxLen) {
            uni.showToast({
              title: `最大上传${this.maxCount}张`,
              icon: 'none',
              duration: 2000,
            })
          } else {
            uni.showLoading({
              title: '上传中',
            })
            for (var i = 0; i < value.tempFilePaths.length; i++) {
              let isEnd = i == value.tempFilePaths.length - 1 ? true : false
              this.compressImageWechat(value.tempFilePaths[i], isEnd)
            }
          }
          // this.imageList = this.imageList.concat(res.tempFilePaths)  //头条
        },
      })
    },
    wechatChooseImg () {
      //选择图片
      // this.setWxConfig()
      // if (!this.wx) {
      //   uni.showToast({
      //     title: '上传组件未初始化完毕，请稍后重试',
      //     icon: 'none',
      //   })
      //   // this.setWxConfig()
      //   this.$emit('chooseimgfail')
      //   return
      // }
      let _this = this
      let chooseCount = this.maxCount - this.imageList.length
      this.wx.chooseImage({
        count: chooseCount > 9 ? 9 : chooseCount, // 默认9
        sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
        success: function (res) {
          var tempFilePaths = res.localIds // 返回选定照片的本地ID列表，localId可以作为img标签的src属性显示图片
          if (tempFilePaths.length > _this.maxCount - _this.imageList.length) {
            uni.showToast({
              title: '最多只能上传' + _this.maxCount + '张图片',
              icon: 'none',
            })
            return false
          }
          let i = 0
          uni.showLoading({
            title: '正在上传',
            mask: true,
          })
          // if (_this.customer) {
          //   _this.image_list = []
          // } else {
          _this.imageList = Array.from(_this.imgs)
          // }
          _this.$emit('chooseDon', tempFilePaths)
          function upload () {
            _this.wx.getLocalImgData({
              localId: tempFilePaths[i], // 图片的localID
              success: function (r) {
                var localData = r.localData // localData是图片的base64数据，可以用img标签显示
                let formData = {
                  category: _this.upload_category,
                  time_watermark: _this.time_watermark,
                  website_id: _this.website_id
                }
                if (_this.upInfo) {
                  formData = Object.assign(formData, _this.upInfo)
                }
                _this.$uploadFile(_this.action, localData, formData, (re) => {
                  if (re.statusCode == 200) {
                    _this.imageList.push(re.data.url)
                  } else {
                    uni.showToast({
                      title: re.message || '上传失败',
                      icon: 'none',
                    })
                    _this.$emit('error', re)
                  }
                  i++
                  if (i < tempFilePaths.length) {
                    upload()
                  } else {
                    _this.$emit('uploadDone', _this.imageList)
                    uni.hideLoading()
                  }
                })
              },
            })
          }
          upload()
        },
      })
    },
    // compressionImg(img) {
    //   var that = this;
    //   uni.getImageInfo({
    //     src: img,
    //     success: function(res) {
    //       console.log(res,"0000");
    //       let canvasWidth = res.width; //图片原始长宽
    //       let canvasHeight = res.height;
    //       let base = canvasWidth / canvasHeight;
    //       if (canvasWidth > 500) {
    //         canvasWidth = 500;
    //         canvasHeight = Math.floor(canvasWidth / base);
    //       }
    //       let img = new Image();
    //       // console.log(img,"1234567");
    //     img.src = res.path; // 要压缩的图片
    //       let canvas = document.createElement("canvas");
    //       let ctx = canvas.getContext("2d");
    //       canvas.width = canvasWidth;
    //       canvas.height = canvasHeight;
    //       //  将图片画到canvas上面   使用Canvas压缩
    //       ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);
    //       canvas.toBlob(function(fileSrc) {
    //         let imgSrc = window.URL.createObjectURL(fileSrc); //原生JS生成文件路径
    //         uni.downloadFile({
    //           url: imgSrc, //仅为示例，并非真实的资源
    //           success: (resFilePath) => {
    //             if (resFilePath.statusCode === 200) {
    //               // canvas.toDataURL 返回的是一串Base64编码的URL
    //               // 指定格式 PNG
    //               var uploadimg = canvas.toDataURL("image/png");
    //               uni.uploadFile({
    //                 url: config.h5Api + config.uploadApi,
    //                 method: "POST",
    //                 header: {
    //                   Authorization:
    //                     "Bearer " +
    //                     uni.getStorageSync(
    //                       "token" + that.$store.state.website_id
    //                     ),
    //                 },
    //                 name: "file",
    //                 filePath: uploadimg,
    //                 formData: {
    //                   category: that.upload_category,
    //                   time_watermark: that.time_watermark,
    //                 },
    //                 success: (res) => {
    //                   // let imgUrls = JSON.parse(res.data); //微信和头条支持
    //                   if (res.statusCode === 200) {
    //                     let img_list = JSON.parse(res.data);
    //                     that.imageList.push(img_list.url);
    //                     uni.hideLoading();
    //                   } else {
    //                     uni.showToast({
    //                       title: res.data.message || "上传失败",
    //                       icon: "none",
    //                     });
    //                   }
    //                 },
    //               });
    //             }
    //           },
    //         });
    //       });
    //     },
    //   });
    // },
    
    compressImageWechat(img, isEnd) {
      uni.getImageInfo({
        src: img,
        success: res=>{
          console.log(res, '');
          let imgWidth = res.width,
              imgHeight = res.height,
              imgPath = res.path;
          if(imgWidth > 500){
            let pct = imgWidth / imgHeight;
            imgWidth = 500;
            imgHeight = Math.floor(imgWidth / pct);

            uni.compressImage({
              src: imgPath,
              compressedWidth: imgWidth,
              compressHeight: imgHeight,
              success: res => {
                this.uploadFile(res.tempFilePath, isEnd);
              }
            })
          }else{
            this.uploadFile(imgPath, isEnd);
          }
        },
        fail: (e)=>{
          console.log(e);
          this.uploadFile(img, isEnd);
        }
      });
    },
    uploadFile(filePath, isEnd){
      const that = this;
      let formData = {
        category: that.upload_category,
        time_watermark: that.time_watermark,
        website_id: that.website_id
      }
      if (that.upInfo) {
        formData = Object.assign(formData, that.upInfo)
      }

      let uploadUrl = config.appApi + (this.action || config.uploadApi),
          token = uni.getStorageSync('wxwork_token'),
          headers = {
            Authorization: token ? 'Bearer ' + token : '',
            From: "weixinxiaochengxuCrm"
          };
      console.log(uploadUrl, formData);
      uni.uploadFile({
        url: uploadUrl,
        header: headers,
        name: 'file',
        filePath: filePath,
        formData: formData,
        success: (res) => {
          // let imgUrls = JSON.parse(res.data); //微信和头条支持
          if (res.statusCode === 200) {
            let img_list = JSON.parse(res.data)
            that.imageList.push(img_list.url)
            if (isEnd) {
              that.$emit('uploadDone', that.imageList)
            }
            uni.hideLoading()
          } else {
            uni.showToast({
              title: res.data.message || '上传失败',
              icon: 'none',
            })
          }
        },
        fail: (er)=> {
          console.log(er);
          uni.showToast({
            title: er.errMsg || '上传失败',
            icon: 'none',
          })
        }
      })
    },

    compressionImg(img) {
      console.log(img,"img");
  var that = this;
  uni.getImageInfo({
    src: img,
    success: function (res) {
      uni.hideLoading();
      console.log(res, "0000");
      let canvasWidth = res.width; // 图片原始长宽
      let canvasHeight = res.height;
      let base = canvasWidth / canvasHeight;
      if (canvasWidth > 500) {
        canvasWidth = 500;
        canvasHeight = Math.floor(canvasWidth / base);
      }
console.log(res.path,"=====");
      // 创建Canvas
      const ctx = uni.createCanvasContext("myCanvas", that); // 创建一个Canvas上下文
       console.log(33333);
      // 将图片画到Canvas上
      ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);
      console.log(img, ctx,"0000000");
      // Canvas绘图完成后，导出图片数据
 
        ctx.draw(true,  ()=> {
        console.log(5555);

        uni.canvasToTempFilePath({
          canvasId: "myCanvas",
          success: function (tempFilePath) {
            console.log(tempFilePath,"tempFilePath");
            console.log(777);
            // 获取到临时文件路径后，可以进行图片上传等操作
            // 上传逻辑
            let website_id = uni.getStorageSync("website_id")
            uni.uploadFile({
              url: config.appApi + config.uploadApi,
              filePath: tempFilePath.tempFilePath,
              name: "file",
              formData: {
                'category': 'that.upload_category',
                'time_watermark': 'that.time_watermark',
                'user': 'test'
              },
              header: {
                Authorization: "Bearer " + uni.getStorageSync("wxwork_token"),
              },
              success: function (res) {
                console.log(res,"锻炼锻炼登录");
                if (res.statusCode === 200) {
                  console.log(res.data,"tptptptp");
                  let img_list = JSON.parse(res.data);
                  that.imageList.push(img_list.url);
                  uni.hideLoading();
                } else {
                  console.log(67890);
                  // console.log(res.data,"一步一步");
                  uni.showToast({
                    title: res.message || '上传失败',
                    icon: "none",
                  });
                }
              },
              fail: function (err) {
                // console.log(url,"-----");
                console.log(err,"上传失败");
                uni.showToast({
                  title: "上传失败",
                  icon: "none",
                });
              },
            });
          },
          fail: function (err) {
            console.log(err,"00000");
            uni.showToast({
              title: "导出图片失败",
              icon: "none",
            });
          },
        },that);
      });
 

    },
  });
},
// async  compressionImg(img) {
//   console.log(img,"00000");
//   try {
//     const res = await uni.getImageInfo({ src: img });

//     uni.hideLoading();
//     console.log(res, "0000");

//     let canvasWidth = res.width;
//     let canvasHeight = res.height;
//     let base = canvasWidth / canvasHeight;

//     if (canvasWidth > 500) {
//       canvasWidth = 500;
//       canvasHeight = Math.floor(canvasWidth / base);
//     }

//     // console.log(res.path, "=====");

//     const ctx = uni.createCanvasContext("myCanvas");

//     ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);

//     console.log(33333);

//     await new Promise((resolve, reject) => {
//       ctx.draw(false, function () {
//         console.log(5555);
//         resolve();
//       });
//     });

//     const tempFilePath = await new Promise((resolve, reject) => {
//       uni.canvasToTempFilePath({
//         canvasId: "myCanvas",
//         success: function (tempFilePath) {
//           console.log(777);
//           resolve(tempFilePath.tempFilePath);
//         },
//         fail: function (err) {
//           reject(err);
//         },
//       });
//     });

//     console.log(tempFilePath);

//     // 现在可以进行图片上传等操作
//     await uploadImage(tempFilePath);

//     uni.hideLoading();
//   } catch (error) {
//     console.error("Error:", error);
//     // 处理错误情况
//     // 可以添加适当的错误处理逻辑
//   }
// },
    delImg (index) {
      this.imageList.splice(index, 1)
      console.log(this.imageList);
      this.$emit('delImg', this.imageList)
    },
    chooseVideoImage () {
      //  if (this.isWechat){
      //   let _this  =this
      //    this.getWxConfig(
      //   ['chooseImage', 'uploadImage', 'getLocalImgData', 'hideOptionMenu'],
      //   (wx) => {
      //    _this.wx =wx
      //    if (_this.is_video) {
      //   uni.showActionSheet({
      //     title: "选择上传类型",
      //     itemList: _this.imageList.length > 0 ? ["图片"] : ["图片", "视频"],
      //     success: (res) => {
      //       if (res.tapIndex == 0) {
      //         _this.addImg();
      //       } else {
      //         _this.addVideo();
      //       }
      //     },
      //   });
      // } else {
      //   this.addImg();
      // }
      //   })
      //   return
      // }
      if (this.disabled) {
        uni.showToast({
          title: this.disabledText,
          icon: 'none'
        })
        return
      }
      if (this.is_video) {
        uni.showActionSheet({
          title: '选择上传类型',
          itemList: this.imageList.length > 0 ? ['图片'] : ['图片', '视频'],
          success: (res) => {
            if (res.tapIndex == 0) {
              this.addImg()
            } else {
              this.addVideo()
            }
          },
        })
      } else {
        this.addImg()
      }
    },
    addVideo () {
      var that = this
      // 选择视频
      uni.chooseVideo({
        count: 1,
        sourceType: ['camera', 'album'],
        success: (res) => {
          const videoSrc = res.tempFilePath
          uni.showLoading({
            title: '正在上传',
            mask: true,
          })
          // eslint-disable-next-line no-unused-vars
          const uploadTask = uni.uploadFile({
            url: config.h5Api + config.uploadApi,
            methods: 'POST',
            header: {
              Authorization: 'Bearer ' + uni.getStorageSync('token' + that.$store.state.website_id),
            },
            name: 'file',
            filePath: videoSrc,
            formData: {
              category: 103,
            },
            success: (res) => {
              if (res.statusCode === 200) {
                uni.hideLoading()
                let video_list = JSON.parse(res.data)
                that.imageList.push(video_list.url)
              } else {
                uni.showToast({
                  title: res.data.message || '上传失败',
                  icon: 'none',
                })
              }
            },
            complete: (com) => {
              console.log(com)
            },
          })
        },
      })
    },
  },
}
</script>

<style scoped lang="scss">
#canvas {
  position: fixed;
  left: -1000px;
}
.img-list {
  justify-content: flex-start;
  flex-wrap: wrap;
  // margin-top: 40rpx;
  .icon-box {
    margin-top: 10rpx;
    align-items: center;
    padding: 46rpx;
    width: 180rpx;
    height: 180rpx;
    background: #efefef;
    border-radius: 10rpx;
  }
  .img-box {
    position: relative;
    .del-img {
      font-size: 32rpx;
      align-items: center;
      position: absolute;
      color: #fff;
      width: 40rpx;
      height: 40rpx;
      top: 0;
      right: 10rpx;
      border-radius: 50%;
      background: #708efc;
    }
    .img {
      width: 180rpx;
      height: 180rpx;
      margin-right: 30rpx;
      margin-top: 10rpx;
      border-radius: 10rpx;
      overflow: hidden;
      image {
        width: 100%;
        height: 100%;
      }
    }
    image {
      margin-right: 30rpx;
      margin-top: 10rpx;
      width: 180rpx;
      height: 180rpx;
      border-radius: 10rpx;
    }
    video {
      margin-right: 30rpx;
      margin-top: 10rpx;
      width: 180rpx;
      height: 180rpx;
      border-radius: 10rpx;
    }
  }
  &::after {
    content: '';
    margin-right: 30rpx;
    margin-top: 10rpx;
    width: 160rpx;
  }
}
</style>
