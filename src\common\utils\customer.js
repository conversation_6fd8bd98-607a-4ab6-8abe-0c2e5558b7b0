//获取客户状态
import ajax from "@/page_outside/tools/ajax";
import { navigateTo } from "@/page_outside/tools/index";
import utils from "@/common/utils.js";


let 
_ajax = (type, api, params, errMsg, cb) => {
    return new Promise((resolve, reject) => {
        ajax[type](api, params, res => {
            if (res.statusCode == 200) {
                resolve(res.data);
                cb && cb(res.data)
            }else{
                if(errMsg || res?.data?.message){
                    uni.showToast({
                        title: res?.data?.message || errMsg,
                        icon: 'none'
                    });
                }
            }
            reject();
        }, er => {
            reject();
        })
    })
},
_promise = {
    get(api, params, errMsg = '', cb){
        return _ajax('get', api, params, errMsg, cb)
    },
    post(api, params, errMsg = '', cb){
        return _ajax('post', api, params, errMsg, cb)
    }
};


//是否需要强制跟进
export function checkForceFollow(options = {}){
    return new Promise((resolve, reject) => {
        ajax.get('/qywx/client/verify_follow', {}, res => {
            if (res.statusCode == 200) {
                const data = res.data || {}
                if (data?.id > 0 && data?.client_id > 0) {
                    resolve(data);
                    if(options.params){
                        uni.showModal({
                            title: '提示',
                            content: '您有查看电话未跟进 去跟进？',
                            success: (result) => {
                                if (result.confirm) {
                                    options.params.id = data.client_id;
                                    navigateTo('/customer/detail?'+utils.buildHttpQuery(options.params));
                                } else if (result.cancel) {
                                    console.log('用户点击取消');
                                }
                            }
                        })
                    }
                }
            }else{
                uni.showToast({
                    title: res?.data?.message || '查询跟进状态失败',
                    icon: 'none'
                });
            }
            reject();
        }, er => {
            reject();
        })
    })
}


export function seeTel(id){
    return _promise.get(`/qywx/client/see_tel/${id}`, {}, '查看电话失败');
}

export function seePrivateTel(id){
    return _promise.get(`/admin/private_client/see_tel/${id}`, {}, '查看电话失败');
}

//查询手机号归属地
export function queryMobilePlace(id){
    return _promise.get(`/admin/crm/client/query_mobile_place/${id}`, {}, '查询手机号归属地失败');
}
export function queryPrivateMobilePlace(id){
    return _promise.get(`/admin/private_client/query_mobile_place/${id}`, {}, '查询手机号归属地失败');
}


//转为私客
export function transToPrivate(id, confirm = true){
    if(!confirm){
        return _promise.post(`/admin/private_client/to_crm_private_client`, { ids: String(id) }, '转为私客失败');
    }
    return new Promise((resolve, reject) => {
        uni.showModal({
            title: '提示',
            content: '此操作会将客户转为私客, 是否继续?',
            success: async res => {
                if (res.confirm) {
                    let data = _promise.post(`/admin/private_client/to_crm_private_client`, {ids: String(id)}, '转为私客失败');
                    resolve(data)
                }
                reject();
            },
        })
    })
}


//提醒跟进
export function reminder(data){
    if(isTrans) return transReminder(data);
    return crmReminder(data);
}
export function crmReminder(data){
    return _promise.post(`/qywx/client_remind/create`, data, '提醒跟进失败');
}
export function transReminder(data){
    return _promise.post(`/admin/private_client/client_remind/create`, data, '提醒跟进失败');
}

//判断是否可认领
export function isClaimAbled(item, current){
    let isUseless = current === 'useless',
        isMy = current === 'my',
        isBelongPotential = isUseless || current == 'potential';

    //废客有follow_id不可认领
    if(isUseless){
        if(item.follow_id){
            return false;
        }
    }

    if (item.push_type == 2) {
        // 我司成交的不可认领
        if (item.tracking_identify == 1) {
            return false
        }
        // 掉工转公标记的可以领取 
        if (item.public2_status > 0) {
            return true
        }
        // 我掉工转公的可认领
        if (isMy){
            return true;
        }

        // 潜在客户可以领取
        if (isBelongPotential) {
            return true
        }
        
        // 其他情况 不可领取 
        return false
    }
    return true
}


//置顶/取消置顶
export function setCrmCustomerTop(id){
    return _promise.get(`/admin/crm/client/top/${id}`, {}, '设置失败');
}
export function setTransCustomerTop(id){
    return _promise.get(`/admin/private_client/top/${id}`, {}, '设置失败');
}


//设置跟进置顶
export function setCrmFollowTop(id){
    return _promise.get(`/admin/crm/client_follow/top/${id}`, {}, '设置失败');
}

//设置跟进点赞
export function setCrmFollowPraise(id){
    return _promise.get(`/admin/crm/client_follow/click/${id}`, {}, '设置失败');
}


//获取自定义tabs
export function getCrmCustomTabs(tab_type){
    return _promise.get(`/admin/crm/crm_tab/tabs`, { tab_type });
}

//获取客户详情-更多操作配置
export function getCrmDetailMoreOptions(client_id){
    return _promise.get(`/admin/crm/client/get_app_menu_show?client_id=${client_id}`, {}, '获取更多操作失败');
}

//删除流转客
export function delTransCustomer(id){
    return _promise.get(`/admin/private_client/del_client/${id}`, {}, '删除客户失败');
}

//获取客户新增表单
export function getCustomerForms(params = {}){
    return _promise.get(`/admin/crm/client/add_form`, params, '获取表单失败');
}