<template>
  <view class="page">
    <!-- 页面内容 -->
    <view class="table">
      <table style="border-collapse: collapse">
        <thead class="thead flex-row" style="background-color: #f8f8f8">
          <tr class="flex-row items-center flex-1">
            <th class="flex-row flex-2 items-center" @click="sortList('')">
              <view class="title"> 排行榜 </view>
            </th>
            <th class="flex-row flex-1 items-center" @click="sortList(2)">
              <view class="title"> 发布 </view>
              <view class="icon">
                <view class="up"></view>
                <view class="down" :class="{ active: params.order_type == 2 }"></view>
              </view>
            </th>
            <th class="flex-row flex-1 items-center" @click="sortList(1)">
              <view class="title"> 粉丝 </view>
              <view class="icon">
                <view class="up"></view>
                <view class="down" :class="{ active: params.order_type == 1 }"></view>
              </view>
            </th>
            <th class="flex-row flex-1 items-center" @click="sortList(4)">
              <view class="title"> 分享 </view>
              <view class="icon">
                <view class="up"></view>
                <view class="down" :class="{ active: params.order_type == 4 }"></view>
              </view>
            </th>
            <th class="flex-row flex-1 items-center" @click="sortList(3)">
              <view class="title"> 点赞 </view>
              <view class="icon">
                <view class="up"></view>
                <view class="down" :class="{ active: params.order_type == 3 }"></view>
              </view>
            </th>
          </tr>
        </thead>
        <tbody class="tbody">
          <tr class="flex-row items-center flex-1" v-for="(item, index) in list" :key="index">
            <td class="flex-row flex-2 items-center">
              <view class="user flex-row items-center">
                <view class="header">
                  <image :src="item.avatar | imageFilter('w_80')"> </image>
                </view>
                <view class="name">
                  {{ item.name }}
                </view>
              </view>
            </td>
            <td class="flex-row flex-1 items-center">{{ item.total_issue }}</td>
            <td class="flex-row flex-1 items-center">{{ item.total_fans }}</td>
            <td class="flex-row flex-1 items-center">{{ item.total_share }}</td>
            <td class="flex-row flex-1 items-center">{{ item.total_like }}</td>
          </tr>
        </tbody>
        <load-more :status="load_status"></load-more>
      </table>
    </view>
    <view class="btm">
        <bottomBar :currentIndex="2"></bottomBar>
      </view>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore";
import bottomBar from "./components/bottomBars"
export default {
  components: { loadMore ,bottomBar},
  data () {
    return {
      list: [],
      params: {
        page: 1,
        rows: 30,
        order_type: '', // 1粉丝降序 2作品量降序，3：点赞降序，4：分享降序",

      },
      load_status: ""
    };
  },
  methods: {
    // 方法
    getData () {
      if (this.params.page == 1) {
        this.list = []
      }
      this.load_status = "loading";
      this.$ajax.get('/qywx/byte_dance/rand_list', this.params, res => {
        console.log(res);
        if (res.statusCode == 200) {
          this.list = this.list.concat(res.data.data)
          if (res.data.data.length < this.params.rows) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'more'
          }
        }
      })
    },
    sortList (type) {
      this.params.order_type = type
      this.params.page = 1
      this.getData()
    }
  },
  onLoad () {
    // 生命周期钩子函数
    this.getData()
  },
  onReachBottom () {
    if (this.load_status == 'more') {
      this.params.page++
      this.getData()
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  padding-bottom: 160rpx;
  /* 样式 */
  .thead {
    padding: 24rpx 48rpx;
    text-align: center;
    width: 100vw;
    tr {
      padding: 0 48rpx;
      th {
        text-align: center;
        justify-content: center;
        color: #999999;
      }
    }
    .icon {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      height: 100%;
      .up {
        width: 0;
        height: 0;
        border-left: 8rpx solid transparent;
        border-right: 8rpx solid transparent;
        border-bottom: 8rpx solid #d9d9d9;
        &.active {
          border-bottom: 8rpx solid #2d84fb;
        }
      }
      .down {
        margin-top: 7rpx;
        width: 0;
        height: 0;
        border-left: 8rpx solid transparent;
        border-right: 8rpx solid transparent;
        border-top: 8rpx solid #d9d9d9;
        &.active {
          border-top: 8rpx solid #2d84fb;
        }
      }
    }
  }
  .tbody {
    padding: 0 48rpx;
    text-align: center;
    width: 100vw;
    tr {
      padding: 32rpx 48rpx;
      td {
        text-align: center;
        justify-content: center;
        color: #999999;
      }
    }
    .user {
      .header {
        position: relative;
        image {
          width: 96rpx;
          height: 96rpx;
          border-radius: 50%;
        }
      }
      .name {
        font-size: 32rpx;
        color: #000000;
      }
    }
  }
}
</style>