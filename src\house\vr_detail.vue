<template>
  <view>
    <web-view :src="url" :webview-styles="webviewStyles"></web-view>
  </view>
</template>

<script>

export default {
  data () {
    return {
      url: '',
      webviewStyles: {
        progress: {
          color: '#f65354'
        }
      },
    }
  },
  components: {

  },
  computed: {

  },
  filters: {

  },
  onLoad (options) {
    if (options.url) {
      this.url = decodeURIComponent(options.url)
    }
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
</style>
