<template>
    <view class="whole">
        <!-- 智慧经营 -->
        <!-- 本周 -->
        <view class="whole_con">
            <scroll-view class="weektop" scroll-x="true" @scroll="scroll">
                <view class="weeked" v-for="(tab, index) in navs" :key="tab.id" :id="tab.id"
                    :class="navIndex == index ? 'activite' : ''" @click="checkIndex(index)">{{ tab.name }}</view>
            </scroll-view>
            <view class="change">
                <!-- 比例模块 -->
                <scroll-view class="weekbili" scroll-x="true" @scroll="scroll">
                    <view class="proportion_whole flex-row items-center">
                        <view class="proportion" v-for="item in censusList" :key="item.id">
                            <view>
                                <view class="header">
                                    <view v-for="ic in icon" :key="ic.id">
                                        <image :src="ic.url" style="width: 64rpx; height: 64rpx;"
                                            v-if="item.desc == '客户总量' ? ic.id == 1 : ''"></image>
                                        <image :src="ic.url" style="width: 64rpx; height: 64rpx;"
                                            v-if="item.desc == '有效客户' ? ic.id == 2 : ''"></image>
                                        <image :src="ic.url" style="width: 64rpx; height: 64rpx;"
                                            v-if="item.desc == 'A级客户' ? ic.id == 3 : ''"></image>
                                        <image :src="ic.url" style="width: 64rpx; height: 64rpx;"
                                            v-if="item.desc == 'B级客户' ? ic.id == 4 : ''"></image>
                                        <image :src="ic.url" style="width: 64rpx; height: 64rpx;"
                                            v-if="item.desc == 'C级客户' ? ic.id == 5 : ''"></image>
                                    </view>
                                    <view class="name">{{ item.desc }}</view>
                                </view>
                                <view class="middle">{{ item.num }}</view>
                                <view class="foot">
                                    <view class="proportion_foot_left">占比：</view>
                                    <view class="proportion_foot_right">{{ (item.ratio * 100).toFixed(2) + "%" }}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>
           <view class="trantions_top">
            <view class="whole_middle">团队成员</view>
            <view class="whole_input">
                <view class="whole_input_left">
                    <view class="whole_input_left_name">按部门</view>
                    <view>
                        <image src="../static/icon/index/xia.png" style="width:32rpx;height:32rpx;"></image>
                    </view>
                </view>
                <view class="whole_shu">|</view>
                <view class="whole_bai">
                    请输入搜索内容
                    <!-- <view> <input placeholder="请输入搜索内容" placeholder-style="font-size:28rpx;color:#ACACAC;" type="text"></view> -->
                </view>
            </view>
            <view style="margin-bottom: 200rpx;">
                <view class="whole_list" v-for="item in dataList " :key="item.id">
                    <view class="whole_list_header">
                        <view>
                            <!-- <image src="../static/icon/index/客户-我录入的.png" style="width: 48rpx; height: 48rpx;"></image> -->
                            <view class="names">{{ item.user_name.split('')[0] }}</view>
                        </view>
                        <view class="whole_list_name">{{ item.user_name }}</view>
                        <view class="whole_list_department">{{ item.department }}</view>
                    </view>
                    <view class="whole_list_flex">
                        <view class="whole_list_footer">
                            <view class="whole_list_names">客户量</view>
                            <view class="whole_list_num">{{ item.khzl_num }}</view>
                        </view>
                        <view class="whole_list_footer">
                            <view class="whole_list_names">认领</view>
                            <view class="whole_list_num">{{ item.rl_num }}</view>
                        </view>
                        <view class="whole_list_footer">
                            <view class="whole_list_names">录入</view>
                            <view class="whole_list_num">{{ item.lr_num }}</view>
                        </view>
                        <view class="whole_list_footer">
                            <view class="whole_list_names">跟进</view>
                            <view class="whole_list_num">{{ item.gj_num }}</view>
                        </view>
                    </view>

                </view>
            </view>
           </view>
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {
            navIndex: 0, // 本周
            navs: [
                { id: 1, name: '全部' },
                { id: 2, name: "今天" },
                { id: 3, name: "昨天" },
                { id: 4, name: "本周" },
                { id: 5, name: "上周" },
                { id: 6, name: "本月" },
                { id: 7, name: "上月" },
            ],
            proportionList: {
                page: 1,
                per_page: 10,
                date_type: '1'
            },
            censusList: [],
            dataList: [],
            names: '',
            icon: [
                { id: 1, url: '../static/icon/index/客户总量.png' },
                { id: 2, url: '../static/icon/index/有效客户.png' },
                { id: 3, url: '../static/icon/index/A级客户.png' },
                { id: 4, url: '../static/icon/index/B级客户.png' },
                { id: 5, url: '../static/icon/index/C级客户.png' },
            ]
        };
    },
    watch: {
        navIndex: {
            handler(nval) {
                // console.log(nval+1,'tepy');
                this.proportionList.date_type = nval + 1
                this.getworkList()
            },
            immediate: true
        }
    },
    created() {
        uni.showLoading({
            title: "加载中",
        })
        //  this.getworkList()
    },
    methods: {
        // tab互斥效果
        changeAct(item) {
            // console.log(item.id);
            // 激活样式是当前点击的对应下标--list中对应id
            this.act = item.id;
        },
        scroll: function (e) {
            // console.log(e)
            // this.old.scrollTop = e.detail.scrollTop
        },
        checkIndex(index) {
            // console.log(index, '000')
            this.navIndex = index;
        },
        getworkList() {
            this.$ajax.get('/admin/crm/operate/search_new2', this.proportionList, (res) => {
                // console.log(res.data.census, '比例');
                console.log(res.data.data, 'data');
                // console.log(res.data.header, 'header');
                if (res.statusCode === 200) {
                    this.censusList = res.data.census

                    this.dataList = res.data.data
                    console.log(this.censusList, 'push');
                    uni.hideLoading()
                } else {
                    uni.hideLoading()
                }
            },
                () => {
                    uni.hideLoading()
                }
            )
        },
    },
};
</script>
<style lang="scss">
.trantions_top{
    height: 100%;
}
.whole_shu {
    color: rgba(41, 44, 57, 0.70);
    font-size: 24rpx;
    font-weight: 400;
    line-height: 28rpx;

}

.whole_bai {
    width: 70%;
    height: 32rpx;
    line-height: 32rpx;
    color: rgba(41, 44, 57, 0.40);
}

.whole_input_left_name {
    color: rgba(41, 44, 57, 0.70);
    font-size: 28rpx;
    font-weight: 400;
    line-height: 32rpx;
    margin-right: 16rpx;
}

.whole_input_left {
    display: flex;
    flex-direction: row;

}

.whole_con {
    padding: 32rpx;
}

.names {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
    text-align: center;
    line-height: 48rpx;
    color: #fff;
    font-size: 20rpx;
    background: #488AF6;
}

.whole_list_flex {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.whole_list_num {
    color: #000;
    margin-top: 16rpx;
    font-size: 32rpx;
    text-align: center;
    font-weight: 500;
}

.whole_list_names {
    color: rgba(41, 44, 57, 0.40);
    font-size: 24rpx;
    font-weight: 400;
}

.whole_list_department {
    padding: 8rpx 16rpx;
    background: rgba(72, 138, 246, 0.20);
    color: #488AF6;
    text-align: center;
    border-radius: 8rpx;
    font-size: 24rpx;
    font-weight: 400;
}

.whole_list {
    width: 686rpx;
    padding: 32rpx;
    margin: 32rpx 0 0;
    background: #fff;
    border-radius: 16rpx;
}

.whole_list_name {
    color: #292C39;
    font-size: 32rpx;
    font-weight: 500;
    margin: 0 24rpx;
}

.whole_list_header {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
    margin-bottom: 32rpx;
}

.whole_input {
    width: 100%;
    // height: 80rpx;
    padding: 24rpx;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    // margin: 0 16rpx;
    background-color: #fff;
    border-radius: 16rpx;
}

.whole_middle {
    color: rgba(41, 44, 57, 0.70);
    font-size: 28rpx;
    font-weight: 400;
    line-height: 36rpx;
    margin: 32rpx 0;
}

.proportion_whole {
    width: 996rpx;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}

page {
    background: #F6F6F6;
}

.proportion_foot_right {
    color: #292C39;
    font-size: 24rpx;
    font-weight: 400;
}

.foot {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
}

.proportion_foot_left {
    color: rgba(41, 44, 57, 0.70);
    font-size: 22rpx;
    font-weight: 400;
    line-height: 32rpx;
}

.middle {
    color: #292C39;
    font-size: 40rpx;
    font-weight: 500;
    margin: 28rpx 0;
}

.proportion {
    padding: 24rpx;
    width: 300rpx;
    background: #fff;
    border-radius: 16rpx;
    margin-right: 32rpx;
    margin-top: 32rpx;

    &.proportion:last-child {
        margin-right: 0;
    }
}

.name {
    margin-left: 16rpx;
    color: rgba(41, 44, 57, 0.70);
    font-size: 24rpx;
    font-weight: 400;
}

.header {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
}

.whole {
    width: 100%;
    height: 286rpx;
    background: linear-gradient(180deg, rgba(99, 165, 255, 0.40) 0%, rgba(255, 255, 255, 0.00) 100%);
}

.weektop {
    // margin-left: 10rpx;
    // width: 25%;
    margin-top: 30rpx;
    white-space: nowrap;
    text-align: center;
}

.weekbili {
    white-space: nowrap;
}

// 本周
.weeked {
    box-sizing: border-box;
    padding: 14rpx 24rpx;
    border-radius: 16rpx;
    margin-right: 24rpx;
    font-size: 24rpx;
    color: rgba(41, 44, 57, 0.40);
    background: #f8f8f8;
    display: inline-block;

    /* 必要，导航栏才能横向*/
    &.weeked:last-child {
        margin-right: 0;
    }
}

.activite {
    box-sizing: border-box;
    padding: 14rpx 24rpx;
    border-radius: 16rpx;
    color: #fff;
    font-size: 24rpx;
    background: #488AF6
}</style>