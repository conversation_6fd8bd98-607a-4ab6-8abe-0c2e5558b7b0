<template>
  <view class="edit">
    <view class="user-box row">
      <view class="input-box row">
        <view class="right">姓名</view>
        <view class="row input-value">
          <input
            type="text"
            class="uni-input"
            placeholder-style="color:#333"
            placeholder="请输入姓名"
            v-model.trim="form_info.user_name"
          />
        </view>
      </view>
    </view>
    <view class="user-box row">
      <view class="input-box row">
        <view class="right">手机号</view>
        <view class="row input-value">
          <input
            type="text"
            class="uni-input"
            maxlength="11"
            placeholder-style="color:#333"
            placeholder="请输入手机号"
            v-model.trim="form_info.phone"
          />
        </view>
      </view>
    </view>

    <view class="user-box row" v-show="needPhoneVerifyCode">
      <tPhoneVerifyCode label="手机验证码" type="number" v-model.trim="form_info.captcha" :phone-number="form_info.phone" api="/qywx/common/send_sms_bind"></tPhoneVerifyCode>
    </view>

    

    <view class="user-box row">
      <tPicker label="部门" :disabled="departmentSelectAbled" placeholder="请选择部门" v-model="form_info.department_id" :datas="memberList" :map="{label:'name',value:'id',children:'subs'}" class="t-picker"></tPicker>
    </view>

    <!-- <view class="user-box row" @click="showTree">
      <view class="input-box row">
        <view class="right">选择部门</view>
        <view class="row input-value">
          <input
            type="text"
            class="uni-input"
            placeholder-style="color:#333"
            disabled
            placeholder="请选择"
            v-model="department_name"
          />
        </view>
      </view>
    </view> -->
    <!-- <view
      class="user-box row"
      @click="showroleTree"
      v-if="userInfo.roles && userInfo.roles.length >= 0 && userInfo.roles[0].name !== '站长'"
    >
      <view class="input-box row">
        <view class="right">选择角色</view>
        <view class="row input-value">
          <input
            type="text"
            class="uni-input"
            placeholder-style="color:#333"
            disabled
            placeholder="请选择"
            v-model="roles_name"
          />
        </view>
      </view>
    </view> -->
    <button @click="onSubmit" class="btn" :loading="submiting">确认修改</button>
    <peng-tree
      ref="pengTree"
      :range="memberList"
      idKey="id"
      nameKey="name"
      allKey="subs"
      :multiple="false"
      :cascade="false"
      :selectParent="false"
      :foldAll="false"
      confirmColor="#007aff"
      cancelColor="#757575"
      title="部门"
      titleColor="#757575"
      @confirm="treeConfirm"
    >
    </peng-tree>
    <peng-tree
      ref="pengRoleTree"
      :range="roles_list"
      idKey="id"
      nameKey="name"
      allKey="subs"
      :cascade="false"
      :selectParent="false"
      :foldAll="false"
      confirmColor="#007aff"
      cancelColor="#757575"
      title="角色"
      titleColor="#757575"
      @confirm="treeRoleConfirm"
    >
    </peng-tree>
  </view>
</template>

<script>
import tPicker from '@/components/tplus/tPicker';
import tPhoneVerifyCode from '@/components/tplus/tPhoneVerifyCode';
import pengTree from "./components/my_tree/components/peng-tree/peng-tree.vue";
export default {
  components: {
    pengTree, tPicker, tPhoneVerifyCode
  },
  data () {
    return {
      form_info: {
        user_name: "",
        phone: "",
        captcha: '',
        department_id: []
      },
      memberList: [], //部门列表
      roles_list: [],
      department_name: "", //部门名称
      roles_name: "",
      userInfo: {},
      submiting: false,
      needPhoneVerifyCode: false,   //需要手机验证码
      departmentSelectAbled: true
    };
  },
  onShow () { },
  onLoad (options) {
    //设置表单默认值
    this.userInfo = uni.getStorageSync("userInfo") ? JSON.parse(uni.getStorageSync("userInfo")) : {}
    this.form_info.user_name = this.userInfo?.user_name ?? '';
    this.form_info.phone = this.userInfo?.phone ?? '';
    if(this.userInfo?.all_department_id){
      this.form_info.department_id = String(this.userInfo.all_department_id).split(',').map(e => e*1).filter(e => e);
    }
    this.departmentSelectAbled = this.form_info.department_id.length > 0;

    this.getDepartment();
  },
  watch: {
    'form_info.phone': {
      handler(){
        if(this.needPhoneVerifyCode){
          this.needPhoneVerifyCode = false;
          this.form_info.captcha = '';
        }
      }
    }
  },
  methods: {
    getDepartment () {
      uni.showLoading({
        title: '加载中'
      });
      this.$ajax.get("/qywx/common/departments/search", {}, (res) => {
        uni.hideLoading();
        if (res.statusCode === 200) {
          this.memberList = res.data;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      },er=>{
        uni.hideLoading();
      });
    },
    getRolesData () {
      this.$ajax.get("/qywx/common/role/search", { per_page: 100 }, (res) => {
        if (res.statusCode === 200) {
          this.roles_list = res.data.data.filter(
            (item) => item.name !== "站长"
          );
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    onSubmit () {
      // ||
      // (!this.form_info.role_names && ((this.userInfo.roles && this.userInfo.roles.length > 0 && this.userInfo.roles[0].name !== '站长')) || (this.userInfo.roles && this.userInfo.roles.length == 0))
      if (this.form_info.user_name === '') {
        uni.showToast({
          title: "请填写姓名",
          icon: "none",
        });
        return;
      }
      if (this.form_info.phone === '') {
        uni.showToast({
          title: "请填写手机号",
          icon: "none",
        });
        return;
      }
      if(this.needPhoneVerifyCode){
        if (this.form_info.captcha === '') {
          uni.showToast({
            title: "请填写手机验证码",
            icon: "none",
          });
          return;
        }
      }
      if(this.submiting){
        return;
      }
      const params = {...this.form_info};
      if(!this.needPhoneVerifyCode){
        delete params.captcha;
      }
      params.department_id = params.department_id.join(',');
      try{
        this.submiting = true;
        this.$ajax.post("/qywx/common/up_info_bind", params, (res) => {
          this.submiting = false;
          if (res.statusCode === 200) {
            const code = res?.data?.code;
            if(code == 1000){
              //存在相同手机号，需要合并账号，此时显示手机验证码
              uni.showModal({
                content: res.data?.msg || '该手机号已被使用，继续操作将会合并两个账号',
                success: (res) => {
                  if (res.confirm) {
                    this.needPhoneVerifyCode = true;      
                  }
                }
              });
            }else{
              uni.showToast({
                  title: res?.data?.msg || "保存成功"
              });
              //账号已合并，设置新token
              const newToken = res?.data?.token_new;
              if(newToken){
                const website_id = this.$store.state.website_id;
                uni.setStorageSync("token"+website_id, newToken);
                uni.setStorageSync("wxwork_token", newToken);
              }
              this.$store.commit('setNeedRefreshWhenShow', true);
              setTimeout(()=>{
                this.$navigateBack();
              }, 1500)
            }
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        }, (err) => {
          this.submiting = false;
        });
      }catch(e){
        console.error(e);
        this.submiting = false;
      }
    },
    showTree () {
      //打开选择器
      this.$refs.pengTree._show();
      //关闭选择器
      //this.$refs.pengTree._hide();
    },
    showroleTree () {
      if (this.userInfo.roles && this.userInfo.roles.length >= 0 && this.userInfo.roles[0].name === '站长') {
        uni.showToast({
          title: '站长不可以选择角色',
          icon: 'none'
        })
        return
      }
      this.$refs.pengRoleTree._show();
    },
    treeConfirm (e) {
      /* var val = e[0];
      this.form_info.department_id = val.id + "";
      this.department_name = val.name; */

      this.$refs.pengTree.getCheckedDetails();

    },
    treeRoleConfirm (e) {
      var names = e.map((item) => {
        return item.name;
      });
      this.form_info.role_names = names;
      this.roles_name = names.join(",");
    },
  },
};
</script>

<style scoped lang="scss">
page {
  background: #f5f5f5;
}
.uni-input {
  text-align: end;
  margin-right: 24rpx;
}
.edit {
  .user-box {
    background: #fff;
    padding: 24rpx 48rpx;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .right {
      min-width: 112rpx;
      text-align: center;
      color: #666;
      padding: 10rpx;
      font-size: 32rpx;
    }
    .left {
      align-items: center;
    }
    .input-box {
      justify-content: space-between;
      width: 100%;
      align-items: center;
      .input-value {
        align-items: center;
      }
      input {
        margin-left: 40rpx;
        font-size: 28rpx;
      }
    }
  }
}
button::after {
  border: none;
}
.btn {
  margin-top: 40rpx;
  padding: 10rpx;
  align-items: center;
  color: #fff;
  font-size: 34rpx;
  width: 630rpx;
  height: 104rpx;
  box-shadow: 0 2px 10px 0 rgba(1, 116, 255, 0.2);
  background: #0174ff;
  border-radius: 50rpx;
  &:last-child {
    margin-bottom: 80rpx;
  }
}
::v-deep .picker-input-label,
::v-deep .phone-code-label{
  min-width: 56px;
  text-align: center;
  color: #666;
  padding: 5px;
  font-size: 16px;
}
</style>
