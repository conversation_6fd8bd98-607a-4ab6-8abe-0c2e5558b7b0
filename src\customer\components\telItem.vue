<template>
  <view class="share-item">
    <view class="share-left">
      <text class="month">{{ share.call_time | timeFilter('month') }}</text>
      <!-- <text class="time">{{ share.ctime | timeFilter }}</text> -->
    </view>
    <view class="share-right flex-row"  @click='$emit("click",share.id)'>
      <view class="time">
        {{ share.call_time | timeFilter('time') }}
      </view>
      <view class="c">
         <view v-if="share.record_url" class="share-img_list share-voice" @click.prevent.stop="">
        <!-- <slot name="voice" :shareData="share" :voice="share.attached[0]" /> -->
        <view class="voice felx-1" @click="playVoice(share.record_url)">
          <image class="play_vioce_icon" :src="(voice_playing?'/static/icon/voice/play_voice.gif':'/static/icon/voice/voice_icon.png') | imageFilter('w_320')"></image>
          <text>{{share.duration}}</text>
        </view>
        <!-- (voice_playing
                    ? '/static/icon/voice/play_voice.gif'
                    : '/static/icon/voice/voice_icon.png') | imageFilter('w_80') -->
      </view>
      <view v-if="share.content" class="share-content" @click.prevent.stop="$emit('click', share.id)" >{{ share.content }}</view>
      </view>
     
    </view>
  </view>
</template>

<script>
import myIcon from '@/components/my-icon.vue'
import { formatImg } from '@/page_outside/tools/index'
export default {
  components: {
    myIcon
  },
  props: {
    share: {
      type: Object,
      default: () => {
        return {}
      }
    },
    voice_playing: {
      type: Boolean,
      default: false
    },
    show_date: {
      type: Boolean,
      default: true
    },
    show_bottom_line: {
      type: Boolean,
      default: true
    },
    mode:{
      type: String,
      default: "default"
    }
  },
  data() {
    return {
      innerAudioContext: uni.createInnerAudioContext()
    }
  },
  watch: {
    voice_playing(val){
      console.log('监听播放状态：', val)
      if(!val){
        this.stopPlay()
      }
    }
  },
  created(){
    this.innerAudioContext.onPlay(()=>{
      this.$emit('voicePlay')
      // this.play_voice_index = this.current_voice_index
    })
    this.innerAudioContext.onStop(()=>{
      this.$emit('voiceStop')
      // console.log("暂停了")
      // this.play_voice_index = -1
    })
    this.innerAudioContext.onEnded(()=>{
      this.$emit('voiceEnded')
      // this.play_voice_index = -1
    })
    // 监听语音播放失败事件
    this.innerAudioContext.onError(()=>{
      uni.showToast({
          title:'播放失败，请重试',
          icon:'none'
      })
      this.$emit('voiceError')
    })
  },
  filters: {
    // 获取时间的月和日
    timeFilter(val, type) {
      let res ='',time=''
      if (!val) {
        return ''
      }
      let value =''
      value = val.split(" ")
      if(value.length>0){
        res= value[0]
      }
      if(value.length>1) {
        time = value[1]
      }
      if (type=='month') {
        return res
      }else {
        return time
      }
      // let time = new Date(val.replace(/-/g,'/'))
      // let month = time.getMonth()
      // let day = time.getDate()
      // if (type === 'month') {
      //   return month + 1 + '月'
      // }
      // return (month + 1 < 10 ? '0' + (month + 1) : month + 1) + '-' + (day < 10 ? '0' + day : day)
    },
    imageFilter(img,param=""){
				return formatImg(img,param)
		}
  },
  methods: {
    prevImg(index,img_list){
      let img_arr = img_list.map(item=>formatImg(item.path,"w_800"))
      uni.previewImage({
        urls: img_arr,
        current: index
      })
    },
    playVoice(src){
      if(!src){
          uni.showToast({
              title: '暂无录音文件',
              icon: 'none'
          })
          return
      }
      this.$emit('clickvoice', src)
      if(this.voice_playing){
        this.stopPlay()
      }else{
        setTimeout(()=>{
          this.innerAudioContext.src = src
          this.innerAudioContext.play()
        }, 300)
      }
    },
    stopPlay(){
      this.innerAudioContext.stop()
    },
    toVideo(){
      // this.$navigateTo(`/vr/pre_comm_video?video_id=${this.share.attached[0].id}&id=${this.share.id}`)
    },
   
  },
  beforeDestroy(){
    if(this.voice_playing){
      this.stopPlay()
    }
  }
}
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}

.share-item {
  // margin-bottom: 28rpx;
  .share-left {
    margin-bottom: 28rpx;
    // margin-right: 15rpx;
    // min-width: 70rpx;
    .month {
      font-size: 28rpx;
      // font-weight: bold;
      color: #333;
    }
    .time {
      font-size: 22rpx;
      color: #999;
    }
  }
  .share-right {
    flex: 1;
    overflow: hidden;
    background: #fff;
    border-radius:10rpx;
    // margin-top:28rpx;
    padding: 28rpx;
    font-size: 28rpx;
    color: #828488;
    line-height: 40rpx;
    // &.bottom-line{
    //   padding-bottom: 48rpx;
    // }
    .time {
      margin-right: 10rpx;
    }
  }
  .name-row {
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    .share-build_name {
      font-size: 24rpx;
      color: $uni-color-primary;
    }
    .share-zan {
      align-items: center;
      font-size: 22rpx;
      color: #d8d8d8;
      .zan_num {
        margin-left: 6rpx;
        position: relative;
        top: -3rpx;
      }
    }
  }
  .share-content {
    // margin-top: 15rpx;
    color: #828488;
    white-space:normal;
    display:-webkit-box;
    -webkit-box-orient:vertical;
    -webkit-line-clamp:3;
    overflow:hidden;
  }
  .share-img_list {
    flex-direction: row;
    justify-content: space-between;
    flex-wrap: wrap;
    position: relative;
    &.small{
      padding-right: 52rpx;
    }
    &.share-video-list{
      max-height: 600rpx;
      max-width: 490rpx;
      overflow: hidden;
      &.small{
        width: 176rpx;
        height: 176rpx;
        padding: 0;
        .video-icon{
          width: 60rpx;
          height: 60rpx;
        }
      }
    }
     &.share-voice{
      width: 490rpx;
      margin-bottom: 15rpx;
      image{
        width: 40rpx;
        height: 40rpx;
      }
    }
    
    .share-img_item {
      width: 176rpx;
      height: 176rpx;
      position: relative;
      &.mb24{
        margin-bottom: 24rpx;
      }
      &.place {
        height: 0;
        margin: 0;
      }
      image {
        width: 100%;
        height: 100%;
        
      }
    }
    .share-img_list_alone{
      width: 68%;
      max-height: 600upx;
      position: relative;
      overflow: hidden;
    }
    .share-img_alone {
      width: 100%;
      // height: 100%;
      // margin-bottom: 24rpx;
      overflow: unset;
      &.small{
        width: 176rpx;
        height: 176rpx;
        object-fit: cover;
        position: relative;
      }
    }
    .video-icon{
      width: 16vw;
      height: 0;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0);
    }
  }
}


.voice{
  flex-direction: row;
  align-items: center;
  // padding: 20rpx 24rpx;
  border-radius: 4rpx;
  width: 300rpx;
  box-sizing: border-box;
  background-color: #2d84fb;
  color: #333;
  .play_vioce_icon{
    margin-right: 16rpx;
    width: 40rpx;
    height: 40rpx;
  }
  text {
    color: #fff;
  }
}

.house_desc{
    margin-top: 4rpx;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
}

.house_type{
  margin-top: 24rpx;
  padding: 24rpx;
  border-radius: 8rpx;
  background-color: #f8f8f8;
  image{
    width: 128rpx;
    height: 128rpx;
    margin-right: 24rpx;
    border-radius: 8rpx;
  }
  .house_info{
    .title{
      margin-bottom: 24rpx;
       font-size: 32rpx;
       color: #333;
    }
    .desc{
      font-size: 22rpx;
      color: #666;
      .huxing_desc{
        margin-right: 16rpx;
      }
    }
  }
}
</style>
