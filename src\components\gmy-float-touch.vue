<template name="gmy-float-touch">
  <view>
    <view @touchmove.stop.prevent="doNothing">
      <view
        class="box"
        @click="show"
        style="z-index: 999;"
        :style="{ top: top + 'px', right: right + 'px' }"
        @touchmove="touchmove"
        @touchend="touchend"
      >
        <image
          src="https://img.tfcs.cn/static/img/baobei-caidan.png"
          mode="aspectFill"
        ></image>
        <view class="badge-box" v-if="parseInt(unread_msg) > 0 && !isShow">{{
          unread_msg
        }}</view>
      </view>
      <view
        class="menu  "
        :class="{
          menu0: !isShow && isleft,
          menu1: isShow && isleft,
          menu1R: isShow && !isleft,
          menu0R: !isShow && !isleft,
        }"
        :style="{ top: top - 75 + 'px' }"
        v-if="isShow"
        @tap="menuClick(1)"
      >
        <image :src="imgLists[0]" mode=""></image>
      </view>
      <view
        class="menu "
        v-show="isShow"
        :class="{
          menu0: !isShow && isleft,
          menu2: isShow && isleft,
          menu2R: isShow && !isleft,
          menu0R: !isShow && !isleft,
        }"
        :style="{ top: top - 53 + 'px' }"
        @tap="menuClick(2)"
      >
        <image :src="imgLists[1]" mode="aspectFill"></image>
      </view>
      <view
        class="menu "
        v-show="isShow"
        :class="{
          menu0: !isShow && isleft,
          menu3: isShow && isleft,
          menu3R: isShow && !isleft,
          menu0R: !isShow && !isleft,
        }"
        :style="{ top: top + 'px' }"
        @tap="menuClick(3)"
      >
        <image :src="imgLists[2]" mode="aspectFill"></image>
        <view class="badge" v-if="parseInt(unread_msg) > 0">{{
          unread_msg
        }}</view>
      </view>
      <view
        class="menu "
        v-show="isShow"
        :class="{
          menu0: !isShow && isleft,
          menu4: isShow && isleft,
          menu4R: isShow && !isleft,
          menu0R: !isShow && !isleft,
        }"
        :style="{ top: top + 53 + 'px' }"
        @tap="menuClick(4)"
      >
        <image :src="imgLists[3]" mode="aspectFill"></image>
      </view>
      <view
        class="menu "
        v-show="isShow"
        :class="{
          menu0: !isShow && isleft,
          menu5: isShow && isleft,
          menu5R: isShow && !isleft,
          menu0R: !isShow && !isleft,
        }"
        :style="{ top: top + 75 + 'px' }"
        @tap="menuClick(5)"
      >
        <image :src="imgLists[4]" mode="aspectFill"></image>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from "vuex";
export default {
  computed: {
    ...mapState(["unread_msg"]),
  },
  name: "gmy-float-touch",
  data() {
    return {
      isShow: false,
      top: 350,
      left: 3,
      right: 3,
      isleft: true,
      animationData: "",
    };
  },
  props: {
    imgLists: {
      type: Array,
      default: () => [
        "https://img.tfcs.cn/static/img/baobei-shouye.png",
        "https://img.tfcs.cn/static/img/baobei-xiangmu.png",
        "https://img.tfcs.cn/static/img/baobei-xiaoxi.png",
        "https://img.tfcs.cn/static/img/baobei-wode.png",
        "https://img.tfcs.cn/static/img/baobei-bbkehu.png",
      ],
    },
  },
  methods: {
    doNothing: function() {},
    show: function() {
      this.isShow = !this.isShow;
    },
    touchmove: function(e) {
      if (this.isShow) {
        return;
      }
      var position = e.touches[0];
      var wHeight = this.getSystemInfo("H");
      var wWidth = this.getSystemInfo("W");
      if (position.pageY > 100 && position.pageY + 100 < wHeight) {
        this.top = position.pageY - 20;
      }
      this.right = wWidth - position.pageX;
    },
    touchend: function(e) {
      var wWidth = this.getSystemInfo("W");
      const pageX = e.changedTouches[0].clientX;
      if (pageX > wWidth * 0.5) {
        this.isleft = false;
        this.right = 3;
      } else {
        this.isleft = true;
        this.right = wWidth - 50;
      }
    },
    menuClick: function(index) {
      switch (index) {
        case 1:
          uni.switchTab({
            url: "/index/index",
          });
          break;
        case 2:
          uni.switchTab({
            url: "/index/project",
          });
          break;
        case 3:
          uni.switchTab({
            url: "/index/message",
          });
          break;
        case 4:
          uni.switchTab({
            url: "/index/mine",
          });
          break;
        case 5:
          this.$navigateTo("/report/report_client?currentTel=1");
          break;
      }
      this.$emit("menuClick", index);
    },
    getSystemInfo: function(type) {
      var result;
      const screen = uni.getSystemInfoSync();
      const wHeight = screen.windowHeight;
      const wWidth = screen.windowWidth;
      switch (type) {
        case "H":
          result = wHeight;
          break;
        case "W":
          result = wWidth;
          break;
        default:
          result = "Err";
      }
      return result;
    },
  },
};
</script>

<style lang="scss" scoped>
image {
  width: 40upx;
  height: 40upx;
  z-index: 99;
  margin: 20upx;
}
.box {
  position: fixed;
  right: 5upx;
  top: 70%;
  width: 80upx;
  height: 80upx;
  border-radius: 50%;
  background-color: rgba($color: #000000, $alpha: 0.5);
  .badge-box {
    right: 0;
    width: 40rpx;
    height: 40rpx;
    text-align: center;
    line-height: 40rpx;
    border-radius: 50%;
    bottom: 0;
    position: absolute;
    color: #fff;
    background: #dd524d;
  }
}
.menu {
  color: white;
  position: fixed;
  width: 80upx;
  height: 80upx;
  border-radius: 50%;
  background-color: rgba($color: #000000, $alpha: 0.5);
}
.menu0 {
  left: 5upx;
  opacity: 0;
  transition: top 0.2s, left 0.2s, opacity 0.2s;
}
.menu1 {
  left: 20upx;
  transition: top 0.2s, left 0.2s, opacity 0.2s;
}
.menu2 {
  left: 106upx;
  transition: top 0.2s, left 0.2s, opacity 0.2s;
}
.menu3 {
  left: 145upx;
  transition: top 0.2s, left 0.2s, opacity 0.2s;
}
.menu4 {
  left: 106upx;
  transition: top 0.2s, left 0.2s, opacity 0.2s;
}
.menu5 {
  left: 20upx;
  transition: top 0.2s, left 0.2s, opacity 0.2s;
}
.menu0R {
  right: 5upx;
  opacity: 0;
  transition: top 0.2s, right 0.2s, opacity 0.2s;
}
.menu1R {
  right: 20upx;
  transition: top 0.2s, right 0.2s, opacity 0.2s;
}
.menu2R {
  right: 106upx;
  transition: top 0.2s, right 0.2s, opacity 0.2s;
}
.menu3R {
  right: 145upx;
  transition: top 0.2s, right 0.2s, opacity 0.2s;
  .badge {
    position: absolute;
    width: 40rpx;
    height: 40rpx;
    background: #0174ff;
    border-radius: 50%;
    text-align: center;
    line-height: 40rpx;
    left: -10rpx;
    top: -4rpx;
  }
}
.menu4R {
  right: 106upx;
  transition: top 0.2s, right 0.2s, opacity 0.2s;
}
.menu5R {
  right: 20upx;
  transition: top 0.2s, right 0.2s, opacity 0.2s;
}
</style>
