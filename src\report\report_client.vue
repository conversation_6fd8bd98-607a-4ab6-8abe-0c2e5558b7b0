<template>
  <view class="list">
    <view class="report-build" v-if="isCheckProject">
      <view class="title-bar">
        <view class="search-tab">
          <my-search
            width="287px"
            placeholder="请输入楼盘名称"
            @confirm="confirm"
            @input="handleInput"
          >
            <template v-slot:left>
              <my-icon type="ic_sousuo3x1" color="#999"></my-icon>
            </template>
            <template v-slot:right>
              <text class="xinzeng" @click="goHome">首页</text>
            </template>
          </my-search>
        </view>
        <view
          :key="index"
          v-for="(item, index) in build_list"
          class="build-item"
          :class="{
            checkon: build_arr.map((item) => item.project_id).includes(item.project_id),
          }"
        >
          <view class="check" @click="checkOn(index, item)">
            <view class="ctn row">
              <view class="ctn-box row">
                <view class="left row">
                  <view class="left-label">{{ item.build_name }}</view>
                  <view class="right-label">{{ item.brokerage_rule }}</view>
                </view>
                <view class="right row">
                  <view class="left-label"
                    >{{ item.b_region_0_name }} {{ item.b_region_1_name }}</view
                  >
                </view>
                <view class="right row">
                  <view class="left-label">{{ item.build_avg_price }}元/平</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <load-more :status="load_status"></load-more>
      <view class="bottom row">
        <view class="left">
          <text>已选择{{ build_arr.length }}个</text>
          <text>最多选择{{ webinfo.max_select_project_total }}个</text>
        </view>
        <view class="btn" @click="onSubmitBuild">{{ btn_text }}</view>
      </view>
    </view>
    <view class="content-build" v-else>
      <view class="content" v-if="is_project_build">
        <view
          class="build row"
          @click="$navigateTo(`/build/detail?buildID=${build_list_obj.build_id}`)"
        >
          <view class="left"
            ><image
              mode="aspectFill"
              :src="
                build_list_obj.build_img
                  ? build_list_obj.build_img
                  : 'https://img.tfcs.cn/static/img/que.jpg'
              "
            ></image>
          </view>
          <view class="right row">
            <view class="top"
              >楼盘名称：{{ build_list_obj.build_name || build_list_obj.label }}</view
            >
            <view class="bottom-rules"
              >佣金规则：<text>{{
                build_list_obj.brokerage_rule || build_list_obj.store_brokerage_rule
              }}</text></view
            >
          </view>
        </view>
      </view>
      <view class="content row" v-else @click="selectProject">
        <view class="left">推荐项目</view>
        <view class="time-check row">
          <input type="text" disabled="true" placeholder="请选择" />
          <myIcon class="icon" type="you" size="20rpx" color="#bbb"></myIcon>
        </view>
      </view>
      <view class="backfff" v-if="build_arr.length > 0 && !is_project_build">
        <view class="check-project" v-for="(item, index) in build_arr" :key="index">
          <view class="left-box row">
            <view class="row row-box">
              <view class="left">{{ item.build_name }}</view>
              <view class="left-box-label" v-if="item.full_num_reported === 0">隐</view>
            </view>
            <view class="right" @click="deleteCheck(index, item)">-</view>
          </view>
        </view>
      </view>
      <view
        class="content row"
        v-if="is_identify && build_arr.length === 0"
        @click="box_show = !box_show"
      >
        <view class="left">智能识别（粘贴复制的客户信息）</view>
        <myIcon
          class="icon"
          :type="box_show ? 'xiala' : 'shangla'"
          size="20rpx"
          color="#bbb"
        ></myIcon>
      </view>
      <view class="intelligent" v-show="box_show && is_identify && build_arr.length === 0">
        <textarea placeholder="粘贴后点击智能识别" v-model="intelligent_ctn"></textarea>
        <view class="row identify-box-row">
          <view class="use" @click="$navigateTo('/commission/help_desc?id=7')"> 如何使用？ </view>
          <view class="identify-box row">
            <view class="clear btn" @click="intelligent_ctn = ''"> 清空</view>
            <view class="identify btn" @click="distinguishCtn">智能识别</view>
          </view>
        </view>
      </view>
      <view class="content row">
        <view class="left">客户姓名</view>
        <input
          type="text"
          placeholder-class="input-phaceholder"
          class="uni-input"
          placeholder="请输入客户姓名"
          v-model="customer_name"
        />
      </view>
      <view class="content row">
        <view class="left">客户性别</view>
        <view class="sex-box">
          <radio-group @change="radioChange" class="row uni-group">
            <label class="row sex-box-row" v-for="item in sex_list" :key="item.value">
              <radio :value="item.value" :checked="item.value === current_sex"></radio>
              <view>{{ item.description }}</view>
            </label>
          </radio-group>
        </view>
      </view>
      <view
        class="content row"
        v-if="build_list_obj.reported_go_with === 2 || webinfo.reported_go_with === 2"
      >
        <view class="left">是否陪同</view>
        <view class="sex-box">
          <radio-group @change="radioWith" class="row uni-group">
            <label class="row sex-box-row" v-for="item in is_check" :key="item.value">
              <radio :value="item.value"></radio>
              <view>{{ item.description }}</view>
            </label>
          </radio-group>
        </view>
      </view>
      <!-- 手机号 -->
      <view class="content row" style="margin-bottom: 0">
        <view class="left row" :style="{ fontSize: current_tel == 0 ? '26rpx' : '' }"
          >手机号码
          {{
            current_tel == 0 && is_num_mode
              ? '（前三后五）'
              : current_tel == 0
              ? '（前三后四）'
              : ''
          }}
        </view>
        <text v-if="tips_full && build_arr.length > 1" class="tips_full">
          您报备的项目包含隐号项目，系统将自动为您处理手机号码
        </text>
        <view class="sex-box" v-if="isFull && is_full_show == 0">
          <radio-group @change="radioChangeTel" class="row uni-group">
            <label class="row sex-box-row" v-for="item in full_number_list" :key="item.value">
              <radio :value="item.value" :checked="item.value === current_tel"></radio>
              <view>{{ item.description }}</view>
            </label>
          </radio-group>
        </view>
      </view>
      <view class="input-phone row" v-if="current_tel == 1">
        <text>+86</text>
        <input
          type="number"
          class="input-tel"
          maxlength="11"
          v-model="customer_phone"
          style="margin-left: 20rpx"
        />
        <myIcon class="icon-input" type="open" size="32rpx" color="#fff" @click="addInput"></myIcon>
      </view>
      <view class="input-phone row" v-if="current_tel == 0">
        <text>+86</text>
        <input
          type="number"
          class="input-tel"
          maxlength="3"
          v-model="phone.start"
          style="margin-left: 20rpx"
        />
        <text style="margin: 0 30rpx"> {{ is_num_mode ? '***' : '****' }}</text>
        <input
          type="number"
          class="input-tel"
          :maxlength="is_num_mode ? 5 : 4"
          v-model="phone.end"
        />
        <myIcon class="icon-input" type="open" size="32rpx" color="#fff" @click="addInput"></myIcon>
      </view>
      <!-- <view
        class="content row"
        v-if="other_phone_list.length > 0"
        style="padding:8rpx 48rpx"
      >
        <view class="row" style="color:#0174ff;">系统将自动为您处理手机号</view>
      </view> -->
      <view v-for="(item, index) in other_phone_list" :key="index">
        <view class="input-phone row" v-if="item.other">
          <text class="label-name">称呼</text>
          <input type="text" class="label-input" v-model="item.name" />
          <text class="label-name">性别</text>
          <input
            type="text"
            class="label-input"
            v-model="item.sex"
            placeholder="先生/女士"
            placeholder-style="font-size:20rpx"
            @input="onSexinput"
          />
          <text class="label-name">电话</text>
          <input
            v-if="current_tel == 0"
            type="number"
            v-model="item.start_phone"
            class="label-input label-name-phone"
            style="width: 80rpx"
            maxlength="3"
          />
          <text v-if="current_tel == 0">{{ is_num_mode ? '***' : '****' }}</text>
          <input
            v-if="current_tel == 0"
            type="number"
            v-model="item.end_phone"
            :maxlength="is_num_mode ? 5 : 4"
            class="label-input label-name-phone"
            style="width: 100rpx"
          />
          <input
            v-if="current_tel == 1"
            type="number"
            v-model="item.phone"
            maxlength="11"
            class="label-input label-name-phone"
          />

          <myIcon
            class="icon-input"
            type="jianshao"
            size="32rpx"
            color="#fff"
            @click="removeInput(item)"
          ></myIcon>
        </view>
      </view>
      <view class="content row" v-if="build_list_obj.reported_id_no_category === 2">
        <view class="left">证件号码</view>
        <input
          type="text"
          placeholder-class="input-phaceholder"
          class="uni-input"
          maxlength="6"
          placeholder="请输入身份证号后六位"
          v-model="form_create.reported.customer_id_no"
        />
      </view>
      <view class="content row" @click="selectTime">
        <view class="left">预计到场时间</view>
        <view class="time-check row">
          <input
            type="text"
            disabled="true"
            v-model="form_create.reported.visit_time"
            class="time-style"
          />
        </view>
      </view>
      <view
        class="backfff"
        style="margin: 0"
        v-if="build_list_obj.reported_intention === 1 && build_type_list.length > 1"
      >
        <view class="content-check">
          <view class="left left-title">客户意向</view>
          <view class="check-box row">
            <text
              class="check-group"
              :class="{
                check_group_on: build_type_checkon.includes(item.value),
              }"
              v-for="item in build_type_list"
              :key="item.id"
              @click="checkGroup(item)"
              >{{ item.description }}</text
            >
          </view>
        </view>
      </view>
      <view class="backfff" style="margin: 0" v-if="build_list_obj.reported_visit === 1">
        <view class="content row">
          <view class="left">来访方式</view>
          <view class="nav">
            <view
              class="nav-item"
              v-for="(nav, index) in visit_category_list"
              :key="index"
              :class="{
                active: nav.value === form_create.reported.visit_category,
              }"
              @click="selectVisitCategory(index)"
              >{{ nav.description }}</view
            >
          </view>
        </view>
        <view class="content row">
          <view class="left">来访人数</view>
          <view class="time-check row">
            <input type="text" v-model="form_create.reported.visit_people" placeholder="请输入" />
          </view>
        </view>
      </view>
      <view class="beizhu">
        <view class="row beizhu-title">
          <view class="title">描述</view>
          <!-- <view class="shibie" @click="onIdentifyRemark">一键识别</view> -->
        </view>
        <textarea
          v-model="form_create.reported.remark"
          class="text-input"
          placeholder="和项目助理留言，其他要求说明！"
          style="height: 200rpx"
          maxlength="190"
        ></textarea>
        <view class="length-box"> {{ remark_lenght }}/190 </view>
      </view>
      <view class="uni-btn-v">
        <button @click="onCheckSubmit">立即提交</button>
      </view>
    </view>
    <!-- 时间选择弹出列表 -->
    <timePicker
      end="2030-12-31 23:59"
      :mode="time_picker"
      ref="picker"
      @confirm="confirmTime"
    ></timePicker>
    <gmyFloatTouch></gmyFloatTouch>
    <myShowmodal
      :show="is_show_modal"
      title="提示"
      confirm-text="我的客户"
      @cancel="bindBtn('cancel')"
      @confirm="bindBtn('confirm')"
    >
      <div class="modal" v-html="is_show_content"></div>
    </myShowmodal>
  </view>
</template>

<script>
import myIcon from "@/components/my-icon";
import loadMore from "@/components/loadMore";
import mySearch from "@/components/my-search";
import timePicker from "@/components/time-picker";
import myShowmodal from "@/components/my-showmodal/neil-modal.vue";
import { mapState, mapActions } from "vuex";
export default {
  components: { myIcon, loadMore, mySearch, timePicker, myShowmodal },
  data () {
    return {
      // 判断报备方式是否一致
      hide_num_mode: "",
      params: {
        page: 1,
        build_name: "",
      },
      load_status: "",
      build_list: [],
      //   存放选择的项目id
      build_arr: [],
      //   存放选择的项目
      btn_text: "返回",
      isCheckProject: false,
      box_show: false, // 智能识别
      intelligent_ctn: "", // 需要识别的内容
      form_create: {
        // 提交客户信息表单
        customer: {
          name: "",
          phone: "",
          sex: "",
        },
        reported: {
          project_id: "",
          remark: "",
          intention_build_category: "",
          // budget: "",
          visit_time: "",
          visit_people: "",
          visit_category: "1",
          customer_attached_phone: "",
          reported_go_with: "0",
          customer_id_no: "", // 身份证号后六位
        },
        // 报备隐/全
        project_data: {
          full_num_reported: "",
          build_name: "",
        },
      },
      current_sex: "", // 性别,
      current_tel: "1", // 手机号类型
      sex_list: [],
      time_picker: "YMDhm", // 时间选择
      remark_lenght: 0, // 备注长度   默认0
      visit_category_list: [], // 来访方式选择
      build_type_list: [], // 选择楼盘类型
      build_type_checkon: [], //楼盘选中数据
      full_number_list: [
        { value: "1", description: "全号" },
        { value: "0", description: "隐号" },
      ],
      phone: {
        // 隐号前三后四
        start: "",
        end: "",
      },
      other_phone_list: [], // 其他手机号添加报备信息
      customer_name: "", //待处理的客户姓名
      customer_phone: "", // 待处理的联系方式
      tips_full: false, // 多选判断是否有隐号
      isFull: true,
      is_project_build: false, // 设置默认标识
      build_list_obj: {},
      build_type_show: false, // 楼盘类型显示选择
      is_identify: true, // 智能识别显示隐藏
      is_full_show: "", //  显示隐/全
      module_msg: "",
      is_check: [
        { value: "0", description: "否" },
        { value: "1", description: "是" },
      ],
      webinfo: "",
      is_show_modal: false,
      is_show_content: "",
      is_num_mode: false, // 前三后五显示
    };
  },
  watch: {
    // 选择的数组长度
    build_arr (new_val, old_val) {
      if (new_val.length === 0) {
        this.btn_text = "返回";
      } else {
        let isHide = this.filterData({ full_num_reported: 1 }, new_val);
        if (isHide.length === 0) {
          // 全部是0，都是隐号
          this.tips_full = false;
          this.isFull = true;
          this.current_tel = "0";
          // 数组长度不小于0说明是前三后五
          //过滤隐号报备且是前三后五
          let isNum = this.filterData(
            { full_num_reported: 0, hide_num_mode: 2 },
            new_val
          );
          if (isNum.length > 0) {
            this.is_num_mode = true;
          } else {
            this.is_num_mode = false;
          }
        } else if (isHide.length === new_val.length) {
          // 长度相等都是全号
          this.isFull = false;
          this.tips_full = false;
        } else if (isHide !== new_val.length) {
          // 不等   => 全号、隐号
          this.isFull = false;
          this.tips_full = true;
        }

        this.btn_text = "确认";
      }
    },
    // 监听备注输入长度
    "form_create.reported.remark" (new_val, old_val) {
      this.remark_lenght = new_val.length;
    },
  },
  onShow () {
    if (!uni.getStorageSync("token" + this.$store.state.website_id)) {
      uni.showModal({
        title: "登录提示",
        content: "当前操作需要登录，是否去登录？",
        success: (res) => {
          if (res.confirm) {
            this.$navigateTo("/user/phone_login");
          }
        },
      });
      return;
    }
    this.getUserInfo({
      success: (res) => {
        var that = this;
        if (res.statusCode === 200) {
          if (!res.data.phone || !res.data.name) {
            uni.showToast({
              title: "请完善信息后报备客户",
              icon: "none",
            });
            setTimeout(() => {
              that.$navigateTo(`/user/user_edit?id=${res.data.id}`);
            }, 1000);
          }
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      },
    });
  },
  computed: {
    ...mapState(["user_info", "siteConfig", "city"]),
  },
  onLoad (options) {
    if (options.id) {
      this.is_full_show = options.full_num_reported;
      this.getBuildInfo(options.id);
    } else {
      this.init();
      this.share = { forward_title: "快速报备客户" };
      this.getWxConfig();
    }
    this.$ajax.get(`/common/website/query/${options.website_id}`, {}, (res) => {
      if (res.statusCode === 200) {
        this.webinfo = res.data;
      } else {
        uni.showToast({
          title: res.data.message || "获取失败",
          icon: "none",
        });
      }
    });
    this.matchTimeFormat(this.webinfo.visit_time_format_category);
  },
  methods: {
    ...mapActions(["getUserInfo"]),

    //@param condition 过滤条件
    //@param data 需要过滤的数据
    filterData (condition, data) {
      return data.filter((item) => {
        return Object.keys(condition).every((key) => {
          return String(item[key])
            .toLowerCase()
            .includes(
              String(condition[key])
                .trim()
                .toLowerCase()
            );
        });
      });
    },
    // 匹配时间格式
    matchTimeFormat (category) {
      switch (category) {
        case "1":
        case "Y-m-d H:i:s":
        case "Y/m/d H:i":
        case "Y年m月d日 H时i分":
          this.time_picker = "YMDhm";
          break;
        case "2":
        case "Y年m月d日":
        case "Y/m/d":
        case "Y-m-d":
          this.time_picker = "YMD";
          break;
        default:
          this.time_picker = "YMDhm";
          break;
      }
      this.form_create.reported.visit_time = this.$getTime(this.time_picker);
    },
    // 搜索
    confirm (e) {
      this.params.page = 1;
      this.params.build_name = e;
      this.getDataList();
    },
    handleInput (e) {
      this.$debounce(this.handleInputDebounce, 500)(e);
    },
    handleInputDebounce (e) {
      if (e === "") {
        this.params.build_name = e;
        this.params.page = 1;
        this.getDataList();
      }
    },
    getBuildInfo (id) {
      this.$ajax.get(`/common/project/query/build/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.is_project_build = true;
          this.is_identify = false;
          this.build_list_obj = res.data;
          this.init();
          this.build_arr.push(res.data);
          if (res.data.full_num_reported === 0) {
            this.current_tel = "0";
          }
          this.matchTimeFormat(res.data.visit_time_format_category);
          this.share = { forward_title: res.data.build_name }; // 默认分享标题
          this.getWxConfig();
        }
      });
    },
    // 字典数据获取
    init () {
      this.$setDictionary((e) => {
        e.find((item) => {
          switch (item.name) {
            case "SEX":
              this.sex_list = item.childs;
              break;
            case "REPORTED_VISIT_CATEGORY":
              this.visit_category_list = item.childs;
              break;
            case "BUILD_CATEGORY":
              if (this.build_list_obj.build_category) {
                let arr = this.build_list_obj.build_category.split(",");
                item.childs.find((item) => {
                  arr.map((item1) => {
                    if (item1 === item.value) {
                      this.build_type_list.push(item);
                    }
                  });
                });
                if (arr.length === 1) {
                  this.form_create.reported.intention_build_category = arr[0];
                }
              }
              // this.build_type_list = item.childs;
              break;
            default:
              break;
          }
        });
      });
    },
    //  返回首页
    goHome () {
      uni.switchTab({
        url: "/",
      });
    },
    radioWith (e) {
      this.form_create.reported.reported_go_with = e.detail.value;
    },
    // 获取项目楼盘数据
    getDataList () {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.build_list = [];
      }
      this.$ajax.get("/client/project/list?type=1", this.params, (res) => {
        if (res.statusCode === 200) {
          this.load_status = "loadend";
          this.build_list = this.build_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        } else {
          uni.showToast({
            title: res.data.message || "获取列表失败",
            icon: "none",
          });
        }
      });
    },
    // 选择项目
    checkOn (index, item) {
      if (this.build_arr.length === 0) {
        this.hide_num_mode = "";
      }
      if (this.hide_num_mode == "") {
        this.hide_num_mode = item.hide_num_mode;
      }
      if (item.hide_num_mode != this.hide_num_mode) {
        uni.showToast({
          title: "报备方式不一致",
          icon: "none",
        });
        return;
      } else {
        let value = this.build_arr
          .map((item) => item.project_id)
          .includes(item.project_id);
        if (!value) {
          this.build_arr.push(item);
        } else {
          this.build_arr = this.build_arr.filter((ele) => {
            return ele.project_id != item.project_id;
          });
        }
        if (this.build_arr.length > 1) {
          this.current_tel = "1";
        }
      }
    },
    onSubmitBuild () {
      if (this.build_arr.length > this.webinfo.max_select_project_total) {
        uni.showToast({
          title: "最多选择" + this.webinfo.max_select_project_total + "个项目",
          icon: "none",
        });
        return;
      }
      this.isCheckProject = false;
    },
    // 删除项目
    deleteCheck (index, item) {
      var e = this.build_arr
        .map((item1) => item1.project_id)
        .indexOf(item.project_id);
      if (e != -1) {
        this.build_arr.splice(e, 1);
      }
      if (this.build_arr.length === 0) {
        this.tips_full = false;
        this.customer_phone = "";
        this.phone.start = "";
        this.phone.end = "";
        this.isFull = false;
      }
    },
    selectProject () {
      if (this.siteConfig.city_type === 2) {
        this.params.region_0 = this.city.region_0;
        this.params.region_1 = this.city.region_1;
      }
      this.getDataList();
      this.isCheckProject = true;
    },
    // 智能识别

    distinguishCtn () {
      if (!this.intelligent_ctn) {
        uni.showToast({
          title: "请输入识别内容",
          icon: "none",
        });
        return;
      }
      //如果输入框不为空开启识别
      var arr = [
        { label: "楼盘名称", value: "" },
        { label: "客户姓名", value: "" },
        { label: "客户性别", value: "" },
        { label: "客户电话", value: "" },
        { label: "添加时间", value: "" },
        { label: "报备经纪人", value: "" },
        { label: "经纪人公司", value: "" },
        { label: "联系方式", value: "" },
      ];
      var result = [];
      this.$IdentifyTools(this.intelligent_ctn, arr, result);
      var project,
        customer_name,
        customer_phone,
        customer_sex,
        create_at,
        reported_broker,
        broker_company,
        broker_phone;
      result.map((item) => {
        switch (item.label) {
          case "楼盘名称":
            project = item.value;
            break;
          case "客户姓名":
            customer_name = item.value;
            break;
          case "客户性别":
            customer_sex = item.value;
            break;
          case "客户电话":
            customer_phone = item.value;
            break;
          case "添加时间":
            create_at = item.value;
            break;
          case "报备经纪人":
            reported_broker = item.value;
            break;
          case "经纪人公司":
            broker_company = item.value;
            break;
          case "联系方式":
            broker_phone = item.value;
            break;
        }
      });
      try {
        this.$ajax.get(
          `/common/project/query/build/by/name?name=${project}`,
          {},
          (res) => {
            if (res.statusCode === 200) {
              this.is_project_build = true;
              if (res.data.length === 0) {
                uni.showToast({
                  title: "未识别出该项目楼盘！",
                  icon: "none",
                });
                return;
              }
              this.build_list_obj = res.data;
              this.build_arr.push(res.data);
              this.customer_name = customer_name;
              this.form_create.reported.remark = `【楼盘名称】${project}\n【报备经纪人】${reported_broker}\n【经纪人公司】${broker_company}\n【联系方式】${broker_phone}`;
              if (customer_sex === "先生") {
                this.current_sex = "1";
                this.form_create.customer.sex = "1";
              } else if (customer_sex === "女士") {
                this.current_sex = "0";
                this.form_create.customer.sex = "0";
              }
              if (customer_phone.indexOf("*") != -1) {
                this.current_tel = "0";
                this.phone.start = customer_phone.slice(0, 3);
                this.phone.end = customer_phone.slice(7, 11);
              } else {
                this.current_tel = "1";
                this.customer_phone = customer_phone;
              }
              if (res.data.full_num_reported == 1) {
                this.isFull = false;
                this.current_tel = "1";
              }
              this.matchTimeFormat(res.data.visit_time_format_category);
            }
          }
        );
      } catch (e) {
        if (e) {
          uni.showToast({
            title: "粘贴内容无法识别",
            icon: "none",
          });
        }
      }
    },
    // 客户性别选择
    radioChange (e) {
      this.form_create.customer.sex = e.detail.value;
      this.current_sex = e.detail.value;
    },
    // 选择时间
    selectTime () {
      this.$refs.picker.show();
    },
    confirmTime (e) {
      this.form_create.reported.visit_time = e.result;
    },
    // 选择来访方式
    selectVisitCategory (index) {
      this.form_create.reported.visit_category = this.visit_category_list[
        index
      ].value;
    },
    // 选择楼盘类型
    checkBuildCategory (e) {
      this.form_create.reported.intention_build_category = e.detail.value.toString();
    },
    // 多选楼盘类型
    checkGroup (item) {
      if (this.build_type_checkon.includes(item.value)) {
        this.build_type_checkon = this.build_type_checkon.filter((ele) => {
          return ele != item.value;
        });
      } else {
        this.build_type_checkon.push(item.value);
      }
      this.form_create.reported.intention_build_category = this.build_type_checkon.toString();
    },
    // 选择手机号类型
    radioChangeTel (e) {
      this.current_tel = e.detail.value;
      this.customer_phone = "";
      this.phone.start = "";
      this.phone.end = "";
    },
    // 添加多个报备手机号
    addInput () {
      if (this.other_phone_list.length > 10) {
        uni.showToast({
          title: "最多添加10个联系人",
          icon: "none",
        });
        return;
      }
      this.other_phone_list.push({
        name: "",
        phone: "",
        sex: "",
        other: 1,
      });
    },
    // 删除下标数组
    removeInput (item) {
      let index = this.other_phone_list.indexOf(item);
      if (index != -1) {
        this.other_phone_list.splice(index, 1);
      }
    },

    // 处理数组对象信息
    onChangeArr (arr) {
      var customerLen = arr.length,
        projectLen = this.build_arr.length,
        key = 0,
        changeArr = [];
      // 循环中将项目id存入对象数组
      for (var i = 0; i < customerLen; i++) {
        for (var j = 0; j < projectLen; j++) {
          var temCustomer = this.cloneArr(arr[i]);
          temCustomer["reported"]["project_id"] = this.build_arr[j][
            "project_id"
          ];
          temCustomer["project_data"]["full_num_reported"] = this.build_arr[j][
            "full_num_reported"
          ];
          temCustomer["project_data"]["build_name"] = this.build_arr[j][
            "build_name"
          ];
          temCustomer["project_data"]["reported_visit"] = this.build_arr[j][
            "reported_visit"
          ];
          temCustomer["project_data"]["hide_num_mode"] = this.build_arr[j][
            "hide_num_mode"
          ];
          changeArr[key] = temCustomer;
          key++;
        }
      }
      return changeArr;
    },
    // 处理数组对象信息
    cloneArr (Obj) {
      var buf;
      if (Obj instanceof Array) {
        buf = [];
        var i = Obj.length;
        while (i--) {
          buf[i] = this.cloneArr(Obj[i]);
        }
        return buf;
      } else if (Obj instanceof Object) {
        buf = {};
        for (var k in Obj) {
          buf[k] = this.cloneArr(Obj[k]);
        }
        return buf;
      } else {
        return Obj;
      }
    },
    assignData (params, index) {
      // 手机号码验证
      var phone_reg = /^1[3-9]\d{9}$/;
      // 判断前三后五/前三后四过滤
      var hide_phone_reg = this.is_num_mode
        ? /^1[3-9]\d{1}\*\*\*\d{5}$/
        : /^1[3-9]\d{1}\*\*\*\*\d{4}$/;
      if (!params.reported.visit_category) {
        delete params.reported.visit_category;
      }
      if (!params.reported.visit_people) {
        delete params.reported.visit_people;
      }
      if (!params.reported.intention_build_category) {
        delete params.reported.intention_build_category;
      }
      if (params.customer.phone.indexOf("*") != -1) {
        if (!hide_phone_reg.test(params.customer.phone)) {
          this.other_phone_list.splice(index, 1);
          uni.showToast({
            title: "请检查客户[" + params.customer.name + "]的联系方式",
            icon: "none",
          });
          return false;
        }
      } else {
        if (!phone_reg.test(params.customer.phone)) {
          this.other_phone_list.splice(index, 1);
          uni.showToast({
            title: "请检查客户[" + params.customer.name + "]的联系方式",
            icon: "none",
          });
          return false;
        }
      }
      return true;
    },
    // 立即提交
    onCheckSubmit () {
      if (!(this.isFull && this.is_full_show == 0) || this.current_tel == 1) {
        this.checkTelAuth()
      } else {
        this.onSubmit()
      }

    },
    checkTelAuth () {
      let otherPhone = []
      this.other_phone_list.map(item => {
        otherPhone.push(item.phone)
      })
      let allPromise = []
      let phoneList = [this.customer_phone, ...otherPhone]
      for (let index = 0; index < phoneList.length; index++) {
        const element = phoneList[index];
        let promiseItem = new Promise((res, rej) => {
          this.$ajax.post("/client/customer/reported/create/auth", { mobile: element }, (result) => {
            if (result.statusCode == 200) {
              res(element)
            } else {
              rej(element)
            }
          })
        })
        allPromise.push(promiseItem)
      }

      Promise.all(allPromise).then(() => {
        this.onSubmit()
      })
        .catch((err) => {

          uni.showToast({
            title: `号码${err}已经存在跟进人无法报备 请重新填写`,
            icon: "none"
          })
        })
    },
    async onSubmit () {
      if (this.build_arr.length === 0 && !this.build_list_obj.build_id) {
        uni.showToast({
          title: "请选择项目",
          icon: "none",
        });
        return;
      }
      if (!this.customer_name) {
        uni.showToast({
          title: "请输入客户姓名",
          icon: "none",
        });
        return false;
      }
      if (!this.form_create.customer.sex) {
        uni.showToast({
          title: "请选择客户性别",
          icon: "none",
        });
        return false;
      }
      if (
        // 如果开启客户意向且报备楼盘类型可选大于一种，就必须选择一种类型进行报备
        this.build_list_obj.reported_intention === 1 &&
        this.build_type_list.length > 1 &&
        !this.form_create.reported.intention_build_category
      ) {
        uni.showToast({
          title: "请选择意向楼盘",
          icon: "none",
        });
        return;
      }
      // 如果楼盘开启身份证后六位且未输入身份证号
      if (
        this.build_list_obj.reported_id_no_category === 2 &&
        !this.form_create.reported.customer_id_no
      ) {
        uni.showToast({
          title: "请输入身份证号后六位",
          icon: "none",
        });
        return false;
      }
      // phone: 前三后四  phone1：前三后五
      var phone =
        this.customer_phone || this.phone.start + "****" + this.phone.end;
      var phone1 =
        this.customer_phone || this.phone.start + "***" + this.phone.end;
      if (!this.phone) {
        uni.showToast({
          title: "请输入联系方式",
          icon: "none",
        });
        return false;
      }
      uni.showLoading({
        title: "正在上传",
        mask: true,
      });
      this.other_phone_list.unshift({
        name: this.customer_name,
        phone: this.is_num_mode ? phone1 : phone,
        sex: "",
      });
      // 将客户名称，联系方式存入对象数组
      let arr = [];
      for (var i = 0; i < this.other_phone_list.length; i++) {
        // 区分前三后五位
        this.other_phone_list[i].phone =
          this.other_phone_list[i].phone ||
          this.other_phone_list[i].start_phone +
          `${this.is_num_mode ? "***" : "****"}` +
          this.other_phone_list[i].end_phone;
        let obj = JSON.parse(JSON.stringify(this.form_create));
        obj.customer.name = this.other_phone_list[i].name;
        obj.customer.phone = this.other_phone_list[i].phone;
        switch (this.other_phone_list[i].sex) {
          case "先生":
            obj.customer.sex = "1";
            break;
          case "女士":
            obj.customer.sex = "0";
            break;
        }
        arr.push(obj);
      }
      // this.batchSub(this.onChangeArr(arr));
      let arr2 = this.onChangeArr(arr);
      let project_ids = [],
        customer_phones = [];
      let arr3 = [];
      for (let i = 0; i < arr2.length; i++) {
        let params = arr2[i];
        let formData = JSON.parse(JSON.stringify(params));
        // 循环方式处理手机号码
        if (!this.assignData(formData, i)) return;
        // 包含全号/隐号
        if (this.tips_full && this.current_tel == 1) {
          //当前项目全号不处理手机号
          if (formData.project_data.full_num_reported === 1) {
            formData.customer.phone;
          } else if (
            // 当前项目隐号且前三后五
            formData.project_data.full_num_reported === 0 &&
            formData.project_data.hide_num_mode == 2
          ) {
            formData.customer.phone = formData.customer.phone.replace(
              /(\d{3})\d{3}(\d{5})/,
              "$1***$2"
            );
          } else {
            // 当前项目前三后四
            formData.customer.phone = formData.customer.phone.replace(
              /(\d{3})\d{4}(\d{4})/,
              "$1****$2"
            );
          }
        } else if (this.current_tel == 0) {
          //如果包含前三后五且报备的项目中包含前三后五
          if (formData.project_data.hide_num_mode == 2) {
            formData.customer.phone = formData.customer.phone.replace(
              /(\d{3})\d{3}(\d{5})/,
              "$1***$2"
            );
          } else {
            formData.customer.phone = formData.customer.phone.replace(
              /(\d{3})\d{4}(\d{4})/,
              "$1****$2"
            );
          }
        }
        if (formData.project_data.reported_visit === 0) {
          formData.reported.visit_people = 0;
        }
        // 将需要验证的手机号存入数组
        // 如果数组下标与数组相等将处理过的项目id和联系方式存入数组
        project_ids.push(formData.reported.project_id);
        customer_phones.push(formData.customer.phone);
        let form_data = {};
        if (i + 1 === arr2.length) {
          form_data = {
            project_ids: project_ids.filter(
              (item, index, arr) => arr.indexOf(item, 0) === index // 过滤数组重复数据
            ),
            customer_phones: customer_phones.filter(
              (item, index, arr) => arr.indexOf(item, 0) === index // 过滤数组重复数据
            ),
          };
        }
        arr3.push(formData);
        if (JSON.stringify(form_data) !== "{}") {
          await this.onValidatePhone(
            // 验证手机号码
            form_data
          ).then((res) => {
            uni.hideLoading();
            if (res.length === 0) {
              this.batchSub(arr3);
            } else if (res.type) {
              uni.showModal({
                title: "提示",
                content: res.message,
                confirmText: "确认",
                showCancel: res.type === "alert" ? false : true,
                success: (res1) => {
                  if (res1.confirm) {
                    console.log("确认");
                    this.other_phone_list.splice(i, 1);
                    if (res.type === "confirm") {
                      this.batchSub(arr3);
                    }
                  }
                  if (res1.cancel) {
                    formData.customer.phone = "";
                  }
                },
              });
            }
          });
        }
      }
    },
    // 提交过程
    async batchSub (arr) {
      uni.hideLoading();
      this.module_msg = ''
      for (let i in arr) {
        await this.onsubmitFormData(arr[i], i)
          .then((res) => {
            // 提交报备流程
            this.isShowLoading(res);
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    isShowLoading (res) {
      this.is_show_content = res;
      this.is_show_modal = true;
    },
    onsubmitFormData (formData, index) {
      this.other_phone_list.splice(index, 1);
      return new Promise((resolve, reject) => {
        // resolve();
        // console.log(JSON.parse(JSON.stringify(formData))); // 浏览器打印数据默认展开
        this.$ajax.post(
          "/client/customer/reported/create/simple",
          formData,
          (res) => {
            let msg =
              res.statusCode === 200 ? `成功：` : `失败：${res.data.message}`;
            // 拼接报备状态显示文字描述
            this.module_msg += `<div style='color:${res.statusCode === 200 ? "green" : "red"
              }'>报备${msg}[${formData.customer.name}-${formData.customer.phone
              }-${formData.project_data.build_name}]；</div>\n`;
            resolve(this.module_msg);
          }
        );
      });
    },
    onValidatePhone (arr) {
      if (arr.length === 0) {
        return;
      }
      return new Promise((resolve, reject) => {
        this.$ajax.post(
          `/client/customer/reported/validatePhone`,
          arr,
          (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject("error");
              uni.showToast({
                title: res.data.message,
                icon: "none",
              });
            }
          }
        );
      });
    },
    onSexinput (e) {
      if (e.detail.value !== "先生" && e.detail.value !== "女士") {
        uni.showToast({
          title: "客户性别仅限于先生/女士输入",
          icon: "none",
        });
        return;
      }
    },
    onIdentifyRemark () {
      if (!this.form_create.reported.remark) {
        uni.showToast({
          title: "输入描述内容后进行识别",
          icon: "none",
        });
        return;
      }
    },
    bindBtn (type) {
      if (type === "confirm") {
        this.is_show_modal = false;
        this.$navigateTo(`/client/list?type=-1`);
      } else {
        this.module_msg = ''
        this.is_show_modal = false;
      }
    },
  },
  onPullDownRefresh () {
    this.params.page = 1;
    this.getDataList();
    uni.stopPullDownRefresh();
  },
  onReachBottom () {
    if (this.isCheckProject) {
      if (this.load_status === "nomore") {
        return;
      }
      this.params.page++;
      this.getDataList();
    }
  },
};
</script>

<style scoped lang="scss">
page {
  background: #eee;
}
.list {
  .report-build {
    background: #fff;
    margin-bottom: 180rpx;
    .title-bar {
      background: #fff;
    }
    // 导航
    .xinzeng {
      position: absolute;
      right: 48rpx;
      color: #0174ff;
    }
    .title-tips {
      margin: 12rpx 0;
      align-items: center;
      padding: 24rpx 48rpx;
      color: #0174ff;
      background: #fff;
      text {
        margin-left: 8rpx;
      }
    }
    // 选择项目
    .checkon {
      background: url('../static/check.png') no-repeat 90%;
      background-color: #eee;
    }
    .build-item {
      &::after {
        content: '';
        height: 2rpx;
        width: 100%;
        background: #e0e0e0;
      }
      .ctn {
        padding: 24rpx 48rpx;
        line-height: 48rpx;
        .build-img {
          width: 200rpx;
          height: 200rpx;
        }
        .ctn-box {
          flex-direction: column;
          justify-content: space-between;
          margin-left: 30rpx;
          .left {
            justify-content: space-between;
            .left-label {
              font-size: 32rpx;
            }
            .right-label {
              color: #cb726e;
              margin-left: 20rpx;
            }
          }
          .right {
            justify-content: space-between;
            font-size: 26rpx;
            color: #999;
          }
        }
      }
    }
    .bottom {
      justify-content: space-between;
      padding: 30rpx;
      align-items: center;
      position: fixed;
      bottom: 0;
      height: 150rpx;
      width: 100%;
      background: #fff;
      text {
        font-size: 32rpx;
        &:last-child {
          color: #999;
        }
      }
      .btn {
        width: 400rpx;
        line-height: 80rpx;
        height: 80rpx;
        align-items: center;
        color: #fff;
        background: #0097fe;
        border-radius: 10rpx;
      }
    }
  }
  .content-build {
    .content {
      padding: 40rpx 48rpx;
      background: #fff;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16rpx;
      .build {
        background: #fff;
        justify-content: flex-start;
        align-items: center;
        .left {
          image {
            width: 200rpx;
            height: 120rpx;
          }
        }
        .right {
          flex-direction: column;
          line-height: 44rpx;
          justify-content: space-between;
          margin-left: 20rpx;
          .bottom-rules {
            text {
              width: 450rpx;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              color: #d3525b;
            }
          }
        }
      }
      .input-placeholder {
        color: #999;
      }
      .left {
        font-size: 32rpx;
        color: #666;
      }
      .uni-input {
        text-align: end;
        font-size: 32rpx;
        color: #333;
        height: 44rpx;
      }
      .uni-group {
        display: flex;
      }
      .sex-box-row {
        align-items: center;
        display: flex;
        margin-left: 56rpx;
      }
      .time-check {
        align-items: center;
        text-align: end;
        .time-style {
          color: #0174ff;
          font-size: 32rpx;
        }
        .icon {
          margin-left: 10rpx;
        }
      }
      .build {
        background: #fff;
        justify-content: flex-start;
        align-items: center;
        .left {
          image {
            width: 200rpx;
            height: 120rpx;
          }
        }
        .right {
          flex-direction: column;
          line-height: 44rpx;
          justify-content: space-between;
          margin-left: 20rpx;
          .bottom-rules {
            text {
              width: 450rpx;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              color: #d3525b;
            }
          }
        }
      }
      .left {
        font-size: 32rpx;
        color: #666;
      }
      .uni-input {
        text-align: end;
        font-size: 32rpx;
        color: #333;
        height: 44rpx;
      }
      .input-placeholder {
        color: #999;
      }
      .time-check {
        align-items: center;
        text-align: end;
        .time-style {
          color: #0174ff;
          font-size: 32rpx;
        }
        .icon {
          margin-left: 10rpx;
        }
      }
    }
    .backfff {
      background: #fff;
      margin: 20rpx 0;
      .content-check {
        padding: 40rpx 48rpx;
        background: #fff;
        margin-bottom: 16rpx;
        .left-title {
          font-size: 32rpx;
          color: #666;
        }
        .check-box {
          margin-top: 30rpx;
          flex-wrap: wrap;
          .check-group {
            margin: 10rpx 10rpx 10rpx 0;
            padding: 10rpx 28rpx;
            background: #f4f3f5;
            text-align: center;
          }
          .check_group_on {
            background: #0174ff;
            color: #fff;
          }
        }
      }
      .check-project {
        border-bottom: 2rpx solid #eee;
        padding: 24rpx 48rpx;
        justify-content: space-between;
        .left {
          font-size: 30rpx;
        }
        .right {
          font-size: 32rpx;
          font-weight: bold;
          width: 40rpx;
          height: 30rpx;
          line-height: 30rpx;
          background: crimson;
          color: #fff;
          align-items: center;
          border-radius: 10rpx;
        }
        .left-box {
          align-items: center;
          justify-content: space-between;
          .row-box {
            align-items: center;
          }
          .left-box-label {
            background-color: #65d4ba;
            border-color: #fde2e2;
            border-radius: 8rpx;
            font-size: 32rpx;
            margin: 0 10rpx;
            padding: 10rpx;
            color: #fff;
          }
        }
      }
      // 选择
      .nav {
        flex-direction: row;
        justify-content: flex-start;
        font-size: 28rpx;
        margin-left: 60rpx;
        .nav-item {
          margin-left: 20rpx;
          padding: 10rpx 30rpx;
          background: #f4f3f5;
          &.active {
            color: #fff;
            background: #0174ff;
          }
        }
      }
    }
    // 智能识别样式
    .intelligent {
      padding: 24rpx 48rpx;
      width: 100%;
      background: #fff;
      margin-bottom: 15rpx;
      textarea {
        padding: 24rpx;
        height: 400rpx;
        width: 100%;
        background: #f3f2f7;
      }
      .identify-box-row {
        align-items: center;
        margin-top: 20rpx;
        justify-content: space-between;
        .use {
          color: #708efc;
        }
        .identify-box {
          justify-content: flex-end;
          align-items: center;
          .clear {
            margin-right: 20rpx;
          }
          .identify {
            padding: 10rpx;
            border-radius: 10rpx;
            background: #708efc;
            color: #fff;
            margin-left: 20rpx;
          }
        }
      }
    }
    // 备注
    .beizhu {
      padding: 40rpx 48rpx;
      background: #fff;
      position: relative;
      // margin-top: 20rpx;
      .beizhu-title {
        justify-content: space-between;
        align-items: center;
      }
      .shibie {
        border: 1px solid #0097fe;
        font-size: 22rpx;
        padding: 4rpx 10rpx;
        border-radius: 4rpx;
        color: #0097fe;
      }
      .title {
        font-size: 32rpx;
        color: #666;
      }
      .text-input {
        margin-top: 24rpx;
      }
      .length-box {
        position: absolute;
        right: 48rpx;
        bottom: 40rpx;
      }
    }
    .uni-btn-v {
      margin: 40rpx 0;
    }
  }
  .input-phone {
    padding: 40rpx 48rpx;
    background: #fff;
    margin-bottom: 16rpx;
    align-items: center;

    .icon-input {
      background: #0174ff;
      padding: 4rpx;
      border-radius: 50rpx;
      margin-right: 0;
      margin-left: 30rpx;
    }
    .label-name {
      font-size: 30rpx;
      margin-right: 10rpx;
    }
    .label-input {
      border-bottom: 2rpx solid #999;
      width: 20%;
      margin: 0 10rpx;
    }
    .label-name-phone {
      width: 40%;
      border-bottom: 2rpx solid #999;
    }
    text {
      font-size: 36rpx;
    }
    .input-tel {
      width: 100%;
      font-size: 36rpx;
      letter-spacing: 22rpx;
      border-bottom: 2rpx solid #999;
    }
  }
  .tips_full {
    background: #fff;
    flex: 1;
    margin-left: 30rpx;
    line-height: 40rpx;
  }
}
button {
  width: 654rpx;
  height: 88rpx;
  color: #fff;
  background: #0174ff;
  box-shadow: 0 4px 16px 0 rgba(1, 116, 255, 0.4);
  border-radius: 22px;
}
.modal {
  padding: 30rpx;
  max-height: 600rpx;
  overflow-y: scroll;
}
</style>
