<template>
  <view
    class="border-row border-top flex-row"
    :style="{ 'padding-bottom': paddingBottomHeight + 'rpx' }"
  >
    <block v-for="(item, index) in tabList" :key="index">
      <view
        class="navigator"
        :class="currentTabIndex == index ? 'on' : ''"
        @tap="switchTab(index, item)"
      >
        <view class="icon">
          <image
            :src="currentTabIndex == index ? item.active_icon : item.icon"
            :class="!item.text ? 'publish' : ''"
          ></image>
          <text v-if="item.badge" class="uni_badge">{{ item.badge }}</text>
          <text v-if="item.badgeDot" class="uni_badge uni_badge_dot"></text>
        </view>
        <view
          class="text"
          :style="[
            currentTabIndex == index ? { color: tintColor } : { color: color },
          ]"
          >{{ item.text }}</view
        >
      </view>
    </block>
  </view>
</template>

<script>
export default {
  data() {
    return {
      paddingBottomHeight: 0, //苹果X以上手机底部适配高度
      currentTabIndex: this.current,
    };
  },
  created() {
    let that = this;
    uni.getSystemInfo({
      success: function(res) {
        let model = ["X", "XR", "XS", "11", "12", "13", "14", "15"];
        model.forEach((item) => {
          //适配iphoneX以上的底部，给tabbar一定高度的padding-bottom
          if (
            res.model &&
            res.model.indexOf(item) != -1 &&
            res.model.indexOf("iPhone") != -1
          ) {
            that.paddingBottomHeight = 40;
          }
        });
      },
    });
  },
  props: {
    current: { type: [Number, String], default: 0 },
    backgroundColor: { type: String, default: "#FFFFFF" },
    color: { type: String, default: "#808080" },
    tintColor: { type: String, default: "#3172F6" },
    tabList: {
      type: Array,
      default: () => [
        {
          icon: "../static/tab_icon/index-select.png",
          active_icon: "../static/tab_icon/index.png",
          text: "首页",
          path: "/index/index",
        },
        {
          icon: "../static/tab_icon/ic_xiangmu_nor.png",
          active_icon: "../static/tab_icon/ic_xiangmu.png",
          text: "项目",
          path: "/index/project",
        },
        {
          icon: "../static/tab_icon/message-select.png",
          active_icon: "../static/tab_icon/message.png",
          text: "消息",
          path: "/index/message",
        },
        {
          icon: "../static/tab_icon/main-select.png",
          active_icon: "../static/tab_icon/main.png",
          text: "我的",
          path: "/index/mine",
        },
      ],
    },
  },
  methods: {
    switchTab(index, item) {
      // this.currentTabIndex = index
      this.$emit("click", index, item);
    },
  },
};
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.flex-row {
  flex-direction: row;
}
.border-top {
  position: relative;
}
/* 上边框 */
.border-top:before {
  border-top: 1px solid #ccc;
  content: "";
  display: block;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  transform-origin: left top;
  -webkit-transform-origin: left top;
}
@media screen and(-webkit-min-device-pixel-ratio:2) {
  .border-top:before {
    transform: scaleY(0.5);
    -webkit-transform: scaleY(0.5);
  }
}
@media screen and(-webkit-min-device-pixel-ratio:3) {
  .border-top:before {
    transform: scaleY(0.3333);
    -webkit-transform: scaleY(0.3333);
  }
}
.border-row {
  z-index: 9999;
  width: 100%;
  background: #fff;
  height: 110rpx;
  position: sticky;
  bottom: 0;
  justify-content: space-around;
  .navigator {
    align-items: center;
    justify-content: center;
    .icon {
      image {
        width: 48rpx;
        height: 48rpx;
      }
      .publish {
        width: 48px;
        height: 48px;
      }
    }
    .text {
      font-size: 24rpx;
      margin-top: 6rpx;
    }
  }
}
</style>
