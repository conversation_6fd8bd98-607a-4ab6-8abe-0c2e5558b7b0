<template>
    <view class="forms-row" :class="{'forms-cloumn-row': form.type=='textarea'}">
        <view class="label">
            {{form.title}}
            <text class="required" v-if="form.require">*</text>
        </view>
        <view class="cont" :class="{disabled: !!form.disabled}">
            <slot name="content">
                <template v-if="form.type=='radio-button'">
                    <tRadioButton :options="form.options" v-model="curValue"/>
                </template>
                <template v-else-if="form.type=='text'">
                    <input class="input" type="text" v-model="curValue" :placeholder="form.placeholder || '请输入'" :maxlength="form.maxlength || 140" disabled v-if="form.disabled"/>
                    <input class="input" type="text" v-model="curValue" :placeholder="form.placeholder || '请输入'" :maxlength="form.maxlength || 140" v-else/>
                </template>
                <template v-else-if="form.type=='number'">
                    <input class="input" type="number" v-model="curValue" :placeholder="form.placeholder || '请输入'" :maxlength="form.maxlength || 140" disabled v-if="form.disabled"/>
                    <input class="input" type="number" v-model="curValue" :placeholder="form.placeholder || '请输入'" :maxlength="form.maxlength || 140" v-else/>
                </template>
                <template v-else-if="form.type=='digit'">
                    <input class="input" type="digit" v-model="curValue" :placeholder="form.placeholder || '请输入'" :maxlength="form.maxlength || 140" disabled v-if="form.disabled"/>
                    <input class="input" type="digit" v-model="curValue" :placeholder="form.placeholder || '请输入'" :maxlength="form.maxlength || 140" v-else/>
                </template>
                <template v-else-if="form.type=='tel'">
                    <input class="input" type="tel" v-model="curValue" :placeholder="form.placeholder || '请输入'" :maxlength="form.maxlength || 140" disabled v-if="form.disabled"/>
                    <input class="input" type="tel" v-model="curValue" :placeholder="form.placeholder || '请输入'" :maxlength="form.maxlength || 140" v-else/>
                </template>
                <template v-else-if="form.type=='select'">
                    <template v-if="form.multiple">
                        <tCustomPicker :placeholder="form.placeholder || '请选择'" multiple :filterable="form.filterable" v-model="curValue" :datas="form.options" :map="{label:form.labelKey,value:form.valueKey, children: form.childrenKey}" align="left" size="small" class="t-picker"></tCustomPicker>
                    </template>
                    <template v-else>
                        <tPicker :placeholder="form.placeholder || '请选择'" v-model="curValue" :datas="form.options" :map="{label:form.labelKey,value:form.valueKey, children: form.childrenKey}" align="left" size="small" class="t-picker"></tPicker>
                    </template>
                </template>
                <template v-else-if="form.type=='textarea'">
                    <view class="textarea-wrapper">
                        <textarea :placeholder="form.placeholder || '请输入'" auto-height="true" v-model="curValue"  :maxlength='form.maxlength || -1' style="width:100%" :style="{paddingBottom: form.maxlength?0:'12px'}"></textarea>
                        <view class="words-limit" v-if="form.maxlength">{{ form.value.length }} / {{form.maxlength}}</view>
                    </view>
                </template>
            </slot>
        </view>

        <slot name="append"></slot>
    </view>
</template>
<script>
import tRadioButton from '@/components/tplus/tRadio/tRadionButton'
import tPicker from '@/components/tplus/tPicker'
import tCustomPicker from '@/components/tplus/tCustomPicker'
export default {
    props: {
        form: {type: Object, default: ()=>({})},
        value: {type: [ String, Number, Array ], default: ''},
    },
    options: {
        multipleSlots: true,
        styleIsolation: 'shared'
    },
    components: {
        tRadioButton,
        tPicker,
        tCustomPicker
    },
    data(){
        return{
            curValue: ''
        }
    },
    watch: {
        value: {
            handler(val) {
                this.curValue = val;
            },
            immediate: true
        },
        curValue(val){
            this.$emit('input', val);
        }
    }
}
</script>

<style lang="scss" scoped>
.forms-block{
    width: 100%;
}
.forms-row{
    display: flex;
    flex-direction: row;
    align-items: baseline;
    padding: 20rpx 0;
    border-bottom: 1px solid #f7f7f7;
    &.forms-cloumn-row{
        flex-direction: column;
        padding-top: 32rpx;
        .cont{
            width: 100%;
        }

    }
    .label{
        color: #4E5969;
        font-size: 32rpx;
        white-space: nowrap;
        padding-right: 40rpx;
        flex-direction: row;
        min-width: 190rpx;
        &.no-visible{
            visibility: hidden;
        }
        .required{
            color: #f40;
        }
    }
    .cont{
        flex: 1;
        min-height: 70rpx;
        flex-direction: row;
        align-items: center;
        &.disabled{
            opacity: .7;
        }
        .t-picker,.input{
            width: 100%;
        }
        .textarea-wrapper{
            width: 100%;
            border-radius: 4px;
            border: 1px solid #F0F1F5;
            background: #F8F8F8;
            padding: 12px 12px 0px 12px;
            margin-top: 16px;
            margin-bottom: 16px;
            textarea,.uni-textarea{
                min-height: 80rpx;
            }
            .words-limit{
                text-align: right;
                padding-bottom: 24rpx;
            }
        }
        ::v-deep{
            .input-placeholder{
                color: #C9CDD4;
                font-size: 32rpx;
            }
        }
    }
}

</style>