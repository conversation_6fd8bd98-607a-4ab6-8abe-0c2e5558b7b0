<template>
  <view>
    <scroll-view class="weektop" scroll-x>
      <view class="weekeds">
        <view class="weeked" v-for="(tab, index) in navs" :key="tab.id" :id="tab.id"
          :class="navIndex == tab.id ? 'activite' : ''" @click="checkIndex(tab, index)">{{ tab.name }}</view>
      </view>
    </scroll-view>
    <view class="client-box">
      <view class="client-item" v-for="(item, index) in clientList" :key="index">
        <view class="user-info">
          <image v-if="item.wx_work_avatar != ''" src="@/static/img/checked.png" />
          <text class="avatar" :style="'background:' + item.color" v-else>{{ item.user_name[0] }}</text>
          <view class="info">
            <text>{{ item.user_name }}</text>
            <view class="department">
              <text v-for="(item2, index2) in item.department" :key="index2">{{ item2 }}</text>
            </view>
          </view>
        </view>
        <view class="client-info">
          <view class="info">
            <text>{{ item.browse_num }}</text>
            <text>浏览次数</text>
          </view>
          <view class="info">
            <text>{{ item.share_num }}</text>
            <text>浏览次数</text>
          </view>
          <view class="info">
            <text>{{ item.customer_num }}</text>
            <text>获客量</text>
          </view>
        </view>
      </view>
    </view>
    <loadMore :status="load_status" @reload="getClientList()" />
  </view>
</template>
<script>
import loadMore from "@/components/loadMore.vue";
export default {
  components: {
    loadMore,
  },
  data() {
    return {
      navIndex: 3,
      navs: [
        { id: 1, name: '全部' },
        { id: 2, name: "今天" },
        { id: 3, name: "昨天" },
        { id: 4, name: "本周" },
        { id: 5, name: "上周" },
        { id: 6, name: "本月" },
        { id: 7, name: "上月" },
      ],
      clientList: [],
      proportionList: {
        page: 1,
        per_page: 10,
        date_type: 3,
      },
      load_status: ''
    };
  },
  onReachBottom() {
    if (this.load_status == 'loading') return
    this.proportionList.page++
    this.getClientList()
  },
  onLoad() {
    this.getClientList()
  },
  methods: {
    getClientList() {
      this.load_status = "loading";
      this.$ajax.get(`/admin/crm/client_track/admin_ranking`, this.proportionList, res => {
        if (res.statusCode == 200) {
          this.clientList = this.clientList.concat(res.data.data)
          this.clientList.forEach((element, index) => {
            switch (index) {
              case 0:
                element.color = '#f52000'
                break;
              case 1:
                element.color = '#f65600'
                break;
              case 2:
                element.color = '#f7bd00'
                break;
              default:
                break;
            }
          });
          if (res.data.data.length < this.proportionList.per_page) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'loadend'
          }
        } else {
          this.load_status = 'nomore'
        }
      })
    },
    checkIndex(item, index) {
      this.navIndex = item.id
      this.proportionList.page = 1
      this.proportionList.date_type = item.id
      this.clientList = []
      this.getClientList()
    }
  },
};
</script>
<style lang="scss">
page {
  background-color: #f3f3f3;
}

.avatar {
  width: 48rpx *2;
  height: 48rpx *2;
  line-height: 100rpx;
  text-align: center;
  font-size: 40rpx;
  color: #fff;
  border-radius: 50%;
  margin-right: 15rpx;
  background-color: #3C7EFF;
}

.client-box {
  margin-top: 30rpx;
  width: 100%;
  padding: 0 20rpx;

  .client-item {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 20rpx;
    border-radius: 3px;
    margin-bottom: 30rpx;
    background-color: #fff;

    .user-info {
      display: flex;
      flex-direction: row;
      width: 100%;

      image {
        display: inline-block;
        width: 48rpx * 2;
        height: 48rpx * 2;
        margin-right: 20rpx;
        border-radius: 50%;
      }

      .info {
        &>text:nth-child(1) {
          font-size: 32rpx;
          font-weight: 500;
          color: #292C39;
          margin-top: 10rpx;
        }

        .department {
          display: flex;
          flex-direction: row;
          width: 100%;
          text-wrap: nowrap;
          overflow-x: auto;

          text {
            font-size: 24rpx;
            font-weight: 400;
            color: #a2a2a3;
            margin-top: 20rpx;
            margin-right: 10rpx;
          }
        }
      }
    }

    .client-info {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin-top: 30rpx;

      .info {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0 30rpx;

        &>text:nth-child(1) {
          font-size: 40rpx;
          font-weight: 500;
          color: #292C39;
          margin-bottom: 10rpx;
        }

        &>text:nth-child(2) {
          font-size: 24rpx;
          font-weight: 400;
          color: #a2a2a3;
        }
      }
    }
  }
}

.weektop {
  white-space: nowrap;
  background-color: #fff;
}

.weekbili {
  white-space: nowrap;
}

// 本周
.weeked {
  box-sizing: border-box;
  width: 68.6rpx *2;
  height: 88rpx;
  line-height: 85rpx;
  text-align: center;
  font-size: 28rpx;
  color: rgba(41, 44, 57, 0.40);
  display: inline-block;
}

.weekeds {
  display: inline-block;
}

.activite {
  color: #3C7EFF;
  font-size: 28rpx;
}

.activite::after {
  content: '';
  display: block;
  width: 23%;
  height: 3px;
  margin: 0 auto;
  background-color: #3C7EFF;
}
</style>