<template>
    <view class="cell-phone">
        <view>
            <text class="binding">手机号绑定</text>
            <view class="login_phone row">
                <input v-model="form.phone" type="number" placeholder-style="font-size:28rpx;color:#d8d8d8" maxlength="11"
                    placeholder="请输入手机号码" />
                <view class="getcaptcha" @click="getCode">{{
                    time ? time + 's后获取' : '获取验证码'
                }}</view>
            </view>
            <view class="login_phone">
                <input maxlength="6" placeholder-style="font-size:28rpx;color:#d8d8d8" type="number" placeholder="请输入验证码"
                    v-model="form.captcha" @input="inputValue" />
                <!-- <text v-if="code_value" class="clearCode" @click="clearCode">X</text> -->
            </view>
            <button :class="checked ? 'hava-value' : 'btn'" class="btn btn-left" @click="onSubmit">
                立即绑定
            </button>
            <view class="radio-box row">
                <view class="radio-content row">
                    <radio class="radio-form" :checked="checked" @click="changeRadio" />
                    <text class="row">
                        我已阅读并同意<text style="text-decoration: underline" @click="openContent(3)">《隐私政策》</text>及<text
                            style="text-decoration: underline" @click="openContent(4)">《用户服务协议》</text>
                    </text>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {
            form: [{
                phone: "",
                captcha: ""
            }],
            code_value: false,
            time: "",
            checked: false,
            sending: false,
            website_id: ""
        }
    },
    onLoad(options) {
        // console.log(12312);
        if (options.code) {
            this.code = options.code
            this.website_id = options.website_id
            // alert(this.code)
            //   this.login()
            return
        }
    },
    methods: {
        onSubmit() {
            if (!this.checked) {
                uni.showToast({
                    title: "请阅读并同意相关内容",
                    icon: "none",
                });
                return;
            }
            if (!this.form.phone || !/^1[3456789]\d{9}$/.test(this.form.phone)) {
                uni.showToast({
                    title: "请检查手机号码格式",
                    icon: "none",
                });
                return;
            }
            if (!this.form.captcha) {
                uni.showToast({
                    title: "请输入验证码",
                    icon: "none",
                });
                return;
            }
            this.$ajax.post(
                "/poster/user/bind_phone",
                {
                    captcha: this.form.captcha,
                    phone: this.form.phone,
                },
                (res) => {
                    if (res.statusCode === 200) {
                        this.phone_token = res.data.token;
                        uni.showToast({
                            title: "绑定成功",
                        });
                        // uni.setStorageSync("token", this.phone_token);
                        uni.setStorageSync(
                            "token" + this.$store.state.website_id,
                            this.phone_token
                        );
                    } else {
                        uni.showToast({
                            title: res.data.message || "登录失败",
                            icon: "none",
                        });
                    }
                }
            );
        },
        //获取验证码按钮点击计时事件
        getCode() {
            if (this.sending) {
                // console.log(this.sending);
                return;
            }
            if (!this.form.phone || !/^1[3456789]\d{9}$/.test(this.form.phone)) {
                uni.showToast({
                    title: "请检查手机号码格式",
                    icon: "none",
                });
                return;
            }
            this.sending = true;
            this.$ajax.post(
                "/auth/poster/send_sms",
                {
                    phone: this.form.phone,
                },
                (res) => {
                    if (res.statusCode === 200) {
                        uni.showToast({
                            title: "发送成功",
                            icon: "none",
                        });
                        this.time = 60;
                        this.timerDown();
                        this.sending = true;
                    } else {
                        uni.showToast({
                            title: res.data.message || "网络错误",
                            icon: "none",
                        });
                        // this.refCode();
                    }
                }
            );
        },
        timerDown() {
            // 倒计时
            if (this.timer) {
                clearInterval(this.timer);
            }
            this.timer = setInterval(() => {
                if (this.time <= 0) {
                    clearInterval(this.timer);
                    this.sending = false;
                    return;
                }
                this.time--;
            }, 1000);
        },
        inputValue(e) {
            this.code_value = e.target.value ? true : false;
        },
        changeRadio() {
            this.checked = !this.checked;
        }
    }
}
</script>
<style scoped lang="scss" >
.cell-phone {
    padding: 24rpx 48rpx;

    .binding {
        font-size: 20px;
        text-align: center;
        margin-top: 30px;
    }

    .login_phone {
        justify-content: space-between;
        padding: 24rpx 0;
        width: 100%;
        border-bottom: 2rpx solid #eee;
        margin-top: 30px;
        position: relative;

        .clearCode {
            position: absolute;
            right: 0;
            top: 40rpx;
            font-size: 28rpx;
            color: #999;
        }
    }

    .radio-content {
        font-size: 28rpx;
        color: #d8d8d8;
        margin: 48rpx 0;
        align-items: center;

        .radio-form {
            transform: scale(0.7);
        }
    }

    .btn {
        color: #fff;
        text-align: center;
        font-size: 36rpx;
        width: 100%;
        // height: 112rpx;
        opacity: 0.6;
        background: #0174ff;
        border-radius: 44rpx;
        margin-top: 30px;
    }

    .hava-value {
        opacity: 1;
    }
}</style>