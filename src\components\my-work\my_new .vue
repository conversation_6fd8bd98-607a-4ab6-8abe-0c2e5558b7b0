<template>
    <view>
        <!-- 房源 -->
        <view style="padding:12px; width:343px;background-color: #fff;border-radius: 8px;">
            <view
                style="border-radius: 8px;background: #F6F6F6; padding: 12px; display: flex;flex-direction: row;justify-content: space-between;">
                <view
                    style="width:80px;background: #F6F6F6;display: flex;flex-direction: row;justify-content: space-between;align-items: center;">
                    <view style="color: rgb(137 137 137);">电话号码</view>
                    <view>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path
                                d="M3.52864 5.52859C3.78899 5.26824 4.2111 5.26824 4.47145 5.52859L8.00004 9.05719L11.5286 5.52859C11.789 5.26824 12.2111 5.26824 12.4714 5.52859C12.7318 5.78894 12.7318 6.21105 12.4714 6.4714L8.47145 10.4714C8.2111 10.7318 7.78899 10.7318 7.52864 10.4714L3.52864 6.4714C3.26829 6.21105 3.26829 5.78894 3.52864 5.52859Z"
                                fill="#292C39" fill-opacity="0.7" />
                        </svg>
                    </view>
                </view>
                <view style="color: rgb(137 137 137);margin-top: 3px;">|</view>
                <view>
                    <input placeholder="请输入搜索内容">
                </view>
            </view>
            <view style="display: flex;flex-direction: row;margin-top: 16px;width: 200%;">
                <view v-for="(item, index) in houseList" :key="item.id" @tap="changeAct(item)">
                    <view class="my-style-one" :class="[act == index ? 'active' : '']">
                        {{ item.name }}
                    </view>
                </view>
                <!-- 本周 -->
                <scroll-view style="
                margin-left: 5px;
                width: 25%; 
                margin-top: 8px;
                white-space: nowrap;
		        text-align: center;
                " scroll-x="true" @scroll="scroll">
                    <view class="weeked" v-for="(tab, index) in navs" :key="tab.id" :id="tab.id"
                        :class="navIndex == index ? 'activite' : ''" @click="checkIndex(index)">{{ tab.name }}</view>
                </scroll-view>
            </view>
            <!-- 公盘房源 -->
            <view
                style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;margin-top: 16px;"
                v-if="act == 0">
                <view style="display: flex;flex-direction: row;align-items: center;">
                    <view>
                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 50 50" fill="none">
                            <circle cx="25" cy="25" r="25" fill="#FE6C17" fill-opacity="0.2" />
                            <g filter="url(#filter0_d_1742_471)">
                                <path
                                    d="M31.1251 35.4C30.4251 35.4 29.7626 35.2667 29.1376 35C28.5126 34.7333 27.9667 34.3667 27.5001 33.9C27.0334 33.4333 26.6667 32.8875 26.4001 32.2625C26.1334 31.6375 26.0001 30.975 26.0001 30.275C26.0001 29.575 26.1334 28.9125 26.4001 28.2875C26.6667 27.6625 27.0334 27.1208 27.5001 26.6625C27.9667 26.2042 28.5126 25.8417 29.1376 25.575C29.7626 25.3083 30.4251 25.175 31.1251 25.175C31.8251 25.175 32.4876 25.3083 33.1126 25.575C33.7376 25.8417 34.2792 26.2042 34.7376 26.6625C35.1959 27.1208 35.5584 27.6625 35.8251 28.2875C36.0917 28.9125 36.2251 29.575 36.2251 30.275C36.2251 30.975 36.0917 31.6375 35.8251 32.2625C35.5584 32.8875 35.1959 33.4333 34.7376 33.9C34.2792 34.3667 33.7376 34.7333 33.1126 35C32.4876 35.2667 31.8251 35.4 31.1251 35.4ZM33.6501 29.45H31.8001V27.725C31.8001 27.4917 31.7251 27.2917 31.5751 27.125C31.4251 26.9583 31.2334 26.875 31.0001 26.875C30.7667 26.875 30.5834 26.9583 30.4501 27.125C30.3167 27.2917 30.2501 27.4917 30.2501 27.725V29.45H28.6251C28.3917 29.45 28.1917 29.5333 28.0251 29.7C27.8584 29.8667 27.7751 30.0667 27.7751 30.3C27.7751 30.5333 27.8584 30.7083 28.0251 30.825C28.1917 30.9417 28.3917 31 28.6251 31H30.2501V32.775C30.2501 33.0083 30.3167 33.2083 30.4501 33.375C30.5834 33.5417 30.7667 33.625 31.0001 33.625C31.2334 33.625 31.4251 33.5417 31.5751 33.375C31.7251 33.2083 31.8001 33.0083 31.8001 32.775V31H33.6501V31.05C33.8834 31.05 34.0834 30.9833 34.2501 30.85C34.4167 30.7167 34.5001 30.5333 34.5001 30.3C34.5001 30.0667 34.4167 29.8667 34.2501 29.7C34.0834 29.5333 33.8834 29.45 33.6501 29.45ZM27.4751 23.225C27.4084 23.4917 27.3417 23.7333 27.2751 23.95C27.2084 24.1333 27.1251 24.3208 27.0251 24.5125C26.9251 24.7042 26.8167 24.85 26.7001 24.95C26.5501 25.0667 26.4459 25.1917 26.3876 25.325C26.3292 25.4583 26.2792 25.5958 26.2376 25.7375C26.1959 25.8792 26.1501 26.025 26.1001 26.175C26.0501 26.325 25.9667 26.475 25.8501 26.625C25.4667 27.125 25.1792 27.6083 24.9876 28.075C24.7959 28.5417 24.6626 28.9917 24.5876 29.425C24.5126 29.8583 24.4917 30.2875 24.5251 30.7125C24.5584 31.1375 24.6251 31.55 24.7251 31.95C24.7917 32.25 24.8917 32.5625 25.0251 32.8875C25.1584 33.2125 25.3542 33.5458 25.6126 33.8875C25.8709 34.2292 26.2001 34.5625 26.6001 34.8875C27.0001 35.2125 27.5001 35.525 28.1001 35.825C27.7001 35.9083 27.2334 35.9833 26.7001 36.05C26.2501 36.1 25.6959 36.1458 25.0376 36.1875C24.3792 36.2292 23.6251 36.25 22.7751 36.25C22.3417 36.25 21.8376 36.2333 21.2626 36.2C20.6876 36.1667 20.0959 36.125 19.4876 36.075C18.8792 36.025 18.2792 35.9667 17.6876 35.9C17.0959 35.8333 16.5542 35.7625 16.0626 35.6875C15.5709 35.6125 15.1542 35.5333 14.8126 35.45C14.4709 35.3667 14.2584 35.2917 14.1751 35.225C14.0251 35.0917 13.9084 34.7208 13.8251 34.1125C13.7417 33.5042 13.7667 32.7083 13.9001 31.725C13.9834 31.175 14.2042 30.7542 14.5626 30.4625C14.9209 30.1708 15.3417 29.9417 15.8251 29.775C16.3084 29.6083 16.8126 29.4542 17.3376 29.3125C17.8626 29.1708 18.3251 28.975 18.7251 28.725C19.0417 28.525 19.2834 28.3375 19.4501 28.1625C19.6167 27.9875 19.7334 27.8083 19.8001 27.625C19.8667 27.4417 19.9001 27.25 19.9001 27.05C19.9001 26.85 19.8917 26.6333 19.8751 26.4C19.8417 26.05 19.7209 25.775 19.5126 25.575C19.3042 25.375 19.0834 25.175 18.8501 24.975C18.7167 24.875 18.6001 24.725 18.5001 24.525C18.4001 24.325 18.3167 24.1333 18.2501 23.95C18.1667 23.7333 18.1001 23.4917 18.0501 23.225C17.9334 23.1917 17.8251 23.1417 17.7251 23.075C17.6417 23.0083 17.5501 22.9083 17.4501 22.775C17.3501 22.6417 17.2584 22.4417 17.1751 22.175C17.0917 21.925 17.0626 21.6917 17.0876 21.475C17.1126 21.2583 17.1584 21.075 17.2251 20.925C17.2917 20.7417 17.3834 20.5833 17.5001 20.45C17.5001 19.8833 17.5334 19.3167 17.6001 18.75C17.6667 18.2667 17.7709 17.75 17.9126 17.2C18.0542 16.65 18.2834 16.1583 18.6001 15.725C18.9001 15.3083 19.2209 14.9667 19.5626 14.7C19.9042 14.4333 20.2584 14.225 20.6251 14.075C20.9917 13.925 21.3584 13.8208 21.7251 13.7625C22.0917 13.7042 22.4417 13.675 22.7751 13.675C23.2084 13.675 23.6251 13.725 24.0251 13.825C24.4251 13.925 24.7959 14.0583 25.1376 14.225C25.4792 14.3917 25.7834 14.5792 26.0501 14.7875C26.3167 14.9958 26.5334 15.2083 26.7001 15.425C27.0834 15.9083 27.3626 16.4417 27.5376 17.025C27.7126 17.6083 27.8417 18.1583 27.9251 18.675C28.0084 19.275 28.0417 19.875 28.0251 20.475C28.1251 20.5583 28.2084 20.6583 28.2751 20.775C28.3417 20.875 28.3917 21.0083 28.4251 21.175C28.4584 21.3417 28.4584 21.55 28.4251 21.8C28.3917 22.1167 28.3251 22.3667 28.2251 22.55C28.1251 22.7333 28.0167 22.875 27.9001 22.975C27.7667 23.0917 27.6251 23.175 27.4751 23.225Z"
                                    fill="url(#paint0_linear_1742_471)" />
                            </g>
                            <defs>
                                <filter id="filter0_d_1742_471" x="9.77698" y="11.675" width="34.4481" height="34.575"
                                    filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                    <feColorMatrix in="SourceAlpha" type="matrix"
                                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                    <feOffset dx="2" dy="4" />
                                    <feGaussianBlur stdDeviation="3" />
                                    <feComposite in2="hardAlpha" operator="out" />
                                    <feColorMatrix type="matrix"
                                        values="0 0 0 0 0.996078 0 0 0 0 0.423529 0 0 0 0 0.0901961 0 0 0 0.4 0" />
                                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1742_471" />
                                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1742_471"
                                        result="shape" />
                                </filter>
                                <linearGradient id="paint0_linear_1742_471" x1="25.001" y1="13.675" x2="25.001" y2="36.25"
                                    gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#FF995E" />
                                    <stop offset="1" stop-color="#FE6C17" />
                                </linearGradient>
                            </defs>
                        </svg>
                    </view>
                    <view style="margin-left: 12px;">出售</view>
                </view>
                <view style="display: flex;flex-direction: row;">
                    <view>11</view>
                    <view>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path
                                d="M5.52864 12.4714C5.26829 12.2111 5.26829 11.789 5.52864 11.5286L9.05723 8L5.52864 4.47141C5.26829 4.21106 5.26829 3.78895 5.52864 3.5286C5.78899 3.26825 6.2111 3.26825 6.47144 3.5286L10.4714 7.5286C10.7318 7.78895 10.7318 8.21106 10.4714 8.47141L6.47145 12.4714C6.2111 12.7318 5.78899 12.7318 5.52864 12.4714Z"
                                fill="#292C39" fill-opacity="0.2" />
                        </svg>
                    </view>
                </view>
            </view>
            <!-- 我的房源 -->
            <view
                style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;margin-top: 16px;"
                v-if="act == 1">
                <view style="display: flex;flex-direction: row;align-items: center;">
                    <view>
                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 50 50" fill="none">
                            <circle cx="25" cy="25" r="25" fill="#FE6C17" fill-opacity="0.2" />
                            <g filter="url(#filter0_d_1742_471)">
                                <path
                                    d="M31.1251 35.4C30.4251 35.4 29.7626 35.2667 29.1376 35C28.5126 34.7333 27.9667 34.3667 27.5001 33.9C27.0334 33.4333 26.6667 32.8875 26.4001 32.2625C26.1334 31.6375 26.0001 30.975 26.0001 30.275C26.0001 29.575 26.1334 28.9125 26.4001 28.2875C26.6667 27.6625 27.0334 27.1208 27.5001 26.6625C27.9667 26.2042 28.5126 25.8417 29.1376 25.575C29.7626 25.3083 30.4251 25.175 31.1251 25.175C31.8251 25.175 32.4876 25.3083 33.1126 25.575C33.7376 25.8417 34.2792 26.2042 34.7376 26.6625C35.1959 27.1208 35.5584 27.6625 35.8251 28.2875C36.0917 28.9125 36.2251 29.575 36.2251 30.275C36.2251 30.975 36.0917 31.6375 35.8251 32.2625C35.5584 32.8875 35.1959 33.4333 34.7376 33.9C34.2792 34.3667 33.7376 34.7333 33.1126 35C32.4876 35.2667 31.8251 35.4 31.1251 35.4ZM33.6501 29.45H31.8001V27.725C31.8001 27.4917 31.7251 27.2917 31.5751 27.125C31.4251 26.9583 31.2334 26.875 31.0001 26.875C30.7667 26.875 30.5834 26.9583 30.4501 27.125C30.3167 27.2917 30.2501 27.4917 30.2501 27.725V29.45H28.6251C28.3917 29.45 28.1917 29.5333 28.0251 29.7C27.8584 29.8667 27.7751 30.0667 27.7751 30.3C27.7751 30.5333 27.8584 30.7083 28.0251 30.825C28.1917 30.9417 28.3917 31 28.6251 31H30.2501V32.775C30.2501 33.0083 30.3167 33.2083 30.4501 33.375C30.5834 33.5417 30.7667 33.625 31.0001 33.625C31.2334 33.625 31.4251 33.5417 31.5751 33.375C31.7251 33.2083 31.8001 33.0083 31.8001 32.775V31H33.6501V31.05C33.8834 31.05 34.0834 30.9833 34.2501 30.85C34.4167 30.7167 34.5001 30.5333 34.5001 30.3C34.5001 30.0667 34.4167 29.8667 34.2501 29.7C34.0834 29.5333 33.8834 29.45 33.6501 29.45ZM27.4751 23.225C27.4084 23.4917 27.3417 23.7333 27.2751 23.95C27.2084 24.1333 27.1251 24.3208 27.0251 24.5125C26.9251 24.7042 26.8167 24.85 26.7001 24.95C26.5501 25.0667 26.4459 25.1917 26.3876 25.325C26.3292 25.4583 26.2792 25.5958 26.2376 25.7375C26.1959 25.8792 26.1501 26.025 26.1001 26.175C26.0501 26.325 25.9667 26.475 25.8501 26.625C25.4667 27.125 25.1792 27.6083 24.9876 28.075C24.7959 28.5417 24.6626 28.9917 24.5876 29.425C24.5126 29.8583 24.4917 30.2875 24.5251 30.7125C24.5584 31.1375 24.6251 31.55 24.7251 31.95C24.7917 32.25 24.8917 32.5625 25.0251 32.8875C25.1584 33.2125 25.3542 33.5458 25.6126 33.8875C25.8709 34.2292 26.2001 34.5625 26.6001 34.8875C27.0001 35.2125 27.5001 35.525 28.1001 35.825C27.7001 35.9083 27.2334 35.9833 26.7001 36.05C26.2501 36.1 25.6959 36.1458 25.0376 36.1875C24.3792 36.2292 23.6251 36.25 22.7751 36.25C22.3417 36.25 21.8376 36.2333 21.2626 36.2C20.6876 36.1667 20.0959 36.125 19.4876 36.075C18.8792 36.025 18.2792 35.9667 17.6876 35.9C17.0959 35.8333 16.5542 35.7625 16.0626 35.6875C15.5709 35.6125 15.1542 35.5333 14.8126 35.45C14.4709 35.3667 14.2584 35.2917 14.1751 35.225C14.0251 35.0917 13.9084 34.7208 13.8251 34.1125C13.7417 33.5042 13.7667 32.7083 13.9001 31.725C13.9834 31.175 14.2042 30.7542 14.5626 30.4625C14.9209 30.1708 15.3417 29.9417 15.8251 29.775C16.3084 29.6083 16.8126 29.4542 17.3376 29.3125C17.8626 29.1708 18.3251 28.975 18.7251 28.725C19.0417 28.525 19.2834 28.3375 19.4501 28.1625C19.6167 27.9875 19.7334 27.8083 19.8001 27.625C19.8667 27.4417 19.9001 27.25 19.9001 27.05C19.9001 26.85 19.8917 26.6333 19.8751 26.4C19.8417 26.05 19.7209 25.775 19.5126 25.575C19.3042 25.375 19.0834 25.175 18.8501 24.975C18.7167 24.875 18.6001 24.725 18.5001 24.525C18.4001 24.325 18.3167 24.1333 18.2501 23.95C18.1667 23.7333 18.1001 23.4917 18.0501 23.225C17.9334 23.1917 17.8251 23.1417 17.7251 23.075C17.6417 23.0083 17.5501 22.9083 17.4501 22.775C17.3501 22.6417 17.2584 22.4417 17.1751 22.175C17.0917 21.925 17.0626 21.6917 17.0876 21.475C17.1126 21.2583 17.1584 21.075 17.2251 20.925C17.2917 20.7417 17.3834 20.5833 17.5001 20.45C17.5001 19.8833 17.5334 19.3167 17.6001 18.75C17.6667 18.2667 17.7709 17.75 17.9126 17.2C18.0542 16.65 18.2834 16.1583 18.6001 15.725C18.9001 15.3083 19.2209 14.9667 19.5626 14.7C19.9042 14.4333 20.2584 14.225 20.6251 14.075C20.9917 13.925 21.3584 13.8208 21.7251 13.7625C22.0917 13.7042 22.4417 13.675 22.7751 13.675C23.2084 13.675 23.6251 13.725 24.0251 13.825C24.4251 13.925 24.7959 14.0583 25.1376 14.225C25.4792 14.3917 25.7834 14.5792 26.0501 14.7875C26.3167 14.9958 26.5334 15.2083 26.7001 15.425C27.0834 15.9083 27.3626 16.4417 27.5376 17.025C27.7126 17.6083 27.8417 18.1583 27.9251 18.675C28.0084 19.275 28.0417 19.875 28.0251 20.475C28.1251 20.5583 28.2084 20.6583 28.2751 20.775C28.3417 20.875 28.3917 21.0083 28.4251 21.175C28.4584 21.3417 28.4584 21.55 28.4251 21.8C28.3917 22.1167 28.3251 22.3667 28.2251 22.55C28.1251 22.7333 28.0167 22.875 27.9001 22.975C27.7667 23.0917 27.6251 23.175 27.4751 23.225Z"
                                    fill="url(#paint0_linear_1742_471)" />
                            </g>
                            <defs>
                                <filter id="filter0_d_1742_471" x="9.77698" y="11.675" width="34.4481" height="34.575"
                                    filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                    <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                    <feColorMatrix in="SourceAlpha" type="matrix"
                                        values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                    <feOffset dx="2" dy="4" />
                                    <feGaussianBlur stdDeviation="3" />
                                    <feComposite in2="hardAlpha" operator="out" />
                                    <feColorMatrix type="matrix"
                                        values="0 0 0 0 0.996078 0 0 0 0 0.423529 0 0 0 0 0.0901961 0 0 0 0.4 0" />
                                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1742_471" />
                                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1742_471"
                                        result="shape" />
                                </filter>
                                <linearGradient id="paint0_linear_1742_471" x1="25.001" y1="13.675" x2="25.001" y2="36.25"
                                    gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#FF995E" />
                                    <stop offset="1" stop-color="#FE6C17" />
                                </linearGradient>
                            </defs>
                        </svg>
                    </view>
                    <view style="margin-left: 12px;">已跟进</view>
                </view>
                <view style="display: flex;flex-direction: row;">
                    <view>11</view>
                    <view>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path
                                d="M5.52864 12.4714C5.26829 12.2111 5.26829 11.789 5.52864 11.5286L9.05723 8L5.52864 4.47141C5.26829 4.21106 5.26829 3.78895 5.52864 3.5286C5.78899 3.26825 6.2111 3.26825 6.47144 3.5286L10.4714 7.5286C10.7318 7.78895 10.7318 8.21106 10.4714 8.47141L6.47145 12.4714C6.2111 12.7318 5.78899 12.7318 5.52864 12.4714Z"
                                fill="#292C39" fill-opacity="0.2" />
                        </svg>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {
            navIndex: 0,// 本周
            act: 0,
            navs: [
                { id: 0, name: "全部" },
                { id: 1, name: "今天" },
                { id: 2, name: "昨天" },
                { id: 3, name: "本周" },
                { id: 4, name: "上周" },
                { id: 5, name: "本月" },
                { id: 6, name: "上月" },
            ],
            houseList: [
                { id: 0, name: '我的新房' },
                { id: 1, name: '我的房源' },
            ]
        }
    },
    methods: {
        // tab互斥效果
        changeAct(item) {
            console.log(item.id);
            // 激活样式是当前点击的对应下标--list中对应id
            this.act = item.id;
        },
        checkIndex(index) {
            console.log(index)
            this.navIndex = index;
        },
        scroll: function (e) {
            // console.log(e)
            // this.old.scrollTop = e.detail.scrollTop
        }
    }
}


</script>
<style lang="scss">
.my-style-one {
    padding: 6px 10px;
    // border-radius: 0px 4px 4px 0px;
    border: 0.5px solid rgba(41, 44, 57, 0.20);
    background: var(--unnamed, #FFF);
    color: var(--unnamed, rgba(41, 44, 57, 0.40));
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

// 我的房客
.active {
    padding: 7px 10px;
    background: #DAE8FD;
    color: #488AF6;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    border: none;
}

// 本周
.weeked {
    padding: 3px 8px;
    border-radius: 4px;
    margin: 0 3px;
    font-size: 12px;
    background: #F8F8F8;
    display: inline-block;
		/* 必要，导航栏才能横向*/
}

.activite {
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    background: rgba(72, 138, 246, 0.20);
}

</style>