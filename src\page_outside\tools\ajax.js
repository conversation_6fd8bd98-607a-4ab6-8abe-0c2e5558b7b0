/**
 * <AUTHOR>
 * @date 2020-04-21 10:09:10
 * @desc 公共请求方法
 */
import config from "@/page_outside/config/index";
import { navigateTo, showModal } from "./index";
// import store from "@/page_outside/store/index";
let isLogining = false
let timeout = 60000
const ajax = {
  get: function (url, params, doSuccess, doFail, options = {}) {

    if (url.indexOf('/admin/personnelMatters/inheritMember') != -1 || url.indexOf('/admin/personnelMatters/inheritMemberOnJob') != -1) {
      timeout = 600000
    }
    const header = {
      "content-type": "application/json",
    };
    header.Authorization = uni.getStorageSync("wxwork_token") ? ("Bearer " + uni.getStorageSync("wxwork_token")) : '';
    params.website_id = uni.getStorageSync("website_id") || 176;
    // #ifdef MP-WEIXIN
    header.From = "weixinxiaochengxuCrm"
    // #endif
    let apiBase;
    apiBase = config.appApi;
    uni.request({
      url: apiBase + url,
      data: params,
      header,
      method: "GET",
      timeout,
      success: (res) => {
        if (res.statusCode === 500) {
          uni.showToast({
            title: res.data.message || "服务器内部错误",
            icon: "none",
            duration: 2000,
          });
          doFail && doFail(res);
          return;
        }
        if (res.statusCode === 404) {
          uni.showToast({
            title: res.data.message || "请求不存在",
            icon: "none",
            duration: 2000,
          });
          doFail && doFail(res);
          return;
        }
        // uni.setStorageSync("loginUrl", window.location.href);
        if (
          res.statusCode === 401
        ) {
          if (isLogining) {
            return;
          }
          isLogining = true;
          setTimeout(() => {
            isLogining = false;
          }, 1500)
          uni.removeStorageSync("wxwork_token");
          // if (store.state.is_logining) return 
          // store.state.is_logining = true
          // let pathname = window.location.pathname;
          // // let pathlist = ["/fenxiao/customer/mark", "/fenxiao/customer/index"];
          // if (
          //   pathname.indexOf("/fenxiao/customer") == -1 &&
          //   pathname.indexOf("/fenxiao/house") == -1 &&
          //   pathname.indexOf("/fenxiao/outbound") == -1
          // ) {
          // 如果路径包括/fenxiao/customer则不需要弹出授权跳转登录的弹出框
          uni.hideLoading()
          uni.navigateTo({ url: '/pages/user/phone_login' })
          // showModal({
          //   title: "登录提示",
          //   content: "当前操作需要登录，是否去登录？",
          //   cancelText: "暂不登录",
          //   confirmText: "去登录",
          //   confirm: (result) => {
          //     console.log(result);
          //     // if (result.confirm) {
          //     //    navigateTo("/pages/user/phone_login");
          //     // }

          //   },
          // });
          return
        } else {
          if (res.data?.message) {
            uni.showToast({
              title: res.data.message,
              icon: "none",
              duration: 2000,
            });
          }
        }
        if (options.disableAutoHandle) {
          // 如果禁用自动处理code则直接执行请求成功回调
          doSuccess(res);
          return;
        }
        doSuccess(res);
      },
      fail: (err) => {
        if (doFail) {
          doFail(err);
          return;
        }
        console.log(apiBase + url);
        console.log(JSON.stringify(header));
        console.log(JSON.stringify(err));
        console.log(JSON.stringify(params));
        console.log(JSON.stringify(err))
        uni.showToast({
          title: "网络链接失败，请重试",
          icon: "none",
          duration: 2000,
        });
      },
    });
  },
  post: function (url, params, doSuccess, doFail, options = {}) {
    if (url.indexOf('/admin/personnelMatters/inheritMember') != -1 || url.indexOf('/admin/personnelMatters/inheritMemberOnJob') != -1) {
      timeout = 600000
      console.log(url, '测试-');
    }
    // 判断企业微信环境且项目目录在customer下走企微token
    const header = {
      "content-type": "application/json",
    };

    header.Authorization = uni.getStorageSync("wxwork_token") ? ("Bearer " + uni.getStorageSync("wxwork_token")) : '';
    params.website_id = uni.getStorageSync("website_id") || 176;
    // #ifdef MP-WEIXIN
    header.From = "weixinxiaochengxuCrm"
    // #endif

    header.platform = uni.getSystemInfoSync().platform
    let apiBase;
    apiBase = config.appApi;
    uni.request({
      url: apiBase + url,
      data: params,
      header,
      method: "POST",
      timeout,
      success: (res) => {
        if (res.statusCode === 500) {
          uni.showToast({
            title: res.data.message || "服务器内部错误",
            icon: "none",
            duration: 2000,
          });
          doFail && doFail(res);
          return;
        }

        if (options.disableAutoHandle) {
          doSuccess(res);
          return;
        }
        // let loginurl = window.location.pathname == "/fenxiao/user/login";
        // #ifdef H5
        if (res.statusCode === 401) {
          uni.removeStorageSync("wxwork_token");
          // 如果路径包括/fenxiao/customer则不需要弹出授权跳转登录的弹出框
          showModal({
            title: "登录提示",
            content: "当前操作需要登录，是否去登录？",
            cancelText: "暂不登录",
            confirmText: "去登录",
            confirm: () => {
              navigateTo("/pages/user/phone_login");
            },
          });
          return;
        }
        // #endif

        doSuccess(res);
        if (res.statusCode === 403) {
          showModal({
            title: "绑定手机号码",
            content: "当前操作需要绑定手机号码，请绑定后进行操作",
            cancelText: "暂不绑定",
            confirmText: "去绑定",
            confirm: () => {
              navigateTo("/user/change_tel");
            },
          });
        } else if (res.statusCode != 200) {
          if (res.data?.message) {
            uni.showToast({
              title: res.data.message,
              icon: "none",
              duration: 2000,
            });
          }
        }
      },
      fail: (err) => {
        if (doFail) {
          doFail(err);
          return;
        }
        // console.log(JSON.stringify(err))
        uni.showToast({
          title: "网络链接失败，请重试",
          icon: "none",
          duration: 2000,
        });
      },
    });
  },
};

module.exports = ajax;
