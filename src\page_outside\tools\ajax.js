/**
 * <AUTHOR>
 * @date 2020-04-21 10:09:10
 * @desc 公共请求方法
 */
import config from "@/page_outside/config/index";
import { navigateTo, showModal, isWxWork } from "./index";
let timeout = 60000
const ajax = {
  get: function (url, params, doSuccess, doFail, options = {}) {
    if (url.indexOf('/admin/personnelMatters/inheritMember') != -1 || url.indexOf('/admin/personnelMatters/inheritMemberOnJob') != -1) {
      timeout = 600000
    }
    const header = {
      "content-type": "application/json",
    };
    // 判断项目目录在customer下走企微token
    // if (["wxwork", "com-wx-pc"].includes(isWxWork())) {
    if (
      window.location.pathname.includes("/fenxiao/customer") ||
      window.location.pathname.includes("/fenxiao/house") ||
      window.location.pathname.includes("/fenxiao/outbound") ||
      window.location.pathname.includes("/fenxiao/client/listMy") ||
      window.location.pathname.includes("/fenxiao/report/myReport")
    ) {
      header.Authorization = uni.getStorageSync("wxwork_token") ? ("Bearer " + uni.getStorageSync("wxwork_token")) : '';
      params.website_id = uni.getStorageSync("wxwork_id");

    } else {

      header.Authorization = uni.getStorageSync("token" + config.website_id) ? ("Bearer " + uni.getStorageSync("token" + config.website_id)) : '';
      if (uni.getStorageSync("website_id" + config.website_id)) {
        params.website_id = uni.getStorageSync(
          "website_id" + config.website_id
        );
      } else {
        params.website_id = "1";
      }
    }
    /**
     * header.ClientType 1：微信小程序，2：微信公众号，3：h5，4：PC，5：百度小程序，8：安卓 9 ios
     */

    // #ifdef H5
    var ua = navigator.userAgent.toLowerCase();
    if (ua.match(/wxwork/i) && ua.match(/wxwork/i)[0] === "wxwork") {
      // 企业微信
      header.From = "qiyeweixin"
    } else if (ua.match(/MicroMessenger/i) && ua.match(/MicroMessenger/i)[0] === "micromessenger") {
      // 如果是微信浏览器
      header.ClientType = 2;
      // 微信公众号 
      header.From = "weixingongzhonghao"
    } else {
      // 手机浏览器
      header.From = "shoujiliulanqi"
      header.ClientType = 3;
    }
    // 手机端平台 ios、android
    header.platform = uni.getSystemInfoSync().platform
    header.authway = uni.getStorageSync("auth_way") || 0;
    // #endif
    let apiBase;
    // #ifdef H5
    apiBase = config.h5Api;
    // #endif
    //

    //webview
    const wxwork_headerFrom = uni.getStorageSync('wxwork_headerFrom')
    if(wxwork_headerFrom){
      header.From = wxwork_headerFrom;
      header.Authorization = uni.getStorageSync("wxwork_token") ? ("Bearer " + uni.getStorageSync("wxwork_token")) : '';
      params.website_id = uni.getStorageSync("wxwork_id");
    }

    uni.request({
      url: apiBase + url,
      data: params,
      header,
      method: "GET",
      timeout,
      success: (res) => {
        if (res.statusCode === 500) {
          // uni.removeStorageSync("wxwork_token");
          uni.showToast({
            title: res.data.message || "服务器内部错误",
            icon: "none",
            duration: 2000,
          });
          doFail && doFail(res);
          return;
        }
        if (options.disableAutoHandle) {
          // 如果禁用自动处理code则直接执行请求成功回调
          doSuccess(res);
          return;
        }
        if (res.statusCode === 404) {
          uni.showToast({
            title: res.data.message || "请求不存在",
            icon: "none",
            duration: 2000,
          });
          doFail && doFail(res);
          return;
        }
        uni.setStorageSync("loginUrl", window.location.href);
        if (
          res.statusCode === 401
        ) {
          if (window.location.pathname.indexOf("qywx/qy_loading") === -1 // 判断是否为企业微信启动页如果是的话不弹出提示登录
          ) {
            uni.removeStorageSync("token" + config.website_id);
            uni.removeStorageSync("wxwork_token");
            let pathname = window.location.pathname;
            // let pathlist = ["/fenxiao/customer/mark", "/fenxiao/customer/index"];
            if (
              pathname.indexOf("/fenxiao/customer") == -1 &&
              pathname.indexOf("/fenxiao/house") == -1 &&
              pathname.indexOf("/fenxiao/outbound") == -1 &&
              pathname.indexOf("/fenxiao/client/listMy") == -1 &&
              pathname.indexOf("/fenxiao/report/myReport") == -1 &&
              pathname.indexOf("/fenxiao/report/list") == -1 &&
              pathname.indexOf("/fenxiao/wisdomWork") == -1
            ) {
              // 如果路径包括/fenxiao/customer则不需要弹出授权跳转登录的弹出框
              showModal({
                title: "登录提示",
                content: "当前操作需要登录，是否去登录？",
                cancelText: "暂不登录",
                confirmText: "去登录",
                confirm: () => {
                  navigateTo("/user/phone_login");
                },
              });
            } else {
              // sessionStorage（临时存储） ：为每一个数据源维持一个存储区域，在浏览器的此标签页打开期间存在，包括此标签页的页面重新加载
              uni.removeStorageSync("token" + config.website_id);
              uni.removeStorageSync("wxwork_token");
              sessionStorage.removeItem("isChange")
              setTimeout(() => {
                if (isWxWork() == 'wxwork') {
                  if (uni.getStorageSync("is_reload")) return
                  uni.setStorageSync("is_reload", "111")
                  window.location.replace(window.location.href)
                } else {
                  sessionStorage.setItem("backUrl", window.location.href)
                  window.location.replace("https://yun.tfcs.cn")
                }
                setTimeout(() => {
                  uni.removeStorageSync("is_reload")
                }, 1800);

              }, 200);

            }
            // uni.removeStorageSync("token" + config.website_id);
            // uni.removeStorageSync("wxwork_token");
            // let pathname = window.location.pathname;
            // // let pathlist = ["/fenxiao/customer/mark", "/fenxiao/customer/index"];
            // if (
            //   pathname.indexOf("/fenxiao/customer") == -1 &&
            //   pathname.indexOf("/fenxiao/house") == -1
            // ) {
            //   // 如果路径包括/fenxiao/customer则不需要弹出授权跳转登录的弹出框
            //   showModal({
            //     title: "登录提示",
            //     content: "当前操作需要登录，是否去登录？",
            //     cancelText: "暂不登录",
            //     confirmText: "去登录",
            //     confirm: () => {
            //       navigateTo("/user/phone_login");
            //     },
            //   });
          }
          return
        }

        doSuccess(res);
      },
      fail: (err) => {
        if (doFail) {
          doFail(err);
          return;
        }
        // console.log(apiBase + url);
        // console.log(JSON.stringify(header));
        // console.log(JSON.stringify(err));
        // console.log(JSON.stringify(params));
        // console.log(JSON.stringify(err))
        uni.showToast({
          title: "网络链接失败，请重试",
          icon: "none",
          duration: 2000,
        });
      },
    });
  },
  post: function (url, params, doSuccess, doFail, options = {}) {
    if (url.indexOf('/admin/personnelMatters/inheritMember') != -1 || url.indexOf('/admin/personnelMatters/inheritMemberOnJob') != -1) {
      timeout = 600000
    }
    // 判断企业微信环境且项目目录在customer下走企微token
    const header = {
      "content-type": "application/json",
    };
    // if (["wxwork", "com-wx-pc"].includes(isWxWork())) {
    if (
      window.location.pathname.includes("/fenxiao/customer") ||
      window.location.pathname.includes("/fenxiao/house") ||
      window.location.pathname.includes("/fenxiao/outbound") ||
      window.location.pathname.includes("/fenxiao/client/listMy") ||
      window.location.pathname.includes("/fenxiao/report/myReport")
    ) {
      header.Authorization = "Bearer " + uni.getStorageSync("wxwork_token");
      params.website_id = uni.getStorageSync("wxwork_id");
      // }
      // 如果是企业微信环境
    } else {
      header.Authorization =
        "Bearer " + uni.getStorageSync("token" + config.website_id);
      if (uni.getStorageSync("website_id" + config.website_id)) {
        params.website_id = uni.getStorageSync(
          "website_id" + config.website_id
        );
      } else {
        params.website_id = "1";
      }
    }
    // #ifdef H5
    var ua = navigator.userAgent.toLowerCase();
    if (ua.match(/MicroMessenger/i) === "micromessenger") {
      // 如果是微信浏览器
      header.ClientType = 2;
      // 微信公众号 
      header.From = "weixingongzhonghao"
    } else if (isWxWork() == 'wxwork') {
      // 企业微信
      header.From = "qiyeweixin"
    } else {
      // 手机浏览器
      header.From = "shoujiliulanqi"
      header.ClientType = 3;
    }
    // #endif
    header.platform = uni.getSystemInfoSync().platform
    let apiBase;
    // #ifdef H5
    apiBase = config.h5Api;
    // #endif

    //webview
    const wxwork_headerFrom = uni.getStorageSync('wxwork_headerFrom')
    if(wxwork_headerFrom){
      header.From = wxwork_headerFrom;
      header.Authorization = uni.getStorageSync("wxwork_token") ? ("Bearer " + uni.getStorageSync("wxwork_token")) : '';
      params.website_id = uni.getStorageSync("wxwork_id");
    }

    uni.request({
      url: apiBase + url,
      data: params,
      header,
      method: "POST",
      timeout,
      success: (res) => {
        if (res.statusCode === 500) {
          uni.showToast({
            title: res.data.message || "服务器内部错误",
            icon: "none",
            duration: 2000,
          });
          doFail && doFail(res);
          return;
        }

        if (options.disableAutoHandle) {
          doSuccess(res);
          return;
        }
        // let loginurl = window.location.pathname == "/fenxiao/user/login";
        // #ifdef H5
        if (res.statusCode === 401) {
          if (window.location.pathname.indexOf("qywx/qy_loading") === -1 // 判断是否为企业微信启动页如果是的话不弹出提示登录
          ) {
            uni.removeStorageSync("token" + config.website_id);
            uni.removeStorageSync("wxwork_token");
            let pathname = window.location.pathname;
            // let pathlist = ["/fenxiao/customer/mark", "/fenxiao/customer/index"];
            if (
              pathname.indexOf("/fenxiao/customer") == -1 &&
              pathname.indexOf("/fenxiao/house") == -1 &&
              pathname.indexOf("/fenxiao/outbound") == -1 &&
              pathname.indexOf("/fenxiao/client/listMy") == -1 &&
              pathname.indexOf("/fenxiao/report/myReport") == -1 &&
              pathname.indexOf("/fenxiao/report/list") == -1 &&
              pathname.indexOf("/fenxiao/wisdomWork") == -1
            ) {
              // 如果路径包括/fenxiao/customer则不需要弹出授权跳转登录的弹出框
              showModal({
                title: "登录提示",
                content: "当前操作需要登录，是否去登录？",
                cancelText: "暂不登录",
                confirmText: "去登录",
                confirm: () => {
                  navigateTo("/user/phone_login");
                },
              });
            } else {
              uni.removeStorageSync("token" + config.website_id);
              uni.removeStorageSync("wxwork_token");
              sessionStorage.removeItem("isChange")
              setTimeout(() => {
                if (uni.getStorageSync("is_reload")) return
                uni.setStorageSync("is_reload", "111")
                if (isWxWork() == 'wxwork') {
                  window.location.replace(window.location.href)
                } else {
                  localStorage.setItem("backUrl", window.location.href)
                  window.location.replace("https://yun.tfcs.cn")
                }

                setTimeout(() => {
                  uni.removeStorageSync("is_reload")
                }, 1800);

              }, 200);

            }
            // uni.removeStorageSync("token" + config.website_id);
            // uni.removeStorageSync("wxwork_token");
            // let pathname = window.location.pathname;
            // // let pathlist = ["/fenxiao/customer/mark", "/fenxiao/customer/index"];
            // if (
            //   pathname.indexOf("/fenxiao/customer") == -1 &&
            //   pathname.indexOf("/fenxiao/house") == -1
            // ) {
            //   // 如果路径包括/fenxiao/customer则不需要弹出授权跳转登录的弹出框
            //   showModal({
            //     title: "登录提示",
            //     content: "当前操作需要登录，是否去登录？",
            //     cancelText: "暂不登录",
            //     confirmText: "去登录",
            //     confirm: () => {
            //       navigateTo("/user/phone_login");
            //     },
            //   });
          }
          return;
        }
        // #endif

        doSuccess(res);
        if (res.statusCode === 403) {
          showModal({
            title: "绑定手机号码",
            content: "当前操作需要绑定手机号码，请绑定后进行操作",
            cancelText: "暂不绑定",
            confirmText: "去绑定",
            confirm: () => {
              navigateTo("/user/change_tel");
            },
          });
        }
      },
      fail: (err) => {
        if (doFail) {
          doFail(err);
          return;
        }
        // console.log(JSON.stringify(err))
        uni.showToast({
          title: "网络链接失败，请重试",
          icon: "none",
          duration: 2000,
        });
      },
    });
  },
};

module.exports = ajax;
