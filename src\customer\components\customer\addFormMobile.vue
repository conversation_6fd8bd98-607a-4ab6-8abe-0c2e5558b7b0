<template>
    <view class="mobiles">
        <view class="mobiles-item" v-for="(item,index) in curList" :key="index">
            <addFormTpl v-for="(form, i) in item" :key="form.name" :form="form" v-model="curList[index][i].value" style="width: 100%;">
                <template #append>
                    <view class="mobiles-item-op" v-if="form.name == 'mobile'">
                        <image :src="'/icons/del-danger.png' | imgDomain" class="del-icon" @click="removeRow(index)" v-if="!form.isMainNumber"/>
                        <image :src="'/fabu/fabu/Vector.png' | imgDomain" class="sort-icon" mode="widthFix" v-if="curList.length > 1" @click="orderRow(index)"/>
                    </view>
                </template>
            </addFormTpl>
        </view>

        <addFormTpl :form="addPlaceForms" v-if="addAble">
            <template #content>
                <view class="add-btn" @click="addRow">
                    <icons type="a-tianjia1x" size="32" color="#2d84fb"></icons> 
                    <text class="text">添加手机号</text>
                </view>
            </template>
        </addFormTpl>
    </view>
</template>
<script>
import icons from '@/components/my-icon'
import addFormTpl from './addFormTpl.vue'
export default {
    props: {
        value: {type: Array, default: ()=>[]},
        options: { type: Array, default: ()=>[] },
        addAble: {type: Boolean, default: false}
    },
    components: {
        icons,
        addFormTpl
    },
    data(){
        return{
            list: [],
            addPlaceForms: { title: '' }
        }
    },
    computed: {
        curList(){
            return this.list.filter(item =>  {
                const mobile = item.find(e => e.name == "mobile")
                return mobile && mobile.is_del == 0 || false;
            })
        }
    },
    watch: {
        value: {
            handler(val){
                if(JSON.stringify(val) != JSON.stringify(this.list)){
                    this.list = val;
                }
            },
            immediate: true
        },
        list: {
            handler(val){
                this.$emit('input', val)
            },
            deep: true
        }
    },
    methods:{
        addRow(){
            this.list.push(JSON.parse(JSON.stringify(this.options)))
        },
        removeRow(i){
            let index = this.getListIndex(i);
            if(index !== -1){
                const mobile = this.list[index].find(e => e.name == "mobile") || {}
                if(mobile.id){
                    mobile.is_del = 1
                }else{
                    this.list.splice(index, 1);
                }
            }
        },
        orderRow(index){
            if(this.curList.length === index + 1){
                index--;
            }
            let curIndex = this.getListIndex(index),
                nextIndex = this.getListIndex(index + 1);
            this.list.splice(nextIndex, 0, this.list.splice(curIndex, 1)[0])
        },
        getListIndex(index){
            let i = 0, j = 0;
            for(const item of this.list){
                const mobile = item.find(e => e.name == "mobile");
                if(mobile && mobile.is_del == 0){
                    if(j++ == index){
                        break;
                    }
                }
                i++;
            }
            return i;
        }
    }
}
</script>

<style lang="scss" scoped>
.mobiles-item{
    display: flex;
    flex-direction: column;
    .mobiles-item-op{
        display: flex;
        flex-direction: row;
        align-items: center;
        .sort-icon{
            width: 40rpx;
            height: 40rpx;
            margin-left: 16rpx;
        }
        .del-icon{
            width: 40rpx;
            height: 40rpx;
            margin-left: 16rpx;
        }
    }
}

.add-btn{
    display: block;
    width: 100%;
    color: $color-primary;
    font-size: 28rpx;
    text-align: center;
    line-height: 1;
    padding: 20rpx 0;
    margin-top: 26rpx;
    border: 1px solid $color-primary;
    border-radius: 8rpx;
    .text{
        padding-left: 16rpx;
    }
}
</style>