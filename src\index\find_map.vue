<template>
  <view class="page">
    <!-- 页面内容 -->
    <view class="table">
      <table style="border-collapse: collapse">
        <thead class="thead flex-row" style="background-color: #f8f8f8">
          <tr class="flex-row items-center flex-1">
            <th class="flex-row flex-2 items-center">
              <view class="title"> 标题 </view>
            </th>
            <th class="flex-row flex-2 items-center">
              <view class="title"> 图片 </view>
            </th>
            <th class="flex-row flex-1 items-center">
              <view class="title"> 分享 </view>
            </th>
            <th class="flex-row flex-1 items-center">
              <view class="title"> 获客 </view>
            </th>
          </tr>
        </thead>
        <tbody class="tbody">
          <tr class="flex-row items-center flex-1" v-for="(item, index) in map" :key="index">

            <td class="flex-row flex-1 items-center">{{ item.title }}</td>
            <td class="flex-row flex-2 items-center">
              <!-- <view class="user flex-row items-center">-->
              <view class="header">
                <image :src="item.share_pic | imageFilter('w_80')"> </image>
                <!-- </view> -->
              </view>
            </td>
            <td class="flex-1 items-center share">
              <view class="share_c" @click="share(item)"> 分享</view>
            </td>
            <td class="flex-1 items-center share">
              <view class="share_c" @click="logs(item)"> 获客</view>
            </td>

            <!-- <td class="flex-row flex-1 items-center">{{ item.total_like }}</td> -->
          </tr>
        </tbody>
        <load-more :status="load_status"></load-more>
      </table>
    </view>
    <my-popup ref="remind" :show="showPop" height="500px" position="center" @hide="showPop = false">
      <view class="pop_c">
        <view class="pop_title"> 成员专属码 </view>
        <view class="pop_img">
          <image :src="imgCode" mode="widtFix"> </image>
        </view>
        <view class="pop_b"> 长按识别进入分享获客通道 </view>
        <view class="pop_b"> （授权手机号后分享给客户） </view>
      </view>
    </my-popup>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore";
import myPopup from '@/components/myPopup';
export default {
  components: { loadMore, myPopup },
  data () {
    return {
      map: [],
      params: {
        page: 1,
        rows: 50,
      },
      imgCode: "",
      showPop: false,
      load_status: ""
    };
  },
  methods: {
    // 方法
    getData () {
      if (this.params.page == 1) {
        this.list = []
      }
      this.load_status = "loading";
      this.$ajax.get('/admin/map_plugin/select_map', this.params, res => {
        console.log(res);
        if (res.statusCode == 200) {
          this.map = this.map.concat(res.data.maps)
          if (res.data.maps.length < this.params.rows) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'more'
          }
        }
      })
    },
    share (item) {
      this.$ajax.get(`/admin/map_plugin/share_code_new/${item.id}`, {}, res => {
        if (res.statusCode == 200) {
          this.imgCode = res.data
          this.showPop = true
        }
      })
    },
    logs (item) {
      this.$navigateTo(`/customer/customer_logs?id=${item.id}`)
    }
  },
  onLoad () {
    // 生命周期钩子函数
    this.getData()
  },
  onReachBottom () {
    if (this.load_status == 'more') {
      this.params.page++
      this.getData()
    }
  }
};
</script>

<style lang="scss" scoped>
.flex-2 {
    -webkit-box-flex: 2;
    -webkit-flex: 2;
    flex-grow: 2;
    flex-shrink: 1;
    flex-basis: 0%;
    flex: 1;
}
.page {
  padding-bottom: 160rpx;
  /* 样式 */
  .thead {
    padding: 24rpx 48rpx;
    text-align: center;
    width: 100vw;
    tr {
      // padding: 0 48rpx;
      th {
        text-align: center;
        justify-content: center;
        color: #999999;
      }
    }
    .icon {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      height: 100%;
      .up {
        width: 0;
        height: 0;
        border-left: 8rpx solid transparent;
        border-right: 8rpx solid transparent;
        border-bottom: 8rpx solid #d9d9d9;
        &.active {
          border-bottom: 8rpx solid #2d84fb;
        }
      }
      .down {
        margin-top: 7rpx;
        width: 0;
        height: 0;
        border-left: 8rpx solid transparent;
        border-right: 8rpx solid transparent;
        border-top: 8rpx solid #d9d9d9;
        &.active {
          border-top: 8rpx solid #2d84fb;
        }
      }
    }
  }
  .tbody {
    padding: 0 48rpx;
    text-align: center;
    width: 100vw;
    tr {
      padding: 32rpx 48rpx;
      td {
        text-align: center;
        justify-content: center;
        color: #999999;
      }
    }
    .header {
      width: 100rpx;
      height: 100rpx;
      border-radius: 5rpx;
      overflow: hidden;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .share {
      color: #2d84fb;
      .share_c {
        margin-bottom: 10rpx;
      }
    }
    .user {
      .header {
        position: relative;
        image {
          width: 96rpx;
          height: 96rpx;
          border-radius: 50%;
        }
      }
      .name {
        font-size: 32rpx;
        color: #000000;
      }
    }
  }
}
.pop_c {
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 80vw;
  margin: 0 auto;
  padding: 40rpx;
  border-radius: 20rpx;
  .pop_title {
    text-align: center;
    font-size: 32rpx;
  }
  .pop_img {
    width: 520rpx;
    height: 520rpx;
    margin: 30rpx auto;
    image {
      width: 100%;
    }
  }
  .pop_b {
    font-size: 24rpx;
    ~ .pop_b {
      margin-top: 15rpx;
    }
  }
}
</style>