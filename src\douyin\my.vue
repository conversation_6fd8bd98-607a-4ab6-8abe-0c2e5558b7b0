<template>
    <view class ='my'>
      <view class="my_top block flex-row ">
          <view class="prelogo">
            <image :src='myInfo.byte_user.avatar | imageFilter("w_80")' mode ="widthFix"></image>
          </view>
          <view class="info flex-1">
              <view class="my_name">
                {{myInfo.byte_user.name || myInfo.byte_user.nickname || ''  }}
              </view>
              <!-- <view class="my_info">
                置业顾问
              </view> -->
          </view>
          <view class="paiming ">
              <view class="paiming_title">
                当前排名
              </view>
              <view class="paiming_num">
                {{myInfo.byte_user.rand}}
              </view>
          </view>
      </view>
      <view class="my_middle block">
        <view class="middle_title flex-row items-center">
          <view class="middle_left flex-row items-center flex-1">
            <view class="icon">
              <image :src='"/yidongduan/douyin/chart.png" | imageFilter("w_80")' mode="widthFix"></image>
            </view>
            <view class="icon_name">
              数据看板
            </view>
            
          </view>
          <view class="middle_right flex-row items-center" @click ='show_drop = !show_drop'>
            <view class="right_name" >
              {{type=='day'?'今日新增':'昨日新增'}}
            </view>
            <view class="right_icon">
              <my-icon type='xiala' size="24rpx"></my-icon>
            </view>
            <view class="drop" v-if='show_drop' @click.prevent.stop ="changType">
              {{type=='day'?'昨日新增':'今日新增'}}
            </view>
          </view>

        </view>
        <view class="middle_content flex-row ">
          <view class="middle_content_item">
            <view class="middle_content_item_top">
              {{myInfo[type].new_issue||0}}
            </view>
            <view class="middle_content_item_bottom">
              发布
            </view>
          </view>
          <view class="middle_content_item">
            <view class="middle_content_item_top">
              {{myInfo[type].new_fans ||0}}
            </view>
            <view class="middle_content_item_bottom">
             粉丝
            </view>
          </view>
          <view class="middle_content_item">
            <view class="middle_content_item_top">
              {{myInfo[type].new_share  ||0}}
            </view>
            <view class="middle_content_item_bottom">
              分享
            </view>
          </view>
          <view class="middle_content_item">
            <view class="middle_content_item_top">
              {{myInfo[type].new_like ||0}}
            </view>
            <view class="middle_content_item_bottom">
              点赞
            </view>
          </view>
        </view>
      </view>
      <view class="my_bottom block">
        <view class="table">
          <table style="border-collapse: collapse">
        <thead class="thead flex-row" style="background-color: #f8f8f8">
          <tr class="flex-row items-center flex-1">
            <th class="flex-row flex-2 items-center">
              <view class="title"> 排行榜 </view>
            </th>
            <th class="flex-row flex-1 items-center" >
              <view class="title"> 发布 </view>
              <!-- <view class="icon">
                <view class="up"></view>
                <view class="down" :class="{ active: params.order_type == 2 }"></view>
              </view> -->
            </th>
            <th class="flex-row flex-1 items-center" >
              <view class="title"> 粉丝 </view>
              <!-- <view class="icon">
                <view class="up"></view>
                <view class="down" :class="{ active: params.order_type == 1 }"></view>
              </view> -->
            </th>
            <th class="flex-row flex-1 items-center" @click="sortList(4)">
              <view class="title"> 分享 </view>
              <!-- <view class="icon">
                <view class="up"></view>
                <view class="down" :class="{ active: params.order_type == 4 }"></view>
              </view> -->
            </th>
            <th class="flex-row flex-1 items-center" @click="sortList(3)">
              <view class="title"> 点赞 </view>
              <!-- <view class="icon">
                <view class="up"></view>
                <view class="down" :class="{ active: params.order_type == 3 }"></view>
              </view> -->
            </th>
          </tr>
        </thead>
        <tbody class="tbody">
          <tr class="flex-row items-center flex-1" v-for="(item, index) in list" :key="index">
            <td class="flex-row flex-2 items-center">
              <view class="user flex-row items-center">
                <view class="header">
                  <image :src="item.avatar | imageFilter('w_80')"> </image>
                </view>
                <view class="name">
                  {{ item.name }}
                </view>
              </view>
            </td>
            <td class="flex-row flex-1 items-center">{{ item.total_issue }}</td>
            <td class="flex-row flex-1 items-center">{{ item.total_fans }}</td>
            <td class="flex-row flex-1 items-center">{{ item.total_share }}</td>
            <td class="flex-row flex-1 items-center">{{ item.total_like }}</td>
          </tr>
        </tbody>
        <!-- <load-more :status="load_status"></load-more> -->
      </table>
        </view>
      </view>


      <view class="btm">
        <bottomBar :currentIndex="3"></bottomBar>
      </view>

    </view>

</template>

<script>
import myIcon from "@/components/my-icon"
import bottomBar from "./components/bottomBars"
export default {
  components:{
    myIcon,
    bottomBar
  },
  data(){
    return {
      list:[],
      params:{

      },
      type:'day',
      show_drop:false,
      myInfo:{
        byte_user:{
        },
        "day": {
        },
        "yesterday": {
        },
      }
    }
  },
  onLoad(){
    this.getList()
    this.getMyInfo()
  },
  methods:{
    getMyInfo(){
      this.$ajax.get('/qywx/byte_dance/my_data',{},res=>{
        if(res.statusCode ==200){
          this.myInfo = res.data
        }
      })
    },
    changType(){
      this.type=='day'?this.type='yesterday' :this.type='day'
      this.show_drop =false
    },
    getList(){
      this.load_status = "loading";
      this.$ajax.get('/qywx/byte_dance/rand_list', this.params, res => {
        console.log(res);
        if (res.statusCode == 200) {
          this.list = this.list.concat(res.data.data)
          if (res.data.data.length < this.params.rows) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'more'
          }
        }
      })
    }
  }
}
</script>

<style lang ="scss" scoped>
  .my {
    background: #f7f7f7;
    padding-bottom: 160rpx;
    .block {
      margin-top: 24rpx;
      
      background: #fff;
    }
    .prelogo {
      width: 128rpx;
      height: 128rpx;
      min-width: 128rpx;
      background: #f7f7f7;
      border-radius: 50%;
      margin-right: 24rpx;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .my_top {
      padding: 48rpx;
    }
    .info {
      
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      padding: 12rpx 0;
      .my_name {
        color: #000;
        font-size: 32rpx;
      }
      .my_info  {
        color: #999;
        font-size: 24rpx;
      }
    }
    .paiming {
      display: flex;
      justify-content: space-between;
      flex-direction: column;
      padding: 12rpx 0;
      .paiming_num {
        color: #333;
        font-size: 48rpx;
        font-weight: 600;
      }
      .paiming_title {
        color: #999;
        font-size: 28rpx;
      }
    }
    .my_middle {
      padding: 24rpx 48rpx;
    }
    .middle_title {
      .middle_left {
        .icon{
          width: 48rpx;
          height: 48rpx;
          image {
            width: 100%;
          } 
        }
        .icon_name {
          margin-left: 4rpx;
          color: #666;
          font-size: 24rpx;
        }
      }
      .middle_right{
        position: relative;
        .drop{
          position: absolute;
          top:38rpx;
          right: 0;
          width: 112rpx;
          padding: 12rpx 24rpx;
          border: 1px solid #f7f7f7;
          border-radius: 4rpx;
          box-sizing: content-box;
          z-index: 5;
          /* height: ; */
        }
        .right_name {
          margin-right: 10rpx;

        }
        .right_icon {

        }
      }
    }
    .middle_content {
      margin-top: 48rpx;
      background: #F8F8F8;;
      .middle_content_item{
        padding: 32rpx 0;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .middle_content_item_top{
          color: #333;
          font-size: 32rpx;
        }
        .middle_content_item_bottom{
          margin-top: 24rpx;
          color: #999;
          font-size: 24rpx;
        }
      }
    }
  }

  .thead {
    padding: 24rpx 48rpx;
    text-align: center;
    width: 100vw;
    tr {
      /* padding: 0 48rpx; */
      th {
        text-align: center;
        justify-content: center;
        color: #999999;
      }
    }
    .icon {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      height: 100%;
      .up {
        width: 0;
        height: 0;
        border-left: 8rpx solid transparent;
        border-right: 8rpx solid transparent;
        border-bottom: 8rpx solid #d9d9d9;
        &.active {
          border-bottom: 8rpx solid #2d84fb;
        }
      }
      .down {
        margin-top: 7rpx;
        width: 0;
        height: 0;
        border-left: 8rpx solid transparent;
        border-right: 8rpx solid transparent;
        border-top: 8rpx solid #d9d9d9;
        &.active {
          border-top: 8rpx solid #2d84fb;
        }
      }
    }
  }
  .tbody {
    padding: 0 48rpx;
    text-align: center;
    width: 100vw;
    tr {
      padding: 32rpx 48rpx;
      td {
        text-align: center;
        justify-content: center;
        color: #999999;
      }
    }
    .user {
      .header {
        position: relative;
        margin-right: 10rpx;
        image {
          width: 96rpx;
          height: 96rpx;
          border-radius: 50%;
        }
      }
      .name {
        font-size: 32rpx;
        color: #000000;
      }
    }
  }
  

</style>