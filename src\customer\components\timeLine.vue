<template>
  <view class="time_line">
    <view
      class="item"
      :class="{ current: item.is_finish !== 0 }"
      v-for="(item, index) in lineData"
      :key="index"
    >
      <template v-if="custom">
        <slot :slotItem="item" :slotIndex="index"></slot>
      </template>
      <template v-else>
        <!-- <view class="title">{{ item.description }}</view> -->
    <view style="display: flex; flex-direction: row; justify-content: space-between; align-items: center;">
      <view class="time">{{ item.created_at }}</view>
        <view class="follow-card-picture">
                <view class="follow-giveLike-box">
                  <view class="follow_giveLike-controls">
                    <!-- 点赞 -->
                    <view class="follow_giveLike_icon" @click.prevent.stop="followGiveLike(item.id)">
                      <image :src="'/static/admin/customer/<EMAIL>' | imageFilter('w_80')" />
                    </view>
                    <!-- 复制 -->
                    <view style="margin: 0 20px; box-sizing: content-box" class="follow_giveLike_icon"
                      @click.prevent.stop="followCopy(item)">
                      <image
                        src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/<EMAIL>" />
                    </view>
                    <!-- 置顶 -->
                    <view class="follow_giveLike_more" @click.prevent.stop="showPinned(item, index)">
                      <image :src="'/static/admin/customer/<EMAIL>' | imageFilter('w_80')" />
                    </view>
                    <view class="follow-Pinned-box" ref="tops" v-if="item.show || setTopShow">
                      <text class="Pinned-text" v-if="item.order == 0" @click.stop="setTop(item)">设置置顶</text>
                      <text class="Pinned-text" v-if="item.order == 1" @click.stop="setTop(item)">取消置顶</text>
                    </view>
                  </view>
                </view>
              </view>
    </view>
    <view style="color:  rgba(41, 44, 57, 0.40);font-size: 28rpx; padding:0 0 24rpx 0">{{ item.admin.user_name }} / {{ item.admin.department_name }}</view>
        <view class="timeline_item">
            <view class="time flex-row items-center space-between">
              <view class="follow-card-content row" style="padding-bottom: 0">
                <view class="right text_right">
                  <view class="row text_rowLIST">
                    <view class="order" v-if="item.order">
                      <view class="order_c"> 顶</view>
                    </view>
                    <!-- {{ item.content }} -->
                    <view class="follow-picture-box">
                  <image v-for="(img, i) in item.file_path_list" :key="i"
                    @click.prevent.stop="preFollowImgs(item.file_path_list, i)" :src="img" mode="aspectFill" />
                </view>
                    <view v-if="!item.url" class="contenturkl">{{ item.content }}
                    </view>
                    <view v-else class="voice row" @click.prevent.stop="playVoice(item, index)">
                      <image v-if="!item.playing" :src="'/static/icon/voice/voice_icon.png' | imageFilter('w_80')">
                      </image>
                      <image v-if="item.playing" :src="'/static/icon/voice/play_voice.gif' | imageFilter('w_80')"></image>
                      <text>{{ parseInt(item.time / 1000) }}"</text>
                    </view>
                  </view>
                  <view class="follow-giveLike-main" v-if="item.top_list && item.top_list.length">
                    <view class="follow-giveLike-zan">
                      <image
                        src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/<EMAIL>" />
                    </view>
                    <template v-if="item.top_list && item.top_list.length">
                      <view class="follow-giveLike-user" v-for="(it, i) in item.top_list" :key="it">
                        <template v-if="i < 2">
                          {{ it }}
                        </template>
                        <template v-if="i >= 2">
                          {{ '等' + item.top_list.length + '人' }}
                        </template>
                      </view>
                    </template>
                  </view>
                  <!-- <text class="username" v-if="item.admin">由「{{ item.admin.user_name }}」跟进</text> -->
                </view>
              </view>
            
       </view>
       </view>
      </template>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    lineData: Array,
    custom: {
      type: [Boolean],
      default: false,
    },
    client_id:Number
  },
  create(){
    console.log(this.lineData,'lineData12345');
  },
  data() {
    return {
      setTopShow:false,
      follow_list:[],
      follow_params: {
        page: 1,
        per_page: 10,
        client_id: this.client_id,
      },
    };
  },
  onShow() {
    if (this.is_refresh) {
      uni.$on('refresh', (data) => {
        if (data.refreh) {
          this.follow_params.page == 1; // 重置页码
          this.follow_list = []; // 清空跟进记录列表
          this.getFollowData(); // 获取跟进记录
        }
      })
      this.is_refresh = false;
    }
  },
  methods: {
    // 预览图片
    preFollowImgs(filePath, index) {
      uni.previewImage({
        current: index,
        urls: filePath,
        indicator: 'number',
      })
    },
    // 跟进列表点赞功能
    followGiveLike(id) {
      this.$ajax.get(`/qywx/client_follow/click/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
          title: '点赞成功' || "取消点赞",
          icon: "none",
        });
          this.follow_params.page = 1
          this.getFollowData(); // 获取最新数据
        }
      })
    },
    // 跟进列表赋值文本功能
    followCopy(item) {
      console.log(item, "复制");
      let contents = item.content;
      this.$copyText(contents, () => {
        uni.showToast({
          title: '复制成功',
          icon: "none",
        });
      })
    },
    setTop(item) {
      this.followSetTop(item)
    },
    // 跟进列表设置为置顶
    followSetTop(item) {
      this.$ajax.get(`/qywx/client_follow/top/${item.id}`, {}, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: '置顶成功',
            icon: "none",
          });
          this.setTopShow = false
          this.follow_params.page = 1;
          this.getFollowData(); // 获取最新数据
        }
      })
    },
    playVoice(item, index) {
      innerAudioContext && innerAudioContext.stop()
      this.follow_list.map((i, idx) => {
        if (idx != index) {
          i.playing = false
        }
        return i
      })

      // setTimeout(() => {
      this.$set(this.follow_list[index], 'playing', true)

      // item.playing = true
      innerAudioContext.src = item.url
      console.log(this.follow_list, 11123, item.url);
      innerAudioContext.play()
      this.$forceUpdate()
      // }, 100);
    },
    // 显示置顶操作框
    showPinned(item, index) {
      console.log(item, index);
      this.follow_list.map(item => {
        item.show = false
        return item
      })
      item.show = true
      this.$forceUpdate()
    },
    getFollowData() {
      if (this.follow_params.page === 1) {
        this.follow_list = [];
      }
      this.is_f_loading = "loading"
      this.$ajax.get(
        "/qywx/client/follow/search",
        this.follow_params,
        (res) => {
          if (res.statusCode === 200) {
            if (res.data.data.length === 0 || res.data.data.length < this.follow_params.per_page) {
              this.is_f_loading = 'nomore';
            } else {
              this.is_f_loading = ''
            }
            res.data.data.map(i => {
              i.playing = false
              return i
            })
            this.follow_list = this.follow_list.concat(res.data.data);
            // this.getLogsData();
          }
        }
      );
    },
  },
};
</script>

<style lang="scss" scope>
.text_right{
  width: 100%;
}
.time_line {
  background: #fff;
  padding: 20upx 30upx;
  .item {
    position: relative;
    padding: 0 20upx 36upx 32upx;
    border-left: 5rpx solid #e4e4e4;
    .title {
      font-size: 28upx;
      margin-bottom: 15upx;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
    }
    .time {
      color:  rgba(41, 44, 57, 0.40);
font-size: 28rpx;
    }
  }
  .item::after {
    content: "";
    height: 40rpx;
    width: 40rpx;
    box-sizing: border-box;
    border-radius: 50%;
    position: absolute;
    border: 4rpx solid #3399ff;
    background-color: #fff;
    left: -20upx;
    top: -8rpx;
  }
  .current::before {
    content: "";
    height: 20upx;
    width: 20upx;
    border-radius: 50%;
    background-color: #ffff;
    position: absolute;
    left: -12upx;
    top: 0;
    z-index: 2;
  }
}

.contenturkl {
  display: block;
  color:  #292C39;
font-size: 28rpx;
font-weight: 400;
  white-space: normal; // 规定段落中的文本不进行换行
  word-break: break-all; // 允许单词中换行，在容器的最右边进行断开不会浪费控件
  word-wrap: break-word;
}
.top-card-contenter{
  position: relative;
  border-radius: 8rpx;
border: 2rpx solid #F0F1F5;
background: #F8F8F8;
padding: 0 24rpx 0rpx 0;
margin-top: 24rpx;
display: flex;
flex-direction: row;
// align-items: center;
}
.top-cardser {
  padding: 24rpx 32rpx 32rpx 32rpx;
  background: #fff;
  // border-radius: 10rpx;
  display: block;
  margin-bottom: 48rpx;
  margin-top: 24rpx;
  // position: relative;
}

.top-cardser.fixed {
  position: -webkit-sticky;
  position: sticky;
  z-index: 8;
  top: 0;
}

.content {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 24rpx;
  // justify-content: space-between;

}
.textarear {
        position: relative;
        // margin-top: 8px;
        padding: 24rpx 16rpx;
        width: 100%;
        padding-left: 30rpx;
        // margin-left: 42rpx;
      }
.content_detail {
  width: 470rpx;
  display: inline-block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-top: 24rpx;
  font-size: 32rpx;
  margin-left: 40rpx;
}

.Outbound {
  margin-top: 24rpx;
  color: #488AF6;
  font-size: 28rpx;
}

.top_tel_all {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-size: 24rpx;
  padding: 0rpx 0 24rpx 0;
  color: rgba(41, 44, 57, 0.40);
}

.tong_tel {
  margin-right: 24rpx;
  color: #292C39;
  font-size: 32rpx;

}

.top_marginleft {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 24rpx;

}

.top_tel_tel {
  color: rgba(41, 44, 57, 0.40);
  font-size: 32rpx;
  margin-top: 24rpx;
}

.top-cards {
  padding: 32rpx 32rpx;
  // background: #fff;
  border-radius: 5px;
  // margin: 0 18px 10px;
  display: block;
  position: relative;
}

.salesProgress_card {
  margin-top: 32rpx;
}

.levelimage {
  width: 280rpx;
  height: 264rpx;
  background: #eee;
  border-radius: 16rpx;
}

.top_gj {
  margin-left: 8rpx;
  margin-right: 24rpx;
}

page {
  background: #f6f6f6;
  color: #2e3c4e;
}

.top-card-content-labels {
  width: 15%;
  color: rgba(41, 44, 57, 0.4);
  font-size: 32rpx;
  font-weight: 400;
  margin-top: 6rpx;
}

.top {
  display: block;

  .top_img {
    background: #fff;
    height: 400rpx;
    position: relative;

    .top_img_main {
      width: 100%;
      height: 41px;
      // padding: 0 18px;
      box-sizing: border-box;
      position: absolute;
      // bottom: 326rpx;
      left: 0px;

      .top_img_follow {
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 100%;
        background-color: #FFF7E8;
        ;
        // border-radius: 5px;
        padding: 0 32rpx;
        box-sizing: border-box;

        .top_img_cus {
          font-size: 14px;
          color: #FF7D00;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }

        .top_img_time {
          font-size: 14px;
          color: #FF7D00;
        }

        .top_img_cus {
          .un_follow {
            color: #e15762;
          }
        }

        .top_img_time.drop_to_sea {
          color: #dcc05e;
        }
      }
    }
  }

  &-card {
    padding: 0rpx 32rpx 32rpx 32rpx;
    background: #fff;
    // border-radius: 5px;
    // margin: 0 18px 10px;
    display: block;
    position: relative;

    &-title {
      justify-content: space-between;
      font-size: 14px;
      color: #292C39;
      font-size: 36rpx;
      font-weight: 500;
    }

    &-left {
      .pic {
        width: 128rpx;
        height: 128rpx;
      }
    }

    &-right {
      margin-left: 12px;
      // text-align: center;

      .setting {
        width: 20px;
        height: 20px;
        position: absolute;
        right: 14px;
      }

      &-top {
        align-items: center;

        &_name {
          color: #292C39;
          // text-align: center;
          font-size: 36rpx;
          font-weight: 500;
          margin-top: 26rpx;
        }

        &_qw {
          height: 18px;
          width: 18px;
          margin-left: 4px;
        }

        &_sex {
          height: 16px;
          width: 16px;
          margin-left: 4px;
          margin-top: 22rpx;
        }

        &_tracking {
          color: #3e8afd;
          margin-top: 20rpx;
          // margin: 0 6px;
          font-size: 12px;
        }

        &_level {
          color: #fff;
          padding: 2px 12px;
          border-radius: 2px;
        }
      }

      &-bottom {
        margin-top: 10px;
        color: #828488;
        font-size: 12px;
      }
    }

    &-bot-bottom {
      // margin-top: 16px;
      width: 100%;
      // border-top: 1px solid #eeeeee;
      padding-top: 32rpx;
      font-size: 12px;
      justify-content: space-between;
      color: #828488;
    }

    &-content {
      // margin-top: 16px;
      display: flex;
      width: 100%;
      flex-direction: row;
      padding: 24rpx 0 24rpx 0;
      white-space: normal; // 规定段落中的文本不进行换行
  word-break: break-all; // 允许单词中换行，在容器的最右边进行断开不会浪费控件
  word-wrap: break-word;
      &.tel {
        align-items: center;
      }

      &-label {
        color: #737373;
        color: rgba(41, 44, 57, 0.40);
        font-size: 32rpx;
        font-weight: 400;

        &.top-card-content-label_top {
          min-width: 112rpx;
          align-self: flex-start;
        }
      }

      &-right {
        margin-left: 20px;
        font-size: 32rpx;

        &.has_follow {
          display: inline-block;

          .un_tong {
            position: relative;

            &.tong {
              &::after {
                background: #9edf2e;
              }
            }

            &::after {
              content: '';
              position: absolute;
              right: -10rpx;
              top: -2rpx;
              background: #f56c6c;
              width: 10rpx;
              height: 10rpx;
              border-radius: 50%;
            }
          }
        }
      }
.auto-scroll-container {
  overflow: hidden;
  height: 250px; /* 可视区域高度，根据需要调整 */
}

.auto-scroll-content {
  transition: transform 1s ease; /* 滚动动画效果 */
}

.scroll-item {
  height: 50px; /* 项目高度，根据需要调整 */
  line-height: 50px; /* 垂直居中文本 */
  text-align: center;
  border: 1px solid #ccc;
}
      .textarea {
        position: relative;
        // margin-top: 8px;
        padding: 24rpx 16rpx;
        background: #f5f7fa;
        border: 1px solid #e8e8e8;
        height: 200rpx;
        width: 100%;
        padding-left: 100rpx;
        // margin-left: 42rpx;
      }

      .placeholderClass {
        color: #292C39;
        font-size: 32rpx;
      }
    }

    &-label-list {
      margin-top: 8px;
      flex-wrap: wrap;

      .item {
        margin-bottom: 4px;
        padding: 4px 12px;
        border-radius: 4px;
        background: #e8f1ff;
        color: #2f6aff;
        margin-right: 10px;
      }
    }

    .chakan {
      align-self: flex-start;
      font-size: 13px;
      color: #2f6aff;
      position: absolute;
      right: 14px;
    }

    .level {
      width: 60px;
      display: inline-block;
      margin-top: 10px;
      text-align: center;
      border-radius: 4px;
      background: #f1f4fa;
      color: #737373;
      height: 28px;
      line-height: 28px;
      font-size: 14px;
    }

    &-tabs {
      font-size: 14px;
      justify-content: space-around;

      &-item {
        position: relative;

        &.isactive {
          color: #2f6aff;

          &::after {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            content: '';
            height: 4px;
            background: #2d84fb;
            width: 30px;
            display: block;
            margin-top: 18px;
          }
        }
      }
    }
  }
}

.setting-pop {
  height: 400px;
  border-radius: 20px 20px 0px 0px;
  background: #f6f6f6;
  padding: 14px 18px 42px;

  &-top {
    align-items: center;
    background: #fff;
    justify-content: space-around;
    border-radius: 5px;
    padding: 22px 0;

    .item {
      align-items: center;
      font-size: 16px;
    }

    .item-img {
      width: 20px;
      margin-bottom: 6px;
      height: 20px;
    }
  }

  &-bottom {
    background: #fff;
    border-radius: 5px;
    padding: 22px 20px;
    margin-top: 16px;

    .item {
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .left {
        align-items: center;
        font-size: 16px;

        .item-img {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
.timeline_item {
  .time {
    margin-bottom: 16rpx;
    font-size: 24rpx;
    font-weight: bold;
    color: #999;
  }
  .agent_name {
    font-weight: normal;
  }
  .user_info {
    margin-bottom: 16rpx;
    flex-direction: row;
    align-items: center;
    .avatar {
      margin-right: 12rpx;
      width: 62rpx;
      height: 62rpx;
      border-radius: 50%;
    }
    .name {
      font-size: 24rpx;
      color: #555555;
    }
    .tname {
      margin-top: 6rpx;
      font-size: 23rpx;
      color: #8a929f;
    }
    .tel {
      margin-left: 12rpx;
      flex-direction: row;
      align-items: center;
      font-size: 26rpx;
      color: #2d84fb;
    }
  }
  .content {
    margin-bottom: 16rpx;
    margin-top: 24rpx;
    font-size: 28rpx;
    line-height: 1.5;
    word-break: break-all;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // -webkit-line-clamp: 2;
    // -webkit-box-orient: vertical;
    // display: -webkit-box;
    .status {
      margin-right: 12rpx;
      font-weight: bold;
      &.status1 {
        color: #3cc53c;
      }
      &.status2 {
        color: #8a929f;
      }
      &.status3 {
        color: #2d84fb;
      }
      &.status4 {
        color: #fe6c17;
      }
    }
  }
  .img_list {
    flex-direction: row;
    flex-wrap: wrap;
    > image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 4rpx;
      margin-right: 16rpx;
      margin-bottom: 16rpx;
    }
  }
}
.istotop {
  width: 42px;
  height: 42px;
  position: fixed;
  right: 24px;
  bottom: 100px;
}

.content-box {
  background: #fff;
  padding: 14px;
  border-radius: 5px;
}

.card-border-line {
  padding-bottom: 14px;
  border-bottom: 1px solid #eeeeee;
}

.pic_box {
  display: flex;
  flex-direction: column;
  margin-bottom: 100px;
  background: none;
  padding: 0;

  .basic-info-box {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 14px;
    box-sizing: border-box;
    background-color: #ffffff;
    margin-top: 4px;
    margin-bottom: 20px;

    .basic-info-title {
      color: #292C39;
      font-size: 18px;
    }

    .basic-info-content {
      display: flex;
      flex-direction: row;
      margin-top: 20px;

      .content-title {
        font-size: 13px;
        color: #737373;
        margin-right: 20px;
        white-space: pre;
      }
    }
  }

  .list {
    background: #fff;
    border-radius: 6px;
    padding: 14px;
    box-sizing: border-box;

    .info {
      font-size: 16px;
      margin-top: 25px;
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;

      .c2 {
        text-align: end;
        font-size: 12px;
        color: #828488;

        &.depart {
          align-items: flex-start;
          text-align: left;
          margin-top: 20rpx;
        }

        &.flex_start {
          text-align: left;
          align-items: flex-start;
        }
      }

      .time {
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
        color: #737373;
      }

      .pic {
        align-items: center;
        margin-top: 12px;

        image {
          width: 90rpx;
          height: 90rpx;
          min-width: 90rpx;
          border-radius: 50%;
          margin-right: 12px;
        }
      }
    }
  }
}
.follow-giveLike-main{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 16rpx;
  margin-top: 24rpx;
  font-weight: 400;
  color: #FF7D00;
  width: 20%;
font-size: 28rpx;
  border-radius: 22%;
background: #FFF7E8;
}
.follow-card {
  &-item {
    margin-bottom: 24px;
  }

  &-content {
    // background: #fff;
    border-radius: 5px;
    // margin-top: 14px;
    // padding: 14px;
    font-size: 28rpx;
    width: 100%;
    color: #828488;
    line-height: 20px;
    white-space: normal; // 规定段落中的文本不进行换行
  word-break: break-all; // 允许单词中换行，在容器的最右边进行断开不会浪费控件
  word-wrap: break-word;
    span {
      // color: #000;
    }

    .order {
      display: block;
      margin-right: 4rpx;

      .order_c {
        display: inline-block;
        padding: 0 8rpx;
        font-size: 24rpx;
        background: #e15762;
        color: #fff;
        border-radius: 2rpx;
      }
    }
  }


}
.follow-giveLike-zan {
          width: 32rpx;
          height: 32rpx;

          image {
            width: 100%;
            height: 100%;
          }}
.lijibtn {
  border-radius: 5px;
  background: #3172f6;
  box-shadow: 0px 4px 10px 0px #3172f67f;
  position: fixed;
  color: #fff;
  line-height: 40px;
  text-align: center;
  left: 50%;
  bottom: 10%;
  transform: translate(-50%, 0);
  width: 300px;
}
.follow-card-picture {
    padding: 20rpx 28rpx 28rpx 120rpx;
    background-color: #fff;
    position: relative;



    .follow-giveLike-box {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-top: 6px;

      .follow-giveLike-main {
        display: flex;
        flex-direction: row;
        align-items: center;
        border-radius: 12px;
        padding: 5px 14px;
        box-sizing: border-box;
        background-color: #ebebeb;

        .follow-giveLike-zan {
          width: 14px;
          height: 14px;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .follow-giveLike-user {
          display: flex;
          flex-direction: row;
          padding-left: 16rpx;
          margin-left: 16rpx;
          color: #8a929f;
          font-size: 24rpx;
          border-left: 1px solid #8a929f;
        }
      }

      .follow_giveLike-controls {
        display: flex;
        flex-direction: row;
        flex: 1;
        justify-content: flex-end;

        .follow_giveLike_icon {
          width: 28rpx;
          height: 28rpx;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .follow_giveLike_more {
          width: 28rpx;
          height: 8rpx;
          margin-top: 10rpx;

          // line-height: 16rpx;
          image {
            width: 100%;
            height: 100%;
          }
        }

        .follow-Pinned-box {
          // display: none;
          padding: 16rpx;
          border-radius: 5px;
          background: #ffffff;
          box-shadow: 0px 0px 8px 0px #0000003f;
          position: absolute;
          bottom: -45px;
          right: 10px;

          .Pinned-text {
            color: #6c6f74;
            font-size: 28rpx;

            &:first-child {
              padding-bottom: 5px;
              // border-bottom: 1px solid #6c6f74;
              margin-bottom: 5px;
            }
          }
        }
      }
    }
  }
.isborder {
  animation: glow 800ms ease-out infinite alternate;
}

@keyframes glow {
  0% {
    border-color: #2d84fb;
    box-shadow: 0 0 5px #2d84fb, inset 0 0 5px rgba(221, 225, 233, 1), 0 1px 0 #2d84fb;
  }

  100% {
    border-color: rgba(221, 225, 233, 1);
    box-shadow: 0 0 20px #2d84fb, inset 0 0 10px rgba(221, 225, 233, 1), 0 1px 0 #2d84fb;
  }
}
.follow-picture-box {


      image {
        width: 113rpx;
        height: 113rpx;
        border-radius: 4rpx;
        margin-right: 16rpx;
        margin-bottom: 16rpx;
      }
    }
.footer_btn_group {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9;
  background-color: #fff;
  padding: 12px 24px;

  .option_btn {
    display: block;
    text-align: center;

    .more_con {
      align-items: center;
      padding: 0 20rpx;
    }

    .more {
      display: block;
      width: 6rpx;
      font-size: 44rpx;
      transform: rotate(90deg);
      // line-height: 10rpx;
      // overflow: hidden;
      // word-wrap: break-word;
      // line-height: 18rpx;
      // padding: 6rpx 0;
    }

    &.option_btn_cloumn {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 32rpx;
      color: #8a929f;

      image {
        width: 45rpx;
        height: 34rpx;
      }
    }

    ~.option_btn {
      margin-left: 24rpx;
    }

    ::v-deep .my-btn.big {
      padding: 0 16rpx;
    }
  }
}

.l-title {
  font-size: 16px;
  color: #2e3c4e;
}

.top-card.fixed {
  position: sticky;
  z-index: 8;
  top: 0;
}

.tag_input,
.top-card-label-list {
  // margin-top: 20px;
  // border: 1px solid #dde1e9;
  border-radius: 4px;
  align-items: center;
  // padding: 9px 12px;
  flex-wrap: wrap;

  .tag_item {
    margin-bottom: 4px;
    padding: 4px 12px;
    border-radius: 4px;
    background: #e8f1ff;
    color: #2f6aff;
    margin-right: 10px;
    // padding: 8px 20px;
    // text-align: center;
    // margin-right: 10px;
    // // margin-left: 10px;
    // margin-bottom: 12px;
    // background: #fff;
    // border-radius: 4px;
    // border: 1px solid #eee;
    // min-width: 80px;
    // color: #8d9099;
    position: relative;
    overflow: hidden;

    &.checked {
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
      background: #fff;
    }

    .tag_t {
      font-size: 14px;
      margin-bottom: 10rpx;
    }

    .tag_b {
      font-size: 14px;
    }

    .checked {
      position: absolute;
      right: 0;
      top: 0;
      width: 32rpx;
      height: 24rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }

  input {
    width: 100%;
    font-size: 14px;
  }
}

.top-card-label-lists {
  // margin-top: 20px;
  // border: 1px solid #dde1e9;
  border-radius: 4px;
  align-items: center;
  // padding: 9px 12px;
  flex-wrap: wrap;

  // margin-left: 40rpx;
  .tag_item {
    // margin-bottom: 4px;
    padding: 4px 12px;
    border-radius: 4px;
    background: #e8f1ff;
    color: #2f6aff;
    margin-right: 10px;
    // padding: 8px 20px;
    // text-align: center;
    // margin-right: 10px;
    // // margin-left: 10px;
    // margin-bottom: 12px;
    // background: #fff;
    // border-radius: 4px;
    // border: 1px solid #eee;
    // min-width: 80px;
    // color: #8d9099;
    position: relative;
    overflow: hidden;

    &.checked {
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
      background: #fff;
    }

    .tag_t {
      font-size: 14px;
      margin-bottom: 10rpx;
    }

    .tag_b {
      font-size: 14px;
    }

    .checked {
      position: absolute;
      right: 0;
      top: 0;
      width: 32rpx;
      height: 24rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }

  input {
    width: 100%;
    font-size: 14px;
  }
}

.reason {
  width: 80vw;
  height: 400rpx;
  margin: calc(100% - 200rpx) auto;
  padding: 40rpx;
  border-radius: 20rpx;
  background: #fff;
  position: relative;

  .close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    font-size: 48rpx;
  }

  textarea {
    margin-top: 8px;
    padding: 6px 16px;
    box-sizing: border-box;
    background: #f5f7fa;
    border: 1px solid #e8e8e8;
    height: 280rpx;
    width: 100%;
  }

  .btn {
    display: inline-block;
    padding: 20rpx 20rpx;
    background: #2d84fb;
    color: #fff;
    margin: 20rpx auto 0;
  }
}

.follow-config-main {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
  justify-content: space-between;

  .InstalledTop {
    padding: 0px 4px;
    border-radius: 2px;
    background: #2d84fb;
    color: #ffffff;
    font-size: 12px;
  }

  .follow-config-titel {
    display: flex;
    flex-direction: row;
    color: #2e3c4e;
    font-size: 14px;
    font-weight: bold;
  }
}

.follow-box-content {
  margin-bottom: 10px;
}

.redInfo {
  color: #ff0000;
}

.blueInfo {
  color: #3d91ff;
}

.voice {
  background: #2d84fb;
  width: 300rpx;

  image {
    width: 40rpx;
    height: 40rpx;
  }

  text {
    color: #fff;
    margin-left: 10rpx;
  }
}

.setting_level {
  width: 80rpx;
  height: 40rpx;
  position: absolute;
  right: 16rpx;
  top: 44rpx;
}

.setting_levels {
  width: 80rpx;
  height: 40rpx;
  position: absolute;
  right: 0rpx;
  top: 22px;
}

.un_level {
  background: #bcbcbc;
  color: #8a929f;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  position: absolute;
  right: 28rpx;
}

.nameText {
  // margin-left: 50rpx;
  width: 160rpx;
  overflow: hidden;
  text-overflow: ellipsis; //text-overflow: clip|ellipsis|string;
  overflow: hidden;
  white-space: nowrap;
}

.top-card-content-act {
  width: 140rpx;
  // margin-left: 40rpx;
  font-size: 22rpx;
  margin-top: 10rpx;
  padding: 4rpx 8rpx;
  border: 1rpx solid #2d84fb;
  color: #2d84fb;

  &.address {
    color: #828488;
    border: none;
    width: auto;
  }
}

.tel_flex {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
}

.top-card-content-last_follow {
  color: #828488;
  margin-top: 10rpx;
  margin-left: 40rpx;
  line-height: 1.5;
}

.p_con {
  padding: 40rpx;
  background: #fff;
  border-radius: 8rpx;
  position: relative;
  padding-top: 140rpx;
  width: 80vw;

  /* height: 50vh; */
  .title {
    font-size: 40rpx;
    font-weight: 600;
    padding: 40rpx 0;
    text-align: center;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    text-align: center;
  }

  .p_content {
    .p_item {
      .label {
        margin-right: 5rpx;
      }
    }

    .btns {
      margin-top: 200rpx;

      .btn {
        background: #2d84fb;
        padding: 30rpx 0;
        color: #fff;
        border-radius: 10rpx;
      }
    }
  }
}

.option_btn.disabled {
  ::v-deep .my-btn.primary {
    background: rgba(45, 132, 251, 0.3);
    color: rgba(45, 132, 251, 0.8);
  }
}
</style>
