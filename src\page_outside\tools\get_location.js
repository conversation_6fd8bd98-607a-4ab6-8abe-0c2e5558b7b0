var location = {
  methods: {
    getLocation() {
      this.$ajax.get("/common/website/region", {}, (res) => {
        if (res.statusCode === 200) {
          // 获取位置信息
          let regiondata = res.data;
          var that = this;
          uni.getLocation({
            type: "gcj02",
            success: function(res) {
              console.log("获取的位置信息：", res);
              that.getAdressInfo(res.longitude, res.latitude, regiondata);
            },
            fail: function(err) {
              console.log("获取位置失败：", err);
              // "117.166545", "35.077644" 滕州坐标
              // "115.68","32.18" 固始县坐标
              that.getAdressInfo("117.166545", "35.077644", regiondata);
            },
          });
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    getAdressInfo(long, lat, data) {
      this.$jsonp("https://apis.map.qq.com/ws/geocoder/v1/", {
        location: `${lat},${long}`,
        key: this.$store.state.qqmapkey,
        output: "jsonp",
      })
        .then((res) => {
          if (res.status === 0) {
            // console.log(
            //   "获取位置成功：region_0=" + res.result.ad_info.city,
            //   "region_1=" + res.result.ad_info.district
            // );
            let region_0, region_1;
            data.map((item) => {
              if (res.result.ad_info.city.indexOf(item.name) != -1) {
                item.pid == 0 ? (region_0 = item.id) : (region_1 = item.id);
              }
              if (res.result.ad_info.district.indexOf(item.name) != -1) {
                item.pid == 0 ? (region_0 = item.id) : (region_1 = item.id);
              }
            });
            let city = {
              name: res.result.ad_info.district,
              region_0: region_0 || 0,
              region_1: region_1 || 0,
              location_name: res.result.ad_info.district,
            };
            this.$store.commit("setCityData", city);
          }
        })
        .catch((err) => {
          console.log("获取位置失败jsonp：", err);
          let city = {
            name: "全部",
            region_0: 0,
            region_1: 0,
            location_name: "获取位置",
          };
          this.$store.commit("setCityData", city);
        });
    },
  },
};

module.exports = location;
