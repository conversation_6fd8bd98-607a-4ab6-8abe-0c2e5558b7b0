<template>
  <view class="list">
    <view class="search row">
      <view class="right">
        <myIcon class="icon" type="sousuo" color="#999" size="40rpx"></myIcon>
        <input class="uni-input" type="text" placeholder="请输入昵称或手机号" />
      </view>
    </view>
    <view class="navs row">
      <view
        v-for="(item, index) in navs_menu"
        :key="index"
        class="nav-item"
        :class="{ active: item.id === data_type }"
        @click="onClickNav(index)"
        >{{ item.name }}</view
      >
    </view>
    <!-- 访客列表 -->
    <view class="pad-list" v-if="data_type === 1">
      <userList
        :type="1"
        @onClick="onClickVisitors"
        @delData="delDataVisitors"
        :msg_list="visitors_list"
      ></userList>
    </view>
    <!-- 聊天列表 -->
    <view class="pad-list" v-if="data_type === 2">
      <userList
        :type="2"
        @onClick="onClickMsg"
        @delData="delDataMsg"
        :msg_list="im.friendList"
      ></userList>
    </view>
    <view class="pad-list" v-if="data_type === 3">
      <customerCard
        :status_category="status_category"
        :cardList="customer_list"
      ></customerCard>
      <button
        @click="$navigateTo(`/only_build_user/customer_detail`)"
        class="btn"
      >
        新增客户
      </button>
    </view>
    <!-- 管理客户列表 -->
    <view
      class="socket_close"
      v-if="im.socketOpen === false && is_setting.config_support_im"
    >
      <view class="err-tip row">
        <view class="tip-text">聊天连接已断开</view>
        <view class="tip-btn" @click="connectChatAgain()">{{
          reconnect_status
        }}</view>
      </view>
    </view>
    <view class="socket_close" v-if="user_info.id && !user_info.wx_open_id">
      <view class="err-tip row">
        <view class="tip-text"> 授权公众号可随时接收聊天消息</view>
        <view
          class="tip-btn"
          @click="$bindingPublic('/only_build_im/msg_list?type=1')"
          >立即授权</view
        >
      </view>
    </view>
    <loadMore :status="load_status"></loadMore>
  </view>
</template>

<script>
import userList from "../only_build/components/user_list";
import loadMore from "../components/loadMore";
import myIcon from "../only_build/components/my-icon";
import chat from "../only_build_im/chatmixin.js";
import customerCard from "./components/customer-card";
import { mapActions, mapState } from "vuex";
export default {
  components: { loadMore, myIcon, userList, customerCard },
  mixins: [chat],
  data() {
    return {
      load_status: "",
      params: {
        page: 1,
        keyword: "",
      },
      visitors_params: {
        page: 1,
        keyword: "",
      },
      visitors_list: [],
      customer_params: {
        page: 1,
        keyword: "",
      },
      customer_list: [],
      content: "",
      navs_menu: [
        { id: 1, name: "最新访客" },
        { id: 2, name: "聊天" },
        { id: 3, name: "管理客户" },
      ],
      data_type: 1,
      status_category: [],
      reconnect_status: "点击重连", // 点击重新连接按钮
    };
  },
  onLoad(options) {
    if (this.is_setting.config_support_im) {
      this.navs_menu = [
        { id: 1, name: "最新访客" },
        { id: 2, name: "聊天" },
        { id: 3, name: "管理客户" },
      ];
    } else {
      this.navs_menu = [
        { id: 1, name: "最新访客" },
        { id: 3, name: "管理客户" },
      ];
    }
    this.data_type = parseInt(options.type);
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "CUSTOMER_FROM_CATEGORY":
            this.status_category = item.childs;
            break;
        }
      });
    });
  },
  computed: {
    ...mapState(["im", "user_info", "is_setting"]),
  },
  onShow() {
    // 如果没连接，初始化聊天
    if (!this.im.socketOpen && this.is_setting.config_support_im) {
      this.initChat();
    }
    this.params.page = 1;
    this.visitors_params.page = 1;
    this.customer_params.page = 1;
    this.getvisitorsList();
    this.getDataList();
    this.getUserInfo();
    this.getCustomerList();
  },
  onUnload() {
    // 路由时间的页面切换并不会触发onUnload事件 ， 返回时会触发onUnload事件
    this.closeSocket("active");
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    ...mapActions(["getImToken", "getUserInfo"]),
    getDataList() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.im.friendList = [];
      }
      this.$ajax.get("/client/im/session/search", this.params, (res) => {
        if (res.statusCode === 200) {
          this.load_status = "loadend";
          this.im.friendList = this.im.friendList.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        } else {
          this.load_status = "loadend";
          uni.showToast({
            title: res.data.message || "获取对话列表失败",
            icon: "none",
          });
        }
      });
    },
    initChat() {
      if (!this.im.socketOpen) {
        this.getImToken((options) => {
          if (options === 200) {
            this.initMsg = {
              flag: "init",
              from_id: this.im.myChatInfo.platform + this.im.myChatInfo.from_id,
              dialog_id: this.im.myChatInfo.dialog_id,
            };
            this.handleConnectSocket(); // this.onMessage()
            this.onClose();
            this.onSocketError();
          }
        });
      } else {
        console.log("聊天功能已经是连接状态");
      }
    },
    getvisitorsList() {
      this.load_status = "loading";
      if (this.visitors_params.page === 1) {
        this.visitors_list = [];
      }
      this.$ajax.get(
        "/client/user/visitor/search",
        this.visitors_params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.visitors_list = this.visitors_list.concat(res.data.data);
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
            }
          } else {
            this.load_status = "loadend";
            uni.showToast({
              title: res.data.message || "获取访客列表失败",
              icon: "none",
            });
          }
        }
      );
    },
    getCustomerList() {
      this.load_status = "loading";
      if (this.customer_params.page === 1) {
        this.customer_list = [];
      }
      this.$ajax.get("/client/customer/search", this.customer_params, (res) => {
        if (res.statusCode === 200) {
          this.load_status = "loadend";
          this.customer_list = this.customer_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        } else {
          this.load_status = "loadend";
          uni.showToast({
            title: res.data.message || "获取客户列表失败",
            icon: "none",
          });
        }
      });
    },
    // 点击访客信息
    onClickMsg(item) {
      this.$navigateTo(
        `/only_build_im/msg_detail?to_id=${item.u_id}&is_list=1`
      );
    },
    onClickVisitors(item) {
      this.$navigateTo(`/message/friend_info?id=${item.u_id}`);
    },
    // 断线重连
    connectChatAgain() {
      this.reconnect_status = "正在连接...";
      uni.showLoading({
        title: "正在连接",
        mask: true,
      });
      setTimeout(() => {
        if (this.im.socketOpen) {
          uni.hideLoading();
        } else {
          uni.hideLoading();
          uni.showToast({
            title: "连接失败，请稍后重连",
            icon: "none",
          });
        }
      }, 5000);
      this.handleConnectSocket();
      return;
    },
    onClickNav(index) {
      this.data_type = this.navs_menu[index].id;
      if (this.data_type == 1) {
        this.getvisitorsList();
      }
      if (this.data_type == 2) {
        this.getDataList();
      }
      if (this.data_type == 3) {
        this.getCustomerList();
      }
    },
    onConfirm(e) {
      if (this.data_type === 1) {
        this.visitors_params.keyword = e.detail.value;
        this.visitors_params.page = 1;
        this.getvisitorsList();
      }
      if (this.data_type === 2) {
        this.params.keyword = e.detail.value;
        this.params.page = 1;
        this.getDataList();
      }
      if (this.data_type === 3) {
        this.customer_params.keyword = e.detail.value;
        this.customer_params.page = 1;
        this.getCustomerList();
      }
    },
    delDataVisitors(item) {
      var that = this;
      uni.showModal({
        title: "提示",
        content: "确认删除该记录？",
        success: function(res) {
          if (res.confirm) {
            that.$ajax.get(`/client/customer/delete/${item.id}`, {}, (res) => {
              if (res.statusCode === 200) {
                that.visitors_params.page = 1;
                that.getvisitorsList();
              }
            });
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
    delDataMsg(item) {
      var that = this;
      uni.showModal({
        title: "提示",
        content: "确认删除该记录？",
        success: function(res) {
          if (res.confirm) {
            that.$ajax.get(
              `/client/im/session/delete/${item.id}`,
              {},
              (res) => {
                if (res.statusCode === 200) {
                  that.params.page = 1;
                  that.getDataList();
                }
              }
            );
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
  },
  onPullDownRefresh: function() {
    this.params.page = 1;
    this.visitors_params.page = 1;
    this.customer_params.page = 1;
    this.getDataList();
    this.getvisitorsList();
    this.getCustomerList();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    switch (this.data_type) {
      case 1:
        this.visitors_params.page++;
        this.getvisitorsList();
        break;
      case 2:
        this.params.page++;
        this.getDataList();
        break;
      case 3:
        this.customer_params.page++;
        this.getCustomerList();
        break;
    }
  },
};
</script>

<style lang="scss" scoped>
.list {
  .pad-list {
    margin-bottom: 100rpx;
    padding: 0 48rpx;
  }
  .search {
    padding: 16rpx 48rpx;
    align-items: center;
    justify-content: space-between;
    height: 96rpx;
    background: #fff;
    width: 100%;
    z-index: 10;
    .left {
      font-size: 28rpx;
      color: #333;
      align-items: center;
      text {
        margin-right: 6rpx;
      }
    }
    .right {
      position: relative;
      width: 100%;
      .icon {
        position: absolute;
        top: 14rpx;
        left: 28rpx;
      }
      input {
        font-size: 28rpx;
        padding-left: 96rpx;
        background: #eee;
        height: 64rpx;
        // width: 530rpx;
        border-radius: 16rpx;
      }
    }
  }
  // 导航
  .navs {
    width: 100%;
    background: #fff;
    flex-direction: row;
    padding: 30rpx;
    justify-content: space-around;
    z-index: 1;
    font-size: 28rpx;
    .nav-item {
      padding: 10rpx 5rpx;
      text-align: center;
      font-weight: bold;
      color: #666;
      &.active {
        color: #0174ff;
        border-bottom: 2rpx solid #0174ff;
      }
    }
  }
  .socket_close {
    z-index: 1000;
    position: fixed;
    top: 20rpx;
    width: 100%;
    .err-tip {
      align-items: center;
      justify-content: space-between;
      padding: 30upx 24upx;
      background-color: #2e8cef;
      color: #fff;
      .tip-btn {
        padding: 6upx 12upx;
        border: 1upx solid #fff;
        border-radius: 6upx;
        font-size: 26upx;
      }
    }
  }
  .btn {
    align-items: center;
    color: #fff;
    font-size: 34rpx;
    width: 630rpx;
    height: 104rpx;
    line-height: 104rpx;
    background: #2e8cef;
    border-radius: 50rpx;
    position: fixed;
    left: 50%;
    transform: translate(-50%);
    bottom: 100rpx;
  }
}
</style>
