<template>
  <view class="addressBook">
    <view class="addressBook-search">
      <view class="search-box">
        <view class="search-type">
          <text @click="openSearchType">{{ searchType == 1 ? '姓名' : '手机号' }}</text>
          <text class="icon"></text>
          <!-- <uni-icons v-if="param.department_id || param.user_name" style="padding: 10rpx;" type="clear" color="#c1c1c1"
            size="26" @click="cancleDeptSeled"></uni-icons> -->
        </view>
        <view class="search-body">
          <input v-if="searchType == 1" v-model="param.user_name" placeholder-class="my-input" confirm-type="search"
            placeholder="请输入姓名" @input="inputChange" />
          <input v-if="searchType == 2" v-model="param.phone" type="number" placeholder-class="my-input"
            confirm-type="search" placeholder="请输入11位手机号" @input="inputChange" />
        </view>
      </view>
      <view class="search-type-select" v-if="isSearchTypeBox">
        <text v-for="(item, index) in searchTypeData" :key="index" @click="searchTypeChange(item)">{{ item.text
          }}</text>
      </view>
      <view class="user">
        <view class="user-list" v-if="userList.length">
          <view class="list-item" v-for="(item, index) in userList" :key="index" @click="toDetail(item)">
            <view class="item-l">
              <text>{{ item.user_name[0] }}</text>
              <text>{{ item.user_name }}</text>
              <!-- <text v-if="item.roles[0].name == '站长'">创始人</text> -->
            </view>
            <view class="item-r">
              <text>{{ item.phone }}</text>
              <text :style="item.status == 0 ? 'color:#f65248;background: #F6524833' : ''">{{ item.status == 1 ? '启用' :
            '禁用'
                }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="addressBook-body">
      <view class="addressBook-body-item" v-for="(item, index) in departments" :key="index">
        <view class="item-1">
          <view>
            <text></text>
            <text @click.stop="toUserList(item)">{{ item.name }}</text>
          </view>
        </view>
        <view class="item-sub" v-for="(subItem, subIndex) in item.subs" :key="subIndex">
          <view style="display: flex;flex-direction: row;align-items: center;">
            <view>
              <text @click.stop="toUserList(subItem)">{{ subItem.name }}</text>
            </view>
            <myIcon v-if="subItem.subs" style="margin:0 10rpx;" type="you" color="#a1a1a1" size="24rpx">
            </myIcon>
          </view>
          <view class="item-sub-2" v-for="(subItem2, subIndex2) in subItem.subs" :key="subIndex2">
            <text @click.stop="toUserList(subItem2)">{{ subItem2.name }}</text>
            <!-- <myIcon type="you" color="#a1a1a1" size="24rpx">
            </myIcon> -->
          </view>
        </view>
      </view>
    </view>
    <view class="addressBook-btm">
      <view class="btm-box">
        <view class="btm-box-item" v-for="(item, index) in regulate" :key="index" @click="toSetMember(index)">
          <image :src="item.url" />
          <text>{{ item.text }}</text>
        </view>
      </view>
    </view>
    <view class="mask" v-if="isSearchTypeBox || userList.length" @click="closeSearch"></view>
    <my-popup position="bottom" ref="show_edit_pop" :show="show_edit_pop">
      <view class="inherit">
        <uni-icons v-if="!load" style="position: absolute;top: 0;right: 0; padding: 30rpx;" type="closeempty"
          color="#d1d1d1" size="26" @click="show_edit_pop = false"></uni-icons>
        <text class="title">{{ inheritType == 1 ? '离职继承' : '在职继承' }}</text>
        <view class="inherit-item">
          <text>{{ inheritType == 1 ? '离职人员' : '在职人员' }}</text>
          <text v-if="inheritType == 1" @click="selectMember">{{ member1 ? member1 : '请选择离职人员' }}</text>
          <text v-if="inheritType == 2" @click="selectMember">{{ member1 ? member1 : '请选择在职人员' }}</text>
          <myIcon @click="selectMember" style="padding: 0 20rpx;" type="xiala" color="#808080" size="24rpx">
          </myIcon>
        </view>
        <view class="inherit-item">
          <text>继承人员</text>
          <text @click="selectMember2">{{ member2 ? member2 : '请选择继承人员' }}</text>
          <myIcon @click="selectMember2" style="padding: 0 20rpx;" type="xiala" color="#808080" size="24rpx">
          </myIcon>
        </view>
        <button v-if="!load" @click='inherit'>确定继承</button>
        <view class="warn" v-else>
          <text>正在继承相关数据，请稍后...</text>
          <text>过程需要一些时间，请勿进行其它操作</text>
          <progress :percent="pgList[progress]" :duration="duration" active show-info activeColor="#10AEFF"
            stroke-width="6" />
        </view>
        <view v-if="progress == 2 && showStatus" class="status">
          <uni-icons type="checkbox" size="80" color="#10AEFF"></uni-icons>
          <text>数据继承完毕</text>
        </view>
      </view>
    </my-popup>
    <tMemberPicker :visible.sync="dialogs.adminPicker" v-model="admin_id" @confirm="confirmSeledAdmin"></tMemberPicker>
    <tMemberPicker :visible.sync="dialogs.adminPicker2" v-model="admin_id2" @confirm="confirmSeledAdmin2">
    </tMemberPicker>
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
import myPopup from '@/components/myPopup'
import tMemberPicker from '@/components/tplus/tMemberPicker.vue';
export default {
  components: {
    myIcon,
    myPopup,
    tMemberPicker
  },
  data() {
    return {
      showStatus: false,
      duration: 1000,
      progress: 0,
      pgList: [0, 75, 100],
      load: false,
      inheritType: 1,//1离职 2在职
      admin_id: null,
      admin_id2: null,
      member1: '',
      member2: '',
      dialogs: {
        adminPicker: false,
        adminPicker2: false
      },
      show_edit_pop: false,
      isSearchbody: false,
      isSearchTypeBox: false,
      userList: [],
      searchTypeData: [
        { text: '姓名', value: 1 },
        { text: '手机号', value: 2 }
      ],
      searchType: 1,
      param: {
        page: 1,
        per_page: 50,
        user_name: '',
        phone: ''
      },
      inheritParam: {
        from_id: 3,
        to_id: 1681
      },
      departments: [],
      regulate: [
        { url: 'https://img.tfcs.cn/tfyfxbb/tianjiachengyuan.png', text: '添加成员' },
        { url: 'https://img.tfcs.cn/tfyfxbb/jueseguanli.png', text: '角色管理' },
        { url: 'https://img.tfcs.cn/tfyfxbb/lizhijicheng.png', text: '离职继承' },
        { url: 'https://img.tfcs.cn/tfyfxbb/zaizhijicheng.png', text: '在职继承' }
      ]
    }
  },
  computed: {
    isAdmin() {
      let userInfo = JSON.parse(uni.getStorageSync('userInfo'))
      return userInfo.roles.some(item => item.name == '站长')
    },
    // isMe() {
    //   let userInfo = JSON.parse(uni.getStorageSync('userInfo'))
    //   return userInfo.id == this.userData.id
    // }
  },
  onLoad() {
    this.getDepartment()
  },
  methods: {
    getDepartment() {
      this.$ajax.get('/admin/personnelMatters/departments', {}, (res) => {
        if (res.statusCode == 200) {
          this.departments = res.data
        }
      })
    },
    getUserList() {
      this.$ajax.get('/admin/personnelMatters/memberList', this.param, (res) => {
        if (res.statusCode == 200) {
          this.userList = res.data.data
        }
      })
    },
    // 离职继承
    setDimissionInherit() {
      this.$ajax.post('/admin/personnelMatters/inheritMember', this.inheritParam, (res) => {
        if (res.statusCode == 200) {
          this.duration = 30
          this.progress = 2
          setTimeout(() => {
            this.showStatus = true
            setTimeout(() => {
              this.admin_id = null
              this.admin_id2 = null
              this.member1 = ''
              this.member2 = ''
              this.showStatus = false
              this.load = false
            }, 1500)
          }, 3000)
        }
      })
    },
    // 在职继承
    setInherit() {
      this.$ajax.post('/admin/personnelMatters/inheritMemberOnJob', this.inheritParam, (res) => {
        if (res.statusCode == 200) {
          this.duration = 30
          this.progress = 2
          setTimeout(() => {
            this.showStatus = true
            setTimeout(() => {
              this.admin_id = null
              this.admin_id2 = null
              this.member1 = ''
              this.member2 = ''
              this.showStatus = false
              this.load = false
            }, 1500)
          }, 3000)
        }
      })
    },
    openSearchType() {
      this.userList = []
      this.isSearchTypeBox = !this.isSearchTypeBox
    },
    inputChange(e) {
      this.isSearchTypeBox = false
      if (!e.detail.value) {
        this.userList = []
        this.isSearchbody = false
        return
      }
      if (this.searchType == 1) {
        this.param.user_name = e.detail.value
        this.getUserList()
      } else if (this.searchType == 2) {
        if (e.detail.value.length == 11) {
          this.param.phone = e.detail.value
          this.getUserList()
        }
      }
    },
    searchTypeChange(item) {
      this.param.user_name = ''
      this.param.phone = ''
      this.searchType = item.value
      this.isSearchTypeBox = false
    },
    closeSearch() {
      this.userList = []
      this.isSearchTypeBox = false
    },
    toSetMember(index) {
      switch (index) {
        case 0:
          if (this.isAdmin) {
            this.$navigateTo(`/addressBook/add_member`)
          } else {
            uni.showToast({
              title: '暂无权限',
              icon: 'none'
            })
          }
          break;
        case 1:
          if (this.isAdmin) {
            this.$navigateTo(`/addressBook/role_management`)
          } else {
            uni.showToast({
              title: '暂无权限',
              icon: 'none'
            })
          }
          break;
        case 2:
          if (this.isAdmin) {
            this.show_edit_pop = true
            this.inheritType = 1
          } else {
            uni.showToast({
              title: '暂无权限',
              icon: 'none'
            })
          }
          break;
        case 3:
          if (this.isAdmin) {
            this.show_edit_pop = true
            this.inheritType = 2
          } else {
            uni.showToast({
              title: '暂无权限',
              icon: 'none'
            })
          }
          break;

        default:
          break;
      }
    },
    toUserList(item) {
      this.$navigateTo(`/addressBook/user_list?id=${item.id}&title=${item.name}`)
    },
    toDetail(item) {
      this.$navigateTo(`/addressBook/user_detail?phone=${item.phone}`)
    },
    confirmSeledAdmin(data) {
      if (data.value.length) {
        this.member1 = data.label[0]
      }
      this.dialogs.adminPicker = false
    },
    confirmSeledAdmin2(data) {
      if (data.value.length) {
        this.member2 = data.label[0]
      }
      this.dialogs.adminPicker2 = false
    },
    selectMember() {
      this.dialogs.adminPicker2 = false
      this.dialogs.adminPicker = true
    },
    selectMember2() {
      this.dialogs.adminPicker = false
      this.dialogs.adminPicker2 = true
    },
    inherit() {
      if (!this.member1 || !this.member2) return
      this.inheritParam.from_id = this.admin_id
      this.inheritParam.to_id = this.admin_id2
      this.progress = 1
      this.load = true
      if (this.inheritType == 1) {
        this.setDimissionInherit()
      } else if (this.inheritType == 2) {
        this.setInherit()
      }
    }
  },
}
</script>
<style lang="scss" scoped>
.inherit {
  position: relative;
  padding: 32rpx;
  border-radius: 8px 8px 0 0;
  background-color: #ffffff;

  .title {
    width: 100%;
    text-align: center;
    font-size: 36rpx;
    font-weight: 500;
    color: #333;
    padding: 20rpx 0 22rpx;
  }

  .inherit-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 30rpx 20rpx;
    font-size: 32rpx;

    &>text:nth-child(1) {
      margin-right: 30rpx;
      color: #999;
    }

    &>text:nth-child(2) {
      color: #666;
    }
  }

  button {
    width: 100%;
    margin: 40rpx 0 20rpx;
    color: #fff;
    background-color: #488AF6;
  }

  .warn {
    width: 100%;
    margin: 40rpx 0 20rpx;

    text {
      margin-bottom: 20rpx;
    }

    &>text:nth-child(1) {
      font-size: 32rpx;
      font-weight: 500;
      color: #666;
    }

    &>text:nth-child(2) {
      font-size: 28rpx;
      font-weight: 400;
      color: #a1a1a1;
    }
  }

  .status {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 40rpx 0 20rpx;

    text {
      margin-top: 20rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #666;
    }
  }
}

.user {
  position: relative;

  .user-list {
    position: absolute;
    top: 19rpx;
    left: 0;
    width: 100%;
    overflow-y: auto;
    max-height: 800rpx;
    padding: 20rpx 0;
    border-radius: 0 0 16rpx 16rpx;
    background-color: #fff;

    .list-item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;
      height: 128rpx;
      padding: 32rpx;

      .item-l {
        display: flex;
        flex-direction: row;
        align-items: center;

        &>text:nth-child(1) {
          width: 60rpx;
          height: 60rpx;
          margin-right: 15rpx;
          border-radius: 50%;
          font-size: 32rpx;
          line-height: 60rpx;
          text-align: center;
          color: #fff;
          background-color: #488AF6;
        }

        &>text:nth-child(2) {
          font-size: 32rpx;
          font-weight: 500;
          color: #292C39;
        }

        &>text:nth-child(3) {
          margin-left: 15rpx;
          font-size: 24rpx;
          padding: 6rpx 16rpx;
          color: #292C39;
          border-radius: 4px;
          color: #488AF6;
          background: #488AF633;
        }
      }

      .item-r {
        display: flex;
        flex-direction: row;
        align-items: center;

        &>text:nth-child(1) {
          margin-right: 15rpx;
          font-size: 32rpx;
          color: #292C3966;
        }

        &>text:nth-child(2) {
          border-radius: 4px;
          padding: 6rpx 16rpx;
          font-size: 24rpx;
          color: #13A834;
          background: #13A83433;
        }
      }
    }
  }
}

@keyframes gradual {
  0% {
    width: auto;
    height: 0;
  }

  100% {
    width: auto;
    height: 170rpx;
  }
}

@keyframes gradualMask {
  0% {
    width: 100vw;
    height: 0vh;
  }

  100% {
    width: 100vw;
    height: 100vh;
  }
}

.mask {
  position: fixed;
  z-index: 1;
  width: 100%;
  height: 100vh;
  left: 0;
  background-color: rgba($color: #000000, $alpha: 0.5);
  display: block;
  animation-name: gradualMask;
  animation-duration: 0.2s;
}

.icon {
  display: inline-block;
  width: 0;
  height: 0;
  margin: 4px 0 0 8px;
  border: 4px solid transparent;
  border-top-color: #b8b9ba;
}

.my-input {
  font-size: 28rpx;
  color: #292C3966;
}

.addressBook {
  // padding: 0 32rpx;

  .addressBook-search {
    position: sticky;
    top: 0;
    padding: 20rpx 32rpx;
    background-color: #fff;
    z-index: 9;

    .search-box {
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 24rpx 32rpx;
      border-radius: 16rpx;
      background-color: #f6f6f6;

      .search-type {
        display: flex;
        flex-direction: row;
        align-items: center;
        font-size: 28rpx;
        color: #292C39B2;
        margin-left: 30rpx;
      }

      .search-type::after {
        content: '';
        display: inline-block;
        width: 1px;
        height: 24rpx;
        margin: 0 20rpx;
        background-color: #292C39B2;
      }

      .search-body {
        // margin-left: 20rpx;
      }
    }

    .search-type-select {
      position: absolute;
      top: 123rpx;
      left: 32rpx;
      padding: 20rpx 35rpx;
      border-radius: 0 0 4px 4px;
      background-color: #fff;
      z-index: 9;
      animation-name: gradual;
      animation-duration: 0.2s;

      text {
        color: #666;
        font-size: 28rpx;
        padding: 20rpx;
      }

      &>text:nth-child(1) {
        border-bottom: 1px solid #f6f6f6;
      }
    }
  }

  .addressBook-body {
    width: 100%;
    padding: 0 32rpx 200rpx;

    .addressBook-body-item {
      display: flex;
      padding: 30rpx 0 30rpx 32rpx;

      .item-1 {
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        view {
          display: flex;
          flex-direction: row;
          align-items: center;
          font-size: 32rpx;
          color: #333;

          &>text:nth-child(1) {
            width: 24rpx;
            height: 24rpx;
            margin-right: 30rpx;
            margin-bottom: 10rpx;
            border-left: 1px solid #292C3966;
            border-bottom: 1px solid #292C3966;
          }
        }
      }

      .item-sub {
        display: flex;
        flex-direction: row;
        // justify-content: space-between;
        width: 88vw;
        margin-top: 30rpx;
        color: #666;
        padding-left: 85rpx;
        overflow-x: auto;
        white-space: nowrap;

        .item-sub-2 {
          padding-right: 40rpx;
          color: #999;
          font-size: 28rpx;
        }
      }
    }
  }

  .addressBook-btm {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 0 32rpx;
    border-top: 1px solid #f6f6f6;
    background-color: #fff;

    .btm-box {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding: 20rpx 10rpx 40rpx;

      .btm-box-item {
        display: flex;
        align-items: center;

        image {
          display: inline-block;
          width: 80rpx;
          height: 80rpx;
          margin-bottom: 20rpx;
        }

        text {
          font-size: 24rpx;
          color: #488AF6;
        }
      }
    }
  }
}
</style>