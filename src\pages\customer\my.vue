<template>
  <view class="page" @click="handlePageClick">
    <view
      style=" background: #3975C6;width: 100%;height: 160rpx; color: #ffff;font-size: 30rpx; text-align: center; line-height: 240rpx;"
      class="titlebgc" v-if="showBackgroundImage">我的</view>
    <view class="mybgc">
      <view class="flex-row items-center" style="margin-top: 130rpx;">
        <!-- 信息卡片 -->
        <view style="margin-right: 32rpx;">
          <!-- <Avatar :username="user.user_name" background-color="#2C5A99" color="#fff"
                              style=" vertical-align: middle;" :inline="true">
                          </Avatar> -->
          <view class="namecalss" @click="goPersonalPage">{{ name }}</view>
        </view>
        <view class="my-info">
          <view style="display: flex;flex-direction: row;">
            <view style="
                                  margin:6rpx 16rpx 16rpx 0;
                                  color: #ffffff;
                                  font-size: 36rpx;
                                  ">
              {{ user.user_name || '' }}
            </view>
            <!-- <view class="my-svg">{{user. department }}</view> -->
          </view>
          <view style="
                              display: flex;
                              flex-direction: row;
                              color:  #ffff;
                              font-size: 24rpx;
                          ">
            <!-- 销售部 -->
            <view>{{ user.department || '' }}</view>
            <text style="margin: 0 8px;"> | </text>

            <text> {{ user.phone || '' }} </text>
          </view>
        </view>
      </view>
    </view>
    <!-- tab标签 -->
    <view :class="[(isFixedTop) ? 'fixedTop' : '']" id="box" class="box bgwhite">
      <view>

        <view class="my-tab">
          <view class="my-tab-left">
            <view v-for="(item, index) in mylist" :key="item.id" @tap="changeAct(item)">
              <view :class="{ 'tabShow': act == item.id }" style=" margin:0 16rpx;">
                {{ item.name }}
              </view>
              <view :class="{ 'tabar': act == item.id }" style=" margin:0 16rpx;"></view>
            </view>
          </view>
          <view style="flex-direction: row;">
            <workStatus :dialogVisible.sync="dialogs.switchWorkStatus" :status="user.is_allocation"  v-show="act=== 1"/>
            <!-- act=== 1 -->
            <!-- @click="actShowFn" -->
            <view v-if="act === 1">
              <!-- @click="buttonFn" -->
              <button class="my-tab-button" @click="buttonFn">智慧经营</button>
            </view>
            <view v-if="false">
              <button class="my-tab-button" @click="buttonBn">智慧经营</button>
            </view>
          </view>
        </view>

      </view>
    </view>
    <!-- 为保证固定定位不会跑 添加盒子 -->
    <view class="sckterbox" v-if="rowShow"></view>
    <view>
      <view>

        <!-- 电话号码以下 -->
        <!-- 客户 -->
        <my_custom :text="text" :keywords="keywords" v-if="act === 1" class="botton" :myhouseList="myhouseList"
          ref="myCustom"></my_custom>
        <!-- 房源 -->
        <my_house :text="text" :keywords="keywords" v-if="act === 16" class="botton" :houseLIst="houseLIst"></my_house>
      </view>

    </view>

    <div class="logout-wrapper">
      <my-button block type="primary" size="big" @click="logout">退出登录</my-button>
    </div>
  </view>
</template>
<script>
import my_custom from "@/components/my-work/my_custom"
import my_house from "@/components/my-work/my_house"
import my_new from "@/components/my-work/my_new "
import myButton from '@/components/house/myButton'
import workStatus from "@/components/my-work/workStatus"
// 默认头像
export default {
  components: {
    my_custom,
    my_house,
    my_new, myButton, workStatus
  },
  data() {
    return {
      // 默认激活样式是第一个
      act: '',
      mylist: [],
      myhouseList: [],// 客户
      currentTabIndex: 2,
      content: '',
      houseLIst: [],// 房源
      user: {
          is_allocation: -1     //是否正常接单
      },
      text: "",
      name: '',
      keywords: "",
      actShow: true,// 智慧经营
      isFixedTop: false,// 吸顶
      rowShow: false,//控制盒子的显示和隐藏
      showBackgroundImage: false, // 控制背景图片显示与隐藏
      dialogs: {
          switchWorkStatus: false
      }
    }
  },
  onLoad() {
    this.getmyOneList()
    this.getInforUser()
    // 接收父组件传过来的值
    uni.$on("getData", () => {
      let text = uni.getStorageSync('text')
      let keywords = uni.getStorageSync('keywords')
      if (text) {
        this.text = text
      }
      if (keywords) {
        this.keywords = keywords
      }
    }
    )
    uni.showLoading({
      title: "加载中",
    })
    //  uniapp里面的吸顶
    uni.pageScrollTo({
      scrollTop: 0,
      duration: 0
    })
    setTimeout(() => {
      this.GetTop()
      // this.aaa()
    }, 1000)
  },
  onUnload() {
    // 传过来要销毁
    uni.$off("getData")
  },
  async onPullDownRefresh() {
    try {
      this.getInforUser();
      const curId = this.mylist[0].id;
      if (this.act === curId) {
        this.$refs.myCustom.refresh()
      } else {
        this.act = curId
      }
      await this.$Utils.sleep(500)
    } catch (e) { }
    uni.stopPullDownRefresh();
  },
  //监测页面滑动
  onPageScroll(e) {
    if (e.scrollTop > this.Topdistance) {
      // console.log(e.scrollTop,'e.scrollTop');
      this.isFixedTop = true
      this.rowShow = true
    } else {
      this.isFixedTop = false
      this.rowShow = false
    }

    // 当页面滚动时触发该事件
    const scrollTop = e.scrollTop;
    // 根据滚动高度来设置背景图片的显示与隐藏
    this.showBackgroundImage = scrollTop > 60; // 滚动到200的高度时显示背景图片
  },
  methods: {
    // 清空
    del() {
      uni.removeStorageSync('mobiles');
      uni.removeStorageSync('keywords');
    },
    goPersonalPage(){
        this.$navigateTo('/customer/personal')
    },
    GetTop() {
      console.log(1111);
      // 获取元素距离顶部的距离
      var _this = this
      uni.getSystemInfo({
        success: (resu) => {
          const query = uni.createSelectorQuery()
          query.select('#box').boundingClientRect()
          query.selectViewport().scrollOffset()
          query.exec(function (res) {
            console.log(res, "======");
            _this.Topdistance = res[0]?.top

          })
        },
        fail: (res) => { }
      })
    },
    // 智慧经营
    // actShowFn(){
    //     uni.showToast({
    //       title: "更新中....",
    //       icon: "none",
    //     });
    // },
    // 客户
    buttonFn() {
      this.$navigateTo("/pages/webview?url="+encodeURIComponent('/customer/wisdomWork'))
    },
    // 房源
    // buttonBn(){
    //     this.$navigateTo("/customer/wisdomHours")
    // },
    // 获取用户信息
    getInforUser() {
      // this.user = JSON.parse(uni.getStorageSync("userInfo"))
      // console.log(this.user.phone, '我的用户');
      this.$ajax.get('/qywx/my/query', {}, (res) => {
        console.log(res.data, '获取一级菜单');
        if (res.statusCode === 200) {
          console.log(res.data, '6666666');
          this.user = res.data
          let name = this.user.user_name.split("");
          console.log(name, "名字");
          this.name = name[0];
          uni.hideLoading()
        } else {
          uni.hideLoading()
        }
      },
        () => {
          uni.hideLoading()
        }
      )
    },
    // 获取一级菜单
    getmyOneList() {
      this.$ajax.get('/qywx/welcome/my_menu_list', {}, (res) => {
        console.log(res.data, '获取一级菜单');
        if (res.statusCode === 200) {
          this.mylist = res.data
          // console.log(res.data[0].children, '二级菜单');
          let index = res.data.findIndex(item => item.id == 1)
          if (index >= 0) {
            this.myhouseList = res.data[index].children
          }
          let index1 = res.data.findIndex(item => item.id == 16)
          console.log(index1, '44');
          if (index1 >= 0) {
            this.houseLIst = res.data[index1].children
          }
          // this.myhouseList = res.data.children
          // console.log(res.data[1].children, '二级菜单');
          // this.houseLIst = res.data.children
          this.act = this.mylist[0].id
          uni.hideLoading()
        } else {
          uni.hideLoading()
        }
      },
        () => {
          uni.hideLoading()
        }
      )
    },
    switchTabs(index, item) {
      console.log(index, item, "1234567890===");
      if (this.currentTabIndex == index) {
        return;
      }
      uni.navigateTo({
        url: item.path,
      });
    },
    // tab互斥效果
    changeAct(item) {
      this.del()
      console.log(item.id, 'tab');
      // 激活样式是当前点击的对应下标--list中对应id
      this.act = item.id;
    },
    //退出登录
    logout() {
      const website_id = uni.getStorageSync('website_id');
      uni.clearStorageSync('token' + website_id);
      uni.clearStorageSync('wxwork_token');
      uni.redirectTo({
        url: '/pages/user/phone_login'
      });
    },
    handlePageClick(){
        this.dialogs.switchWorkStatus = false;
    }
  }
}
</script>
<style lang="scss">
.titlebgc {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  transition: all 1s;
}

.namecalss {
  width: 128rpx;
  height: 128rpx;
  text-align: center;
  line-height: 128rpx;
  border-radius: 50%;
  font-size: 50rpx;
  background: #2C5A99;
  color: #fff
}

.sckterbox {
  width: 100%;
  height: 112rpx;
  background: #FFF;
}

.fixedTop {
  position: fixed;
  width: 100%;
  top: 160rpx;
  left: 0;
  z-index: 999;
}

.page {
  background-color: #F6F6F6;
}

// .all{
//     width: 100%;
//     height: 100%;
// }

.mybgc {
  width: 100%;
  height: 320rpx;
  padding: 32rpx;
  background: #3975C6;
}

.my-text {
  margin-top: 116rpx;
  color: #FFF;
  text-align: center;
  font-family: PingFang SC;
  font-size: 36rpx;
  font-style: normal;
  font-weight: 500;
  line-height: 36rpx;
}

.my-bgc {
  width: 100%;
  padding: 32rpx;
  display: flex;
  flex-direction: row;
  background-color: #fff;
  border-radius: 16rpx;
  margin: 0 auto;
  margin-top: -92rpx;
  justify-content: space-between;
  align-items: flex-start;

}

.my-svg {
  color: #488AF6;
  font-family: PingFang SC;
  height: 40rpx;
  font-size: 22rpx;
  margin-top: 16rpx;
  font-weight: 500;
  background: rgba(72, 138, 246, 0.20);
  padding: 6rpx 8rpx 4rpx 8rpx;
  border-radius: 8rpx;
}

.my-tab {
  width: 100%;
  // height: 55px;
  display: flex;
  padding: 32rpx 32rpx 32rpx 16rpx;
  flex-direction: row;
  justify-content: space-between;
  background: #FFF;
  // box-shadow: inset 6rpx -4rpx 20rpx 0px rgba(0, 0, 0, 0.05);
}

.my-tab-left {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.my-tab-button {
  // width: 144rpx;
  height: 56rpx;
  padding: 12rpx 24rpx;
  border-radius: 32rpx;
  background: linear-gradient(135deg, #6CA5FC 0%, #236DFE 100%);
  color: #FFF;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 32rpx;
  /* 133.333% */
}



// tab标签
.tabShow {
  color: #292C39;
  font-size: 36rpx;
  font-weight: 700;
}

.tabar {
  width: 72rpx;
  height: 16rpx;
  background-color: #236DFE;
  border-radius: 60rpx;
}

.logout-wrapper {
  box-sizing: border-box;
  padding: 52rpx 48rpx;
}
</style>