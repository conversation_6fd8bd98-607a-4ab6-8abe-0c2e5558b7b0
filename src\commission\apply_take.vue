<template>
  <view class="list">
    <view class="take_money">
      <text class="title">可提金额（元）</text>
      <text class="title-b">{{ user_withdraw.brokerage_balance_amount }}</text>
    </view>
    <view class="take_money">
      <view class="title">提现金额（元）</view>
      <view class="input-box row">
        <text style="font-size:64rpx">￥</text>
        <input
          type="number"
          placeholder="请输入提现金额"
          :class="[isEnter ? 'uni-input-value' : 'uni-input']"
          @input="watchInput"
          v-model="money"
        />
        <view v-if="isEnter" class="clear" @click="clearMoney"
          ><text>X</text></view
        >
      </view>
      <view v-if="withdraw_fee > 0 && isEnter" class="desc"
        >提现手续费{{ withdraw_fee }}%，预计到账{{ end_money }}元</view
      >
    </view>
    <view class="take_money " @click="pickerVisibleBank = true">
      <text class="title">银行卡</text>
      <view class="check row">
        <text v-if="bank_name == ''">请选择银行</text>
        <text v-else style="font-size: 36rpx;color: #333333;"
          >{{ bank_name }}&nbsp;&nbsp;{{ card_no | formatBankCard }}</text
        >
        <myIcon type="you" size="20rpx"></myIcon
      ></view>
    </view>
    <!-- 弹出选择框 -->
    <VuePicker
      :data="pickDataBank"
      title="请选择银行卡类型"
      cancelText="取消"
      confirmText="确认"
      :showToolbar="true"
      @cancel="cancelBank"
      @confirm="confirmBank"
      :visible.sync="pickerVisibleBank"
    />
    <view class="btn" @click="onSubmit">提交</view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import VuePicker from "vue-pickers";
import myIcon from "@/components/my-icon";
import { mapActions } from "vuex";
export default {
  components: { myIcon, VuePicker },
  data() {
    return {
      money: "",
      isEnter: false,
      user_withdraw: {},
      pickDataBank: [],
      pickerVisibleBank: false,
      bank_name: "",
      card_no: "",
      real_name: "",
      withdraw_fee: "",
      end_money: "",
    };
  },
  onLoad() {
    this.getWithdrawData();
    this.getBankList();
  },
  methods: {
    ...mapActions(["getSetting"]),
    getWithdrawData() {
      this.$ajax.get("/client/my/query/brokerage", {}, (res) => {
        if (res.statusCode === 200) {
          this.user_withdraw = res.data;
          this.getSetting((e) => {
            this.withdraw_fee = parseFloat(e.withdraw_fee);
          });
        }
      });
    },
    cancelBank() {},
    confirmBank(res) {
      res.map((item) => {
        this.bank_name = item.label;
        this.card_no = item.card_no;
        this.real_name = item.real_name;
      });
    },
    getBankList() {
      this.$ajax.get("/client/user/bank_card/all", {}, (res) => {
        if (res.statusCode === 200) {
          var arr = res.data.map((item) => {
            return {
              value: item.id,
              label: item.bank_name,
              card_no: item.card_no,
              real_name: item.real_name,
            };
          });
          this.pickDataBank.push(arr);
        }
      });
    },
    watchInput(e) {
      var value = parseFloat(e.detail.value);
      this.$nextTick(() => {
        if (value) {
          let withdraw_fee = this.withdraw_fee / 100;
          this.isEnter = true;
          this.end_money = this.money - this.money * withdraw_fee;
          this.end_money = this.end_money.toFixed(2);
        } else {
          this.isEnter = false;
        }
      });
    },
    clearMoney() {
      if (this.money) {
        this.money = "";
        this.isEnter = false;
      }
    },
    onSubmit() {
      if (!this.money) {
        uni.showToast({
          title: "请输入提现金额",
          icon: "none",
        });
      } else if (this.money < 100) {
        uni.showToast({
          title: "提现金额不能小于100元",
          icon: "none",
        });
      } else if (!this.bank_name) {
        uni.showToast({
          title: "请选择银行卡",
          icon: "none",
        });
      } else if (
        /^(?!0+(?:\.0+)?$)(?:[1-9]\d*|0)(?:\.\d{1,2})?$/.test(this.money)
      ) {
        this.$ajax.post(
          "/client/my/apply/brokerage/withdraw",
          {
            amount: this.money,
            bank_name: this.bank_name,
            bank_card_no: this.card_no,
            real_name: this.real_name,
          },
          (res) => {
            if (res.statusCode === 200) {
              uni.showToast({
                title: "发起提现成功",
              });
              this.clearMoney();
              this.getWithdrawData();
            } else {
              uni.showToast({
                title: res.data.message || "发起提现失败",
                icon: "none",
              });
            }
          }
        );
      } else {
        uni.showToast({
          title: "最多保留两位小数",
          icon: "none",
        });
        // 返回false即为校验失败
        return false;
      }
    },
  },
  onPullDownRefresh() {
    this.getWithdrawData();
    this.getBankList();
    uni.stopPullDownRefresh();
  },
};
</script>

<style scoped lang="scss">
page {
  background: #f5f5f5;
}
.take_money {
  margin: 16rpx 0;
  background: #fff;
  padding: 24rpx 48rpx;
  line-height: 64rpx;
  .title {
    font-size: 28rpx;
    color: #999;
  }
  .title-b {
    font-size: 64rpx;
    color: #333;
  }
  .input-box {
    align-items: center;

    .uni-input {
      margin-left: 20rpx;
    }
    .uni-input-value {
      font-size: 64rpx;
    }
    .clear {
      color: #fff;
      width: 50rpx;
      height: 40rpx;
      background: #b5b5b5;
      border-radius: 50%;
      align-items: center;
      line-height: 44rpx;
    }
  }
  .check {
    font-size: 36rpx;
    color: #999;
    align-items: center;
    justify-content: space-between;
    text {
      text-align: center;
    }
  }
  .desc {
    font-size: 28rpx;
    color: #fb656a;
  }
}
.btn {
  margin: 40rpx 48rpx 0;
  align-items: center;
  color: #fff;
  padding: 30rpx;
  background: #0174ff;
  box-shadow: 0 4px 16px 0 rgba(1, 116, 255, 0.4);
  border-radius: 22px;
}
</style>
