<template>
  <view class="photos">
    <view class="photoList">
      <view class="photo">
        <view class="title">请上传图片</view>
        <view class="photo_item">
          <upload
            :upInfo="upInfo"
            @uploadDone="uploadSuccess($event)"
            :action="upload_api"
            @delImg="delImg"
            :imgs="cateList"
            :chooseType="1"
          ></upload>
  
        </view>
      </view>
      <view class="btn" @click="submit">确定 </view>
    </view>
  </view>
</template>

<script>

import upload from "./components/upload.vue";
import myIcon from '@/components/my-icon'
export default {
  components: {
    upload,
    myIcon
  },
  data () {
    return {
      photoList: [],
      cateList: [

      ],
      upInfo: {
        category: 6,
      },
      upload_api: '/common/file/upload/admin',
      currentCate: "all",
      show_pop: false,
      detail: {}

    }
  },
  onLoad () {
    this.cateList = uni.getStorageSync("uploadImg") ? JSON.parse(uni.getStorageSync("uploadImg")) : []
    uni.removeStorageSync("uploadImg")
  },
  methods: {

    uploadSuccess (e) {
      console.log(e);
      // let obj = {
      //   url: e.url,
      // }
      this.cateList = e
      // this.uploadImg(JSON.stringify(arr))
    },
    delImg (e) {
      this.cateList = e
    },
    submit () {
      console.log(123);
      this.$navigateBack()
      setTimeout(() => {
        uni.$emit("uploadOk", this.cateList)
      }, 200);
    }


  }

}
</script>

<style scoped lang="scss">
.photos {
  ::-webkit-scrollbar {
    display: none;
    width: 0 !important;
    height: 0 !important;
    -webkit-appearance: none;
    background: transparent;
  }
  // background: #f7f7f7;
  // padding-bottom: 100rpx;、
  min-height: 100vh;
  .filters {
    padding: 24rpx;
    position: sticky;
    background: #f7f7f7;
    top: 0;
    z-index: 2;
    justify-content: space-between;
    &.pop {
      background: #f7f7f7;
      height: 100vh;
      display: block;
      .close {
        position: absolute;
        top: 20rpx;
        right: 24rpx;
        width: 40rpx;
        height: 40rpx;
        padding: 10rpx;
        border-radius: 50%;
        background: #d8d8d8;
        color: #666;
      }
      .filter_list {
        padding-top: 50rpx;
        .filter_item {
          min-width: calc((100% - 48rpx - 48rpx) / 4);
          margin-bottom: 24rpx;
        }
      }
    }
    .filter_t {
      // min-width: 115rpx;
      padding-top: 10rpx;
      margin-right: 10rpx;
    }
    .filter_list {
      overflow-y: auto;
      .filter_item {
        padding: 20rpx;
        background: #fff;
        font-size: 22rpx;
        // text-align-last: justify;
        margin-right: 16rpx;
        text-align: center;
        // margin-bottom: 10rpx;
        min-width: 136rpx;
        color: #666;
        border-radius: 64rpx;
        &.active {
          background: #3e81d6;
          color: #fff;
        }
      }
    }
  }
  .photoList {
    padding: 10rpx 24rpx;
    .photo {
      .title {
        font-size: 30rpx;
        padding: 24rpx 0;
        color: #666;
      }
      .photo_item {
        // overflow: hidden;
        ::v-deep .img-box {
          margin-right: 24rpx;
          width: calc((100% - 72rpx) / 4);
          height: 0;
          padding-bottom: calc((100% - 72rpx) / 4);
          text-align: center;
          box-sizing: border-box;
          margin-bottom: 24rpx;
          // overflow: hidden;
          &:nth-child(4n) {
            margin-right: 0;
          }
          image {
            width: 100%;
            height: 100%;
            margin-right: 0;
            margin-top: 0;
            position: absolute;
          }
        }
        ::v-deep .icon-box {
          margin-top: 0;
          width: calc((100% - 72rpx) / 4);
          height: calc((100vw - 72rpx - 24rpx) / 4);
        }
      }
    }
  }
}
.btn {
  position: fixed;
  left: 48rpx;
  right: 48rpx;
  bottom:60rpx;
  text-align: center;
  background: #3e81d6;
  color: #fff;
  font-size: 28rpx;
  border-radius: 10rpx;
  padding: 20rpx 0;
}
</style>