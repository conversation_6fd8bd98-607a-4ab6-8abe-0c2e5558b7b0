<template>
    <wisdomContainer>
        <wisdomTitleBar>
            成员数据统计
            <template #right>
                <wisdomFilters v-model="params.date"></wisdomFilters>
            </template>
        </wisdomTitleBar>

        <view class="menus">
            <view class="menus-item" v-for="(item,index) in menus" :key="index" @click="goPath(item.path)">
                <view class="icon">
                    <image :src="item.icon | imgDomain" mode="widthFix"/>
                </view>
                <view class="title">{{ item.title }}</view>
            </view>
        </view>
    </wisdomContainer>
</template>

<script>
import wisdomContainer from './components/wisdomContainer';
import wisdomTitleBar from './components/wisdomTitleBar';
import wisdomFilters from './components/wisdomFilters';
export default {
    components: {
        wisdomContainer,
        wisdomTitleBar,
        wisdomFilters
    },
    data(){
        return {
            params: {
                date: ''
            },
            menus: [
                { title: '分客', path: '/wisdomWork/user_data_sorted', icon: '/icons/wisdom-work/crm-followed.png' },
                { title: '带看', path: '/wisdomWork/user_data_seed', icon: '/icons/wisdom-work/crm-seed.png' },
                { title: '回访', path: '/wisdomWork/user_data_visit', icon: '/icons/wisdom-work/crm-call.png' },
                { title: '维护', path: '/wisdomWork/user_data_maintain', icon: '/icons/wisdom-work/crm-maintain.png' },
                { title: '主播', path: '/wisdomWork/user_data_anchor', icon: '/icons/wisdom-work/crm-anchor.png' },
                { title: '流转', path: '/wisdomWork/user_data_trans', icon: '/icons/wisdom-work/crm-trans.png' }
            ]
        }
    },
    methods: {
        goPath(path){
            this.$navigateTo(path+(path.includes('?') ? '&' : '?')+'date='+this.params.date)
        }
    },
}
</script>

<style lang="scss" scoped>
.menus{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    background-color: #fff;
    padding: 8rpx;
    border-radius: 16rpx;
    .menus-item{
        width: 25%;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 24rpx 0;
        .icon{
            width: 88rpx;
            height: 88rpx;
            image{
                width: 100%;
                height: 100%;
            }
        }
        .title{
            color: #4E5969;
            font-size: 24rpx;
            text-align: center;
            margin-top: 16rpx;
        }
    }
}
</style>