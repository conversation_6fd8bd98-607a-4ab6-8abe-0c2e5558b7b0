<template>
  <view class="house_item" @click="toDetail(house)">
    <view class="house_name flex-row">
      <view class="house_img">
        <image :src="house.pic" mode="espectFit"></image>
        <view
          class="istop"
          :class="{
            wuxiao: house.trade_status_id == 8,
            zanhuan: house.trade_status_id == 5,
            chengjiao: house.trade_status_id == 100 || house.trade_status_id == 101,
            suoding: house.trade_status_id == 102,
          }"
          v-if="house.trade_status_id"
          >{{ house.trade_status | statusFilter }}</view
        >
        <view class="picnum" v-if="house.pic_num">{{ house.pic_num }}张</view>
      </view>
      <view class="house_info flex-1">
        <text class="house_title">
          <!-- <text class="flex-row items-center du mr12" v-if="house.exclusive == 1"> -->
          <image
            class="du_img"
            v-if="house.exclusive == 1"
            src="@/static/icon/index/<EMAIL>"
            mode="widthFix"
          ></image>
          <!-- </text> -->
          {{ house.title }}
          <text style="margin-left: 3px">
            | {{ house.shi }}室{{ house.ting }}厅{{ house.wei }}卫
          </text>
          <text style="margin-left: 3px" v-if="house.mianji"> | {{ house.mianji }}平米 </text>
          <text style="margin-left: 3px" v-if="house.chaoxiang"> | {{ house.chaoxiang }} </text>
          <text style="margin-left: 3px" v-if="house.total_floor || house.sz_floor">
            | {{ house.sz_floor }} <template v-if="house.sz_floor">/</template
            >{{ house.total_floor }}层</text
          >
          <!-- <text class="ctime">{{ house.ctime }}</text> -->
        </text>
        <view class="house_area small mt12 flex-row">
          <!-- <text class="mr12" v-if="house.floor && house.total_floor"
            >{{ house.floor }}/{{ house.total_floor }}层
          </text>
          <text v-if="house.floor && house.total_floor" class="line mr12">|</text> -->
          <!-- <text class="mr12">{{ house.loudong }}-{{ house.danyuan }}-{{ house.fanghao }}</text>
          <text class="line mr12">|</text> -->
          <text class="mr12"
            ><text>编号:</text> <text>{{ house.id }}</text></text
          >
          <text class="mr12"
            ><text>类型:</text> <text>{{ house.trade_type }}</text></text
          >
          <!-- <text class="line mr12">|</text> -->
          <text class="mr12">{{ house.area_name }}</text>
          <text class="mr12">{{ house.region_name }}</text>
        </view>
        <view class="house_area small mt24 flex-row items-center">
          <!-- <view class="flex-row mr12 zushou" v-if="house.trade_type">
            {{ house.trade_type }}
          </view> -->
          <!-- <view class="flex-row items-center du mr12" v-if="house.exclusive == 1">
            <image src="/static/icon/index/<EMAIL>" mode="widthFix"></image>
          </view> -->
          <!-- <view
            class="flex-row items-center youxiao key mr12"
            :class="{ wuxiao: house.trade_status_id == 8 }"
            v-if="house.trade_status_id == 8 || house.trade_status_id == 9"
          >
            <text>{{ house.trade_status }}</text>
          </view> -->
          <view class="flex-row items-center justify-center level a mr12" v-if="house.level == 'A'">
            A级
          </view>
          <view class="flex-row items-center justify-center level b mr12" v-if="house.level == 'B'">
            B级
          </view>
          <view class="flex-row items-center justify-center level c mr12" v-if="house.level == 'C'">
            C级</view
          >
          <view
            class="flex-row items-center justify-center company mr12"
            v-if="house.is_share == 1"
          >
            公盘</view
          >
          <view class="flex-row items-center weituo key mr12" v-if="house.wtr_id">
            <view class="img mr12">
              <image src="/static/icon/index/<EMAIL>" mode="widthFix"></image>
            </view>
            <text>委托</text>
          </view>
          <view class="flex-row items-center jujiao key mr12" v-if="house.is_top == 1">
            <!-- <view class="img mr12">
              <image src="/static/icon/index/<EMAIL>" mode="widthFix"></image>
            </view> -->
            <text>聚焦</text>
          </view>
          <view class="flex-row items-center vip key mr12" v-if="house.djwtr_id">
            <view class="img mr12">
              <image src="/static/icon/index/<EMAIL>" mode="widthFix"></image>
            </view>
            <text>VIP</text>
          </view>
          <view class="flex-row items-center key mr12" v-if="house.ysr_id > 0">
            <view class="img mr12">
              <image src="/static/icon/index/<EMAIL>" mode="widthFix"></image>
            </view>
            <text>有钥匙</text>
          </view>
        </view>
        <view class="jiangjia" v-if="house.price_change && house.price_change.num > 0">
          <view class="pr_top">
            <view class="pr_label_item flex-row items-center">
              <view class="pr_label flex-row items-center flex-1">
                <view class="img">
                  <image src="@/static/icon/jiang.png" mode="widthFix" alt="" />
                </view>
                <view class="pr_label_name">
                  近期业主调价{{ house.price_change.num }}次
                  {{ house.price_change.direct_desc }}
                  {{ house.price_change.range | formatPrice(house.trade_type_status) }}
                </view>
              </view>
              <!-- <view class="pr_label_right flex-row flex-1"></view> -->
            </view>
          </view>
        </view>
        <view class="footer">
          <view class="footer_info flex-row items-center">
            <text class="title_price mr12 no-wrap">
              <template v-if="house.trade_type_status === 1 || house.trade_type_status === 3">
                <template v-if="house.sale_price">
                  <text class="price_con big">{{ house.sale_price | priceFilter }}</text>
                  <template v-if="house.price_change && house.price_change.num > 0">
                    <text :class="house.price_change.direct == 1 ? 'up' : 'down'">
                      {{ house.price_change.direct == 1 ? '↑' : '↓' }}
                    </text>
                  </template>
                  <!-- <text class="price_unit">万</text> -->
                </template>
                <template v-else>
                  <text class="price_con">面议</text>
                </template>
              </template>
              <template v-if="house.trade_type_status === 2">
                <template v-if="house.rent_price">
                  <text class="price_con">{{ house.rent_price }}</text>
                  <text class="price_unit">元/月</text>
                </template>
                <template v-else>
                  <text class="price_con">面议</text>
                </template>
              </template>
            </text>
            <text
              class="mr12 no-wrap"
              v-if="
                (house.trade_type_status === 1 || house.trade_type_status === 3) && house.danjia
              "
              >{{ house.danjia }}元/m²
            </text>
            <text class="mr12 no-wrap" v-if="house.trade_type_status === 3 && house.rent_price"
              >{{ house.rent_price }}元/月
            </text>
            <view class="oper_btn flex-row items-center" v-if="false">
              <Dropdown ref="dropdown" :is_bottom="false">
                <my-button
                  style="color: #8a929f; font-size: 32rpx"
                  plain
                  size="big"
                  block
                  :round="false"
                  >...</my-button
                >
                <view class="options_list" slot="dropdown_list">
                  <!-- 已经上架不显示 -->

                  <DropdownItem plain :round="false" size="big" @click="houseGroup(house)"
                    >一键房源群发</DropdownItem
                  >
                  <DropdownItem
                    plain
                    :round="false"
                    size="big"
                    v-if="!(house.unilateral_agent == 1 && house.unilateral_agent_auth == 0)"
                    @click="setLevel(house)"
                    ><picker
                      :range="levelArr"
                      :value="levelIndex"
                      @change="changeLevel($event, house)"
                      mode="selector"
                      >设置等级</picker
                    ></DropdownItem
                  >
                  <DropdownItem
                    plain
                    :round="false"
                    size="big"
                    v-if="house.unilateral_agent == 1 && house.unilateral_agent_auth == 0"
                    @click="setLevel(house)"
                  >
                    设置等级</DropdownItem
                  >
                  <DropdownItem plain :round="false" size="big" @click="copy(house)"
                    >复制</DropdownItem
                  >
                  <DropdownItem
                    plain
                    :round="false"
                    v-if="
                      house.current_login_user &&
                      house.current_login_user.privilege &&
                      house.current_login_user.privilege.auth_is_top &&
                      !(house.unilateral_agent == 1 && house.unilateral_agent_auth == 0)
                    "
                    size="big"
                    @click="houseTop(house)"
                    >聚焦房源</DropdownItem
                  >
                  <DropdownItem plain :round="false" size="big" @click="tixing(house)"
                    >提醒</DropdownItem
                  >
                  <DropdownItem plain :round="false" size="big" @click="follow(house)"
                    >跟进</DropdownItem
                  >
                </view>
              </Dropdown>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Dropdown from './Dropdown'
import DropdownItem from './DropdownItem'
import myButton from './myButton'
export default {
  data () {
    return {
      levelArr: ['A', 'B', 'C'],
      levelIndex: 0,
    }
  },
  components: {
    Dropdown,
    DropdownItem,
    myButton,
  },
  props: {
    house: Object,
  },
  filters: {
    priceFilter (val) {
      if (!val) {
        return '面议'
      }
      if (isNaN(Number(val))) {
        return val + '万'
      }
      let price = val / 10000
      if (parseInt(price) == parseFloat(price)) {
        return parseInt(price) + '万'
      } else {
        return Number(price).toFixed(2) + '万'
      }
    },
    formatPrice (val, type) {
      console.log(val, type);
      let unit = "元"

      if (Number(val) >= 10000) {
        if (type == 1 || type == 3) {
          unit = "万元"
        } else {
          unit = "万元/月"
        }
        return Number(val) / 10000 + unit
      } else {
        if (type == 1 || type == 3) {
          unit = "元"
        } else {
          unit = "元/月"
        }
        return val + unit
      }
    },
    statusFilter (val) {
      if (val) {
        return val.substr(0, 2)
      }
      return val
    }
  },
  created () {
    if (this.house.level) {
      this.levelIndex = this.levelArr.findIndex((item) => this.house.level == item)
    }
  },
  methods: {
    toDetail (e) {
      this.$emit('toDetail', e)
    },
    makPhoneCall (house) {
      this.$emit('call', house)
      uni.makePhoneCall({
        phoneNumber: house,
      })
    },
    copy (house) {
      this.$emit('copy', house)
    },
    follow (house) {
      this.$emit('follow', house)
    },
    tixing (house) {
      this.$emit('tixing', house)
    },
    setLevel (house) {
      this.$emit('setLevel', house)
    },

    addLianmai (house) {
      this.$emit('addLianmai', house)
    },

    addShowing (house) {
      this.$emit('addShowing', house)
    },
    houseGroup (house) {
      this.$emit('houseGroup', house)
    },
    setCompanyHouse (house) {
      this.$emit('setCompanyHouse', house)
    },
    changeLevel (e, house) {
      console.log(e, house)
      this.$emit('setLevels', { level: this.levelArr[e.detail.value], house })
    },
    houseTop (house) {
      this.$emit('houseTop', house)
    },
    houseDel (house) {
      this.$emit('houseDel', house)
    },
  },
}
</script>

<style lang="scss" scoped>
.house_item {
  width: 100%;
  border-bottom: 4rpx solid #dfdfdf;
  padding: 0 48rpx 20rpx;
  .mr12 {
    margin-right: 12rpx;
  }
  .house_name {
    width: 100%;
  }
  .house_img {
    width: 190rpx;
    min-width: 190rpx;
    height: 146rpx;
    margin-right: 24rpx;
    border-radius: 10rpx;
    overflow: hidden;
    position: relative;
    .istop {
      position: absolute;
      top: 0;
      left: 0;
      // background: #1cd300;
      // background-image: linear-gradient(180deg, #f8a707, #f85d02 100%);
      // color: #fff;
      // background: #dbe8fa;
      // color: #2d84fb;
      background: #2d84fb;
      color: #fff;
      padding: 4rpx 10rpx;
      border-bottom-right-radius: 10rpx;
      font-size: 22rpx;
      &.wuxiao {
        color: #fff;
        background: #d1cbcb;
        // background-image: linear-gradient(180deg, #e4e7ed, #a2a3a6 100%);
      }
      &.zanhuan {
        color: #fff;
        background: #fda148;
        // background-image: linear-gradient(180deg, #e4e7ed, #a2a3a6 100%);
      }
      &.chengjiao {
        color: #fff;
        background: #2f84f7;
        // background-image: linear-gradient(180deg, #e4e7ed, #a2a3a6 100%);
      }
      &.tingzhi {
        color: #fff;
        background: #f74c4c;
        // background-image: linear-gradient(180deg, #e4e7ed, #a2a3a6 100%);
      }
    }
    .picnum {
      position: absolute;
      bottom: 0;
      right: 0;
      color: #fff;
      background: rgba($color: #000000, $alpha: 0.3);
      font-size: 22rpx;
      padding: 0 10rpx;
    }
    image {
      width: 100%;
      height: 100%;
    }
  }
  .house_info {
    // overflow: hidden;
    .house_title {
      font-size: 32rpx;
      color: #2e3c4e;
      font-weight: 600;
      line-height: 1.5;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      .du_img {
        width: 28rpx;
        height: 28rpx;
        vertical-align: middle;
        margin-right: 8rpx;
      }
      .ctime {
        font-size: 22rpx;
        font-weight: normal;
        color: #8a929f;
        background: #eff1f2;
        padding: 0 10rpx;
        border-radius: 4rpx;
        margin-left: 10rpx;
      }
    }
    .house_area {
      color: #8a929f;
      .zushou {
        background: #4198ff;
        color: #fff;
        font-size: 22rpx;
      }
      &.small {
        font-size: 22rpx;
      }
      &.mtb12 {
        margin: 12rpx 0;
      }
      &.mb12 {
        margin-bottom: 12rpx;
      }
      &.mt12 {
        margin-top: 12rpx;
      }
      &.mt24 {
        margin-top: 24rpx;
      }
      .du {
        width: 32rpx;
        height: 32rpx;
        overflow: hidden;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .level {
        // width: 32rpx;
        // height: 32rpx;
        color: #fff;
        padding: 4rpx 18rpx;
        height: 42rpx;
        white-space: nowrap;
        overflow: hidden;
        font-size: 22rpx;
        &.a {
          background-image: linear-gradient(180deg, #f9762e, #fc0606 100%);
        }
        &.b {
          background-image: linear-gradient(180deg, #f8a707, #f85d02 100%);
        }
        &.c {
          background-image: linear-gradient(180deg, #feda38, #fdb508 100%);
        }
      }
      .company {
        background: #dbe8fa;
        color: #2d84fb;
        white-space: nowrap;
        height: 42rpx;
        padding: 0 18rpx;
        height: 42rpx;
        font-size: 22rpx;
      }
      .key {
        font-size: 22rpx;
        padding: 4rpx 18rpx;
        background: #dbe8fa;
        white-space: nowrap;
        height: 42rpx;
        color: #2d84fb;
        &.vip {
          background: #3a3f53;
          color: #f3c840;
        }
        &.wuxiao {
          background: #f1f4fb;
          color: #dee1ea;
        }

        .img {
          width: 32rpx;
          height: 32rpx;
          overflow: hidden;

          image {
            width: 100%;
            height: 100%;
          }
        }
      }

      .line {
        padding: 0 12rpx;
      }

      .price_info {
        align-items: flex-end;
      }
      .price {
        font-size: 32rpx;
        color: #fe6c17;
        &.price_unit {
          font-size: 22rpx;
        }
      }
    }
  }
}
.footer_info {
  font-size: 22rpx;
  color: #8a929f;
  margin-top: 20rpx;
  // padding: 24rpx 0;
  .title_price {
    color: #fe6c17;
    .up {
      font-weight: 600;
    }
    .down {
      font-weight: 600;
      color: #00caa7;
    }
    .price_con {
      &.big {
        font-size: 28rpx;
      }
    }
  }
  .oper_btn {
    font-size: 28rpx;
    color: #2d84fb;
    font-weight: 400;
    margin-left: auto;
    ::v-deep .my-btn {
      border: 0;
      display: inline-block;
      padding-bottom: 20rpx;
      height: auto;
    }
    ~ .oper_btn {
      margin-left: 24rpx;
    }
    .oper_name {
      margin-left: 16rpx;
      &.more {
        display: inline-block;
        padding-bottom: 20rpx;
      }
    }
    .oper_img {
      width: 32rpx;
      height: 32rpx;
      overflow: hidden;
      image {
        width: 100%;
        height: 100%;
      }
    }
  }
  .tel {
    font-size: 22rpx;
    color: #2d84fb;
    font-weight: 500;
    margin-left: auto;
  }
  .tel_img {
    width: 32rpx;
    height: 32rpx;
    object-fit: cover;
  }
}
.footer_oper {
  padding-bottom: 48rpx;
  padding-top: 24rpx;
  justify-content: flex-end;
  .oper_btn {
    font-size: 28rpx;
    color: #2d84fb;
    font-weight: 400;
    ::v-deep .my-btn {
      border: 0;
      display: inline-block;
      padding-bottom: 20rpx;
      height: auto;
    }
    ~ .oper_btn {
      margin-left: 24rpx;
    }
    .oper_name {
      margin-left: 16rpx;
      &.more {
        display: inline-block;
        padding-bottom: 20rpx;
      }
    }
    .oper_img {
      width: 32rpx;
      height: 32rpx;
      overflow: hidden;
      image {
        width: 100%;
        height: 100%;
      }
    }
  }
}
.jiangjia {
  margin: 20rpx 0 0;
  .pr_top {
    .pr_label_item {
      .pr_label {
        // margin-bottom: 12rpx;
        background: #fff1f5;
        color: #fb656a;
        font-size: 22rpx;
        align-items: center;
        white-space: nowrap;
        padding: 4rpx 8rpx 4rpx 4rpx;
        // margin-top: 10rpx;
        // margin-right: 10rpx;
        border-radius: 2rpx;
        font-weight: 700;
        .img {
          width: 40rpx;
          height: 40rpx;
          margin-right: 5rpx;
          overflow: hidden;
          image {
            width: 100%;
            height: 100%;
          }
        }

        .pr_label_name {
          color: #fb656a;
          font-size: 22rpx;
        }
      }
    }
  }
}
</style>
