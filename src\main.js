import "@/page_outside/tools/pc.js";

import Vue from "vue";
import store from "./page_outside/store";
import App from "./App";
import Tplus from "@/common/tplus";
import {
  navigateTo,
  previewImage,
  getQueryString,
  groupBy,
  getTime,
  getBase64,
  setDictionary,
  showModal,
  IdentifyTools,
  matchBuildType,
  addTime,
  debounce,
  createVisitorsRecord,
  handlePhoneArrVerification,
  getUnreadMsg,
  isWxWork,
  copyText,
  bindingPublic,
  sortPro,
  pySegSort,
} from "./page_outside/tools/index";
import ajax from "./page_outside/tools/ajax";
import wx from "weixin-js-sdk"; // 引入微信jssdk
import { getDictionaryList } from "./page_outside/tools/dictionary";
import formatImg from "./page_outside/mixin/image_filter";
import VueJsonp from "vue-jsonp";
import uploadFile from "./page_outside/tools/upload";
import wxApi from "./page_outside/mixin/wx_api";
import qyWxApi from "./page_outside/mixin/qy_wx_api"; // 企业微信jssdk
import titleBar from "./components/titleBar"; // 顶部导航
import gmyFloatTouch from "./components/gmy-float-touch.vue"; //悬浮窗
import BottomBar from "./components/BottomBar.vue"; // 底部导航
import mySkeleton from "./components/my_skeleton/J-skeleton.vue"; // 骨架屏
import config from "@/page_outside/config/index.js";
// #ifdef H5
// import Vconsole from 'vconsole'
// #endif
Vue.component("BottomBar", BottomBar);
Vue.component("titleBar", titleBar);
Vue.component("gmyFloatTouch", gmyFloatTouch);
Vue.component("mySkeleton", mySkeleton);
// 定义全局过滤手机号 344

Vue.filter("formatPhone", (e) => {
  if (e) {
    return e.replace(/^(.{3})(.*)(.{4})$/, "$1 $2 $3");
  } else {
    return e;
  }
});
// 过滤显示佣金显示元/套
Vue.filter("formatBrokerageRule", (e) => {
  if (e.indexOf("元/套") > 0) {
    return e;
  } else if (e.indexOf("元") > 0) {
    return e + "/套";
  } else {
    return e + "元/套";
  }
});

// 银行卡分割
Vue.filter("formatBankCard", (e) => {
  if (e) {
    return e.replace(/[\s]/g, "").replace(/(\d{4})(?=\d)/g, "$1  ");
  }
});
// 过滤过万的佣金显示
Vue.filter("million", (value) => {
  let num;
  if (value > 9999) {
    //大于9999显示x.xx万
    num = Math.floor(value / 1000) / 10 + "万";
  } else if (value < 9999 && value > -9999) {
    num = value;
  } else if (value < -9999) {
    //小于-9999显示-x.xx万
    num = -(Math.floor(Math.abs(value) / 1000) / 10) + "万";
  }
  return num;
});
// 过滤性别
Vue.filter("formatSex", (e) => {
  return e === 1 ? "先生" : e === 0 ? "女士" : "暂未填写";
});

// 过滤时间时分秒
Vue.filter("formatTime", (e) => {
  return /\d{4}-\d{1,2}-\d{1,2}/g.exec(e)[0];
});
// 过滤掉标签
// 定义过滤器函数
Vue.filter('removeTags', function(value) {
  // 使用正则表达式替换标签
  return value.replace(/<\/?[^>]+(>|$)/g, "");
});

//oss图片路径
Vue.filter('imgDomain', function(src) {
  return config.imgDomain +src
});
let navigateBack = function (num = 1, backHome = false) {
  if (getCurrentPages().length > 1) {
    uni.navigateBack({
      delta: num,
    });
  }else if(backHome){
    uni.navigateBack({
      delta: 1,
    });
  }
};

let isVideoReg = function (url) {
  var video_format = [
    ".mp4",
    ".avi",
    ".wmv",
    ".mpg",
    ".mpeg",
    ".mov",
    ".rm",
    ".ram",
    ".swf",
    ".flv",
  ];
  var video_reg = new RegExp(`(${video_format.join("|")})`);
  var videos = video_reg.test(url);
  return videos;
};
import * as filters from './customer/components/messageList/formatTime' 
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key]) // 插入过滤器名和对应方法
})

Vue.use(VueJsonp);
Vue.mixin(formatImg);
Vue.mixin(wxApi);
Vue.mixin(qyWxApi);
Vue.config.productionTip = false;
// #ifdef H5
// let website_id = uni.getStorageSync("website_id")
// if (website_id == 176) {
//   const vConsole = new Vconsole();
//   console.log(vConsole,'4444');
//   Vue.use(vConsole)
// }

// #endif
App.mpType = "app";
Vue.prototype.$uploadFile = uploadFile;
Vue.prototype.$navigateTo = navigateTo;
Vue.prototype.$isVideoReg = isVideoReg;
Vue.prototype.$previewImage = previewImage;
Vue.prototype.$getQueryString = getQueryString;
Vue.prototype.$showModal = showModal;
Vue.prototype.$groupBy = groupBy;
Vue.prototype.$getTime = getTime;
Vue.prototype.$getBase64 = getBase64;
Vue.prototype.$addTime = addTime;
Vue.prototype.$debounce = debounce;
Vue.prototype.$createVisitorsRecord = createVisitorsRecord;
Vue.prototype.$getUnreadMsg = getUnreadMsg;
Vue.prototype.$handlePhoneArrVerification = handlePhoneArrVerification;
Vue.prototype.$navigateBack = navigateBack;
Vue.prototype.$isWxWork = isWxWork;
Vue.prototype.$copyText = copyText;
Vue.prototype.$sortPro = sortPro;
Vue.prototype.$bindingPublic = bindingPublic;
Vue.prototype.$wx = window.jWeixin;
Vue.prototype.$pySegSort = pySegSort;
Vue.prototype.$setDictionary = setDictionary;
// 测试识别工具
Vue.prototype.$IdentifyTools = IdentifyTools;
// 匹配楼盘属性
Vue.prototype.$matchBuildType = matchBuildType;
Vue.prototype.$getDictionaryList = getDictionaryList;
Vue.prototype.$store = store;
Vue.prototype.$ajax = ajax;
Vue.prototype.$getParent = function (component_name) {
  var _self = this;
  var getParent = function (parent) {
    if (
      parent.$options.name === component_name ||
      parent.$options._componentTag === component_name
    ) {
      return parent;
    } else if (parent.$parent) {
      return getParent(parent.$parent);
    } else {
      return null;
    }
  };
  return getParent(_self.$parent);
};

Vue.use(Tplus);
// Vue.config.ignoredElements = ["wx-open-launch-weapp"];
const app = new Vue({
  ...App,
});
app.$mount();
