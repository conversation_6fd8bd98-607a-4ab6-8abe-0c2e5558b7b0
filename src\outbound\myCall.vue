<template>
  <view class="page">
    <view class="whole">
<view class="whole_header">
  <view class="header">
        <view class="name_role">{{ name }}</view>
        <view class="name_user">{{ username }}</view>
      </view>
      <view class="whole_all">
        <view @click="is_tan = true">拨打电话</view>
      </view>
</view>
<view class="list" v-for="item in detail" :key="item.id">
  <view class="list_item">
    <view class="list_heater">
      <view class="list_left">
        <view>拨打时间</view>
        <view class="list_header_time">{{ item.created_at }}</view>
      </view>
     <view  class="list_right">
      <view >通话时长s</view>
      <view style="margin-left: 16rpx;">{{item.call_status}}s</view>
     </view>
  
    </view>
    <view class="list_whole">
      <view class="list_one" v-if="item.call_status == 0 && item.caller_duration == 0">主叫</view>
      <view class="list_ones" v-else>主叫</view>

      <view class="heng">
<view>
  <text style="
        color: #FF7D00;
        text-align: center;
        font-size: 22rpx;margin-bottom: 24rpx;" v-if="item.call_status == 0 && item.caller_duration == 0">未接通</text>
  <text style="
        color: #00D131;
        text-align: center;
        font-size: 22rpx;margin-bottom: 24rpx;" v-else>接通</text>
  <view >
          <svg xmlns="http://www.w3.org/2000/svg" width="77" height="12" viewBox="0 0 77 12" fill="none">
<path d="M77 6L67 0.226497V11.7735L77 6ZM0 7H68V5H0V7Z" fill="#292C39" fill-opacity="0.1" />
</svg>
        </view>
        <view class="weijie" v-if="item.call_status == 0 && item.caller_duration == 0">
          <image src="../static/icon/index/未接.png" style="width: 32rpx; height: 32rpx;margin-left: 8rpx; margin-top: 8rpx;"></image>
        </view>
        <view class="weijies" v-else>
          <image src="../static/icon/index/接通.png" style="width: 32rpx; height: 32rpx;margin-left: 8rpx; margin-top: 8rpx;"></image>
        </view>
</view>
      </view>
      <view class="list_one" v-if="item.call_status == 0 && item.caller_duration == 0">被叫</view>
      <view class="list_ones" v-else>被叫</view>
    </view>
    <view class="list_footer">
      <view>{{ item.caller }}</view>
      <view>{{ item.callee }}</view>
    </view>
  </view>
</view>
    </view>
    <loadMore :status="load_status" @reload="getInfo()" />
    <d-tan v-model="is_tan">
            <view style="width: 100%;background-color: #fff;border-radius: 30rpx 30rpx 0 0;display: flex;height: 1000rpx;
            flex-direction: column;overflow: hidden;">

                <view>
                    <view style="width: 100%;text-align: right;padding-right: 30rpx;height: 100rpx;background: #f1f4fa ;line-height: 100rpx;" @click="clone()">关闭</view>
                </view>
                <view class="p_con">
        <view class="title"> 拨打电话 </view>
        <view class="p_content">
          <view class="p_item">
            <view style="margin-bottom: 32rpx;outline:none" class="value">
              
            <uni-easyinput v-model="phone" defaultValue="请输入被叫号码"  placeholder="请输入被叫号码" ></uni-easyinput>
            </view>
            <view class="value">
              <!-- <selectDown valueName="phone" :multiple="false" v-model="show_id" :localdata="phoneList"
                @change="changeSelect" defaultValue="请选择外显号码" placeholder="请选择外显号码">
              </selectDown> -->
              <uni-data-select
              v-model="show_id" 
              :localdata="phoneList"
              @change="changeSelect"
              defaultValue="请选择外显号码" 
              placeholder="请选择外显号码"
    ></uni-data-select>
            </view>
          </view>
          <view class="btns flex-row items-center">
            <view class="btn flex-1" @click="conMakePhone"> 确认拨打 </view>
          </view>
        </view>
      </view>
                </view>
        </d-tan>
  </view>
</template>

<script>
import loadMore from '@/components/loadMore'
import selectDown from '@/outbound/components/uni-data-select'
export default {
  components: { loadMore,selectDown },
  data() {
    return {
      name:'',//名字首字母
      user:'',// 获取用户信息
      username:'',// 用户姓名
      detail:[],
      load_status: "loading",
      is_tan:false,
      show_id: "",
      phoneList: [],
      phone:''
    }
  },
  onLoad() {
this. getInforUser()
this.getInfo()
this.getSeatsPhone()
  },
  methods: {
    changeSelect() {

},
clone(){
this.is_tan=false
this.phone = ''
this.show_id = ''
},
getSeatsPhone(){
this.$ajax.get('/admin/call_clue/getSeatsPhone',{},res=>{
  console.log(res.data,"1234567890");
  this.phoneList = res.data
  for(let item of this.phoneList){
    console.log(item.phone);
    console.log(item.show_id);
    item.value = item.show_id
    item.text = item.phone
  //  this. phone = item.text
   this.show_id = item.value
  }
})
},
conMakePhone(item) {
      if (!this.show_id) {
        uni.showToast({
          title: "请选择外显号码",
          icon: "none"
        })
        return
      }
      let api = `/admin/call_clue/directCallPhone`
      let params = { show_id: this.show_id, phone: this. phone}
      this.$ajax.post(api, params, res => {
        if (res.statusCode == 200) {
          console.log(res.data.telX,"pppp");
          uni.makePhoneCall({ phoneNumber: res.data.telX }) // 传参带入号码即可
          uni.showToast({
            title: '正在拨打中 请稍后',
            icon: "none",
            duration: 2000
          })
          this.is_tan=false
this.phone = ''
this.show_id = ''
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
            duration: 2000
          })
          this.is_tan=false
this.phone = ''
this.show_id = ''
        }
      })
    },
        // 获取用户信息
        getInforUser() {
      this.user = JSON.parse(uni.getStorageSync("userInfo"));
    this.username = this.user.user_name
      let name = this.user.user_name.split("");
      console.log(name, "名字");
      this.name = name[0];
    },
    // 获取列表信息
    getInfo () {
      this.load_status = "loading"
      this.$ajax.get('/admin/call_clue/myDirectCallLog', { }, res => {
        console.log(res);
        if (res.statusCode == 200) {
          this.load_status = 'nomore'
          this.detail = res.data.data
          console.log(this.detail,'列表');
        }
      })
    },
  }
}
</script>

<style lang ="scss" scoped>
.p_con {
  padding: 40rpx;
  background: #fff;
  border-radius: 8rpx;
  position: relative;
  padding-top: 140rpx;
  // width: 80vw;

  /* height: 50vh; */
  .title {
    font-size: 40rpx;
    font-weight: 600;
    padding: 40rpx 0;
    text-align: center;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    text-align: center;
  }

  .p_content {
    .p_item {
      .label {
        margin-right: 5rpx;
      }
    }

    .btns {
      margin-top: 100rpx;

      .btn {
        background: #2d84fb;
        padding: 30rpx 0;
        color: #fff;
        border-radius: 10rpx;
        text-align: center;
      }
    }
  }
}

.option_btn.disabled {
  ::v-deep .my-btn.primary {
    background: rgba(45, 132, 251, 0.3);
    color: rgba(45, 132, 251, 0.8);
  }
}
.heng{
  position: relative;
  width: 25%;
}

.weijie{
  position: absolute;
  left: 50rpx;
  top: 36rpx;
  width: 48rpx;
height: 48rpx;
border-radius: 50%;
background: #FF7D00;
}
.weijies{
  position: absolute;
  left: 50rpx;
  top: 36rpx;
  width: 48rpx;
height: 48rpx;
border-radius: 50%;
background: #00D131;
}
.list_one{
  padding: 8rpx 32rpx;
  border-radius: 32rpx;
  background: rgba(255, 125, 0, 0.20);
  color: #FF7D00;
text-align: center;
font-size: 24rpx;
line-height: 32rpx;
margin-top: 40rpx;
}
.list_ones{
  padding: 8rpx 32rpx;
  border-radius: 32rpx;
  background: rgba(0, 209, 49, 0.20);
  color:#00D131;
text-align: center;
font-size: 24rpx;
line-height: 32rpx;
margin-top: 40rpx;
}
.list_footer{
  display: flex;
flex-direction: row;
flex-wrap: nowrap;
justify-content: space-between;
color: #292C39;
font-size: 36rpx;

}
.list_whole{
  display: flex;
flex-direction: row;
flex-wrap: nowrap;
align-items: center;
margin: 32rpx 0;
justify-content: space-around
}
.list_right{
  display: flex;
flex-direction: row;
flex-wrap: nowrap;
align-items: center;
}
.list_left{
  display: flex;
flex-direction: row;
flex-wrap: nowrap;
align-items: center;
}
.list_header_time{
  color: rgba(41, 44, 57, 0.70);
  margin-left: 16rpx;
}
.list{
  padding: 16rpx 32rpx; 
}
.list_item{
  width: 100%;
  background: #ffff;
  border-radius: 16rpx;
  padding: 24rpx;
}
.list_heater{
  display: flex;
flex-direction: row;
flex-wrap: nowrap;
align-items: center;
justify-content: space-between;
color: rgba(41, 44, 57, 0.40);
text-align: center;
font-size: 28rpx;
}
.whole_all{
  display: flex;
flex-direction: row;
flex-wrap: nowrap;
align-items: center;
color: #fff;
border-radius: 8rpx;
  padding: 12rpx 24rpx;
  background: #12D367;
}
.whole_header{
  display: flex;
flex-direction: row;
flex-wrap: nowrap;
align-items: center;
justify-content: space-between;
padding: 32rpx;
}
.name_user{
  color:  #292C39;
font-size: 36rpx;
margin-left: 16rpx;
}
.page {
  min-height: 100vh;
  background: #f6f6f6;
}
.header{
display: flex;
flex-direction: row;
flex-wrap: nowrap;
align-items: center;

}
.name_role{
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: #3172f6;
  text-align: center;
  color: #f6f6f6;
  line-height: 56rpx;
  font-weight: 500;
  font-size: 26rpx;
}
</style>