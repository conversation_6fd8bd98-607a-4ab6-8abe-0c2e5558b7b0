<template>
  <view class="card">
    <view class="card_title">客户轨迹</view>
    <time-line :lineData="lineData"></time-line>
    <loadMore :status="load_status"></loadMore>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import timeLine from "@/components/timeLine";
import loadMore from "../components/loadMore";
export default {
  components: { timeLine, loadMore },
  data() {
    return {
      lineData: [],
      params: {
        page: 1,
      },
      load_status: "",
    };
  },
  onLoad(options) {
    if (options) {
      this.visi_id = options.id;
      this.getDataList();
    }
  },
  methods: {
    getDataList() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.lineData = [];
      }
      this.$ajax.get(
        `/client/user/visitor/search/track?visitor_user_id=${this.visi_id}`,
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.lineData = this.lineData.concat(res.data.data);
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
            }
          } else {
            uni.showToast({
              title: res.data.message || "获取数据失败",
              icon: "none",
            });
          }
        }
      );
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.getDataList();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>

<style scoped lang="scss">
.card {
  padding: 20upx 24upx;
  .card_title {
    padding: 30upx 20upx;
    margin-bottom: 20upx;
    font-size: 32upx;
    border-bottom: 1upx solid #f1f1f1;
  }
}
</style>
