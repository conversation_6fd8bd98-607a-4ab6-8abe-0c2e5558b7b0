<template>
  <view class="list">
    <view class="title-top row">
      <text class="left">{{ customer_item.customer_name }}</text>
      <text class="right">{{ customer_item.customer_phone }}</text>
      <text v-if="is_case === 1 && customer_item.is_dispatch === 1" class="yi"
        >已分配-{{ customer_item.dispatch_user }}</text
      >
      <text v-if="is_case === 1 && customer_item.is_dispatch === 0" class="wei"
        >未分配</text
      >
      <text
        v-if="customer_item.project_user_id === 0 && is_case !== 1"
        class="wei"
        @click="getCustomer(customer_item.id)"
        >未领取 <text v-if="isGet">（点击领取）</text></text
      >
      <text v-if="customer_item.project_user_id > 0 && is_case !== 1" class="yi"
        >已领取-{{ customer_item.pu_name }}</text
      >
      <view
        class="is-dispatch"
        @click="
          $navigateTo(
            `/project_broker/set_customer_dispatch?customer_id=${customer_item.id}`
          )
        "
        v-if="
          is_case === 1 &&
            customer_item.is_dispatch === 0 &&
            customer_item.status === 2
        "
        >分配客户</view
      >
    </view>
    <view>
      <view class="ctn-box row">
        <view class="label">报备楼盘</view>
        <view class="content">{{ customer_item.build_name }}</view>
      </view>
      <view class="ctn-box row">
        <view class="label">客户性别</view>
        <view class="content">{{
          customer_item.customer_sex | formatSex
        }}</view>
      </view>
      <view class="ctn-box row" style="align-items: center;">
        <view class="label">报备会员</view>
        <view class="cus_content row">
          <text style="">{{ customer_item.u_name }}</text>
          <!-- <text
            @click="callPhone"
            style="background: #33be85;color:#fff;padding:0 10rpx;font-size:24rpx;border-radius:10rpx;
      text-align: center;"
            >拨打电话</text
          > -->
          <view class="row">
            <image
              @click="callPhone"
              class="image"
              src="../static/dianhua.png"
            ></image>
            <image
              @click="$emit('sendMsg', customer_item)"
              class="image"
              src="../static/xiaoxi.png"
            ></image>
          </view>
        </view>
      </view>
      <view class="ctn-box row" v-if="customer_item.u_company_name">
        <view class="label">公司名称</view>
        <view class="content">{{ customer_item.u_company_name }}</view>
      </view>
      <view class="ctn-box row" v-if="customer_item.u_company_name">
        <view class="label">公司区域</view>
        <view
          class="content"
          v-if="
            customer_item.com_region_0_name && customer_item.com_region_1_name
          "
          >{{
            customer_item.com_region_0_name +
              "-" +
              customer_item.com_region_1_name
          }}</view
        >
        <view class="content" v-else>{{
          customer_item.com_region_0_name || "--"
        }}</view>
      </view>
      <view class="ctn-box row">
        <view class="label">报备时间</view>
        <view class="content">{{ customer_item.created_at }}</view>
      </view>
      <view class="ctn-box row">
        <view class="label">最后更新</view>
        <view class="content">{{ customer_item.updated_at }}</view>
      </view>
      <view class="ctn-box row" v-if="customer_item.customer_id_no">
        <view class="label">身份证号</view>
        <view class="content">{{ customer_item.customer_id_no }}</view>
      </view>
      <view class="ctn-box row" v-if="customer_item.intention_build_category">
        <view class="label">
          客户意向
        </view>
        <view class="content">
          {{ changeBuildType(customer_item.intention_build_category) }}</view
        >
      </view>
      <view class="ctn-box row">
        <view class="label">报备状态</view>
        <view
          class="content txt"
          :class="{
            report: customer_item.status == 0,
            visite: customer_item.status == 1,
            subscribe: customer_item.status == 2,
            isBuy: customer_item.status == 3,
            isbuy4: customer_item.status == 4,
            isDeal: customer_item.status == 5,
            failure: customer_item.status == 10,
          }"
          >{{ changeStr(txt) }}</view
        >
      </view>
      <view class="ctn-box row" v-if="customer_item.cancel_reason">
        <view class="label">无效原因</view>
        <view :class="cancelShow ? 'content_show' : 'content'">{{
          customer_item.cancel_reason
        }}</view>
        <myIcon
          @click="showAllCancel"
          style="margin-top:14rpx"
          :type="cancelShow ? 'xiala' : 'shangla'"
          size="20rpx"
          color="#bbb"
        ></myIcon>
      </view>
      <view class="ctn-box row" v-if="customer_item.remark">
        <view class="label">备注内容</view>
        <view :class="showContent ? 'content_show' : 'content'">{{
          customer_item.remark
        }}</view>
        <myIcon
          @click="showAll"
          style="margin-top:14rpx"
          :type="showContent ? 'xiala' : 'shangla'"
          size="20rpx"
          color="#bbb"
        ></myIcon>
      </view>
      <!-- <view
        class="ctn-box row"
        v-if="
          customer_item.customer_attached_phone !== '[]' &&
            customer_item.customer_attached_phone !== null
        "
      >
        <view class="label">其他客户</view>
        <view
          class="content"
          style="color:#00ad65"
          @click="clickMoreCustomer(customer_item.customer_attached_phone)"
          >点击查看</view
        >
      </view> -->
    </view>
    <view class="btn-box row" v-if="btn">
      <view
        v-if="customer_item.status !== 10 && customer_item.status !== 5"
        class="btn"
        @click="setInvalid(customer_item.status, customer_item.id)"
        >设为无效</view
      >
      <view
        class="btn valid"
        v-if="customer_item.status === 0"
        @click="setEffective(customer_item.status, customer_item.id)"
        >设为有效</view
      >
      <view
        class="btn fill"
        v-if="
          !/^[1][3,4,5,7,8,9][0-9]{9}$/.test(customer_item.customer_phone) &&
            (customer_item.p_fill_customer_phone_category === 1 ||
              customer_item.p_fill_customer_phone_category === 4)
        "
        @click.stop="fillPhone"
      >
        补全号码
      </view>
      <view class="btn copy" @click="copyCustomer">复制客户</view>
      <view
        v-if="customer_item.status < 2"
        class="btn visit"
        @click="$emit('isVisit', customer_item)"
      >
        设为到访
      </view>
      <view
        class="btn follow"
        @click="
          $navigateTo(
            `/report/report_detail?id=${customer_item.id}&data_type=2`
          )
        "
      >
        跟进
      </view>
      <view
        class="btn status"
        @click="followStatu(customer_item.status, customer_item.id)"
        >状态</view
      >
    </view>

    <Mpop
      ref="pop"
      direction="center"
      :is_close="true"
      :is_mask="true"
      :width="90"
    >
      <view
        @touchmove.stop.prevent="doNothing"
        class="copy-list"
        v-for="(item, index) in copy_template_list"
        :key="item.id"
      >
        <view class="copy-item row" @click="copyItemCTn(item.content)">
          <view class="name">{{ item.name }}</view>
          <myIcon
            @click.native.stop="showCopyCtn(index)"
            :type="item.showContent ? 'xiala' : 'shangla'"
            size="30rpx"
            color="#fff"
          ></myIcon>
        </view>
        <text class="copy-contet" v-if="item.showContent">{{
          item.content
        }}</text>
      </view>
      <view class="default-ctn" @click="defaultCtn">默认模板</view>
    </Mpop>
    <Mpop
      ref="pop2"
      direction="center"
      :is_close="true"
      :is_mask="true"
      :width="90"
    >
      <view class="input-box row" @touchmove.stop.prevent="doNothing">
        <input
          type="number"
          class="m-input"
          placeholder="请输入隐号内容"
          v-model="form_fill_phone.part_number"
          maxlength="4"
        />
        <view class="form-btn" @click="createFillPhone(customer_item.id)">
          确认
        </view>
      </view></Mpop
    >
    <Mpop
      ref="pop3"
      direction="center"
      :is_close="true"
      :is_mask="true"
      :width="90"
    >
      <view class="other-box-list" @touchmove.stop.prevent="doNothing">
        <view
          class="other-box"
          v-for="(item, index) in other_customer"
          :key="index"
        >
          <view class="content row">
            <view class="left">客户：</view>
            <view class="right row"
              >{{ item.name }} <view class="right">{{ item.phone }}</view></view
            >
          </view>
        </view>
      </view>
    </Mpop>
  </view>
</template>

<script>
import Mpop from "./ming-pop";
import myIcon from "./my-icon";
export default {
  components: { myIcon, Mpop },
  props: {
    customer_item: [Object],
    load_text: {
      type: Object,
      default: () => {
        return {
          0: "待审核",
          1: "报备有效",
          2: "已到访",
          3: "已认筹",
          4: "已认购",
          5: "已成交",
          10: "已无效",
        };
      },
    },
    btn: {
      type: Boolean,
      default: true,
    },
    isGet: {
      type: Boolean,
      default: true,
    },
    is_case: [Number, Boolean, String],
    build_type_list: [Array],
  },
  data() {
    return {
      txt: "",
      copy_value: "",
      showContent: false,
      copy_template_list: [],
      isShowCopy: false,
      copy_value_end: "",
      form_fill_phone: {
        id: "",
        part_number: "",
      },
      cancelShow: false,
      other_customer: [],
      copy_is_show: false,
    };
  },
  methods: {
    changeStr() {
      if (this.customer_item.status) {
        return this.load_text[this.customer_item.status];
      } else if (this.customer_item.status == 0) {
        return this.load_text[this.customer_item.status];
      }
    },
    // 设置为无效
    setInvalid(status, id) {
      this.$emit("setInvalid", status, id);
    },
    // 设置有效
    setEffective(status, id) {
      this.$emit("setEffective", status, id);
    },
    followStatu(status, id) {
      this.$emit("status", status, id);
    },
    showCopyCtn(index) {
      this.copy_template_list[index].showContent = !this.copy_template_list[
        index
      ].showContent;
    },
    callPhone() {
      if (this.customer_item.u_phone) {
        uni.makePhoneCall({
          phoneNumber: this.customer_item.u_phone,
          success: () => {
            console.log("拨打经纪人电话");
          }, //仅为示例
        });
      }
    },
    showAll() {
      this.showContent = !this.showContent;
    },
    // 点击复制弹出
    copyCustomer() {
      uni.showLoading({
        title: "加载中",
      });
      this.$ajax.get(
        "/client/customer/reported/copy_template/search?enable=1",
        {},
        (res) => {
          if (res.statusCode === 200) {
            for (var i = 0; i < res.data.data.length; i++) {
              res.data.data[i].showContent = false;
            }
            this.copy_template_list = res.data.data;
            this.$refs.pop.show();
            uni.hideLoading();
          } else {
            uni.showToast({
              title: res.data.message || "获取失败",
              type: "none",
            });
          }
        }
      );
    },
    // 默认复制内容
    defaultCtn() {
      var sex = this.customer_item.customer_sex == 1 ? "先生" : "女士";
      this.copy_value = `【楼盘名称】${this.customer_item.build_name}\n【客户姓名】${this.customer_item.customer_name}\n【客户性别】${sex}\n【客户电话】${this.customer_item.customer_phone}\n【添加时间】${this.customer_item.created_at}\n【报备经纪人】${this.customer_item.u_name}\n【经纪人公司】${this.customer_item.u_company_name}\n【联系方式】${this.customer_item.u_phone}`;
      this.$copyText(this.copy_value, () => {
        uni.showToast({
          title: "复制成功",
          icon: "none",
        });
      });
    },
    doNothing: function() {
      // 阻止弹窗后屏幕滑动事件
    },
    // 复制模板内容
    copyItemCTn(e) {
      let {
        customer_name,
        customer_phone,
        build_name,
        customer_sex,
        created_at,
        u_name,
        u_company_name,
        u_phone,
        visit_time,
        remark,
        visit_people,
        visit_category,
        copy_time,
        visit_time_str_1,
        go_with,
        customer_id_no,
        intention_build_category,
      } = this.customer_item;
      if (customer_sex === 1) {
        customer_sex = "先生";
      } else if (customer_sex === 0) {
        customer_sex = "女士";
      }
      if (visit_category === 1) {
        visit_category = "自访";
      } else if (visit_category === 2) {
        visit_category = "带访";
      } else {
        visit_category = "未选择带访方式";
      }
      if (go_with == 1) {
        go_with = "是";
      } else if (go_with == 0) {
        go_with = "否";
      }
      if (intention_build_category) {
        intention_build_category = this.changeBuildType(
          intention_build_category
        );
      }
      var date = new Date();
      let now_time = date.toTimeString().substr(0, 5);
      copy_time = now_time;
      this.copy_value_end = eval("`" + e + "`");
      this.$copyText(this.copy_value_end, () => {
        uni.showToast({
          title: "复制成功",
          icon: "none",
        });
        this.$refs.pop.close();
      });
    },
    fillPhone() {
      this.$refs.pop2.show();
    },
    createFillPhone(id) {
      this.form_fill_phone.id = id;
      this.$emit("createFillPhone", this.form_fill_phone);
    },
    showAllCancel() {
      this.cancelShow = !this.cancelShow;
    },
    // 点击领取客户
    getCustomer(id) {
      this.$emit("getCustomer", id);
    },
    // 点击查看更多客户
    clickMoreCustomer(customer) {
      this.other_customer = JSON.parse(customer);
      this.$refs.pop3.show();
    },
    changeBuildType(str) {
      //匹配数组中字符串对应数组
      let obj = {};
      for (let i in this.build_type_list) {
        obj[this.build_type_list[i].value] = this.build_type_list[i];
      }
      let strArr = str.split(",");
      let newArr = [];
      for (let item of strArr) {
        if (obj[item]) {
          newArr.push(obj[item]);
        }
      }
      var newarr1 = newArr.map((item) => {
        return item.description + "";
      });
      return newarr1 + "";
    },
  },
};
</script>

<style scoped lang="scss">
.list {
  margin: 30rpx;
  padding: 48rpx 24rpx 30rpx;
  background: #fff;
  // width: auto;
  .title-top {
    margin-top: 20rpx;
    font-size: 32rpx;
    margin-bottom: 30rpx;
    align-items: center;
    position: relative;
    .is-dispatch {
      position: absolute;
      right: 0;
      color: #0077ff;
      border: 1px solid #0077ff;
      border-radius: 8rpx;
      padding: 10rpx;
      font-size: 22rpx;
    }
    .left {
      margin-right: 10rpx;
    }
    .wei {
      margin-left: 20rpx;
      color: #ff0000;
    }
    .yi {
      margin-left: 20rpx;
      color: #00ad65;
    }
  }
  .ctn-box {
    font-size: 28rpx;
    line-height: 52rpx;
    color: #999;
    .label {
      margin-right: 30rpx;
    }
    .cus_content {
      align-items: center;
      justify-content: space-between;
    }
    .content {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .content_show {
      flex: 1;
    }
    .txt {
      &.report {
        color: #ff0000;
      }
      &.visite {
        color: #638ff9;
      }
      &.subscribe {
        color: #39becd;
      }
      &.isBuy {
        color: #ff8062;
      }
      &.isBuy4 {
        color: #ffa53a;
      }
      &.isDeal {
        color: #33be85;
      }
      &.failure {
        color: #808080;
      }
    }
  }
  .btn-box {
    font-size: 28rpx;
    justify-content: flex-start;
    margin-top: 30rpx;
    flex-wrap: wrap;
    .btn {
      margin-right: 14rpx;
      margin-top: 20rpx;
      background: #859bb2;
      border-radius: 30rpx;
      color: #fff;
      align-items: center;
      padding: 10rpx 20rpx;
      &.valid {
        background: #00ad65;
      }
      &.copy {
        background: #0077ff;
      }
      &.fill {
        background: #fa6e71;
      }
      &.follow {
        background: #2fbef7;
      }
      &.status {
        background: #f25273;
      }
      &.visit {
        background: #62d4ba;
      }
    }
  }
  .image {
    width: 60rpx;
    height: 60rpx;
    margin-left: 30rpx;
  }
  .copy-list {
    margin-top: 20rpx;
    font-size: 28rpx;
    .copy-item {
      background: #fff;
      align-items: center;
      justify-content: space-between;
      padding: 10rpx;
      border-radius: 10rpx;
      background: rgba(17, 67, 233, 0.651);
    }
    padding: 10rpx 0;
    .name {
      color: #fff;
    }
    .copy-contet {
      font-size: 28rpx;
      line-height: 40rpx;
      padding: 10rpx;
      box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.2);
    }
  }
  .default-ctn {
    width: 150rpx;
    background: #0174ff;
    padding: 10rpx;
    align-items: center;
    color: #fff;
    border-radius: 16rpx;
    margin-top: 20rpx;
  }
}

.input-box {
  margin-top: 40rpx;
  align-items: center;
  font-size: 28rpx;
  .m-input {
    width: 80%;
    flex-direction: row;
    align-items: center;
    padding-left: 16rpx;
    height: 32px;
    border-radius: 4px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    background-color: #eee;
    border: 1rpx solid #f3f3f3;
  }
  .form-btn {
    background: #638ff9;
    color: #fff;
    width: 20%;
    height: 32px;
    line-height: 32px;
    align-items: center;
    border-radius: 4px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
.other-box-list {
  margin-top: 30rpx;
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.06);
  padding: 24rpx;
  .other-box {
    .content {
      margin-bottom: 20rpx;
      color: #999;
      .right {
        margin-left: 20rpx;
      }
    }
  }
}
</style>
