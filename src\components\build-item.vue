<template>
  <view
    class="list row"
    hover-class="item-hover"
    :hover-start-time="60"
    :hover-stay-time="120"
    @click="onClick()"
    v-if="item.project_display === 1"
  >
    <slot name="check"></slot>
    <view class="left">
      <!-- https://dss3.bdstatic.com/70cFv8Sh_Q1YnxGkpoWK1HF6hhy/it/u=2108439922,486236279&fm=26&gp=0.jpg -->
      <image
        mode="aspectFill"
        :src="
          item.build_image
            ? item.build_image
            : 'https://img.tfcs.cn/static/img/que.jpg' | imageFilter('w_220')
        "
      ></image>
      <!-- <myIcon class="icon" type="ic_vr" color="#fff"></myIcon> -->
      <!-- <view
        class="right-mark"
        :class="[type === 0 ? 'right-mark' : 'right-mark-l']"
        >{{ build_type }}</view
      > -->
      <view class="right-mark" v-if="item.label">
        {{ item.label || "" }}
      </view>
    </view>
    <view class="right">
      <view class="title-top row">
        <view class="title">{{ item.build_name }}</view>
        <view
          v-if="item.build_status"
          class="title-right"
          :class="item.build_status === 1 ? 'title-right' : 'title-right-l'"
          >{{ item.build_status }}</view
        >
      </view>
      <view class="area-box"
        >{{ item.b_region_0_name || "" }} {{ item.b_region_1_name || "" }}</view
      >
      <view class="build-type row">
        <text v-for="(type, index) in item.build_category_name" :key="index">{{
          type
        }}</text>
      </view>
      <view class="price-box row">
        <view class="price-left row">
          <text class="price">{{ item.build_avg_price || "" }}</text>
          <text class="unit">元/㎡</text>
        </view>
        <slot name="yong" />
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from "./my-icon";
export default {
  components: {
    myIcon,
  },
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  onLoad() {},
  computed: {},
  methods: {
    onClick() {
      this.$emit("click");
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  margin-bottom: 48rpx;
  .left {
    width: 204rpx;
    height: 172rpx;
    position: relative;
    image {
      width: 100%;
      height: 100%;
    }
    .right-mark {
      align-items: center;
      font-size: 20rpx;
      color: #ffffff;
      line-height: 32rpx;
      width: 58rpx;
      height: 32rpx;
      right: 0;
      top: 0;
      position: absolute;
      background-image: linear-gradient(132deg, #f7918f 0%, #fb656a 100%);
      // background-image: linear-gradient(135deg, #69d4bb 0%, #00caa7 100%);
      border-radius: 0 0 0 16rpx;
    }
    .right-mark-l {
      background-image: linear-gradient(132deg, #f7918f 0%, #fb656a 100%);
    }
    .icon {
      position: absolute;
      bottom: 16rpx;
      left: 16rpx;
      width: 40rpx;
      height: 40rpx;
      border: 1rpx solid #fff;
      border-radius: 50rpx;
    }
  }
  .right {
    width: 484rpx;
    margin-left: 16rpx;
    justify-content: space-between;
    .title-top {
      justify-content: space-between;
      .title {
        font-size: 32rpx;
        color: #1e1f20;
        font-weight: bold;
      }
      .title-right {
        font-size: 22rpx;
        align-items: center;
        line-height: 32rpx;
        color: #fff;
        padding: 0 10rpx;
        // width: 62rpx;
        height: 32rpx;
        background-image: linear-gradient(180deg, #69d4bb 0%, #00caa7 100%);
      }
      .title-right-l {
        background-image: linear-gradient(180deg, #f7918f 0%, #fb656a 100%);
      }
    }
    .area-box {
      font-size: 22rpx;
      color: #999999;
    }
    .build-type {
      font-size: 22rpx;
      flex-wrap: wrap;
      color: #999;
      text {
        background: #f2f2f2;
        margin-right: 20rpx;
        margin-top: 10rpx;
      }
    }
    .price-box {
      justify-content: space-between;
      .price-left {
        align-items: flex-end;
        .price {
          font-size: 48rpx;
          color: #fb656a;
        }
        .unit {
          font-size: 28rpx;
          color: #333333;
          margin-left: 10rpx;
        }
      }
      .price-right {
        align-items: center;
        .yong {
          line-height: 40rpx;
          text-align: center;
          width: 40rpx;
          height: 40rpx;
          color: #fff;
          background: #fec923;
          border-radius: 4px;
        }
        .jiage {
          font-size: 24rpx;
          color: #333;
        }
      }
    }
  }
}
</style>
