<template>
    <view class="picker-input-box">
        <view class="picker-input-wrapper" @click="openPicker">
            <slot></slot>
       </view>
 
            <myPopup ref="pickerSelector" :show="show" @close="show = false">
                <view class="picker-selector">
                    <view class="picker-selector-header">
                        <view class="picker-selector-action cancle" @click.stop="cancle">取消</view>
                        <view class="picker-selector-title">{{placeholder}}</view>
                        <view class="picker-selector-action confirm" @click.stop="confirm">确定</view>
                    </view>
                    <picker-view :value="currentIndexs" @change="onSelectChange">
                        <picker-view-column v-for="(range, index) in ranges" :key="index">
                            <view class="picker-selector-column-item" v-for="(item, key) in range" :key="key">
                                {{item.label}}
                            </view>
                        </picker-view-column>
                    </picker-view>
                </view>
            </myPopup> 

    </view>
</template>
<script>
import icons from '@/components/my-icon';
import myPopup from '@/components/myPopup';
export default {
    name: 'tPicker',
    props: {
        value: { type: String, default: ''},
        valueFormat: { type: String, default: 'YYYY-MM-DD HH:ii:ss'},
        start: { type: String, default: ''},
        end: { type: String, default: ''},
        placeholder: {type: String, default: '请选择'},
    },
    components: {
        icons, myPopup
    },
    model: {
        prop: 'value',
        event: 'change',
    },
    data(){
        return {
            show: false,
            currentValues: [],
            currentIndexs: [],
        }
    },
    computed: {
        mins(){
            let start = this.start ? new Date(this.start).getTime() : 0;
            !start && (start = (new Date().getFullYear() - 10)+'-01-01');
            return this.getDateValues(start);
        },
        maxs(){
            let end = this.end ? new Date(this.end).getTime() : 0;
            !end && (end = (this.mins[0] + 20)+'-12-31 23:59:59');
            return this.getDateValues(end);
        },
        formatSupport(){
            let format =  this.valueFormat.trim() || false;
            return [
                !format || format.includes('YYYY'),
                !format || format.includes('M'),
                !format || format.includes('D'),
                !format || format.includes('H'),
                !format || format.includes('i'),
                !format || format.includes('s')
            ]
        },
        minValue(){
            return this.parseDate(this.mins[0]+'-'+this.mins[1]+'-'+this.mins[2]+' '+this.mins[3]+':'+this.mins[4]+':'+this.mins[5]).getTime()
        },
        maxValue(){
            return this.parseDate(this.maxs[0]+'-'+this.maxs[1]+'-'+this.maxs[2]+' '+this.maxs[3]+':'+this.maxs[4]+':'+this.maxs[5]).getTime()
        },
        years(){
            let list = [],
                min = this.mins[0],
                max = this.maxs[0];
            for(;min <= max; min++){
                list.push(min);
            }
            return list.map(e => ({value: e, text: e, label: e+'年', type: 'year'}));
        },
        months(){
            return this.getRangeCloumn(1, 1, 12).map(e => ({value: e, text: e>9?e:'0'+e, label: (e>9?e:'0'+e)+'月', type: 'month'}));
        },
        days(){
            return this.getRangeCloumn(2, 1, this.getMonthDays(this.currentValues[0], this.currentValues[1]))
                        .map(e => ({value: e, text: e>9?e:'0'+e, label: (e>9?e:'0'+e)+'日', type: 'day'}));
        },
        hours(){
            return this.getRangeCloumn(3, 0, 23).map(e => ({value: e, text: e>9?e:'0'+e, label: (e>9?e:'0'+e)+'时', type: 'hours'}));
        },
        minutes(){
            return this.getRangeCloumn(4, 0, 59).map(e => ({value: e, text: e>9?e:'0'+e, label: (e>9?e:'0'+e)+'分', type: 'minute'}));
        },
        seconds(){
            return this.getRangeCloumn(5, 0, 59).map(e => ({value: e, text: e>9?e:'0'+e, label: (e>9?e:'0'+e)+'秒', type: 'second'}));
        },
        ranges(){
            let list = [];
            this.formatSupport[0] && list.push(this.years);
            this.formatSupport[1] && list.push(this.months);
            this.formatSupport[2] && list.push(this.days);
            this.formatSupport[3] && list.push(this.hours);
            this.formatSupport[4] && list.push(this.minutes);
            this.formatSupport[5] && list.push(this.seconds);
            return list;
        },
        safeValue() {
            return this.value || ''; // 如果 value 为 null 或 undefined，则返回空字符串
        }
    },
    methods: {
        
        getDateValues(time){
            let d;
            if (typeof time === 'string') {
                d = this.parseDate(time);
            } else {
                d = new Date(time);
            }
            if (!d || isNaN(d.getTime())) {
                d = new Date();
            }
            return [d.getFullYear(), d.getMonth() + 1, d.getDate(), d.getHours(), d.getMinutes(), d.getSeconds()];
        },

        parseDate(dateString) {
            const formats = [
                'YYYY/MM/DD HH:mm:ss',
                'YYYY/MM/DD',
                'YYYY-MM-DD HH:mm:ss',
                'YYYY-MM-DD',
                'YYYY-MM-DDTHH:mm:ss',
                'YYYY-MM-DDTHH:mm:ss+HH:mm'
            ];
            
            for (let format of formats) {
                const d = this.parseDateWithFormat(dateString, format);
                if (d && !isNaN(d.getTime())) {
                return d;
                }
            }
            
            return new Date();
        },

        parseDateWithFormat(dateString, format) {
            const parts = dateString.match(/(\d+)/g);
            const formatParts = format.match(/([YMDHms]+)/g);
            const date = new Date(0);

            if (!parts || parts.length !== formatParts.length) {
                return null;
            }

            for (let i = 0; i < formatParts.length; i++) {
                const value = parseInt(parts[i], 10);
                switch (formatParts[i]) {
                case 'YYYY':
                    date.setFullYear(value);
                    break;
                case 'MM':
                    date.setMonth(value - 1);
                    break;
                case 'DD':
                    date.setDate(value);
                    break;
                case 'HH':
                    date.setHours(value);
                    break;
                case 'mm':
                    date.setMinutes(value);
                    break;
                case 'ss':
                    date.setSeconds(value);
                    break;
                }
            }
            return date;
        },



        getMonthDays(year,month){
            var arr = [31, null, 31, 30,31, 30, 31, 31,30, 31, 30, 31];
            return arr[month-1] || (year % 400 === 0 || year % 100 !== 0 && year % 4 === 0 ? 29 : 28);
        },
        getRangeCloumn(index, defaultMin, defaultMax){
            let list = [],
                indexs = this.currentIndexs,
                indexsNoEmpty = indexs.length != 0,
                values = JSON.stringify(this.currentValues.slice(0, index).map((val, i) => {
                    if(indexsNoEmpty && this.formatSupport[i]){
                        let cur = indexs[i], last = 0;
                        switch(i){
                            case 0:
                                last = this.years.length - 1;
                                return this.years[cur > last ? last : cur].value;
                            case 1:
                                last = this.months.length - 1;
                                return this.months[cur > last ? last : cur].value;
                            case 2:
                                last = this.days.length - 1;
                                return this.days[cur > last ? last : cur].value;
                            case 3:
                                last = this.hours.length - 1;
                                return this.hours[cur > last ? last : cur].value;
                            case 4:
                                last = this.minutes.length - 1;
                                return this.minutes[cur > last ? last : cur].value;
                        }
                    }
                    return val;
                })),
                min = values == JSON.stringify(this.mins.slice(0, index)) ? this.mins[index] : defaultMin,
                max = values == JSON.stringify(this.maxs.slice(0, index)) ? this.maxs[index] : defaultMax;

            for(;min <= max; min++){
                list.push(min);
            }
            return list;
        },
        setCurrentValues(){
            let d = new Date(this.safeValue),
            time = d.getTime();
            if(!time || isNaN(time)){
                d = new Date();
                time = d.getTime();
            }

            if(time < this.minValue){
                this.currentValues =  [...this.mins];
            }else if(time > this.maxValue){
                this.currentValues =  [...this.maxs];
            }else{
                this.currentValues = [d.getFullYear(), d.getMonth()+1, d.getDate(), d.getHours(), d.getMinutes(), d.getSeconds()]
            }
        },
        setCurrentIndexs(){
            let i = 0, j = 0, indexs = [];
            for(const bool of this.formatSupport){
                let value = this.currentValues[j++];
                if(bool){
                    let range = this.ranges[i++], last = range.length - 1;
                    if(value > range[last].value){
                        indexs.push(last)
                    }else if(value < range[0].value){
                        indexs.push(0)
                    }else{
                        indexs.push(range.findIndex(e => e.value == value))
                    }
                }
            }
            this.currentIndexs = indexs;
        },
        cancle(){
            this.show = false
        },
        confirm(){
            let format = this.valueFormat.trim() || 'YYYY-MM-DD HH:ii:ss';
            let [year,month,day,hours,minutes,seconds] = this.getCurrentValues();
            format = format.replace('YYYY', year);
            format = format.replace('MM', month > 9 ? month : '0'+month);
            format = format.replace('M', month);
            format = format.replace('DD', day > 9 ? day : '0'+day);
            format = format.replace('D', day);
            format = format.replace('HH', hours > 9 ? hours : '0'+hours);
            format = format.replace('H', hours);
            format = format.replace('ii', minutes > 9 ? minutes : '0'+minutes);
            format = format.replace('i', minutes);
            format = format.replace('ss', seconds > 9 ? seconds : '0'+seconds);
            format = format.replace('s', seconds);
            this.$emit('input', format);
            this.$emit('change', format);
            this.show = false
        },
        openPicker(){
            this.setCurrentValues()
            this.setCurrentIndexs();
            this.show = true
        },
        getCurrentValues(){
            let i = 0;
            return this.currentValues.map((val, index) => {
                if(this.formatSupport[index]){
                    let range = this.ranges[i],
                        last = range.length - 1,
                        cur = this.currentIndexs[i++];
                    return range[cur > last ? last : cur].value;
                }else{
                    return val;
                }
            })
        },
        onSelectChange(e){
            this.currentIndexs = e.detail.value;
            this.currentValues = this.getCurrentValues();
        }
    }
}
</script>
<style lang="scss" scoped>
.picker-input-box{
    flex:1;
    flex-direction: column;
    justify-content: center;
}
.picker-input-wrapper{
    display: block;
}
.picker-selector{
    background-color: #fff;
    line-height: 1;
    
    .picker-selector-header{
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 46px;
        :after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            height: 1px;
            clear: both;
            border-bottom: 1px solid #e5e5e5;
            color: #e5e5e5;
            -webkit-transform-origin: 0 100%;
            transform-origin: 0 100%;
            -webkit-transform: scaleY(.5);
            transform: scaleY(.5);
        }
        .picker-selector-title{
            flex: 1;
            color: #999;
            text-align: center;
            display: inline-block;
            max-width: 50%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .picker-selector-action{
            padding: 0 14px;
            font-size: 17px;
            &.cancle{
                color: #888;
            }
            &.confirm{
                color: #007aff;
            }
        }
        
    }
}
::v-deep uni-picker-view, picker-view{
    height: 36vh;
    .picker-selector-column-item{
        display: flex;
        justify-content: center;
        overflow: hidden;
        white-space: nowrap;
        text-align: center;
    }
}
</style>