<template>
  
        <view class="bottom_bars flex-row ">
        <view class="bottom_bars_item flex-1"  :class ="{ active :currentIndex ==1}" @click ='$navigateTo("/douyin/index")'>
          <view class="bottom_bars_item_top">
            <image mode='widthFix' :src='`/yidongduan/douyin/tabbar/kanban${currentIndex ==1?"_active":""}.png` | imageFilter("w_80")'></image> 
          </view>
          <view class="bottom_bars_item_bottom">
            数据看板
          </view>
        </view>
        <view class="bottom_bars_item flex-1" :class ="{ active :currentIndex ==2}"  @click ='$navigateTo("/douyin/teamList")'>
          <view class="bottom_bars_item_top">
            <image mode='widthFix' :src='`/yidongduan/douyin/tabbar/team${currentIndex ==2?"_active":""}.png` | imageFilter("w_80")'></image> 
          </view>
          <view class="bottom_bars_item_bottom">
            团队成员
          </view>
        </view>
        <view class="bottom_bars_item flex-1" :class ="{ active :currentIndex ==3}"  @click ='$navigateTo("/douyin/my")'>
          <view class="bottom_bars_item_top">
            <image mode='widthFix' :src='`/yidongduan/douyin/tabbar/my${currentIndex ==3?"_active":""}.png` | imageFilter("w_80")'></image> 
          </view>
          <view class="bottom_bars_item_bottom">
            我的
          </view>
        </view>
      </view>
</template>

<script>
export default {
  props:{
    currentIndex:{
      type:[Number,String],
      default:1
    }
  }
}
</script>

<style lang="scss" scoped>
    .bottom_bars {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    
    .bottom_bars_item {
      padding: 24rpx;
      display: flex;
      justify-content:flex-start;
      align-items: center;
      flex-direction: column;
      &.active{
        .bottom_bars_item_bottom{
          color: #2D84FB;
        }
      }
      .bottom_bars_item_top {
        width: 48rpx;
        height: 48rpx;
        margin-bottom: 8rpx;
        image {
          width: 100%;
        }
      }
      .bottom_bars_item_bottom {
        color: #999;
        font-size: 20rpx;
      }
    }

  }
</style>