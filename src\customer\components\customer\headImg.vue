<template>
    <view class="avatar" :class="[size, {female:sex == 2, word: useWord}]">
        <template v-if="useWord">{{cnameWord}}</template>
        <template v-else>
            <image :src="headImgUrl" class="avatar-img"/>
            <image :src="levelIcon | imgDomain" class="level-img" v-if="levelIcon"/>
        </template>
    </view>
</template>

<script>
import config from "@/page_outside/config/index.js";
export default {
    props: {
        size: { type: String, value: 'small'},
        customer: { type: Object, default: () => ({}) },
        useWord: { type: Boolean, default: false },
    },
    data() {
        return {
            
        };
    },
    computed: {
        cnameWord(){
            return this.customer.cname[0] || '';
        },
        sex(){
            return this.customer.sex || 1;
        },
        headImgUrl(){
            return this.customer.dy_avatar || config.imgDomain+ (this.sex == 2 ? '/static/admin/customer/nv2.png' : '/static/admin/customer/nan2.png');
        },
        levelIcon(){
            switch(this.customer?.level?.title){
                case 'A':
                    return '/yidongduan/customer/level/level_a.png';
                case 'B':
                    return '/yidongduan/customer/level/level_b.png';
                case 'C':
                    return '/yidongduan/customer/level/level_c.png';       
                case 'D':
                    return '/yidongduan/customer/level/level_d.png';
                default:
                    return '';
            }
        }
    },
};
</script>

<style scoped lang="scss"> 
.avatar{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background-color: #ddd;
    &.word{
        font-size: 36rpx;
        color: #fff;
        background-color: #3172f6;
        &.female{
            background-color: #ff82be;
        }
    }
    &.large{
        width: 128rpx;
        height: 128rpx;
    }
    .avatar-img{
        width: 100%;
        height: 100%;
        border-radius: 50%;
    }
    .level-img{
        position: absolute;
        z-index: 1;
        width: 35%;
        height: 35%;
        right: 0;
        bottom: 0;
    }
}

</style>