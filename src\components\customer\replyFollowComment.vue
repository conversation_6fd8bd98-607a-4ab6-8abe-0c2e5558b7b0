<template>
    <myPopup :show="show"  @close="show = false">
        <view class="container">
            <view class="header">
                <view class="action cancle" @click.stop="cancle">取消</view>
                <view class="title">批注回复</view>
                <view class="action confirm" @click.stop="confirm">确认</view>
            </view>
            <view class="body" v-if="show">
                <textarea v-model="content" placeholder="请输入批注回复"  maxlength="-1" auto-focus adjust-position :show-confirm-bar="false"
                confirm-type="确认" @confirm="confirm" class="content-textarea"/>
            </view>
        </view>
    </myPopup>
</template>
<script>
import myPopup from '@/components/myPopup';
export default {
    props: {
        visible: { type: Boolean, default: false },
        followId: { type: [String, Number], default: '' },
    },
    components: {
        myPopup
    },
    data(){
        return {
            show: false,
            submiting: false,
            content: ''
        }
    },
    watch: {
        visible(val){
            this.show = val;
        },
        show(val){
            val != this.visible && this.$emit('update:visible', val);
            if(val === false){
                this.content = '';
            }
        }
    },
    created(){
        const userInfo = uni.getStorageSync("userInfo") ? JSON.parse(uni.getStorageSync("userInfo")) : {};
        this.selfUid = userInfo.id || 0;
    },
    methods: {
        cancle(){
            this.show = false;
        },
        async confirm(){
            if(!this.content){
                uni.showToast({
                    title: '请输入批注回复',
                    icon: 'none',
                });
                return;
            }

            this.submiting = true;
            try{
                const data = await this.submit(this.followId, this.content)
                uni.showToast({
                    title: data && data.msg ? data.msg : '批注成功',
                    icon: 'none',
                });
                this.show = false;
                this.$emit('success');
            }catch(e){}
            this.submiting = false;
        },
        submit(parentid, content){
            return new Promise((resolve, reject) => {
                this.$ajax.post('/admin/crm/client_follow/reply', {parentid, content}, res => {
                    if (res.statusCode == 200) {
                        resolve(res.data);
                    }else{
                        uni.showToast({
                            title: res?.data?.message || '批注失败',
                            icon: 'none'
                        });
                    }
                    reject();
                }, er => {
                    reject();
                })
            })
        }
    }
}


</script>
<style lang="scss" scoped>
.container{
    background: #fff;
    line-height: 1;
    
    .header{
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 46px;
        :after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            height: 1px;
            clear: both;
            border-bottom: 1px solid #e5e5e5;
            color: #e5e5e5;
            -webkit-transform-origin: 0 100%;
            transform-origin: 0 100%;
            -webkit-transform: scaleY(.5);
            transform: scaleY(.5);
        }
        .title{
            flex: 1;
            color: #999;
            text-align: center;
            display: inline-block;
            max-width: 50%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .action{
            padding: 0 14px;
            font-size: 17px;
            &.cancle{
                color: #888;
            }
            &.confirm{
                color: #007aff;
            }
        }
    }
}

.body{
    background: #F8F8F8;
    padding: 12px 12px 0px 12px;
}
.content-textarea{
    min-height: 400rpx;
    max-height: 30vh;
}
</style>