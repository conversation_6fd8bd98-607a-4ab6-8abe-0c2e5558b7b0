<template>
  <view class="list">
    <view class="card" v-for="item in bank_list" :key="item.id">
      <view class="top row">
        <view class="left-bank-name row">
          <view class="bank-name">{{ item.bank_name }}</view>
          <!-- <view class="bank-type">储蓄卡</view> -->
        </view>
        <view class="release" @click="deleteBank(item.id)">解除绑定</view>
      </view>
      <view class="bottom">{{ item.card_no | formatBankCard }}</view>
    </view>
    <!-- 添加新卡 -->
    <view class="new-card">
      <view class="center " @click="addNewCard">
        <text class="icon-baobei icon-baobei-tianjia_b"></text>
        <text class="add">添加银行卡</text>
      </view>
    </view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
export default {
  data() {
    return {
      bank_list: [],
    };
  },
  onLoad() {
    this.getBank();
  },
  methods: {
    addNewCard() {
      this.$navigateTo("/commission/add_bank_card");
    },
    getBank() {
      this.$ajax.get("/client/user/bank_card/all", {}, (res) => {
        if (res.statusCode === 200) {
          this.bank_list = res.data;
        } else {
          uni.showToast({
            title: res.data.message || "获取失败",
            icon: "none",
          });
        }
      });
    },
    deleteBank(id) {
      this.$ajax.get(`/client/user/bank_card/delete/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "解除绑定成功",
          });
          this.getBank();
        } else {
          uni.showToast({
            title: res.data.message || "解除绑定失败",
            icon: "none",
          });
        }
      });
    },
  },
  onPullDownRefresh() {
    this.getBank();
    uni.stopPullDownRefresh();
  },
};
</script>

<style scoped lang="scss">
.icon-baobei-tianjia_b {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22icon-baobei-tianjia_b%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M512%20512m-512%200a512%20512%200%201%200%201024%200%20512%20512%200%201%200-1024%200Z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M544%20320v160h160v64h-160v160h-64v-160h-160v-64h160v-160h64z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
  height: 64rpx;
  width: 64rpx;
}
.list {
  padding: 24rpx 48rpx;
  color: #fff;
  .card {
    justify-content: space-between;
    margin-top: 40rpx;
    padding: 24rpx;
    height: 184rpx;
    width: 100%;
    background: #0174ff;
    box-shadow: 0 0 8px 0 rgba(1, 116, 255, 0.4);
    border-radius: 4px;
    .top {
      justify-content: space-between;
      align-items: flex-end;
      .left-bank-name {
        align-items: flex-end;
        font-size: 40rpx;
        .bank-type {
          font-size: 24rpx;
          margin-left: 16rpx;
        }
      }
      .release {
        font-size: 24rpx;
      }
    }
    .bottom {
      font-size: 40rpx;
    }
  }
  .new-card {
    margin-top: 24rpx;
    height: 182rpx;
    background: #ffffff;
    border: 1px solid #d8d8d8;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    position: relative;
    .center {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      align-items: center;
      .add {
        font-size: 24rpx;
        margin-top: 12rpx;
        color: #999999;
      }
    }
  }
}
</style>
