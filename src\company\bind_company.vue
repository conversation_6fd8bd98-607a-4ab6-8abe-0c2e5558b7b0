<template>
  <view class="list">
    <view class="title">请输入门店码或者门店名称</view>
    <view class="title-tip">咨询公司绑定店长或员工获取门店码/门店名称</view>
    <input
      v-model="company_code"
      placeholder="请输入门店码/门店名称"
      type="text"
    />
    <view class="row btn-box">
      <view class="btn" @click="onSubmit">门店码绑定</view>
      <view class="btn" @click="onSubmitName">门店名称绑定</view>
    </view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
export default {
  data() {
    return {
      company_code: "",
    };
  },
  onLoad(options) {
    if (options.company_store_code) {
      this.company_code = options.company_store_code;
    }
  },
  methods: {
    onSubmit() {
      this.$ajax.get(
        `/client/my/bind/company/${this.company_code}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            uni.showToast({
              title: "绑定成功",
            });
          } else {
            uni.showToast({
              title: res.data.message || "绑定失败",
              icon: "none",
            });
          }
        }
      );
    },
    onSubmitName() {
      this.$ajax.post(
        "/client/my/bind/company/name",
        { name: this.company_code },
        (res) => {
          if (res.statusCode === 200) {
            uni.showToast({
              title: "绑定成功",
            });
          } else {
            uni.showToast({
              title: res.data.message || "绑定失败",
              icon: "none",
            });
          }
        }
      );
    },
  },
};
</script>

<style scoped lang="scss">
.list {
  padding: 24rpx 48rpx;
  .title {
    font-size: 32rpx;
    margin: 0 auto;
  }
  .title-tip {
    margin: 24rpx auto 48rpx;
    color: #999;
  }
  input {
    width: 100%;
    border-bottom: 1rpx solid #eee;
    font-size: 28rpx;
    height: 80rpx;
  }
  .btn-box {
    justify-content: flex-end;
  }
  .btn {
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 10rpx;
    padding: 0 20rpx;
    margin-left: 10rpx;
    margin-top: 50rpx;
    align-items: center;
    background: #5877f4;
    color: #fff;
  }
}
</style>
