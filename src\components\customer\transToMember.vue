<template>
    <tMemberPicker v-model="admin_id" 
        :visible.sync="show" :submiting="submiting" :filterId="selfUid"
        @confirm="handleConfirm">
    </tMemberPicker>
</template>
<script>
import tMemberPicker from '@/components/tplus/tMemberPicker';
export default {
    props: {
        visible: { type: Boolean, default: false },
        customerId: { type: [String, Number], default: '' },
        current: { type: String, default: 'my' },
    },
    components: {
        tMemberPicker
    },
    data(){
        return {
            show: false,
            submiting: false,
            selfUid: 0,
            admin_id: ''
        }
    },
    watch: {
        visible(val){
            this.show = val;
        },
        show(val){
            val != this.visible && this.$emit('update:visible', val)
            if(val === false){
                this.admin_id = '';
            }
        }
    },
    created(){
        const userInfo = uni.getStorageSync("userInfo") ? JSON.parse(uni.getStorageSync("userInfo")) : {};
        this.selfUid = userInfo.id || 0;
    },
    methods: {
        async handleConfirm(item){
            console.log(item?.label?.[0],'---------选项的内容----------------');
            const option_label = item?.label?.[0] || '';
            if (process.env.NODE_ENV === 'development') {
                this.show = false;
                uni.showToast({
                    title: '开发模式下模拟转交给同事成功',
                    icon: 'none',
                });
                this.$emit('success',{'name':option_label});
                return;
            }
            if(this.admin_id.length == 0){
                uni.showToast({
                    title: '请选择要转交到的同事',
                    icon: 'none',
                });
                return;
            }

            this.submiting = true;
            try{
                const data = await this.postTransToMember(this.customerId, this.admin_id)
                this.show = false;
                uni.showToast({
                    title: data && data.msg ? data.msg : '转交到同事成功',
                    icon: 'none',
                });
                this.$emit('success',{'name':option_label});
            }catch(e){}
            this.submiting = false;
        },
        postTransToMember(id, be_transfer_id){
            let url = this.current == 'trans' ? '/admin/private_client/transfer' : '/admin/crm/client/transfer';
            let params = this.current == 'trans' ? {ids: String(id), user_id: be_transfer_id} : {ids: String(id), be_transfer_id};
            return new Promise((resolve, reject) => {
                this.$ajax.post(url, params, res => {
                    if (res.statusCode == 200) {
                        resolve(res.data);
                    }else{
                        uni.showToast({
                            title: res?.data?.message || '转交到同事失败',
                            icon: 'none'
                        });
                    }
                    reject();
                }, er => {
                    reject();
                })
            })
        }
    }
}


</script>
<style lang="scss" scoped>
</style>