<template>
  <view class="detail">
    <view class="banner-box">
      <swiper
        @change="currentChange"
        class="swiper"
        autoplay
        :current="current"
      >
        <swiper-item
          v-for="(item, index) in imgs"
          :key="index"
          @click="previewImgs(imgs, index, item)"
        >
          <image
            mode="aspectFill"
            :src="item.img | imageFilter('w_6401')"
          ></image>
        </swiper-item>
      </swiper>
      <text class="indicato" v-if="imgs && house_imgs.length > 0">
        {{ current + 1 }}/{{ imgs.length }}</text
      >
      <view class="nav-house row"
        ><view
          v-for="(item, index) in navs_house"
          :key="index"
          class="nav-item-house"
          :class="{ active: item.type === data_type_house }"
          @click="onClickNavHouse(index, item.type)"
          >{{ item.name }}</view
        >
      </view>
    </view>
    <view class="new-detail">
      <view class="top-box">
        <view class="title-top _margin ">
          <view class="title row"
            ><text>{{ build.build_name }}</text>
            <view v-if="follow">
              <view
                v-if="isFollow === 0"
                class="shoucang row"
                @click="handleFollow"
              >
                <myIcon type="tianjia_w" color="#0174ff"></myIcon>
                <text>收藏</text></view
              >
              <view v-else class="shoucang row" @click="handleCancleFollow">
                <text>取消收藏</text></view
              >
            </view>
          </view>
          <view class="type-item row item">
            <text class="type-item-label" v-if="build.label">{{
              build.label
            }}</text>
            <text v-for="(item, index) in build_category_Label" :key="index">{{
              item
            }}</text>
          </view>
          <view class="price-desc bottom-line row">
            <view class="left-p " v-if="build.build_avg_price"
              >{{ build.build_avg_price }} 元/㎡起</view
            >
            <view class="left-p" v-else>--</view>
            <view
              class="daohang"
              @click="
                onOpenAddress({
                  latitude: build.build_location_lat,
                  longitude: build.build_location_long,
                })
              "
            >
              <image
                src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/daohang.png"
              ></image>
            </view>
          </view>
          <view class="build-detail-list row">
            <view
              class="build-detail-item"
              v-for="item in new_build_type"
              :key="item.id"
              @click="onClickMenu(item)"
            >
              <image :src="item.img | imageFilter('w_150')"></image>
              <text class="name">{{ item.name || "--" }}</text>
            </view>
          </view>
          <view class="build-item  item">
            <view class="row">
              <view class="left">价格说明</view>：
              <view class="label-ctn" style="color:#3172F6">{{
                build.b_price_description || "--"
              }}</view>
            </view>
            <view class="row">
              <view class="left">开盘时间</view>：
              <view class="label-ctn">{{
                build.newest_opening_time || "--"
              }}</view>
            </view>
            <view class="row "
              ><view class="left">开发</view>：
              <view class="label-ctn"
                >{{ build.developers_company_name || "--" }}
              </view>
            </view>
          </view>
        </view>
      </view>
      <view v-if="project_user" class="top-box">
        <view class="_list">
          <!-- 导航 -->
          <view class="nav row" ref="nav">
            <view
              v-for="(item, index) in navs"
              :key="index"
              class="nav-item"
              :class="{ active: item.id === data_type }"
              @click="onClickNav(index)"
            >
              <text style="z-index:1">
                {{ item.name }}
              </text>
              <image
                v-if="item.id === data_type"
                src="../static/new_detail/<EMAIL>"
                class="is-active"
              ></image>
            </view>
          </view>
        </view>
        <view class="content-list last-list" v-show="data_type === 1">
          <view
            class="_line row"
            v-if="recommend_rule.reported || recommend_rule.visit"
          >
            <view class="label">规则内容</view>：
            <text :class="cancelShow ? 'content_show' : 'content'"
              >{{ recommend_rule.reported }}{{ recommend_rule.visit }}</text
            >
            <myIcon
              style="margin-top:4rpx"
              @click="showAllCancel"
              :type="cancelShow ? 'xiala' : 'shangla'"
              size="30rpx"
              color="#b0b0b0"
            ></myIcon>
          </view>
          <view class="_line row">
            <view class="label"> 人脸识别 </view>：
            <text class="content _ctn">{{
              build.face_recognition === 1 ? "是" : "否"
            }}</text>

            <view class="label"> 回访客户 </view>：
            <text class="content _ctn">{{
              build.return_visit_phone === 1 ? "是" : "否"
            }}</text>
          </view>
          <view class="_line row" v-if="report_protection_time">
            <view class="label">报备保护</view>：
            <text class="content _ctn">{{ report_protection_time }}</text>
          </view>
          <view class="_line row" v-if="visit_protection_time">
            <view class="label">带看保护</view>：
            <text class="content _ctn">{{ visit_protection_time }}</text>
          </view>
        </view>
        <view class="content-list last-list" v-show="data_type === 2">
          <view class="_line row">
            <view class="label">佣金</view>：
            <view class="content _ctn">{{
              company_rules.brokerage_rule ||
                is_login_store.store_brokerage_rule ||
                build.brokerage_rule
            }}</view>
          </view>
          <view class="_line row">
            <view class="label">奖励说明</view>：
            <text class="content _ctn _ctn_">{{
              company_rules.brokerage_description ||
                is_login_store.store_brokerage_description ||
                build.brokerage_description
            }}</text>
          </view>
          <view
            class="_line row"
            v-if="
              company_rules.shopping_guide_reward ||
                is_login_store.store_shopping_guide_reward ||
                build.shopping_guide_reward
            "
          >
            <view class="label">带看奖励</view>：
            <text class="content _ctn">{{
              company_rules.shopping_guide_reward ||
                is_login_store.store_shopping_guide_reward ||
                build.shopping_guide_reward
            }}</text>
          </view>
          <view class="_line row">
            <view class="label">结佣公司</view>：
            <text class="content _ctn">{{
              company_rules.company_name || build.p_company_name
            }}</text>
          </view>
          <!-- <view class="look_more">查看详情</view> -->
        </view>
        <view class="bacfff" v-show="data_type === 3">
          <no-data v-if="nodiscount" tip="暂无优惠"></no-data>
          <view v-else class="activity-box row">
            <view class="ctn ">
              <!-- <view class="activity-txt ">
                现场优惠
              </view> -->
              <view class="row activity-txt  title" v-if="build.all_price">
                <view class="label row"
                  >均价：<text style="font-size:36rpx">{{
                    build.all_price
                  }}</text></view
                >
                <view class="label row"
                  >特价房源：<text style="font-size:36rpx">{{
                    build.discount_price
                  }}</text></view
                >
              </view>
              <view class="row activity-content title" v-else>
                <view class="label row"
                  ><text style="font-size:28rpx">{{
                    build.buy_coupon
                  }}</text></view
                >
              </view>
            </view>
            <view class="detail-btn " @click="CallBuildTel">详情</view>
          </view>
        </view>
        <view class="bacfff" v-show="data_type === 4">
          <no-data v-if="noshare" tip="暂无分享内容"></no-data>
          <shareContent
            v-else
            @click="copyShare(share_list)"
            :user_img="build.build_img"
            :user_name="build.build_name"
            :content="share_list.content"
            :share_list="share_list"
            :content_imgs="img_list"
          ></shareContent>
          <view
            class="tianjia"
            @click="
              $navigateTo(
                `/project_broker/get_customer?id=${build.project_id}&isPro=${user_info.category}`
              )
            "
          >
            <view class="huokebox row">
              <image
                class="tianjia-end"
                src="../static/new_detail/<EMAIL>"
              ></image>
              <view class="huoke">{{
                user_info.category === 3 ? "发布分享" : "查看更多"
              }}</view>
            </view>
          </view>
        </view>
      </view>
      <view
        class="top-box"
        v-if="user_info.category === 3 || user_info.company_id"
      >
        <view class="fujian ">
          <view class="title-top row"
            ><view class="title">附件资料</view
            ><view
              class="more"
              @click="
                $navigateTo(`/build/files_list?build_id=${params.buildID}`)
              "
              >更多</view
            ></view
          >
          <text class="files_text"
            >点击更多查看项目营销方案，推广等，为经纪人线上营销推广获客提供便利</text
          >
        </view>
      </view>
      <view
        class="top-box"
        v-if="
          build.project_id !== null &&
            build.reported_rule_category === 2 &&
            project_user
        "
      >
        <view class="content-list-dt">
          <view class="title-top row">
            <view class="title">推荐规则</view>
          </view>
          <view class="recommend-rule ">
            <view class="content-box " v-if="recommend_rule.deal">
              <view class="time row"
                ><view class="yuan"></view><text class="attr">成交</text></view
              >
              <view class="sub_title left-line">
                {{ recommend_rule.deal }}
              </view>
            </view>
            <view class="content-box " v-if="recommend_rule.commission">
              <view class="time row"
                ><view class="yuan"></view><text class="attr">结佣</text></view
              >
              <view class="sub_title left-line">
                {{ recommend_rule.commission }}
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="top-box menu2" v-if="build_dynamic_list.length > 0">
        <view class="content-list-dt">
          <view class="row title-top">
            <view class="title">楼盘动态</view>
            <!-- <view class="more">更多</view> -->
          </view>
          <view
            class="build-dynamic"
            v-for="item in build_dynamic_list"
            :key="item.id"
          >
            <view class="time row">
              <text class="attr">动态</text>
              <text>{{ item.created_at | formatTime }}</text>
            </view>
            <view class="title">{{ item.title }}</view>
            <view class="sub_title">{{ item.content }}</view>
          </view>
        </view>
      </view>

      <view class="top-box menu1">
        <view class="content-list-dt">
          <view class="row title-top">
            <view class="title">户型介绍</view>
            <view
              class="more"
              @click="
                $navigateTo(`/build/build_house?build_id=${build.build_id}`)
              "
              >更多</view
            >
          </view>
          <no-data v-if="nodata" tip="暂无户型图"></no-data>
          <house-type
            v-else
            :arr="house_type"
            @onClickNav="onClickNavType"
          ></house-type>
        </view>
      </view>

      <view class="top-box" v-if="build.build_selling_points">
        <view class="content-list-dt">
          <view class="title" style="">项目亮点</view>
          <view class="article-content" style="padding:24rpx 0">
            <view v-html="build.build_selling_points"></view>
            <!-- <u-parse
          :content="build.feature"
          @linkpress="navigate"
          :tag-style="tagStyle"
        ></u-parse> -->
          </view>
        </view>
      </view>
      <view class="top-box menu4">
        <view class="content-list-dt">
          <view class="title-top row">
            <view class="title">周边配套</view>
            <view class="more" @click="getNavigation">
              更多
            </view>
          </view>
          <view class="project-address row">
            <view class="left">项目地址：</view>
            <view class="right">{{ build.build_address }}</view>
          </view>
          <view class="mapbox">
            <view @click="goAroundMap(build.build_id)">
              <map
                :markers="markers"
                :circles="circles"
                :latitude="lat"
                :longitude="long"
              ></map>
            </view>
            <!-- @click="$navigateTo(`/build/around_map?id=${build.id}`)" -->
          </view>
          <view class="map-nav row">
            <view
              class="navs-box row"
              v-for="(item, index) in map_navs"
              :key="item.id"
              :class="{ active: item.id === data_type_map }"
              @click="surrounding(item.name, item.bgColor, item.id)"
            >
              <text>{{ item.name }}</text>
            </view>
          </view>
          <view class="address-list">
            <view
              class="address-item row"
              v-for="item in markers.slice(0, 3)"
              :key="item.id"
              @click="onOpenAddress(item)"
            >
              <view class="left">{{ item.callout.content }}</view>
              <view class="right">{{ item.distance }}</view>
            </view>
          </view>
        </view>
      </view>
      <view
        v-if="project_user && consultant_list.length > 0"
        class="top-box"
        style="margin-bottom:200rpx"
      >
        <view class="content-list-dt">
          <view class="row title-top">
            <view class="title">项目助理</view>
            <!-- <view class="more">更多</view> -->
          </view>
          <view style="margin-top:44rpx">
            <counselor-list
              v-for="item in consultant_list"
              :key="item.id"
              :headImg="item.avatar"
              :phone="item.phone"
              :name="item.name || item.user_name || item.nickname"
              :grade="item.grade"
              :number="item.number_people"
              @clickMsg="clickMsg(item)"
              @clickTel="clickTel(item.phone)"
            ></counselor-list>
          </view>
          <view class="personal-ctn row">
            <view
              class="ctn-box row"
              v-for="item in personal_desc"
              :key="item.value"
            >
              <image
                src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/xmzl.png"
              ></image>
              <text>{{ item.name }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- <view class="bacfff">
      <view class="title-top _margin ">
        <view class="build-item row item">
          <view class="row">
            <view class="label">开盘</view>
            <view class="label-ctn">{{ build.newest_opening_time }}</view>
          </view>
          <view class="row">
            <view class="label">建面</view>
            <view class="label-ctn">{{ build.total_build_area }}㎡</view>
          </view>
          <view
            class="row"
            @click="
              $navigateTo(`/build/build_description?build_id=${build.build_id}`)
            "
          >
            <text class="label" style="margin-right:0">详情</text>
            <myIcon type="you" size="28rpx"></myIcon>
          </view>
        </view>
        <view class="company-item row item"
          ><view class="label">开发</view>
          <view class="price row">{{ build.developers_company_name }} </view>
        </view>
      </view>
    </view> -->

    <view v-if="build.project_id == null" class="bacfff">
      <view
        class="bottom-box row"
        :style="{ position: !$store.state.in_mobile ? 'relative' : 'fixed' }"
      >
        <view class="left row" style="width:200rpx">
          <view class="icon-box" @click="shareDetail">
            <image
              src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/fx.png"
            ></image>
            <!-- <myIcon type="ic_fenxiang" size="40rpx"></myIcon> -->
            <text>分享</text>
          </view>
        </view>
        <view class="right row project_id">
          <view class="btn" @click="CallBuildTel">电话咨询</view>
          <view class="btn" @click="touch_hide = true">预约看房</view>
        </view>
      </view>
    </view>
    <view v-else-if="project_user" class="bacfff">
      <view
        class="bottom-box row"
        :style="{ position: !$store.state.in_mobile ? 'relative' : 'fixed' }"
      >
        <view class="left row">
          <view
            class="icon-box"
            @click="
              $navigateTo(
                `/report/report_list?id=${build.project_id}&full_num_reported=${build.full_num_reported}`
              )
            "
          >
            <image
              src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/kh.png"
            ></image>
            <text>客户</text>
          </view>
          <view class="icon-box" @click="shareDetail">
            <image
              src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/fx.png"
            ></image>
            <text>分享</text>
          </view>
        </view>
        <view class="right row">
          <view class="btn" @click="CallBuildTel">电话咨询</view>
          <view
            class="btn"
            @click="
              $navigateTo(
                `/report/report_client?id=${build.build_id}&full_num_reported=${build.full_num_reported}`
              )
            "
            >报备客户</view
          >
        </view>
      </view>
    </view>
    <view v-else style="margin-top:100rpx">
      <view class="share-btn row">
        <view class="btn" @click="forwordShare">转发分享</view>
        <view class="btn" @click="dialTel">电话咨询</view>
      </view>
    </view>
    <myLoading
      ref="loading"
      :custom="false"
      :shadeClick="true"
      :type="1"
    ></myLoading>
    <myPopup :show="isShare" @hide="isShare = false">
      <shareH5
        :build_name="build.build_name"
        :selling_point="build.build_selling_points"
        :discount="build.buy_coupon"
        :house_type="build.total_build_area"
        :average_price="build.build_avg_price"
        :broke_phone="user_info.phone"
        :long_url="poster_url"
        :short_url="poster_url"
        @cancel="cancelShare"
        @share="shareTo"
      ></shareH5>
    </myPopup>
    <myPopup :show="isPoster" @hide="isPoster = false">
      <myPoster
        :img="img"
        :build_name="build.build_name"
        :build_label="build.label"
        :price="build.build_avg_price"
        :open_time="build.newest_opening_time"
        :address="build.build_address"
        :url="poster_url"
      ></myPoster>
    </myPopup>
    <shareTip :show="shareTip" @hide="shareTip = false"></shareTip>
    <myPopup :show="touch_hide" @hide="touch_hide = false">
      <subForm
        @subdata="subdata"
        :build_id="build.build_id"
        @close="touch_hide = false"
        ref="subForm"
      >
      </subForm>
    </myPopup>
    <gmyFloatTouch v-if="!share_id"></gmyFloatTouch>
  </view>
</template>
<script>
import houseType from "@/components/house-type";
import myIcon from "@/components/my-icon";
import banner from "@/components/banner";
import counselorList from "@/components/counselor-list";
import shareContent from "@/components/share-content";
import noData from "@/components/noData";
import myLoading from "@/components/my-loading";
import shareH5 from "@/components/share-h5";
import { mapState, mapActions, mapMutations } from "vuex";
import myPopup from "@/components/myPopup";
import shareTip from "@/components/shareTip";
import uParse from "@/components/uParse/parse.vue";
import myPoster from "@/components/my-poster";
import subForm from "@/components/subForm";
// import VConsole from "vconsole";
export default {
  components: {
    myLoading,
    noData,
    banner,
    houseType,
    myIcon,
    counselorList,
    shareContent,
    shareH5,
    myPopup,
    shareTip,
    uParse,
    myPoster,
    subForm,
  },
  data() {
    return {
      build: {},
      commission: {},
      activity: {},
      house_type: [],
      map_navs: [
        {
          id: 1,
          name: "商业",
          path: "https://img.tfcs.cn/static/img/shangye.png",
          bgColor: "#f87d7e",
        },
        {
          id: 2,
          name: "教育",
          path: "https://img.tfcs.cn/static/img/jiaoyu.png",
          bgColor: "#38cfb1",
        },
        {
          id: 3,
          name: "医疗",
          path: "https://img.tfcs.cn/static/img/yiliao.png",
          bgColor: "#f88383",
        },
        {
          id: 4,
          name: "交通",
          path: "https://img.tfcs.cn/static/img/jiaotong.png",
          bgColor: "#67cbf8",
        },
      ],
      consultant_list: [],
      data_type: "",
      navs: [],
      share_list: {},
      data_type_house: "img",
      navs_house: [
        { id: 1, name: "VR", type: "vr" },
        { id: 2, name: "图片", type: "img" },
        { id: 3, name: "户型", type: "house" },
      ],
      build_img_main: [],
      build_img_list: [],
      params: {},
      nodata: false,
      noshare: true,
      nodiscount: false,
      img_list: [],
      build_category_list: [],
      build_category_Label: [],
      markers: [],
      circles: [],
      lat: "",
      long: "",
      isFollow: 0,
      follow: false,
      isShare: false,
      shareTip: false,
      personal_desc: [
        { icon: "wenda", name: "问题解答", value: 1 },
        { icon: "yaoqing", name: "邀约陪同", value: 2 },
        { icon: "fenxi", name: "专业分析", value: 3 },
        { icon: "youhui", name: "优惠便利", value: 4 },
      ],
      project_user: false,
      website_id: "",
      share_id: "",
      share_phone: "",
      tagStyle: {
        video: "max-width:100%",
      },
      isPoster: false,
      poster_url: "",
      short_url: "",
      build_dynamic_list: [],
      touch_hide: false,
      current: 0,
      house_imgs: [],
      imgs: [],
      company_rules: {},
      is_login_store: {},
      share_info: {},
      // 提交访客信息
      track_info: {
        user_id: "",
        visit_category: 0,
        build_id: "",
      },
      recommend_rule: {},
      // 报备保护
      report_protection_time: "",
      // 带看保护
      visit_protection_time: "",
      cancelShow: true,
      img: "",
      new_build_type: [
        {
          id: 1,
          name: "户型图",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/hxt.png",
        },
        {
          id: 2,
          name: "楼盘动态",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/dt.png",
        },
        {
          id: 3,
          name: "楼盘详情",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/xq.png",
        },
        {
          id: 4,
          name: "周边配套",
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/pt.png",
        },
      ],
      data_type_map: 1,
    };
  },
  onLoad(options) {
    // new VConsole();
    this.params.buildID = options.buildID;
    this.website_id = options.website_id;
    this.poster_url = window.location.href;
    this.init();
    // 匹配楼盘类型
    if (options.share) {
      this.getSetting((res) => {
        // 获取站点分享显示报备开关
        if (res.share_report_entrance == 1) {
          this.project_user = true;
        } else {
          this.project_user = false;
        }
      });
      this.share_id = options.share_id;
      // 分享添加访客记录
      setTimeout(() => {
        this.track_info.user_id = options.share_id;
        this.track_info.build_id = options.buildID;
        this.$ajax.post(
          "/client/user/visitor/record",
          this.track_info,
          (res) => {
            if (res.statusCode === 200) {
            }
          }
        );
      }, 1000);
      this.getShareUser();
    } else {
      this.project_user = true;
    }
  },
  computed: {
    ...mapState(["qqmapkey", "user_info", "im", "is_setting"]),
  },
  onReady() {
    this.$refs.loading.open();
  },
  methods: {
    ...mapActions(["getUserInfo", "getImToken", "getSetting"]),
    ...mapMutations(["setUserInfo"]),
    // 轮播图
    currentChange(e) {
      this.current = e.detail.current;
      this.data_type_house = this.imgs[this.current].type;
    },
    init() {
      if (uni.getStorageSync("token" + this.website_id)) {
        this.getUserInfo();
        this.getImToken();
      }
      // 获取字典中楼盘标签
      this.$getDictionaryList("BUILD_CATEGORY", {}, (res) => {
        if (res.statusCode === 200) {
          this.build_category_list = res.data.data;
          this.getDetailData();
          this.$ajax.get(
            `/common/build/img/${this.params.buildID}`,
            {},
            (res) => {
              if (res.statusCode === 200) {
                this.build_img_list = res.data;
              }
            }
          );
        }
      });
    },
    //  查询公司项目是否有单独的分佣规则如果有就显示
    queryCompanyRules() {
      this.$ajax.get(
        `/client/company/brokerage/rule/query/project/company/${this.build.project_id}/${this.user_info.company_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.company_rules = res.data;
          } else {
            uni.showToast({
              title: res.data.message || "获取失败",
              icon: "none",
            });
          }
        }
      );
    },
    // 获取楼盘动态信息
    getbuildDynamic() {
      this.$ajax.get(
        `/common/project/query/build/news/last/${this.build.build_id}/1`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.build_dynamic_list = res.data;
          } else {
            uni.showToast({
              title: res.data.message || "楼盘动态获取失败",
              icon: "none",
            });
          }
        }
      );
    },
    // 根据楼盘id查询是否收藏
    queryFavorite() {
      if (this.follow) {
        this.$ajax.get(
          `/client/project/favorite/queryId/build/${this.params.buildID}`,
          {},
          (res) => {
            if (res.statusCode === 200) {
              if (res.data.id == 0) {
                this.isFollow = 0;
              } else {
                this.isFollow = res.data.id;
              }
            }
          }
        );
      }
    },
    // 获取分享来的接口查询分享者的信息
    getShareUser() {
      this.$ajax.get(
        `/common/user/query/${this.share_id}?website_id=${this.website_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.share_info = res.data;
            this.share_phone = res.data.phone;
          }
        }
      );
    },
    // 获取优秀置业顾问
    getManagerData() {
      this.$ajax.get(
        `/common/project/all/manager/${this.build.project_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.consultant_list = res.data;
          } else {
            uni.showToast({
              title: res.data.message || "获取项目助理列表失败",
              icon: "none",
            });
          }
        }
      );
    },
    getDetailData() {
      this.$ajax.get(
        `/common/project/query/build/${this.params.buildID}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            // 判断用户id ，有就显示收藏按钮
            if (this.user_info.id) {
              this.follow = true;
              this.queryFavorite();
            }
            this.build = res.data;
            // 生成海报图片
            this.getBase64Image(
              this.build.build_img === ""
                ? "https://img.tfcs.cn/1/1/build/155/house_type/1597993921624510.jpg"
                : this.build.build_img,
              "coopCachetImg"
            );
            // 创建tab切换内容显示隐藏
            var tab_navs = [
              { id: 1, name: "报备带看", status: 1 },
              { id: 2, name: "佣金奖励", status: 2 },
              { id: 3, name: "优惠活动", status: 3 },
              { id: 4, name: "分享获客", status: 4 },
            ];
            // 如果关闭佣金规则和报备规则
            if (
              this.build.brokerage_rule_category !== 1 &&
              this.build.reported_rule_category === 1
            ) {
              this.data_type = 3;
              this.navs = tab_navs.filter((item) => {
                return (item.status !== 2) & (item.status !== 1);
              });
              // 如果关闭佣金规则
            } else if (this.build.brokerage_rule_category !== 1) {
              this.data_type = 1;
              this.navs = tab_navs.filter((item) => {
                return item.status !== 2;
              });
              // 如果关闭报备规则
            } else if (this.build.reported_rule_category === 1) {
              this.data_type = 2;
              this.navs = tab_navs.filter((item) => {
                return item.status !== 1;
              });
              // 都打开状态
            } else {
              this.data_type = 1;
              this.navs = tab_navs;
            }
            // 设置报备保护显示内容
            this.changeDateDayHour(
              this.build.reported_protected_day,
              this.build.reported_protected_hour,
              this.build.visit_protected_day,
              this.build.visit_protected_hour
            );
            // 获取设置的报备规则内容
            if (this.build.reported_rule) {
              this.recommend_rule = JSON.parse(this.build.reported_rule);
            }
            this.$refs.loading.close();
            // 获取公司单独规则（分佣）
            if (uni.getStorageSync("token" + this.website_id)) {
              // 登录且登录用户绑定门店
              this.is_login_store = {
                store_brokerage_description: this.user_info.company_store_code
                  ? this.build.store_brokerage_description
                  : "",
                store_brokerage_rule: this.user_info.company_store_code
                  ? this.build.store_brokerage_rule
                  : "",
                store_shopping_guide_reward: this.user_info.company_store_code
                  ? this.build.store_shopping_guide_reward
                  : "",
              };
              this.queryCompanyRules();
            }
            // 设置导航栏内容
            uni.setNavigationBarTitle({
              title: this.build.build_name,
            });
            this.getbuildDynamic();
            // 判断项目id是否为null值，如果不是获取项目助理列表
            if (this.build.project_id !== null) {
              this.getManagerData();
            }
            // 判断楼盘是否为项目
            if (this.build.project_id === null) {
              this.project_user = false;
            }
            this.build_category_Label = this.build.build_category;
            // 获取地图操作
            this.lat = parseFloat(res.data.build_location_lat);
            this.long = parseFloat(res.data.build_location_long);
            this.markers = [
              {
                id: this.params.id,
                latitude: this.lat,
                longitude: this.long,
                width: 1,
                height: 1,
                iconPath: "https://images.zaodaoxiao.com/icons/xiaoxuequ.png",
                callout: {
                  content: res.data.build_name,
                  display: "ALWAYS",
                  textAlign: "center",
                  borderRadius: 4,
                },
              },
            ];
            //  地图圆圈
            this.circles = [
              {
                latitude: this.lat,
                longitude: this.long,
                color: "#0178f7",
                fillColor: "#a0c7f3AA",
                radius: 30,
                strokeWidth: 2,
              },
            ];
            // 楼盘标签
            var arr1 = [];
            this.build_category_list.map((e) => {
              if (this.build_category_Label.includes(e.value))
                arr1.push(e.description);
            });
            this.build_category_Label = arr1;
            // this.poster_url =
            //   window.location.origin +
            //   window.location.pathname +
            //   `?buildID=${this.params.buildID}&website_id=${uni.getStorageSync(
            //     "website_id"
            //   )}&share=from_share&share_id=${this.user_info.id}`;
            // this.getShortUrl();
            // 将楼盘图片存入数组
            this.$ajax.get(
              `/common/build/img/${this.params.buildID}`,
              {},
              (res) => {
                if (res.statusCode === 200) {
                  // 判断如果长度为空，将楼盘详情缩略图添加进数组
                  if (res.data.length > 0) {
                    this.build_img_main = res.data.map((item) => {
                      return {
                        img: item.img,
                        type: "img",
                      };
                    });
                  } else {
                    this.build_img_main.push({
                      img:
                        this.build.build_img ||
                        "https://img.tfcs.cn/static/img/que.jpg",
                      type: "img",
                    });
                  }
                  this.getHouseTypeData();
                }
              }
            );
            //  判断用户类型是否为经纪人且是否有手机号
            if (this.user_info.category === 1 && this.user_info.phone) {
              this.share = {
                forward_title: this.build.share_title || this.build.build_name,
                forward_desc:
                  this.build.share_description ||
                  this.build.build_selling_points,
                forward_pic: this.build.share_img || this.build.build_img,
                forward_id: this.user_info.id,
                forward_config: "build",
              };
              this.getWxConfig();
              if (
                this.$isWxWork() === "wxwork" ||
                this.$isWxWork() === "com-wx-pc"
              ) {
                // this.getWxQyWxConfig();
              }
              // 判断分享的用户类型是否为经纪人且是否有手机号
            } else if (
              this.share_info.category === 1 &&
              this.share_info.phone
            ) {
              this.share = {
                forward_title: this.build.share_title || this.build.build_name,
                forward_desc:
                  this.build.share_description ||
                  this.build.build_selling_points,
                forward_pic: this.build.share_img || this.build.build_img,
                forward_id: this.share_id,
                forward_config: "build",
              };
              this.getWxConfig();
              if (
                this.$isWxWork() === "wxwork" ||
                this.$isWxWork() === "com-wx-pc"
              ) {
                // this.getWxQyWxConfig();
              }
            } else {
              this.share = {
                forward_title: this.build.share_title || this.build.build_name,
                forward_desc:
                  this.build.share_description ||
                  this.build.build_selling_points,
                forward_pic: this.build.share_img || this.build.build_img,
                forward_id: this.user_info.id,
                forward_config: "build",
              };
              this.getWxConfig();
              if (
                this.$isWxWork() === "wxwork" ||
                this.$isWxWork() === "com-wx-pc"
              ) {
                // this.getWxQyWxConfig();
              }
            }
            this.queryShareContent(); // 获取分享内容
            this.surrounding("商业", "f87d7e", 1);
          } else {
            uni.showToast({
              title: "获取楼盘详情失败",
              icon: "none",
              mask: true,
            });
          }
        }
      );
    },

    // 过滤报备，带看时间中为零的显示
    changeDateDayHour(Rday, Rhour, Vday, Vhour) {
      Rday === 0 ? (Rday = " ") : (Rday = Rday + "天");
      Rhour === 0 ? (Rhour = " ") : (Rhour = Rhour + "小时");
      Vday === 0 ? (Vday = " ") : (Vday = Vday + "天");
      Vhour === 0 ? (Vhour = " ") : (Vhour = Vhour + "小时");
      this.report_protection_time = Rday + Rhour;
      this.visit_protection_time = Vday + Vhour;
    },
    // 获取户型列表，将户型图放进新数组
    getHouseTypeData(c) {
      this.$ajax.get(
        `/common/project/all/house_type/${this.params.buildID}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            if (c) {
              this.house_type = res.data.filter((item) => {
                return c == item.category;
              });
              return;
            }
            this.house_type = res.data;
            if (this.house_type.length > 0) {
              this.house_imgs = res.data.map((item) => {
                return { img: item.img, type: "house" };
              });
            } else {
              this.house_imgs = [
                {
                  img: "https://img.tfcs.cn/static/img/que.jpg",
                  type: "house",
                },
              ];
            }
            this.imgs = this.build_img_main.concat(this.house_imgs);
            if (!this.house_type.length) {
              this.nodata = true;
            }
          } else {
            uni.showToast({
              title: "获取户型列表失败",
              icon: "none",
              mask: true,
            });
          }
        }
      );
    },
    clickMsg(item) {
      // 楼盘详情进入聊天页面参数拼接项目id
      if (this.im.imToken) {
        this.$navigateTo(
          `/im_list/msg_detail?to_id=${item.user_id}&project_id=${item.project_id}`
        );
      }
    },
    // 置业顾问电话
    clickTel(e) {
      if (e) {
        uni.makePhoneCall({
          phoneNumber: e,
          success: () => {
            console.log("拨打置业顾问电话电话");
          }, //仅为示例
        });
      }
    },
    // 转发分享，判断登录状态
    forwordShare() {
      if (!uni.getStorageSync("token" + this.website_id)) {
        uni.showToast({
          title: "暂未登录",
          icon: "none",
        });
        this.$navigateTo("/user/phone_login");
      } else {
        this.shareTip = true;
      }
    },
    // 分享后获取
    dialTel() {
      if (this.share_phone) {
        uni.makePhoneCall({
          phoneNumber: this.share_phone,
        });
      } else {
        uni.showToast({
          title: "暂无电话",
          icon: "none",
        });
      }
    },
    // 点击查询周边
    surrounding(keyword, bgColor, id) {
      this.data_type_map = id;
      this.$jsonp("https://apis.map.qq.com/ws/place/v1/search", {
        keyword,
        boundary: `nearby(${this.lat},${this.long},1000)`,
        key: this.qqmapkey,
        output: "jsonp",
      })
        .then((res) => {
          this.markers = res.data.map((item) => {
            return {
              id: item.id,
              longitude: item.location.lng,
              latitude: item.location.lat,
              width: 1,
              height: 1,
              distance: this.kmUnit(item._distance),
              iconPath: "https://images.zaodaoxiao.com/icons/xiaoxuequ.png",
              callout: {
                color: "#fff",
                content: item.title,
                display: "ALWAYS",
                textAlign: "center",
                borderRadius: 4,
                bgColor: bgColor,
              },
            };
          });
          this.circles = [];
        })
        .catch((err) => {
          console.log(err);
        });
    },
    onOpenAddress(item) {
      uni.openLocation({
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        success: function() {
          console.log("success");
        },
        fail: function(err) {
          uni.showToast({
            title: "打开地图失败",
            icon: "none",
          });
        },
      });
    },
    //  米转千米
    kmUnit(m) {
      var v;
      if (typeof m === "number" && !isNaN(m)) {
        v = (m / 1000).toFixed(2) + "km";
      } else {
        v = "0m";
      }
      return v;
    },
    queryShareContent() {
      this.$ajax.get(
        `/common/project/query/share/content/${this.build.project_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.share_list = res.data;
            if (res.data) {
              this.noshare = false;
            } else {
              this.noshare = true;
            }
            const share_img_list = [];
            share_img_list.push(
              this.share_list.img_1,
              this.share_list.img_2,
              this.share_list.img_3,
              this.share_list.img_4,
              this.share_list.img_5,
              this.share_list.img_6,
              this.share_list.img_7,
              this.share_list.img_8,
              this.share_list.img_9
            );
            this.img_list = share_img_list.filter(function(s) {
              return s && s.trim(); // 注：IE9(不包含IE9)以下的版本没有trim()方法  （移除收尾空格）
            });
          }
        }
      );
    },
    // 点击切换栏目
    onClickNav(index) {
      this.data_type = this.navs[index].id;
    },
    // 点击bannner图切换显示图片内容标签
    onClickNavHouse(index, type) {
      if (type === "vr") {
        window.location.href = this.build.build_vr_url;
      }
      if (type === "img") {
        this.current = 0;
      }
      if (type === "house") {
        this.current = this.build_img_main.length;
      }
      this.data_type_house = this.navs_house[index].type;
    },
    CallBuildTel() {
      if (!this.build.sales_office_phone) {
        uni.showToast({
          title: "暂无联系方式",
          icon: "none",
        });
        return;
      }
      uni.makePhoneCall({
        phoneNumber: this.build.sales_office_phone,
        success: () => {
          console.log("拨打项目咨询电话");
        },
      });
    },

    // 复制剪切内容
    copyShare(e) {
      // #ifdef  H5
      // #endif
      //#ifndef H5
      //提示模板
      uni.showModal({
        content: e.content, //模板中提示的内容
        confirmText: "复制内容",
        success: () => {
          console.log("复制");
        },
      });
      //#endif
    },
    goAroundMap(id) {
      this.$navigateTo(`/build/around_map?id=${id}`);
    },
    // 收藏
    handleFollow() {
      this.$ajax.post(
        "/client/project/favorite/create",
        {
          build_id: this.params.buildID,
        },
        (res) => {
          if (res.statusCode === 200) {
            this.isFollow = res.data.id;
            uni.showToast({
              title: "收藏成功",
            });
          } else {
            uni.showToast({
              title: res.data.message || "收藏失败",
              icon: "none",
            });
          }
        }
      );
    },
    // 取消收藏
    handleCancleFollow() {
      this.$ajax.get(
        `/client/project/favorite/delete/${this.isFollow}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.isFollow = 0;
            uni.showToast({
              title: "取消收藏",
              icon: "none",
            });
          }
        }
      );
    },
    // 分享
    shareDetail() {
      this.isShare = true;
      uni.pageScrollTo({
        scrollTop: 0, //距离页面顶部的距离
        duration: 300,
      });
    },
    cancelShare(bool) {
      this.isShare = bool;
    },
    shareTo(item) {
      if (item.value == 3) {
        this.isPoster = true;
        this.isShare = false;
      } else {
        this.shareTip = true;
      }
    },
    // 未绑定项目显示预约看房显示提交信息
    subdata(e) {
      if (!e.name || !e.phone) {
        uni.showToast({
          title: "请输入内容后提交",
          icon: "none",
        });
        return;
      }
      if (!/^1[3456789]\d{9}$/.test(e.phone)) {
        uni.showToast({
          title: "请输入正确联系方式",
          icon: "none",
        });
        return;
      }
      this.$ajax.post("/common/build/enroll/create", e, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "预约成功",
          });
          this.touch_hide = false;
          e.name = "";
          e.phone = "";
        } else {
          uni.showToast({
            title: res.data.message || "预约失败，请稍后重试",
            icon: "none",
          });
        }
      });
    },
    // 获取微信短链接
    getShortUrl() {
      this.$ajax.post(
        "/common/wx_open/short_url",
        {
          long_url: this.poster_url,
        },
        (res) => {
          if (res.statusCode === 200) {
            this.short_url = res.data.short_url;
          } else {
            uni.showToast({
              title: res.data.message || "获取微信链接失败",
              icon: "none",
            });
          }
        }
      );
    },
    showAllCancel() {
      this.cancelShow = !this.cancelShow;
    },
    // 图片转base64
    getBase64Image(url, ref) {
      var that = this;
      var image = new Image();
      image.src = url + "?v=" + Math.random(); // 处理缓存
      image.crossOrigin = "*"; // 支持跨域图片
      image.onload = function() {
        that.img = that.drawBase64Image(image);
      };
    },
    drawBase64Image(img) {
      var canvas = document.createElement("canvas");
      canvas.width = img.width;
      canvas.height = img.height;
      var ctx = canvas.getContext("2d");
      ctx.drawImage(img, 0, 0, img.width, img.height);
      var dataURL = canvas.toDataURL("image/png");
      return dataURL;
    },
    // 查看图片数组
    previewImgs(imgs, index, item) {
      if (item.type === "img") {
        this.$navigateTo(`/build/build_photos?build_id=${this.params.buildID}`);
      }
      // let arr = imgs.map((item) => {
      //   return item.img;
      // });
      // this.previewImage(arr, index);
    },
    // 位置及周边导航
    getNavigation() {
      uni.openLocation({
        latitude: parseFloat(this.build.build_location_lat),
        longitude: parseFloat(this.build.build_location_long),
        success: function() {
          console.log("success");
        },
        fail: (res) => {
          console.log(res);
        },
      });
    },
    onClickMenu(item) {
      if (item.id == 3) {
        this.$navigateTo(
          `/build/build_description?build_id=${this.build.build_id}`
        );
        return;
      }
      uni
        .createSelectorQuery()
        .select(".new-detail")
        .boundingClientRect((res) => {
          uni
            .createSelectorQuery()
            .select(".menu" + item.id)
            .fields({ rect: true, scrollOffset: true }, (data) => {
              uni.pageScrollTo({
                duration: 300,
                scrollTop: data.top - res.top + 200, //滚动到实际距离是元素距离顶部的距离减去最外层盒子的滚动距离
              });
            })
            .exec();
        })
        .exec();
    },
    // 户型筛选
    onClickNavType(c) {
      this.getHouseTypeData(c);
    },
  },
  onPullDownRefresh: function() {
    this.init();
    setTimeout(() => {
      uni.stopPullDownRefresh();
    }, 1000);
  },
  onNavigationBarButtonTap(e) {
    this.isShare = true;
    uni.pageScrollTo({
      scrollTop: 0, //距离页面顶部的距离
      duration: 300,
    });
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f9f9f9;
}
.tianjia {
  align-items: flex-end;
  margin-right: 48rpx;
  padding: 24rpx 0;
  .huokebox {
    align-items: center;
    font-size: 24rpx;
    .tianjia-end {
      width: 40rpx;
      height: 40rpx;
    }
    .huoke {
      text-align: center;
      color: #3172f6;
      font-size: 28rpx;
      margin-left: 14rpx;
    }
  }
}
.share-btn {
  position: fixed;
  width: 100%;
  height: 128rpx;
  bottom: 0;
  background: #fff;
  padding: 24rpx 48rpx;
  justify-content: space-between;
  .btn {
    align-items: center;
    line-height: 80rpx;
    color: #fff;
    border-radius: 10rpx;
    width: 48%;
    height: 80rpx;
    background: #eb4247;
    &:last-child {
      background: #0097fe;
    }
  }
}
.bacfff {
  background: #fff;
  margin-bottom: 24rpx;

  .detail-btn {
    font-size: 22rpx;
    top: 48rpx;
    right: 36rpx;
    position: absolute;
    color: #fff;
    height: 40rpx;
    width: 70rpx;
    line-height: 40rpx;
    align-items: center;
    background: #ff7a00;
    border-radius: 4rpx;
  }
  .bottom-box {
    width: 100%;
    position: fixed;
    bottom: 0;
    justify-content: space-between;
    height: 180rpx;
    padding: 24rpx 48rpx;
    background: #fff;
    border-radius: 40rpx 40rpx 0 0;
    z-index: 99;
    .left {
      width: 100%;
      align-items: center;
      .icon-box {
        margin-right: 56rpx;
      }
      font-size: 22rpx;
      color: #141414;
      image {
        width: 48rpx;
        height: 48rpx;
      }
      text {
        margin-top: 10rpx;
      }
    }

    .right {
      align-items: center;
      .btn {
        width: 216rpx;
        align-items: center;
        line-height: 80rpx;
        height: 80rpx;
        color: #fff;
        border-radius: 6rpx;
        &:first-child {
          background: #3bc48c;
        }
        &:last-child {
          margin-left: 24rpx;
          background: #3172f6;
        }
      }
    }
    .project_id {
      width: 100%;
      justify-content: space-around;
      .btn {
        width: 240rpx;
      }
    }
  }
  .activity-box {
    position: relative;
    width: 654rpx;
    height: 136rpx;
    margin: 24rpx auto;
    background-image: url("https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/yhq.png");
    background-size: 100% 100%;
    align-items: center;
    .activity-txt {
      font-size: 28rpx;
      color: #8a6c49;
      margin-bottom: 6rpx;
    }
    .ctn {
      margin-left: 140rpx;
      width: 500rpx;
      .title {
        font-size: 18rpx;
        color: #8a6c49;
      }
      .label {
        align-items: flex-end;
        font-size: 18rpx;
      }
    }
  }
}

._margin {
  padding: 48rpx;
  justify-content: space-between;
}
.title {
  font-size: 40rpx;
  color: #333;
}
.title-top {
  .title {
    justify-content: space-between;
    align-items: flex-end;
    font-size: 40rpx;
    font-weight: bold;
    .shoucang {
      align-items: center;
      color: #0174ff;
      font-size: 28rpx;
      text {
        margin-left: 10rpx;
      }
    }
  }
  .item {
    margin-top: 24rpx;
  }
  .type-item {
    flex-wrap: wrap;
    text {
      margin: 4rpx 10rpx;
      padding: 4rpx 12rpx;
      border-radius: 1px;
      border: 0.5px solid #d3d3d3;
      font-size: 22rpx;
      color: #6f6f6f;
    }
    .type-item-label {
      color: #fff;
      margin-left: 0;
      border: none;
      background: #3172f6;
    }
  }
  .label {
    margin-right: 16rpx;
    font-size: 28rpx;
    color: #999;
  }
  .label-ctn {
    font-size: 28rpx;
    color: #333;
  }
  .price-item {
    align-items: flex-end;

    .price {
      font-size: 48rpx;
      color: #fb656a;
      align-items: flex-end;
    }
  }
}
._list {
  // 导航
  padding: 48rpx 0 0 48rpx;
  .nav {
    width: 100%;
    background: #fff;
    flex-direction: row;
    // padding: 0 48rpx;
    .nav-item {
      font-size: 28rpx;
      padding-bottom: 20rpx;
      text-align: center;
      font-weight: bold;
      color: #989898;
      position: relative;
      padding-right: 48rpx;
      &.active {
        color: #141414;
      }
      .is-active {
        position: absolute;
        bottom: 12rpx;
        background-repeat: no-repeat;
        height: 16rpx;
        width: 108rpx;
      }
    }
  }
}

.content-list {
  background: #fff;
  padding: 0 48rpx;
  .title-top {
    justify-content: space-between;
    align-items: center;
  }
  ._line {
    padding-top: 20rpx;
    color: #999;
    line-height: 40rpx;
    font-size: 26rpx;
    .label {
      width: 118rpx;
      margin-right: 36rpx;
      text-align-last: justify;
      display: inline-block;
      text-align: justify;
    }
    &:last-child {
      border: none;
    }
    .content {
      flex: 1;
      color: #333;
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
    }
    ._ctn_ {
      -webkit-line-clamp: 2;
    }
    .content_show {
      flex: 1;
      color: #333;
    }
  }
  .look_more {
    align-items: center;
    color: #999;
    margin-top: 48rpx;
  }
  .more {
    font-size: 22rpx;
    color: #6f6f6f;
  }
}
.project-address {
  margin-top: 22rpx;
  .left {
    font-size: 26rpx;
    color: #6f6f6f;
  }
  .right {
    color: #141414;
    font-size: 28rpx;
  }
}
.mapbox {
  margin-top: 24rpx;
  position: relative;

  map {
    width: 100%;
    height: 340rpx;
  }
}
.map-nav {
  justify-content: space-between;
  background: #fff;
  margin-top: 40rpx;
  font-size: 32rpx;
  color: #141414;
  align-items: baseline;
  .navs-box {
    align-items: center;
    &.active {
      color: #3172f6;
      border-bottom: 4rpx solid #3172f6;
      padding-bottom: 8rpx;
    }
  }
}
.address-list {
  margin-top: 30rpx;
  .address-item {
    color: #141414;
    font-size: 26rpx;
    justify-content: space-between;
    align-items: center;
    line-height: 50rpx;
    .right {
      color: #6f6f6f;
    }
  }
}
.content-list:last-child {
  margin-bottom: 60rpx;
}
.last-list {
  padding-top: 0;
}

.banner-box {
  height: 552rpx;
  position: relative;
  width: 100%;
  .indicato {
    position: absolute;
    right: 26rpx;
    bottom: 128rpx;
    height: 30rxp;
    line-height: 44rpx;
    padding: 0 20rpx;
    border-radius: 22rpx;
    font-size: $uni-font-size-sm;
    background-color: rgba($color: #000000, $alpha: 0.5);
    color: #fff;
  }
  .swiper {
    width: 100%;
    height: 560rpx;
    image {
      width: 100%;
      height: 560rpx;
    }
  }

  // 导航
  .nav-house {
    position: absolute;
    left: 50%;
    bottom: 128rpx;
    transform: translate(-50%);
    width: auto;
    align-items: center;
    color: #000;
    height: 48rpx;
    background: #fff;
    justify-content: space-evenly;
    border-radius: 24rpx;
    .nav-item-house {
      height: 100%;
      width: 80rpx;
      line-height: 48rpx;
      text-align: center;
      font-size: 22rpx;
      color: #000;
      &.active {
        color: #fff;
        background: #3172f6;
        border-radius: 24rpx;
      }
      &:first-child {
        border-radius: 12px 0 0 12px;
      }
      &:last-child {
        border-radius: 0 12px 12px 0;
      }
    }
  }
}
.personal-ctn {
  justify-content: space-between;
  font-size: 24rpx;
  margin-top: 20rpx;
  color: #141414;
  .ctn-box {
    align-items: center;
    margin: 24rpx 0;
    image {
      width: 24rpx;
      height: 24rpx;
    }
    text {
      margin-left: 6rpx;
    }
  }
}
.build-dynamic {
  padding-left: 20rpx;
  padding-bottom: 24rpx;
  position: relative;
  margin-top: 30rpx;
  &::before {
    content: "";
    position: absolute;
    width: 2rpx;
    top: -10rpx;
    bottom: 0;
    left: -0.5px;
    background-color: #f5f5f5;
  }
  &::after {
    content: "";
    position: absolute;
    width: 20rpx;
    height: 20rpx;
    border-radius: 50%;
    background-image: linear-gradient(180deg, #f7918f 0%, #fb656a 100%);
    top: 10rpx;
    left: -10rpx;
  }
  .time {
    align-items: center;
    padding-left: 20rpx;
    margin-bottom: 24rpx;
    font-size: 28rpx;
    color: #999;
    .attr {
      line-height: 1;
      padding: 4rpx 10rpx;
      margin-right: 10rpx;
      font-size: 28rpx;
      color: #fff;
      background-image: linear-gradient(180deg, #f7918f 0%, #fb656a 100%);
      border-top-left-radius: 8rpx;
      border-bottom-right-radius: 8rpx;
    }
  }
  .title {
    font-weight: bold;
    padding: 0 20rpx;
    margin-bottom: 24rpx;
  }
  .sub_title {
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #666;
    margin-bottom: 24rpx;
    line-height: 40rpx;
  }
}
.recommend-rule {
  padding-bottom: 24rpx;
  position: relative;
  margin-top: 30rpx;
  .content-box {
    margin-bottom: 20rpx;
    .time {
      font-size: 28rpx;
      align-items: center;
      .attr {
        padding: 8rpx 30rpx;
      }
      .yuan {
        left: -12rpx;
        width: 28rpx;
        height: 28rpx;
        border: 1px solid #88abf5;
        border-radius: 50%;
        position: absolute;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        &::before {
          content: "";
          width: 18rpx;
          height: 18rpx;
          background: #88abf5;
          border-radius: 50%;
        }
      }
    }
    .sub_title {
      padding: 12rpx 30rpx;
      font-size: 26rpx;
      color: #6f6f6f;
      line-height: 40rpx;
    }
    // &::before {
    //   position: absolute;
    //   content: "";
    //   width: 24rpx;
    //   height: 24rpx;
    //   border-radius: 50%;
    //   border: 4rpx solid #88abf5;
    //   background: #88abf5;
    //   left: -14rpx;
    // }
  }
}
.files_text {
  line-height: 40rpx;
  margin-top: 20rpx;
  color: #6f6f6f;
  padding-top: 20rpx;
}
.new-detail {
  padding: 0 20rpx;
  width: 100%;
  position: relative;
  top: -110rpx;
  // background: #f9f9f9;
  .top-box {
    min-height: 200rpx;
    border-radius: 20rpx;
    background: #fff;
    margin-bottom: 32rpx;
    .price-desc {
      justify-content: space-between;
      padding: 28rpx 0;
      .left-p {
        font-size: 40rpx;
        color: #ff381f;
      }
      .daohang {
        width: 104rpx;
        height: 40rpx;
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
    .build-detail-list {
      justify-content: space-between;
      margin-top: 28rpx;
      .build-detail-item {
        align-items: center;
        image {
          width: 92rpx;
          height: 92rpx;
        }
        .name {
          font-size: 24rpx;
          color: #141414;
          margin-top: 24rpx;
        }
      }
    }
    .build-item {
      font-size: 26rpx;
      margin-top: 38rpx;
      .row {
        margin-bottom: 32rpx;
        color: #6f6f6f;
        align-items: center;
        .left {
          width: 130rpx;
          text-align-last: justify;
          display: inline-block;
          text-align: justify;
        }
        .label-ctn {
          color: #333;
          line-height: 40rpx;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    .fujian {
      padding: 48rpx;
      .title-top {
        align-items: center;
        justify-content: space-between;
      }
      .more {
        color: #6f6f6f;
      }
    }
    .content-list-dt {
      padding: 48rpx;
      .title {
        font-weight: bold;
      }
      .title-top {
        justify-content: space-between;
        align-items: center;
        .more {
          color: #6f6f6f;
        }
      }
    }
  }
}
</style>
