<template>
    <myPopup :show="show" @close="show = false">
        <view class="popup-container">
            <view class="header">
                <view class="action cancle" @click.stop="cancle">取消</view>
                <view class="title"></view>
                <view class="action confirm" @click.stop="confirm">确认</view>
            </view>
            <view class="filter-body">
                <scroll-view scroll-y class="filter-body-scroll">
                    <view class="filter-content"> 
                        <view class="filter-group" v-for="(group, groupIndex) in groups" :key="groupIndex">
                            <template v-if="group.options && group.options.length">
                                <view class="filter-group-title" v-if="group.title">
                                    {{group.title}}
                                </view>
                                <view class="filter-list">
                                    <view class="filter-list-item" v-for="(option,index) in group.options" :key="index"
                                        :class="{ active: group.multiple ? group.value.includes(option.value) : group.value === option.value }" @click="handleClickFilterItem(groupIndex, group, option)"
                                    >{{ option.label }}</view>
                                </view>
                            </template>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </view>
    </myPopup>
</template>

<script>
import myPopup from '@/components/myPopup';
export default {
    props: {
        visible: { type: Boolean, default: false },
        datas: { type: Array, default: () => [] },
        params: { type: Object, default: () => ({}) },
    },
    components: {
        myPopup
    },
    data(){
        return {
            show: false,
            groups: []
        }
    },
    watch: {
        datas: {
            handler(val){
                this.groups = val.map(e => {
                    let value = this.params[e.field] ?? '';
                    return {...e, value };
                })
            },
            immediate: true
        },
        visible(val){
            this.show = val;
        },
        show(val){
            val !== this.visible && this.$emit('update:visible', val);
        }
    },
    methods: {
        cancle(){
            this.show = false;
        },
        confirm(){
            this.show = false;
            const datas = [];
            const params = {...this.params};
            for(const group of this.groups){
                if(group.value !== ''){
                    let item = (group.options || []).find(e => e.value === group.value),
                        label = item ? item.label : '';
                    datas.push({
                        field: group.field,
                        label,
                        value: group.value
                    })
                }

                params[group.field] = group.value;
            }

            this.$emit('update:params', params);
            this.$emit('confirm', datas);
        },
        //点击 filter 下拉菜单项
        handleClickFilterItem(index, item, { value }){
            item.value = value;
        },
        clear(){
            for(const group of this.groups){
                group.value = group.default !== undefined ? group.default : ''; 
            }
        },
    }
}
</script>
<style lang="scss" scoped>
.popup-container{
    background-color: #fff;
    line-height: 1;
    .header{
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 46px;
        :after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            height: 1px;
            clear: both;
            border-bottom: 1px solid #e5e5e5;
            color: #e5e5e5;
            -webkit-transform-origin: 0 100%;
            transform-origin: 0 100%;
            -webkit-transform: scaleY(.5);
            transform: scaleY(.5);
        }
        .title{
            flex: 1;
            color: #999;
            text-align: center;
            display: inline-block;
            max-width: 50%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .action{
            padding: 0 14px;
            font-size: 17px;
            &.cancle{
                color: #888;
            }
            &.confirm{
                color: #007aff;
            }
        }
    }
    .filter-body{
        background-color: #fff;
        .filter-body-scroll{
            min-height: 35vh;
            max-height: 65vh;
        }
        .filter-content{
            padding: 24rpx 0 8rpx 32rpx;
            .filter-group{
                padding-top: 8rpx;
                .filter-group-title{
                    flex-direction: row;
                    align-items: center;
                    font-size: 32rpx;
                    padding: 0 0 24rpx 0;
                    .label{
                        color: #9c9c9c;
                        font-size: 24rpx;
                        font-weight: 400;
                    }
                }
            }
            
            .filter-list{
                display: flex;
                flex-direction: row;
                flex-wrap: wrap;
                .filter-list-item{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    height: 76rpx;
                    font-size: 28rpx;
                    color: #4E5969;
                    background-color: #f6f6f6;
                    min-width: calc( (100vw - 112rpx) / 3);
                    margin: 0 24rpx 24rpx 0;
                    padding: 0 4rpx;
                    &.active{
                        color: #2d84fb;
                        background-color: #e5eeff;
                    }
                }
            }
        }

        .filter-dropdown-footer{
            display: flex;
            flex-direction: row;
            padding: 32rpx 20rpx;
            border-top: 0.5px solid #F2F3F5;
            .btn{
                flex: 1;
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                height: 80rpx;
                margin: 0 12rpx;
                font-size: 32rpx;
                color: #fff;
                background-color: #2d84fb;
                border-radius: 8rpx;
                &.btn-reset{
                    color: #4E5969;
                    background-color: #f2f3f5;
                    
                }
            }
        }
    }
}
</style>