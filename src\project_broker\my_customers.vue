<template>
  <view class="list">
    <view class="title-bar">
      <view class="search-tab">
        <my-search
          width="287px"
          placeholder="请输入客户手机号码"
          @input="onInput"
        >
          <template v-slot:left>
            <my-icon type="ic_sousuo3x1" color="#999"></my-icon>
          </template>
          <template v-slot:right>
            <text v-if="isVisit == false" class="xinzeng" @click="newAdd"
              >消息</text
            >
            <unibadge
              type="error"
              class="unibadge"
              v-if="parseInt(unread_msg) > 0"
              :text="unread_msg"
            ></unibadge>
          </template>
        </my-search>
      </view>
    </view>
    <tab-bar
      ref="tab_bar"
      :tabs="report_cates"
      :nowIndex="
        report_cates.findIndex((item) => Number(item.value) == params.type)
      "
      fixed_top
      top="100"
      @click="onClickCate"
    ></tab-bar>
    <view class="title-tips row">
      <text class="icon-baobei-ic_guanyu3x1 icon-baobei"></text>
      <text>扫描客户码将自动转为我的客户</text>
      <view
        class="row"
        @click="showContent = !showContent"
        style="margin-left:30rpx"
      >
        选择时间
        <myIcon
          :type="showContent ? 'xiala' : 'shangla'"
          size="30rpx"
          color="#0174ff"
          style="margin-left:10rpx"
        ></myIcon>
      </view>
    </view>
    <view class="time-box row" v-if="showContent">
      <view class="input-box row">
        <input
          id="start"
          class="input"
          disabled="true"
          type="text"
          v-model="deal_at_date_start"
          placeholder="开始时间"
          @click="$refs.picker_start.show()"
        />
        <text>至</text>
        <input
          id="end"
          class="input"
          type="text"
          disabled="true"
          v-model="deal_at_date_end"
          placeholder="结束时间"
          @click="$refs.picker_end.show()"
        />
      </view>
      <view class="search" @click="searchTime">
        搜索
      </view>
    </view>
    <view class="report-list">
      <my-customer
        v-for="(item, index) in customer_list"
        :key="index"
        :customer_item="item"
        :btn="false"
        @status="followStatus"
        @sendMsg="sendMsg"
      ></my-customer>
      <load-more :status="load_status"></load-more>
    </view>
    <backTop :scrollTop="scrollTop"></backTop>
    <!-- 弹出列表 -->
    <!-- 开始 -->
    <timePicker
      end="2030-12-30"
      mode="YMD"
      ref="picker_start"
      @confirm="confirmTimeStart"
    ></timePicker>
    <!-- 结束 -->
    <timePicker
      end="2030-12-30"
      mode="YMD"
      ref="picker_end"
      @confirm="confirmTimeEnd"
    ></timePicker>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import timePicker from "@/components/time-picker";
import mySearch from "@/components/my-search";
import myIcon from "@/components/my-icon";
import tabBar from "@/components/tabBar";
import myCustomer from "@/components/customer_item";
import loadMore from "@/components/loadMore";
import unibadge from "@/components/uni-badge/uni-badge";
import { mapActions, mapState } from "vuex";
import backTop from "../components/backtop/components/back-top/back-top";
export default {
  components: {
    mySearch,
    tabBar,
    myIcon,
    myCustomer,
    loadMore,
    backTop,
    timePicker,
    unibadge,
  },
  data() {
    return {
      report_cates: [],
      broker_id: "",
      cate_index: -1,
      customer_list: [],
      params: {
        page: 1,
        type: "",
        customer_phone: "",
        status: 1,
        project_user_id: "",
      },
      isVisit: false,
      load_status: "",
      statistics: [],
      report_tatol: "",
      scrollTop: 0,
      deal_at_date_start: "",
      deal_at_date_end: this.$getTime("YMD"),
      url: "/client/customer/reported/search/project",
      showContent: false,
    };
  },
  computed: {
    ...mapState(["unread_msg"]),
  },
  onShow() {},
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  onLoad(options) {
    if (options.id) {
      this.broker_id = options.id;
      this.params.project_user_id = options.id;
      this.params.type = parseInt(options.type);
      this.params.status = parseInt(options.type);
    }
    if (options.visit) {
      this.getUserInfo({
        success: (res) => {
          if (res.statusCode === 200) {
            this.params.project_user_id = res.data.id;
          } else {
            uni.showToast({
              title: res.data.message || "页面出错",
              icon: "none",
            });
          }
        },
      });
      this.isVisit = true;
      this.params.type = parseInt(options.type);
      this.params.status = parseInt(options.type);
    } else {
      this.isVisit = false;
    }
    this.useInit();
  },
  methods: {
    ...mapActions(["getUserInfo", "getImToken"]),
    useInit() {
      this.init();
      this.getDataList();
    },
    init() {
      this.$getDictionaryList("REPORTED_STATUS", {}, (res) => {
        if (res.statusCode === 200) {
          setTimeout(() => {
            this.report_cates = [
              { value: -1, description: "全部" },
              ...res.data.data,
            ];
            for (var i = 0; i < this.report_cates.length; i++) {
              this.report_cates[i].statistics = "";
              this.statistics.map((e) => {
                if (this.report_cates[i].value == e.status) {
                  this.report_cates[i].statistics = e.total;
                }
                // if (this.report_cates[i].value === -1) {
                //   this.report_cates[i].statistics = this.report_tatol;
                // }
              });
            }
          }, 500);
        }
      });
    },

    getDataList() {
      this.load_status = "loading";
      this.params.status === -1 || this.params.type === -1
        ? delete this.params.status
        : (this.params.status = this.params.type);
      if (this.params.page === 1) {
        this.customer_list = [];
      }
      this.$ajax.get(this.url, this.params, (res) => {
        this.load_status = "loadend";
        if (res.statusCode === 200) {
          let arr = res.data.data;
          this.statistics = res.data.statistics;
          this.customer_list = this.customer_list.concat(arr);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        } else {
          uni.showToast({
            title: res.data.message || "获取失败",
            icon: "none",
          });
        }
      });
    },
    newAdd() {
      // this.$navigateTo("/report/report_client?currentTel=1");
      uni.switchTab({
        url: "/index/message",
      });
    },
    onClickCate(e) {
      this.params.type = Number(e.value);
      this.params.page = 1;
      this.params.status = this.params.type;
      this.useInit();
    },
    followStatus(status, id) {
      this.$navigateTo(`/project_broker/status_step?id=${id}&status=${status}`);
    },
    onInput(e) {
      this.$debounce(this.onInputDebounce, 500)(e);
    }, // 选择开始时间
    onInputDebounce(e) {
      let params = {
        page: 1,
        customer_phone: e,
        status: this.params.type,
        project_user_id: this.params.project_user_id,
      };
      if (params.status === -1) {
        delete params.status;
      } else {
        params.status = this.params.type;
      }
      this.load_status = "loading";
      this.$ajax.get(
        "/client/customer/reported/search/project",
        params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.customer_list = res.data.data;
            this.statistics = res.data.statistics;
            this.init();
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
              uni.showToast({
                title: "没有更多数据了",
                icon: "none",
              });
            }
          }
        }
      );
    },
    confirmTimeStart(e) {
      this.deal_at_date_start = e.result;
    },
    //选择结束时间
    confirmTimeEnd(e) {
      this.deal_at_date_end = e.result;
    },
    searchTime() {
      if (!this.deal_at_date_start) {
        uni.showToast({
          title: "请选择开始日期",
          icon: "none",
        });
        return;
      } else {
        if (this.deal_at_date_start && this.deal_at_date_end) {
          this.url = `/client/customer/reported/search/project?updated_date[start]=${this.deal_at_date_start}&updated_date[end]=${this.deal_at_date_end}`;
        }
      }
      this.params.status = -1;
      this.init();
      this.getDataList();
    },
    sendMsg(item) {
      this.getImToken();
      this.$navigateTo(`/im_list/msg_detail?to_id=${item.u_id}`);
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.useInit();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.useInit();
  },
};
</script>

<style scoped lang="scss">
.icon-baobei-ic_guanyu3x1 {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22icon-baobei-ic_guanyu3x1%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M512%2056.888889a455.111111%20455.111111%200%201%201%200%20910.222222A455.111111%20455.111111%200%200%201%20512%2056.888889z%20m0%20113.777778a341.333333%20341.333333%200%201%200%200%20682.666666A341.333333%20341.333333%200%200%200%20512%20170.666667z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M512%20341.333333m-56.888889%200a56.888889%2056.888889%200%201%200%20113.777778%200%2056.888889%2056.888889%200%201%200-113.777778%200Z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M455.111111%20455.111111h113.777778v284.444445H455.111111z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
page {
  background: #eee;
}
.list {
  .time-box {
    background: #fff;
    padding: 0 48rpx;
    line-height: 100rpx;
    height: 100rpx;
    justify-content: space-between;
    border-top: 1rpx solid #eee;
    .input-box {
      width: 250px;
      margin: 20rpx 0;
      border-radius: 4px;
      align-items: center;
      background-color: #eee;
      padding: 0 16rpx;
      input {
        font-size: 28rpx;
        text-align: center;
        background-color: #eee;
        border: 1rpx solid #f3f3f3;
        height: 32rpx;
        width: 284rpx;
        border: none;
      }
    }
    .search {
      color: #0174ff;
    }
  }
  .title-bar {
    background: #fff;
  }
  .search-tab {
    background: #fff;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 9;
  }
  // 导航
  .xinzeng {
    position: absolute;
    right: 48rpx;
    color: #0174ff;
  }
  .unibadge {
    position: absolute;
    right: 20rpx;
    top: 10rpx;
  }
  .screen-tab {
    flex-direction: row;
    width: 100%;
    height: 80rpx;
    background-color: #fff;
    .screen-tab-item {
      flex: 1;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      text {
        margin-right: 10rpx;
        transition: 0.3s;
      }
    }
  }
  .title-tips {
    align-items: center;
    padding: 24rpx 48rpx;
    color: #0174ff;
    background: #fff;
    margin-top: 180rpx;
    text {
      margin-left: 8rpx;
    }
  }
}
</style>
