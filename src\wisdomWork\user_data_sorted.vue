<template>
    <view>
        <view class="top">
            <view class="filters">
                <view class="filters-group">
                    <wisdomDepartmentSelect  class="filters-select" placeholder="按部门" v-model="params.department_id" @select="search"></wisdomDepartmentSelect>
                    <view class="line"></view>
                    <view class="search-input">
                        <input type="text" confirm-type="search"  placeholder="请输入搜索内容" v-model="params.user_name" @confirm="search"/>
                    </view>
                </view>
                <view class="filters-right">
                    <wisdomMemberSelect placeholder="录入人" v-model="params.admin_id" @select="search"></wisdomMemberSelect>
                </view>
            </view>
        </view>

        <wisdomTable :api="getList" :headers="headers" top="144rpx" ref="table" summary>
           <template #default="{ row }">
                <template v-if="row.field == 'user_name'">
                    <view class="user-info">
                        <view class="name">{{ row.value }}</view>
                        <view class="department" v-if="row.info.department">{{ row.info.department }}</view>
                    </view>
                </template>
                <template v-else>{{ row.value }}</template>
            </template>
        </wisdomTable>
    </view>
</template>

<script>
import wisdomDepartmentSelect from './components/wisdomDepartmentSelect';
import wisdomMemberSelect from './components/wisdomMemberSelect';
import wisdomTable from './components/wisdomTable';
import { getSortedUserData } from '@/common/utils/wisdom-work.js'
export default {
    components: {
        wisdomDepartmentSelect,
        wisdomMemberSelect,
        wisdomTable
    },
    data(){
        return {
            params: {
                date_value: '',
                department_id: '',
                admin_type: 1,
                admin_id: '',
                user_name: ''
            },
            headers: [],
            list: [],
        }
    },
    onLoad(options){
        this.params.date_value = options.date || '';
    },
    methods: {
        async getList(page){
            const [start_date, end_date ] = this.$Utils.getDateRange(this.params.date_value);
            const params = { ...this.params, start_date, end_date, page };
            delete params.date_value;

            const res = await getSortedUserData(params);

            //if(this.headers.length == 0){
            if(page == 1){
                this.headers = (res.header || []).map((e, index) => {
                    return {
                        label: e.name,
                        field: e.field,
                        width: e.web_width || 120,
                        fixed: index == 0
                    }
                })
            }
            //}
            
            return {
                list: (res.data || []),
                pageSize: res.per_page || 0
            }
        },
        async search(){
            await this.$refs.table.search();
        }
    },
    async onPullDownRefresh(){
		await this.search();
		uni.stopPullDownRefresh();
	},
	onReachBottom () {
		this.$refs.table.getList();
	},
}
</script>

<style lang="scss" scoped>
.top{
    position: sticky;
    z-index: 11;
    top: 0;
    background-color: #fff;
}
.filters{
    display: flex;
    flex-direction: row;
    padding: 32rpx;
    align-items: center;
    .filters-group{
        flex: 1;
        flex-direction: row;
        align-items: center;
        height: 80rpx;
        padding: 0 24rpx;
        background-color: #F7F8FA;
        border-radius: 16rpx;
        .filters-select{
            max-width: 160rpx;
        }
        .line{
            width: 2px;
            height: 28rpx;
            margin: 0 36rpx;
            background-color: rgba(41, 44, 57, 0.2);
        }
        .search-input{
            flex: 1;
            input{
                width: 100%;
            }
        }
    }
    .filters-right{
        max-width: 150rpx;
        margin-left: 32rpx;
    }
}

::v-deep{
    .input-placeholder{
        color: rgba(41, 44, 57, 0.4);
        font-size: 28rpx;
    }
}

.user-info{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 0 24rpx;
    width: 100%;
    .name{
        font-weight: 600;
        font-size: 32rpx;
    }
    .department{
        font-size: 24rpx;
        color: rgba(41, 44, 57, 0.7);
        margin-top: 10rpx;
    }
    .name,.department{
        display: inline-block;
        width: 100%;
        text-align: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
</style>