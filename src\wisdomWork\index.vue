<template>
    <view>
        <view class="panes">
            <view class="panes-item" v-for="(item, index) in panes" :key="index" :style="{background: item.bg}" @click="goPath(item.path)">
                <view class="content">
                    <view class="title" :style="{color: item.color}">{{item.title}}</view>
                    <view class="desc" v-if="item.desc">{{item.desc}}</view>
                </view>
                <view class="icon">
                    <image :src="item.icon | imgDomain" mode="widthFix"/>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            panes: [
                { title: '个人数据简报', desc: '', path: '/wisdomWork/personal', icon: '/icons/personal-data.png', color: '#488AF6', bg: 'linear-gradient(90deg, #EDF6FF 0%, #DCECFE 100%)' },
                { title: '团队数据简报', desc: '', path: '/wisdomWork/team', icon: '/icons/team-data.png', color: '#FF6937', bg: 'linear-gradient(90deg, #FFF3EC 0%, #FFEDE4 100%)' },
                { title: '成员数据统计', desc: '', path: '/wisdomWork/user_data', icon: '/icons/wisdom-work/user-data.png', color: '#14C9C9', bg: 'linear-gradient(90deg, #E0FBFB 0%, #AEFBFB 100%)' },
                { title: '外呼数据看板', desc: '', path: '/wisdomWork/outbound_call', icon: '/icons/wisdom-work/outbound-call.png', color: '#13C95D', bg: 'linear-gradient(90deg, #EDFFEA 0%, #E0FADD 100%)' },
            ]
        }
    },
    onLoad(){
        if (this.$isWxWork() == "wxwork") {
            this.token = uni.getStorageSync("wxwork_token");
            if (!this.token) {
                return;
            }
        } else {
            let website_id = uni.getStorageSync("website_id");
            this.token = uni.getStorageSync("token" + website_id);
            if (!this.token) {
                localStorage.setItem("backUrl", location.href);
                location.href = "https://yun.tfcs.cn";
                return;
            }
        }
    },
    methods: {
        goPath(path){
            this.$navigateTo(path)
        }
    }
}
</script>

<style lang="scss" scoped>
.panes{
    padding: 0 32rpx;
    .panes-item{
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-top: 32rpx;
        border-radius: 16rpx;
        padding: 32rpx 48rpx;
        .content{
            flex: 1;
            line-height: 1.2;
            .title{
                color: #488AF6;
                font-size: 40rpx;
            }
            .desc{
                color: #86909C;
                font-size: 14rpx;
                font-weight: 400;
                margin-top: 22rpx;
            }
        }
        .icon{
            padding: 0 16rpx;
            image{
                width: 172rpx;
                height: 172rpx;
            }
        }
    }
}
</style>