<template>
  <view style="padding:30px">
    tips: 点击living_id获取living_code然后点击进入直播间
    <view
      style="margin:40rpx 0"
      class=""
      @click="onLive(last_living.living_id)"
    >
      {{ "living_id：" + last_living.living_id }}
    </view>

    <!-- 样式类的话貌似只能在style的内联样式或行内样式才生效 -->
    <wx-open-launch-weapp
      id="launch-btn"
      @launch="handleLaunch"
      @error="handleError"
      username="gh_25e071b83ee0"
      :path="url"
    >
      <script type="text/wxtag-template">
        <button class="btn">打开直播</button>
      </script>
    </wx-open-launch-weapp>
  </view>
</template>

<script>
export default {
  data() {
    return {
      last_living: {},
      living_code: "",
      url: "",
    };
  },
  onLoad() {
    this.getLConfig();
  },
  methods: {
    // 获取最后一次直播记录
    getLastLiving() {
      this.$ajax.get("/client/wx_work/living/get/last", {}, (res) => {
        if (res.statusCode === 200) {
          this.last_living = res.data;
        }
      });
    },
    onLive(id) {
      this.$ajax.post(
        "/client/wx_work/living/get/code",
        {
          living_id: id,
        },
        (res) => {
          if (res.statusCode === 200) {
            this.url = `pages/watch/index?living_code=${res.data.living_code}`;
            uni.showToast({
              title: this.url,
              icon: "none",
            });
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        }
      );
    },
    getLConfig() {
      let url;
      url = window.location.href;
      let params = { url };
      params.website_id = uni.getStorageSync("website_id");
      this.$ajax.get("/common/wx_open/query/js_sdk/wx_conf", params, (res) => {
        if (res.statusCode === 200) {
          this.getLastLiving();
          res.data.jsApiList = ["wx-open-launch-weapp"];
          res.data.openTagList = ["wx-open-launch-weapp"]; // 跳转小程序时必填
          res.data.debug = false;
          this.$wx.config(res.data);
          this.$wx.ready(() => {
            this.$nextTick(() => {
              let btn = document.getElementById("launch-btn");
              alert("btn:" + JSON.stringify(btn));
              btn.addEventListener("launch", (e) => {
                alert("launch" + JSON.stringify(e));
                console.log("success");
              });
              btn.addEventListener("error", (e) => {
                alert("error" + JSON.stringify(e));
                alert("小程序打开失败");
                console.log("fail", e.detail);
              });
            });
            this.$wx.error((res) => {
              alert("$wxerror" + JSON.stringify(res));
              // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名
              console.log(res);
            });
          });
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },

    // 监听跳转
    handleLaunch(res) {
      uni.showToast({
        title: JSON.stringify(res),
        icon: "none",
      });
    },
    // 监听错误
    handleError(err) {
      uni.showToast({
        title: JSON.stringify(err),
        icon: "none",
      });
    },
  },
};
</script>

<style></style>
