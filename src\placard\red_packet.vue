<template>
    <view class="Mask">
        <view class="background">
            <view class="backcloth">
                <view class="openred">
                    <view class="opentop">
                        <text style="margin-top: 50rpx;font-size: 36rpx;">活动奖金池</text>
                        <text>———— 金额：4.24 ————</text>
                        <image src="./photo/redbg.png"></image>
                    </view>
                    <view class="opencenter">
                        <text>
                            活动倒计时：22天08时58分
                        </text>
                        <text style="font-size: 100rpx;">
                            1.00
                        </text>
                        <text style="margin-top: -50rpx;margin-left: 250rpx;font-size: 28rpx;">元</text>
                        <text>已存入微信领钱</text>
                    </view>
                    <view class="opencen">
                        <view>
                            <image src="./photo/red_envelopes.png"></image>
                            <text class="read">
                                阅读红包
                            </text>
                            <text class="open">
                                (打开可得)
                            </text>
                            <text class="highest">
                                最高3.8元
                            </text>
                        </view>
                        <view>
                            <image src="./photo/red_packet.png" style="margin-left: 33rpx;"></image>
                            <text style="margin-left: 33rpx;">
                                分享红包
                            </text>
                            <text class="open">
                                (分享新用户可得)
                            </text>
                            <text class="highest" style="margin-left: 40rpx;">
                                最高88元
                            </text>
                        </view>
                        <view>
                            <image src="./photo/or_kickback.png"></image>
                            <text class="read">
                                榜单奖励
                            </text>
                            <text class="open" style="margin-left: -15rpx;">
                                (排行榜奖金)
                            </text>
                            <text class="highest">
                                暂未设置
                            </text>
                        </view>
                    </view>
                    <view class="openfoot">
                        <view>
                            <text>分享红包，转发给有效区域好友，且对方首次打
                                开可得</text>
                        </view>
                    </view>
                </view>

            </view>
        </view>
        <view class="foot">
            <view class="foot_left">
                <view class="publicity_Icon">
                    <view class="publicity">
                        <image src="./photo/publicity.png"></image>
                    </view>
                    <text>关注</text>
                    <view class="publicity">
                        <image src="./photo/billboard.png"></image>
                    </view>
                    <text>榜单</text>
                    <view class="publicity">
                        <image src="./photo/share.png"></image>
                    </view>
                    <text>分享</text>
                    <view class="publicity">
                        <image src="./photo/billfold.png"></image>
                    </view>
                    <text>钱包</text>
                </view>
            </view>
            <view class="foot_right">
                <button type="primary">拨打电话</button>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {

        }
    },
}
</script>
<style scoped lang="scss">
.Mask {
    width: 100%;
    height: 1540rpx;
    background-color: rgba(0, 0, 0, 0.6);
    position: fixed;

    .background {
        width: 100%;
        height: 1350rpx;
        background-image: url(./photo/picture.png);
        background-repeat: no-repeat;
        background-size: cover;

        .backcloth {
            width: 100%;
            // height: 1000rpx;
            height: 1350rpx;
            background-color: rgba(0, 0, 0, 0.6);
            position: relative;

            .openred {
                width: 80%;
                height: 1000rpx;
                margin: 0 auto;
                margin-top: 200rpx;
                background: #ffffff;
                border-radius: 10rpx;

                .opentop {
                    width: 100%;
                    height: 250rpx;
                    // background-color: palevioletred;
                    position: relative;

                    text {
                        color: #E5CD9F;
                        z-index: 99;
                        text-align: center;
                        line-height: 50rpx;
                    }

                    image {
                        width: 100%;
                        height: 250rpx;
                        position: absolute;
                    }
                }

                .opencenter {
                    width: 100%;
                    height: 350rpx;
                    // background-color: plum;
                    border: 1px solid #fff;
                    border-bottom-color: #e5e6e6;

                    text {
                        text-align: center;
                        color: #E5CD9F;
                        margin-top: 40rpx;
                    }

                }

                .opencen {
                    width: 100%;
                    height: 250rpx;
                    // background-color: plum;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-around;

                    image {
                        margin-top: 20rpx;
                        width: 120rpx;
                        height: 120rpx;
                    }

                    .open {
                        font-size: 20rpx;
                        color: #858a8e;
                        text-align: center;
                        margin-top: 20rpx;
                    }

                    .highest {
                        margin-top: 20rpx;
                        font-size: 14px;
                        color: #ff0000;
                    }
                }

                .openfoot {
                    width: 100%;
                    height: 150rpx;

                    // background-color: powderblue;
                    view {
                        width: 90%;
                        height: 100rpx;
                        background-color: #FBDAB1;
                        margin: 20rpx auto;
                        border-radius: 10rpx;
                        padding: 20rpx;
                        font-size: 20rpx;
                        color: #7B360F;
                    }
                }
            }
        }

        // image {
        //     width: 100%;
        //     height: 1350rpx;
        // }
    }

    .foot {
        width: 100%;
        height: 190rpx;
        // background-color: rgb(27, 198, 198);
        display: flex;
        flex-wrap: wrap;

        .foot_left {
            width: 50%;
            height: 190rpx;
            // background-color: crimson;

            .publicity_Icon {
                width: 320rpx;
                height: 100rpx;
                // background-color: chocolate;
                margin: 0 auto;
                display: flex;
                flex-wrap: wrap;
                align-content: space-between;
                margin-top: 45rpx;

                text {
                    color: #324157;
                    font-size: 11px;
                    margin-left: 10rpx;
                    margin-top: 10rpx;
                }
            }

            .publicity {
                width: 60rpx;
                height: 60rpx;

                // border: 1px dashed #7c8084;
                // background-color: aquamarine;
                image {
                    width: 100%;
                    height: 100%;
                }
            }
        }

        .foot_right {
            width: 50%;
            height: 190rpx;

            // background-color: rgb(50, 220, 20);
            button {
                width: 300rpx;
                height: 100rpx;
                margin: 50rpx auto;
            }
        }
    }
}
</style>