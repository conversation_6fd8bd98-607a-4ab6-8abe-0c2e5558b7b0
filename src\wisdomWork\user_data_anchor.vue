<template>
    <view>
        <wisdomNavs :datas="navs" v-model="navName"></wisdomNavs>

        <keep-alive>
            <component :is="navName" ref="comp" :date-value="params.date_value"></component>
        </keep-alive>
    </view>
</template>

<script>
import wisdomNavs from './components/wisdomNavs';
import wisdomUserDataAnchorAccount from './components/wisdomUserDataAnchorAccount';
import wisdomUserDataAnchorGuest from './components/wisdomUserDataAnchorGuest';
export default {
    components: {
        wisdomNavs,
        wisdomUserDataAnchorAccount,
        wisdomUserDataAnchorGuest,
    },
    data(){
        return {
            navName: 'wisdomUserDataAnchorAccount',
            navs: [
                { label: '主播账号', name: 'wisdomUserDataAnchorAccount' },
                { label: '客资分布', name: 'wisdomUserDataAnchorGuest' },
            ],
            params: {
                date_value: ''
            }
        }
    },
    onLoad(options){
        this.params.date_value = options.date || '';
    },
    methods: {
        loadData(){
            this.$refs.comp.loadData()
        },
        async search(){
            await this.$refs.comp.search();
        }
    },
    async onPullDownRefresh(){
		await this.search();
		uni.stopPullDownRefresh();
	},
	onReachBottom () {
		this.loadData();
	},
}
</script>