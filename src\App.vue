<script>
import { mapState, mapMutations } from "vuex";
// #ifdef H5
import jumpToPC from "@/common/utils/jump-to-pc.js"
// #endif
// import VConsole from "vconsole";
export default {
  globalData: {
    website_id: "1",
    dictionary_data: [],
    region_0: 0,
    region_1: 0,
  },
  computed: {
    ...mapState(["unread_msg", "user_info", "qqmapkey"]),
  },
  onLaunch: function (options) {
    //webview
    uni.removeStorageSync('wxwork_headerFrom')
    if(options?.query?.website_id && options.query.headerFrom && options.query.token){
      uni.setStorageSync("website_id", options.query.website_id);
      uni.setStorageSync("wxwork_id", options.query.website_id)
      uni.setStorageSync('wxwork_headerFrom', options.query.headerFrom)
      uni.setStorageSync('wxwork_token', options.query.token)
      uni.setStorageSync('token'+options.query.website_id, options.query.token)
    }
    // #ifdef H5
    if(jumpToPC()){
      return;
    }
    // #endif

    //  修复切换公司不更换website_id 
    if (["wxwork", "com-wx-pc"].includes(this.$isWxWork())) {
      let isChange = sessionStorage.getItem("isChange")
      if(!isChange){
        if(options?.query?.website_id && options.query.website_id == uni.getStorageSync("wxwork_id")){
          isChange = 1;
        }
      }
      if (!isChange) {
        uni.removeStorageSync("wxwork_token")
        sessionStorage.setItem("isChange", "1")
      }
    }
    if(options.website_id){
      uni.setStorageSync('website_id'+options.website_id,options.website_id)
      uni.setStorageSync('wxwork_id',options.website_id)
    }
    if(options.token){
      uni.setStorageSync("token"+uni.getStorageSync('wxwork_id'+options.website_id),options.token)
      uni.setStorageSync("wxwork_token",options.token)
    }
    // uni.removeStorageSync("wxwork_token")
    // 刷新页面vuex数据丢失问题
    // 全局监听，页面刷新时将store存在本地存储中，然后在赋值给store，再把本地存储数据删除即可
    // if (sessionStorage.getItem("store")) {
    //   this.$store.replaceState(
    //     Object.assign(
    //       {},
    //       this.$store.state,
    //       JSON.parse(sessionStorage.getItem("store"))
    //     )
    //   );
    //   console.log(this.$store.state);
    //   sessionStorage.removeItem("store");
    // }
    // // 页面刷新时将vuex的信息保存在本地存储中
    // window.addEventListener("beforeunload", () => {
    //   sessionStorage.setItem("store", JSON.stringify(this.$store.state));
    // });
    // var params = this.queryUrlParams(window.location.href);
    // if (options.query.website_id == 176) {
    //   console.log(JSON.stringify(options.query));
    // }
    // if (options.query.website_id) {
    //   if (options.query.website_id != uni.getStorageSync('website_id')) {
    //     if (options.query.website_id == 176) {
    //       alert(uni.getStorageSync('website_id'))
    //     }
    //     uni.setStorageSync('website_id', options.query.website_id)
    //     uni.removeStorageSync("wxwork_token")
    //   }
    // }


    uni.hideTabBar(); // 隐藏官方tabbar
    // new VConsole();
    var ua = window.navigator.userAgent.toLowerCase(); // 判断当前环境
    if (ua.match(/Mobile/i)) {
      this.$store.state.in_mobile = true;
    } else {
      this.$store.state.in_mobile = false;
    }
    // 模板消息 带参数souce_from  pc端跳转到pc页面
    if (!this.$store.state.in_mobile) {
      if (options.path && options.path.indexOf("customer/detail") > -1 && options.query.souce_from == 1) {
        let url = `https://yun.tfcs.cn/admin/#/crm_customer_detail?id=${options.query.id}&type=seas&website_id=${options.query.website_id}`
        window.location.replace(url)
        return;
      }
    }

    if (options.query.website_id) {
      uni.setStorageSync(
        "website_id" + options.query.website_id || 1,
        options.query.website_id || 1
      );
      uni.setStorageSync(
        "wxwork_id",
        options.query.website_id || 1
      );
      this.globalData.website_id = options.query.website_id;

      // 往vuex存数据
    }
    this.$store.commit("setWebsiteid", options.query.website_id || 1);

    if (uni.getStorageSync("token" + this.$store.state.website_id)) {
      if (options.path === "im_list/msg_detail") {
        return;
      }
    }
  },
  onShow (options) {
    // console.log("地址栏参数：" + options.query);
    // 企微授权

    if (["wxwork", "com-wx-pc"].includes(this.$isWxWork())) {
      // if (options.query.website_id != uni.getStorageSync("website_id")) {

      // }
      if (options.query.code && options.query.type) {
        //   // 获取到code换取token
        // this.setWxworkCode(options.query.code, options.query.website_id)
        // let token = uni.getStorageSync("token" + options.query.website_id);
        // let token = uni.getStorageSync("wxwork_token");
        // if (token) return;
        let type = options.query.type

        this.setAuthCode(options.query.code, type, options.query.website_id);
      } else {
        // 如果未登录
        this.getSelfUrl();
        // 企微授权未获取到code获取code
        // this.setWxworkOauth(options.query.website_id);
      }
    } else {
      // this.getUnReadMsg();
      if (!this.$store.state.is_setting.is_enable) {
        this.getSetting(options.query.website_id);
      }
    }
  },
  onLoad () {

  },
  methods: {
    ...mapMutations(["setUnreadMsg"]),
    // 获取地址参数方法
    queryUrlParams (url) {
      let result = {},
        reg1 = /([^?=&#]+)=([^?=&#]+)/g,
        reg2 = /#([^?=&#]+)/g;
      url.replace(reg1, (n, x, y) => (result[x] = y));
      url.replace(reg2, (n, x) => (result["HASH"] = x));
      return result;
    },
    setWxworkOauth (website_id) {
      let token = uni.getStorageSync("token" + website_id);
      let pathname = window.location.pathname;
      let url = window.location.href;
      // if (pathname === "/fenxiao/customer/mark" && !token) {
      if (pathname.indexOf("/fenxiao/customer") != -1 && !token) {
        this.$ajax.get(
          "/auth/qywx/authorize",
          { redirect_uri: url, website_id: website_id },
          (res) => {
            if (res.statusCode === 200) {
              window.location.href = res.data;
            } else {
              uni.showToast({
                title: res.data.message,
                icon: "none",
              });
            }
          }
        );
      }
    },
    getSelfUrl () {
      let token = uni.getStorageSync("wxwork_token");
      let pathname = window.location.pathname;
      let url = window.location.href.indexOf("?") > -1 ? (window.location.href.indexOf("type=3") == -1 ? window.location.href + "&type=3" : window.location.href) : window.location.href + "?type=3";
      if (pathname.indexOf("/fenxiao/customer") != -1 && !token) {
        this.$ajax.get(
          "/common/qywx/thirdparty_t/authorize",
          {
            redirect_uri: url,
          },
          (res) => {
            if (res.statusCode === 200) {
              window.location.replace(res.data);
              // console.log(res.data);
            }
          }
        );
      }
    },
    // 企业微信登录
    setWxworkCode (code, id) {
      this.$ajax.post(
        "/auth/qywx/login",
        { website_id: id, code: code },
        (res) => {
          if (res.statusCode === 200) {
            // uni.showToast({
            //   title: "登录成功",
            //   icon: "none",
            // });
            uni.setStorageSync("token" + id, res.data.token);
            // setTimeout(() => {
            //   window.location.href(
            //     window.location.origin +
            //       window.location.pathname +
            //       "?website_id=" +
            //       id
            //   );
            // }, 500);
            window.location.href =
              window.location.origin +
              window.location.pathname +
              "?website_id=" +
              id;
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        }
      );
    },
    getSetting (website_id) {
      // 获取站点套餐的内容开启情况
      // console.log(uni.getExtConfigSync().website_id);
      this.$store.state.is_setting.is_enable = true;
      this.$ajax.get(`/common/website/private_config/${website_id}`, {},
        (res) => {
          if (res.statusCode === 200) {
            if (res.data.config_support_online_live == 1) {
              this.$store.state.is_setting.config_support_online_live = true;
            }
            if (res.data.config_support_reported == 1) {
              this.$store.state.is_setting.config_support_reported = true;
            }
            if (res.data.config_support_im == 1) {
              this.$store.state.is_setting.config_support_im = true;
            }
          } else {
            this.$store.state.is_setting.is_enable = false;
          }
        }
      );
    },
    getUnReadMsg () {
      if (!uni.getStorageSync("token" + this.$store.state.website_id)) {
        return;
      }
      this.$ajax.get("/client/im/session/unread_total", {}, (res) => {
        if (res.statusCode === 200) {
          this.setUnreadMsg(res.data.total);
          if (res.data.total > 0) {
            uni.setTabBarBadge({
              index: 2,
              text: res.data.total + "",
            });
          } else {
            uni.removeTabBarBadge({
              index: 2,
            });
          }
        }
      });
    },
    setAuthCode (code, type, website_id) {
      var url;
      if (type == 1) {
        // 第三方报备应用登录授权
        url = `/common/qywx/thirdparty/login?code=${code}`;
      }
      if (type == 2) {
        // 自建应用代开发登录授权
        url = `/common/qywx/self/login?code=${code}&website_id=${website_id}`;
      }
      if (type == 3) {
        // 第三方T+系统登录授权
        url = `/common/qywx/thirdparty_t/login?code=${code}`;
      }
      this.$ajax.get(url, {}, (res) => {
        if (res.statusCode === 200) {
          this.$store.commit("setWebsiteid", res.data.website_id);
          uni.setStorageSync("token" + res.data.website_id, res.data.token);
          uni.setStorageSync("auth_way", res.data.auth_way || 0);
          // 存储toekn用于区分企业微信管理员和前端用户
          uni.setStorageSync("wxwork_token", res.data.token);
          uni.setStorageSync("website_id" + res.data.website_id, res.data.website_id)
          uni.setStorageSync("wxwork_id", res.data.website_id);
          this.getSetting(res.data.website_id);
          // 登录成功重定向至携带站点id的路由
          let para = this.queryUrlParams(window.location.href)
          let url = window.location.origin + window.location.pathname + "?";
          para.website_id = res.data.website_id;
          for (const key in para) {
            if (Object.hasOwnProperty.call(para, key)) {
              const element = para[key];
              if (key != 'code' && key != "STATE" && key != 'state') {
                url += `${key}=${element}&`
              }
            }
          }
          url = url.substring(0, url.length - 1)
          uni.setStorageSync("isLoadEnd", 1)
          location.replace(url)

          // this.authWxworkLogin((cal) => {
          //   if (cal === 200) {
          //     let url = window.location.origin + window.location.pathname;
          //     // window.location.href = url;
          //   }
          // });
        } else {
          console.log("url:", res.data.message);
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    // 验证企业客户是否可以在企业微信登录
    authWxworkLogin (callback) {
      this.$ajax.get("/admin/role/isAllowQywxLogin", {}, (res) => {
        if (res.statusCode === 200) {
          callback(200);
        } else {
          console.log("/admin/role/isAllowQywxLogin", res.data.message);
          // uni.showToast({
          //   title: res.data.message,
          //   icon: "none",
          // });
        }
      });
    },
  },
  // onShow: function() {},
  // onHide: function() {
  //   console.log("App Hide");
  // },
};
</script>

<style lang="scss">
/*  #ifdef  H5  */
body {
  // max-width: 828rpx; //最大宽度自己可以调整
  // margin: auto !important;
}
/*  #endif  */
[type='search']::-webkit-search-decoration {
  display: none;
}
.uni-tabbar-border {
  background-color: #fff !important;
}

.icon-baobei-css {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

.icon-baobei {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

.baobeo-icon {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}
uni-radio .uni-radio-input {
  border-radius: 8rpx;
}
.remind .uni-radio-input {
  border-radius: 50%;
  width: 30rpx;
  height: 30rpx;
}
.add_raido .uni-radio-input {
  width: 32rpx;
  height: 32rpx;
}
.remind .uni-radio-input.uni-radio-input-checked:before {
  font-size: 20rpx;
}
.remind .add_raido .uni-radio-input.uni-radio-input-checked:before {
  font-size: 24rpx;
}

// vue-picker
.vue-picker {
  .header {
    height: 88rpx;
    background: #f5f5f5;
    .title {
      font-size: 22rpx !important;
      color: #999;
    }
    .right {
      .btn {
        font-size: 16px;
        color: #0174ff;
      }
    }
  }
}
/*
checkbox样式
*/

/*自定义样式*/
/* reg	  */

uni-checkbox-group {
  width: 100% !important;
}
uni-checkbox-group uni-label {
  width: 33% !important;
  display: inline-flex;
  margin-bottom: 20rpx;
}
/*未选中*/
uni-checkbox:not([disabled]) .uni-checkbox-input:hover {
  border-color: #999;
}
/*checkbox 选项框大小  */
uni-checkbox .uni-checkbox-input {
  width: 44rpx !important;
  height: 44rpx !important;
  border: 1rpx solid #999;
}
/*checkbox选中后样式  */
uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked {
  background-color: rgb(0, 122, 255);
  border-color: rgb(0, 122, 255);
}
/*checkbox选中后图标样式  */
uni-checkbox .uni-checkbox-input.uni-checkbox-input-checked::before {
  width: 20rpx;
  height: 20rpx;
  text-align: center;
  padding-left: 4rpx;
  font-size: 18rpx;
  color: #fff;
  background: transparent;
  transform: translate(-70%, -50%) scale(1);
  -webkit-transform: translate(-70%, -50%) scale(1);
}

.select-list {
  uni-swiper {
    height: 100%;
    line-height: 88rpx;
  }
  .uni-swiper-slides {
    top: 176rpx !important;
  }
  uni-swiper-item {
    height: 88rpx !important;
  }
  .uni-swiper-slide-frame {
    height: 88rpx !important;
  }
}
/*每个页面公共css */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/*隐藏滚动条*/
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
body {
  font-size: $uni-font-size-base;
  color: $uni-text-color;
  line-height: 1;
}
view {
  font-family: PingFangSC-Medium;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  &.row {
    flex-direction: row;
  }
}
.active {
  color: $uni-color-primary;
}
.bottom-line,
.top-line,
.left-line,
.right-line {
  position: relative;
}
.bottom-line:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: #f5f5f5;
}
.top-line:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: #f5f5f5;
}
.left-line:before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 1px;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
  background-color: #f5f5f5;
}
.right-line:after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 1px;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
  background-color: #f5f5f5;
}
.on-hover {
  background-color: $uni-bg-color-hover;
}
.text-center {
  text-align: center;
}
.mgb-20 {
  margin-bottom: 20rpx;
}
.article-content {
  padding: 24rpx 40rpx;
  line-height: 1.6;
  font-size: 32rpx;
  font-family: -apple-system-font, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC',
    'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei', Arial, sans-serif;
  letter-spacing: 0.544px;
  white-space: normal;
  p {
    min-height: 1em;
    margin-bottom: 30rpx;
  }
  img {
    max-width: 100%;
  }
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-box {
  display: flex;
  flex-direction: column;
}
.flex-1 {
  flex: 1;
}

.flex-2 {
  flex: 2;
}
.flex-3 {
  flex: 3;
}
.flex-4 {
  flex: 4;
}
.bottom-line {
  position: relative;
}
.bottom-line:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: $border-color;
}

.items-center {
  align-items: center;
}
.items-end {
  align-items: flex-end;
}
.flex-wrap {
  flex-wrap: wrap;
}
.justify-center {
  justify-content: center;
}
.space-between {
  justify-content: space-between;
}
</style>
