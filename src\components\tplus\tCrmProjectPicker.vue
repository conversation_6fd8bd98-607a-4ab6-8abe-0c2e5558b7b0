<template>
<myPopup :show="show"  @close="cancle">
    <view class="filter-wrapper">
    <view class="header">
            <view class="action cancle" @click.stop="cancle">取消</view>
            <view class="title"></view>
            <view class="action confirm" @click.stop="confirm">确认</view>
        </view>
        <view class="body">
            <tCustomPickerView v-model="selectedId" :datas="list" ref="picker" v-if="show" :multiple="multiple"
                :multipleLimit="multipleLimitNum" @exceed="handleSeledExceed" remote @filter="onFilter"
                filter-placeholder="请搜索并选择项目"
                filterable :map="{label: 'name', value: 'id'}" :loading="loading">
                <template #empty v-if="isEmpty && projectType == 2">
                    <!-- <view class="empty-text">
                        <view class="add-btn">
                            没找到？申请添加
                            <uni-icons type="plusempty" size="14" color="#488AF6" class="icon"></uni-icons>
                        </view>
                    </view> -->
                </template>
            </tCustomPickerView>
        </view>
    </view>
</myPopup>
</template>

<script>
import myPopup from '@/components/myPopup';
import tCustomPickerView from '@/components/tplus/tCustomPickerView';
export default {
    components: {
        myPopup, tCustomPickerView
    },
    props: {
        visible: { type: Boolean, default: false },
        value: {type: [Array,String,Number], default: ''},
        datas: {type: Array, default: ()=>[]},
        filterable: {type: Boolean, default: true},
        remote: {type: Boolean, default: true},
        valueKey: {type: String, default: "id"},
        placeholder: {type: String, default: "请搜索并选择项目"},
        width: {type: String, default: "auto"},
        allowCreate: {type: Boolean, default: false},
        multiple: {type: Boolean, default: true},
        multipleLimit: {type: Number, default: 0},
    },
    data(){
        return {
            loading:false,
            show: false,
            isInited: false,
            isSearch: false,
            selectedId: [],
            seledProjects: [],
            list: [],
            defaultList: [],
            projectType: 0,
            isAllowCreate: false,
            multipleLimitNum: 0,
            isEmpty: false,
            searchKeyword: '',
            addCommunity: {
                show: false,
                params: {
                    title: "",
                    addr: "",
                    lat: "",
                    lng: "",
                    region_id: '',
                    region_name: ''
                }
            }
        }
    },
    computed: {
        currentProjectType(){
            return this.$store.state.crmConfigProjectType;
        }
    },
    watch: {
        selectedId(val){
            !this.multiple && (val = [val])
            this.seledProjects = this.seledProjects.filter(e => val.includes(e.id));
            let seledIds = this.seledProjects.map(e=>e.id);
            let ids = val.filter(e => !seledIds.includes(e));
            
            if(ids.length){
                this.seledProjects = this.seledProjects.concat(this.list.filter(e => ids.includes(e.id)));
            }
        },
        visible(val){
            this.show = val;
            if(val && !this.isInited){
                this.isInited = true;
                this.loadData();
            }
        },
        show(val){
            this.$emit('update:visible', val)
            if(val === false){
                if(this.isSearch){
                    this.isSearch = false;
                    this.list = [...this.defaultList];
                }
            }
        },
        datas: {
            handler(d){
                this.list = d;
            },
            immediate: true
        },
        value: {
            handler(val){
                this.selectedId = val;
            },
            immediate: true
        },
        currentProjectType: {
            handler(val){
                //默认项目为小区库：不使用自动创建，多选最多选10个
                if(val == 2){
                    this.isAllowCreate = false;
                    this.multipleLimitNum = 10;
                }else{
                    //自建项目库
                    this.isAllowCreate = this.allowCreate;
                    this.multipleLimitNum = this.multipleLimit;
                }
            },
            immediate: true
        }
    },
    created(){
        this.$store.commit('setCrmConfigProjectType', 0);
        this.projectType = this.currentProjectType;
    },
    methods: {
        handleSeledExceed(){
            uni.showToast({
                title: '最多选择'+this.multipleLimitNum+'个项目',
                icon: 'none'
            })
        },
        async queryCrmConfigProjectType(){
            await this.$store.dispatch('queryCrmConfigProjectType');
        },
        //获取菜单数据
        async loadData(keyword = ''){
            this.searchKeyword = keyword;
            
            this.loading = true;
            try{
                await this.queryCrmConfigProjectType();
                this.projectType = this.currentProjectType;
                if(this.projectType){
                    const data = await this.getCrmProjectByConfig(keyword);
                
                    this.list = data || [];
                    this.isEmpty = this.list.length == 0;
                    if(keyword === ''){
                        this.defaultList = [...this.list];
                    }else{
                        if(this.isEmpty && this.isAllowCreate){
                            this.isEmpty = false;
                            this.list = [{
                                id: keyword,
                                name: keyword
                            }]
                        }
                    }
                }
            }catch(e){
                console.error(e);
            }
            this.loading = false;
        },
        getCrmProjectByConfig(keyword){
            return new Promise((resolve, reject) => {
                this.$ajax.get('/admin/crm/config/get_project_by_config', {keyword}, res => {
                    if (res.statusCode == 200) {
                        resolve(res.data);
                    }else{
                        uni.showToast({
                            title: res?.data?.message || '获取项目列表失败',
                            icon: 'none'
                        });
                    }
                    reject();
                }, er => {
                    reject();
                })
            })
        },
        onFilter(keyword){
            this.isSearch = keyword !== '';
            this.loadData(keyword);
        },
        cancle(){
            this.show = false;
            this.selectedId = this.value;
        },
        confirm(){
            this.show = false;
            this.$emit('input', this.selectedId);
            this.$emit('confirm', this.multiple ? this.seledProjects : this.seledProjects[0] || null);
        },
        //打开添加小区页面
        openAddCommunityPage(){
            this.addCommunity.params.title = this.searchKeyword;
            this.addCommunity.show = true;
        },
        //添加小区成功回调
        onAddCommunitySuccess(e){
            const data = {...e, name: e.title};

            this.list.push(data);
            this.isEmpty = false;
            let value = this.selectVal;
            if(Array.isArray(value)){
                this.multipleLimitNum > value.length && value.push(data[this.valueKey]);
            }else{
                value = data[this.valueKey];
            }
            this.onChange(value);
            this.addCommunity.show = false;
        }
    },
}
</script>

<style scoped lang="scss">
.filter-wrapper{
    background-color: #fff;
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
    overflow: hidden;
}
.header{
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 46px;
      
    .title{
        flex: 1;
        color: #999;
        text-align: center;
        display: inline-block;
        max-width: 50%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .action{
        padding: 10px 16px;
        font-size: 17px;
        &.cancle{
            color: #888;
        }
        &.confirm{
            position: relative;
            color: #007aff;
            &.loading{
                opacity: .6;
            }
        }
    }
}

.empty-text{
    display: flex;
    flex-direction: row;
    justify-content: center;
    height: 100%;
    align-items: center;
    .add-btn{
        display: inline-block;
        text-align: center;
        padding: 30rpx;
        color: #488AF6;
        font-size: 30rpx;
        .icon{
            margin-left: 8rpx;
        }
    }
}
</style>