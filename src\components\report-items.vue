<template>
  <view class="list">
    <view class="item">
      <view class="left">
        <view class="row title-box">
          <view class="title-top">{{ report_item.build_name }}</view>
          <view class="is-receive" v-if="report_item.pu_id">已领取</view>
        </view>
        <view class="">
          <view class="client-box row">
            <view class="label">客户称呼</view>
            <view class="ctn row"
              >{{ report_item.customer_name }}
              <span
                class="is_1"
                v-if="is_case === 1 && report_item.is_dispatch === 1"
                >{{ report_item.dispatch_type }}</span
              >
              <span
                class="is_2"
                v-if="is_case === 1 && report_item.is_dispatch === 0"
                >{{ report_item.dispatch_type }}</span
              >
            </view>
          </view>
          <view class="client-box row">
            <view class="label">客户电话</view>
            <view class="ctn row"
              >{{ report_item.customer_phone
              }}<view
                class="fill-phone"
                v-if="
                  /^1[3-9]\d{1}\*\*\*\*\d{4}$/.test(
                    report_item.customer_phone
                  ) &&
                    (report_item.p_fill_customer_phone_category === 2 ||
                      report_item.p_fill_customer_phone_category === 4)
                "
                @click.stop="fillPhone"
                >补全号码</view
              ></view
            ></view
          >
          <view class="client-box row">
            <view class="label">客户性别</view>
            <view class="ctn">{{ report_item.customer_sex | formatSex }}</view>
          </view>
          <!-- <view class="client-box row">
            <view class="label">助理回复</view>
            <view class="ctn huifu" style="color:#638ff9">{{
              report_item.last_im_msg || "无回复内容"
            }}</view>
          </view> -->
          <view class="client-box row">
            <view class="label">创建时间</view>
            <view class="ctn">{{ report_item.created_at }}</view>
          </view>
          <slot name="bottom" />
        </view>
        <view
          class="client-box row"
          style="align-items: center;"
          v-if="
            (status !== 0 && report_item.pu_name && report_item.pu_phone) ||
              (status2 !== 0 && report_item.pu_name && report_item.pu_phone)
          "
        >
          <view class="label">联系案场</view>
          <view class="ctn row"
            >{{ report_item.pu_name }} <text style="width:20rpx"></text>
          </view>
          <image
            @click="callReportProject"
            class="image"
            src="../static/dianhua.png"
          ></image>
          <image
            @click="$emit('sendMsg', report_item)"
            class="image"
            src="../static/xiaoxi.png"
          ></image>
        </view>
        <view
          class="client-box row"
          v-if="
            report_item.auto_reported_valid === 1 && report_item.status === 1
          "
        >
          <view class="label">自动报备</view>
          <view class="ctn">已开启</view>
        </view>
        <view
          class="client-box row"
          v-if="
            (report_item.protected_day > 0 || report_item.protected_hour > 0) &&
              is_case !== 1
          "
        >
          <view class="label">结束时间</view>
          <view class="ctn"
            >{{ report_item.protected_expired_at }}（保护期）</view
          >
        </view>
        <view class="client-box row" v-if="report_item.customer_id_no">
          <view class="label">
            身份证号
          </view>
          <view class="ctn">{{ report_item.customer_id_no }}</view>
        </view>
        <view class="client-box row" v-if="report_item.cancel_reason">
          <view class="label">无效原因</view>
          <view :class="show_cancel ? 'ctn_show_content' : 'ctn_show'">{{
            report_item.cancel_reason
          }}</view>
          <myIcon
            @click="showCancel"
            :type="show_cancel ? 'xiala' : 'shangla'"
            size="20rpx"
            color="#bbb"
          ></myIcon>
        </view>

        <view
          class="client-box row"
          v-if="report_item.status > 0 && report_item.p_delay_visit_minute > 0"
        >
          <view class="label">到访时间</view>
          <!-- 后台延迟到访/报备保护开启 -->
          <view
            v-if="
              report_item.reported_valid_at && report_item.from_follow_up === 1
            "
            class="ctn"
          >
            {{
              $addTime(
                report_item.follow_up_time || report_item.updated_at,
                report_item.p_delay_visit_minute
              )
            }}
          </view>
          <!-- 后台延迟到访/报备保护未开启（默认） -->
          <view
            v-if="
              report_item.reported_valid_at && report_item.from_follow_up !== 1
            "
            class="ctn"
          >
            {{
              $addTime(
                report_item.reported_valid_at,
                report_item.p_delay_visit_minute
              )
            }}
          </view>
          <view
            v-else-if="!report_item.reported_valid_at"
            class="ctn"
            style="flex:1"
          >
            {{
              $addTime(
                report_item.created_at_original,
                report_item.p_delay_visit_minute
              )
            }}后进行到访确认
          </view>
        </view>
        <!-- <view class="client-box btn-box row">
          <view style="background:#0077ff" class="btn status" @click="onClick"
            >跟进</view
          >
          <view
            v-if="is_case === 1"
            class="btn status"
            @click="followStatu(report_item.status, report_item.id)"
            >设为无效</view
          >
        </view> -->
        <!-- <view
          class="client-box row"
          v-if="
            report_item.customer_attached_phone !== '[]' &&
              report_item.customer_attached_phone !== null
          "
        >
          <view class="label">其他客户</view>
          <view
            class="content"
            style="color:#00ad65"
            @click="clickMoreCustomer(report_item.customer_attached_phone)"
            >点击查看</view
          >
        </view> -->
      </view>
      <view class="right">
        <!-- <view
          class="mark"
          :class="{
            report: status == 0 || status2 == 0,
            visite: status == 1 || status2 == 1,
            subscribe: status == 2 || status2 == 2,
            isBuy: status == 3 || status2 == 3,
            isbuy4: status == 4 || status2 == 4,
            isDeal: status == 5 || status2 == 5,
            failure: status == 10 || status2 == 10,
          }"
          >{{ filterText(status) || filterText2(status2) }}</view
        >
        <view
          class="icon-box"
          @click="clickCode"
          v-if="status !== 0 && is_case !== 1"
        >
          <text class="baobeo-icon baobeo-icon-erweima3x"></text>
          <view>客户码</view>
        </view> -->
        <!-- <view
          @click="callProject"
          class="icon-box callproject"
          v-if="status === 0"
        >
          案场列表
        </view> -->
      </view>
    </view>
    <Mpop
      ref="pop"
      direction="center"
      :is_close="true"
      :is_mask="true"
      :width="90"
    >
      <view class="input-box row">
        <input
          type="number"
          class="m-input"
          placeholder="请输入隐号内容"
          v-model="form_fill_phone.part_number"
          maxlength="4"
        />
        <view class="form-btn" @click="createFillPhone(report_item.id)">
          确认
        </view>
      </view>
    </Mpop>
    <Mpop
      ref="pop3"
      direction="center"
      :is_close="true"
      :is_mask="true"
      :width="90"
    >
      <view class="other-box-list">
        <view
          class="other-box"
          v-for="(item, index) in other_customer"
          :key="index"
        >
          <view class="content row">
            <view class="left">客户：</view>
            <view class="right row"
              >{{ item.name }} <view class="right">{{ item.phone }}</view></view
            >
          </view>
        </view>
      </view>
    </Mpop>
  </view>
</template>

<script>
import myIcon from "./my-icon";
import Mpop from "./ming-pop";
export default {
  components: { Mpop, myIcon },
  props: {
    status: {
      type: [String, Number],
      default: "", // (2)invalid 默认无效    (1)effective 有效  (0)unconfirmed未确认
    },
    status2: {
      type: [String, Number],
      default: "",
    },
    report_item: [Object],
    is_case: [Number, Boolean],
    load_text: {
      type: Object,
      default: () => {
        return {
          10: "已无效",
          0: "待审核",
          1: "已报备",
          2: "已到访",
          3: "已认筹",
          4: "已认购",
          5: "已成交",
        };
      },
    },
    load_text2: {
      type: Object,
      default: () => {
        return {
          10: "报备无效",
          0: "报备未接收",
          1: "报备有效",
          2: "报备有效",
          3: "报备有效",
          4: "报备有效",
          5: "报备有效",
        };
      },
    },
  },
  data() {
    return {
      show_invit: false,
      form_fill_phone: {
        id: "",
        part_number: "",
      },
      show_cancel: false,
      other_customer: [],
    };
  },
  computed: {},
  methods: {
    filterText() {
      if (this.status) {
        return this.load_text[this.status];
      } else if (this.status == 0) {
        return this.load_text[this.status];
      }
    },
    filterText2() {
      if (this.status2) {
        return this.load_text2[this.status2];
      } else if (this.status2 == 0) {
        return this.load_text2[this.status2];
      }
    },
    clickCode() {
      let updated_time = this.$addTime(
        this.report_item.from_follow_up === 1
          ? this.report_item.follow_up_time || this.report_item.updated_at
          : this.report_item.updated_at,
        this.report_item.p_delay_visit_minute
      ).replace(/-/g, "/"); //   到访时间计算根据客户后台开关 来区分
      updated_time = new Date(updated_time).getTime();
      let delay_time = new Date().getTime();
      if (updated_time <= delay_time) {
        this.$navigateTo(`/report/report_code?id=${this.report_item.id}`);
      } else {
        uni.showToast({
          title:
            "请于" +
            this.$addTime(
              this.report_item.from_follow_up === 1
                ? this.report_item.follow_up_time || this.report_item.updated_at
                : this.report_item.updated_at,
              this.report_item.p_delay_visit_minute
            ) +
            "后进行到访确认",
          icon: "none",
          duration: 3000,
        });
      }
    },
    onClick() {
      this.$emit("open");
    },
    callProject() {
      this.$navigateTo(
        `/client/case_list?project_id=${this.report_item.project_id}&customer_id=${this.report_item.id}`
      );
    },
    callReportProject() {
      if (this.report_item.pu_phone) {
        uni.makePhoneCall({
          phoneNumber: this.report_item.pu_phone,
          success: () => {
            console.log("拨打置业顾问电话电话");
          }, //仅为示例
        });
      }
    },
    fillPhone() {
      this.$refs.pop.show();
    },
    createFillPhone(id) {
      this.form_fill_phone.id = id;
      this.$emit("createFillPhone", this.form_fill_phone);
    },
    showCancel() {
      this.show_cancel = !this.show_cancel;
    },

    followStatu(status, id) {
      this.$emit("status1", status, id);
    },
    // 点击查看更多客户
    clickMoreCustomer(customer) {
      this.other_customer = JSON.parse(customer);
      this.$refs.pop3.show();
    },
  },
};
</script>

<style lang="scss" scoped>
.baobeo-icon-erweima3x {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22baobeo-icon-erweima3x%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M921.6%20614.4V512H1024v102.4h-102.4z%20m0-614.4H1024v409.6h-102.4V0z%20m-204.8%20204.8h102.4v409.6h-102.4V204.8z%20m0-204.8h102.4v102.4h-102.4V0zM1024%201024h-307.2v-307.2H1024V1024z%20m-102.4-204.8h-102.4v102.4h102.4v-102.4zM204.8%20921.6h409.6V1024H204.8v-102.4z%20m307.2-204.8h102.4v102.4H512v-102.4zM0%200h614.4v614.4H0V0z%20m102.4%20512H512V102.4H102.4V512z%20m307.2%20307.2H0v-102.4h409.6v102.4zM102.4%201024H0v-102.4h102.4V1024z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M204.8%20204.8h204.8v204.8H204.8z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
.list {
  margin-top: 24rpx;
  background: #fff;
  height: auto;
  width: 100%;
  .title-box {
    align-items: center;
  }
  .is-receive {
    color: #fff;
    background: #638ff9;
    padding: 5rpx 10rpx;
    border-radius: 8rpx;
    margin-left: 40rpx;
    font-size: 24rpx;
  }
  .item {
    height: auto;
    justify-content: space-between;
    padding: 24rpx 48rpx;
    position: relative;
    .left {
      font-size: 32rpx;
      position: relative;
      .client-box {
        margin-top: 24rpx;
        font-size: 28rpx;
        .label {
          color: #999;
          margin-right: 16rpx;
        }
        .ctn {
          height: auto;
          color: #666;
          align-items: center;
          .fill-phone {
            margin-left: 20rpx;
            color: #638ff9;
            padding: 0 10rpx;
            border-radius: 10rpx;
          }
          .is_1 {
            background: #3366ff;
            margin-left: 20rpx;
            color: #fff;
            padding: 4rpx 10rpx;
          }
          .is_2 {
            background: #33cc99;
            margin-left: 20rpx;
            color: #fff;
            padding: 4rpx 10rpx;
          }
        }
        .ctn_show {
          flex: 1;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          overflow: hidden;
          -webkit-box-orient: vertical;
        }
        .ctn_show_content {
          flex: 1;
        }
      }
    }
    .mark {
      font-size: 24rpx;
      color: #fff;
      line-height: 48rpx;
      align-items: center;
      position: absolute;
      background: #ff7401;
      border-radius: 0 0 0 10px;
      height: 48rpx;
      width: 136rpx;
      right: 48rpx;
      top: 0;
      &.visite {
        background: #638ff9;
      }
      &.subscribe {
        background: #39becd;
      }
      &.isBuy {
        background: #ff8062;
      }
      &.isBuy4 {
        background: #ffa53a;
      }
      &.isDeal {
        background: #33be85;
      }
      &.failure {
        background: #808080;
      }
    }

    .upload_time {
      font-size: 24rpx;
      color: #999;
    }
    .user_info {
      color: #999;
      font-size: 32rpx;
    }
    .end {
      align-items: flex-end;
      height: 24rpx;
    }
  }
  .icon-box {
    align-items: center;
    position: absolute;
    right: 48rpx;
    top: 88rpx;
    .icon-font {
      width: 60rpx;
      height: 60rpx;
    }
    .baobeo-icon {
      width: 70rpx;
      height: 70rpx;
    }
    view {
      margin-top: 16rpx;
      font-size: 24rpx;
      color: #999;
    }
  }
  &::after {
    content: "";
    margin-bottom: 30rpx;
  }
  .callproject {
    text-align: center;
    border-radius: 10rpx;
    padding: 10rpx;
    background: #33be85;
    font-size: 24rpx;
    color: #fff;
  }
}
.qrcode {
  width: 215px;
  height: 215px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
text {
  margin: 0 4rpx;
}
.image {
  width: 70rpx;
  margin-left: 20rpx;
  height: 70rpx;
}
.input-box {
  margin-top: 40rpx;
  align-items: center;
  font-size: 28rpx;
  .m-input {
    width: 80%;
    flex-direction: row;
    align-items: center;
    padding-left: 16rpx;
    height: 32px;
    border-radius: 4px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    background-color: #eee;
    border: 1rpx solid #f3f3f3;
  }
  .form-btn {
    background: #638ff9;
    color: #fff;
    width: 20%;
    height: 32px;
    line-height: 32px;
    align-items: center;
    border-radius: 4px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}

.other-box-list {
  margin-top: 30rpx;
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.06);
  padding: 24rpx;
  .other-box {
    .content {
      margin-bottom: 20rpx;
      color: #999;
      .right {
        margin-left: 20rpx;
      }
    }
  }
}
.btn-box {
  justify-content: flex-end;
}
.btn {
  // margin-right: 20rpx;
  // margin-top: 10rpx;
  background: #859bb2;
  border-radius: 10rpx;
  color: #fff;
  padding: 10rpx 20rpx;
  margin-left: 10rpx;
  &.status {
    background: #f25273;
  }
}
</style>
