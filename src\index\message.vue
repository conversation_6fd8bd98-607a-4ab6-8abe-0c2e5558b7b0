<template>
  <view class="msg">
    <!-- 导航 -->
    <view style="min-height:100vh">
      <view class="nav row">
        <view
          v-for="(item, index) in navs"
          :key="index"
          class="nav-item"
          :class="{ active: item.id === data_type }"
          @click="onClickNav(index)"
          >{{ item.name }}
          <UniBadge
            type="error"
            class="unibadge"
            v-if="index == 1"
            :text="noticeUnread"
          ></UniBadge>
        </view>
      </view>
      <!-- 内容 -->
      <view v-if="data_type === 1" style="margin-top:110rpx;padding:0 48rpx">
        <userList
          :type="2"
          @onClick="onClickMsg"
          @delData="delDataMsg"
          :msg_list="im.friendList"
        ></userList>
      </view>

      <view v-if="data_type === 2" class="msg_content_box">
        <view class="click-box row">
          <text class="title">系统通知</text>
          <view class="right-click row">
            <view @click="allRead">全部已读</view>
            <view @click="allDelete">全部删除</view>
          </view>
        </view>
        <view class="msg_box row" v-for="item in message_list" :key="item.id">
          <view class="admin-img">
            <myIcon
              type="ic_dingyuehao3x"
              size="100rpx"
              color="#0174ff"
            ></myIcon>
            <view v-if="!item.read == 1" class="badge"></view>
          </view>
          <view class="msg-content">
            <view class="top row">
              <view class="top-l">系统消息</view>
              <view class="top-r">{{ item.created_at }}</view>
            </view>
            <view class="bottom">
              <text>{{ item.msg }}</text>
            </view>
          </view>
        </view>
      </view>
      <view v-if="data_type === 3" style="margin-top:90rpx">
        <view
          v-for="item in track_list"
          :key="item.id"
          class="visitors row bottom-line"
          @click="
            $navigateTo(`/message/friend_info?id=${item.visitor_user_id}`)
          "
        >
          <view class="img-box">
            <image class="chat-header" :src="item.u_avatar" mode="aspectFill" />
            <!-- <view class="dot" ></view> -->
          </view>
          <view class="info-box flex-1">
            <view class="title">{{ item.u_name || item.u_nickname }}</view>
            <view class="desc">{{ item.uvt_description }}</view>
          </view>
          <view class="right-icon">
            <view class="time">{{ item.uvt_created_at_str }}</view>
            <view class="dot_place"></view>
          </view>
        </view>
      </view>
      <view v-if="data_type === 4" class="msg_info">
        <view
          class="info_list row"
          @click="clickNews(item)"
          v-for="item in data_list"
          :key="item.id"
        >
          <view class="info_img">
            <image mode="aspectFill" :src="item.img | imageFilter('w_120')" />
          </view>
          <view class="info_ctn">
            <view class="info_title">
              <text>{{ item.title }}</text>
            </view>
            <view class="info_time">{{ item.created_at }}</view>
          </view>
        </view>
      </view>
    </view>
    <myLoading
      ref="loading"
      :custom="false"
      :shadeClick="true"
      :type="1"
    ></myLoading>
    <load-more :status="load_status"></load-more>
    <!-- <view v-if="!this.message_list.length || !this.track_list.length">
      <noData tip="暂无更多数据"></noData>
    </view> -->
    <view class="socket_close" v-if="!im.socketOpen && isOpenInfo">
      <view class="err-tip row">
        <view class="tip-text">聊天连接已断开</view>
        <view class="tip-btn" @click="connectChatAgain()">{{
          reconnect_status
        }}</view>
      </view>
    </view>
    <view class="socket_close" v-if="user_info.id && !user_info.wx_open_id">
      <view class="err-tip row">
        <view class="tip-text"> 授权公众号可随时接收聊天消息</view>
        <view
          class="tip-btn"
          @click="$bindingPublic('/only_build_im/msg_list?type=1')"
          >立即授权</view
        >
      </view>
    </view>
    <BottomBar @click="switchTabBottom" :current="currentTabIndex"></BottomBar>
  </view>
</template>

<script>
import myLoading from "@/components/my-loading";
import myIcon from "@/components/my-icon";
import loadMore from "@/components/loadMore";
// import noData from "@/components/noData";
import userList from "../im_list/components/user_list";
import { mapState, mapActions } from "vuex";
import chat from "../im_list/chatmixin";
import UniBadge from "../components/uni-badge/uni-badge";
export default {
  components: { myLoading, myIcon, loadMore, userList, UniBadge },
  mixins: [chat],
  data() {
    return {
      data_type: 1,
      navs: [
        { id: 1, name: "聊天列表" },
        { id: 2, name: "通知消息" },
        { id: 3, name: "访客消息" },
        { id: 4, name: "楼盘动态" },
      ],
      data_list: [],
      message_list: [],
      params: {
        page: 1,
        pagesize: 10,
        total: 0,
      },
      params_news: {
        page: 1,
        pagesize: 10,
        total: 0,
      },
      params_track: {
        page: 1,
        pagesize: 10,
        total: 0,
      },
      load_status: "",
      noRead: "",
      track_list: [],
      params_im: {
        page: 1,
      },
      reconnect_status: "点击重连", // 点击重新连接按钮
      token: false,
      isOpenInfo: false, // 是否开启警告
      noticeUnread: "", // 通知消息未读
      currentTabIndex: 2,
    };
  },
  onReady() {
    this.$refs.loading.open();
  },
  onShow() {
    this.setUrlWebsiteId();
    // 判断登录状态设置角标
    let token = uni.getStorageSync("token" + this.$store.state.website_id);
    if (token) {
      this.token = true;
      this.getNoticeUnread();
      // 每次进入页面调用查看未读消息
      this.$getUnreadMsg();
      // 如果没连接，初始化聊天
      this.params_im.page = 1;
      this.getImList();
    } else {
      this.token = false;
    }
    if (!this.im.socketOpen) {
      this.getImToken();
    }
  },
  onLoad(options) {
    if (options.type) {
      this.data_type = parseInt(options.type);
      switch (this.data_type) {
        case 2:
          this.getMessage();
          break;
        case 3:
          this.getTarck();
        default:
          this.getDataNews();
          break;
      }
    }
    uni.$on("closeChat", () => {
      this.closeSocket();
    });
    setTimeout(() => {
      this.$refs.loading.close();
    }, 1000);
    this.getUserInfo();
    this.getUserToken();
  },
  onUnload() {
    // 路由时间的页面切换并不会触发onUnload事件 ， 返回时会触发onUnload事件
    this.closeSocket("active");
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  computed: {
    ...mapState(["im", "user_info", "is_setting"]),
  },
  methods: {
    ...mapActions(["getUserInfo"]),

    switchTabBottom(index, item) {
      if (this.currentTabIndex == index) {
        return;
      }
      uni.switchTab({
        url: item.path,
      });
    },
    //初始化聊天列表
    initChat() {
      if (!this.im.socketOpen) {
        this.initMsg = {
          flag: "init",
          from_id: this.im.myChatInfo.platform + this.im.myChatInfo.from_id,
          dialog_id: this.im.myChatInfo.dialog_id,
        };
        this.handleConnectSocket(); // this.onMessage()
        // this.onClose();
        this.onSocketError();
      } else {
        console.log("聊天已连接");
      }
    },
    getUserToken() {
      this.$ajax.get("/client/my/tfy/user_token", {}, (res) => {
        if (res.statusCode === 200) {
          this.im.userToken = res.data.token;
        } else {
          uni.showToast({
            title: res.data.message || "获取usertoken",
            icon: "none",
          });
        }
      });
    },
    getImToken() {
      this.$ajax.get("/client/my/tfy/im_token", {}, (res) => {
        if (res.statusCode === 200) {
          this.im.imToken = res.data.token;
          this.im.myChatInfo.platform = res.data.platform_id_prefix;
          this.im.myChatInfo.from_id = res.data.user_id;
          this.initChat();
        } else {
          uni.showToast({
            title: res.data.message || "获取imtoken",
            icon: "none",
          });
        }
      });
    },
    onClickNav(index) {
      this.$refs.loading.open();
      this.data_type = this.navs[index].id;
      switch (this.data_type) {
        case 4:
          uni.setNavigationBarTitle({ title: "楼盘动态" });
          if (this.params_news.page === 1) {
            this.getDataNews();
          }
          break;
        case 2:
          uni.setNavigationBarTitle({ title: "通知消息" });
          this.$refs.loading.close();
          if (this.params.page === 1) {
            this.getMessage();
          }
          break;
        case 3:
          uni.setNavigationBarTitle({ title: "访客消息" });
          if (this.params_track.page === 1) {
            this.getTarck();
          }
          break;
        case 1:
          uni.setNavigationBarTitle({ title: "聊天消息" });
          if (this.params_im.page === 1) {
            this.getImList();
          }
          break;
      }
    },
    getDataNews() {
      this.load_status = "loading";
      if (this.params_news.page === 1) {
        this.data_list = [];
      }
      this.$ajax.get("/common/news/list", this.params_news, (res) => {
        if (res.statusCode === 200) {
          this.load_status = "loadend";
          this.$refs.loading.close();
          this.data_list = this.data_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        } else {
          this.load_status = "loadend";
          uni.showToast({
            title: res.data.message || "获取信息失败",
            icon: "none",
          });
        }
      });
    },
    getMessage() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.message_list = [];
      }
      this.$ajax.get("/client/msg/search", this.params, (res) => {
        if (res.statusCode === 200) {
          this.load_status = "loadend";
          this.$refs.loading.close();
          this.message_list = this.message_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        } else {
          this.load_status = "loadend";
          uni.showToast({
            title: res.data.message || "获取消息失败",
            icon: "none",
          });
        }
      });
    },
    getTarck() {
      this.load_status = "loading";
      if (this.params_track.page === 1) {
        this.track_list = [];
      }
      this.$ajax.get(
        "/client/user/visitor/search",
        this.params_track,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.$refs.loading.close();
            this.track_list = this.track_list.concat(res.data.data);
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
            }
          } else {
            this.load_status = "loadend";
            uni.showToast({
              title: res.data.message || "获取消息失败",
              icon: "none",
            });
          }
        }
      );
    },
    getImList() {
      this.load_status = "loading";
      if (this.params_im.page === 1) {
        this.im.friendList = [];
      }
      this.$ajax.get("/client/im/session/search", this.params_im, (res) => {
        if (res.statusCode === 200) {
          this.load_status = "loadend";
          this.$refs.loading.close();
          this.isOpenInfo = true;
          this.im.friendList = this.im.friendList.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        } else {
          this.load_status = "loadend";
          uni.showToast({
            title: res.data.message || "获取对话列表失败",
            icon: "none",
          });
        }
      });
    },
    // 点击访客信息
    onClickMsg(item) {
      this.$navigateTo(`/im_list/msg_detail?to_id=${item.u_id}&is_list=1`);
    },
    delDataMsg(item) {
      var that = this;
      uni.showModal({
        title: "提示",
        content: "确认删除该记录？",
        success: function(res) {
          if (res.confirm) {
            that.$ajax.get(
              `/client/im/session/delete/${item.id}`,
              {},
              (res) => {
                if (res.statusCode === 200) {
                  that.params_im.page = 1;
                  that.getImList();
                } else {
                  uni.showToast({
                    title: res.data.message || "删除失败",
                    icon: "none",
                  });
                }
              }
            );
          } else if (res.cancel) {
            console.log("用户点击取消");
          }
        },
      });
    },
    // 全部已读
    allRead() {
      this.params.page = 1;
      this.$ajax.get("/client/msg/read/my/all", {}, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "全部已读",
            icon: "none",
          });
          this.noticeUnread = 0;
          this.getMessage();
        }
      });
    },
    allDelete() {
      this.params.page = 1;
      this.$ajax.get("/client/msg/delete/my/all", {}, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "已删除",
            icon: "none",
          });
          this.noticeUnread = 0;
          this.getMessage();
        }
      });
    },
    clickNews(item) {
      if (item.out_link_address) {
        window.open(item.out_link_address);
      } else {
        this.$navigateTo(`/message/detail?id=${item.id}`);
      }
    },
    // 点击tabbar添加参数
    setUrlWebsiteId() {
      let nowlink = window.location.href;
      const reg = /\?.+=.{0,}/;
      if (
        nowlink.indexOf("?website_id=") === -1 &&
        nowlink.indexOf("&website_id=") === -1
      ) {
        if (reg.test(nowlink)) {
          nowlink += `&website_id=${this.$store.state.website_id || 1}`;
        } else {
          nowlink += `?website_id=${this.$store.state.website_id || 1}`;
        }
        history.replaceState(null, " ", nowlink);
      }
    },

    // 断线重连
    connectChatAgain() {
      this.reconnect_status = "正在连接...";
      this.handleConnectSocket();
      return;
    },
    // 获取通知未读数量
    getNoticeUnread() {
      this.$ajax.get("/client/msg/total/unread", {}, (res) => {
        if (res.statusCode === 200) {
          res.data.total > 0
            ? (this.noticeUnread = res.data.total)
            : (this.noticeUnread = "");
        } else {
          uni.showToast({
            title: res.data.message || "获取通知未读消息失败",
            icon: "none",
          });
        }
      });
    },
  },
  onPullDownRefresh: function() {
    switch (this.data_type) {
      case 2:
        this.params.page = 1;
        this.getMessage();
        break;
      case 3:
        this.params_track.page = 1;
        this.getTarck();
        break;
      case 4:
        this.params_news.page = 1;
        this.getDataNews();
        break;
      default:
        this.params_im.page = 1;
        this.getImList();
        break;
    }
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    switch (this.data_type) {
      case 2:
        this.params.page++;
        this.getMessage();
        break;
      case 3:
        this.params_track.page++;
        this.getTarck();
        break;
      case 4:
        this.params_news.page++;
        this.getDataNews();
        break;
      default:
        this.params_im.page++;
        this.getImList();
        break;
    }
  },
  onTabItemTap() {
    this.setUrlWebsiteId();
  },
};
</script>

<style lang="scss" scoped>
.icon-font {
  width: 100rpx;
  height: 100rpx;
}
// 导航
.nav {
  width: 100%;
  background: #fff;
  flex-direction: row;
  padding: 30rpx;
  justify-content: space-around;
  position: fixed;
  z-index: 1;
  .nav-item {
    padding: 10rpx 5rpx;
    text-align: center;
    font-weight: bold;
    color: #666;
    position: relative;
    .unibadge {
      position: absolute;
      right: -28rpx;
      top: -10rpx;
    }
    &.active {
      color: #0174ff;
      border-bottom: 4rpx solid #0174ff;
    }
  }
}
.msg_content_box {
  margin-top: 90rpx;
  .click-box {
    padding: 24rpx 48rpx;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 38rpx;
    }
    .right-click {
      view {
        padding: 14rpx;
        background: #f4f9ff;
        color: #0174ff;
        margin-left: 30rpx;
        border-radius: 40rpx;
      }
    }
  }
}
.msg_box {
  justify-content: space-between;
  padding: 24rpx 48rpx;
  .admin-img {
    position: relative;
    .badge {
      position: absolute;
      top: -5px;
      right: 0px;
      width: 20rpx;
      height: 20rpx;
      border-radius: 50%;
      background: $uni-color-error;
    }
  }

  .msg-content {
    width: 100%;
    margin-left: 24rpx;
    justify-content: space-around;
    .top {
      justify-content: space-between;
      align-items: center;
      .top-l {
        font-size: 32rpx;
        font-weight: bold;
      }
      .top-r {
        font-size: 22rpx;
        color: #999;
      }
    }
    .bottom {
      line-height: 40rpx;
      width: 265px;
      font-size: 28rpx;
      color: #666;
      // text {
      //   overflow: hidden;
      //   text-overflow: ellipsis;
      //   white-space: nowrap;
      // }
    }
    &::after {
      content: "";
      height: 2rpx;
      background: #d8d8d8;
    }
  }
}
.visitors {
  align-items: center;
  padding: 24rpx 48rpx;
  .img-box {
    width: 110upx;
    height: 110upx;
    margin-right: 36upx;
    position: relative;
    .chat-header {
      border-radius: 50%;
      position: absolute;
      width: 100%;
      height: 100%;
    }
  }
  .dot {
    min-width: 32upx;
    height: 32upx;
    padding: 0 10upx;
    box-sizing: border-box;
    line-height: 30upx;
    text-align: center;
    font-size: 22upx;
    border-radius: 16upx;
    background-color: #f44;
    color: #fff;
    position: absolute;
    top: 30rpx;
    left: 130rpx;
    z-index: 2;
  }
  .info-box {
    overflow: hidden;
    flex: 1;
    .title {
      line-height: 1.2;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: 32upx;
      font-weight: bold;
      color: #0f1d32;
      letter-spacing: 2upx;
      margin-bottom: 12upx;
      .uname {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .levelname {
        line-height: 32rpx;
        margin: 0 16upx;
        padding: 0 16rpx;
        display: inline-block;
        border-radius: 16upx;
        font-size: 22upx;
        // border: 1upx solid #4ebdf8;
        color: #999;
        background-color: #f2f2f2;
        font-weight: initial;
        &.official {
          background-color: #1296db;
          color: #fff;
          // border: 1upx solid #1296db;
        }
        &.agent {
          // border: 1upx solid #f96063;
          background-color: #f96063;
          color: #fff;
        }
        &.adviser {
          // border: 1upx solid #f0bb2c;
          background-color: #f0bb2c;
          color: #fff;
        }
      }
    }
    .desc {
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      font-size: 26upx;
      height: 40upx;
      line-height: 40upx;
      color: #888;
    }
    .other {
      // white-space: nowrap;
      // overflow: hidden;
      // text-overflow: ellipsis;
      font-size: 24upx;
      color: #999;
    }
  }
  .right-icon {
    position: relative;
    height: 90upx;
    // width: 120upx;
    // .icon-box{
    //     position: absolute;
    //     display:inline-block;
    //     height: 28px;
    //     width: 28px;
    //     left: 0;
    //     right: 0;
    //     top: 0;
    //     bottom: 0;
    //     margin: auto;
    // }
    .time {
      font-size: 24upx;
      color: #999;
    }
    .dot {
      min-width: 32upx;
      height: 32upx;
      padding: 0 10upx;
      box-sizing: border-box;
      line-height: 30upx;
      text-align: center;
      font-size: 22upx;
      border-radius: 16upx;
      background-color: #f44;
      color: #fff;
      position: absolute;
      // display: inline-block;
      margin-top: 20upx;
      right: 0;
    }
    .dot_place {
      // margin-top: 20upx;
      // height: 32upx;
    }
  }
}
.msg_info {
  padding: 24rpx 48rpx;
  margin-top: 120rpx;
  .info_list {
    margin-bottom: 48rpx;
    .info_img {
      width: 240rpx;
      height: 172rpx;
      image {
        width: 100%;
        height: 172rpx;
      }
    }
    .info_ctn {
      margin-left: 10rpx;
      width: 404rpx;
      justify-content: space-between;
      .info_title {
        font-size: 32rpx;
        line-height: 52rpx;
        text {
          text-overflow: -o-ellipsis-lastline;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }
      }
      .info_description {
        font-size: 22rpx;
        color: #999;
        text {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .info_time {
        font-size: 22rpx;
        color: #999;
        align-items: flex-end;
      }
    }
  }
}
.socket_close {
  z-index: 1000;
  position: fixed;
  top: 20rpx;
  width: 100%;
  .err-tip {
    align-items: center;
    justify-content: space-between;
    padding: 30upx 24upx;
    background-color: #2e8cef;
    color: #fff;
    .tip-btn {
      padding: 6upx 12upx;
      border: 1upx solid #fff;
      border-radius: 6upx;
      font-size: 26upx;
    }
  }
}
</style>
