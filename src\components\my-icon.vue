<template>
  <text class="iconfont" :class="'iconf-' + type" :style="{
    color: color,
    fontSize: size,
    lineHeight: lineHeight,
    fontWeight: fontWeight,
  }" @click="$emit('click')"></text>
</template>

<script>
export default {
  name: "myIcon",
  props: {
    type: {
      type: String,
      default: "",
    },
    color: {
      type: String,
      default: "#333333",
    },
    size: {
      type: [String, Number],
      default: "38rpx",
    },
    lineHeight: {
      type: [Number, String],
      default: 1,
    },
    fontWeight: {
      type: [String],
      default: "initial",
    },
  },
};
</script>

<style>
@font-face {
  font-family: "iconfont";
  /* Project id 1345020 */
  src: url('//at.alicdn.com/t/c/font_1345020_o9i5nu98bso.woff2?t=1708499589986') format('woff2'),
    url('//at.alicdn.com/t/c/font_1345020_o9i5nu98bso.woff?t=1708499589986') format('woff'),
    url('//at.alicdn.com/t/c/font_1345020_o9i5nu98bso.ttf?t=1708499589986') format('truetype');
  /* src: url('//at.alicdn.com/t/c/font_1345020_mzeblkem0hr.woff2?t=1690338529332') format('woff2'),
    url('//at.alicdn.com/t/c/font_1345020_mzeblkem0hr.woff?t=1690338529332') format('woff'),
    url('//at.alicdn.com/t/c/font_1345020_mzeblkem0hr.ttf?t=1690338529332') format('truetype'); */
}

.iconfont {
  font-family: 'iconfont' !important;
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  text-decoration: none;
  -webkit-font-smoothing: antialiased;
}

.iconf-fuzhi:before {
  content: "\e8b0";
}

.iconf-fenxiang:before {
  content: "\e656";
}

.iconf-follow1:before {
  content: "\e642";
}

.iconf-purse1x:before {
  content: "\e637";
}

.iconf-topList1x:before {
  content: "\e627";
}

.iconf-luyin:before {
  content: '\e6d8';
}

.iconf-douyin:before {
  content: '\e8db';
}

.iconf-caidan:before {
  content: '\e666';
}

.iconf-ic_sousuo:before {
  content: '\e691';
}

.iconf-jinrujiantou:before {
  content: '\e8b6';
}

.iconf-yichu:before {
  content: '\e658';
}

.iconf-yichu1:before {
  content: '\e77f';
}

.iconf-a-tianjia1x:before {
  content: '\e692';
}

.iconf-a-lianjie3x:before {
  content: '\e690';
}

.iconf-a-xiazai3x:before {
  content: '\e69c';
}

.iconf-guanbi:before {
  content: '\e656a';
}

.iconf-shizhong:before {
  content: '\e657a';
}

.iconf-shouruguanli:before {
  content: '\e655';
}

.iconf-open:before {
  content: '\e654';
}

.iconf-jianshao:before {
  content: '\e6aa';
}

.iconf-yanzhengma:before {
  content: '\e652';
}

.iconf-shouji:before {
  content: '\e653';
}

.iconf-ic_guanli:before {
  content: '\e651';
}

.iconf-ic_xiangmu:before {
  content: '\e64f';
}

.iconf-ic_xiangmu_nor:before {
  content: '\e650';
}

.iconf-4:before {
  content: '\e603';
}

.iconf-ic_tongxun:before {
  content: '\e64e';
}

.iconf-ic_wode:before {
  content: '\e647';
}

.iconf-ic_shouye_nor:before {
  content: '\e649';
}

.iconf-ic_xiaoxi_nor:before {
  content: '\e64a';
}

.iconf-ic_wode_nor:before {
  content: '\e64b';
}

.iconf-ic_shouye:before {
  content: '\e64c';
}

.iconf-ic_xiaoxi:before {
  content: '\e64d';
}

.iconf-ic_fenxiang:before {
  content: '\e646';
}

.iconf-erweima:before {
  content: '\e645';
}

.iconf-ic_shouji:before {
  content: '\e634';
}

.iconf-ic_zixun_b:before {
  content: '\e635';
}

.iconf-tianjia_b:before {
  content: '\e636';
}

.iconf-tianjia_w:before {
  content: '\eaaa';
}

.iconf-youhui:before {
  content: '\e638';
}

.iconf-yaoqing:before {
  content: '\e639';
}

.iconf-wenda:before {
  content: '\e63a';
}

.iconf-dianhua:before {
  content: '\e63b';
}

.iconf-weixin:before {
  content: '\e63c';
}

.iconf-ic_guanzhu:before {
  content: '\e63d';
}

.iconf-pengyouquan:before {
  content: '\e63e';
}

.iconf-fenxi:before {
  content: '\e63f';
}

.iconf-zixun1:before {
  content: '\e640';
}

.iconf-zhengmian:before {
  content: '\e641';
}

.iconf-pengyou:before {
  content: '\e643';
}

.iconf-ic_weixin:before {
  content: '\e644';
}

.iconf-ic_gongzhonghao:before {
  content: '\e632';
}

.iconf-anchang:before {
  content: '\e633';
}

.iconf-toutiao1x:before {
  content: '\e630';
}

.iconf-yingyong:before {
  content: '\e631';
}

.iconf-icons01:before {
  content: '\e698';
}

.iconf-zixun:before {
  content: '\e62f';
}

.iconf-uploadPic:before {
  content: '\e679';
}

.iconf-shanchu:before {
  content: '\e62e';
}

.iconf-shangla1:before {
  content: '\e604';
}

.iconf-xiala:before {
  content: '\e660';
}

.iconf-shangla:before {
  content: '\e62d';
}

.iconf-tuichu:before {
  content: '\e62c';
}

.iconf-ic_queren3x:before {
  content: '\e62b';
}

.iconf-ic_gaoxiao3x:before {
  content: '\e62a';
}

.iconf-ic_loupan3x:before {
  content: '\e629';
}

.iconf-dengluchahua:before {
  content: '\e618';
}

.iconf-ic_vr3x:before {
  content: '\e628';
}

.iconf-ic_sousuo3x1:before {
  content: '\e627a';
}

.iconf-ic_baobei3x1:before {
  content: '\e622';
}

.iconf-ic_fankui3x1:before {
  content: '\e623';
}

.iconf-ic_guanyu3x1:before {
  content: '\e624';
}

.iconf-ic_kefu3x1:before {
  content: '\e625';
}

.iconf-ic_tongxun3x1:before {
  content: '\e626';
}

.iconf-you:before {
  content: '\e648';
}

.iconf-ic_vr:before {
  content: '\e620';
}

.iconf-ic_video:before {
  content: '\e621';
}

.iconf-xialajiantou:before {
  content: '\e61f';
}

.iconf-ic_xiaoxi3x:before {
  content: '\e607';
}

.iconf-up3x:before {
  content: '\e608';
}

.iconf-ic_xiaoxi_nor3x:before {
  content: '\e609';
}

.iconf-ic_xiezilou3x:before {
  content: '\e60a';
}

.iconf-ic_guanyu3x:before {
  content: '\e60b';
}

.iconf-ic_fankui3x:before {
  content: '\e60c';
}

.iconf-ic_baobei3x:before {
  content: '\e60d';
}

.iconf-erweima3x:before {
  content: '\e60e';
}

.iconf-kehu3x:before {
  content: '\e60f';
}

.iconf-ic_shuju3x:before {
  content: '\e610';
}

.iconf-baobei3x:before {
  content: '\e611';
}

.iconf-ic_shouye3x:before {
  content: '\e612';
}

.iconf-ic_bieshu3x:before {
  content: '\e613';
}

.iconf-ic_wode3x:before {
  content: '\e614';
}

.iconf-ic_tongxun3x:before {
  content: '\e615';
}

.iconf-down3x:before {
  content: '\e616';
}

.iconf-ic_kefu3x:before {
  content: '\e617';
}

.iconf-ic_sousuo3x:before {
  content: '\e619';
}

.iconf-ic_shangpu3x:before {
  content: '\e61a';
}

.iconf-ic_dingyuehao3x:before {
  content: '\e61b';
}

.iconf-ic_shouye_nor3x:before {
  content: '\e61c';
}

.iconf-ic_wode_nor3x:before {
  content: '\e61d';
}

.iconf-ic_xiaoqu3x:before {
  content: '\e61e';
}
</style>
