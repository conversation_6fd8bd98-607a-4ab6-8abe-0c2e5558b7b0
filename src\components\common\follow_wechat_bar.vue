<template>
    <view class="follow-wechat">
        <uni-notice-bar single text="请关注公众号接收系统消息提醒" showGetMore moreText="关注" @getmore ="openFollowWeachPopup"/>
    
        <my-popup ref="qrcode_popup" position="top" :show="showPopup">
            <view class="qrcode-box">
                <!-- #ifndef H5 -->
                <view class="qrimg-box">
                    <image @longtap="saveQrcode" class="qrcode" :src="qrcode" mode="aspectFill"></image>
                    <view>
                        <view class="title">长按保存图片</view>
                        <view class="tip">相册选取，识别关注</view>
                    </view>
                </view>
                <!-- #endif -->
                <!-- #ifdef H5 -->
                <view class="qrimg-box">
                    <image class="qrcode" :src="qrcode" mode="aspectFill"></image>
                    <view>
                        <view class="tip">长按识别二维码关注公众号</view>
                    </view>
                </view>
                <!-- #endif -->
                <view class="icon-box" @click="showPopup=false">
                    <my-icon type="guanbi" color="#fff" size="62rpx"></my-icon>
                </view>
            </view>
        </my-popup>

    </view>
</template>

<script>
import myPopup from '@/components/myPopup'
import myIcon from '@/components/my-icon'
export default {
    data(){
        return {
            showPopup: false,
            qrcode: '/static/img/t-qrcode.jpg'
        }
    },
    components: {
        myPopup, myIcon
    },
    options: {
        styleIsolation: 'shared'
    },
    methods: {
        openFollowWeachPopup(){
            this.showPopup = true;
        },
        // 保存二维码
        saveQrcode(){
            uni.authorize({
                scope: 'scope.writePhotosAlbum',
                success: ()=> {
                    uni.saveImageToPhotosAlbum({
                        filePath: this.qrcode,
                        success: result => {
                            uni.showToast({
                                title: '保存成功，在微信从相册中选取识别吧',
                                icon: 'none',
                                duration: 4000
                            })
                        },
                        fail: err => {
                            console.log(err);
                            uni.showToast({
                                title: '保存失败，请重试',
                                icon: 'none'
                            })
                        }
                    })
                },
                fail: err => {
                    console.log(err);
                    uni.showToast({
                        title: '未授权保存到相册',
                        icon: 'none'
                    })
                }
            })
            
        },
            

    }
}
</script>

<style lang="scss" scoped>
::v-deep .uni-noticebar{
    padding: 10px 24px;
    margin: 0;
    .uni-noticebar__more{
        height: 22px;
        line-height: 22px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding:0 8px;
        border: 1px solid #FF9A43;
        border-radius: 2px;
        text{
            font-size: 13px!important;
        }    
    }
    
}

.qrcode-box{
	position: relative;
	margin-top: 15vh;
	.qrimg-box{
		width: 584rpx;
		padding: 12rpx;
		margin: auto;
		background-color: #fff;
		border-radius: 8rpx;
		overflow: hidden;
		.title{
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
			color: #333;
		}
		.tip{
			padding: 24rpx;
			padding-bottom: 48rpx;
			text-align: center;
			color: #666;
		}
	}
	.qrcode{
		width: 560rpx;
		height: 560rpx;
	}
	.icon-box{
		position: absolute;
		bottom: -80rpx;
		width: 52rpx;
		height: 52rpx;
		left: 0;
		right: 0;
		margin: auto;
	}
}
</style>