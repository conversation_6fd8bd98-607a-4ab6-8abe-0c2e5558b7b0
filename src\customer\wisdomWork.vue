<template>
    <view>
        <view class="whole"></view>
        <!-- 智慧经营 -->
        <!-- 本周 -->
        <view class="whole_con">
            <scroll-view class="weektop" scroll-x="true" @scroll="scroll">
                <view class="weekeds">
                    <view class="weeked" v-for="(tab, index) in navs" :key="tab.id" :id="tab.id"
                        :class="navIndex == index ? 'activite' : ''" @click="checkIndex(tab, index)">{{ tab.name }}
                    </view>
                </view>
            </scroll-view>
            <view class="change">
                <!-- 比例模块 -->
                <scroll-view class="weekbili" scroll-x="true" @scroll="scroll">
                    <view class="proportion_whole flex-row items-center">
                        <view class="proportion" v-for="item in censusList" :key="item.id" @click="toPublicList(item)">
                            <view class="header">
                                <image :src="item.app_images" style="width:64rpx;height:64rpx"></image>
                                <view class="name">{{ item.desc }}</view>
                            </view>
                            <view class="middle">{{ item.num }}</view>
                            <view class="foot">
                                <view class="proportion_foot_left">占比：</view>
                                <view class="proportion_foot_right">{{ (item.ratio * 100).toFixed(2) + "%" }}</view>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <view class="trantions_top">
                <view style="display: flex;flex-direction: row;justify-content: space-between;align-items: center;">
                    <view class="whole_middle">团队成员</view>
                    <view style="display: flex;flex-direction: row;align-items: center;">
                        <view class="whole_middle" style="margin-right: 10rpx;color: #488AF6;" @click="selectMember
                ">{{ recordMember }}</view>
                        <text style="font-size: 20rpx;color: #488AF6;" @click="selectMember
                ">&#9660;</text>
                        <uni-icons v-if="recordMember != '维护人'" style="padding: 15rpx;" type="clear" size="22"
                            color="#b1b1b1" @click="maintainChange"></uni-icons>
                    </view>
                </view>
                <view class="whole_input">
                    <view class="whole_input_left">
                        <view class="whole_input_left_name" @click="openDepartment">{{ deptSelectedLable }}</view>
                        <text style="font-size: 20rpx;color: #a1a1a1;" @click="openDepartment">&#9660;</text>
                        <uni-icons v-if="deptSelectedLable != '按部门'" style="padding: 15rpx;" type="clear" size="22"
                            color="#b1b1b1" @click="departmentChange"></uni-icons>
                    </view>
                    <text style="color: #b1b1b1;" @click="openDepartment">请输入搜索内容</text>
                </view>
            </view>
            <uni-transition mode-class="slide-bottom" :show="isExplain">
                <uni-notice-bar background-color="#fff" color="#a1a1a1" :text="explainText" />
            </uni-transition>
            <view class="table">
                <view class="table-l">
                    <view class="table-l-header">成员</view>
                    <view class="table-body">
                        <view class="table-body-item" v-for="(item, index) in tableHeader" :key="index">
                            <text class="table-l-avatar" :style="'background:' + item.color">{{ item.name[0] }}</text>
                            <view class="table-l-info">
                                <text>{{ item.name }}</text>
                                <text>{{ item.department }}</text>
                            </view>
                        </view>
                    </view>
                </view>
                <scroll-view class="table-r" scroll-x>
                    <view class="table-header">
                        <view v-for="(item, index) in dataHeader " :key="index" @click="explain(item.tips, index)">
                            <text :style="'width:' + item.web_width * 2 + 'rpx'">{{ item.name }}</text>
                        </view>
                    </view>
                    <view class="table-body">
                        <view class="table-item" v-for="(item, index) in dataList" :key="index">
                            <view v-for="(hitem, hindex) in dataHeader" :key="hindex">
                                <text class="item-info" v-if="hitem.field != 'user_name'"
                                    :style="'width:' + hitem.web_width * 2 + 'rpx'">{{ hitem.level
                ==
                1 ? item[hitem.field] : item[hitem.field].num }}</text>
                            </view>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </view>
        <loadMore :status="load_status" @reload="getworkLister()" />
        <!-- 部门弹框 -->
        <!-- <uni-popup ref="popup" type="bottom" class="popup" :safe-area="false">
            <uni-collapse ref="collapse" v-model="values" @change="change" accordion>
                <uni-search-bar @input="search" v-model="keywords" placeholder="请输入部门名称">
                </uni-search-bar>
                <uni-collapse-item v-if="resValuie == ''" :title="item.name" v-for="item in departmentList"
                    :key="item.id">
                    <view class="content" v-for="son in item.subs" :key="son.id" @click="chengs(son, son.id)">
                        <text class="text">{{ son.name }}</text>
                    </view>
                </uni-collapse-item>
                <uni-list v-if="resValuie != ''">
                    <uni-list-item @click="idfn(item)" :clickable="true" :title="item.name" v-for="item in vagueList"
                        :key="item.id" />
                </uni-list>
            </uni-collapse>
            <view style="height: 400rpx; background-color: #ffff;"></view>
        </uni-popup> -->
        <uni-drawer ref="showRight" mode="right">
            <view class=" members">
                <text class="title">请选择录入人</text>
                <scroll-view scroll-y style="height: 100%;">
                    <view class="member-item" :style="memberActivite == index ? 'border-bottom:1px solid #488AF6' : ''"
                        v-for="(item, index) in memberList" :key="index" @click="memberChange(item, index)">
                        <text class="avatar">{{ item.user_name[0] }}</text>
                        <view class="member-info">
                            <text :style="memberActivite == index ? 'color:#488AF6' : ''">{{ item.user_name }}</text>
                            <text :style="memberActivite == index ? 'color:#488AF6' : ''">{{ item.phone }}</text>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </uni-drawer>
        <tDepartmentPicker :visible.sync="dialogs.deptPicker" v-model="proportionList.department_id"
            @confirm="confirmSeledDept">
        </tDepartmentPicker>
    </view>
</template>
<script>
import loadMore from "@/components/loadMore.vue";
import tDepartmentPicker from '@/components/tplus/tDepartmentPicker.vue';
export default {
    components: {
        loadMore,
        tDepartmentPicker
    },
    data() {
        return {
            deptSelectedLable: '按部门',
            dialogs: {
                deptPicker: false
            },
            memberActivite: null,
            explainIndex: null,
            isExplain: false,
            explainText: '',
            recordMember: '维护人',
            departmentText: '按部门',
            keywords: '',
            values: [''],
            load_status: "",
            navIndex: 2, // 本周
            navs: [
                { id: 1, name: '全部' },
                { id: 2, name: "今天" },
                { id: 3, name: "昨天" },
                { id: 4, name: "本周" },
                { id: 5, name: "上周" },
                { id: 6, name: "本月" },
                { id: 7, name: "上月" },
            ],
            departmentList: [],
            proportionList: {
                page: 1,
                per_page: 10,
                date_type: '1',
                department_id: 0,
            },
            censusList: [],// 客户卡片
            dataList: [],
            dataHeader: [],
            tableHeader: [],
            names: '',
            vagueList: [],
            icon: [
                { id: 1, url: '../static/icon/index/客户总量.png' },
                { id: 2, url: '../static/icon/index/有效客户.png' },
                { id: 3, url: '../static/icon/index/A级客户.png' },
                { id: 4, url: '../static/icon/index/B级客户.png' },
                { id: 5, url: '../static/icon/index/C级客户.png' },
            ],
            resValuie: '',
            memberList: []
        };
    },
    watch: {
        navIndex: {
            handler(nval) {
                return;
                // console.log(nval+1,'tepy');
                this.proportionList.date_type = nval + 1
                this.getworkList()
                this.getworkLister()
            },
            immediate: true
        }
    },
    onReachBottom() {
        if (this.load_status == 'loading') return
        this.proportionList.page++
        this.getworkLister()
    },
    // onPageScroll(e) {
    //     this.getDomTop()
    // },
    created() {
        uni.showLoading({
            title: "加载中",
        })
    },
    onLoad(options) {
        uni.redirectTo({ url: '/wisdomWork/index?website_id='+options.website_id });
        // this.getMembers()
    },
    computed: {
        isAdmin() {
            let userInfo = JSON.parse(uni.getStorageSync('userInfo'))
            return userInfo.roles.some(item => item.name == '站长')
        },
        // isMe() {
        //   let userInfo = JSON.parse(uni.getStorageSync('userInfo'))
        //   return userInfo.id == this.userData.id
        // }
    },
    methods: {
        // 卡片跳转
        toPublicList(item) {
            let url = ''
            if (item.id <= 6) {
                url = `/customer/my_list?wisdomWork=1&data_type=${this.proportionList.date_type}`
            } else if (item.id == 6) {
                url = `/customer/seas_list?wisdomWork=1&data_type=${this.proportionList.date_type}`
            } else if (item.id == 7) {
                url = `/customer/my_list?wisdomWork=1&data_type=${this.proportionList.date_type}`
            } else if (item.id > 7) {
                url = `/customer/qianzai_list?wisdomWork=1&data_type=${this.proportionList.date_type}`
            }
            switch (item.id) {
                case 2:
                    // 我的客户 a
                    this.$navigateTo(url + `&level_id=10`)
                    break;
                case 3:
                    // 我的客户 b
                    this.$navigateTo(url + `&level_id=11`)
                    break;
                case 4:
                    // 我的客户 c
                    this.$navigateTo(url + `&level_id=12`)
                    break;
                case 5:
                    // 我的客户 有效
                    this.$navigateTo(url + `&tracking_id=13`)
                    break;
                case 7:
                    // 我的客户 成员（激活部门下拉菜单）
                    this.$navigateTo(url + `&member=1`)
                    break;
                case 9:
                    // 潜在客户 掉公
                    this.$navigateTo(url + `&c_type4=2`)
                    break;
                case 10:
                    // 潜在客户 转公
                    this.$navigateTo(url + `&c_type4=1`)
                    break;
                default:
                    this.$navigateTo(url)
                    break;
            }
        },
        getDomTop() {
            var that = this;
            const query = uni.createSelectorQuery().in(that);
            query.select('.table').boundingClientRect(data => {
                if (data.top < 0) {
                    console.log('吸顶了----')
                }
            }).exec();
        },
        // 表头描述
        explain(e, i) {
            if (i == this.explainIndex && this.isExplain) return this.isExplain = false
            this.isExplain = true
            this.explainIndex = i
            this.explainText = e
        },
        confirmSeledDept(data) {
            if (data.value.length) {
                this.proportionList.department_id = data.value.splice(-1)[0]
                this.deptSelectedLable = data.label.splice(-1)[0]
                this.dataList = []
                this.tableHeader = []
                this.censusList = []
                this.proportionList.page = 1
                this.proportionList.admin_type = 0
                this.proportionList.admin_id = 0
                this.getworkList()
                this.getworkLister()
            }
            this.dialogs.deptPicker = false
        },
        // 部门清除
        departmentChange() {
            this.deptSelectedLable = '按部门'
            this.dataList = []
            this.tableHeader = []
            this.censusList = []
            this.proportionList.page = 1
            this.proportionList.department_id = 0
            this.getworkList()
            this.getworkLister()
        },
        // 部门筛选
        openDepartment() {
            this.dialogs.deptPicker = true
        },

        maintainChange() {
            this.memberActivite = null
            this.recordMember = '维护人'
            this.dataList = []
            this.tableHeader = []
            this.censusList = []
            this.proportionList.page = 1
            this.proportionList.admin_type = 0
            this.proportionList.admin_id = 0
            this.getworkList()
            this.getworkLister()
        },
        // 选择录入人
        memberChange(e, i) {
            if (this.memberActivite == i) return
            this.memberActivite = i
            this.dataList = []
            this.tableHeader = []
            this.censusList = []
            this.proportionList.page = 1
            this.proportionList.admin_type = 1
            this.proportionList.admin_id = e.id
            this.recordMember = `录入人：${e.user_name}`
            this.getworkList()
            this.getworkLister()
            this.$refs.showRight.close();
        },
        closeDrawer() {
            this.$refs.showRight.close();
        },
        selectMember() {
            this.getMembers()
            this.$refs.showRight.open();
        },
        // 获取成员列表
        getMembers() {
            this.$ajax.get('/admin/crm/admin_users/list', {}, (res) => {
                if (res.statusCode == 200) {
                    this.memberList = res.data
                }
            })
        },
        chengs(item, son) {
            if (item.id) {
                this.departmentText = item.name
                this.dataList = []
                this.tableHeader = []
                this.censusList = []
                this.proportionList.page = 1
                this.proportionList.department_id = son
                this.getworkList()
                this.getworkLister()
            }
            this.$refs.popup.close()
        },
        // 搜索id自动检索
        // idfn(item) {
        //     this.departmentText = item.name
        //     this.dataList = []
        //     this.tableHeader = []
        //     this.censusList = []
        //     this.proportionList.department_id = item.id
        //     this.proportionList.page = 1
        //     this.getworkList()
        //     this.getworkLister()
        //     this.$refs.popup.close()
        //     if (this.keywords != '') {
        //         this.keywords = ''
        //     }
        // },
        // 按部门
        search(res) {
            this.$ajax.get('/admin/personnelMatters/departmentByName', { keywords: this.keywords }, (res) => {
                if (res.statusCode === 200) {
                    this.vagueList = res.data
                }
            })
            this.resValuie = res
        },
        change(e) {
        },
        //获取元素离页面顶部的距离
        getElementScollTop() {
            // this.$refs.popup.open('bottom')
            this.$ajax.get('/qywx/department/list', {}, (res) => {
                this.departmentList = res.data
            })
        },
        // tab互斥效果
        changeAct(item) {
            this.act = item.id;
        },
        scroll(e) {
            // console.log(e)
        },
        checkIndex(tab, index) {
            if (this.navIndex == index) return
            this.censusList = []
            this.dataList = []
            this.dataHeader = []
            this.tableHeader = []
            this.navIndex = index;
        },
        // 客户卡片
        getworkList() {
            this.load_status = "loading";
            this.$ajax.get('/admin/crm/smart_management/census', this.proportionList, (res) => {
                if (res.statusCode === 200) {
                    this.censusList = res.data
                    this.load_status = 'nomore'
                    uni.hideLoading()
                } else {
                    uni.hideLoading()
                }
            },
                () => {
                    uni.hideLoading()
                }
            )
        },
        // 客户列表
        getworkLister() {
            this.load_status = "loading";
            this.$ajax.get('/admin/crm/smart_management/search', this.proportionList, (res) => {
                if (res.statusCode === 200) {
                    let user_name = []
                    this.dataList = this.dataList.concat(res.data.data)
                    res.data.data.forEach((element, index) => {
                        let headerObj = {}
                        headerObj.id = element.id
                        headerObj.name = element.user_name
                        headerObj.department = element.department
                        if (this.proportionList.page == 1) {
                            switch (index) {
                                case 0:
                                    headerObj.color = '#f52000'
                                    break;
                                case 1:
                                    headerObj.color = '#f65600'
                                    break;
                                case 2:
                                    headerObj.color = '#f7bd00'
                                    break;
                                default:
                                    break;
                            }
                        }
                        this.tableHeader.push(headerObj)
                    });
                    res.data.header.splice(0, 1)
                    this.dataHeader = res.data.header
                    if (res.data.data.length < this.proportionList.per_page) {
                        this.load_status = 'nomore'
                    } else {
                        this.load_status = 'loadend'
                    }
                    uni.hideLoading()
                } else {
                    this.load_status = 'nomore'
                    uni.hideLoading()
                }
            },
                () => {
                    uni.hideLoading()
                }
            )
        }
    },
};
</script>
<style lang="scss">
.member-activite {
    color: #488AF6;
    border-bottom: 1px solid #488AF6;
}

.uni-noticebar.data-v-0279da34 {
    margin: 0 !important;
    border-top: 1px solid #f3f3f3;
}

.members {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    border-radius: 5px 5px 0 0;
    padding-bottom: 80rpx;
    background-color: #fff;

    .title {
        margin-top: 30rpx;
        font-size: 32rpx;
        font-weight: 500;
        padding: 40rpx 0;
        color: #333;
    }

    .member-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding: 20rpx;
        border-bottom: 1px solid #f3f3f3;

        .avatar {
            width: 80rpx;
            height: 80rpx;
            line-height: 80rpx;
            font-size: 32rpx;
            text-align: center;
            border-radius: 50%;
            background-color: #488AF6;
            color: #fff;
            margin-right: 20rpx;
        }

        .member-info {
            &>text:nth-child(1) {
                font-size: 32rpx;
                color: #333;
                font-weight: 500;
                margin-bottom: 10rpx;
            }

            &>text:nth-child(2) {
                font-size: 24rpx;
                color: #666;
                font-weight: 400;
                padding: 10rpx 20rpx;
                background-color: #f3f3f3;
            }
        }
    }
}

.table {
    position: relative;
    display: flex;
    flex-direction: row;

    .table-l {
        width: 156rpx * 2;
        border-right: 1px solid #F2F3F5;

        .table-l-header {
            width: 100%;
            height: 88rpx;
            text-align: center;
            line-height: 88rpx;
            font-size: 28rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #86909C;
            border-right: 1px solid #F2F3F5;
            background-color: #F7F8FA;
        }

        .table-body {
            display: flex;
            background-color: #fff;

            .table-body-item {
                display: flex;
                flex-direction: row;
                align-items: center;
                justify-content: center;
                height: 162rpx;
                border-bottom: 1px solid #F2F3F5;

                .table-l-avatar {
                    width: 48rpx * 2;
                    height: 48rpx * 2;
                    font-size: 40rpx;
                    text-align: center;
                    line-height: 48rpx * 2;
                    border-radius: 50%;
                    margin-right: 15rpx;
                    color: #fff;
                    background-color: #488AF6;
                }

                .table-l-info {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    height: 48rpx * 2;

                    text {
                        overflow: scroll;
                        white-space: nowrap;
                    }

                    &>text:nth-child(1) {
                        width: 48rpx * 2;
                        margin-top: 10rpx;
                        font-size: 32rpx;
                        font-weight: 500;
                        color: #292C39;
                    }

                    &>text:nth-child(2) {
                        width: 52rpx * 2;
                        height: 40rpx;
                        line-height: 40rpx;
                        text-align: center;
                        font-size: 24rpx;
                        font-weight: 400;
                        border-radius: 4px;
                        color: #488AF6;
                        background-color: #d5e4f9;
                    }
                }
            }
        }
    }

    .table-r {
        position: absolute;
        left: 156rpx * 2;
        width: 100%;

        .table-header {
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            width: 100%;
            background-color: #F7F8FA;

            text {
                display: flex;
                align-items: center;
                justify-content: center;
                height: 88rpx;
                line-height: 88rpx;
                text-align: center;
                font-size: 28rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                color: #86909C;
                border-right: 1px solid #F2F3F5;
            }

            text::after {
                content: '?';
                display: inline-block;
                font-size: 20rpx;
                width: 25rpx;
                height: 25rpx;
                margin-left: 7rpx;
                line-height: 25rpx;
                text-align: center;
                color: #d1d1d1;
                border-radius: 50%;
                border: 1px solid #d1d1d1;
            }
        }
    }

    .table-body {
        display: flex;
        width: 100%;

        .table-item {
            display: flex;
            flex-direction: row;
            align-items: center;

            .item-info {
                display: inline-block;
                text-align: center;
                width: 160rpx;
                height: 160rpx;
                line-height: 160rpx;
                font-size: 28rpx;
                color: #86909C;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                border-bottom: 1px solid #F2F3F5;
                border-right: 1px solid #F2F3F5;
                background-color: #fff;
            }
        }
    }
}

.loading {
    text-align: center;
    margin-top: 30rpx;
    color: rgb(138, 138, 138);
    font-size: 25rpx;
}

.popup {
    background: #fff;
}

.example-body {
    flex-direction: column;
    flex: 1;
}

.content {
    padding: 15px;
    background-color: #F6F6F6;
}

.text {
    font-size: 14px;
    color: #666;
    line-height: 20px;
}

@mixin flex {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: row;
}

@mixin height {
    /* #ifndef APP-NVUE */
    height: 100%;
    /* #endif */
    /* #ifdef APP-NVUE */
    flex: 1;
    /* #endif */
}

.box {
    @include flex;
}

.button {
    @include flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    height: 35px;
    margin: 0 5px;
    border-radius: 5px;
}

.example-body {
    background-color: #fff;
    padding: 10px 0;
}

.button-text {
    color: #fff;
    font-size: 12px;
}

.popup-content {
    @include flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    height: 50px;
    background-color: #fff;
}

.popup-height {
    @include height;
    width: 200px;
}

.text {
    font-size: 12px;
    color: #333;
}

.popup-success {
    color: #fff;
    background-color: #e1f3d8;
}

.popup-warn {
    color: #fff;
    background-color: #faecd8;
}

.popup-error {
    color: #fff;
    background-color: #fde2e2;
}

.popup-info {
    color: #fff;
    background-color: #f2f6fc;
}

.success-text {
    color: #09bb07;
}

.warn-text {
    color: #e6a23c;
}

.error-text {
    color: #f56c6c;
}

.info-text {
    color: #909399;
}

.dialog,
.share {
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-direction: column;
}

.dialog-box {
    padding: 10px;
}

.dialog .button,
.share .button {
    /* #ifndef APP-NVUE */
    width: 100%;
    /* #endif */
    margin: 0;
    margin-top: 10px;
    padding: 3px 0;
    flex: 1;
}

.dialog-text {
    font-size: 14px;
    color: #333;
}

.trantions_top {
    padding: 0 32rpx;
    background-color: #fff;
}

.whole_shu {
    color: rgba(41, 44, 57, 0.70);
    font-size: 24rpx;
    font-weight: 400;
    line-height: 28rpx;

}

.whole_bai {
    height: 32rpx;
    line-height: 32rpx;
    color: rgba(41, 44, 57, 0.40);
}

.whole_input_left_name {
    color: rgba(41, 44, 57, 0.70);
    font-size: 28rpx;
    font-weight: 400;
    line-height: 32rpx;
    margin-right: 10rpx;
}

.whole_input_left {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.whole_input_left::after {
    content: '|';
    display: inline-block;
    font-size: 20rpx;
    margin: 0 20rpx 0 30rpx;
    color: #a1a1a1;
}

.whole_con {
    padding-top: 32rpx;
    margin-top: -312rpx;
}

.names {
    width: 48rpx;
    height: 48rpx;
    border-radius: 50%;
    text-align: center;
    line-height: 48rpx;
    color: #fff;
    font-size: 20rpx;
    background: #488AF6;
}

.whole_list_flex {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.whole_list_num {
    color: #000;
    margin-top: 16rpx;
    font-size: 32rpx;
    text-align: center;
    font-weight: 500;
}

.whole_list_names {
    color: rgba(41, 44, 57, 0.40);
    font-size: 24rpx;
    font-weight: 400;
}

.whole_list_department {
    padding: 8rpx 16rpx;
    background: rgba(72, 138, 246, 0.20);
    color: #488AF6;
    text-align: center;
    border-radius: 8rpx;
    font-size: 24rpx;
    font-weight: 400;
}

.whole_list {
    width: 100%;
    padding: 32rpx;
    margin: 32rpx 0 0;
    background: #fff;
    border-radius: 16rpx;
}

.whole_list_name {
    color: #292C39;
    font-size: 32rpx;
    font-weight: 500;
    margin: 0 24rpx;
}

.whole_list_header {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
    margin-bottom: 32rpx;
}

.whole_input {
    width: 100%;
    height: 80rpx;
    padding: 20rpx;
    margin-bottom: 32rpx;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    background-color: #F7F8FA;
}

.whole_middle {
    color: rgba(41, 44, 57, 0.70);
    font-size: 28rpx;
    font-weight: 400;
    line-height: 36rpx;
    margin: 32rpx 0;
}

.proportion_whole {
    width: 1722rpx;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding-left: 32rpx;
}

page {
    background: #F6F6F6;
}

.proportion_foot_right {
    color: #292C39;
    font-size: 24rpx;
    font-weight: 400;
}

.foot {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
}

.proportion_foot_left {
    color: rgba(41, 44, 57, 0.70);
    font-size: 22rpx;
    font-weight: 400;
    line-height: 32rpx;
}

.middle {
    color: #292C39;
    font-size: 40rpx;
    font-weight: 500;
    margin: 28rpx 0;
}

.proportion {
    padding: 20rpx;
    width: 300rpx;
    background: #fff;
    border-radius: 16rpx;
    margin-right: 32rpx;
    margin-top: 20rpx;

    &.proportion:last-child {
        margin-right: 0;
    }
}

.name {
    margin-left: 16rpx;
    color: rgba(41, 44, 57, 0.70);
    font-size: 24rpx;
    font-weight: 400;
}

.header {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
}

.whole {
    width: 100%;
    height: 286rpx;
    background: linear-gradient(180deg, rgba(99, 165, 255, 0.40) 0%, rgba(255, 255, 255, 0.00) 100%);
}

.weektop {
    // margin-left: 10rpx;
    // width: 25%;
    margin-top: 30rpx;
    white-space: nowrap;
    text-align: center;
    // padding-left:  32rpx;
}

.weekbili {
    white-space: nowrap;
    margin: 15rpx 0 20rpx 0;
}

// 本周
.weeked {
    box-sizing: border-box;
    padding: 14rpx 24rpx;
    border-radius: 16rpx;
    margin-right: 24rpx;
    font-size: 24rpx;
    color: rgba(41, 44, 57, 0.40);
    background: #f8f8f8;
    display: inline-block;

    // padding-left: 32rpx;
    /* 必要，导航栏才能横向*/
    &.weeked:last-child {
        margin-right: 0;
    }
}

.weekeds {
    padding-left: 32rpx;
    // background: #f8f8f8;
    display: inline-block;
}

.activite {
    box-sizing: border-box;
    padding: 14rpx 24rpx;
    border-radius: 16rpx;
    color: #fff;
    font-size: 24rpx;
    background: #488AF6
}
</style>