<template>
  <view>
    <view class="list">
      <view class="info row">
        <view>跟进提醒</view>
        <text>2022.07.22</text>
      </view>
      <view class="content">您的客户【大同天下】需要跟进,请及时处理。</view>
    </view>
    <view class="list">
      <view class="info row">
        <view>跟进提醒</view>
        <text>2022.07.22</text>
      </view>
      <view class="content">您的客户【大同天下】需要跟进,请及时处理。</view>
    </view>
  </view>
</template>
<style lang="scss" scoped>
page {
  background: #f6f6f6;
  padding: 12px;
  color: #2e3c4e;
}
.list {
  padding: 24rpx;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  .info {
    align-items: center;
    margin-bottom: 24rpx;
    view {
      flex: 1;
      font-size: 32rpx;
      font-weight: 500;
    }
    text {
      font-size: 22rpx;
      color: #8a929f;
    }
  }
}
</style>
