<template>
    <view>
        <view id="map" @click="loadingmap">

        </view>
    </view>
</template>
<script>
export default{
    data() {
        return {
            
        }
    },
    onLoad(){
        // this.loadingmap()
    },
    methods:{
        loadingmap(){
            var T = window.T
        //    console.log(window.T);
               this.tdtMap = new T.Map('map');
               this.tdtMap.centerAndZoom(new T.LngLat(117.163139, 35.079158), 12);
        }
    },
    
    
}
</script>
<style scoped lang="scss">
#map {
    width: 100%;
    height: 1540rpx;
    // background-color: palevioletred;
}
</style>