<template>
<view class="filter-wrapper">
    <view class="filter-bar">
        <view class="filter-bar-item" :class="{opened: tab.name===curTab.name}" v-for="tab in tabs" :key="tab.name" @click="toggleFilterDropdown(tab)">
            <view class="title">{{ tab.label || tab.title }}</view>
            <view class="icon"></view>
        </view>
    </view>

    <view class="filter-dropdown-container" v-if="isShowDropdown" @touchmove.stop.prevent="stopMove">
        <view class="filter-dropdown">
            <labelFilter v-if="curTab.name == 'label'" :data="labelList" :multiple="curTab.items[0].multiple" v-model="curTab.items[0].value" :label.sync="curTab.items[0].label"></labelFilter>

            <template v-else>
                <scroll-view scroll-y class="filter-body-scroll">
                    <view class="filter-content"> 
                        <view class="filter-group" v-for="(item, index) in curTab.items" :key="item.name" v-show="item.visible !== false">
                            <template v-if="item.list && item.list.length">
                                <view class="filter-group-title" v-if="item.title">
                                    {{item.title}}
                                    <text v-if="item.multiple" class="label">（多选）</text>
                                </view>
                                <view class="filter-list">

                                    <template v-if="item.name=='custom_time'">
                                        <tDatePicker :value-format="item.valueFormat || 'YYYY-MM-DD'" v-model="v.map.value"  v-for="v in item.list" :key="v.value" @change="handleDateChange(item, index, v)">
                                            <view class="filter-list-item filter-date-item" :class="{ active: item.multiple ? item.value.includes(v.value) : item.value === v.value }">
                                                <text v-if="v.map.value">{{v.map.value}}</text>
                                                <text class="placeholder" v-else> {{ v.label }}</text>
                                            </view>
                                        </tDatePicker>
                                    </template>
                                    <template v-else>
                                        <view class="filter-list-item" v-for="v in item.list" :key="v.value"
                                            :class="{ active: item.multiple ? item.value.includes(v.value) : item.value === v.value }" @click="handleClickFilterItem(index, item, v)"
                                        >{{ v.label }}</view>
                                    </template>

                                </view>
                            </template>
                        </view>
                    </view>
                </scroll-view>
            </template>

            <view class="filter-dropdown-footer">
                <view class="btn btn-reset" @click="handleReset">清空</view>
                <view class="btn" @click="handleConfirm">确定</view>
            </view>
        </view>
        <view class="mask" @click="closeFilterDropdown"></view>
    </view>
</view>
</template>

<script>
import utils from '@/common/utils/customer-options.js';
import { getCrmCustomTabs } from '@/common/utils/customer.js';
import labelFilter from './label_filter.vue';
import tDatePicker from '@/components/tplus/tDatePicker';

export default {
    props: {
        current: { type: String, default: 'my' },
        popupVisible: { type: Boolean, default: false},
        params: { type: Object, default: ()=>({}) },
    },
    components: {
        labelFilter,
        tDatePicker
    },
    data() {
        return {
            isShowDropdown: false,          //是否显示 filter 下拉菜单
            tabs: [],
            curTab: {},
            curTabParams: {},
            labelList: [],
            emitParams: {},
            isEmited: false,
            showPopup: false,
        }
    },
    computed: {
        isTrans(){
            return this.current === 'trans';
        },
        isSeas(){
            return this.current === 'seas';
        },
        isPotential(){
            return this.current === 'potential';
        },
    },
    watch: {
        showPopup() {
            this.showPopup = true;
        },
        hidePopup() {
            this.showPopup = false;
        },
        popupVisible(val){
            if(val === false){
                this.closeFilterDropdown()
            }
        },
        isShowDropdown(val){
            this.$emit('update:popupVisible', val);
        },
        params: {
            handler(){
                if(JSON.stringify(this.params) === JSON.stringify(this.emitParams)){
                    return;
                }
                for(const name in this.params){
                    const value = this.params[name];
                    for(const tab of this.tabs){
                        for(const item of tab.items){
                            if(item.name === name){
                                const listItem = item.list.find(e => e.value === value)
                                if(listItem){
                                    item.value = value;
                                    item.label = listItem.label;
                                }
                            }else{
                                for(const listItem of item.list){
                                    if(listItem.map && listItem.map.name === name && listItem.map.value === value){
                                        item.value = listItem.value;
                                        item.label = listItem.label;
                                    }
                                }
                            }
                        }
                    }
                }
                for(const tab of this.tabs){
                    if(tab.label != null){
                        tab.label = this.getTabLabel(tab.items);
                    }
                }
            },
            immediate: true
        }
    },
    mounted() {
        this.tabs = this.getTabs();
        this.getTabsItems();
    },
    methods: {
        toggleFilterDropdown(tab){
            if(this.curTab.name == tab.name){
                this.closeFilterDropdown()
            }else{
                if(this.isShowDropdown){
                    this.restoreCurentTabCheckedItems();
                }
                this.showFilterDropdown(tab)
            }
        },
        //显示 filter 下拉菜单
        showFilterDropdown(tab){
            this.curTab = tab;
            this.curTabParams = {};
            this.isEmited = false;
            for(const item of tab.items){
                this.curTabParams[item.name] = item.multiple ? {value: [...item.value], label: [...item.label]} : {value: item.value, label: item.label};
            }
            this.isShowDropdown = true
        },
        //隐藏 filter 下拉菜单
        hideFilterDropdown(){
            this.curTab = {};
            this.isShowDropdown = false;
        },
        closeFilterDropdown(){
            this.restoreCurentTabCheckedItems();
            this.hideFilterDropdown();
        },
        //恢复选中项
        restoreCurentTabCheckedItems(){
            if(this.curTab.items){
                for(const item of this.curTab.items){
                    item.value = this.curTabParams[item.name].value;
                    item.label = this.curTabParams[item.name].label;
                }

                this.restoreCurentTabVisibleItems();
            }
        },
        //恢复显示/隐藏项
        restoreCurentTabVisibleItems(){
            let visibleName = '', hideNames = [];
            for(const item of this.curTab.items){
                if(item.hasLinkage){
                    for(const sub of item.list){
                        if(sub.linkage){
                            if(sub.value === item.value){
                                visibleName = sub.linkage; 
                            }else{
                                hideNames.push(sub.linkage);
                            }
                        }
                    }
                } 
            }
            if(visibleName || hideNames.length){
                for(const citem of this.curTab.items){
                    if(citem.name === visibleName){
                        citem.visible = true;
                    }else if(hideNames.includes(citem.name)){
                        citem.visible = false;
                    }
                }
            }
        },
        //点击 filter 下拉菜单项
        handleClickFilterItem(index, item, { value, label, title }){

            if(item.multiple){
                let index = item.value.indexOf(value);
                if(index !== -1){
                    item.value.splice(index, 1)
                    item.label.splice(index, 1)
                }else{
                    item.value.push(value)
                    item.label.push(title ?? label)
                }
            }else{
                if(item.cancleAble !== false && item.value === value){
                    item.value = '';
                    item.label = ''
                }else{
                    item.value = value;
                    item.label = title ?? label;
                }
            }

            if(item.hasLinkage){
                this.restoreCurentTabVisibleItems();
            }


            //取消与tab相同的项
            this.isEmited = false;
            if(item.name == 'custom_tab' && item.value == value){
                for(const tab of this.tabs){
                    for(const tabItem of tab.items){
                        if(tabItem.name !== 'custom_tab'){
                            if(tabItem.multiple){
                                tabItem.value = [];
                                tabItem.label != null && (tabItem.label = []);
                            }else{
                                tabItem.value = '';
                                tabItem.label != null && (tabItem.label ='');
                            }
                        }
                    }

                    if(tab.name === this.curTab.name){
                        this.showFilterDropdown(tab);
                    }
                }
                this.isEmited = true;
                this.emitFilter();
            }

            //取消自定义时间
            if(item.name == 'date_type'){
                for(const tab of this.tabs){
                    for(const tabItem of tab.items){
                        if(tabItem.name == 'custom_time'){
                            tabItem.value = [];
                            for(const tabItemItem of tabItem.list){
                                tabItemItem.map.value = '';
                            }
                        }
                    }
                }
            }
        },
        
        //重置下拉筛选项
        handleReset(){
            for(const item of this.curTab.items){
                item.value = item.default != null ? item.default : (item.multiple ? [] : '');
                if(item.visible !== undefined){
                    item.visible = item.defaultVisible || false
                }

                item.label &&  (item.label = item.multiple ? [] : '');
            }
            this.hideFilterDropdown();
            this.emitFilter()
        },
        //确认下拉筛选项
        handleConfirm(){
            this.hideFilterDropdown();
            if(!this.isEmited){
                this.emitFilter()
            }else{
                this.isEmited = false;
            }
        },
        emitFilter(){
            const params = {};
            for(const tab of this.tabs){
                for(const item of tab.items){

                
                    if(Array.isArray(item.value) && item.value.length){
                        const values = [...item.value];
                        let i = 0;
                        for(const val of item.value){
                            const seledListItem = item.list.find(e => e.value === val);
                            if(seledListItem && seledListItem.map){
                                params[seledListItem.map.name] = seledListItem.map.value;
                                values.splice(i--, 1)
                            }
                            i++;
                        }

                        if(values.length){
                            params[item.name] = values;
                        }
                    }else if(item.value !== ''){
                        const seledListItem = item.list.find(e => e.value === item.value);
                        if(seledListItem && seledListItem.map){
                            params[seledListItem.map.name] = seledListItem.map.value;
                        }else{
                            params[item.name] = item.value;
                        }
                    }
                }
                if(tab.label != null){
                    tab.label = this.getTabLabel(tab.items);
                }
            }
            
            this.emitParams = params;
            this.$emit('filter', params);
        },
        getTabLabel(items){
            let curLabel = '';
            for(const item of items){
                if(item.multiple){
                    curLabel = item.label && item.label.length ? item.label.join(',') : '';
                }else if(item.value){
                    curLabel = item.label;
                }
                if(curLabel){
                    break;
                }
            }
            return curLabel;
        },
        //获取筛选项
        getTabsItems(){
            for(const tab of this.tabs){
                for(const item of tab.items){
                    switch(item.name){
                        //客户状态
                        case 'tracking_id':
                            utils.getStatusList().then(data => {
                                //过滤他司成交
                                if(this.isTrans && data){
                                    data = data.filter( e => e.types != 5)
                                }
                                item.list = data || [];
                            }).catch(e => console.log(e))
                            break;
                        case 'label':
                            utils.getLabelList().then(data => {
								this.labelList = data
                            }).catch(e => console.log(e))
                            break;
                        case 'type':
                            utils.getTypeList().then(data => {
                                item.list = data || [];
                            }).catch(e => console.log(e))
                            break;
                        case 'level_id':
                            utils.getLevelList().then(data => {
                                item.list = data || [];
                            }).catch(e => console.log(e))
                            break;
                        case 'source_id':
                            utils.getSourceList().then(data => {
                                item.list = data || [];
                            }).catch(e => console.log(e))
                            break;
                        case 'custom_tab':
                            let tabType = 1;
                            if(this.isSeas){
                                tabType = 2;
                            }else if(this.isPotential){
                                tabType = 3;
                            }

                            getCrmCustomTabs(tabType).then(data => {
                                item.list = (data || []).map(e => {
                                    e.value = e.id + e.path.substring(e.path.indexOf('?'));
                                    e.label = e.name;
                                    return e;
                                });
                            }).catch(e => console.log(e))
                            break;
                    }
                }
            }
        },
        getTabs(){
            const dateType = { name: 'date_style', title: '时间类型', label:'', list: [
                { label: '创建时间', value: 1 },
                { label: '跟进时间', value: 2 },
                { label: '线索时间', value: 3 },
                { label: '更新时间', value: 4 },
                { label: '掉公时间', value: 5 },
                { label: '转公时间', value: 6 },
                { label: '带看时间', value: 8 },
                { label: '转交时间', value: 10 },
            ], value: '' }; 
            const sortType = { name: 'date_sort', title: '', label:'', list: [
                { label: '降序', value: 1 },
                { label: '升序', value: 2 },
            ], value: 1, default: 1 }; 
            const status = { name: 'tracking_id', title: '客户状态', multiple: true, value: [], label:[], list: [] };
            const label = { name: 'label', title: '客户标签', multiple: true, value: [], label:[], list: [] };
            const type = { name: 'type', title: '客户类型', label:'', list: [], value: '' };
            const level =  { name: 'level_id', title: '客户等级', multiple: true, value: [], label:[], list: [] };
            const bindQw = { name: 'is_bind', title: '绑定企微', label:'', list: [
                { label: '已绑定', value: 1 },
                { label: '未绑定', value: 2 },
            ], value: '' };
            const source =  { name: 'source_id', title: '客户来源',  multiple: true, value: [], label:[], list: [] };
            const time =  { name: 'date_type', title: '筛选时间', label:'', list: [
                { label: '全部', value: 0, map: {name: 'date_type', value: '' } },
                { label: '今天', value: 1 },
                { label: '昨天', value: 2 },
                { label: '本周', value: 3 },
                { label: '上周', value: 4 },
                { label: '本月', value: 5 },
                { label: '上月', value: 6 },
			], value: '' };
            const callStatus = { name: 'call_status', title: '通话状态', label: '', list: [
                { label: '未联系', value: 1 },
                { label: '未接通', value: 2 },
                { label: '已接通', value: 3 },
            ], value: '' };
            const takeStatus = { name: 'take_status', title: '带看状态', label: '', list: [
                { label: '未带看', value: 1 },
                { label: '已带看', value: 2 },
                { label: '有复看', value: 3 },
            ], value: '' };
            const customTabs =  { name: 'custom_tab', title: '自定义', label: '', list: [], value: '' };
            const customTimes = { name: 'custom_time', title: '', label:[],  multiple: true, list: [
                { label: '开始时间', value: 1, map: {name: 'start_date', value: '' } },
                { label: '结束时间', value: 2, map: {name: 'end_date', value: '' } },
            ], value: [] };

            switch(this.current) {
                case 'my':
                    const myType = { name: 'c_type2', title: '全部', list: [
                        { label: '全部', value: 0 },    
                        { label: '已认领', value: 1 },
                        { label: '已跟进', value: 2 },
                        { label: '未跟进', value: 3 },
                        { label: '私客', value: 4 },
                        { label: '即将掉公', value: 5 },
                        { label: '已带看', value: 7 },
                        //{ label: '已成交', value: 8 },
                    ], label:'', value: 0, default: 0 };
                    const c_type6 = { name: 'c_type6', title: '我参与的', list: [ 
                        { label: '我录入的', value: 1 },
                        { label: '我带看的', value: 3 },
                        //{ label: '我成交的', value: 4 },
                        { label: '我转公的', value: 5 },
                        { label: '我掉公的', value: 6 },
                        { label: '共享客户', value: 8, map: {name: 'shared', value: 2} },  //我共享的
                        { label: '共享给我的', value: 7, map: {name: 'shared', value: 1} },
                    ], label:'', value: '' };
                    return [
                        { name: 'dateType', title: '全部',  label:'', items: [ myType, c_type6, customTabs, dateType, sortType,time, customTimes]},
                        { name: 'status', title: '状态',  label:'', items: [ status ]},
                        { name: 'label', title: '标签', label:'', items: [ label ]},
                        { name: 'more', title: '更多', label: null, items: [ type, level, bindQw, callStatus, takeStatus, source ]},
                    ];
                case 'seas':
                    const c_type5 = { name: 'c_type5', title: '', visible: true, defaultVisible: true, list: [
                        { label: '待分配', value: 1 },    
                        { label: '待跟进', value: 2 },
                    ], label:'', value: '' };
                    const c_type2 = { name: 'c_type2', title: '', visible: false, list: [
                        { label: '已认领', value: 1 },    
                        { label: '未跟进', value: 3 },
                    ], label:'', value: '' };
                    const seasType = { name: 'seas_type', title: '客户状态', list: [
                        { label: '新增线索', value: 1, map: {name: 'c_type1', value: 1}, linkage: 'c_type5' },
                        { label: '最近跟进', value: 2, map: {name: 'c_type2', value: 2}, linkage: 'c_type2' },
                        { label: '多条线索', value: 3, map: {name: 'c_type1', value: 3} },
                        { label: '最新活跃', value: 4, map: {name: 'c_type1', value: 2} },
                    ], label:'', value: 1, default: 1, hasLinkage: true, cancleAble: false };
                    return [
                        { name: 'dateType', title: '新增线索',  label:'', items: [ seasType, c_type5, c_type2, customTabs, dateType, sortType,time, customTimes]},
                        { name: 'status', title: '状态',  label:'', items: [ status ]},
                        { name: 'label', title: '标签', label:'', items: [ label ]},
                        { name: 'more', title: '更多', label: null, items: [ type, level, bindQw, callStatus, takeStatus, source]},
                    ];
                case 'potential':
                    const potentialStatus = { name: 'c_type4', title: '客户状态', label:'', list: [
                        { label: '全部', value: 0 },
                        { label: '转公', value: 1 },
                        { label: '掉公', value: 2 },
                    ], value: 0, default: 0 };
                    return [
                        { name: 'dateType', title: '全部',  label:'', items: [ potentialStatus, customTabs, dateType, sortType,time, customTimes]},
                        { name: 'status', title: '状态',  label:'', items: [ status ]},
                        { name: 'label', title: '标签', label:'', items: [ label ]},
                        { name: 'more', title: '更多', label: null, items: [ type, level, bindQw, callStatus, takeStatus, source]},
                    ];
                case 'my_trader':
                case 'useless':
                    return [
                        { name: 'dateType', title: '全部',  label:'', items: [ dateType, sortType,time, customTimes ]},
                        { name: 'status', title: '状态',  label:'', items: [ status ]},
                        { name: 'label', title: '标签', label:'', items: [ label ]},
                        { name: 'more', title: '更多', label: null, items: [ type, level, bindQw, callStatus, takeStatus, source]},
                    ];
                case 'seas_trader':
                    const clientType  = { name: 'client_type', title: '客户分类', label:'', list: [
                        { label: '公海客户', value: 1 },
                        { label: '潜在客户', value: 2 },
                        { label: '成员私客', value: 3 },
                    ], value: '' }; 
                    return [
                        { name: 'dateType', title: '全部',  label:'', items: [ dateType, sortType,time, customTimes ]},
                        { name: 'status', title: '状态',  label:'', items: [ status ]},
                        { name: 'label', title: '标签', label:'', items: [ label ]},
                        { name: 'more', title: '更多', label: null, items: [ clientType, type, level, bindQw, callStatus, takeStatus, source]},
                    ];
                case 'trans':
                    const transType = { name: 'c_type2', title: '全部', list: [
                        { label: '全部', value: 0 },    
                        { label: '已跟进', value: 2 },
                        { label: '未跟进', value: 3 },
                        { label: '已带看', value: 7 },
                        { label: '我转交的', value: 8 , map: {name: 'related_member', value: 1}},
                        { label: '我复制的', value: 9 , map: {name: 'related_member', value: 2}},
                        
                    ], label:'', value: 0, default: 0 };
                    dateType.list = [
                        { label: '创建时间', value: 1 },
                        { label: '跟进时间', value: 2 },
                        { label: '线索时间', value: 3 },
                        { label: '更新时间', value: 4 },
                    ];
                    //流转客后端暂未添加多选
                    status.multiple = false;status.value = '';status.label = '';
                    label.multiple = false;label.value = '';label.label = '';
                    level.multiple = false;level.value = '';level.label = '';
                    source.multiple = false;source.value = '';source.label = '';
                    
                    customTimes.valueFormat = "YYYY-MM-DD HH:ii:ss";
     
                    return [
                        { name: 'dateType', title: '全部',  label:'', items: [ transType, dateType, sortType,time, customTimes ]},
                        { name: 'status', title: '状态',  label:'', items: [ status ]},
                        { name: 'label', title: '标签', label:'', items: [ label ]},
                        { name: 'more', title: '更多', label: null, items: [ type, level, callStatus, source]},
                    ];
           }
            return [];
        },
        stopMove(){
            return false;
        },
        handleDateChange(group, index, { value, title, label}){
            let itemIndex = group.value.indexOf(value);
            if(itemIndex === -1){
                group.value.push(value)
                group.label.push(title ?? label)
            }

            let i = this.curTab.items.findIndex(e=>e.name == 'date_type');
            if(i!== -1){
                this.curTab.items[i].value = '';
            }
        }
    }
}
</script>

<style scoped lang="scss"> 
.filter-wrapper{
    position: relative;
    .filter-dropdown-container{
        position: absolute;
        z-index: 10;
        left: 0;
        top: 88rpx;
        width: 100%;
        .filter-dropdown{
            background-color: #fff;
            .filter-body-scroll{
                max-height: 45vh;
            }
            .filter-content{
                padding: 24rpx 0 8rpx 32rpx;
                .filter-group{
                    padding-top: 8rpx;
                    .filter-group-title{
                        flex-direction: row;
                        align-items: center;
                        font-size: 32rpx;
                        padding: 0 0 24rpx 0;
                        .label{
                            color: #9c9c9c;
                            font-size: 24rpx;
                            font-weight: 400;
                        }
                    }
                }
                
                .filter-list{
                    display: flex;
                    flex-direction: row;
                    flex-wrap: wrap;
                    .filter-list-item{
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        height: 76rpx;
                        font-size: 28rpx;
                        color: #4E5969;
                        background-color: #f6f6f6;
                        min-width: calc( (100vw - 112rpx) / 3);
                        margin: 0 24rpx 24rpx 0;
                        padding: 0 4rpx;
                        &.filter-date-item{
                            padding: 0 20rpx;
                        }
                        &.active{
                            color: #2d84fb;
                            background-color: #e5eeff;
                        }
                    }
                }
            }

            .filter-dropdown-footer{
                display: flex;
                flex-direction: row;
                padding: 32rpx 20rpx;
                border-top: 0.5px solid #F2F3F5;
                .btn{
                    flex: 1;
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    justify-content: center;
                    height: 80rpx;
                    margin: 0 12rpx;
                    font-size: 32rpx;
                    color: #fff;
                    background-color: #2d84fb;
                    border-radius: 8rpx;
                    &.btn-reset{
                        color: #4E5969;
                        background-color: #f2f3f5;
                        
                    }
                }
            }
        }
        .mask{
            position: fixed;
            z-index: -1;
            width: 100%;
            height: 100vh;
            left: 0;
            background-color: #000;
            opacity: 0.5;
            transition: 0.26s;
        }
    }
}
.filter-bar{
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 88rpx;
    background-color: #fff;
    border-bottom: 1px solid #F0F1F5;
    padding: 0 18rpx;
    .filter-bar-item{
        flex: 1;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 0 14rpx;
        overflow: hidden;
        &.opened{
            .title{
                color: #2d84fb;
            }
            .icon{
                margin-top: -4px;
                transform: rotate(180deg);
                border-top-color: #2d84fb;
            }
        }
        .title{
            display: inline-block;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-size: 28rpx;
            color: rgba(41, 44, 57, 0.4);
        }
        .icon{
            width: 0;
            height: 0;
            margin: 4px 0 0 8px;
            border: 4px solid transparent;
            border-top-color: #b8b9ba;
        }
    }
}
::v-deep{
    .picker-input-box{
        flex: none;
    }
}
</style>