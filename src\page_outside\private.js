import { copyText } from './tools/index'
import { navigateTo } from './tools/index'
import ajax from './tools/ajax'
export const copyPrivate = (e) => {
  //   let price = ''
  //   if (e.trade_type == 1 || e.trade_type == 3) {
  //     if (e.sale_price) {
  //       price = '售价：' + Number(e.sale_price) / 10000 + '万元'
  //     } else {
  //       price = '售价：' + '面议'
  //     }
  //   }
  //   if (e.trade_type == 2) {
  //     if (e.rent_price) {
  //       price = '租金：' + e.rent_price + '元/月'
  //     } else {
  //       price = '租金：' + '面议'
  //     }
  //   }
  //   let content = `房源名称：${e.title}
  // 户型：${e.shi}室${e.ting}厅${e.wei}卫
  // 商圈：${e.area_name}- ${e.region_name}
  // 楼层：${e.sz_floor || ''}/${e.total_floor || ''}层
  // 楼栋：${e.loudong}- ${e.danyuan}-${e.fanghao}
  // ${price}
  // 电话：${e.owner_tel || ''}`
  let content = `🎉${e.title}${e.shi}室${e.ting
    }厅${e.wei}卫 / 公盘编号：${e.id}
💎小区：${e.title}   
🛏户型：${e.shi}室${e.ting}厅${e.wei}卫
👍楼层：${e.sz_floor}/${e.total_floor}层
🛏装修：${e.zhuangxiu}
🏡面积：${e.mianji}㎡
🎁价格：${e.trade_type_status == 1 || e.trade_type_status == 3 ?
      e.sale_price / 10000 + "万" : e.rent_price + "元/月"}${
        e.unilateral_agent == 0
          ? `
👩联系人：${
              this.detail.current_login_user
                ? this.detail.current_login_user.user_name
                : ""
            }`
          : ""
      }
☎️联系电话：${e.current_login_user ? e.current_login_user.phone : ''}`
  copyText(content, () => {
    uni.showToast({
      title: '复制成功',
      icon: 'none',
    })
    return new Promise((resolve) => {
      resolve()
    })
  })
}
export const tixing = (e) => {
  navigateTo(`/house/remind?remind_id=${e.id}`)
}
export const toPrivateDetail = (e) => {
  navigateTo({
    name: 'private_detail',
    query: {
      id: e.id,
    },
  })
}

export const followPrivate = (e, type) => {
  navigateTo(`/house/follow_up?follow_id=${e.id}&from=${type}`)
}

export const addToLianmai = (house) => {
  // showModal({
  //   title: '确定发布到联卖房源吗？',
  //   confirm: () => {
  //     ajax.get(`/v1/wapLm/releaseToLm/${house.id}`).then((res) => {
  //       uni.showToast({
  //         title: res.data.message,
  //         icon: 'none',
  //       })
  //     })
  //   },
  // })
}
export const setCompanyHouse = (e) => {
  // return new Promise((resolve, reject) => {
  //   showModal({
  //     title: '确定设置该房源为公司房源吗？',
  //     confirm: () => {
  //       ajax.get(`/v1/wapLm/privateHousesToShare/${e.id}`).then((res) => {
  //         uni.showToast({
  //           title: res.data.message,
  //           icon: 'none',
  //         })

  //         if (res.data.status == 200) {
  //           // this.$set(e, 'is_share', 1)
  //           resolve(e)
  //         } else {
  //           reject(e)
  //         }
  //       })
  //     },
  //   })
  // })
}

export const houseDel = (e) => {
  return new Promise((resolve) => {
    uni.showModal({
      title: '是否删除该房源？',
      confirm: () => {
        ajax.post('/v1/wapLm/editPrivateHouse', { id: e.id, is_del: 1 }, (res) => {
          uni.showToast({
            title: res.data.message,
            icon: 'none',
          })
          if (res.data.status == 200) {
            resolve()
          }
        })
      },
    })
  })
}

export const houseTop = (e) => {
  return new Promise((resolve) => {
    uni.showModal({
      title: '是否置顶该房源？',
      success: (result) => {
        if (result.confirm) {
          ajax.post('/v1/wapLm/editPrivateHouse', { id: e.id, is_top: 1 }, (res) => {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
            })
            if (res.data.status == 200) {
              resolve()
            }
          })
        } else if (result.cancel) {
          console.log('用户点击取消');
        }

      },
    })
  })
}
