<template>
  <view class="apply_applove">
    <!-- <title-bar backgroundColor="#fff" :title="title" @goBack="goBack"> </title-bar> -->
    <view class="form">
      <!--  -->
      <view class="form-item bottom-line">
        <view class="left flex-row">
          <view class="label">{{ showGonggong ? '审批类型' : '更改状态' }}</view>
          <picker
            class="flex-1"
            @change="changeType"
            range-key="name"
            :value="currentIndex"
            :range="approveList"
          >
            <label class="">{{ currentApprove }}</label>
          </picker>
        </view>
      </view>

      <template v-for="item in approve">
        <view class="form-item bottom-line" :key="item.name">
          <view class="left flex-row" :class="{ 'items-center': item.show_type !== 4 }">
            <view class="label">{{ item.title }}</view>
            <my-input
              v-if="item.show_type == 1 && item.type != 'date'"
              v-model="form[item.name]"
              size="big"
              class="flex-1"
              :placeholder="'请输入' + item.title"
              :unit="item.units"
            ></my-input>
            <my-input
              v-if="item.type == 'string' && item.show_type == 4"
              v-model="form[item.name]"
              size="big"
              class="flex-1"
              :placeholder="'请输入' + item.title"
              type="textarea"
            >
            </my-input>
            <selectDown
              v-if="item.type == 'select'"
              valueName="name"
              :list="item.options"
              :multiple="item.show_type == 3 ? true : false"
              @input="changeSelect($event, item)"
              :defaultValue="item.default"
              :placeholder="'请选择' + item.title"
            >
            </selectDown>
            <template v-if="item.type == 'date'">
              <UniDatetimePicker
                v-if="item.date_format.length >= 12"
                :value="form[item.name] | formatDate(item.date_format)"
                @change="changeDate($event, item)"
              ></UniDatetimePicker>
              <picker
                v-else
                mode="date"
                :value="form[item.name]"
                @change="changeDate($event, item)"
              >
                <view class="uni-input">{{ date | formatDate(item.date_format) }}</view>
              </picker>
            </template>
          </view>
        </view>
      </template>
      <template v-if="showGonggong">
        <view class="form-item bottom-line">
          <view class="left flex-row">
            <view class="label">备注</view>
            <my-input
              type="textarea"
              v-model="form.remarks"
              size="big"
              class="flex-1"
              placeholder="请输入备注"
            ></my-input>
          </view>
        </view>
        <view class="form-item">
          <view class="left flex-row">
            <view class="label">添加凭证</view>

            <myUpload
              @uploadDone="uploadSuccess($event, 'attachment')"
              :action="upload_api"
              :del="false"
              :upInfo="upInfo"
              :chooseType="1"
              :maxCount="10"
              :imgs="attachment"
            ></myUpload>
          </view>
        </view>
        <view class="form-item bottom-line">
          <view class="left flex-row">
            <view class="label">审批人</view>
            <view class="memberList flex-row items-center flex-wrap">
              <view class="member" v-for="item in approvals" :key="item.id">
                {{ item.user_name }}
              </view>
              <!-- <view class="add member" v-if ="!hasApprovals" @click="addApproval">添加</view> -->
            </view>
          </view>
        </view>
      </template>

      <myButton
        style="margin-left: 160rpx; margin-top: 24rpx"
        type="primary"
        :round="false"
        :loading="submiting"
        @click="submit"
        >{{ showGonggong ? '提交申请' : '确定提交' }}</myButton
      >
    </view>
    <my-popup
      :show="show_add_approval"
      ref="show_select_member"
      @close="show_add_approval = false"
      position="top"
    >
      <view class="selcted_member">
        <selectMember
          v-if="show_add_approval"
          :id="0"
          from="2"
          :values="recipientsList"
          @selectMemberOk="selectedAppreovels"
          @cancel="cancelAppreovels"
        ></selectMember>
      </view>
    </my-popup>
    <my-popup
      :show="show_add_recipients"
      ref="show_select_member"
      @close="show_add_recipients = false"
      position="top"
    >
      <view class="selcted_member">
        <selectMember
          v-if="show_add_recipients"
          :id="0"
          from="2"
          :values="recipients"
          @selectMemberOk="selectedRecipients"
          @cancel="cancelRecipients"
        ></selectMember>
      </view>
    </my-popup>
  </view>
</template>

<script>
import myButton from './components/myButton'
import myInput from './components/myInput'
import myCheckbox from './components/myCheckbox'
import myUpload from './components/upload'
import UniDatetimePicker from './components/uni-datetime/components/uni-datetime-picker/uni-datetime-picker'
import { formatDate } from '@/page_outside/formatDate'
import selectDown from './components/w-select'
import myPopup from '@/components/myPopup'
export default {
  components: {
    myInput,
    myCheckbox,
    UniDatetimePicker,
    myUpload,
    myButton,
    selectDown,
    // titleBar,
    myPopup,
    // selectMember,
  },
  data () {
    return {
      form: {},
      approve: [],
      recipients: [],
      approvers: [],
      date: '',
      attachment: [],
      upload_api: '/uploadHousePhoto',
      submiting: false,
      title: '提交申请',
      show_add_approval: false,
      approvals: [],
      selectedIds: [],
      hasApprovals: 1,
      show_add_recipients: false,
      recipientsList: [],
      selectedRecipientsIds: [],
      approveList: [],
      currentApprove: '请选择类型',
      currentIndex: 0,
      approvals: [],
      upload_api: '/common/file/upload/admin',
      upInfo: {
        category: 6,
      },
      showGongong: false,
      shenpiInfo: {

      },
      showGonggong: true,
      show_template: true,
      from: "house", //来源 默认房源详情  customer 为客户详情
    }
  },
  onLoad (options) {
    if (options.id) {
      this.id = options.id
    }
    if (options.house_id) {
      this.form.sys_hid = options.house_id
    }
    if (options.from) {
      this.from = options.from
    }
    if (options.type) {
      let typeArr = options.type.split("_"),
        value = typeArr[0],
        f_value = typeArr[1];
      this.getApprove(this.id, value)
      this.f_trade_status = f_value

    } else {
      this.getApprove(this.id)
      this.form.f_trade_status = 9
    }
    if (this.id == 1) {
      this.shenpiInfo = uni.getStorageSync('shenpi') ? JSON.parse(uni.getStorageSync('shenpi')) : null
    }


  },
  filters: {
    formatDate (val, params) {
      console.log(val, params)
      if (params.length == 19) {
        params = 'YYYY-MM-DD HH:mm:ss'
      } else if (params.length >= 12) {
        params = 'YYYY-MM-DD HH:mm'
      } else {
        params = 'YYYY-MM-DD'
      }
      if (!val) {
        return formatDate(new Date(), params)
      } else {
        return val
      }
    },
  },
  beforeDestroy () {
    uni.removeStorageSync("shenpi")
  },
  methods: {
    // 根据模块id 获取审批模型
    getApprove (id, value = '') {
      this.$ajax.get(`/admin/house/approveType/${id}`, {}, (res) => {
        console.log(res);
        if (res.statusCode == 200) {
          if (this.id == 1) {
            if (this.shenpiInfo && this.shenpiInfo.is_del == 1) {
              res.data = res.data.filter(item => item.values != 21)
            }
            if (this.shenpiInfo && this.shenpiInfo.is_state == 1) {
              res.data = res.data.filter(item => item.values != 19)
            }
            if (this.shenpiInfo && this.shenpiInfo.is_trader == 1) {
              res.data = res.data.filter(item => item.values != 18)
            }
          }
          this.approveList = res.data
          if (value) {
            let i = this.approveList.findIndex(item => item.values == value)
            if (i >= 0) {
              this.changeType({ detail: { value: i } })
              this.getAuditList(this.approveList[i].values)
            } else {
              this.changeType({ detail: { value: 0 } })
              this.getAuditList(this.approveList[0].values)
            }
          } else {
            this.changeType({ detail: { value: 0 } })
            this.getAuditList(this.approveList[0].values)
          }


        }
      })
    },
    changeType (e) {
      this.currentIndex = e.detail.value
      this.currentApprove = this.approveList[this.currentIndex].name
      this.form.cat_id = this.approveList[this.currentIndex].values
      this.approve = []
      if (this.approveList[this.currentIndex].is_model) {
        this.getModelList(this.form.cat_id)
      } else {
        if (this.shenpiInfo && this.shenpiInfo.is_del == 2) {
          this.showGonggong = false
        }
      }

    },
    // 获取审批人员
    getAuditList (id) {
      this.$ajax.get(`/admin/house/approvePerson/${id}`, {}, (res) => {
        console.log(res);
        if (res.statusCode == 200) {
          this.approvals = res.data


        }
      })

    },
    // 通过id 获取自定义模板
    getModelList (id) {
      this.$ajax.get(`/admin/house/approveFields/${id}/${this.form.sys_hid}`, {}, (res) => {
        console.log(res);
        if (res.statusCode == 200) {
          //隐藏客户姓名、联系方式、收入费用
          if(Array.isArray(res.data)){
            const noNeedFields = ['f_name','f_tel','f_commission'];
            res.data = res.data.filter(e => !noNeedFields.includes(e.name)).map(e => {
              if(e.name == 'f_cj_type'){
                if(this.$Utils.isNumeric(e.default)){
                  e.default = e.default*1
                }
              }
              return e;
            });
          }

          this.approve = res.data || [];
          this.approveBack = []
          res.data.map(item => {
            this.approveBack.push(JSON.parse(JSON.stringify(item)))
          })
          if (this.f_trade_status && this.id == 1) {
            if (this.approve && this.approve.length && this.approve[0].options && this.approve[0].options.length) {
              console.log(this.approve[0].options, "12312321");
              let curr = this.approve[0].options.find(item => item.values == this.f_trade_status)
              console.log(curr);
              if (curr && this.shenpiInfo && this.shenpiInfo.stateList && this.shenpiInfo.stateList.length) {
                let arrs = []
                this.shenpiInfo.stateList.map(item => {
                  if (this.shenpiInfo.state_list.includes(item.id + '')) {
                    arrs.push(item)
                  }
                  return item
                })
                let select = arrs.findIndex(item => item.title == curr.name)
                if (select >= 0) {
                  this.showGonggong = true
                } else {
                  this.showGonggong = false
                }
              }
            }
          }
          console.log(this.showGonggong);
          this.setDefaultValue()
          // this.changeType({detail:{value:0}})

        }
      })

    },
    setDefaultValue () {
      this.approve.map((item) => {
        if (item.type == 'date' && !this.form[item.name]) {
          if (item.date_format.length == 19) {
            item.date_format = 'YYYY-MM-DD HH:mm:ss'
          } else if (item.date_format.length >= 12) {
            item.date_format = 'YYYY-MM-DD HH:mm'
          } else {
            item.date_format = 'YYYY-MM-DD'
          }
          console.log(item);
          this.form[item.name] = formatDate(new Date(), item.date_format)
          console.log(this.form[item.name]);
        }
        if (item.type == "select" && item.default) {
          item.default = item.options[0].name
          this.form[item.name] = item.options[0].values
        }

        if (item.name == 'f_trade_status') {
          if (this.f_trade_status) {
            let curr = item.options.find(item => item.values == this.f_trade_status)
            if (curr) {
              item.default = curr.name
              this.form.f_trade_status = curr.values
            } else {
              this.form.f_trade_status = 9
              item.default = "有效"
            }


          } else {
            this.form.f_trade_status = 9
            item.default = "有效"
          }
        }
      })
      console.log(this.f_trade_status);
    },
    selected (e, item) {
      if (e.length) {
        this.form[item.name] = e
      }
    },
    changeSelect (e, type) {
      console.log(this.form, type);
      console.log(e, 123);

      if (type.name == 'f_cj_type' && e && e.length && e[0].values == 2) {
        this.approve = this.approveBack.filter(item => item.name == 'f_cj_type')
      } else {
        this.approve = this.approveBack
      }
      if (this.id == 1 && this.shenpiInfo) {
        let arrs = []
        this.shenpiInfo.stateList.map(item => {
          if (this.shenpiInfo.state_list.includes(item.id + '')) {
            arrs.push(item)
          }
          return item
        })
        if (e && e.length) {
          console.log(arrs, e[0]);
          let select = arrs.findIndex(item => item.title == e[0].name)
          console.log(select);
          if (select == -1) {
            this.showGonggong = false
          } else {
            this.showGonggong = true
          }
        } else {
          this.showGonggong = true
        }
      }
      let arr = []
      e.map(item => {
        arr.push(item.values)
      })
      this.form[type.name] = arr.join(",")

    },
    uploadSuccess (res, type) {
      this[type] = res
      console.log(res);
      this.form[type] = res
    },
    onRemove (e, type) {
      console.log(e, type)
      this[type] = this[type].filter((item) => item.url !== e)
      this.form[type] = this[type]
    },
    changeDate (e, item) {
      if (e.detail) {
        this.form[item.name] = e.detail.value
      } else {
        if (item.date_format.length == 19) {
          this.form[item.name] = e
        } else {
          this.form[item.name] = e.substr(0, e.lastIndexOf(':'))
        }
      }
    },
    addApproval () {
      this.title = '选择审批人员'
      this.show_add_approval = true
    },
    cancelAppreovels () {
      this.show_add_approval = false
      this.title = '提交申请'
    },
    selectedAppreovels (e) {
      this.selectedIds = e.selected
      this.approvals = e.selectedNode
      this.show_add_approval = false
      this.title = '提交申请'
    },

    addRecipients () {
      this.title = '选择抄送人员'
      this.show_add_recipients = true
    },
    cancelRecipients () {
      this.show_add_recipients = false
      this.title = '提交申请'
    },
    selectedRecipients (e) {
      this.selectedRecipientsIds = e.selected
      this.recipientsList = e.selectedNode
      this.show_add_recipients = false
      this.title = '提交申请'
    },
    goBack () {
      if (this.show_add_approval) {
        this.title = '提交申请'
        this.show_add_approval = false
      } else if (this.show_add_recipients) {
        this.title = '提交申请'
        this.show_add_approval = false
      } else {
        this.$navigateBack()
      }
    },
    submit () {
      if (!this.showGonggong) {
        uni.showModal({
          title: "提示",
          content: `确认该操作吗？`,
          success: (res) => {
            if (res.confirm) {
              this.confirmSubmit()
            }
          },
        });
      } else {
        this.confirmSubmit()
      }
    },
    confirmSubmit () {
      let params = Object.assign({}, this.form)
      let auditPerson = []
      this.approvals.map(item => {
        auditPerson.push(item.id)
      })
      params.approver_uid = auditPerson

      let item = this.approve.find((item) => {
        if (item.required && !params[item.name]) {
          return item
        }
      })
      if (item) {
        uni.showToast({
          title: item.title + '不能为空',
          icon: 'none',
        })
        return
      }

      for (const key in params) {
        if (
          typeof params[key] == 'object' &&
          Object.prototype.toString.call(params[key]) == '[object Array]'
        ) {
          params[key] = params[key].join(',')
        }
      }
      if (!this.showGonggong) {
        params.approver_uid = ''
      }
      this.submiting = true
      this.$ajax
        .post('/admin/house/addApprove', params
          , (res) => {
            console.log(res)
            if (res.statusCode == 200) {
              if (params.cat_id == 21) {
                this.$navigateTo("/customer/seas_list")
                // uni.navigateBack(2)
              } else {
                setTimeout(() => {
                  uni.$emit("getDataAgain")
                }, 200);
                uni.navigateBack()
              }

            } else {
              uni.showToast({
                title: res.data.message,
                icon: "none"
              })
              this.submiting = false
            }
          })

    },
  },
}
</script>

<style lang="scss" scoped>
.apply_applove {
  padding: 0 48rpx 48rpx;
}
.form {
  .form-item {
    padding: 34rpx 0;
    // margin-bottom: 24rpx;
    .label {
      font-size: 32rpx;
      color: #2e3c4e;
      margin-right: 24rpx;
      min-width: 154rpx;
    }
    ::v-deep textarea {
      background: #fcfcfc;
    }
    .memberList {
      .member {
        border: 2rpx solid #888;
        padding: 10rpx 20rpx;
        margin-bottom: 20rpx;
        border-radius: 10rpx;
        color: #888;
        margin-right: 10rpx;
      }
    }
    .up_load_img {
      width: 300rpx;
      height: 200rpx;
    }
    ::v-deep .uni-date-x--border {
      border: 0;
      flex-direction: row;
    }
    ::v-deep .uni-date {
      margin-top: -10rpx;
    }
    .left {
      flex: 1;
      position: relative;
      .select_content {
        align-items: flex-end;
      }
    }
    .input {
      font-size: 36rpx;
      // text-align: right;
    }
    .tip {
      font-size: 22rpx;
      color: #dde1e9;
    }
  }
}
.selcted_member {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  overflow-y: auto;
}
</style>
