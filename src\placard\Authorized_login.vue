<template>
    <view>
        <view class="log_on">
            <image src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/img/login-img.png?x-oss-process=style/w_400"
                class="imgbg">
            </image>
            <button :class="checked ? 'hava-value' : 'btn'" class="btn btn-left" @click="getInfo">
                <myIcon type="weixin" style="margin-right: 10rpx" color="#fff"></myIcon>
                快捷登录
            </button>
            <view class="radio-box row">
                <view class="radio-content row">
                    <radio class="radio-form" :checked="checked" @click="changeRadio" />
                    <text class="row">
                        我已阅读并同意<text style="text-decoration: underline" @click="openContent(3)">《隐私政策》</text>及<text
                            style="text-decoration: underline" @click="openContent(4)">《用户服务协议》</text>
                    </text>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
import myIcon from "@/components/my-icon.vue";
export default {
    components: {
        myIcon,
    },
    data() {
        return {
            checked: false,
            code: '',
            website_id: "",
            resInfo:""
        }
    },
    onLoad(options) {
        console.log(12312);
        console.log(options);
        if (options.code) {
            this.code = options.code
            this.website_id = options.website_id
            // alert(this.code)
              this.login()
            return
        }
    },
    methods: {
        changeRadio() {
            this.checked = !this.checked;
        },
        //跳转隐私政策
        openContent() {

        },
        getInfo() {
            if (!this.checked) {
                uni.showToast({
                    title: "请阅读并同意相关内容",
                    icon: "none",
                });
                return;
            }
            window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx30363bce2ccc42ef&redirect_uri=${window.location.href}&response_type=code&scope=snsapi_userinfo&state=#wechat_redirect`;
        },
        login() {
            this.$ajax.post("/auth/poster/login", { website_id: this.website_id, code: this.code }, res => {
                console.log(res);
                if (res.status == 200) {
                    this.resInfo = JSON.stringify(res.data)
                }
            })
        }
    }
}
</script>
<style scoped lang="scss">
.log_on {
    padding: 24rpx 48rpx;

    image {
        height: 280rpx;
        width: 100%;
        border-radius: 24rpx;
        margin-bottom: 88rpx;
    }

    .radio-content {
        font-size: 28rpx;
        color: #d8d8d8;
        margin: 48rpx 0;
        align-items: center;

        .radio-form {
            transform: scale(0.7);
        }
    }

    .btn {
        color: #fff;
        text-align: center;
        font-size: 36rpx;
        width: 100%;
        // height: 112rpx;
        opacity: 0.6;
        background: #0174ff;
        border-radius: 44rpx;
    }

    .hava-value {
        opacity: 1;
    }
}
</style>