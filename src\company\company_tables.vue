<template>
  <!-- 
  根据进入页面地址栏参数不同显示不同信息
  type='company' 显示公司概况信息
  type='reported' 显示会员报备信息
 -->
  <view class="list">
    <view class="desc">
      <view class="title">{{ tables_company.title }}</view>
      <view class="top-desc row">
        <view class="top-desc-box">
          <view class="top-ctn">{{ tables_company.employee_total }}</view>
          <view class="top-amount">{{
            company_info.employee_total || company_info["0"] || 0
          }}</view>
        </view>
        <view class="top-desc-box">
          <view class="top-ctn">{{ tables_company.customer_total }}</view>
          <view class="top-amount">{{
            company_info.customer_total || company_info["1"] || 0
          }}</view>
        </view>
      </view>
      <view class="top-desc row">
        <view class="top-desc-box">
          <view class="top-ctn">{{ tables_company.reported_total }}</view>
          <view class="top-amount">{{
            company_info.customer_reported_total || company_info["2"] || 0
          }}</view>
        </view>
        <view class="top-desc-box">
          <view class="top-ctn">{{ tables_company.deal_total }}</view>
          <view class="top-amount">{{
            company_info.deal_total || company_info["4"] || 0
          }}</view>
        </view>
      </view>
      <view class="top-desc row">
        <view class="top-desc-box">
          <view class="top-ctn">{{
            tables_company.already_commission_total
          }}</view>
          <view class="top-amount">{{
            company_info.settled_brokerage_amount || company_info["5"] || 0
          }}</view>
        </view>
        <view class="top-desc-box">
          <view class="top-ctn">{{ tables_company.not_commission_total }}</view>
          <view class="top-amount">{{
            company_info.settle_brokerage_amount || company_info["10"] || 0
          }}</view>
        </view>
      </view>
      <view class="top-desc row" v-if="type == 'company'">
        <view class="top-desc-box">
          <view class="top-ctn">{{ tables_company.visit_total }}</view>
          <view class="top-amount">{{ company_info.visit_total || 0 }}</view>
        </view>
        <view class="top-desc-box" style="width:14%"> </view>
      </view>
    </view>
    <view class="filter row">
      <picker
        class="picker-box "
        @change="bindPickerChange"
        :value="index"
        :range="array"
        range-key="desc"
      >
        <view class="uni-input">{{ array[index].desc }}</view>
        <view class="filter-btn">筛选</view>
      </picker>
    </view>
    <view class="tables">
      <t-table @change="change">
        <t-tr class="back">
          <t-th v-for="(item, index) in tables_th" :key="index">{{
            item
          }}</t-th>
        </t-tr>
        <t-tr
          v-for="(item, index) in tableList"
          :key="item.id"
          @click="clickCustomer(item, index)"
          class="name"
        >
          <!-- <t-td>{{ item.name || item.nickname || item.user_name || 0 }}</t-td> -->
          <t-td>{{ item.name || item.nickname || item.user_name || 0 }} </t-td>
          <t-td>{{ item.customer_total || item.status_0 || 0 }}</t-td>
          <t-td>{{ item.customer_reported_total || item.status_1 || 0 }}</t-td>
          <t-td v-if="item.visit_total >= 0">{{ item.visit_total }}</t-td>
          <t-td>{{ item.deal_total || item.status_2 || 0 }}</t-td>
          <!-- 判断company和report进入显示是否有数据 -->
          <t-td v-if="item.settled">{{ item.settled | million }}</t-td>
          <t-td v-else>{{ item.status_3 }}</t-td>
          <t-td v-if="item.not_settle">{{ item.not_settle | million }}</t-td>
          <t-td v-else>{{ item.status_4 }}</t-td>
          <t-td v-if="item.status_5">{{ item.status_5 || 0 }}</t-td>
          <t-td v-if="item.status_10">{{ item.status_10 || 0 }}</t-td>
        </t-tr>
      </t-table>
      <load-more :status="load_status"></load-more>
    </view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import tTable from "@/components/t-table/t-table.vue";
import tTh from "@/components/t-table/t-th.vue";
import tTr from "@/components/t-table/t-tr.vue";
import tTd from "@/components/t-table/t-td.vue";
import loadMore from "@/components/loadMore";
export default {
  components: {
    tTable,
    tTh,
    tTr,
    tTd,
    loadMore,
  },
  data() {
    return {
      type: "",
      tableList: [],
      tables_company: {
        title: "公司概述",
        employee_total: "公司员工总数",
        customer_total: "公司客户总数",
        reported_total: "公司报备总数",
        deal_total: "公司成交总数",
        already_commission_total: "已结佣金总数",
        not_commission_total: "未结佣金总数",
        visit_total: "到访量",
      },
      tables_report: {
        title: "类型概述",
        employee_total: "待审核报备总数",
        customer_total: "已报备报备总数",
        reported_total: "已带看报备总数",
        deal_total: "已认购报备总数",
        already_commission_total: "已成交报备总数",
        not_commission_total: "已失效报备总数",
        visit_total: "到访量",
      },
      tables_th: [],
      array: [
        { desc: "今天", value: "day", id: 1 },
        { desc: "昨天", value: "yesterday", id: 2 },
        { desc: "本周", value: "week", id: 3 },
        { desc: "本月", value: "month", id: 4 },
        { desc: "上月", value: "last_month", id: 5 },
        { desc: "季度", value: "quarter", id: 6 },
        { desc: "今年", value: "year", id: 7 },
      ],
      index: 0,
      // 选择时间范围
      params: {
        date_str: "day",
        page: 1,
      },
      company_info: {},
      showName: false,
      show_name: "",
      company_id: "",
      load_status: "",
    };
  },
  onLoad(options) {
    this.company_id = options.company_id;
    this.params.all = options.all || "";
    this.type = options.type;
    if (options.type === "company") {
      this.tables_th = [
        "姓名",
        "客户量",
        "报备量",
        "到访量",
        "成交量",
        "已结佣金",
        "未结佣金",
      ];
      this.getCompanyInfo();
      this.getCompanyUser();
    } else if (options.type === "reported") {
      this.getReportedInfo();
      this.getReportedUser();
      for (var key in this.tables_company) {
        this.tables_company[key] = this.tables_report[key];
        this.tables_th = [
          "姓名",
          "待审核",
          "已报备",
          "已带看",
          "已认筹",
          "已认购",
          "已成交",
          "已失效",
        ];
      }
    }
  },
  methods: {
    clickCustomer(item, index) {
      if (this.type === "company") {
        this.$navigateTo(
          `/company/query_reported_info?user_id=${item.id}&date_str=${this.params.date_str}`
        );
      } else {
        uni.showToast({
          title: "经纪人名称：" + item.name,
          icon: "none",
          duration: 3000,
        });
      }
    },
    bindPickerChange: function(e) {
      this.index = e.target.value;
      this.params.date_str = this.array[this.index].value;
      this.params.page = 1;
      if (this.type === "company") {
        this.getCompanyInfo();
        this.getCompanyUser();
      } else if (this.type === "reported") {
        this.getReportedInfo();
        this.getReportedUser();
      }
    },
    getCompanyInfo() {
      this.$ajax.get(
        `/client/company/statistics/company?company_id=${this.company_id}`,
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.company_info = res.data;
          } else {
            uni.showToast({
              title: res.data.message || "获取数据失败",
              icon: "none",
            });
          }
        }
      );
    },
    getCompanyUser() {
      if (this.params.page === 1) {
        this.tableList = [];
      }
      this.load_status = "loading";
      this.$ajax.get(
        `/client/company/statistics/company/list?company_id=${this.company_id}`,
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.tableList = this.tableList.concat(res.data.data);
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
              uni.showToast({
                title: "没有更多数据了",
                icon: "none",
              });
            }
          } else {
            uni.showToast({
              title: res.data.message || "获取数据失败",
              icon: "none",
            });
          }
        }
      );
    },
    getReportedInfo() {
      this.$ajax.get(
        `/client/company/statistics/customer/analyze?company_id=${this.company_id}`,
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.company_info = res.data;
          } else {
            uni.showToast({
              title: res.data.message || "获取数据失败",
              icon: "none",
            });
          }
        }
      );
    },
    getReportedUser() {
      if (this.params.page === 1) {
        this.tableList = [];
      }
      this.load_status = "loading";
      this.$ajax.get(
        `/client/company/statistics/customer/analyze/list?company_id=${this.company_id}`,
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.tableList = this.tableList.concat(res.data.data);
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
              uni.showToast({
                title: "没有更多数据了",
                icon: "none",
              });
            }
          } else {
            uni.showToast({
              title: res.data.message || "获取数据失败",
              icon: "none",
            });
          }
        }
      );
    },
  },
  onPullDownRefresh() {
    if (this.type === "company") {
      this.tables_th = [
        "姓名",
        "客户量",
        "报备量",
        "到访量",
        "成交量",
        "已结佣金",
        "未结佣金",
      ];
      this.type = this.type;
      this.getCompanyInfo();
      this.getCompanyUser();
    } else if (this.type === "reported") {
      this.type = this.type;
      this.getReportedInfo();
      this.getReportedUser();
      for (var key in this.tables_company) {
        this.tables_company[key] = this.tables_report[key];
        this.tables_th = [
          "姓名",
          "待审核",
          "已报备",
          "已带看",
          "已认筹",
          "已认购",
          "已成交",
          "已失效",
        ];
      }
    }
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    if (this.type === "company") {
      this.tables_th = [
        "姓名",
        "客户量",
        "报备量",
        "到访量",
        "成交量",
        "已结佣金",
        "未结佣金",
      ];
      this.type = this.type;
      this.getCompanyInfo();
      this.getCompanyUser();
    } else if (this.type === "reported") {
      this.type = this.type;
      this.getReportedInfo();
      this.getReportedUser();
      for (var key in this.tables_company) {
        this.tables_company[key] = this.tables_report[key];
        this.tables_th = [
          "姓名",
          "待审核",
          "已报备",
          "已带看",
          "已认筹",
          "已认购",
          "已成交",
          "已失效",
        ];
      }
    }
  },
};
</script>

<style scoped lang="scss">
page {
  background: #eeeeee;
}
.list {
  .desc {
    background: #fff;
    width: 100%;
    .title {
      margin: 24rpx;
      font-size: 32rpx;
      padding-left: 8rpx;
      border-left: 6rpx solid #3566ff;
    }
    .top-desc {
      margin: 24rpx;
      border-bottom: 2rpx solid #eee;
      justify-content: space-around;
      .top-desc-box {
        line-height: 60rpx;
        align-items: center;
        .top-ctn {
          color: #999;
        }
        .top-amount {
          font-weight: bold;
          font-size: 36rpx;
          color: #3566ff;
        }
      }
    }
  }
  .filter {
    margin: 20rpx 0;
    line-height: 60rpx;
    font-size: 32rpx;
    .picker-box {
      display: flex;
      width: 100%;
      height: 60rpx;
      position: relative;
      .uni-input {
        align-items: center;
        position: absolute;
        left: 0;
        width: 80%;
        background: #fff;
      }
      .filter-btn {
        margin-left: 20rpx;
        background: #fff;
        align-items: center;
        width: 18%;
        position: absolute;
        right: 0;
      }
    }
  }
  .back {
    background: #3566ff;
  }
  // 表格名称点击显示全部
  .tables {
    position: relative;
  }
}
</style>
