<template>
  <view class="list">
    <tabBar
      ref="tab_bar"
      :top="0"
      :tabs="category_list"
      :nowIndex="
        category_list.findIndex((item) => item.value == params.category)
      "
      @click="onClickCate"
    ></tabBar>

    <view class="photos-list top-line">
      <view
        class="item bottom-line row"
        v-for="item in house_list"
        :key="item.id"
        @click="$previewImage(item.img)"
      >
        <view class="left">
          <image :src="item.img | imageFilter('w_220')"></image>
        </view>
        <view class="right">
          <view class="title"
            >{{ item.total_room }}室{{ item.total_salloon }}厅{{
              item.total_washroom
            }}卫</view
          >
          <view class="ctn row">
            <text class="right-line">建面{{ item.area }}㎡</text>
            <text style="padding-left:24rpx">{{ item.name }}</text>
          </view>
        </view>
      </view>
    </view>
    <load-more :status="load_status"></load-more>
  </view>
</template>

<script>
import tabBar from "@/components/tabBar";
import loadMore from "@/components/loadMore";
export default {
  components: { tabBar, loadMore },
  data() {
    return {
      category_list: [],
      params: {
        category: 0,
      },
      build_id: "",
      house_list: [],
      load_status: "",
    };
  },
  onLoad(options) {
    this.build_id = options.build_id;
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "BUILD_HOUSE_TYPE_CATEGORY":
            this.category_list = [
              { description: "全部", value: 0 },
              ...item.childs,
            ];
            break;
        }
      });
    });
    this.getDataList();
  },
  methods: {
    onClickCate(e) {
      this.params.category = e.value;
      this.getDataList(this.params.category);
    },
    getDataList(e) {
      this.load_status = "loading";
      this.$ajax.get(
        `/common/project/all/house_type/${this.build_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            if (e) {
              this.house_list = res.data.filter((item) => {
                return item.category == e;
              });
            } else {
              this.house_list = res.data;
            }
            this.load_status = "loadend";
            if (res.data.length === 0) {
              this.load_status = "nomore";
            }
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        }
      );
    },
  },
  onPullDownRefresh() {
    this.getDataList();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>

<style scoped lang="scss">
.list {
  .photos-list {
    padding: 0 48rpx;
    margin-top: 100rpx;
    margin-bottom: 120rpx;
    .item {
      padding: 44rpx 0;
      .left {
        width: 290rpx;
        height: 208rpx;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .right {
        margin-left: 24rpx;
        .title {
          font-size: 32rpx;
          font-weight: bold;
        }
        .ctn {
          margin-top: 24rpx;
          .right-line {
            padding-right: 24rpx;
          }
        }
      }
    }
  }
}
</style>
