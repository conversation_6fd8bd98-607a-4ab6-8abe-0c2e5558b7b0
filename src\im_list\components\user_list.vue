<template>
  <view class="user-list">
    <uniSwipeAction>
      <uniSwipeActionItem
        class="msg-list bottom-line row"
        v-for="(item, index) in msg_list"
        :key="index"
      >
        <view class="row" style="width:100%" @click="onClick(item)">
          <view class="left-img">
            <image
              :src="item.u_avatar | imageFilter('w_80')"
              mode="aspectFill"
            ></image>
          </view>
          <view class="right-ctn ">
            <view class="right-ctn-top row">
              <view class="top-left row">
                <view class="name">
                  {{ item.u_name || item.u_nickname || item.u_user_name }}
                </view>
                <view
                  class="label"
                  v-if="item.c_from_category !== '' && type === 1"
                  >{{ fromatStatus(item.c_from_category) }}</view
                >
              </view>
              <view class="top-right" v-if="item.unread_msg_total > 0">{{
                item.unread_msg_total >= 100 ? "99" : item.unread_msg_total
              }}</view>
            </view>
            <view class="right-ctn-bottom row">
              <view class="bottom-left">{{
                item.uvt_description || formatMsg(item.im_m_content)
              }}</view>
              <view class="bottom-right">{{
                item.im_m_created_at_str || item.uvt_created_at_str
              }}</view>
            </view>
          </view>
        </view>
        <template v-slot:right>
          <view class="remove" @click="delData(item)">删除</view>
        </template>
      </uniSwipeActionItem>
    </uniSwipeAction>
  </view>
</template>

<script>
import uniSwipeAction from "./uni-swiper-action/uni-swipe-action/uni-swipe-action";
import uniSwipeActionItem from "./uni-swiper-action/uni-swipe-action-item/uni-swipe-action-item";
export default {
  components: { uniSwipeAction, uniSwipeActionItem },
  props: {
    msg_list: Array,
    type: Number,
  },
  data() {
    return {
      status_category: [],
    };
  },
  created() {
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "CUSTOMER_FROM_CATEGORY":
            this.status_category = item.childs;
            break;
        }
      });
    });
  },
  methods: {
    formatMsg(content) {
      var msg;
      if (content) {
        try {
          msg = JSON.parse(content);
        } catch (error) {
          if (error) {
            msg = {
              type: "text",
              content: "",
            };
          }
        }
      } else {
        msg = {
          type: "text",
          content: "",
        };
      }
      switch (msg.type) {
        case "text":
          return msg.content;
          break;
        case "map":
          return "[位置]";
          break;
        case "img":
          return "[图片]";
          break;
        case "video":
          return "[视频]";
          break;
        case "apply_wx":
          return "[查看微信]";
          break;
        case "agreeSendWx":
          return "[查看微信]";
          break;
        case "voice":
          return "[语音]";
          break;
        case "issue":
          return "[查看内容]";
          break;
      }
    },
    onClick(item) {
      this.$emit("onClick", item);
    },
    fromatStatus(category) {
      let item = this.status_category.find((item) => {
        return item.value == category;
      });
      if (item) {
        return item.description;
      }
    },
    delData(item) {
      this.$emit("delData", item);
    },
  },
};
</script>

<style scoped lang="scss">
.user-list {
  overflow: hidden;
  .msg-list {
    padding: 32rpx 0;
    position: relative;
    .left-img {
      width: 96rpx;
      height: 96rpx;
      image {
        width: 96rpx;
        height: 96rpx;
        border-radius: 50%;
      }
    }
    .right-ctn {
      margin-left: 26rpx;
      width: 100%;
      justify-content: space-around;
      .right-ctn-top {
        width: 100%;
        justify-content: space-between;
        align-items: center;
        .top-left {
          .name {
            font-size: 28rpx;
            font-weight: 500;
            color: #333;
          }
          .label {
            margin-left: 20rpx;
            padding: 5rpx 10rpx;
            color: #fff;
            background: linear-gradient(-10deg, #f36771, #f5826a);
            border-radius: 4rpx;
            font-size: 18rpx;
            &.label-1 {
              background: linear-gradient(-10deg, #47e5bf, #49e5bd);
            }
            &.label-2 {
              background: linear-gradient(-10deg, #0d93ff, #2abef5);
            }
            &.label-3 {
              background: #aaaaaa;
            }
          }
        }
        .top-right {
          text-align: center;
          color: #fff;
          width: 40rpx;
          height: 40rpx;
          line-height: 40rpx;
          font-size: 18rpx;
          border-radius: 50%;
          background: #fb656a;
        }
      }
      .right-ctn-bottom {
        width: 100%;
        justify-content: space-between;
        font-size: 26rpx;
        color: #999;
        .bottom-left {
          width: 300rpx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          height: 40rpx;
          line-height: 40rpx;
        }
      }
    }
  }
  .remove {
    margin-left: 48rpx;
    height: 100%;
    width: 100rpx;
    background: #ed5d60;
    justify-content: center;
    text-align: center;
    font-size: 28rpx;
    color: #fff;
  }
}
</style>
