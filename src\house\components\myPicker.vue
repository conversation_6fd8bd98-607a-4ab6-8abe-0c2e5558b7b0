<template>
  <view class="picker-box">
    <view class="selecter" @click="showPicker">
      <slot>
        <view class="selecter-info">
          <view class="contents" v-if="all_selected && all_selected.length > 0">
            <text class="selecter_content" v-for="(item, index) in all_selected" :key="index">{{
              item | formatSelectedOk
            }}</text>
          </view>
          <view class="contents" v-else>
            <text class="selecter_placeholder">请选择</text>
          </view>
        </view>
      </slot>
    </view>
    <my-popup ref="picker" :show="show" @close="show = false">
      <view class="my-picker" v-if="options.length > 0">
        <view class="label-list flex-row">
          <view
            class="label"
            v-for="(option, index) in options"
            :key="index"
            :class="{ active: current === index }"
            @click="handleCheck(index)"
          >
            <text class="label_text">{{ option.label }}</text>
            <text class="value_name">{{ option | formatSelected }}</text>
          </view>
        </view>
        <view class="operation flex-row">
          <view class="tip">请选择{{ options[current].label }}</view>
          <view class="btn" @click="handleSelectted">确定</view>
        </view>
        <picker-view :value="current_value" @change="onChange">
          <picker-view-column v-for="(column, index) in options[current].items" :key="index">
            <view class="picker-view-column-item" v-for="(item, idx) in column.range" :key="idx">{{
              item.name
            }}</view>
          </picker-view-column>
        </picker-view>
      </view>
    </my-popup>
  </view>
</template>

<script>
import myPopup from './myPopup'
// options数据格式
// [
//   {
//     label: '户型',
//     value: [], //选中值对应items中range的索引
//     required: true,
//     items: [
//       {
//         title: '室',
//         identifier: 'shi',
//         range: [],
//       },
//       {
//         title: '厅',
//         identifier: 'ting',
//         range: [],
//       },
//       {
//         title: '卫',
//         identifier: 'wei',
//         range: [],
//       },
//     ],
//   }
// ],
export default {
  components: {
    myPopup,
  },
  data () {
    return {
      show: false,
      all_selected: [],
      current: 0,
      current_value: [],
      // options: options
    }
  },
  watch: {
    options: {
      deep: true,
      handler () {
        // if (this.watch_once) {
        //   return
        // }
        this.initValue(() => {
          this.watch_once = true
        })
      },
    },
  },
  props: {
    options: {
      type: Array,
      default: () => [],
    },
  },
  filters: {
    formatSelected (option) {
      let value = option.value
        .map((item, index) => {
          var _current = option.items[index].range.find((_item) => _item.value === item)
          if (_current) {
            return _current.name
          }
        })
        .join('')
      if (value) {
        return value
      } else {
        return '请选择'
      }
    },
    formatSelectedOk (selected) {
      if (!selected) {
        return '请选择'
      }
      const res = selected.map((item) => item.name).join('')
      return res || '请选择'
    },
  },
  created () {
    this.initValue()
  },
  methods: {
    initValue (callback) {
      this.options.forEach((item, index) => {
        if (item.value.length > 0) {
          this.options[index].selected = this.getSelected(item)
        } else {
          this.options[index].selected = []
        }
      })
      this.all_selected = this.getAllselected()
      callback && callback()
    },
    showPicker () {
      this.show = true
      // this.$refs.picker.show()
      // 初始化第一项
      this.handleCheck(0)
    },
    /**
     * 选择器选择后
     */
    onChange (e) {
      this.current_value = e.detail.value
      // 获取选中的索引
      this.options[this.current].value = this.indexToValue(
        this.options[this.current],
        e.detail.value
      )
      // 获取选中的值
      this.options[this.current].selected = this.getSelected(this.options[this.current])
    },
    // 获取某项选中的值
    getSelected (e) {
      let selected = e.value.map((item, idx) => {
        if (item >= 0) {
          let select_item = e.items[idx].range.find((_item) => _item.value === item)
          if (select_item) {
            select_item.identifier = e.items[idx].identifier
            return select_item
          } else {
            return ''
          }
        } else {
          return ''
        }
      })
      return selected
    },
    /**
     * 切换选择器选项
     */
    handleCheck (index) {
      this.current = index
      this.current_value = []
      // 初始化选中的索引为第一项
      if (this.options[index].value.length === 0) {
        let value_len = this.options[index].items.length
        while (value_len) {
          this.current_value.push(0)
          value_len--
        }
      } else {
        this.current_value = this.valueToIndex(this.options[index], this.options[index].value)
      }
      // 初始化选中项的value后执行一次onChange事件用来初始化选中的值
      this.onChange({
        detail: { value: this.current_value },
      })
    },
    indexToValue (data, index_arr) {
      return index_arr.map((item, index) => {
        return data.items[index].range[item].value
      })
    },
    valueToIndex (data, value_arr) {
      return value_arr.map((item, index) => {
        return data.items[index].range.findIndex((_item) => _item.value === item)
      })
    },
    /**
     * 点击确定
     */
    handleSelectted () {
      // 判断必选项是否必需选择且没有选择
      let no_val = this.options.find((item) => item.required && item.value.length === 0)
      let noValIndex = this.options.findIndex((item) => item.required && item.value.length === 0)
      if (no_val) {
        this.handleCheck(noValIndex)
        // uni.showToast({
        //   title: `请选择${no_val.label}`,
        //   icon: 'none'
        // })
        return
      }
      this.all_selected = this.getAllselected()
      // const value = this.options.map(item=>item.value)
      // console.log(value)
      // this.$refs.picker.hide()
      this.show = false
      this.$emit('input', this.all_selected)
      this.$emit('change', this.all_selected)
    },
    getAllselected () {
      return this.options.filter((item) => item.selected).map((item) => item.selected)
    },
  },
}
</script>

<style scoped lang="scss">
.picker-box {
  background-color: #fff;
}

.selecter {
  width: 100%;
  justify-content: space-between;
  // align-items: center;
  .contents {
    flex-direction: row;
    .selecter_placeholder {
      line-height: 1;
      // padding: 0 10rpx;
      font-size: 36rpx;
      // font-weight: bold;
      color: #8a929f;
    }
    .selecter_content {
      line-height: 1;
      padding-right: 16rpx;
      font-size: 36rpx;
      // font-weight: bold;
      color: #333;
      ~ .selecter_content {
        padding-left: 16rpx;
        border-left: 4rpx solid #333;
      }
    }
  }
}

.my-picker {
  background-color: #fff;
}
.label-list {
  .label {
    flex: 1;
    text-align: center;
    padding: 20rpx;
    border-top: 4rpx solid #fff;
    .label_text {
      font-size: 22rpx;
      margin-bottom: 6rpx;
      color: #666;
    }
    &.active {
      border-top: 4rpx solid #2d84fb;
      background-image: linear-gradient(0deg, rgba(246, 246, 246, 0) 0%, rgba(#2d84fb, 0.1) 100%);
      .value_name {
        color: #2d84fb;
      }
    }
  }
}

.operation {
  padding: 24rpx 32rpx;
  align-items: center;
  background-color: #f5f5f5;
  .tip {
    flex: 1;
    text-align: center;
    font-size: 22rpx;
    color: #999;
  }
  .btn {
    color: #2d84fb;
  }
}
picker-view {
  height: 400rpx;
}
.picker-view-column-item {
  line-height: 70rpx;
  text-align: center;
}
</style>
