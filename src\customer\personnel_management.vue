<template>
  <view class="personnel">
    <view class="search row">
      <myIcon
        class="icon"
        type="ic_sousuo3x1"
        color="#D1D1D1"
        size="24px"
      ></myIcon>
      <input
        class="uni-input"
        :disabled="true"
        type="text"
        placeholder="请输入标题关键字"
        placeholder-style="font-size:14px"
      />
    </view>
    <view class="corpname row">
      <view class="label">{{ corp_name.slice(0, 1) }}</view>
      <view class="name">{{ corp_name }}</view>
    </view>
    <view class="list">
      <view
        class="item row"
        @click="onClick(item)"
        v-for="item in tableData"
        :key="item.id"
      >
        <view class="name row">
          <image
            src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/renshibianji.png"
          ></image>
          {{ item.name }}（{{ item.number }}）</view
        >
        <myIcon color="#2E3C4E" type="you" size="16px"></myIcon>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from "@/components/my-icon.vue";
export default {
  components: {
    myIcon,
  },
  data() {
    return {
      corp_name: "腾房科技",
      tableData: [
        { id: 1, name: "研发部", number: 10 },
        { id: 2, name: "研发部2", number: 10 },
        { id: 3, name: "研发部3", number: 10 },
        { id: 4, name: "客服部", number: 10 },
      ],
    };
  },
  methods: {
    onClick(item) {
      this.$navigateTo(`/customer/personnel_list?id=${item.id}`);
    },
  },
};
</script>

<style scoped lang="scss">
.personnel {
  padding: 0 12px;
}
.search {
  padding: 8px 12px;
  align-items: center;
  background: #f8f8f8;
  border-radius: 22px;
  .uni-input {
    margin-left: 12px;
  }
}
.corpname {
  margin: 12px 0;
  align-items: center;
  .label {
    margin-right: 12px;
    padding: 4px 6px;
    border-radius: 4px;
    background: #2d84fb;
    color: #fff;
    font-size: 20px;
  }
  .name {
    color: #2e3c4e;
  }
}
.item {
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  .name {
    align-items: center;
    image {
      width: 16px;
      margin-right: 20px;
      height: 16px;
    }
  }
}
</style>
