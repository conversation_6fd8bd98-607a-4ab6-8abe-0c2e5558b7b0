<template>
  <view class="">
    <view v-if="show">
      <view class="top">
        <view class="search row">
          <view class="row">
            <picker @change="typeChange" :range="tab" range-key="name" class="lab">
              <view :class="typeVal ? '' : 'novalue'" class="row">
                <text>{{ typeVal || '全部分组' }}</text>
                <myIcon
                  type="xiala"
                  size="22rpx"
                  color="#737373"
                  style="margin: 4rpx 0 0 10rpx"
                ></myIcon>
              </view>
            </picker>
          </view>
          <view class="row input">
            <myIcon class="icon" type="ic_sousuo3x1" size="32rpx" color="#b0b0b0"></myIcon>
            <input
              class="c2"
              v-model="params.keywords"
              @confirm="onSearch"
              type="text"
              placeholder="请在这里输入"
            />
          </view>
        </view>
      </view>
      <view class="tab row">
        <!-- 文件库 -->
        <template v-if="type == 1">
          <view v-for="(item, index) in tab" :key="index">
            <text @click="tabChange(index)" :class="{ active: current == index }">
              {{ item.name }}
            </text>
          </view>
        </template>
        <!-- 营销素材 -->
        <template v-if="type == 2">
          <view v-for="(item, index) in tab1" :key="index">
            <text @click="tabChange(index)" :class="{ active: current == index }">
              {{ item.name }}
            </text>
          </view>
        </template>
      </view>
      <view class="list">
        <view class="info row" v-for="item in file_list" :key="item.id">
          <image
            class="img"
            :src="`../static/customer/${filterStatus(item.type)}.png`"
            mode="aspectFill"
          />
          <view class="tit">
            <view>{{ item.title }}</view>
            <text class="c2">{{ item.created_at }}</text>
          </view>
          <view class="row push" @click="onSendMsg(item)">
            <image src="../static/customer/fs.png" mode="widthFix" />
            <text>发送</text>
          </view>
        </view>
        <load-more :status="load_status"></load-more>
      </view>
      <!-- <BottomBar @click="switchTab" :current="currentTabIndex"></BottomBar> -->
    </view>
  </view>
</template>

<script>
import myIcon from "@/components/my-icon";
import loadMore from "@/components/loadMore.vue";
import BottomBar from "./components/tabbar";
export default {
  components: {
    myIcon,
    loadMore,
    BottomBar,
  },
  data () {
    return {
      type: 1, //1：文件库 2：营销管理
      tab: [
        { name: "全部", id: 0 },
        { name: "图片", id: 1, urlname: "wb" },
        // { name: "音频", id: 2, urlname: "mp3" },
        { name: "视频", id: 3, urlname: "lj" },
        { name: "文件", id: 4, urlname: "xcx" },
        { name: "H5", id: 5, urlname: "xcx" },
        { name: "小程序", id: 6, urlname: "xcx" },
      ],
      tab1: [
        { name: "全部" },
        { name: "文章" },
        { name: "文本" },
        { name: "图片" },
        { name: "链接" },
      ],
      typeVal: "",
      current: 0,
      currentTabIndex: 2,
      params: {
        page: 1,
        keywords: "",
        type: 0,
      },
      load_status: "",
      file_list: [],
      show: false
    };
  },
  onLoad (options) {
    if (options.type && options.type == 2) {
      this.type = options.type;
    } else {
      this.type = 1
    }
    
    let name;
    if (this.type == 1) {
      this.currentTabIndex = 2;
      name = "文件库";
      this.getDataList();
    } else {
      this.currentTabIndex = 3;
      name = "营销管理";
    }
    wx.setNavigationBarTitle({
      title: name,
    });
  },
  methods: {
    filterStatus (e) {
      return this.tab[e].urlname || "";
    },
    typeChange (e) {
      this.typeVal = this.tab[e.detail.value].name;
    },
    getDataList () {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.file_list = [];
      }
      this.$ajax.get("/qywx/file/search", this.params, (res) => {
        this.load_status = "loadend";
        if (res.statusCode === 200) {
          this.file_list = this.file_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        }
      })
    },
    tabChange (index) {

      if (this.load_status == "loading") return
      this.params.page = 1;
      this.current = index;
      this.params.type = this.tab[index].id;
      this.getDataList();
    },
    onSearch () {
      this.params.page = 1;
      this.getDataList();
    },
    switchTab (index, item) {
      if (this.currentTabIndex == index) {
        return;
      }
      this.$navigateTo(item.path);
    },
    onSendMsg (e) {
      if ([1, 2, 3, 4].includes(e.type) && !e.mediaid) {
        uni.showToast({
          title: "暂无文件内容",
          icon: "none",
        });
        return;
      }
      console.log(e);
      var type = "";
      switch (e.type) {
        case 1:
          type = "image";
          break;
        case 2:
        case 4:
          type = "file";
          break;
        case 3:
          type = "video";
          break;
        case 5:
          type = "news";
          break;
        case 6:
          type = "miniprogram";
          break;
        default:
          break;
      }
      this.wx.invoke(
        "sendChatMessage",
        {
          msgtype: type, //消息类型，必填
          enterChat: true, //为true时表示发送完成之后顺便进入会话，仅移动端3.1.10及以上版本支持该字段
          image: {
            mediaid: e.mediaid, //图片的素材id
          },
          video: {
            mediaid: e.mediaid, //视频的素材id
          },
          file: {
            mediaid: e.mediaid, //文件的素材id
          },
          news: {
            link: e.url,
            title: e.title,
            desc: e.desc,
            imgUrl: e.img_url,
          },
          miniprogram: {
            appid: e.appid,
            title: e.title,
            imgUrl: e.img_url,
            page: e.url,
          },
        },
        function (res) {
          if (res.err_msg == "sendChatMessage:ok") {
            //发送成功
            uni.showToast({
              title: "发送成功",
            });
          } else {
            if (type == "miniprogram") {
              uni.showToast({
                title: "企业需要关联小程序",
                icon: "none",
              });
            }
          }
        }
      );
    },
  },
  onReachBottom () {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>

<style lang="scss" scoped>
page {
  background: #f6f6f6;
  color: #2e3c4e;
}
.c2 {
  color: #8a929f;
}
.list {
  margin: 0 12px 75px;
  .info {
    background: #fff;
    border-radius: 6px;
    padding: 12px;
    align-items: center;
    margin-bottom: 12px;
    .tit {
      flex: 1;
      line-height: 22px;
      margin: 0 12px;
      view {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        word-break: break-all;
      }
      text {
        font-size: 11px;
      }
    }
    .img {
      width: 50px;
      height: 50px;
    }
    .push {
      color: #2d84fb;
      image {
        width: 15px;
        margin-right: 5px;
      }
    }
  }
}
.tab {
  margin: 12px;
  overflow: auto;
  view {
    margin-right: 6px;
    text {
      color: #8a929f;
      border-radius: 12px;
      padding: 5px 18px;
      white-space: nowrap;
      &.active {
        background: #fff;
        color: #2d84fb;
      }
    }
  }
}
.top {
  background: #fff;
  padding: 12px;
  .search {
    align-items: center;
    border-radius: 4px;
    background: #f6f6f6;
    padding: 10px 12px;
    .input {
      padding-left: 12px;
      margin-left: 12px;
      border-left: 1px solid #d8d8d8;
      flex: 1;
      .c2 {
        margin-left: 5px;
        font-size: 14px;
        flex: 1;
      }
    }
    > view {
      align-items: center;
    }
  }
}
</style>
