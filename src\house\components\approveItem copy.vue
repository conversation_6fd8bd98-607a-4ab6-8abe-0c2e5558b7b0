<template>
  <view class="approve_item">
    <view class="approve_item_name flex-row">
      <view class="label weight"> 模板名称 </view>
      <view class="value weight"> {{ detail.name }} </view>
    </view>
    <view class="approve_item_name flex-row">
      <view class="label">审 批 人 </view>
      <view class="value flex-row items-center flex-wrap">
        <template v-if="detail.approvers && detail.approvers.length">
          <view class="appr flex-row items-center" v-for="appr in detail.approvers" :key="appr.id">
            <view class="prelogo">
              <image :src="appr.head_image | imageFilter"></image>
            </view>
            <view class="cname"> {{ appr.cname }} </view>
          </view>
          <view class="edit flex-row items-center" @click.prevent.stop="setMember(1)">
            <image src="/static/img/edit.png"> </image>
          </view>
        </template>
        <template v-else>
          <view class="appr flex-row items-center"> 暂无审批人 </view>
          <view class="edit flex-row items-center" @click.prevent.stop="setMember(1)">
            <image src="/static/img/edit.png"> </image>
          </view>
        </template>
      </view>
    </view>
    <view class="approve_item_name flex-row">
      <view class="label">抄 送 人 </view>
      <view class="value flex-row items-center">
        <template v-if="detail.recipients && detail.recipients.length">
          <view class="appr flex-row items-center" v-for="reci in detail.recipients" :key="reci.id">
            <view class="prelogo">
              <image :src="reci.head_image | imageFilter"></image>
            </view>
            <view class="cname"> {{ reci.cname }} </view>
          </view>
          <view class="edit flex-row items-center" @click.prevent.stop="setMember(2)">
            <image src="/static/img/edit.png"> </image>
          </view>
        </template>
        <template v-else>
          <view class="appr flex-row items-center"> 暂无抄送人 </view>
          <view class="edit flex-row items-center" @click.prevent.stop="setMember(2)">
            <image src="/static/img/edit.png"> </image>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: ['detail'],
  data() {
    return {}
  },
  methods: {
    setMember(type) {
      this.$emit('setMember', type)
    },
  },
}
</script>

<style lang="scss" scoped>
.approve_item {
  background: #fff;
  border-radius: 10rpx;
  padding: 24rpx;

  .approve_item_name {
    font-size: 22rpx;
    color: #999;
    margin-bottom: 24rpx;
    .label {
      min-width: 100rpx;
    }
    .value {
      margin-left: 24rpx;
      .appr {
        margin-right: 10rpx;
        .prelogo {
          width: 28rpx;
          height: 28rpx;
          border-radius: 50%;
          overflow: hidden;
          image {
            width: 100%;
            height: 100%;
          }
        }
        .cname {
          font-size: 22rpx;
        }
      }
      .edit {
        width: 32rpx;
        height: 32rpx;
        overflow: hidden;
        image {
          width: 100%;
          height: 100%;
        }
      }
      &.no_member {
        color: #5ccbe2;
      }
      text {
        margin-right: 24rpx;
        margin-bottom: 12rpx;
      }
    }
  }
}
</style>
