<template>
  <view class="list">
    <view class="user-card">
      <view class="switch-box row" v-if="switch_text">
        <text>员工提现</text>
        <view class="row switch-row">
          <switch :checked="allow_withdraw" @change="switchChange" />
          <view class="switch-text row">
            {{ switch_text }}
          </view>
        </view>
      </view>
      <view class="ctn row" v-for="item in company_list" :key="item.id">
        <view class="user_left row">
          <image
            v-if="item.avatar"
            mode="aspectFill"
            :src="
              item.avatar
                ? item.avatar
                : 'https://dss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=2561659095,*********&fm=26&gp=0.jpg'
            "
          ></image>
          <view class="user">
            <view class="user_name row"
              >{{ item.name || item.nickname || item.user_name }}
              <text v-if="item.manager_category" class="manager">{{
                item.manager_category
              }}</text>
            </view>
            <view v-if="item.phone" class="user_phone">{{ item.phone }}</view>
          </view>
        </view>
        <view class="user_right row">
          <view
            v-if="item.can_del"
            class="kick_out btn"
            @click="kickOut(item.id)"
            >移出</view
          >
          <view class="contact btn" @click="contactPhone(item.phone)"
            >联系</view
          >
          <view class="contact btn" @click="reportedClientInfo(item.id)"
            >信息</view
          >
        </view>
      </view>
    </view>
    <load-more :status="load_status"></load-more>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore";
export default {
  components: { loadMore },
  data() {
    return {
      company_id: "",
      company_list: [],
      params: {
        page: 1,
      },
      load_status: "",
      allow_withdraw: "",
      switch_text: "",
    };
  },
  onLoad(options) {
    if (options) {
      this.company_id = options.company_id;
      this.params.all = options.all || "";
    }
    this.queryCompany();
    this.getCompanyList();
  },
  methods: {
    getCompanyList() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.company_list = [];
      }
      this.$ajax.get(
        `/client/company/employee/search?company_id=${this.company_id}`,
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.company_list = this.company_list.concat(res.data.data);
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
            }
          } else {
            this.load_status = "loadend";
            uni.showToast({
              title: res.data.message || "获取失败",
              icon: "none",
            });
          }
        }
      );
    },
    kickOut(id) {
      uni.showModal({
        title: "提示",
        content: "是否删除该经纪人",
        success: (res) => {
          if (res.confirm) {
            this.params.page = 1;
            this.$ajax.get(
              `/client/company/employee/delete/${id}`,
              {},
              (res) => {
                if (res.statusCode === 200) {
                  uni.showToast({
                    title: "删除成功",
                  });
                  this.getCompanyList();
                } else {
                  uni.showToast({
                    title: res.data.message || "删除失败",
                    icon: "none",
                  });
                }
              }
            );
          }
        },
      });
    },
    queryCompany() {
      this.$ajax.get(`/client/company/query/${this.company_id}`, {}, (res) => {
        if (res.statusCode === 200) {
          var allow_withdraw = res.data.allow_withdraw;
          this.switch_text = allow_withdraw === 0 ? "关闭" : "开启";
          this.allow_withdraw = allow_withdraw === 0 ? false : true;
        } else {
          uni.showToast({
            title: res.data.message || "获取失败",
            icon: "none",
          });
        }
      });
    },
    switchChange(e) {
      let value = e.detail.value;
      this.$ajax.post(
        "/client/company/set/withdraw",
        {
          id: this.company_id,
          allow_withdraw: value,
          all: 1,
        },
        (res) => {
          if (res.statusCode === 200) {
            uni.showToast({ title: value ? "已开启" : "已关闭" });
            this.switch_text = value ? "开启" : "关闭";
          } else {
            uni.showToast({
              title: "修改失败",
              icon: "none",
            });
          }
        }
      );
    },
    contactPhone(phone) {
      if (phone) {
        uni.makePhoneCall({
          phoneNumber: phone,
        });
      } else {
        uni.showToast({
          title: "该用户未绑定联系方式",
          icon: "none",
        });
      }
    },
    reportedClientInfo(id) {
      this.$navigateTo(`/company/query_reported_info?user_id=${id}`);
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.getCompanyList();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getCompanyList();
  },
};
</script>

<style scoped lang="scss">
.ctn {
  align-items: center;
  padding: 24rpx 48rpx;
  justify-content: space-between;
  border-bottom: 2rpx solid #eee;
  .user_left {
    align-items: center;
    image {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
    }
    .user {
      margin-left: 24rpx;
      .user_phone {
        margin-top: 12rpx;
        color: #999;
      }
    }
  }
}
.btn {
  color: #0459d2;
  align-items: center;
  line-height: 50rpx;
  border-radius: 10rpx;
  margin: 0 4rpx;
  width: 100rpx;
  height: 50rpx;
  border: 1rpx solid #0459d2;
}
.switch-box {
  border-bottom: 2rpx solid #eee;
  justify-content: space-between;
  padding: 24rpx 48rpx;
  align-items: center;
  font-size: 32rpx;
  .switch-row {
    align-items: center;
  }
}
.user_name {
  font-size: 32rpx;
  align-items: center;
}
.manager {
  font-size: 14rpx;
  margin-left: 10rpx;
  background: #0459d2;
  border-radius: 8rpx;
  color: #fff;
  padding: 4rpx 10rpx;
}
</style>
