<template>
    <view>
        <view class="top">
            <view class="filters">
                <!-- <view class="filters-item">
                    <wisdomSearchSelect placeholder="日期" v-model="params.date_value" :datas="dateGroups" :params.sync="params" auto-popup @confirm="search" @clear="search"></wisdomSearchSelect>
                </view> -->
                <view class="filters-item">
                    <wisdomAnchorAccountSelect placeholder="主播账号" v-model="params.refer_dy_id"  @select="search"></wisdomAnchorAccountSelect>
                </view>
                <view class="filters-item">
                    <wisdomAnchorPlatformSelect placeholder="平台类型" v-model="params.platform"  @select="search"></wisdomAnchorPlatformSelect>
                </view>
                <view class="filters-item">
                    <wisdomMemberSelect placeholder="成员" v-model="params.admin_id" @select="search"></wisdomMemberSelect>
                </view>
            </view>
        </view>

        <wisdomTable :api="getList" :headers="headers" ref="table"></wisdomTable>
    </view>
</template>

<script>
import wisdomSearchSelect from './wisdomSearchSelect';
import wisdomAnchorAccountSelect from './wisdomAnchorAccountSelect';
import wisdomAnchorPlatformSelect from './wisdomAnchorPlatformSelect';
import wisdomMemberSelect from './wisdomMemberSelect';
import wisdomTable from './wisdomTable';
import { getAnchorAccountData } from '@/common/utils/wisdom-work.js';
export default {
    props: {
        dateValue: { type: String, default: '' }
    },
    components: {
        wisdomSearchSelect,
        wisdomAnchorAccountSelect,
        wisdomAnchorPlatformSelect,
        wisdomMemberSelect,
        wisdomTable
    },
    data(){
        return {
            dateGroups: [{
                title: ' ', field: 'date_value', options: [
                    { label: '全部', value: '' },
                    { label: '今天', value: 'today' },
                    { label: '昨天', value: 'yestoday' },
                    { label: '本周', value: 'now_week' },
                    { label: '上周', value: 'last_week' },
                    { label: '本月', value: 'now_month' },
                    { label: '上月', value: 'last_month' },
                ]
            }],
            params: {
                date_value: '',
                start_date: '',
                end_date: '',
                refer_dy_id: '',
                platform: '',
                admin_id: '',
                is_all: 0
            },
            headers: [
                { label: '帐号名称', field: 'refer_dy_name', width: 150, align: 'left', fixed: true },
                { label: '线索名称', field: 'clue_name', width: 150, align: 'left' },
                { label: '主播', field: 'user_name', width: 130 },
                { label: '直播平台来源', field: 'platform', width: 110 },
                { label: '线索总量', field: 'total_custom_num', width: 110 },
                { label: '线索量', field: 'custom_num', width: 110 },
                { label: '重复量', field: 'repeat_num', width: 110 },
                { label: '线索占比', field: 'clue_proportion', width: 110 },
                { label: '客户量占比', field: 'proportion', width: 110 },
                { label: '同比', field: 'last_pop', width: 110 },
                { label: '环比', field: 'last_year_pop', width: 110 },
            ],
            list: [],
        }
    },
    watch: {
        dateValue: {
            handler(val){
                this.params.date_value = val;
            },
            immediate: true
        }
    },
    methods: {
        async getList(page){
            const [start_date, end_date ] = this.$Utils.getDateRange(this.params.date_value, 'YYYY-MM-DD');
            const params = { ...this.params, start_date, end_date, page };
            params.is_all = !start_date &&  !end_date ? 1 : 0;
            delete params.date_value;
            const res = await getAnchorAccountData(params);
            return {
                list: (res.data || []).map(row => {
                    row.clue_name = row.clue_name || '--';
                    row.user_name = row.user_name || '--';
                    row.clue_proportion = row.clue_proportion ? row.clue_proportion + '%' : '--';
                    row.proportion = row.proportion ? row.proportion + '%' : '--';
                    if(row.last_pop){
                        row.last_pop = (row.last_pop_type==1?'+':row.last_pop_type==2?'-':'') + row.last_pop + '%';
                    }else{
                        row.last_pop = '--';
                    }
                    if(row.last_year_pop){
                        row.last_year_pop = (row.last_year_pop_type==1?'+':row.last_year_pop_type==2?'-':'') + row.last_year_pop + '%';
                    }else{
                        row.last_year_pop = '--';
                    }
                    return row;
                }),
                pageSize: res.per_page || 0
            }
        },
        loadData(){
            this.$refs.table.getList();
        },
        async search(){
            await this.$refs.table.search();
        }
    },
}
</script>

<style lang="scss" scoped>
@import "@/common/style/wisdom_work/wisdom_work_top_filters.scss";
</style>