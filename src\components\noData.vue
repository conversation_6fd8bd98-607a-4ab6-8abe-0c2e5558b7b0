<template>
  <view class="nodata_tip">
    <view>
      <my-icon type="ic_guanyu3x1" size="82rpx" color="#dedede"></my-icon>
    </view>
    <view class="tip_text">{{ tip }}</view>
  </view>
</template>

<script>
import myIcon from "./my-icon";
export default {
  components: { myIcon },
  data() {
    return {};
  },
  props: {
    tip: {
      type: String,
      default: "暂无数据",
    },
  },
  methods: {},
};
</script>

<style scoped lang="scss">
.nodata_tip {
  padding: 30rpx;
  text-align: center;
  color: #dedede;
  .tip_text {
    margin-top: 20rpx;
  }
}
</style>
