import {
    isWxWork,
  } from "@/page_outside/tools/index";

const IsPC = () => {
    var userAgentInfo = navigator.userAgent;
    var Agents = ["Android", "iPhone",
      "SymbianOS", "Windows Phone",
      "iPad", "iPod"];
    var flag = true;
    for (var v = 0; v < Agents.length; v++) {
      if (userAgentInfo.indexOf(Agents[v]) > 0) {
        flag = false;
        break;
      }
    }
    return flag;
}

const getQuery = (params)=>{
    if(params){
        let searchParams = new URLSearchParams(location.search); 
        for(const key in params){
            if(!searchParams.has(key)){
                searchParams.set(key, params[key]);
            }
        }
        return '?'+searchParams.toString();
    }
    return location.search;
}

const getPCLink = ()=>{
    switch(location.pathname){
        //我的客户列表
        case '/fenxiao/customer/my_list':
            return '/admin/#/crm_customer_my_list' + getQuery({ type: 2 });
        //公海列表
        case '/fenxiao/customer/seas_list':
            return '/admin/#/crm_customer_clue_list' + getQuery({ status: 0 });
        //潜在客户列表
        case '/fenxiao/customer/qianzai_list':
            return '/admin/#/crm_potential_customers' + getQuery();
    }
    return '';
}

const IsJumpToPC = () => {
    if(IsPC() && ["com-wx-pc"].includes(isWxWork()) ){ // 
        let link = getPCLink();
        if(link){
            location.replace(location.origin + link);
            return true;
        }
    }
    return false;
}

export default IsJumpToPC;