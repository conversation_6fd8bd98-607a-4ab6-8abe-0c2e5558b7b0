<template>
    <view class="clue-content" :class="{'is-overd': isOvered,'no-overd': !isOvered, overflowed: overflowed}">
        <view class="clue-content-inner">
            <view v-if="clue.cname !== ''">姓名：{{ clue.cname }}</view>
            <view v-if="clue.age !== 0">年龄：{{ clue.age }}</view>
            <view v-if="clue.sex !== 0">性别：{{ clue.sex == 1 ? "男" : "女" }}</view>
            <view v-if="clue.intention_community !== ''">意向：{{ clue.intention_community }}</view>
            <view v-if="clue.remark !== ''" class="pre">备注：{{ clue.remark }}</view>
            <view v-if="clue.third_remark !== ''">线索备注：{{ clue.third_remark }}</view>
            <view v-if="clue.province_name !== ''">省份：{{ clue.province_name == "" ? "--" : clue.province_name }}</view>
            <view v-if="clue.city_name !== ''">城市：{{ clue.city_name }}</view>
            <view v-if="clue.country_name !== ''">地区：{{ clue.country_name }}</view>
            <view v-if="clue.address !== ''">详细地址：{{ clue.address }}</view>
            <view v-if="clue.location !== ''">定位城市：{{ clue.location }}</view>
            <view v-if="clue.clue_id !== ''">线索原始id: {{ clue.clue_id }}</view>
            <view v-if="clue.refer_dy_id !== ''">抖音号：{{ clue.refer_dy_id }}</view>
            <view v-if="clue.refer_dy_name !== ''">{{ clueName }}</view>
            <view v-if="clue.config_name !== ''">配置项名称：{{ clue.config_name }}</view>
            <view v-if="clue.label_type">意向等级：{{ clue.label_type }}级</view>
            <view v-if="clue.duration"> 通话时长：{{ clue.duration }}s</view>
            <view v-if="clue.focus"> 关注点：{{ clue.focus }}</view>
            <view v-if="clue.comment" class="pre"> 互动内容：{{ clue.comment }}</view>

            <view v-if="clue.action_record">
                <view v-if="clue.action_record.action_desc"> 互动记录：{{ clue.action_record.action_desc }}</view>
                <view v-if="clue.action_record.action_type_name"> 互动类型: {{ clue.action_record.action_type_name }}</view>
                <!-- <view class="follow-picture" v-if="clue.action_record.cover"> 封面图：
                    <img class="img" :src="$imageFilter(clue.action_record.cover, 'w_240')" alt="">
                    <span class="uploader-actions" v-if="clue.action_record.cover">
                        <span class="uploader-actions-item" @click="handlePictureCardPreview(clue.action_record.cover)">
                            <i class="el-icon-view"></i>
                        </span>
                    </span>
                </view> -->

                <view v-if="clue.action_record.create_time"> 互动时间：{{ clue.action_record.create_time }}</view>
                <view v-if="clue.action_record.video_name"> 短视频名称：{{ clue.action_record.video_name }}</view>
            </view>
        </view>
        <view class="more" v-if="isOvered">
            <view class="more-btn" @click="openMore">
                更多 <icons class="more-icon" type="jinrujiantou" size="28rpx" color="#165DFF"></icons>
            </view>
        </view>
    </view>
</template>

<script>
import icons from '@/components/my-icon';
export default {
    props: {
        clue: { type: Object, default: () => ({}) },
        overflowed: { type: Boolean, default: false },
    },
    components: {
        icons
    },
    data() {
        return {
            isOvered: false
        }
    },
    computed: {
        platformName(){
            switch(this.clue.platform){
                case 1:
                    return '腾房云';
                case 2:
                    return '百度基木鱼';   
                case 3:
                    return '分销报备'
                case 4:
                    return '抖音'
                case 5:
                    return 'T+'
                case 6:
                    return '快手'
                case 7:
                    return '海知道'
                case 8:
                    return '企业微信'
                case 9:
                    return '小程序'
                case 10:
                    return '外呼智机器人'
                case 11:
                    return '微信视频号'
                default:
                    return ''
            }
        },
        clueName() {
            let name = this.clue.refer_dy_name;
            return name === '' ? '' : this.platformName  + '名称：' +  name;
        },
    },
    mounted(){
        if(this.overflowed){
            const queryCont = uni.createSelectorQuery().in(this);
            const queryText = uni.createSelectorQuery().in(this);
            setTimeout(()=>{
                queryCont.select('.clue-content').boundingClientRect(e => {
                    const height = e.height;
                    queryText.select('.clue-content-inner').boundingClientRect(e => {
                        if(e.height > height){
                            this.isOvered = true;
                        }
                    }).exec();
                }).exec();
            }, 50)
        }
    },
    methods: {
        openMore(){
            this.$emit('more')
        }
    }

}

</script>
    
<style scoped lang="scss">
.clue-content{
    align-items: flex-start;
    font-weight: 500;
    color: #393C49;
    background-color: #F7F8FA;
    border-radius: 16rpx;
    
    overflow: hidden;
    margin-top: 24rpx;
    &.overflowed{
        max-height: 280rpx;
        .clue-content-inner{
            visibility: hidden;
        }
    }
    &.no-overd{
        .clue-content-inner{
            visibility: visible;
        }
    }
    &.is-overd{
        height: auto;
        padding: 24rpx 32rpx;
        .clue-content-inner{
            height: 232rpx;
            padding: 0;
            overflow: hidden;
            visibility: visible;
        }
    }
    .clue-content-inner{
        padding: 24rpx 32rpx;
        line-height: 58rpx;
        .pre{
            white-space: pre-line
        }
    }
    .more{
        
        color: #165DFF;
        width: 100%;
        flex-direction: row;
        justify-content: flex-end;
        .more-btn{
            display: inline-flex;
            flex-direction: row;
            padding: 24rpx 0 8rpx;
        }
        .more-icon{
            transform: rotate(90deg);
        }
    }
}

</style>