<template>
  <view class="share_list row">
    <view class="user-img ">
      <image
        mode="aspectFill"
        :src="
          user_img
            ? user_img
            : 'https://dss1.bdstatic.com/70cFuXSh_Q1YnxGkpoWK1HF6hhy/it/u=2561659095,299912888&fm=26&gp=0.jpg'
              | imageFilter('w_220')
        "
      ></image>
    </view>
    <view class="content-box">
      <view class="title">{{ user_name }}</view>
      <view class=""
        ><view class="content"
          ><text>{{ content }}</text
          ><text style="color:#4e66a3" @click="copyCtn">复制全文</text></view
        >
        <view class="img_box row">
          <view
            v-for="(item, index) in content_imgs"
            :key="index"
            class="img_item "
          >
            <video v-if="$isVideoReg(item)" :src="item"></video>
            <image
              v-if="!$isVideoReg(item)"
              mode="aspectFill"
              @click="$previewImage(item)"
              :src="item | imageFilter('w_120')"
            ></image>
          </view>
        </view>
        <view class="time">{{ share_list.created_at }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    user_img: {
      type: String,
      default:
        "https://dss0.bdstatic.com/70cFvHSh_Q1YnxGkpoWK1HF6hhy/it/u=4149550277,3338038883&fm=115&gp=0.jpg",
    },
    user_name: String,
    content: String,
    content_imgs: Array,
    share_list: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  methods: {
    copyCtn() {
      this.$copyText(this.content, () => {
        uni.showToast({
          title: "复制成功",
          icon: "none",
        });
      });
      this.$emit("click", this.content);
    },
  },
};
</script>

<style lang="scss" scoped>
.share_list {
  padding: 24rpx 48rpx;
  .user-img {
    image {
      border-radius: 10rpx;
      width: 80rpx;
      height: 80rpx;
    }
  }
  .content-box {
    margin-left: 16rpx;
    width: 100%;

    .title {
      font-size: 32rpx;
      color: #4e66a3;
    }
    .content {
      margin: 16rpx 0;
      font-size: 14px;
      color: #333333;
      text-align: justify;
      text {
        line-height: 40rpx;
        // text-overflow: -o-ellipsis-lastline;
        // overflow: hidden;
        // text-overflow: ellipsis;
        // display: -webkit-box;
        // -webkit-line-clamp: 3;
        // -webkit-box-orient: vertical;
      }
    }
    .img_box {
      flex-wrap: wrap;
      .img_item {
        margin: 12rpx;
        height: 176rpx;
        margin-left: 0;
        width: 31%;
        justify-content: space-between;
        &::after {
          content: "";
          width: 31%;
        }
        image {
          height: 100%;
          width: 100%;
        }
      }
    }
  }
}
video {
  height: 200rpx;
  width: 548rpx;
}
.time {
  margin-top: 20rpx;
  // justify-content: flex-end;
  // text-align: end;
}
</style>
