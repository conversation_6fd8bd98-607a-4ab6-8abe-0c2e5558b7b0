<template>
  <view class="page">
    <view class="search_container">
      <search
        placeholder="请输入小区关键字"
        :delay="500"
        :focus="search_focus"
        v-model="keyword"
        @input="handleSearch"
      >
      </search>
    </view>
    <view class="list">
      <view
        v-for="(item, index) in community_list"
        :key="index"
        class="bottom-line flex-row items-center space-between list_item"
        @click="onSelect(item)"
      >
        <view class="left flex-1">
          <view class="community_title">
            {{ item.title }}
          </view>
          <view class="community_address"> {{ item.addr }} </view>
        </view>
        <view class="right" v-if="item.house_count"> {{ item.house_count }}套在售 </view>
        <icons :color="rightIcon.color" :size="rightIcon.size" :type="rightIcon.type"></icons>
        <!-- <listItem :title="item.title" /> -->
      </view>
      <loadMore :status="load_status" :load_text="load_text" @reload="handleSearch(keyword)" />
    </view>
    <!-- <view class="pd48" v-if="load_status === 'nomore'">
      <my-button type="primary" plain @click="toApply" block>
        <text style="margin-right: 12rpx">没找到？去申请添加</text>
        <icons type="a-tianjia1x"></icons>
      </my-button>
    </view> -->
    <!-- <view @click="toApply" class=""> 没找到？去申请添加 </view> -->
  </view>
</template>

<script>
import search from './components/search'
// import listItem from '@/components/ui/listItem'
import loadMore from '@/components/loadMore'
import myButton from './components/myButton'
import icons from '@/components/my-icon'
export default {
  name: 'selectCommunity',
  components: {
    search,
    // listItem,
    loadMore,
    myButton,
    icons,
  },
  data () {
    return {
      keyword: '',
      load_status: 'loadend',
      load_text: {
        loading: '搜索中',
        loadend: '',
        nomore: '没有搜索到相关小区',
        loaderror: '搜索失败请重试',
      },
      community_list: [],
      search_focus: false,
      rightIcon: {
        type: 'jinrujiantou',
        color: '#8a929f',
        size: '26',
      },
    }
  },
  onLoad (options) {
    // 从选择小区地图搜索过来的 完成选择以后销毁页面
    if (options.from) {
      this.from = options.from
    }
    // this.area_id = options.area_id
  },
  onReady () {
    setTimeout(() => {
      this.search_focus = true
    }, 300)
  },
  methods: {
    handleSearch (keyword) {
      if (!keyword) {
        return
      }
      this.load_status = 'loading'
      this.community_list = []
      this.$ajax.get(
        `/admin/house/searchCommunity?title=${keyword}`,
        {},
        (res) => {
          console.log(res)
          if (res.statusCode === 200) {
            this.load_status = 'loadend'
            this.community_list = res.data
            console.log(this.community_list)
            if (res.data.length === 0) {
              this.load_status = 'nomore'
            }
          } else {
            this.load_status = 'nomore'
          }
        },
        (err) => {
          this.load_status = 'loaderror'
          console.log(err)
        }
      )
    },
    onSelect (e) {
      uni.navigateBack()
      // if (this.from == 'private') {
      //   // this.$Router.replace({
      //   //   name: 'add_private',
      //   // })
      // } else {
      //   uni.navigateBack()
      // }
      // this.$navigateBack()
      setTimeout(() => {
        uni.$emit('selectcommunity', e)
        uni.$emit('selectedCommunity', e)
      }, 200)
    },
    toApply () {
      // this.$navigateTo({
      //   name: 'collection_commutity',
      // })
    },
  },
}
</script>

<style scoped lang="scss">
.search_container {
  padding: 0 48rpx;
  position: sticky;
  // #ifdef H5
  top: 0px;
  // #ifdef H5-WEIXIN
  top: 0;
  // #endif
  // #endif
  // #ifndef H5
  top: 0;
  // #endif
  z-index: 2;
  background-color: #fff;
}
.pd48 {
  padding: 48rpx;
}
.list_item {
  padding: 24rpx 48rpx;
  .community_title {
    font-size: 28rpx;
    color: #2e3c4e;
    margin-bottom: 16rpx;
  }
  .community_address {
    font-size: 22rpx;
    color: #8a929f;
  }
  .right {
    margin-right: 24rpx;
    font-size: 22rpx;
    color: #8a929f;
  }
}
</style>
