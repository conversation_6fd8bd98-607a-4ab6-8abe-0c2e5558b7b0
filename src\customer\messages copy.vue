<template>
    <view>
        <view class="icon flex-row items-center" v-for="item in AllList" :key="item.id">
            <!-- 图标 -->
            <view >
                <view class="img" style="width: 100rpx;height: 100rpx;">
                    <image :src="item.icon" style="width:100%" mode="widthFix"></image>
                </view>
            </view>
            <!-- 列表 -->
            <view class="list-text" @click="listFn(item)">
          <view class="list_text_time">
                  <!-- 列表名字 -->
                  <view class="list-massage">
                    <view style="color:black;font-size: 32rpx;font-weight: 500;font-style: normal; margin-bottom: 24rpx;">
                        {{ item.name }}
                    </view>
                </view>
                <view>
                         <!-- 列表时间 -->
                        <!-- 系统消息 -->
                        <view style="
                            margin-right: 32rpx;
                            color: var(--unnamed, rgba(0, 0, 0, 0.40));
                            /* 辅助文字 */
                            font-family: PingFang SC;
                            font-size: 24rpx;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 50rpx; /* 133.333% */
                            " v-if="item.name === '系统消息'">
                            {{ messageTime | timeago   }}
                        </view>
                        <!-- 平台消息 -->
                        <view style="
                            margin-right: 32rpx;
                            color: var(--unnamed, rgba(0, 0, 0, 0.40));
                            /* 辅助文字 */
                            font-family: PingFang SC;
                            font-size: 24rpx;
                            font-style: normal;
                            font-weight: 400;
                            line-height: 50rpx; /* 133.333% */
                            " v-if="item.name === '平台消息'">
                            {{ messageTimes | timeago  }}
                        </view>
                        <!-- 分享获客 -->
                        <view style="
                     margin-right: 32rpx;
                    color: var(--unnamed, rgba(0, 0, 0, 0.40));
                    /* 辅助文字 */
                    font-family: PingFang SC;
                    font-size: 24rpx;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 50rpx; /* 133.333% */
                    " v-if="item.name === '分享获客'">
                            {{ hourTime | timeago  }}
                        </view>
                        <!-- 聊天消息 -->
                        <view style="
                      margin-right: 32rpx;
                    color: var(--unnamed, rgba(0, 0, 0, 0.40));
                    /* 辅助文字 */
                    font-family: PingFang SC;
                    font-size: 24rpx;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 50rpx; /* 133.333% */
                    " v-if="item.name === '聊天消息'">
                            {{ hourTimes | timeago   }}
                        </view>
                </view>
          </view>
                <!-- 列表内容 -->
                <!--系统消息  -->
                <view style="
                margin: 0 0 12rpx 0;
                color:rgb(59 60 65 / 88%);font-size: 26rpx;
                display: inline-block;
                width: 400rpx;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;" v-if="item.name === '系统消息'">{{ oneList }}</view>
                <!-- 平台消息 -->
                <view style="
               margin: 0 0 12rpx 0;
               color:rgb(59 60 65 / 88%);font-size: 26rpx;
                display: inline-block;
                width: 400rpx;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;" v-if="item.name === '平台消息'">{{ oneLists }}</view>
                <!-- 分享获客 -->
                <view style="
                   margin: 0 0 12rpx 0;
                   color:rgb(59 60 65 / 88%);font-size: 26rpx;
                display: inline-block;
                width: 400rpx;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;" v-if="item.name === '分享获客'">{{ towList }}</view>
                <!-- 聊天消息 -->
                <view style="
                  margin: 0 0 12rpx 0;
                  color:rgb(59 60 65 / 88%);font-size: 26rpx;
                display: inline-block;
                width: 400rpx;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;" v-if="item.name === '聊天消息'">{{ towLists }}</view>
            </view>
        </view>
        <view class="svg" v-if="MessageswellArr.length">{{ MessageswellArr.length }}</view>
        <view class="svgone" v-if="MessageswellArrs.length">{{ MessageswellArrs.length }}</view>
        <view class="svgtow" v-if="MessagesllArr.length">{{ MessagesllArr.length }}</view>
        <view class="svgfor" v-if="MessagesllArrs.length">{{ MessagesllArrs.length }}</view>
        <tabber @click="switchTab" :current="currentTabIndex"></tabber>
    </view>
</template>
<script>
import tabber from '@/customer/components/tabber/tabber';
export default {
    components: {
        tabber,
    },
    data() {
        return {
            currentTabIndex: 1,
            // 未阅读列表 系统消息
            MessageswellArr: [],
            oneList: '暂无消息',
            messageTime: "",
            // 未阅读列表 平台消息
            MessageswellArrs: [],
            oneLists: '暂无消息',
            messageTimes: "",
            AllList: [],
            // 分享获客
            MessagesllArr: [],
            towList: '暂无消息',
            hourTime: '',
            // 消息聊天
            MessagesllArrs: [],
            towLists: '暂无消息',
            hourTimes: '',
        }
    },
    onLoad() {
        // this.getMessages()
        // this.getUserToken()
        // this.getMtoken()
        // 未阅读列表 系统消息
        this.getMessageswell()
        // 未阅读列表 平台消息
        this.getMessageswells()
        this.getAllMessage()
        uni.$on('getDataload',()=>{
            this.getMessageswells()
            this.getMessageswell()
        }),
        uni.showLoading({
  title:"加载中",
})
    },
    onUnload(){
        uni.$off('getDataload')
    },
    methods: {
        listFn(item) {
            console.log(item, 'item');
            if (item.location === '') {
                uni.showToast({
                    title: '暂未开放',
                    icon: "none"
                })
            } else {
                this.$navigateTo(item.location)
            }
        },
        switchTab(index, item) {
            if (this.currentTabIndex == index) {
                return;
            }
            uni.navigateTo({
                url: item.path,
            });
        },
        navigatorFn() {
            console.log(11);
        },
        // 检测是否绑定前端用户
        getMessages() {
            this.$ajax.get("/qywx/message/im/check", {}, (res) => {
                console.log(res, '系统消息');
            });
        },
        // 获取用户的token
        getUserToken() {
            this.$ajax.get("/qywx/message/im/user_token", {}, (res) => {
                console.log(res, '用户token');
            });
        },
        // 获取IMtoken
        getMtoken() {
            this.$ajax.get('/qywx/message/im/im_token', {}, (res) => {
                console.log(res, 'IMtoken');
            })
        },
        // 未阅读列表 系统消息
        getMessageswell() {
            this.$ajax.get('/qywx/message/letter/not_reading_list', { platform: '2' }, (res) => {
                console.log(res.data, '未阅读列表系统消息');
                if (Array.isArray(res.data)&&res.data.length) {
                    this.MessageswellArr = res.data
                    this.messageTime =res.data[0].created_at
                    this.oneList =res.data[0].content
                }else{
                    this.MessageswellArr=[]
                    this.messageTime= ' '
                    this.oneList='暂无消息'
                }
            })
        },
        // 未阅读列表 平台消息
        getMessageswells() {
            this.$ajax.get('/qywx/message/letter/not_reading_list', { platform: '1' }, (res) => {
                // 始终显示最新的消息
                console.log(res.data, '未阅读列表平台消息');
                if (Array.isArray(res.data)&&res.data.length) {
                    this.MessageswellArrs = res.data
                    this.messageTimes =res.data[0].created_at
                    this.oneLists =res.data[0].content
                }else{
                    this.MessageswellArrs = []
                    this.messageTimes= ' '
                    this.oneLists='暂无消息'
                }
            })
        },
        // 获取消息界面
        getAllMessage() {
            this.$ajax.get('/qywx/welcome/get_message_cate', {}, (res) => {
                console.log(res.data.location, '消息界面');
                if (res.statusCode === 200) {
                res.data.map(item => {
                    item.icon = item.icon.replace("\n", "")
                })
                this.AllList = res.data
                    uni.hideLoading()
                }else{
                    uni.hideLoading()
                }
            },
            ()=>{
                uni.hideLoading()
            })
        },
        // 跳转详情页
        myListFn() {
            uni.navigateTo({
                url: '/customer/msg_detail'
            });
        },
        // 时间转换
        getTimer(stringTime) {
            var minute = 1000 * 60;
            var hour = minute * 60;
            var day = hour * 24;
            var week = day * 7;
            var month = day * 30;
            var time1 = new Date().getTime(); //当前的时间戳
            var time2 = Date.parse(new Date(stringTime)); //指定时间的时间戳
            var time = time1 - time2;
            var result = null;
            if (time < 0) {
                uni.showToast({
                    title: "设置的时间不能早于当前时间！",
                    icon: "none",
                });
            } else if (time / month >= 1) {
                result = parseInt(time / month) + "月前";
            } else if (time / week >= 1) {
                result = parseInt(time / week) + "周前";
            } else if (time / day >= 1) {
                result = parseInt(time / day) + "天前";
            } else if (time / hour >= 1) {
                result = parseInt(time / hour) + "小时前";
            } else if (time / minute >= 1) {
                result = parseInt(time / minute) + "分钟前";
            } else {
                let time = new Date();
                let msg_time =
                    (time.getHours() < 10 ? "0" + time.getHours() : time.getHours()) +
                    ":" +
                    (time.getMinutes() < 10
                        ? "0" + time.getMinutes()
                        : time.getMinutes());
                result = msg_time;
            }
            return result;
        },

    }
}
</script>
<style lang="scss">
.list_text_time{
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    flex-wrap: nowrap;
}
.text {
    margin-top: 100rpx;
    margin-bottom: 30rpx;
    color: #000;
    text-align: center;
    font-family: PingFang SC;
    font-size: 36rpx;
    font-style: normal;
    font-weight: 700;
    line-height: 18px;
    /* 100% */
}

.svg {
    display: flex;
    color: #ffff;
    font-size: 20rpx;
    width: 32rpx;
    height: 32rpx;
    padding: 20rpx;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 20rpx;
    position: absolute;
    left: 100rpx;
    top: 64rpx;
    border-radius: 32rpx;
    background: #F53F3F;
}

.svgone {
    display: flex;
    color: #ffff;
    font-size: 20rpx;
    width: 32rpx;
    height: 32rpx;
    padding: 20rpx;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 20rpx;
    position: absolute;
    left: 100rpx;
    top: 280rpx;
    border-radius: 32rpx;
    background: #F53F3F;
}

.svgtow {
    display: flex;
    color: #ffff;
    font-size: 20rpx;
    width: 32rpx;
    height: 32rpx;
    padding: 20rpx;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 20rpx;
    position: absolute;
    left: 100rpx;
    top: 416rpx;
    border-radius: 32rpx;
    background: #F53F3F;
}

.svgfor {
    display: flex;
    color: #ffff;
    font-size: 20rpx;
    width: 32rpx;
    height: 32rpx;
    padding: 20rpx;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 20rpx;
    position: absolute;
    left: 100rpx;
    top: 586rpx;
    border-radius: 32rpx;
    background: #F53F3F;
}

.icon {
    margin: 0 0 0 32rpx;
    display: flex;
    flex-direction: row;
    padding-top: 32rpx;
}

.list-text {
    margin: 0 0 0 32rpx;
    padding: 24rpx 0 12rpx 0;
    width: 100%;
    border-bottom: solid 2rpx var(--unnamed, rgba(172, 172, 172, 0.4));
    border-radius: 4rpx;
    // background-color: antiquewhite;
}

.icon-text {
    margin-left: 32rpx;
    width: 100%;
    border-bottom: solid 2rpx var(--unnamed, rgba(172, 172, 172, 0.4));
    border-radius: 4rpx;
}

.list-massage {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.icon-name {
    display: flex;
    flex-direction: row;
}
</style>
