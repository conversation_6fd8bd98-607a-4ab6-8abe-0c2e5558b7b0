<template>
<view class="list-item">
	<view class="content">
		<view class="row">
			<view class="row-label">报备项目</view>
			<view class="row-cont">{{item.report_project}}</view>
		</view>
		<view class="row">
			<view class="row-label">客户姓名</view>
			<view class="row-cont">{{item.cname}}</view>
		</view>
		<view class="row">
			<view class="row-label">客户电话</view>
			<view class="row-cont">
				<text class="color-primary" @click.stop="copyText(item.mobile)">{{item.mobile}}</text>
			</view>
		</view>
		<view class="row">
			<view class="row-label">公司名称</view>
			<view class="row-cont">{{ item.company_name }}</view>
		</view>
		
		<template v-if="showAdmin">
			<view class="row">
				<view class="row-label">经纪人姓名</view>
				<view class="row-cont">{{item.admin && item.admin.user_name || '--'}}</view>
			</view>
			<view class="row">
				<view class="row-label">经纪人电话</view>
				<view class="row-cont">{{item.admin && item.admin.phone || '--'}}</view>
			</view>
		</template>

		<view class="row">
			<view class="row-label">报备日期</view>
			<view class="row-cont">
				{{item.visit_time || '--'}}
				<text class="time-type">{{item.visit_type | visitTimeType}}</text>
			</view>
		</view>

		<view class="row" v-if="showCopay">
			<view class="row-label"></view>
			<view class="row-cont row-cont-right">
				<text class="color-primary copy-btn" @click.stop="copyData(item)">复制</text>
			</view>
		</view>


		<view class="row" v-if="showChannel">
			<view class="row-label">审核专员</view>
			<view class="row-cont">
				<template v-if="item.channel && item.channel.length">
					<text v-for="(v,i) in item.channel" :key="v.id">
						<template v-if="i != 0">、</template>{{v.user_name}}
					</text>
				</template>
			</view>
		</view>
		
		<view class="row">
			<view class="row-label">创建时间</view>
			<view class="row-cont">{{item.created_at}}</view>
		</view>
		<view class="row">
			<view class="row-label">回执状态</view>
			<view class="row-cont">
				<text class="color-red" v-if="item.receipt_time">已回执</text>
				<text v-else>--</text>
			</view>
		</view> 
		<view class="row">
			<view class="row-label">回执时间</view>
			<view class="row-cont">
				{{item.receipt_time || '--'}}
			</view>
		</view> 
		<view class="row">
			<view class="row-label">带看记录</view>
			<view class="row-cont">
				{{item.take_status | takeStatus}}
			</view>
		</view>

	</view>
	<slot name="op">
		<view class="op">
			<view class="left">
			</view>
			<view class="right">
				<view class="op-btn" @click.stop="goReportDetail(item.id)">详情</view>
			</view>
		</view>
	</slot>
</view>
</template>

<script>
export default {
	props: {
		item: { type: Object, default: () => ({})},
		showAdmin: { type: Boolean, default: false },
		showChannel: { type: Boolean, default: false },
		showCopay: { type: Boolean, default: false },
	},
	data(){
		return {
			
		}
	},
	filters: {
		//报备时间类型
		visitTimeType(val){
			switch(val){
				case 1: return '上午';
				case 2: return '下午';
				case 3: return '晚上';
				default: return '--';
			}
		},
		//带看记录
		takeStatus(val){
			switch(val){
				case 1: return '未带看';
				case 2: return '已带看';
				case 3: return '有复看';
				default: return '';
			}
		}
	},
	methods: {
		getVisitTimeTypeTitle(val){
			switch(val){
				case 1: return '上午';
				case 2: return '下午';
				case 3: return '晚上';
				default: return '';
			}
		},
		copyText(text){
            this.$copyText(text, ()=>{
                uni.showToast({
                    title: "复制成功",
                    icon: "none"
                })
            })
        },
		copyData(item){
			let text = `报备项目：${item.report_project}`+
			`\n客户姓名：${item.cname}`+
			`\n客户电话：${item.mobile}`+
			`\n公司名称：${item.company_name}`+
			`\n经纪人姓名：${item.admin && item.admin.user_name || '--'}`+
			`\n经纪人电话：${item.admin && item.admin.phone || '--'}`+
			`\n报备日期：${item.visit_time || '--'}`;
			this.copyText(text)

		},
        goReportDetail(id){
            this.$navigateTo(`/report/detail?id=${id}`);
        },
	},

}
</script>

<style scoped lang="scss"> 
.list-item{
	padding: 24rpx 32rpx;
	margin-top: 24rpx;
	background-color: #fff;
	.title{
		font-size: 32rpx;
	}
	.content{
		margin-top: 10rpx;
		.row{
			padding: 8rpx 0;
			line-height: 1.5;
			overflow: hidden;
			display: flex;
			flex-direction: row;
			.row-label{
				color: rgba(41,44,57,.4);
				white-space: nowrap;
				padding-right: 24rpx;
				min-width: 138rpx;
				text-align: right;
			}
			.row-cont{
				flex: 1;
				word-break: break-all;
				flex-direction: row;
				flex-wrap: wrap;
				color: #3c3c3c;
				&.row-cont-right{
					justify-content: flex-end;
				}
				.time-type{
					padding-left: 16rpx;
				}
				.color-red{
					color: #f40;
				}
				.color-primary{
					color: #2d84fb
				}
				.copy-btn{
					padding: 0 6rpx;
				}
			}
		}
	}
	.op{
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		margin-top: 12rpx;
		padding: 24rpx 0 0;
		border-top: 1rpx solid #f1f2f3;
		.right{
			flex: 1;
			flex-direction: row;
			justify-content: flex-end;
		}
		
		.op-btn{
			display: inline-block;
			line-height: 1;
			padding: 12rpx 24rpx;
			color: $color-primary;
			border: 1px solid $color-primary;
			border-radius: 4rpx;
		}
	}
}
</style>