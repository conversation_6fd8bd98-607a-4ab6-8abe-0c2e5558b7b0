<template>
    <myPopup :show="show"  @close="show = false">
      <view class="wrapper">
        <view class="header">
            <view class="btn-cancle" @click="show=false">取消</view>
        </view>
        <scroll-view scroll-y class="body">
            <clueItem :clue="clue"></clueItem>
        </scroll-view>
      </view>
    </myPopup>
</template>

<script>
import myPopup from "@/components/myPopup.vue";
import clueItem from './clueItem.vue'
export default {
    props: {
        clue: { type: Object, default: () => ({}) },
        visible: { type: Boolean, default: false }
    },
    components: {
        myPopup,
        clueItem
    },
    data() {
        return {
            show: false
        }
    },
    watch: {
        visible(val){
            this.show = val;
        },
        show(val){
            val !== this.visible && this.$emit('update:visible', val);
        }
    }
}
</script>
    
<style scoped lang="scss">
.wrapper{
    padding: 0 36rpx 36rpx;
    background-color: #fff;
    .header{
        height: 88rpx;
        flex-direction: row;
        justify-content: flex-end;
        align-item: center;
        .btn-cancle{
            justify-content: center;
            align-item: center;
            color: $color-primary;
            font-size: 32rpx;
            padding: 0 24rpx;
        }
    }
    .body{
        min-height: 35vh;
        max-height: 65vh;
        ::v-deep{
            .clue-content{
                margin: 0
            }
        }
    }
}
</style>