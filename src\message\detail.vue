<template>
  <view class="news_detail">
    <view class="title">{{ news.title }}</view>
    <view class="title_below">
      <view class="left">
        <!-- <image :src="news.img" class="auth_logo" mode="aspectFill"></image> -->
        <view>
          <view class="auth">{{ news.author }}</view>
          <view class="time">{{ news.created_at }}</view>
        </view>
      </view>
      <view class="right">共有{{ news.read_total }}人浏览</view>
    </view>
    <view class="article-content" v-html="news.content"></view>
    <view class="article-content">
      <u-parse
        :content="content.content"
        @linkpress="navigate"
        :tag-style="tagStyle"
      ></u-parse>
    </view>

    <view class="tip-box">
      <view>免责声明</view>
      <text
        >本平台对分享的内容、陈述、观点判断保持中立,不对所包含内容的准确性、可靠性或完善性提供任何明示或暗示的保证,仅供读者参考,本平台将不承担任何责任。</text
      >
    </view>
    <myLoading
      ref="loading"
      :custom="false"
      :shadeClick="true"
      :type="1"
    ></myLoading>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import uParse from "@/components/uParse/parse.vue";
import myLoading from "@/components/my-loading";
export default {
  components: {
    uParse,
    myLoading,
  },
  data() {
    return {
      news: {},
      tagStyle: {
        video: "max-width:100%",
      },
      adv_list: [],
      news_list: [],
      detail: {},
      params: {},
      content: "",
    };
  },
  computed: {},
  onLoad(options) {
    this.params.id = options.id;
    this.getData();
  },
  onReady() {
    this.$refs.loading.open();
  },
  methods: {
    getData() {
      this.$ajax.get(`/common/news/query/${this.params.id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.news = res.data;
          if (this.news) {
            this.$refs.loading.close();
          }
          uni.setNavigationBarTitle({
            title: this.news.title,
          });
        } else {
          uni.showToast({
            title: "获取信息失败",
            icon: "none",
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.news_detail {
  padding-bottom: 120rpx;
}
.title {
  padding: 30rpx 24rpx;
  line-height: 1.5;
  // text-align: center;
  font-size: 40rpx;
  font-weight: bold;
}
.title_below {
  flex-direction: row;
  margin: 10rpx 30rpx 30rpx 30rpx;
  padding-bottom: 40rpx;
  margin-bottom: 20rpx;
  font-size: $uni-font-size-sm;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #dedede;
  .left {
    flex-direction: row;
    align-items: center;
    .auth_logo {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    .auth {
      display: inline-block;
      // max-width: 260rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-bottom: 12rpx;
      font-size: 30rpx;
      // color: #4db0fd;
    }
    .time {
      color: #888;
    }
  }
}

.view_count {
  margin: 30rpx;
  flex-direction: row;
  align-items: center;
  .count {
    margin-left: 5rpx;
    color: #999999;
  }
}

.tip-box {
  margin: 30rpx;
  // flex-direction: row;
  padding: 24rpx;
  line-height: 1.5;
  border-top: 1rpx solid #dedede;
  view {
    font-size: 30rpx;
    margin-bottom: 10rpx;
  }
  text {
    color: #888;
  }
}

.banner-box {
  padding: 0 10rpx;
}

.fixed-bottom {
  width: 100%;
  padding: 10rpx 30rpx;
  position: fixed;
  bottom: 0;
  z-index: 10;
}
</style>
