<template>
  <view>
    <view class="xskcodeinput row">
      <view
        class="box"
        :style="{
          width: width + 'rpx',
          height: height + 'rpx',
          backgroundColor: backgroundColor,
        }"
        @click="focus = true"
        v-for="(item, index) in length"
        :key="index"
      >
        <view
          v-if="password.length > index"
          :style="{ fontWeight: bold ? 'bold' : 'normal' }"
          >{{ showVal ? password[index] : "*" }}</view
        >
        <view
          class="line"
          v-if="password.length == index"
          style="font-weight: normal;font-size:30px;height:144rpx;line-height: 144rpx;"
          >|</view
        >
      </view>

      <input
        class="input"
        type="number"
        :focus="focus"
        v-model="password"
        maxlength="6"
        @focus="focus = true"
        @blur="focus = false"
        @input="userinput"
      />
    </view>
  </view>
</template>

<script>
export default {
  name: "xskCodeInput",
  data() {
    return {
      focus: false,
      password: "",
    };
  },
  /**
   *    @ Date 2022年5月10日09:38:28
   *    @ Description 验证码输入框样式
   *    @ Params {
   *
   *    ******** KEY **************** TYPE **************** DEFAULT **************** DESCRIPTION ****************
   *            value                Number                 ""                       绑定的值(value.sync)
   *            width                Number,String          84                       显示块的宽度
   *            height               Number,String          84                       显示块的高度
   *            backgroundColor      String                 #EDEDED                  显示块的背景色
   *            bold                 Boolean                Boolean                  文字是否加粗
   *            showVal              Boolean                false                    是否显示输入内容
   *            length               Number                 6                        输入框长度
   *    *********************************************************************************************************
   *
   *  }
   * */

  props: {
    width: {
      type: [Number, String],
      default: 84,
    },
    height: {
      type: [Number, String],
      default: 84,
    },
    backgroundColor: {
      type: String,
      default: "#EDEDED",
    },
    bold: {
      type: Boolean,
      default: true,
    },
    showVal: {
      type: Boolean,
      default: false,
    },
    length: {
      type: Number,
      default: 6,
    },
  },
  methods: {
    userinput(e) {
      this.$emit("update:value", this.password);
      if (e.detail.value.length == this.length) {
        this.focus = false;
        this.$emit("confirm");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.xskcodeinput {
  position: relative;
  align-items: center;
  justify-content: center;
  .box {
    margin-right: 12rpx;
    text-align: center;
    font-size: 38rpx;
    line-height: 144upx;
    border: 1px solid #263a97;
    color: #3a3a3c;
    font-size: 48px;
    font-weight: 700;
    border-radius: 16rpx;
    &:last-child {
      margin-right: 0;
    }
    .line {
      opacity: 0;
      animation-name: donghua;
      animation-timing-function: linear;
      animation-iteration-count: infinite;
      animation-duration: 0.5s;
      animation-direction: alternate;
    }
    @keyframes donghua {
      0% {
        opacity: 1;
      }
      100% {
        opacity: 0;
      }
    }
  }

  .input {
    position: absolute;
    top: 0;
    left: -100%;
    width: 200%;
    height: 100%;
    opacity: 0;
  }
}
</style>
