<template>
  <view>
    <view class="top">
      <view class="top_c row">
        <view class="search row c2 flex-1">
          <myIcon class="icon" type="ic_sousuo3x1" color="#8A929F" size="32rpx"></myIcon>
          <input type="text" v-model="params.mobile" @confirm="onSearch" placeholder="请输入手机号码搜索" />
        </view>
        <view class="depart row" @click="quickShow">
          <view class="depart_name">潜客</view>
          <view class="sanjiao"> </view>
        </view>
      </view>
      <view class="second_tab flex-row flex-1 align-center js-between" id="second_tab">
        <!-- <view
          @click="changeSecondTab(1)"
          class="second_tab_item flex-1 flex-row justify-center items-center"
        >
          {{ currentType }}
          <myIcon style="margin-left: 8px" color="#282A2F" type="xiala" size="10px"></myIcon>
        </view> -->

        <view @click="changeSecondTab(2)" class="second_tab_item flex-1 flex-row justify-center items-center">
          {{ currentStatus }}
          <myIcon style="margin-left: 16rpx" color="#282A2F" type="xiala" size="20rpx"></myIcon>
        </view>

        <view @click="changeSecondTab(3)" class="second_tab_item flex-1 flex-row justify-center items-center">
          {{ currentLabel }}
          <myIcon style="margin-left: 16rpx" color="#282A2F" type="xiala" size="20rpx"></myIcon>
        </view>

        <view @click="changeSecondTab(4)" class="second_tab_item flex-1 flex-row justify-center items-center">
          {{ currentMore }}
          <myIcon style="margin-left: 16rpx" color="#282A2F" type="xiala" size="20rpx"></myIcon>
        </view>
        <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 1">
          <view class="second_tab_con_i">
            <view class="second_tab_con_item">
              <view class="second_tab_con_item_title">客户来源</view>
              <view class="second_tab_con_item_list row">
                <view class="second_tab_con_item_list_item" v-for="item in source_list" :key="item.id"
                  :class="{ isactive: params.source_id == item.id }" @click="
                    params.source_id = item.id
                  currentType = item.title
                    ">{{ item.title }}</view>
              </view>
            </view>
            <view class="flex-row btn_group">
              <view class="btn" @click="resetData">重置</view>
              <view class="btn bg_highlight" @click="filterData">确定</view>
            </view>
          </view>
          <view class="mask" @click="hideErjiSearch"></view>
          <!-- <view class="mask1" v-show="showMask"> </view> -->
        </view>
        <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 2">
          <view class="second_tab_con_i">
            <view class="second_tab_con_item">
              <view class="second_tab_con_item_title">客户状态</view>
              <view class="second_tab_con_item_list row">
                <view class="second_tab_con_item_list_item" v-for="item in [
                  {
                    id: 0,
                    title: '全部',
                  },
                  {
                    id: 1,
                    title: '转公',
                  },
                  {
                    id: 2,
                    title: '掉公',
                  },
                ]" :key="item.id" :class="{ isactive: params.c_type4 == item.id }" @click="
  params.c_type4 = item.id
currentStatus = item.title
  ">{{ item.title }}</view>
              </view>
            </view>
            <view class="flex-row btn_group">
              <view class="btn" @click="resetData">重置</view>
              <view class="btn bg_highlight" @click="filterData">确定</view>
            </view>
          </view>
          <view class="mask" @click="hideErjiSearch"></view>
          <!-- <view class="mask1" v-show="showMask"> </view> -->
        </view>
        <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 3">
          <view class="second_tab_con_i">
            <view class="second_tab_con_item filter_list row" style="height: 400rpx; margin: 0 -48rpx">
              <scroll-view class="filter_labels" scroll-y>
                客户标签
                <view class="item" v-for="(item, index) in tag_list" :key="index" @click="checkFilter(index, item)"
                  :class="{ active: index == scroll_into_view_index }">{{ item.name }}</view>
              </scroll-view>
              <scroll-view scroll-y class="second_tab_con_item_right">

                <view class="lab-list row" v-for="item in label_list" :key="item.id" @click="
                  params.label = item.id
                currentLabel = item.name
                  " :class="{ active: item.id == params.label }">
                  <view class="lab-item">{{ item.name }}</view>
                  <image class="ischeck" :src="`https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/${item.id == params.label ? 'ischeck' : 'check'
                    }.png`"></image>
                </view>
              </scroll-view>
            </view>
            <view class="flex-row btn_group">
              <view class="btn" @click="resetData">重置</view>
              <view class="btn bg_highlight" @click="filterData">确定</view>
            </view>
          </view>
          <view class="mask" @click="hideErjiSearch"></view>
        </view>
        <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 4">
          <view class="second_tab_con_i">
            <scroll-view scroll-y style="height: 600rpx">
              <!-- <view class="second_tab_con_item_title">自定义</view>
              <view class="second_tab_con_item_list" >
                <view class="timebox row">
                  <input
                  @focus="focusFn"
                    style="width: 256rpx"
                    placeholder-class="placls"
                    placeholder="请输入年"
                    v-model="startyear"
                  />
                  <input
                  @focus="focusFn"
                    placeholder-class="placls"
                    placeholder="月"
                    v-model="startmonth"
                  />
                  <input
                  @focus="focusFn"
                    placeholder-class="placls"
                    placeholder="日"
                    v-model="startday"
                  />
                </view>
                <view class="zhi">至</view>
                <view class="timebox row">
                  <input
                  @focus="focusFn"
                    style="width: 256rpx"
                    placeholder-class="placls"
                    placeholder="请输入年"
                    v-model="endyear" />
                  <input
                  @focus="focusFn"
                    placeholder-class="placls"
                    placeholder="月"
                    v-model="endmonth" />
                  <input type="number" placeholder-class="placls" v-model="endday" placeholder="日"
                /></view>
              </view> -->
              <view class="second_tab_con_item_title">客户类型</view>
              <view style="margin-bottom: 0" class="second_tab_con_item_list row">
                <view class="second_tab_con_item_list_item" v-for="item in type_list" :key="item.id"
                  :class="{ isactive: params.type == item.id }" @click="params.type = item.id">{{ item.title }}</view>
              </view>
              <view class="second_tab_con_item_title">客户等级</view>
              <view style="margin-bottom: 0" class="second_tab_con_item_list row">
                <view class="second_tab_con_item_list_item" v-for="item in level_list" :key="item.id"
                  :class="{ isactive: params.level_id == item.id }" @click="params.level_id = item.id">{{ item.title }}
                </view>
              </view>
              <view class="second_tab_con_item_title">客户来源</view>
              <view style="margin-bottom: 0" class="second_tab_con_item_list row">
                <view class="second_tab_con_item_list_item" v-for="item in source_list" :key="item.id"
                  :class="{ isactive: params.source_id == item.id }" @click="params.source_id = item.id">{{ item.title }}
                </view>
              </view>
              <view class="second_tab_con_item_title">绑定企微</view>
              <view style="margin-bottom: 0" class="second_tab_con_item_list row">
                <view class="second_tab_con_item_list_item" v-for="item in bind_list" :key="item.id"
                  :class="{ isactive: params.is_bind_qywx == item.id }" @click="params.is_bind_qywx = item.id">{{
                    item.name }}</view>
              </view>
              <view class="second_tab_con_item_title">筛选时间</view>
              <view class="second_tab_con_item_list row" style="padding-bottom: 50rpx">
                <view class="second_tab_con_item_list_item" v-for="item in time_list" :key="item.id"
                  :class="{ isactive: params.date_type == item.id }" @click="params.date_type = item.id">{{ item.name }}
                </view>
              </view>

            </scroll-view>
            <view class="flex-row btn_group">
              <view class="btn" @click="resetData">重置</view>
              <view class="btn bg_highlight" @click="filterData">确定</view>
            </view>
          </view>

          <view class="mask" @click="hideErjiSearch"></view>
          <!-- <view class="mask1" v-show="showMask"> </view> -->
        </view>
        <view class="second_tab_con quick" @touchmove.stop.prevent="disMove" v-if="quick_show">
          <view class="second_tab_con_i">
            <view class="tab_con_item" @click="toMyPath"> 我的 </view>
            <view class="tab_con_item" @click="toGonghai"> 公海 </view>
            <view class="tab_con_item" @click="departShow" v-if="is_editer"> 部门 </view>
          </view>
          <view class="mask" @click="hideErjiSearch"></view>
          <!-- <view class="mask1" v-show="showMask"> </view> -->
        </view>
        <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="depart_show">
          <view class="second_tab_con_i">
            <view class="second_tab_con_item member">
              <view class="second_tab_con_item_list row">
                <view style="width: 60%" class="flex-1">
                  <view class="second_tab_con_item_title">部门</view>
                  <tki-tree ref="tkitree" style="width: 100%; height: 600rpx" :range="department" childName="subs"
                    rangeKey="name" confirmColor="#4e8af7" @confirm="confirmDepartment" />
                </view>

                <scroll-view scroll-y class="second_tab_con_item_right member flex-1">
                  <view class="second_tab_con_item_title">成员</view>
                  <template v-if="userList.length">
                    <view class="lab-list row" v-for="item in userList" :key="item.id" @click="params.admin_id = item.id"
                      :class="{ active: item.id == params.admin_id }">
                      <view class="lab-item">{{ item.user_name }}</view>
                      <image class="ischeck" :src="`https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/${item.id == params.admin_id ? 'ischeck' : 'check'
                        }.png`"></image>
                    </view>
                  </template>
                  <view v-else>该部门暂无成员</view>
                </scroll-view>
              </view>
            </view>
            <view class="flex-row btn_group">
              <view class="btn" @click="resetData">重置</view>
              <view class="btn bg_highlight" @click="filterData">确定</view>
            </view>
          </view>
          <view class="mask" @click="hideErjiSearch"></view>
          <!-- <view class="mask1" v-show="showMask"> </view> -->
        </view>
      </view>
      <view class="mask" @click="showMask = false"></view>
    </view>
    <view class="list">
      <myList :arr="client_list" @onClick="onClickDetail" :showMore="is_editer" @more="more" @tel="tel"
        @searchAddress="searchAddress" type="qianzai"></myList>
    </view>
    <load-more :status="load_status"></load-more>
    <view @click="onClickPost" class="ent">
      <image src="../static/customer/lu.png" mode="widthFix"> </image>
    </view>

    <myPopup ref="show_more" :show="show_more" @hide="show_more = false">
      <view class="more_box">
        <view class="more_title row"> 更多操作 </view>
        <view class="more_list row">
          <view class="more_item" @click="toEditer">
            <view class="icon">
              <image :src="'/yidongduan/customer/<EMAIL>' | imageFilter('w_80')"></image>
            </view>
            <view class="name"> 维护资料 </view>
          </view>
          <view class="more_item" @click="toShenpi">
            <view class="icon">
              <image :src="'/yidongduan/customer/<EMAIL>' | imageFilter('w_80')"></image>
            </view>
            <view class="name"> 审批 </view>
          </view>
          <view class="more_item" @click="toFriend">
            <view class="icon">
              <image :src="'/yidongduan/customer/<EMAIL>' | imageFilter('w_80')"></image>
            </view>
            <view class="name"> 转交到同事 </view>
          </view>
          <view class="more_item" @click="toGenjin">
            <view class="icon">
              <image :src="'/yidongduan/customer/<EMAIL>' | imageFilter('w_80')"></image>
            </view>
            <view class="name"> 跟进 </view>
          </view>
          <!-- <view class="more_item">
            <view class="icon">
              <image :src="'/yidongduan/customer/<EMAIL>' | imageFilter('w_80')"></image>
            </view>
            <view class="name"> 审批 </view>
          </view> -->
        </view>
      </view>
    </myPopup>

    <my-popup ref="reason" :show="giveup_reason" @hide="giveup_reason = false" :touch_hide="true">
      <view class="reason" v-if="giveup_reason">
        <view class="close" @click="giveup_reason = false">×</view>
        <view class="label"> 请输入转到公海的原因 </view>
        <textarea v-model="giveup_content" placeholder="请输入转到公海的原因"></textarea>
        <view class="btn" @click="toSeas"> 转到公海 </view>
      </view>
    </my-popup>
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
import tabBar from "@/components/cusTabBar";
import loadMore from "@/components/loadMore.vue";
import myList from "./components/my_list";
import myPopup from "@/components/myPopup.vue";
import tkiTree from "@/components/tki-tree/tki-tree.vue"
export default {
  components: {
    myIcon,
    tabBar,
    loadMore,
    myList,
    tkiTree,
    myPopup
  },
  data() {
    return {
      source_list: [],
      type: "",
      params: {
        page: 1,
        form: 3, // 1:所有，2我的，3公海
        mobile: "",
        source_id: 0,
        tracking_id: 0,
        label: 0,
        type: 0,
        level_id: 0,
        is_bind_qywx: 0,
        date_type: 0,
        department_id: 0,
        admin_id: 0,
        c_type1: 4
      },
      bind_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "已绑定" },
        { id: 2, name: "未绑定" },
      ],
      time_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "今天" },
        { id: 2, name: "昨天" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
        { id: 5, name: "本月" },
        { id: 6, name: "上月" },
      ],
      load_status: "",
      client_list: [],
      color: "#4D7BFE",
      hasChoose: false,
      tracking_list: [],
      type_list: [],
      current_second_tab: "",
      showMask: false,
      status_list: [],
      tag_list: [],
      scroll_into_view_index: 0,
      scroll_into_view_label: 0,
      label_list: [],
      level_list: [],
      is_search: "",
      currentType: "来源",
      currentStatus: "状态",
      currentLabel: "标签",
      currentMore: "更多",
      startyear: "",
      startmonth: "",
      startday: "",
      endyear: "",
      endmonth: "",
      endday: "",
      depart_show: false,
      department: [],
      userList: [],
      show_more: false,
      quick_show: false,
      user_info: {},
      is_editer: false,
      has_roles: false,
      detail: {

      },
      giveup_content: "",
      giveup_reason: false
    };
  },
  filters: {
    filterTime(val) {
      let new_val = val.replace(/-/g, "/");
      var value = new Date(new_val);
      //获取要过滤的时间与当前时间的时间差。（单位秒）
      const d = Math.floor((Date.now() - value.getTime()) / 1000);
      const arr = [
        0,
        60,
        60 * 60,
        60 * 60 * 24,
        60 * 60 * 24 * 30,
        60 * 60 * 24 * 30 * 12,
      ];
      const _arr = ["刚刚", "分钟前", "小时前", "天前", "月前", "年前"];
      /* 循环判断 */
      for (var i = arr.length - 1; i >= 0; i--) {
        if (d > arr[i]) {
          /* 使用三元进行判断 */
          return i == 0 ? _arr[i] : Math.floor(d / arr[i]) + _arr[i];
        }
      }
      return value;
    },
  },


  onLoad(options) {
    let token = uni.getStorageSync("wxwork_token");
    if (!token) {
      if (this.$isWxWork() == 'wxwork') {
        return
      } else {
        localStorage.setItem('backUrl', location.href)
        this.$router.push("https://yun.tfcs.cn")
      }
      // 未登录中断请求
    }
    if (options.tel) {
      this.params.mobile = options.tel;
    }
    uni.$on("getDataAgain", () => {
      this.params.page = 1
      this.getDataList()
    })
    this.user_info = uni.getStorageSync("userInfo") ? JSON.parse(uni.getStorageSync("userInfo")) : {}

    if (options.wisdomWork) {
      this.params.data_type = options.data_type
      if (options.c_type4) {
        this.params.c_type4 = options.c_type4
      }
    }
    let name = "潜在客户";
    wx.setNavigationBarTitle({
      title: name,
    });
    this.getSourceData();
    this.getDataList();
    this.getTypeData();
    this.getStatusData();
    this.getTagData();
    this.getUserInfo()
    this.getLevelList();
    this.getStateList()
  },
  onPageScroll() {
    this.quick_show = false
    this.current_second_tab = ''
  },
  onUnload() {
    uni.$off("getDataAgain")
  },
  async onPullDownRefresh() {
    uni.$emit("getDataAgain")
    await this.$Utils.sleep(600)
    uni.stopPullDownRefresh();
  },
  methods: {
    focusFn() {
      this.current_second_tab = 4
      setTimeout(() => {

        console.log(this.current_second_tab, 'this.current_second_tab');
      }, 3000);
    },
    disMove() { },
    getLevelList() {
      this.$ajax.get("/qywx/level/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.level_list = [{ id: 0, title: "全部" }, ...res.data];
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    checkStatus(item) {
      if (item.push_type == 2) {
        // 手动认领  
        // 潜在用户 我司成交的不可认领
        if (item.tracking_identify == 1) {
          return true
        }

        // 掉工转公标记的可以领取 
        if (item.public2_status > 0) {
          return false
        }
        // 其他情况 不可领取 
        return true
      }
      return false
      // if (item.public2_status>0 || )
    },
    quickShow() {
      this.quick_show = true
      this.depart_show = false
    },
    toQianzai() {
      this.quick_show = false
    },
    toMyPath() {
      this.$navigateTo('/customer/my_list')
      this.quick_show = false
    },
    toGonghai() {
      this.$navigateTo('/customer/seas_list')
      this.quick_show = false
    },
    toSea() {
      this.show_more = false
      this.giveup_reason = true
    },
    getStateList() {
      this.$ajax.get("/qywx/tracking/list", { type: 4 }, res => {
        if (res.statusCode == 200) {
          this.stateList = res.data
        }
      }, () => {

      })
    },
    toShenpi() {
      let shenPiInfo = {
        is_del: this.detail.is_del,
        is_state: this.detail.is_state,
        state_list: this.detail.state_list,
        stateList: this.stateList
      }
      uni.setStorageSync("shenpi", JSON.stringify(shenPiInfo))
      this.$navigateTo("/house/applyApprove?id=1&house_id=" + this.detail.id)
    },
    toSeas() {
      this.$ajax.post("/qywx/client/discard", { ids: this.detail.id + '', content: this.giveup_content }, (res) => {
        console.log(res);
        if (res.statusCode == 200) {
          uni.showToast({
            title: res.message || '操作成功',
            icon: "none"
          })
          this.giveup_reason = false
          this.getDataList()
        }
      })
    },
    more(e) {
      this.detail = e || {}
      this.show_more = true
    },
    searchAddress(e) {
      this.$ajax.get(`/admin/crm/client/query_mobile_place/${e.id}`, {}, res => {
        if (res.statusCode == 200) {
          console.log(res);
          let index = this.client_list.findIndex(item => item.id == e.id)
          this.$set(this.client_list[index], 'mobile_place', res.data)
        }
      })
    },
    getUserInfo() {
      this.$ajax.get("/qywx/common/query", {}, (res) => {
        if (res.statusCode === 200) {
          this.login_user_info = res.data
        }
      });
    },
    roleStatus(item) {
      if (!Array.isArray(item.admin_list) && item.admin_list == '') {
        item.admin_list = []
      }
      let mangers = item.admin_list
      let keyuanManger = 0, whr = 0, has_roles = false
      if (mangers.includes(this.login_user_info.id + "")) {
        keyuanManger = 1
      }
      if (this.login_user_info.id == item.follow_id) {
        whr = 1
      }
      if (keyuanManger == 1 || whr == 1 || (item.follow_id > 0 && item.follow_id == this.login_user_info.id)) {
        has_roles = true
      } else {
        has_roles = false
      }
      return has_roles
      // if (this.user_detail.follow_id != this.login_user_info.id) {
      //   this.has_roles = false
      // }
    },
    tel(e) {
      this.$ajax.get(`/admin/crm/client/see_tel/${e.id}`, {}, (res) => {
        if (res.statusCode === 200) {
          let view_tel = res.data;
          uni.setStorageSync("telInfo", JSON.stringify(view_tel))
          let url = `/customer/demand?id=${e.id}&tel=1&name=${encodeURIComponent(e.cname)}&telType=${e.call_open_crm}&has_roles=${this.roleStatus(e) ? 1 : 0}`

          this.$navigateTo(url)
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    departShow() {
      this.quick_show = false
      setTimeout(() => {
        this.depart_show = true
      }, 300);
      // this.getUserList()
      this.$ajax.get('/admin/personnelMatters/departmentsAndMembers', {}, res => {
        console.log(res);
        if (res.statusCode == 200) {
          this.department = res.data
          this.userList = this.department.length ? (this.department[0].user || []) : []
        }
      })
    },
    confirmDepartment(e) {
      let depart = e[0]
      this.params.department_id = depart.id
      this.userList = depart.user
    },
    getTagData() {
      this.$ajax.get("/qywx/tag/search", {}, (res) => {
        if (res.statusCode === 200) {
          let arr = res.data.qiwei_tag.map((item) => {
            return {
              id: item.id,
              name: item.name,
              label: item.taggroup,
            };
          });
          this.tag_list = arr.concat(res.data.system_tag);
          this.label_list = this.tag_list[0].label;
        }
      });
    },
    getTypeData() {
      this.$ajax.get("/qywx/type/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.type_list = [{ id: 0, title: "全部" }, ...res.data];
        }
      });
    },
    getStatusData() {
      this.$ajax.get("/qywx/tracking/list", { type: 1 }, (res) => {
        if (res.statusCode === 200) {
          this.status_list = [{ id: 0, title: "全部" }, ...res.data];
        }
      });
    },
    getSourceData() {
      this.$ajax.get("/qywx/source/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.source_list = [{ title: "全部", id: 0 }, ...res.data];
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    toGenjin() {
      let url = `/customer/demand?id=${this.detail.id}`
      this.$navigateTo(url);
    },
    toFriend() {
      this.$navigateTo(
        `/customer/transfer_customer?id=${this.detail.id}`
      );
    },

    toEditer() {
      this.$navigateTo(`uphold?type=2&id=${this.detail.id}`);
    },
    onClickTab(e) {
      this.params.source_id = e.id;
      this.params.page = 1;
      this.getDataList();
    },
    onSearch() {
      this.params.page = 1;
      this.getDataList();
    },
    getDataList() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.client_list = [];
      }
      this.$ajax.get("/qywx/client/search", this.params, (res) => {
        this.load_status = "loadend";
        if (res.statusCode === 200) {

          let admin_list = res.data && res.data.data.length ? res.data.data[0].admin_list : []
          if (admin_list.includes(this.user_info.id + '')) {
            this.is_editer = true
          }
          this.client_list = this.client_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        }
      });
    },
    onClickDetail(item) {



      this.$ajax.get('/admin/crm/client/verify_follow', {}, res => {
        if (res.statusCode == 200) {
          if (res.data && res.data.id > 0 && res.data.client_id > 0) {
            uni.showModal({
              title: '提示',
              content: '您有查看电话未跟进 去跟进？',
              success: (result) => {
                if (result.confirm) {
                  this.$navigateTo(
                    `/customer/detail?id=${res.data.client_id}&form=${this.params.form}&source=1`
                  );
                  return
                } else if (result.cancel) {
                  console.log('用户点击取消');
                }
              }
            })
          } else {
            this.$navigateTo(
              `/customer/detail?id=${item.id}&form=${this.params.form}&source=1&un_renling_status=${this.checkStatus(item)}`
            );
          }
        } else {
          this.$navigateTo(
            `/customer/detail?id=${item.id}&form=${this.params.form}&source=1&un_renling_status=${this.checkStatus(item)}`
          );
        }
      })



    },
    onClickPost() {
      this.$navigateTo("uphold?type=1");
    },
    hideErjiSearch() {
      this.current_second_tab = "";
      this.quick_show = false
      this.showMask = false;
    },
    changeSecondTab(index) {
      if (this.current_second_tab == index)
        return (this.current_second_tab = "");
      this.current_second_tab = index;
      this.showMask = true;
    },
    checkFilter(index, item) {
      this.scroll_into_view_index = index;
      this.label_list = item.label;
    },
    resetData() {
      this.params = {
        page: 1,
        form: 3, // 1:所有，2我的，3公海
        mobile: "",
        source_id: 0,
        tracking_id: 0,
        label: 0,
        type: 0,
        level_id: 0,
        is_bind_qywx: 0,
        date_type: 0,
      };
      this.current_second_tab = "";
      this.currentType = "来源";
      this.currentStatus = "状态";
      this.currentLabel = "标签";
      this.depart_show = false
      this.showMask = false;
      this.getDataList();
    },
    filterData() {
      if (this.startyear && this.startmonth && this.startday) {
        let month =
          this.startmonth < 10 ? "0" + this.startmonth : this.startmonth;
        let day = this.startday < 10 ? "0" + this.startday : this.startday;
        this.params.start_date = this.startyear + "-" + month + "-" + day;
      }
      if (this.endyear && this.endmonth && this.endday) {
        let month = this.endmonth < 10 ? "0" + this.endmonth : this.endmonth;
        let day = this.endday < 10 ? "0" + this.endday : this.endday;
        this.params.end_date = this.endyear + "-" + month + "-" + day;
      }
      this.params.page = 1;
      this.getDataList();
      this.current_second_tab = "";
      this.depart_show = false
      this.showMask = false;
    },
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>
<style scoped lang="scss">
page {
  background: #f6f6f6;
  color: #2e3c4e;
}

.c2 {
  color: #8a929f;
}

.list {
  padding: 24rpx;

  .info {
    background: #fff;
    border-radius: 24rpx;
    padding: 24rpx;
    margin-bottom: 24rpx;

    .claim {
      text-align: center;
      height: 80rpx;
      line-height: 80rpx;
      font-weight: 500;
      margin: 24rpx 0;
      border-radius: 12rpx;

      &.c1 {
        background: #eaf3ff;
        color: #2d84fb;
      }

      &.c2 {
        color: #eaf3ff;
        background: #2d84fb;
      }
    }

    .content {
      >text {
        line-height: 40rpx;
      }
    }

    .type {
      margin: 24rpx 0;
      line-height: 28rpx;

      text {
        font-size: 22rpx;
        margin-right: 48rpx;
      }
    }

    .pers {
      align-items: center;
      justify-content: space-between;

      .time {
        font-size: 22rpx;
      }

      .left {
        align-items: center;

        .level {
          width: 32rpx;
          height: 32rpx;
          line-height: 32rpx;
          text-align: center;
          color: #fff;
          font-weight: 500;
          font-size: 22rpx;
          background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
          border-radius: 4rpx;
        }

        .name {
          font-weight: 500;
        }

        text {
          margin-left: 24rpx;
        }

        .pic {
          width: 50rpx;
          height: 50rpx;
          border-radius: 50%;
        }

        .qw {
          width: 32rpx;
          margin-left: 24rpx;
        }
      }
    }
  }
}

.ent {
  display: inline-block;
  position: fixed;
  right: 20rpx;
  bottom: 300rpx;

  image {
    width: 160rpx;
    height: 160rpx;
  }
}

.sort {
  align-items: center;
  justify-content: space-between;
}

.top {
  background: #fff;
  padding: 12rpx 32rpx 0 32rpx;
  position: sticky;
  top: 0;
  z-index: 10;

  .search {
    background: #f6f6f6;
    border-radius: 12rpx;
    align-items: center;
    padding: 20rpx;

    input {
      font-size: 28rpx;
      margin-left: 24rpx;
      flex: 1;
    }
  }
}

.second_tab {
  padding: 24rpx;
  position: sticky;
  top: 200rpx;
  z-index: 6;
  background: #fff;
  justify-content: space-between;

  .second_tab_item {
    max-width: 20%;
    color: #282a2f;
    white-space: nowrap;

    .item_name {
      font-size: 28rpx;
      margin-right: 8rpx;
      color: #8a929f;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
    }

    .sanjiao {
      margin-top: 10rpx;
      width: 0;
      height: 0;
      border: 13rpx solid;
      border-color: #d8d8d8 transparent transparent transparent;
    }
  }

  .second_tab_con {
    position: absolute;
    top: 80rpx;
    left: 0;
    right: 0;
    z-index: 5;
    // padding: 0 48rpx;
    margin-left: -32rpx;
    margin-right: -32rpx;
    background: #fff;

    &.quick {
      top: 0;
      padding: 20rpx 0;

      .tab_con_item {
        padding: 20rpx 0;
      }
    }

    .second_tab_con_i {
      background: #fff;
      padding: 0 48rpx;
      max-height: 60vh;
      overflow-y: auto;
    }

    .second_tab_con_item {
      padding: 20rpx 0;

      &.member {
        .second_tab_con_item_list {
          margin-bottom: 0;
        }
      }

      &_title {
        margin: 20rpx 0;
        font-size: 32rpx;
      }

      &_list {
        flex-wrap: wrap;

        // margin-bottom: 150px;
        .timebox {
          justify-content: space-between;

          .placls {
            font-size: 24rpx;
          }

          input {
            height: 70rpx;
            padding: 8rpx 20rpx;
            width: 160rpx;
            border-radius: 10rpx;
            border: 2rpx solid #eeeeee;
            text-align: center;
          }
        }

        .zhi {
          text-align: center;
          margin: 30rpx 0;
        }

        &_item {
          width: 30%;
          text-align: center;
          line-height: 70rpx;
          border-radius: 10rpx;
          margin: 4rpx 1.66%;
          border: 2rpx solid #eeeeee;

          &.isactive {
            border-radius: 10rpx;
            background: #f3f7fe;
            border: 2rpx solid #3172f6;
            color: #3172f6;
          }
        }
      }

      &_left {
        width: 50%;
        background: #fff;
        margin: 0 40rpx;
      }

      &_right {
        background: #f9f9f9;
        margin: -24rpx 0;
        line-height: 80rpx;
        padding-left: 48rpx;
        padding-top: 24rpx;

        .lab-list {
          line-height: 80rpx;
          align-items: center;
          justify-content: space-between;

          .lab-item {
            &.active {
              color: #3172f6;
            }

            word-break: break-all;
          }

          .ischeck {
            margin-right: 48rpx;
            width: 28rpx;
            height: 28rpx;
          }
        }
      }
    }

    .btn_group {
      min-height: 128rpx;
      box-shadow: 0 -6rpx 10rpx 0 #f1f1f1;
      padding: 24rpx 48rpx;
      margin-left: -48rpx;
      margin-right: -48rpx;

      .btn {
        text-align: center;
        width: 30%;
        border-radius: 6rpx;
        background: #e5eeff;
        color: #3172f6;
        line-height: 80rpx;

        &.bg_highlight {
          margin-left: 20rpx;
          background-color: $color-primary;
          width: 70%;
          color: #fff;
        }
      }
    }

    .mask {
      position: fixed;
      z-index: -1;
      width: 100%;
      height: 100vh;
      left: 0;
      background-color: #000;
      opacity: 0.5;
      transition: 0.26s;
    }
  }
}

.filter_list {
  overflow: hidden;

  .filter_labels {
    font-size: 32rpx;
    font-size: 26rpx;
    padding-left: 48rpx;
    line-height: 80rpx;

    &.active {
      color: #3172f6;
    }
  }
}

.top_c {
  .depart {
    padding: 14rpx 24rpx;
    border-radius: 4rpx;
    background: #f3f4f8;
    align-items: center;
    margin-left: 10rpx;

    // justify-content: center;
    .depart_name {
      color: #000000;
      font-size: 28rpx;
      margin-right: 10rpx;
    }

    .sanjiao {
      width: 0;
      height: 0;
      margin-top: 10rpx;
      border: 10rpx solid transparent;
      border-top-color: #000;
    }
  }
}

.second_tab_con_item_list ::v-deep .tki-tree-view {
  top: 0;
}

.second_tab .second_tab_con_item_right {
  &.member {
    height: 670rpx;
    background: #fff;

    .second_tab_con_item_title {
      margin-top: 0;
    }
  }
}

.more_box {
  border-radius: 40rpx 40rpx 0px 0px;
  background: #ffffff;

  .more_title {
    justify-content: center;
    color: #8a929f;
    font-size: 32rpx;
    padding: 24rpx;
    border-bottom: 2rpx solid #f2f2f2;
  }

  .more_list {
    padding: 24rpx;
    flex-wrap: wrap;

    .more_item {
      width: 25%;
      justify-content: center;
      align-items: center;
      padding: 24rpx 0;

      .icon {
        width: 40rpx;
        height: 40rpx;
        overflow: hidden;

        image {
          width: 100%;
          height: 100%;
        }
      }

      .name {
        color: #8a929f;
        margin-top: 24rpx;
        font-size: 24rpx;
      }
    }
  }
}

.reason {
  width: 80vw;
  height: 400rpx;
  margin: calc(100% - 200rpx) auto;
  padding: 40rpx;
  border-radius: 20rpx;
  background: #fff;
  position: relative;

  .close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    font-size: 48rpx;
  }

  textarea {
    margin-top: 16rpx;
    padding: 12rpx 32rpx;
    box-sizing: border-box;
    background: #f5f7fa;
    border: 2rpx solid #e8e8e8;
    height: 280rpx;
    width: 100%;
  }

  .btn {
    display: inline-block;
    padding: 20rpx 20rpx;
    background: #2d84fb;
    color: #fff;
    margin: 20rpx auto 0;
  }
}
</style>
