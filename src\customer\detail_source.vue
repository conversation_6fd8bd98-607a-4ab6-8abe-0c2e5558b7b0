<template>
  <view class="" @click.capture="onPageClick">
    <view v-if="is_show_content">
      <!-- <mySkeleton v-if="is_show_skeleton" :showAvatar="false" :row="9" :showTitle="true"></mySkeleton> -->
      <!--  v-if="!is_show_skeleton" -->
      <view class="top" v-if="!is_show_skeleton">
        <view class="top_img">
          <view class="top_img_main">
            <view class="top_img_follow">
              <!-- 客户数据 -->
              <template v-if="user_detail.follow_id > 0">
                <view class="top_img_cus row">
                  <!-- 图片 -->
                  <view
                    v-if="user_detail.get_time != '' && user_detail.get_time != undefined || user_detail.take_num != '' && user_detail.take_num != undefined">
                    <image src="../static/icon/index/gantan.png" style="width: 32rpx;height: 32rpx;"></image>
                  </view>
                  <!-- 跟进 -->
                  <view class="top_gj">
                    <span v-if="user_detail.get_time != '' && user_detail.get_time != undefined">
                      跟客{{ user_detail.get_time | getFollowDay(user_detail.last_follow_time) }}
                    </span>
                    <!-- <span v-if="user_detail.take_num != '' &&
                    user_detail.take_num != undefined &&
                    user_detail.get_time != '' &&
                    user_detail.get_time != undefined
                    " style="margin: 0 5px">
                    |
                  </span> -->
                    <!-- <span v-if="user_detail.take_num != '' && user_detail.take_num != undefined">
                    {{ user_detail.take_num | getTakeLook(user_detail.take_num) }}
                  </span> -->
                  </view>
                  <!-- 带看 -->
                  <view v-if="user_detail.take_num != '' && user_detail.take_num != undefined">
                    带看({{ user_detail.take_num | getTakeLook(user_detail.take_num) }})
                  </view>
                </view>
                <!-- 时间数据 -->
                <div class="auto-scroll-container">
    <div class="auto-scroll-content" :style="{ transform: `translateY(${translateY}px)` }">
      <div v-for="(item, index) in userArrary" :key="index" class="scroll-item">{{ item.created_at }}</div>
    </div>
  </div>
              </template>
              <template v-if="user_detail.follow_id == 0">
                <view class="top_img_cus row">
                  <span class="un_follow"> 待认领 </span>
                </view>
                <!-- 掉工 -->
                <view class="top_img_time drop_to_sea">
                  {{
                    user_detail.public2_status == 1
                    ? '已转公'
                    : user_detail.public2_status == 2
                      ? '已掉公'
                      : ''
                  }}
                </view>
              </template>
            </view>
          </view>
        </view>
        <view class="top-cards" style="margin-top: -320rpx">
          <view class="row">
            <view class="top-card-left">
              <headImg :customer="user_detail" size="large"/>      
            </view>
            <view class="top-card-right">
              <view class="top-card-right-top row">
                <text class="top-card-right-top_name nameText">{{ user_detail.cname }}</text>
                <image v-if="user_detail.wxqy_id" class="top-card-right-top_qw" src="/static/customer/qw.png"
                  mode="widthFix" />
                <image class="pic top-card-right-top_sex" :src="`https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/${user_detail.sex == 1 ? 'nan' : 'nv3'
                  }.png`" mode="aspectFill" />
                <!-- https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan.png -->
                <!-- <span class="top-card-right-top_tracking">{{
                  user_detail.tracking ? user_detail.tracking.title : ''
                }}</span> -->
                <!-- <text
                  :style="{
                    backgroundColor: user_detail.level.color,
                  }"
                  class="top-card-right-top_level"
                  v-if="user_detail.level"
                  >{{ user_detail.level ? user_detail.level.title : '--' }}</text
                > -->
              </view>
              <!-- <image @click="showAction" v-if="user_detail.level && user_detail.level.title" class="setting_level"
                mode="widthFix" :src="('/yidongduan/customer/level/' + user_detail.level.title + '_d.png')
                  | imageFilter('w_80')
                  "></image> -->

                
         <view v-if="user_detail.level && user_detail.level.title">
          <image @click="showAction" v-if="user_detail.level && user_detail.level.title == 'A'" class="setting_level"
                mode="widthFix" :src="('https://img.tfcs.cn/backup/yidongduan/customer/level/A_d.png')
                  | imageFilter('w_80')
                  "></image>
              <image @click="showAction" v-if="user_detail.level && user_detail.level.title == 'B'" class="setting_level"
                mode="widthFix" :src="('https://img.tfcs.cn/backup/yidongduan/customer/level/B_d.png')
                  | imageFilter('w_80')
                  "></image>
              <image @click="showAction" v-if="user_detail.level && user_detail.level.title == 'C'" class="setting_level"
                mode="widthFix" :src="('https://img.tfcs.cn/backup/yidongduan/customer/level/C_d.png')
                  | imageFilter('w_80')
                  "></image>
              <image @click="showAction" v-if="user_detail.level && user_detail.level.title== 'D'" class="setting_level"
                mode="widthFix" :src="('https://img.tfcs.cn/backup/yidongduan/customer/level/20230915161124.png')
                  | imageFilter('w_80')
                  "></image>
         </view>
              <image @click="showAction" v-else class="setting_level"
                mode="widthFix" :src="('https://img.tfcs.cn/backup/yidongduan/customer/level/20230915161133.png')
                  | imageFilter('w_80')
                  "></image>
                <!-- <image @click="showAction" v-else class="setting_level" mode="widthFix"
                :src="'/yidongduan/customer/level/un_d.png' | imageFilter('w_80')"></image> -->
              <!-- <image src="../static/icon/index/xia.png" style="width:32rpx;height:32rpx" class="setting_levels"></image> -->
              <!-- <span v-else   @click="showAction"  class ='un_level'>
                未设置
              </span> -->
              <!-- <image
                @click="showAction"
                v-if ='user_detail.level && ser_detail.level.title'
                class="setting"
                :src="'/yidongduan/customer/level/'+ user_detail.level.title+'.png' | imageFilter('w_80')"
              ></image> -->
              <!-- <view class="top-card-right-bottom">
                客户性别：{{ user_detail.sex == 1 ? '男' : user_detail.sex == 2 ? '女' : '--' }}
              </view> -->
              <span class="top-card-right-top_tracking">
                <span style="color:  rgba(41, 44, 57, 0.70);font-size: 28rpx;">
                  状态：
                </span>
                {{
                  user_detail.tracking ? user_detail.tracking.title : ''
                }}</span>
            </view>
          </view>
          <view class="salesProgress_card">
            <salesProgress :list="progress" @clickItem="clickProgress"></salesProgress>
          </view>
          <!-- <view class="top-card-bot-bottom row">
            <span>跟进状态：{{ user_detail.follow_num > 0 ? '已跟进' : '未跟进' }}</span>
            <span>创建时间：{{ user_detail.created_at }}</span>
          </view> -->
        </view>
        <view class="top-card">
          <!-- <view class="top-card-title row">基本信息
            <myIcon type="you"></myIcon>
          </view> -->
          <view class="top_tel_all" v-if="user_detail.mobile">
            <view style="display: flex; flex-direction: row;">
              <view class="top_tel_tel">电话</view>
              <view style="margin-left: 40rpx;" class="top_marginleft">
                <view class="tong_tel" :class="{
                  has_follow:
                    user_detail.last_call_follow && user_detail.last_call_follow.client_id > 0,
                }" v-if="!is_view_tel" @click="show_last_call_follow_content = !show_last_call_follow_content">
                  <span class="un_tong" :class="{
                    tong:
                      user_detail.last_call_follow && user_detail.last_call_follow.call_status > 0,
                  }">{{ user_detail.mobile | mobileFilter(isTrans) }}</span>
                </view>
                <!-- <view class="top-card-content-right" :class ='{has_follow:user_detail.last_call_follow && user_detail.last_call_follow.client_id>0}' v-if="is_view_tel">{{ user_detail.mobile }}</view> -->
                <view v-if="!user_detail.mobile_place" @click="searchAddress">归属地查询</view>
                <view v-else>{{ user_detail.mobile_place }}</view>
                <view v-if="user_detail.last_call_follow &&
                  user_detail.last_call_follow.content &&
                  show_last_call_follow_content
                  ">{{ user_detail.last_call_follow.content }}</view>
              </view>
            </view>
            <!-- 外呼 -->
            <view class="Outbound" @click="setViewTel">{{
              user_detail.call_open_crm > 1 ? '外呼' : '查看'
            }}</view>
          </view>
          <view  class="top-card-content row" v-else>
            <view class="top-card-content-label">电话</view>
            <view class="top-card-content-right">无</view>
          </view>
          <!-- 城市 -->
          <view class="top-card-content row" v-if="user_detail.is_show_city == 1">
            <view class="top-card-content-label">城市</view>
            <view class="top-card-content-right">{{
              city_name
            }}</view>
          </view>
          <view class="top-card-content row" v-if="user_detail.source">
            <view class="top-card-content-label">来源</view>
            <view class="top-card-content-right">{{userDetailSourceLabel }}</view>
          </view>
          <view class="top-card-content row" v-if="user_detail.type">
            <view class="top-card-content-label">类型</view>
            <view class="top-card-content-right">{{ typeFilter(user_detail.type) }}</view>
          </view>
          <view class="content" v-if="user_detail.intention_community">
            <view class="top_tel_tel">意向</view>
            <view class="content_detail">{{ user_detail.intention_community }}</view>
          </view>
          <!-- 标签 -->
<view style="display: flex;flex-direction: row;" class="tagcontenter" v-if="has_roles">
  <view class="top-card-content-labels" >标签</view>
  <view>
  </view>
  <view class="top-card-label-lists row" style="width: 100%;" v-if="user_detail.label_name && user_detail.label_name.length > 0" >
    <view @click="onClickTag" class="addtagAll">+标签</view>
  <block v-for="(item, index) in user_detail.label_name" :key="index">
    <!-- <view class="l-title tag_item">{{ item.name }}</view> -->
    <block v-for="(item1, index1) in item.son" :key="index1">
      <view class="tag_item" :key="item1.id+'_'+index1">
        <view class="tag_t">
          {{ item1.name }}
        </view>
      </view>
    </block>
  </block>
  </view>
  <view @click="onClickTag" class="addtagAll" v-else>+标签</view>
      </view>
             <!-- 备注 -->

             <view class='top-card-contenter' v-if="isOpenRemark">
            <view style="color: rgba(41, 44, 57, 0.30);font-size: 32rpx;width: 15%;" class="beizu">备注</view>
            <view style="width: 90%;">
              <textarea style="display: flex;flex-direction: row;" @blur="onConfirmRemark"
                v-model="content" auto-height="true" class='textarear'
                :disabled="!has_roles" @input="updateCount" placeholder-class="placeholderClass"
                maxlength='100'></textarea>
              <view style="text-align: right;padding-bottom: 24rpx;">{{ count || content ? content.length : 0 }} / {{
                maxLength }}</view>
            </view>
          </view>
          </view>
   
        </view>
        <view class="top-cardser fixed">
          <tabBar :fixedTop="false" :nowIndex="nowIndex" :tabs="tabs_list" @click="onClickTabs"></tabBar>
        </view>
        <!-- 维护人 -->
        <view class="top-card" style="background: none; padding: 0;margin-bottom: 180rpx" v-if="is_tabs == 9">
          <customerMaintainer :datas="user_detail" :current="current" @pageClick="onRegisterPageClick" @addShareFollowerSuccess="onAddShareFollowerSuccess"></customerMaintainer>
        </view>
        <!-- 动态 -->
        <view class="top-card" v-if="is_tabs == 1" style="margin-bottom: 100px;">
          <view class="top-card-title card-border-line row">2022年01月01日 </view>
        </view>
        <view class="pic_box top-card" v-if="is_tabs == 2">
          <view class="basic-info-box">
            <view class="basic-info-title">基本信息</view>
            <view class="basic-info-content">
              <text class="content-title">客户编号</text>
              <view class="content-value" @click="copyContent(user_detail.id)">
                <text>{{ user_detail.id }}</text>
                <image class="copy-img" :src="'/static/admin/customer/<EMAIL>' | imgDomain" mode="widthFix" />
              </view>
            </view>
            <!-- 客户行为 -->
            <view class="basic-info-content">
              <text class="content-title">客户行为</text>
              <text :class="user_detail.last_behavior_day != '' && user_detail.last_behavior_day != undefined
                ? 'blueInfo'
                : ''
                ">
                {{
                  user_detail.last_behavior_day != '' && user_detail.last_behavior_day != undefined
                  ? user_detail.last_behavior_day
                  : '--'
                }}
              </text>
              <text v-if="user_detail.last_behavior_day != '' && user_detail.last_behavior_day != undefined
                  ">天内</text>
            </view>
            <!-- 活跃时间 -->
            <view class="basic-info-content">
              <text class="content-title">活跃时间</text>
              <template v-if="user_detail.start_date || user_detail.end_date">
                <text style="color: #3d91ff" v-if="user_detail.start_date">{{
                  user_detail.start_date
                }}</text>
                <text style="color: #3d91ff" v-if="user_detail.start_date && user_detail.end_date">/</text>
                <text style="color: #3d91ff" v-if="user_detail.end_date">{{
                  user_detail.end_date
                }}</text>
              </template>
              <template v-else> -- </template>
              <text v-if="user_detail.start_date || user_detail.end_date">点</text>
            </view>

            <view class="basic-info-content" v-if="user_detail&&user_detail.is_show_city==1">
              <text class="content-title">所在城市</text>
              <view class="content-value">
                <text v-if="user_detail.province&&user_detail.city&&user_detail.area">{{ user_detail.province.name +'/'+ user_detail.city.name +'/'+user_detail.area.name }}</text>
                <text v-else>--</text>
              </view>
            </view>
            <view class="basic-info-content">
              <text class="content-title">进线</text>
              <view class="content-value">
                <template v-if="user_detail.jinxian && /\d+/.test(user_detail.jinxian)">
                  <text class="blueInfo">{{ user_detail.jinxian.match(/\d+/)[0] }}</text>
                  <text>{{ user_detail.jinxian.replace(/\d+/, '') }}</text>
                </template>
                <template v-else-if="user_detail.jinxian">
                  <text>{{user_detail.jinxian}}</text>
                </template>
                <template v-else>
                  <text>--</text>
                </template>
              </view>
            </view>
            <view class="basic-info-content">
              <text class="content-title">邀约</text>
              <view class="content-value">
                <template v-if="user_detail.yaoyue && /\d+/.test(user_detail.yaoyue)">
                  <span class="blueInfo">{{ user_detail.yaoyue.match(/\d+/)[0] }}</span>
                  <span>{{ user_detail.yaoyue.replace(/\d+/, '') }}</span>
                </template>
                <template v-else-if="user_detail.yaoyue">
                  <span>{{user_detail.yaoyue}}</span>
                </template>
                <template v-else>
                  <span>--</span>
                </template>
              </view>
            </view>
            <view class="basic-info-content">
              <text class="content-title">流转</text>
              <view class="content-value">
                <template v-if="user_detail.liuzhuan">
                  <span class="blueInfo">{{ user_detail.liuzhuan}}</span>
                  次
                </template>
                <template v-else>
                  <span>--</span>
                </template>
              </view>
            </view>
            <view class="basic-info-content">
              <text class="content-title">带看</text>
              <view class="content-value">
                {{ user_detail.take_date || "--" }}
              </view>
            </view>

            <!-- 客户偏好 -->
            <view class="basic-info-content">
              <text class="content-title">客户偏好</text>
              <text>{{ user_detail.like || '--' }}</text>
            </view>
            <!-- 线索 -->
            <view class="basic-info-content">
              <text class="content-title">线 索</text>
              <text :class="user_detail.operation_log != '' && user_detail.operation_log != undefined
                ? 'redInfo'
                : ''
                ">
                {{ user_detail.operation_log || 0 }}
              </text>
              <text v-if="user_detail.operation_log != '' && user_detail.operation_log != undefined">条</text>
            </view>
            <!-- 社群 -->
            <view class="basic-info-content">
              <text class="content-title">社 群</text>
              <text>{{ user_detail.group_list || '0' }}</text>个
            </view>
            <!-- 好友 -->
            <view class="basic-info-content">
              <text class="content-title">好 友</text>
              <text>
                {{
                  user_detail.qywx_info && user_detail.qywx_info.name
                  ? user_detail.qywx_info.name
                  : '--'
                }}
              </text>
            </view>
            <!-- 报备 -->
            <view class="basic-info-content">
              <text class="content-title">报 备</text>
              <text :class="user_detail.customer_list != '' && user_detail.customer_list != undefined
                    ? redInfo
                    : ''
                  ">
                {{ user_detail.customer_list || '--' }}
              </text>
              <text v-if="user_detail.customer_list != '' && user_detail.customer_list != undefined">条</text>
            </view>
            <!-- 报备状态 -->
            <view class="basic-info-content">
              <text class="content-title">报备状态</text>
              {{ user_detail.customer_status || '--' }}
              <template v-if="user_detail.customer_day">
                ({{ user_detail.customer_day }}天前)
              </template>
            </view>
            <!-- 最近报备 -->
            <view class="basic-info-content">
              <text class="content-title">最近报备</text>
              {{ user_detail.customer_build_name || '--' }}
            </view>
            <view class="basic-info-content">
              <text class="content-title">跟客</text>
              {{ user_detail.last_follow_day | dayFilter(user_detail.last_follow_time,user_detail.get_time) }}天
            </view>
            <view class="basic-info-content" v-if="user_detail.deal_user">
              <text class="content-title">成交周期</text>
              {{ user_detail.deal_user.time | dayFilter(user_detail.created_at, user_detail.deal_at) }}天
            </view>
          </view>
         
        </view>
        <!-- 跟进列表 -->
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 3">
          <view class="follow-card" style="margin-bottom: 100px; background: #fff;" @click="hideShow">
            <view class="time_line">
              <view class="item" :class="{ current: item.is_finish !== 0 }" v-for="(item, index) in follow_list_new"
                :key="index">
                  <!-- <view class="title">{{ item.description }}</view> -->
                  <view class="time_line_itme_header">
                    <view class="time">{{ item.created_at }}</view>
                    <view class="follow-card-picture" v-if="!isTrans">
                      <view class="follow-giveLike-box">
                        <view class="follow_giveLike-controls">
                          <!-- 置顶 -->
                          <view class="follow_giveLike_more" @click.prevent.stop="showPinned(item, index)">
                            <image :src="'/static/admin/customer/<EMAIL>' | imageFilter('w_80')" />
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                  <view style="color:  rgba(41, 44, 57, 0.40);font-size: 28rpx;" v-if="item.admin">{{
                    item.admin.user_name }} / {{ item.admin.department_name }}</view>
                  <view class="timeline_item">
                    <view class="time flex-row items-center space-between">
                      <view class="follow-card-content row" style="padding-bottom: 0">
                        <view class="right text_right">
                          <view class="row text_rowLIST">
                            <view class="order" v-if="item.order">
                              <view class="order_c"> 顶</view>
                            </view>
                         
                            <view v-if="!item.url" class="contenturkl">
                              <tContentOverflow>{{ item.content }}</tContentOverflow>
                            </view>  

                          <view>
                          </view>
                        </view>
                            <view class="follow-picture-box" v-if="item.file_path_path && item.file_path_path.length">
                              <image v-for="(img, i) in item.file_path_path" :key="i"
                                @click.prevent.stop="preFollowImgs(item.file_path_path, i)" :src="img"
                                mode="aspectFill" />
                            </view>
               
                           


                            
                          
                          <view class="time-record-player" v-if="item.url">
                            <tVoicePlayer :path="item.url" :duration="parseInt(item.time/1000)" :name="item.id"/>
                          </view>
                          <view class="time-record-player" v-if="item.call_record && item.call_record.record_url">
                            <tVoicePlayer :path="item.call_record.record_url" :duration="item.call_record.duration" :name="item.id+'_'+item.call_record.id"/>
                          </view>
                          
                          <followReplyList :list="item.reply" :follow-id="item.id" v-if="item.reply && item.reply.length" @add="addFollowReply"/>

                          <view class="follow-giveLike-main" v-if="item.top_list && item.top_list.length">
                            <!-- src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/<EMAIL>"  -->
                            <view class="follow-giveLike-zan">
                              <image
                               src="../static/icon/index/Vector.png" />
                            </view>
                            <template v-if="item.top_list && item.top_list.length">
                              <view class="follow-giveLike-user" v-for="(it, i) in item.top_list" :key="it">
                                <template v-if="i < 2">
                                  {{ it }}
                                </template>
                                <template v-if="i >= 2">
                                  {{ '等' + item.top_list.length + '人' }}
                                </template>
                              </view>
                            </template>
                          </view>
                          <!-- <text class="username" v-if="item.admin">由「{{ item.admin.user_name }}」跟进</text> -->
                        </view>
                      </view>
                    </view>
                  </view>
              </view>
            </view>
            <loadMore :status="is_f_loading_new"></loadMore>
          </view>
        </view>
        <!-- 维护列表 -->
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 7">
          <view class="follow-card" style="margin-bottom: 100px" @click="hideShow">
            <view class="time_line">
              <view class="item" :class="{ current: item.is_finish !== 0 }" v-for="(item, index) in follow_list"
                :key="index">
                <template>
                  <!-- <view class="title">{{ item.description }}</view> -->
                  <view class="time_line_itme_header">
                    <view class="time">{{ item.created_at }}</view>
                    <view class="follow-card-picture"  v-if="!isTrans">
                      <view class="follow-giveLike-box">
                        <view class="follow_giveLike-controls">
                          <!-- 置顶 -->
                          <view class="follow_giveLike_more" @click.prevent.stop="showPinned(item, index)">
                            <image :src="'/static/admin/customer/<EMAIL>' | imageFilter('w_80')" />
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                  <view style="color:  rgba(41, 44, 57, 0.40);font-size: 28rpx; " v-if="item.admin">{{
                    item.admin.user_name }} / {{ item.admin.department_name }}</view>
                  <view class="timeline_item">
                    <view class="time flex-row items-center space-between">
                      <view class="follow-card-content row" style="padding-bottom: 0">
                        <view class="right text_right">
                          <view class="row text_rowLIST">
                            <view class="order" v-if="item.order">
                              <view class="order_c"> 顶</view>
                            </view>
                            <!-- {{ item.content }} -->
                       <view>
                        <view v-if="!item.url" class="contenturkl">
                          <tContentOverflow>{{ item.content }}</tContentOverflow>
                          
                            </view>
                            <view class="follow-picture-box">
                              <image v-for="(img, i) in item.file_path_list" :key="i"
                                @click.prevent.stop="preFollowImgs(item.file_path_list, i)" :src="img"
                                mode="aspectFill" />
                            </view>
                       </view>
                            <view  v-if="item.url" class="voice row" @click.prevent.stop="playVoice(item, index)">
                              <image v-if="!item.playing"
                                :src="'/static/icon/voice/voice_icon.png' | imageFilter('w_80')">
                              </image>
                              <image v-if="item.playing" :src="'/static/icon/voice/play_voice.gif' | imageFilter('w_80')">
                              </image>
                              <view>{{ parseInt(item.time / 1000) }}"</view>
                            </view>
                          </view>
                          <view class="follow-giveLike-main" v-if="item.top_list && item.top_list.length">
                            <view class="follow-giveLike-zan">
                              <image
                              src="../static/icon/index/Vector.png" />
                            </view>
                            <template v-if="item.top_list && item.top_list.length">
                              <view class="follow-giveLike-user" v-for="(it, i) in item.top_list" :key="it">
                                <template v-if="i < 2">
                                  {{ it }}
                                </template>
                                <template v-if="i >= 2">
                                  {{ '等' + item.top_list.length + '人' }}
                                </template>
                              </view>
                            </template>
                          </view>
                          <!-- <text class="username" v-if="item.admin">由「{{ item.admin.user_name }}」跟进</text> -->
                        </view>
                      </view>

                    </view>
                  </view>
                </template>
              </view>
            </view>
            <loadMore :status="is_f_loading"></loadMore>
          </view>
        </view>
        <!-- 线索列表 -->
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 4">
          <view class="follow-card" style="margin-bottom: 100px">
            <view class="time_line">
              <view class="item" :class="{ current: item.is_finish !== 0 }" v-for="(item, index) in logs_list"
                :key="index">
                <template>
                  <!-- <view class="title">{{ item.description }}</view> -->
                  <view style="time_line_item_header">
                    <view class="time">{{ item.created_at }}</view>
                  </view>
                  <view class="timeline_item">
                    <view class="time flex-row items-center space-between">
                      <view class="follow-card-content row" style="padding-bottom: 0">
                        <view class="right text_right">
                          <view class="row text_rowLIST">
                            <view class="order" v-if="item.order">
                              <view class="order_c"> 顶</view>
                            </view>
                            <!-- {{ item.content }} -->
                            <view class="follow-picture-box">
                              <image v-for="(img, i) in item.file_path_list" :key="i"
                                @click.prevent.stop="preFollowImgs(item.file_path_list, i)" :src="img"
                                mode="aspectFill" />
                            </view>
                            <view v-if="!item.url" class="contenturkl">{{ item.content | removeTags}}
                            </view>
                            <view v-else class="voice row" @click.prevent.stop="playVoice(item, index)">
                              <image v-if="!item.playing"
                                :src="'/static/icon/voice/voice_icon.png' | imageFilter('w_80')">
                              </image>
                              <image v-if="item.playing" :src="'/static/icon/voice/play_voice.gif' | imageFilter('w_80')">
                              </image>
                              <view>{{ parseInt(item.time / 1000) }}"</view>
                            </view>
                          </view>

                          <clueItem v-if="item.client_clue && item.client_clue.id" :clue="item.client_clue" overflowed @more="openClueItemPopup(item.client_clue)"></clueItem>
                          
                      <view class="follow-giveLike-main" v-if="item.top_list && item.top_list.length">
                            <view class="follow-giveLike-zan">
                              <image
                                src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/<EMAIL>" />
                            </view>
                            <template v-if="item.top_list && item.top_list.length">
                              <view class="follow-giveLike-user" v-for="(it, i) in item.top_list" :key="it">
                                <template v-if="i < 2">
                                  {{ it }}
                                </template>
                                <template v-if="i >= 2">
                                  {{ '等' + item.top_list.length + '人' }}
                                </template>
                              </view>
                            </template>
                          </view>
                        </view>
                      </view>

                    </view>
                  </view>
                </template>
              </view>
            </view>
            <loadMore :status="is_l_loading"></loadMore>
          </view>
        </view>
        <!-- 轨迹 -->
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 5">
          <view class="follow-card" style="margin-bottom: 100px">
            <view class="time_line">
              <view class="item" :class="{ current: item.is_finish !== 0 }" v-for="(item, index) in guiji_list"
                :key="index">
                <template>
                  <!-- <view class="title">{{ item.description }}</view> -->
                  <view class="time_line_itme_header">
                    <view class="time">{{ item.ctime}}</view>
                  </view>
                  <view class="timeline_item">
                    <view class="time flex-row items-center space-between">
                      <view class="follow-card-content row" style="padding-bottom: 0">
                        <view class="right text_right">
                          <view class="row text_rowLIST">
                            <view class="order" v-if="item.order">
                              <view class="order_c"> 顶</view>
                            </view>
                            <!-- {{ item.content }} -->
                            <view class="follow-picture-box">
                              <image v-for="(img, i) in item.file_path_list" :key="i"
                                @click.prevent.stop="preFollowImgs(item.file_path_list, i)" :src="img"
                                mode="aspectFill" />
                            </view>
                            <view v-if="!item.url" class="contenturkl">{{ item.content | removeTags }}
                            </view>
                            <view v-else class="voice row" @click.prevent.stop="playVoice(item, index)">
                              <image v-if="!item.playing"
                                :src="'/static/icon/voice/voice_icon.png' | imageFilter('w_80')">
                              </image>
                              <image v-if="item.playing" :src="'/static/icon/voice/play_voice.gif' | imageFilter('w_80')">
                              </image>
                              <view>{{ parseInt(item.time / 1000) }}"</view>
                            </view>
                          </view>
                      
                        </view>
                      </view>

                    </view>
                  </view>
                </template>
              </view>
            </view>
            <loadMore :status="is_guiji_loading"></loadMore>
          </view>
        </view>
        <!-- 外呼列表 -->
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 6">
          <view class="follow-card" style="margin-bottom: 100px">
            <view class="time_line">
              <view class="item" :class="{ current: item.is_finish !== 0 }" v-for="(item, index) in  tel_list"
                :key="index">
                <template>
                  <!-- <view class="title">{{ item.description }}</view> -->
                  <view class="time_line_itme_header">
                    <view class="time">{{ item.created_at }}</view>
                  </view>
                  <view style="color:  rgba(41, 44, 57, 0.40);font-size: 28rpx;margin-top: 24rpx;" v-if="item.admin">{{
                    item.admin.user_name }} / {{ item.admin.department_name }}</view>
                  <view class="timeline_item">
                    <view class="time flex-row items-center space-between">
                      <view class="follow-card-content row" style="padding-bottom: 0">
                        <view>
                          <view class=" text_rowLIST">
                            <!-- {{ item.content }} -->
                            <view v-if="!item.url" class="contenturkl">{{ item.content | removeTags}}
                            </view>

                            <view class="time-record-player" v-if="item.call_record && item.call_record.record_url">
                              <tVoicePlayer :path="item.call_record.record_url" :duration="item.call_record.duration" :name="item.id+'_'+item.call_record.id"/>
                            </view> 

                            <view class="follow-picture-box">
                              <image v-for="(img, i) in item.file_path" :key="i"
                                @click.prevent.stop="preFollowImgs(item.file_path, i)" :src="img"
                                mode="aspectFill" />
                            </view>
                          </view>
                          <!-- <text class="username" v-if="item.admin">由「{{ item.admin.user_name }}」跟进</text> -->
                        </view>
                      </view>
                    </view>
                  </view>
                </template>
              </view>
            </view>
            <view class="follow-card-item" v-for="item in tel_list" :key="item.id">
              <!-- <tel-item @clickvoice="onClickVoice" @voiceEnded="voice_playing_index = -1"
                @voiceError="voice_playing_index = -1" :share="item"
                :voice_playing="item.voice_playing_index == voice_playing_index">
              </tel-item> -->
          
            </view>
            <loadMore :status="is_tel_loading"></loadMore>
          </view>
        </view>
        <!-- 带看列表 -->
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 8">
          <view class="follow-card" style="margin-bottom: 100px; background: #fff;" @click="hideShow">
            <view class="time_line">
              <view class="item" :class="{ current: item.is_finish !== 0 }" v-for="(item, index) in daikan_list"
                :key="index">
                <template>
                  <!-- <view class="title">{{ item.description }}</view> -->
                  <view class="time_line_itme_header">
                    <view class="time">{{ item.created_at }}</view>
                  </view>
                  <view style="color:  rgba(41, 44, 57, 0.40);font-size: 28rpx;margin-top: 24rpx;" v-if="item.admin">{{
                    item.admin.user_name }} / {{ item.admin.department_name }}</view>
                  
                  <view style="display: flex; flex-direction: row; align-items: center;margin-top: 32rpx;" v-if="item.take_admin">
                    <view>带看人：</view>
                    <view style="flex:1">{{ item.take_admin }}</view>
                  </view>

                  <view style="display: flex; flex-direction: row; align-items: center;margin-top: 32rpx;">
                    <view>带看时间：</view>
                    <view>{{ item.take_date }}</view>
                    <view>{{ item.take_time== 1 ? "上午" : item.take_time== 2 ?" 下午" :" 晚上" }}</view>
                   </view>
                   <view style="display: flex; flex-direction: row;margin-top: 32rpx;" v-if="item.proportion&&item.proportion.length">
                    <view style="white-space: nowrap;">分边比例：</view>
                    <view style="display: flex; flex-direction: row;flex-wrap: wrap;">
                      <view v-for="(it,index) in item.proportion" :key="index">
                       <view style="margin-right:20rpx;margin-bottom:10rpx;">
                         {{it.user_name}}{{it.radio+'%'}},
                       </view>
                      </view>
                    </view>
                   </view>
                   <view style="display: flex; flex-direction: row; margin-top: 32rpx;" v-if="item.project_name">
                    <view style="white-space: nowrap;">带看项目：</view>
                    <view>{{ item.project_name }}</view>
                   </view>

                   <view style="display: flex; flex-direction: row; align-items: center;margin-top: 32rpx;" v-if="item.accompany">
                    <view>陪看人：</view>
                    <view style="flex:1">{{ item.accompany }}</view>
                   </view>
                   <view class="timeline_item">
                    <view class="time flex-row items-center space-between">
                      <view class="follow-card-content row" style="padding-bottom: 0">
                        <view>
                          <view class=" text_rowLIST">
                            <!-- {{ item.content }} -->
                            <view v-if="!item.url" class="contenturkl">跟进内容：{{ item.content | removeTags}}
                            </view>
                            <view class="follow-picture-box">
                              <image v-for="(img, i) in item.file_path" :key="i"
                                @click.prevent.stop="preFollowImgs(item.file_path, i)" :src="img"
                                mode="aspectFill" />
                            </view>
                          </view>
                          <!-- <text class="username" v-if="item.admin">由「{{ item.admin.user_name }}」跟进</text> -->
                        </view>
                      </view>
                    </view>
                  </view>
                </template>
              </view>
            </view>
            <loadMore :status="is_daikan_loading"></loadMore>
          </view>
        </view>
      </view>
      <!-- 底部菜单 -->
      <view class="container top-line flex-row footer_btn_group">
        <view class="option_btn flex-1" v-if="user_detail.follow_id > 0">
          <my-button @click="concatOwner" type="primary" :round="false" size="big"> 电话</my-button>
          <!-- <view class="loader"></view> -->
        </view>
        <view class="option_btn flex-1" :class="{ disabled: un_renling_status }" v-if="user_detail.follow_id == 0">
          <my-button @click="getCustomer" type="primary" :round="false" size="big">认领</my-button>
        </view>
        <view class="option_btn flex-1">
          <my-button @click="onGoDemand" type="primary" :round="false" size="big" :plain="true">跟进</my-button>
        </view>
        <view class="option_btn flex-1" @click="isShowSetting = true" style="min-width: 192rpx;">
          <my-button type="primary" :round="false" size="big" :plain="true">
            <text class="more_con row">更多 ⋮ </text>
          </my-button>
        </view>
        <!-- 底部菜单 -->
      </view>
      <!-- <view v-if="is_from == 3" class="container top-line flex-row footer_btn_group">
        <view class="option_btn flex-3">
          <my-button @click="getCustomer" type="primary" :round="false" size="big">认领</my-button>
        </view>
        <view class="option_btn flex-3">
          <my-button @click="onGoDemand" type="primary" :round="false" size="big" :plain="true"
            >跟进</my-button
          >
        </view>
        <view class="option_btn flex-3" @click="isShowSetting3 = true">
          <my-button type="primary" :round="false" size="big" :plain="true">
            <view class="more_con row">更多 ⋮ </view></my-button
          >
        </view>
      </view> -->

      <moreOp :visible.sync="isShowSetting" :current="current" :customer="user_detail" :has-role="has_roles"></moreOp>
      <followOp :visible.sync="dialogs.followOp" :current="current" :detail="curSeledFollow" @refresh="handleFollowRefresh"/>

    <image v-if="false" class="istotop" src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/hdb.png"
      @click="ongoTop"></image>

    <myPop ref="showPhone" :show="show_phone_pop" position="center" width="80%" @close="show_phone_pop = false">
      <view class="p_con">
        <view class="title"> 拨打电话 </view>
        <view class="p_content">
          <view class="p_item flex-row items-center">
            <view class="label"> 选择外显号码 </view>
            <view class="value flex-1">
              <selectDown valueName="phone" :multiple="false" v-model="show_id" :localdata="phoneList"
                @change="changeSelect" defaultValue="请选择外显号码" placeholder="请选择外显号码">
              </selectDown>
            </view>
          </view>
          <view class="btns flex-row items-center">
            <view class="btn flex-1" @click="conMakePhone"> 确认拨打 </view>
          </view>
        </view>
      </view>
    </myPop>

    <replyFollowComment :visible.sync="dialogs.addReplyComment" :follow-id="curSeledFollowId" @success="handleFollowRefresh"/>
    <clueItemPopup :visible.sync="dialogs.clueItemMore" :clue="clueItemData"/>
    
  </view>
</template>
<script>
import CustomTextarea from "./components/CustomTextarea"
import myIcon from "@/components/my-icon.vue";
import myPopup from "@/components/myPopup.vue";
import remind from "@/components/remind";
import myButton from "../house/components/myButton";
import loadMore from "@/components/loadMore"
import tabBar from "@/components/tabBars"
import salesProgress from "./components/salesProgress"
import selectDown from '@/outbound/components/uni-data-select'
import telItem from './components/telItem.vue';
import myPop from "@/outbound/components/myPopup";
import customerMaintainer from "@/components/customer/customerMaintainer";
import tVoicePlayer from "@/components/tplus/tVoicePlayer";
import moreOp from '@/customer/components/customer_detail/more_op.vue'
import followOp from '@/customer/components/customer_detail/follow_op.vue'
import headImg from '@/customer/components/customer/headImg.vue'
import followReplyList from '@/customer/components/customer_detail/follow_reply_list.vue'
import replyFollowComment from '@/components/customer/replyFollowComment.vue';
import tContentOverflow from '@/components/tplus/tContentOverflow.vue';
import clueItem from '@/customer/components/customer_detail/clueItem.vue'
import clueItemPopup from '@/customer/components/customer_detail/clueItemPopup.vue'
import { seeTel } from '@/common/utils/customer.js'

const innerAudioContext = uni.createInnerAudioContext();
export default {
  components: { myIcon, myPopup, remind, myButton, loadMore, selectDown, salesProgress, telItem, myPop, selectDown, tabBar, CustomTextarea, 
    customerMaintainer, tVoicePlayer, moreOp, headImg, followOp, followReplyList, replyFollowComment, tContentOverflow,
    clueItem, clueItemPopup
  },
  data() {
    return {
      isLoading: false,
      buttonLabel: "",
      logs_list_content: '',
      logs_list_time: '',
      content: '',// 默认回显列表第一条数据
      last_call_status: '',// 控制接通未接通的状态
      client_id: "",
      user_detail: {},
      follow_params: {
        page: 1,
        per_page: 10,
        client_id: "",
      },
      follow_params_new: {
        page: 1,
        per_page: 10,
        client_id: "",
      },
      log_params: {
        page: 1,
        per_page: 10,
        client_id: "",
      },
      is_guiji_loading: false,
      guiji_params: {
        page: 1,
        per_page: 10,
        client_id: "",
      },
      daikan_params: {
        page: 1,
        per_page: 10,
        client_id: "",
      },
      is_daikan_loading: false,
      guiji_list: [],
      daikan_list: [],
      follow_list: [],
      is_f_loading: '',
      is_f_loading_new: '',
      logs_list: [],
      is_l_loading: '',
      // 查看电话
      is_view_tel: false,
      view_tel: "",
      is_from: "",
      is_show_skeleton: false,
      is_show_content: false, // 用于从侧边栏进入隐藏内容
      type_list: [],
      tabs_list: [
        // { id: 1, name: "动态" },
        { id: 3, name: "跟进", description: "跟进" },
        { id: 2, name: "画像", description: "画像" },
        { id: 7, name: "维护", description: "维护" },
        { id: 4, name: "线索", description: "线索" },
        { id: 5, name: "轨迹", description: "轨迹" },
        { id: 6, name: "外呼", description: "外呼" },
        { id: 8, name: "带看", description: "带看" },
        { id: 9, name: "角色", description: "角色" },
      ],
      // { id: 7, name: "带看", description: "带看" },
      //   { id: 8, name: "复看", description: "复看" },
      is_tabs: 3,
      isShowSetting: false,
      isShowSetting3: false,
      setting_1: [
        {
          id: 1,
          name: "维护资料",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/whzl.png",
        },
        {
          id: 2,
          name: "提醒跟进",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/txtz.png",
        },
      ],
      setting_2: [
        {
          id: 1,
          name: "标无效",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/bwx.png",
        },
        {
          id: 2,
          name: "转交到同事",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/zzl.png",
        },
        {
          id: 5,
          name: "转交到公海",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/zzl.png",
        },
        {
          id: 4,
          name: "绑定企业微信",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/zzl.png",
        },
        {
          id: 3,
          name: "更改客户状态",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/sp.png",
        },
      ],
      maxLength: 100,
      isShow: false,
      is_view_tel_desc: false,
      from: '',    //进入来源  默认为空  如果侧边栏进入不携带id  from=‘fromOther’
      login_user_info: {},//当前登录用户的信息
      has_roles: false, //是否有操作权限 也就是 当前的客户还是不是登陆者的客户 如果不是只能查看不能操作
      is_load_end: false, //定义变量 加载完成以后显示认领按钮
      token: "",
      giveup_content: "",
      giveup_reason: false,
      stateList: [],
      progress: [],
      show_follow_see: false,
      follow_see_type: 0,
      follow_see_params: {
        content: "",
        take_date: "",
        take_time: "",
        take_no: "",
        accompany: []
      },
      voice_playing_index: -1,
      tel_params: {
        page: 1,
        per_page: 10,
        status: 19,
        call_phone: '',
        call_show_phone: ""
      },
      tel_list: [],
      is_tel_loading: "",
      show_phone_pop: false,
      phoneList: [],
      show_id: "",
      nowIndex: 0,
      show_last_call_follow_content: false,
      temp_disable: false,
      un_renling_status: false,  //false  可以认领 true 不可认领 
      count: 0,
      follow_list_new:[],
      item_follow_list_new:[],
      userArrary:[],
      items: ["Item 1", "Item 2", "Item 3", "Item 4", "Item 5"], // 你的滚动内容
      translateY: 0, // 初始的Y轴偏移
      currentIndex: 0, // 当前显示的内容索引
      city_name:'',
      pageClickHandlers: [],  //页面点击处理函数
      current: 'my',
      seeTeling: false,
      curSeledFollowId: 0,
      curSeledFollow: null,
      dialogs: {
        followOp: false,
        addReplyComment: false,
        clueItemMore: false
      },
      clueItemData: {},
      unloaded: false
    };
  },
  onBackPress() {
    let telInfo = JSON.parse(uni.getStorageSync('telInfo'))
    console.log(telInfo, '判断是否有tel_log_id');
  },
  onLoad(options) {
    this.unloaded = false;
    uni.$on("getDataAgain", () => {
      if(this.unloaded) return;
      // this.is_show_content = true
      this.getDataDetail();
      this.getTypeData();
      this.setProgress()
      this.getdaikanList()
      // this.qiangzhigj()
    })
    // if (options . website_id) {
    //   if (options.website_id == 176) {
    this.is_show_skeleton = true;

    console.log(options.lastcallstatus, 'option.last_call_status');
    if (options.lastcallstatus) {
      this.last_call_status = options.lastcallstatus
    }
    
    //#ifdef H5
    if (this.$isWxWork() == 'wxwork' || this.$isWxWork() == 'com-wx-pc') {
      this.token = localStorage.getItem("wxwork_token");
      this.website_id = localStorage.getItem("wxwork_id");
      if (!this.token) {
        return
      }

    } else {
      let website_id = options.website_id
      this.token = localStorage.getItem("token" + website_id)
      console.log(this.token);
      if (!this.token) {
        localStorage.setItem('backUrl', location.href)
        this.$router.push("https://yun.tfcs.cn")
      }

    }
    //#endif

    // 未登录中断请求
    this.is_from = options.form || 2; // 来源公海/我的
    uni.showLoading({
      title: "加载中",
      mask: true
    })
    this.source = options.source || 1
    this.current = options.current || 'my'
    if(this.isMy){
      this.source = 2;
    }else if(this.isSeas || this.isPotential){
      this.source = 1;
    }
    if(this.isTrans){
      this.tabs_list = this.tabs_list.filter(e => e.id != 5)
    }


    // console.log(window.location.href,"====111111111111111111111111111111111111111");
    this.un_renling_status = (options.un_renling_status === "false" || !options.un_renling_status) ? false : true
    if (options.id) {
      this.client_id = options.id;

      this.getDataDetail();
      this.getTypeData();
      // this.qiangzhigj()

    } else {
      this.from = "fromOther"
      this.getWxQyWxConfig(["agentConfig", 'getCurExternalContact'], wx => {
        this.wx = wx
        this.getDataDetail();
        this.getTypeData();
        // this.qiangzhigj()
      })
    }
    innerAudioContext.onPlay(() => {
      // item.playing = true
    })
    // 监听语音播放停止
    innerAudioContext.onStop(() => {
      if (this.follow_list) {
        this.follow_list.map(item => {
          item.playing = false
          return item
        })
      }
      // item.playing = false
    })
    // 监听语音自然播放结束事件
    innerAudioContext.onEnded(() => {
      if (this.follow_list) {
        this.follow_list.map(item => {
          item.playing = false
          return item
        })
      }
    })
    // 监听语音播放失败事件
    innerAudioContext.onError(() => {

      uni.showToast({
        title: '播放失败，请重试',
        icon: 'none'
      })
      if (this.follow_list) {
        this.follow_list.map(item => {
          item.playing = false
          return item
        })
      }

    })
    // this.updateBackendData()
    // this.getFollowData()
    this.getdaikanList()
  },
  onReady() {

  },
  onShow() {
    if (this.is_refresh) {
      uni.$on('refresh', (data) => {
        if (data.refreh) {
          this.follow_params.page == 1; // 重置页码
          this.follow_list = []; // 清空跟进记录列表
          this.getFollowData(); // 获取跟进记录
        }
      })
      this.is_refresh = false;
    }
  },
  onUnload() {
    this.unloaded = true;
    //uni.$off("getDataAgain")
    uni.$off("refresh"); // 解绑自定义事件
  },

  filters: {
    dayFilter(val, last_follow_time, get_time) {
      console.log(val, last_follow_time, get_time);
      if (last_follow_time && get_time) {
        const date1 = new Date(last_follow_time);
        const date2 = new Date(get_time);
        // 计算两个日期之间的时间差（以毫秒为单位）
        const timeDiff = Math.abs(date2.getTime() - date1.getTime());
        // 将时间差转换为天数
        return Math.ceil(timeDiff / (1000 * 3600 * 24));
      }
      return '--'
    },
    mobileFilter(val, isTrans) {
      if(isTrans){
        return val;
      }
      let reg = /^(.{3}).*(.{3})$/;
      if (val) {
        return val.replace(reg, "$1*****$2");
      }
    },

    captureTime1(fullTime) {
      var timeArr = fullTime
        .replace(" ", ":")
        .replace(/\:/g, "-")
        .split("-");
      var nian = timeArr[0];
      var yue = timeArr[1];
      var ri = timeArr[2];
      var shi = timeArr[3];
      var fen = timeArr[4];
      var miao = timeArr[5];

      return yue + "月" + ri + "日";
    },
    captureTime(fullTime) {
      var timeArr = fullTime
        .replace(" ", ":")
        .replace(/\:/g, "-")
        .split("-");
      var nian = timeArr[0];
      var yue = timeArr[1];
      var ri = timeArr[2];
      var shi = timeArr[3];
      var fen = timeArr[4];
      var miao = timeArr[5];

      return shi + ":" + fen;
    },
    // 计算过去时间天数
    getPastDay(val) {
      if (val == 0) {
        return "今天";
      } else {
        return val + "天前";
      }
    },
    // 计算跟客时间
    getFollowDay(val, val2) {
      if(!val || !val2) return '';
      val2 = val2.replace(' ','T');
      val  = val.replace(' ','T');
      const currentDate = new Date(val2);
      const specifiedDate = new Date(val);
      // 计算时间差
      const timeDiff = Math.abs(currentDate.getTime() - specifiedDate.getTime());
      // 将时间差转换为天数
      const daysDiff = timeDiff / (1000 * 60 * 60 * 24);
      if (Math.floor(daysDiff) == 0) {
        return "今天";
      } else {
        return Math.ceil(daysDiff) + "天";
      }
    },
    // 计算带看次数
    getTakeLook(val) {
      switch (val) {
        case 1:
          val = "首看";
          break;
        case 2:
          val = "二看";
          break;
        default:
          if (val > 3) {
            return "多次带看";
          }
          break;
      }
      return val;
    },
    // 是否是新客留资(24小时内新增的)
    getNewCustomer(val) {
      const currentDate = new Date();
      const specifiedDate = new Date(val);
      // 计算时间差
      const timeDiff = currentDate.getTime() - specifiedDate.getTime();
      // 将时间差转换为天数
      const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      if (daysDiff == 0) {
        return "新客留资";
      } else {
        return null;
      }
    }
  },
  mounted() {
    this.startAutoScroll();
    console.log(getCurrentPages().length,'-------测试打印pages-----------');
  },
  computed:{
    isTrans(){
      return this.current === 'trans';
    },
    isMy(){
      return this.current === 'my';
    },
    isSeas(){
      return this.current === 'seas';
    },
    isPotential(){
      return this.current === 'potential';
    },
    //是否我司成交
    isUsDeal(){
      const title = this.user_detail?.tracking?.title;
      return title == '我司成交' || title == '他司成交' ? 1 : 0;
    },
    //来源label
    userDetailSourceLabel(){
      let label = '';
      if(this.user_detail.source){
        label = this.user_detail.source.title;
        if(this.user_detail.source2 && this.user_detail.source2.id != this.user_detail.source.id){
          label += '/'+this.user_detail.source2.title;
        }
      }
      return label || '--';
    },
    isOpenRemark(){  //是否显示备注编辑框
      return this.$store.state.crmConfig.is_open_remark;
    }
  },
  methods: {
    openClueItemPopup(clue){
      this.clueItemData = clue;
      this.dialogs.clueItemMore = true
    },
    handleFollowRefresh(){
      this.follow_params.page = 1;
      this.getFollowData(); // 获取最新数据
      this.getFollowDataNew()
    },
    //点击页面事件
    onPageClick(){
      setTimeout(() =>{
        for(let index = 0; index < this.pageClickHandlers.length; index++){
            const item = this.pageClickHandlers[index];
            if(item.trigger){
              item.handler.apply(item.thisObj || this, item.params);
              this.pageClickHandlers.splice(index, 1);
              index--;
            }else{
              item.trigger = true;
            }
        }
      },20)
    },
    onRegisterPageClick(ops){
      ops.trigger = false;
      this.pageClickHandlers.push(ops);
    },
    onAddShareFollowerSuccess(){
      this.getDataDetail();
    },
    // 头部翻动效果
    startAutoScroll() {
      setInterval(() => {
        this.currentIndex = (this.currentIndex + 1) % this.userArrary.length;
        this.translateY = -this.currentIndex * 41; // 50是每个项目的高度
      }, 5000); // 5000毫秒（5秒）滚动一次
    },
    // 类型：比如  求购
    getTypeData() {
      this.$ajax.get("/qywx/type/list", {}, (res) => {
        if (res.statusCode === 200) {
          // console.log(res.data,'getTypeData////');
          this.type_list = res.data;
        }
      });
    },
    onClickVoice(src) {
      var voice_playing_index = this.tel_list.findIndex(item => item.record_url == src)
      if (this.voice_playing_index == voice_playing_index) {
        this.voice_playing_index = -1
      } else {
        this.voice_playing_index = voice_playing_index
      }
    },
    typeFilter(val) {
      let arr = this.type_list.filter((item) => {
        if (item.id == val) {
          return item;
        }
      })[0];
      if (arr) {
        return arr.title;
      }
    },
    onClickTabs(e) {
      console.log(e);
      this.nowIndex = e.index
      this.is_tabs = e.id;
      if (e.id == 5) {
        this.guiji_params.page = 1
        // 轨迹
        this.getGuijiList()
      }
      if (e.id == 7) {
        this.follow_params.page = 1
        // 维护
        this.getFollowData()
      }
      if (e.id == 3) {
        this.follow_params_new.page = 1
      this.getFollowDataNew();
      }
      if (e.id == 4) {
        this.log_params.page = 1;
        // 线索
        this.getLogsData();
      }
      if (e.id == 6) {
        this.tel_params.page = 1
        // 外呼
        this.getTelList()
      }
      if (e.id == 8) {
        this.daikan_params.page = 1
        // 带看
        this.getdaikanList()
      }
    },
    // 标签正在维护中
    // tagall() {
    //   uni.showToast({
    //     title: "标签功能正在优化中...",
    //     icon: "none"
    //   })
    // },
    // 外呼列表
    getTelList() {
      this.tel_params.client_id = this.client_id
      if (this.tel_params.page == 1) {
        this.tel_list = []
      }

      this.$ajax.get(this.isTrans?"/admin/private_client_follow/search":"/admin/crm/client_follow/search", this.tel_params, res => {
        console.log(res);
        if (res.statusCode == 200) {
          res.data.data.map((item, index) => item.voice_playing_index = index)
          this.tel_list = this.tel_list.concat(res.data.data)
          if (res.data.data.length < this.tel_params.per_page) {
            this.is_tel_loading = "nomore";
          } else {
            this.is_tel_loading = "loadend"
          }
        }


      })
    },
    // 轨迹列表
    getGuijiList() {
      if (this.guiji_params.page === 1) {
        this.guiji_list = [];
      }
      this.guiji_params.id = this.client_id
      this.is_guiji_loading = "loading"
      this.$ajax.get("/qywx/tfy_client_track/search", this.guiji_params, (res) => {
        if (res.statusCode === 200) {
          this.guiji_list = this.guiji_list.concat(res.data.list);
          if (res.data.list.length === 0 || res.data.list.length < this.guiji_list.per_page) {
            this.is_guiji_loading = "nomore";
          } else {
            this.is_guiji_loading = "loadend"
          }
        }
      });
    },
    // 带看列表
    getdaikanList() {
      if (this.daikan_params.page === 1) {
        this.daikan_list = [];
      }
      this.daikan_params.client_id = this.client_id
      this.is_daikan_loading = "loading"
      this.$ajax.get(
        this.isTrans ? "/admin/private_client_follow/follow_take_search" : "/admin/crm/client_follow/follow_take_search",
        this.daikan_params,
        (res) => {
          if (res.statusCode === 200) {
            if (res.data.data.length === 0 || res.data.data.length < this.daikan_list.per_page) {
              this.is_daikan_loading = "nomore";
            } else {
              this.is_daikan_loading = "loadend"
            }
            res.data.data.map(i => {
              i.playing = false
              return i
            })
            this.daikan_list = this.daikan_list.concat(res.data.data);
            // this.getLogsData();
          }
        }
      );
    },
    getDataDetail() {
      var url = "";
      if (this.client_id) {
        url = `/qywx/client/info/${this.client_id}`;
        if(this.isTrans){
          url = `/admin/private_client/info/${this.client_id}`;
        }
        this.getUserData(url);
      } else {
        var _this = this
        this.wx.invoke("getCurExternalContact", {}, function (res) {
          // this.user_id = 'wm-VQJYQAABaPDlf4UPTMNqm40Rq5WXw'
          if (res.err_msg == "getCurExternalContact:ok") {
            _this.user_id = res.userId;
            url = `/qywx/client/qw_info/${_this.user_id}`;
            _this.getUserData(url);
          } else {
            console.log(res);
          }
        });
      }
    },
    searchAddress(e) {
      let url = '/qywx/client/query_mobile_place/';
      if(this.isTrans){
        url = '/admin/private_client/query_mobile_place/';
      }
      this.$ajax.get(`${url}${this.user_detail.id}`, {}, res => {
        if (res.statusCode == 200) {
          // console.log(res,'000000000077777 77777 ');
          this.$set(this.user_detail, 'mobile_place', res.data)
          // let index = this.client_list.findIndex(item => item.id == e.id)
          // this.$set(this.client_list[index], 'mobile_place', res.data)
        }
      })
    },
    getUserData(url) {
      this.is_show_skeleton = false;
      this.$ajax.get(url, { type: this.source }, (res) => {
        console.log(res.data, '1232134444444444444');
        if (res.statusCode === 200) {
          if(res.data?.code == 40001 && res.data?.msg){
            uni.showToast({
              title: res?.data?.msg,
              icon: 'none'
            })
            return;
          }
          this.user_detail = res.data;
          this.content = this.user_detail?.remark || '';
         this.userArrary.push({id:1,created_at:this.user_detail.last_follow_time},{id:2,created_at:this.user_detail.created_at})
        //  console.log(this.userArrary,'this.userArrary????');
          this.follow_params.client_id = res.data.id;
          this.log_params.client_id = res.data.id;
          this.client_id = res.data.id;
      if(this.user_detail.is_show_city == 1){
        if(this.user_detail.province == null || this.user_detail.city==null || this.user_detail.area==null){
          this.city_name = "暂无城市"
        }else{
          this.city_name = this.user_detail.province.name + "/" + this.user_detail.city.name + "/" + this.user_detail.area.name
        }

      }
          console.log(this.city_name,"2222");
          let send_template = uni.getStorageSync("send_template1") ? JSON.parse(uni.getStorageSync("send_template1")) : {}
          if (send_template && send_template.client_id) {
            if (this.client_id == send_template.client_id) {
              let sed_tem = new Date(send_template.date),
                now_tem = new Date()
              let oy = sed_tem.getFullYear(),
                om = sed_tem.getMonth(),
                od = sed_tem.getDate(),
                ny = now_tem.getFullYear(),
                nm = now_tem.getMonth(),
                nd = now_tem.getDate()
              console.log(oy, om, od, ny, nm, nd);
              if (oy = ny && od == nd && om == nm) {
                this.temp_disable = true
              }
            }
          }
          this.setProgress()
          this.getFollowDataNew()
          // this.getFollowData();
          this.getUserInfo()
          // this.getTelList()
          // 第三方来源
          // this.tabs_list = [
          //   { id: 3, name: "跟进", description: "跟进" },
          //   { id: 2, name: "画像", description: "画像" },
          //   { id: 7, name: "维护", description: "维护" },
          //   { id: 4, name: "线索", description: "线索" },
          //   { id: 5, name: "轨迹", description: "轨迹" },
          //   { id: 6, name: "外呼", description: "外呼" },
          // ]
          // if (this.user_detail.access_id) {
          //   if (this.user_detail.follow_id == 0) {
          //     // 公海客户详情
          //     this.tabs_list = this.tabs_list.filter((item) => item.id == 4 || item.id == 6);
          //     this.tabs_list.push(
          //       { id: 5, name: "轨迹", description: "轨迹" },
          //     )
          //     // this.tabs_list.push(
          //     //   { id: 6, name: "电话记录" },
          //     // )
          //     this.is_tabs = 4;
          //   } else {
          //     this.tabs_list = [
          //       { id: 3, name: "跟进", description: "跟进" },
          //       { id: 2, name: "画像", description: "画像" },
          //       { id: 7, name: "维护", description: "维护" },
          //       { id: 4, name: "线索", description: "线索" },
          //       { id: 5, name: "轨迹", description: "轨迹" },
          //       { id: 6, name: "外呼", description: "外呼" },
          //     ]
          //   }

          // }
          if (this.user_detail.wxqy_id) {

            this.setting_2 = this.setting_2.filter(item => item.id != 4)
          }

          if(!this.isTrans){
          this.$ajax.get('/qywx/client/verify_follow', {}, res => {
            console.log(res.data.id, "111111111111111111111");
            if (res.statusCode == 200) {
              if (res.data && res.data.id > 0 && res.data.client_id > 0) {
                if (res.data.client_id == this.client_id) {
                  this.toForceFollow(res.data)

                } else {
                  let website_id = this.$getQueryString('website_id')
                  let url = `/customer/detail?id=${res.data.client_id}&form=${this.is_from}&source=${this.source}&website_id=${website_id || this.website_id}`
                  uni.redirectTo({
                    url
                  })
                }
              }
            }
          })
          }
          // this.qiangzhigj()
          uni.hideLoading()
          this.is_load_end = true
          console.log("qywx", "333333333333333");
          uni.setStorageSync("crm_client_id", res.data.id);
          this.is_show_content = true
        } else {
          // TODO  以后侧边栏用qw_detail 页面  更改完成以后注释这里
          uni.hideLoading()
          let website_id = this.$getQueryString('website_id')
          if(res?.data?.message){
            uni.showToast({
              title: res?.data?.message,
              icon: 'none'
            })
          }

          /* uni.redirectTo({
            url: '/customer/default_crm?website_id=' + (website_id || this.website_id)
          }) */
          // this.$navigateTo(`/customer/default_crm`);
        }
      });
    },
    qiangzhigj() {
      this.$ajax.get('/qywx/client/verify_follow', {}, res => {
        console.log(res.data.id, "111111111111111111111");
        if (res.statusCode == 200) {
          if (res.data && res.data.id > 0 && res.data.client_id > 0) {
            if (res.data.client_id == this.client_id) {
              uni.showModal({
                title: '提示',
                content: '您有查看电话未跟进 去跟进？',
                success: (result) => {
                  if (result.confirm) {
                    console.log(5555);
                    this.toForceFollow(res.data)
                    return
                  } else if (result.cancel) {
                    console.log('用户点击取消');
                  }
                }
              })

            } else {
              let website_id = this.$getQueryString('website_id')
              let url = `/customer/detail?id=${res.data.client_id}&form=${this.is_from}&source=${this.source}&website_id=${website_id || this.website_id}`
              uni.redirectTo({
                url
              })
            }
          }
        }
      })
    },
    toForceFollow(data) {
      let telInfo = { tel_log_id: "", tel: [] }
      telInfo.tel.push(this.user_detail.mobile)
      // telInfo.tel_log_id = 
      if (this.user_detail.subsidiary_mobile_list && this.user_detail.subsidiary_mobile_list.length) {
        telInfo.tel.push(...this.user_detail.subsidiary_mobile_list)
      }
      uni.setStorageSync("telInfo", JSON.stringify(telInfo))
      let website_id = this.$getQueryString('website_id')
      console.log("执行2")
      let url = `/customer/demand?id=${data.client_id}&from=${this.from}&last_call_status=${this.last_call_status}&call_open_crm=${this.user_detail.call_open_crm}&mobile_place=${this.user_detail.mobile_place}&sys_source=customer_detail`
      // if (this.view_tel && this.view_tel.length) {
      url += "&tel=1&name=" + encodeURIComponent(this.user_detail.cname) + '&telType=' + this.user_detail.call_open_crm + "&tracking=" + (this.user_detail.tracking ? this.user_detail.tracking.id : '') + "&has_roles=" + (this.has_roles ? 1 : 0) + "&tel_log_id=" + data.id + "&website_id=" + (website_id || this.website_id)
      url += `&is_us_deal=${this.isUsDeal}`;
      url += `&current=${this.current}`;
      uni.redirectTo({
        url: url
      })
      // }
      // this.$navigateTo(url)
    },
    followSee(type) {
      if (!this.has_roles) {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: 'none'
        })
        return
      }
      this.$navigateTo("/customer/daikan?type=" + type + "&id=" + this.user_detail.id+"&current="+this.current)
      // this.follow_see_type = type
      // this.show_follow_see =true
    },
    setProgress() {
      this.progress = []
      let arr = [
        //   {
        //   name: "level_id",
        //   title: this.user_detail.level_id ? this.user_detail.level.title : "未设置",
        //   events: 'showAction'
        // },
        {
          name: "see_tel_num",
          title: "邀约",
          events: 'setViewTel'
        },
        {
          name: "is_first_look",
          title: '带看',
          events: 'followSee'
        },
        {
          name: "is_repeat_look",
          title: '复看',
          events: 'followSee'
        },
        {
          name: "deal_id",
          title: '成交',
          events: 'toShenpi'
        }]
      for (let index = arr.length - 1; index >= 0; index--) {
        const element = arr[index];
        let icon = '', icon_right = '', status = 0, icon_left = ''
        if (element.name == 'level_id') {
          if (this.user_detail.level_id) {
            icon = this.user_detail.level.title + '-1'
            icon_right = this.user_detail.level.title + '-2'
            status = 1

            icon_left = ''
          } else {
            icon = "5=2"
            icon_right = "7-1"
            status = 0
            icon_left = ''
          }

        } else {
          if (this.user_detail[element.name] > 0) {
            status = 1
            icon = index == arr.length - 1 ? '4-2' : '6-2'
            icon_right = index == arr.length - 1 ? '' : '6-1'
            icon_left = "6-3"
          } else {
            status = 0
            icon = index == arr.length - 1 ? '7-2' : '02'
            icon_right = index == arr.length - 1 ? '' : '7-1'
            icon_left = "7-3"
          }
        }
        console.log(this.user_detail[arr[index + 1]], 1112);
        // let icon = element.name=='level_id'?this.user_detail.level.title+'-1':()
        let obj = {
          name: element.title,
          status: status,
          icon,
          icon_right,
          icon_left,
          value: element.name,
          events: element.events
        }
        this.progress.unshift(obj)
        // console.log(this.progress, '123123123');

      }
      // 如果后边的已完成 前边的就直接完成  所以从后往前追加  
      // if (this.user_detail.deal_id > 0) {
      //   // 成交状态
      //   this.progress.unshift({
      //     title: "已成交",
      //     icon: "4-1",
      //     status: 1,
      //     id: 6
      //   })
      // } else {
      //   this.progress.unshift({
      //     title: "成交",
      //     icon: "7-2",
      //     status: 0,
      //     id: 6
      //   })
      // }
      // // 复看状态
      // if (this.user_detail.deal_id > 0) {

      //   this.progress.unshift({
      //     title: "复看",
      //     icon: "4-1",
      //     status: 1,
      //     id: 5
      //   })
      // } else {
      //   this.progress.unshift({
      //     title: "复看",
      //     icon: "7-2",
      //     status: 0,
      //     id: 5
      //   })
      // }

      // if(this.user_detail.)
      // if (this.user_detail.level_id){
      //   this.progress.push({
      //     title:A,

      //   })
      // }
    },
    clickProgress(e) {
      console.log(e, 'eeeeeee');
      if (e.value == "is_first_look") {
        let type = 1
        // console.log(this.user_detail,'this.user_detail12345');
        // if (this.user_detail.is_first_look > 0) {
        //   type = 2
        // }
        this[e.events](type)
      } else if (e.value == "is_repeat_look") {
        // console.log(this.user_detail,'this.user_detai999999');
        let type = 2
        this[e.events](2)
      } else {
        this[e.events]()
      }
      // let arr = [
      //   {
      //     value: ''
      //   }
      // ]
    },
    getUserInfo() {
      var that = this;
      this.getLevelList()
      this.getStateList()
      this.$ajax.get("/qywx/common/query", {}, (res) => {
        if (res.statusCode === 200) {
          this.login_user_info = res.data
          if (!Array.isArray(this.user_detail.admin_list) && this.user_detail.admin_list == '') {
            this.user_detail.admin_list = []
          }
          let mangers = this.user_detail.admin_list || []
          let keyuanManger = 0, whr = 0
          if (mangers.includes(this.login_user_info.id + "")) {
            keyuanManger = 1
          }
          console.log(keyuanManger);
          if (this.login_user_info.id == this.user_detail.follow_id) {
            whr = 1
          }
          if (keyuanManger == 1 || whr == 1 || (this.user_detail.follow_id > 0 && this.user_detail.follow_id == this.login_user_info.id)) {
            this.has_roles = true
          }
          // if (this.user_detail.follow_id != this.login_user_info.id) {
          //   this.has_roles = false
          // }

          //权限验证，现在由提交数据的时候由后端进行验证20231009
          this.has_roles = true;
        }
      });
    },
    getStateList() {
      this.$ajax.get("/admin/crm/tracking/list", { type: 4 }, res => {
        if (res.statusCode == 200) {
          this.stateList = res.data
        }
      }, () => {

      })
    },
    postData(data) {
      let params = {
        call_phone_id: data.call_id,
        call_name: this.user_detail.cname,
        call_phone: data.callee,
        call_show_phone: data.caller,
        type: this.isTrans ? 3 : 1,
        client_id: this.client_id
      }
      this.$ajax.post("/common/call_module/addCallOutRecord", params, res => {
        // console.log(res);
      })
    },
    getphoneList(callBack) {
      this.$ajax.get('/admin/call_clue/getSeatsPhone', {}, res => {
        if (res.statusCode == 200) {
          this.phoneList = res.data.map(item => {
            item.value = item.show_id
            item.text = item.phone
            return item
          })
          callBack && callBack()
        }else{
          uni.showToast({
            title: res.data.message,
            icon: "none",
            duration: 2000
          })
        }
      })
    },
    changeSelect() {

    },

    conMakePhone(item) {
      if (!this.show_id) {
        uni.showToast({
          title: "请选择外显号码",
          icon: "none"
        })
        return
      }
      let api = `/admin/call_clue/directCallPhone`
      let params = { show_id: this.show_id, phone: this.user_detail.mobile, client_id: this.client_id }
      if (this.user_detail.call_open_crm == 3) {
        api = '/admin/call_clue/anyOneCallByCrm'
      }
      this.$ajax.post(api, params, res => {
        if (res.statusCode == 200) {
          if (res.data.call_id) {
            this.postData(res.data)
          }
        
          uni.makePhoneCall({
            phoneNumber: res.data.telX,
            success: () => {
              console.log("正在拨打中 请稍后");
            },
          });
          this.show_phone_pop = false
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
            duration: 2000
          })
        }
      })
    },
    // getStateList1 () {
    //   this.$ajax.get("/admin/crm/tracking/list", { type: 2 }, res => {
    //     this.getStateList1()
    //     if (res.statusCode == 200) {
    //       this.stateList = res.data
    //     }
    //   }, () => {
    //     this.getStateList1()
    //   })
    // },
    // 跟进列表
    getFollowDataNew() {
      if (this.follow_params_new.page === 1) {
        this.follow_list_new = [];
      }
      this.is_f_loading_new = "loading"
      let params = Object.assign({}, this.follow_params_new, { client_id: this.follow_params.client_id })
      this.$ajax.get(
        this.isTrans ? "/admin/private_client_follow/follow_search" : "/qywx/client/follow/follow_search",
        params,
        (res) => {
          if (res.statusCode === 200) {
            if (res.data.data.length < this.follow_params_new.per_page) {
              this.is_f_loading_new = 'nomore';
            } else {
              this.is_f_loading_new = ''
            }
            res.data.data.map(i => {
              i.playing = false
              return i
            })
            this.follow_list_new = this.follow_list_new.concat(res.data.data);
/*
if(this.follow_list_new == null){
  this.content = "备注信息"
}else{
  this.content = this.follow_list_new[0]?.content.replace(/<\/?[^>]+(>|$)/g, "")
            console.log(this.content,"====")
}
 */
            // // 判断是否为空
            // if(this.follow_list_new[0].content=='' && this.follow_list_new[0].content=="null"){
            //   this.content = "备注信息"
            // }else{
            //   this.content = this.follow_list_new[0].content 
            // }
            // console.log(this.follow_list_new[0].content, 'this.follow_list_new123');
            // this.getLogsData();
          }
        }
      );
    },
    // 维护列表
    getFollowData() {
      console.log('维护列表>>>>>>>>>');
      if (this.follow_params.page === 1) {
        this.follow_list = [];
      }
      this.is_f_loading = "loading"
      this.$ajax.get(
        this.isTrans ? "/admin/private_client_follow/search": "/qywx/client_follow/search",
        this.follow_params,
        (res) => {
          if (res.statusCode === 200) {
            if (res.data.data.length === 0 || res.data.data.length < this.follow_params.per_page) {
              this.is_f_loading = 'nomore';
            } else {
              this.is_f_loading = ''
            }
            res.data.data.map(i => {
              i.playing = false
              return i
            })
            this.follow_list = this.follow_list.concat(res.data.data);
            // this.getLogsData();
          }
        }
      );
    },
    setViewTel() {

      if (this.user_detail.call_open_crm == 3) {
        let actionArr = ['查看电话', '外呼']
        uni.showActionSheet({
          itemList: actionArr,
          success: (result) => {
            if (result.tapIndex == 0) {
              // 查看电话
              /* if (!this.has_roles) {
                uni.showToast({
                  title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
                  icon: "none",
                });
                return
              } */
              this.setViewTels()
              // this.getLogsData()

            } else if (result.tapIndex == 1) {
              // 直接吊起外呼电话

              if (this.phoneList && this.phoneList.length) {
                this.show_phone_pop = true
                this.show_id = this.phoneList.length ? this.phoneList[0].show_id : ""
              } else {
                this.getphoneList(() => {
                  this.show_phone_pop = true
                  this.show_id = this.phoneList.length ? this.phoneList[0].show_id : ""
                })
              }

            }
          },
          fail: (res) => {
            console.log(res.errMsg);
          }
        })
      } else {
        // 如果没开外呼 查看电话
        /* if (!this.has_roles) {
          uni.showToast({
            title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
            icon: "none",
          });
          return
        } */
        this.setViewTels()
        // this.getLogsData()
      }

    },
    async setViewTels() {
      if(this.seeTeling){
        return;
      }
      this.seeTeling = true;
      try{
        if(!this.isTrans){
          await seeTel(this.user_detail.id)
        }
        this.seeTeling = false;
       let url = `/customer/demand?id=${this.user_detail.id}&from=${this.from}&last_call_status=${this.last_call_status}&call_open_crm=${this.user_detail.call_open_crm}&mobile_place=${this.user_detail.mobile_place}&sys_source=customer_detail`
          // if (this.view_tel && this.view_tel.length) {
          url += "&tel=1&name=" + encodeURIComponent(this.user_detail.cname) + '&telType=' + this.user_detail.call_open_crm + "&tracking=" + (this.user_detail.tracking ? this.user_detail.tracking.id : '') + "&has_roles=" + (this.has_roles ? 1 : 0)
          // }
          url += `&is_us_deal=${this.isUsDeal}&current=${this.current}`;
          this.$navigateTo(url)
      }catch(e){
        this.seeTeling = false;
      }
      return;
      console.log(123);
      let api = `/qywx/client/see_tel/${this.client_id}`
      this.$ajax.get(api, {}, (res) => {

        if (res.statusCode === 200) {
          console.log(res, '99999');
          // this.is_view_tel = true;
          // uni.showToast({
          //   title: "不要忘记写跟进哦",
          //   icon: "none",
          // });
          // console.log(res);
           // 模拟异步操作，例如请求数据
           this.view_tel = res.data;
          uni.setStorageSync("telInfo", JSON.stringify(this.view_tel))
          console.log("执行1")
        let url = `/customer/demand?id=${this.user_detail.id}&from=${this.from}&last_call_status=${this.last_call_status}&call_open_crm=${this.user_detail.call_open_crm}&mobile_place=${this.user_detail.mobile_place}`
            // if (this.view_tel && this.view_tel.length) {
            url += "&tel=1&name=" + encodeURIComponent(this.user_detail.cname) + '&telType=' + this.user_detail.call_open_crm + "&tracking=" + (this.user_detail.tracking ? this.user_detail.tracking.id : '') + "&has_roles=" + (this.has_roles ? 1 : 0)
            // }
            url += `&is_us_deal=${this.isUsDeal}`;
            this.$navigateTo(url)
    }
        else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    // 线索列表
    getLogsData() {
      if (this.log_params.page === 1) {
        this.logs_list = [];
      }
      this.is_l_loading = "loading"
      this.$ajax.get(this.isTrans?"/admin/private_client_clue/search":"/qywx/client_clue/search", this.log_params, (res) => {
        if (res.statusCode === 200) {
          this.logs_list = this.logs_list.concat(res.data.data);
          console.log(this.logs_list,"<<<<<");
          this.logs_list_content = this.logs_list[0]?.content // 线索内容
          this.logs_list_time = this.logs_list[0]?.created_at //线索时间
          // console.log(this.logs_list[0]?.content, 'logs_list1234567890');
          if (res.data.data.length === 0 || res.data.data.length < this.log_params.per_page) {
            this.is_l_loading = "nomore";
          } else {
            this.is_l_loading = "loadend"
          }
        }else{
          this.is_l_loading = "nomore";
        }
      });
    },
    loadmoreFollow() {
      if (this.is_f_loading == "nomore" || this.is_f_loading == "loading") {
        // uni.showToast({
        //   title: "没有更多了",
        //   icon: "none",
        // });
        return;
      }
      this.follow_params.page++;
      this.getFollowData();
    },
    loadmoreFollowNew() {
      console.log(this.is_f_loading_new);
      if (this.is_f_loading_new == "nomore" || this.is_f_loading_new == "loading") {
        // uni.showToast({
        //   title: "没有更多了",
        //   icon: "none",
        // });
        return;
      }
      this.follow_params_new.page++;
      this.getFollowDataNew();
    },
    LoadMoreLogs() {
      if (this.is_l_loading == 'nomore' || this.is_l_loading == 'loading') {
        return;
      }
      this.log_params.page++;
      this.getLogsData();
    },
    loadMoreGuiji() {
      if (this.is_guiji_loading == 'nomore') {
        return;
      }
      this.guiji_params.page++;
      this.getGuijiList();
    },
    loadMoredaikan() {
      if (this.is_daikan_loading == 'nomore') {
        return;
      }
      this.daikan_params.page++;
     this. getdaikanList()
    },
    loadMoreTel() {
      if (this.is_tel_loading == 'nomore' || this.is_tel_loading == 'loading') {
        return;
      }
      this.tel_params.page++;
      this.getTelList();
    },
    onClickTag() {
      if (!this.has_roles) {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: 'none'
        })
        return
      }
      this.$navigateTo(`/customer/tag_list?id=${this.user_detail.id}&from=${this.from}&current=${this.current}`);
    },
    onConfirmfocus(e) {
      console.log(e, '000000');
      // e.detail.value = this.content
    },
    // // 更新后端数据
    updateCount() {
      this.count = this.content.length; // 更新字数计数
      // this.value ? this.value.length :
    },
    onConfirmRemark(e) {
      console.log(e, 'eeeeee');
      if (!e.detail.value) {
        // uni.showToast({
        //   title: "请检查备注内容",
        //   icon: "none",
        // });
        return
      }
      const remark = e.detail.value.trim();
      if(this.user_detail.remark === remark){
        return;
      }

      let form = {
        id: this.user_detail.id,
        remark: e.detail.value,
      };
      let url = '/qywx/client/update_remark'
      if(this.isTrans){
        url = '/admin/private_client/update_remark'
      }
      this.$ajax.post(url, form, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "操作成功",
            icon: "none",
          });
          this.user_detail.remark = remark;
          this.log_params.page = 1;
          this.getLogsData();
        } else {
          uni.showToast({
            title: res?.data?.message || '备注失败',
            icon: "none",
          });
        }
      });
    },
    onClickSetting1(e) {
      if (!this.has_roles) {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: 'none'
        })
        return
      }
      if (e.id == 1) {
        this.$navigateTo(`uphold?type=2&id=${this.user_detail.id}`);
      } else {
        this.isShowSetting = false;
        this.isShow = true;
      }
    },
    onClickSetting2(e) {
      if (!this.has_roles) {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: 'none'
        })
        return
      }
      if (e.id == 1) {
        if (this.user_detail.is_state == 1) {
          uni.showToast({
            title: "该客户正在审核中",
            icon: 'none'
          })
          return
        }
        if(this.isTrans){
          this.$navigateTo("/house/applyApprove?id=5&house_id=" + this.user_detail.id)
          return;
        }

        let shenPiInfo = {
          is_del: this.user_detail.is_del,
          is_state: this.user_detail.is_state,
          state_list: this.user_detail.state_list,
          stateList: this.stateList
        }
        uni.setStorageSync("shenpi", JSON.stringify(shenPiInfo))
        let type = '19_2'
        this.$navigateTo("/house/applyApprove?id=1&house_id=" + this.user_detail.id + "&type=" + type)
      }
      if (e.id == 2) {
        // this.$navigateTo(`/customer/choose_friend?id=${this.user_detail.id}`)
        this.$navigateTo(
          `/customer/transfer_customer?id=${this.user_detail.id}`
        );
      }
      if (e.id == 3) {
        this.toShenpi()
        // this.$navigateTo(`/customer/create_audit?id=${this.user_detail.id}`);
      }
      if (e.id == 4) {
        this.$navigateTo(`/customer/qw_friend_list?id=${this.user_detail.id}`);
      }
      if (e.id == 5) {
        this.giveup_reason = true
      }
      this.isShowSetting = false;
    },
    toShenpi() {
      if (!this.has_roles) {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: 'none'
        })
        return
      }
      if (this.user_detail.is_state == 1) {
        uni.showToast({
          title: "该客户正在审核中",
          icon: 'none'
        })
        return
      }
      if(this.isTrans){
        this.$navigateTo("/house/applyApprove?id=5&house_id=" + this.user_detail.id)
        return;
      }
      let shenPiInfo = {
        is_del: this.user_detail.is_del,
        is_state: this.user_detail.is_state,
        state_list: this.user_detail.state_list,
        stateList: this.stateList
      }
      uni.setStorageSync("shenpi", JSON.stringify(shenPiInfo))
      this.$navigateTo("/house/applyApprove?id=1&house_id=" + this.user_detail.id)
    },
    // 客户转公海
    toSeas() {
      this.$ajax.post("/qywx/client/discard", { ids: this.user_detail.id + '', content: this.giveup_content }, (res) => {
        console.log(res);
        if (res.statusCode == 200) {
          uni.showToast({
            title: res.message || '操作成功',
            icon: "none"
          })
          this.giveup_reason = false
          this.getDataDetail()
        } else {
          uni.showToast({
            title: res.data.message || '操作失败',
            icon: "none"
          })
        }
      })
    },
    getLevelList() {
      this.$ajax.get("/admin/crm/level/list", {}, res => {
        if (res.statusCode == 200) {
          this.levelList = res.data
        
        }

      })
    },
    // 切换等级
    showAction() {
      if (!this.has_roles) {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: 'none'
        })
        return
      }
      // console.log(this.levelList,"||||");

      let arr = []
      for(let item of this.levelList ){
        arr.push(item.title)
      }
      // console.log(arr,"===");
      uni.showActionSheet({
        itemList: arr,
        success: (result) => {
          // console.log(result, '1233456678result');
          uni.showModal({
            title: '提示',
            content: '确认更改客户等级吗？',
            success: (res) => {
              if (res.confirm) {
                this.setCustomerLevel(arr[result.tapIndex])
              } else if (res.cancel) {
                console.log('用户点击取消');
              }
            }
          })
      
        },
        fail: (res) => {
          console.log(res.errMsg);
        }
      })

    },
    setCustomerLevel(level) {
      let level_id = ''
      this.levelList.map(item => {
        console.log(item, '00009999888');
        console.log(level, 'level00000');
        // 当item.title 等于传过来的 等级
        if (item.title == level) {
          level_id = item.id
        }
      })
      this.$ajax.post("/admin/crm/client/update_level", {
        id: this.user_detail.id,
        level_id
      }, res => {
        console.log(res);
        if (res.statusCode == 200) {
          uni.showToast({
            title: res.message || '操作成功',
            icon: "none"
          })
          this.getDataDetail()
        }
      })
    },
    onGoDemand() {
      /* this.qiangzhigj()
        // 在这里执行你的异步操作
        if ((this.user_detail.all_follow_status == 0 && this.has_roles) || this.user_detail.all_follow_status == 1) {
        console.log("执行3")
        let url = `/customer/demand?id=${this.user_detail.id}&from=${this.from}&tracking=${this.user_detail.tracking ? this.user_detail.tracking.id : ''}&last_call_status=${this.last_call_status}&call_open_crm=${this.user_detail.call_open_crm}&mobile_place=${this.user_detail.mobile_place}`
        this.$navigateTo(url);
      } else {
        uni.showToast({
          title: this.user_detail.follow_id > 0 ? "暂无权限" : "请先认领客户",
          icon: "none"
        })
      } */
      //权限判断在提交时，由后端判断
      if(!this.user_detail.follow_id){
        uni.showToast({
          title: "请先认领客户",
          icon: "none"
        })
      }
      let url = `/customer/demand?id=${this.user_detail.id}&from=${this.from}&tracking=${this.user_detail.tracking ? this.user_detail.tracking.id : ''}&last_call_status=${this.last_call_status}&call_open_crm=${this.user_detail.call_open_crm}&mobile_place=${this.user_detail.mobile_place}&sys_source=customer_detail`
      url += `&is_us_deal=${this.isUsDeal}`;
      url += `&current=${this.current}`;
      this.$navigateTo(url);
    },
    onClickRemind(e) {
      this.$ajax.post("/qywx/client_remind/create", e, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "操作成功",
            icon: "none",
          });
          this.getDataDetail();
          this.isShow = false;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    onClickRemind1(e) {
      e.client_id = this.user_detail.id
      this.$ajax.post("/admin/crm/client/sms_remind", e, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "操作成功",
            icon: "none",
          });
          uni.setStorageSync("send_template1", JSON.stringify({ date: +new Date(), client_id: this.user_detail.id }))
          // this.getDataDetail();
          this.isShow = false;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    ongoTop() {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300,
      });
    },
    getCustomer() {
      var that = this;
      if (this.un_renling_status) {
        uni.showToast({
          title: "不可认领",
          icon: "none"
        })
        return
      }
      uni.showModal({
        title: "提示",
        content: `是否认领该客户？`,
        success: function (res) {
          if (res.confirm) {
            let form = {
              ids: that.user_detail.id + "",
            };
            that.$ajax.post("/qywx/client/get", form, (res) => {
              if (res.statusCode === 200) {
                uni.showToast({
                  title: "领取成功",
                  icon: "none",
                });
                that.$navigateTo(
                  `/customer/detail?id=${that.client_id}&form=2`
                );
                // that.getDataDetail();
              } else {
                uni.showToast({
                  title: res.data.message,
                  icon: "none",
                });
              }
            });
          }
        },
      });
    },
    goBack() {
      this.$navigateBack();
    },
    concatOwner() {
      this.setViewTel()
      //this.qiangzhigj()
      // if (this.from == "fromOther") return
      // uni.makePhoneCall({
      //   phoneNumber: this.user_detail.mobile,
      //   success: () => {
      //     console.log("拨打经纪人电话");
      //   }, //仅为示例
      // });
    },
    // 预览图片
    preFollowImgs(filePath, index) {
      uni.previewImage({
        current: index,
        urls: filePath,
        indicator: 'number',
      })
    },
    // 跟进列表点赞功能
    followGiveLike(id) {
      this.$ajax.get(`/qywx/client_follow/click/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
          title: '操作成功',
          icon: "none",
        });
          this.follow_params.page = 1
          this.getFollowData(); // 获取最新数据
          this.getFollowDataNew()
        }
      })
    },
    // 跟进列表赋值文本功能
    followCopy(item) {
      console.log(item, "复制");
      let contents = item.content;
      this.$copyText(contents, () => {
        uni.showToast({
          title: '复制成功',
          icon: "none",
        });
      })
    },
    copyContent(txt){
      this.$copyText(txt+'', () => {
        uni.showToast({
          title: '复制成功',
          icon: "none",
        });
      })
    },
    setTop(item) {
      console.log(22222);
      this.followSetTop(item)
    },
    hideShow() {
      console.log(123);
      for (let index = 0; index < this.follow_list.length; index++) {
        const element = this.follow_list[index];
        this.$set(element, "show", false)
        this.$forceUpdate()
      }
      // this.follow_list.map(item => item.show = false)
    },

    // 跟进列表设置为置顶
    followSetTop(item) {
      this.$ajax.get(`/qywx/client_follow/top/${item.id}`, {}, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: '操作成功',
            icon: "none",
          });
          this.follow_params.page = 1;
          this.getFollowData(); // 获取最新数据
          this.getFollowDataNew()
        }
      })
    },
    setHide() {
      this.follow_list.map(item => {
        item.show = false
        return item
      })
      this.$forceUpdate()
    },
    playVoice(item, index) {
      innerAudioContext && innerAudioContext.stop()
      // if (item.playing) {
      //   innerAudioContext.stop()
      //   return
      // }
      this.follow_list.map((i, idx) => {
        if (idx != index) {
          i.playing = false
        }
        return i
      })

      // setTimeout(() => {
      this.$set(this.follow_list[index], 'playing', true)

      // item.playing = true
      innerAudioContext.src = item.url
      console.log(this.follow_list, 11123, item.url);
      innerAudioContext.play()
      this.$forceUpdate()
      // }, 100);
    },
    // 显示置顶操作框
    showPinned(item, index) {
      this.dialogs.followOp = true;
      this.curSeledFollow = item;

      return;
      /* console.log(item, index);
      this.follow_list.map(item => {
        item.show = false
        return item
      })
      item.show = true
      this.$forceUpdate() */

      if(item.show){
        return;
      }
      this.pageClickHandlers.push({
        handler(item){
          this.$set(item, 'show', false)
        },
        params: [item],
        trigger: false
      });

      this.$set(item, 'show', true);
    },
    eventPinned() {
      console.log("123");
    },
    addFollowReply(followId){
      this.curSeledFollowId = followId;
      this.dialogs.addReplyComment = true;
    }
  },
  onReachBottom() {
    if (this.is_tabs == 7) {
      this.loadmoreFollow();
    }
    if (this.is_tabs == 3) {
      this.loadmoreFollowNew();
    }
    if (this.is_tabs == 4) {
      this.LoadMoreLogs();
    }
    if (this.is_tabs == 5) {
      this.loadMoreGuiji();
    }
    if (this.is_tabs == 6) {
      this.loadMoreTel();
    }
    if (this.is_tabs == 8) {
      this.loadMoredaikan();
    }
  },

  async onPullDownRefresh(){
    this.getDataDetail();
    this.getTypeData();
    this.setProgress();
    this.getdaikanList();
    await this.$Utils.sleep(600)
    uni.stopPullDownRefresh();
  }
  
};
</script>
<style lang="scss" scope>
// 按钮自定义加载效果

.button-container {
  position: relative;
}

.custom-button {
  background-color: #007bff;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  z-index: 1;
}

.overlay {
  position: fixed;
  top: 50%;
  left: 42%;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.loader {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #007bff;
  border-radius: 50%;
  width: 110rpx;
  height: 110rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.allbgc{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);

}


.timeline_item{
  margin-top: 24rpx;
}
.auto-scroll-container {
  overflow: hidden;
  height:100%; /* 可视区域高度，根据需要调整 */
}

.auto-scroll-content {
  transition: transform .5s ease; /* 滚动动画效果 */
}

.scroll-item {
  height: 41px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
          color: #FF7D00;
  // border: 2rpx solid #ccc;
  overflow: hidden;
}
.text_right{
  width: 100%;
}
.time_line {
  background: #fff;
  padding: 2rpx 32rpx 24rpx 46rpx;
  .item {
    position: relative;
    padding: 0 0upx 36upx 32upx;
    border-left: 5rpx solid #e4e4e4;
    .time_line_itme_header{
      padding: 4rpx 0 28rpx;
      display: flex; flex-direction: row; justify-content: space-between;
    }
    .time-record-player{
      margin: 16rpx 0 0;
    }
    .title {
      font-size: 28upx;
      margin-bottom: 15upx;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;
    }
    .time {
      color:  rgba(41, 44, 57, 0.40);
font-size: 28rpx;
    }
  }
  .item::after {
    content: "";
    height: 32rpx;
    width: 32rpx;
    box-sizing: border-box;
    border-radius: 50%;
    position: absolute;
    border: 4rpx solid #3399ff;
    background-color: #fff;
    left: -18rpx;
    top: 0;
  }
}
.beizu {
width: 10%;
margin-top: 4rpx;
margin-left: 16rpx;
// margin-right: 32rpx;
}
.contenturkl {
  display: block;
  color:  #292C39;
font-size: 32rpx;
font-weight: 400;
  line-height: 1.8;
  white-space: normal; // 规定段落中的文本不进行换行
  word-break: break-all; // 允许单词中换行，在容器的最右边进行断开不会浪费控件
  word-wrap: break-word;
}
.top-card-contenter{
  position: relative;
  border-radius: 8rpx;
border: 2rpx solid #F0F1F5;
background: #F8F8F8;
padding: 24rpx 24rpx 0rpx 0;
margin-top: 24rpx;
display: flex;
flex-direction: row;
// align-items: center;
}
.top-cardser {
  padding: 24rpx 32rpx 32rpx 32rpx;
  background: #fff;
  // border-radius: 10rpx;
  display: block;
  // margin-bottom: 48rpx;
  border-top: 24rpx solid #F8F8F8;
  // position: relative;

}

.top-cardser.fixed {
  position: -webkit-sticky;
  position: sticky;
  z-index: 8;
  top: -40rpx;
}

.content {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 24rpx;
  // justify-content: space-between;

}
.textarear {
        position: relative;
        // margin-top: 8px;
        // padding: 24rpx 16rpx;
        width: 100%;
        // padding-left: 30rpx;
        // margin-left: 42rpx;
      }
.content_detail {
  width: 470rpx;
  display: inline-block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  margin-top: 24rpx;
  font-size: 32rpx;
  margin-left: 46rpx;
}

.Outbound {
  margin-top: 24rpx;
  color: #488AF6;
  font-size: 28rpx;
}

.top_tel_all {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-size: 24rpx;
  padding: 0rpx 0 24rpx 0;
  color: rgba(41, 44, 57, 0.40);
}

.tong_tel {
  margin-right: 24rpx;
  color: #292C39;
  font-size: 32rpx;

}

.top_marginleft {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 24rpx;

}

.top_tel_tel {
  color: rgba(41, 44, 57, 0.40);
  font-size: 32rpx;
  margin-top: 24rpx;
}

.top-cards {
  padding: 32rpx 32rpx;
  // background: #fff;
  border-radius: 5px;
  // margin: 0 18px 10px;
  display: block;
  position: relative;

}

.salesProgress_card {
  margin-top: 32rpx;
}

.levelimage {
  width: 280rpx;
  height: 264rpx;
  background: #eee;
  border-radius: 16rpx;
}

.top_gj {
  margin-left: 8rpx;
  margin-right: 24rpx;
}

page {
  background: #ffff;
  color: #2e3c4e;
}
.addtagAll{
  margin-right: 20rpx;
  margin-bottom: 16rpx;
  padding: 0 16rpx;
  border-radius: 8rpx;
border: 2rpx solid #488AF6;
text-align: center;
background: #ffff;
color:  #488AF6;
font-size: 24rpx;
height: 48rpx;
line-height: 46rpx;
}
.tagcontenter{
  padding-top: 24rpx;
  padding-bottom: 24rpx;
  display: flex;
  flex-direction: row;
  align-items: self-start;
}
.top-card-content-labels {
  width: 15%;
  color: rgba(41, 44, 57, 0.4);
  font-size: 32rpx;
  font-weight: 400;
  margin-top: 6rpx;
}

.top {
  display: block;

  .top_img {
    background: #fff;
    height: 400rpx;
    position: relative;

    .top_img_main {
      width: 100%;
      height: 41px;
      // padding: 0 18px;
      box-sizing: border-box;
      position: absolute;
      // bottom: 326rpx;
      left: 0px;

      .top_img_follow {
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 100%;
        background-color: #FFF7E8;
        ;
        // border-radius: 5px;
        padding: 0 32rpx;
        box-sizing: border-box;

        .top_img_cus {
          font-size: 14px;
          color: #FF7D00;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }

        .top_img_time {
          font-size: 14px;
          color: #FF7D00;
        }

        .top_img_cus {
          .un_follow {
            color: #e15762;
          }
        }

        .top_img_time.drop_to_sea {
          color: #dcc05e;
        }
      }
    }
  }

  &-card {
    padding: 0rpx 32rpx 32rpx 32rpx;
    background: #fff;
    // border-radius: 5px;
    // margin: 0 18px 10px;
    display: block;
    position: relative;

    &-title {
      justify-content: space-between;
      font-size: 14px;
      color: #292C39;
      font-size: 36rpx;
      font-weight: 500;
    }
&.top-card:last-child{
  border: none;
}
    &-left {
      .pic {
        width: 128rpx;
        height: 128rpx;
      }
    }

    &-right {
      margin-left: 12px;
      // text-align: center;

      .setting {
        width: 20px;
        height: 20px;
        position: absolute;
        right: 14px;
      }

      &-top {
        align-items: center;

        &_name {
          color: #292C39;
          // text-align: center;
          font-size: 36rpx;
          font-weight: 500;
          margin-top: 26rpx;
        }

        &_qw {
          height: 18px;
          width: 18px;
          margin-left: 4px;
        }

        &_sex {
          height: 16px;
          width: 16px;
          margin-left: 4px;
          margin-top: 22rpx;
        }

        &_tracking {
          color: #3e8afd;
          margin-top: 20rpx;
          // margin: 0 6px;
          font-size: 12px;
        }

        &_level {
          color: #fff;
          padding: 2px 12px;
          border-radius: 2px;
        }
      }

      &-bottom {
        margin-top: 10px;
        color: #828488;
        font-size: 12px;
      }
    }

    &-bot-bottom {
      // margin-top: 16px;
      width: 100%;
      // border-top: 1px solid #eeeeee;
      padding-top: 32rpx;
      font-size: 12px;
      justify-content: space-between;
      color: #828488;
    }

    &-content {
      // margin-top: 16px;
      display: flex;
      width: 100%;
      flex-direction: row;
      padding: 24rpx 0 24rpx 0;
      white-space: normal; // 规定段落中的文本不进行换行
  word-break: break-all; // 允许单词中换行，在容器的最右边进行断开不会浪费控件
  word-wrap: break-word;
      &.tel {
        align-items: center;
      }

      &-label {
        color: #737373;
        color: rgba(41, 44, 57, 0.40);
        font-size: 32rpx;
        font-weight: 400;

        &.top-card-content-label_top {
          min-width: 112rpx;
          align-self: flex-start;
        }
      }

      &-right {
        margin-left: 20px;
        font-size: 32rpx;

        &.has_follow {
          display: inline-block;

          .un_tong {
            position: relative;

            &.tong {
              &::after {
                background: #9edf2e;
              }
            }

            &::after {
              content: '';
              position: absolute;
              right: -10rpx;
              top: -2rpx;
              background: #f56c6c;
              width: 10rpx;
              height: 10rpx;
              border-radius: 50%;
            }
          }
        }
      }
.auto-scroll-container {
  overflow: hidden;
  height: 250px; /* 可视区域高度，根据需要调整 */
}

.auto-scroll-content {
  transition: transform 1s ease; /* 滚动动画效果 */
}

.scroll-item {
  height: 50px; /* 项目高度，根据需要调整 */
  line-height: 50px; /* 垂直居中文本 */
  text-align: center;
  border: 1px solid #ccc;
}
      .textarea {
        position: relative;
        // margin-top: 8px;
        padding: 24rpx 16rpx;
        background: #f5f7fa;
        border: 1px solid #e8e8e8;
        height: 200rpx;
        width: 100%;
        padding-left: 100rpx;
        // margin-left: 42rpx;
      }

      .placeholderClass {
        color: #292C39;
        font-size: 32rpx;
      }
    }

    &-label-list {
      margin-top: 8px;
      flex-wrap: wrap;

      .item {
        margin-bottom: 4px;
        padding: 4px 12px;
        border-radius: 4px;
        background: #e8f1ff;
        color: #2f6aff;
        margin-right: 10px;
      }
    }

    .chakan {
      align-self: flex-start;
      font-size: 13px;
      color: #2f6aff;
      position: absolute;
      right: 14px;
    }

    .level {
      width: 60px;
      display: inline-block;
      margin-top: 10px;
      text-align: center;
      border-radius: 4px;
      background: #f1f4fa;
      color: #737373;
      height: 28px;
      line-height: 28px;
      font-size: 14px;
    }

    &-tabs {
      font-size: 14px;
      justify-content: space-around;

      &-item {
        position: relative;

        &.isactive {
          color: #2f6aff;

          &::after {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            content: '';
            height: 4px;
            background: #2d84fb;
            width: 30px;
            display: block;
            margin-top: 18px;
          }
        }
      }
    }
  }
}

.setting-pop {
  height: 400px;
  border-radius: 20px 20px 0px 0px;
  background: #f6f6f6;
  padding: 14px 18px 42px;

  &-top {
    align-items: center;
    background: #fff;
    justify-content: space-around;
    border-radius: 5px;
    padding: 22px 0;

    .item {
      align-items: center;
      font-size: 16px;
    }

    .item-img {
      width: 20px;
      margin-bottom: 6px;
      height: 20px;
    }
  }

  &-bottom {
    background: #fff;
    border-radius: 5px;
    padding: 22px 20px;
    margin-top: 16px;

    .item {
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      .left {
        align-items: center;
        font-size: 16px;

        .item-img {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
.timeline_item {
  margin: 28rpx 0 28rpx;
  .time {
    margin-bottom: 16rpx;
    font-size: 24rpx;
    font-weight: bold;
    color: #999;
  }
  .agent_name {
    font-weight: normal;
  }
  .user_info {
    margin-bottom: 16rpx;
    flex-direction: row;
    align-items: center;
    .avatar {
      margin-right: 12rpx;
      width: 62rpx;
      height: 62rpx;
      border-radius: 50%;
    }
    .name {
      font-size: 24rpx;
      color: #555555;
    }
    .tname {
      margin-top: 6rpx;
      font-size: 23rpx;
      color: #8a929f;
    }
    .tel {
      margin-left: 12rpx;
      flex-direction: row;
      align-items: center;
      font-size: 26rpx;
      color: #2d84fb;
    }
  }
  .content {
    margin-bottom: 16rpx;
    margin-top: 24rpx;
    font-size: 28rpx;
    line-height: 1.5;
    word-break: break-all;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // -webkit-line-clamp: 2;
    // -webkit-box-orient: vertical;
    // display: -webkit-box;
    .status {
      margin-right: 12rpx;
      font-weight: bold;
      &.status1 {
        color: #3cc53c;
      }
      &.status2 {
        color: #8a929f;
      }
      &.status3 {
        color: #2d84fb;
      }
      &.status4 {
        color: #fe6c17;
      }
    }
  }
  .img_list {
    flex-direction: row;
    flex-wrap: wrap;
    > image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 4rpx;
      margin-right: 16rpx;
      margin-bottom: 16rpx;
    }
  }
}
.istotop {
  width: 42px;
  height: 42px;
  position: fixed;
  right: 24px;
  bottom: 100px;
}

.content-box {
  background: #fff;
  padding: 14px;
  border-radius: 5px;
}

.card-border-line {
  padding-bottom: 14px;
  border-bottom: 1px solid #eeeeee;
}

.pic_box {
  display: flex;
  flex-direction: column;
  margin-bottom: 100px;
  background: none;
  padding: 0;

  .basic-info-box {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 14px;
    box-sizing: border-box;
    background-color: #ffffff;
    margin-top: 4px;
    margin-bottom: 20px;

    .basic-info-title {
      color: #292C39;
      font-size: 18px;
    }

    .basic-info-content {
      display: flex;
      flex-direction: row;
      margin-top: 20px;
      .content-value{
        display: inline-block;
      }

      .content-title {
        font-size: 13px;
        color: #737373;
        margin-right: 20px;
        white-space: pre;  
      }
      .copy-img{
          width: 26rpx;
          height: 26rpx;
          margin-left: 10rpx;
      }
    }
  }

  .list {
    background: #fff;
    border-radius: 6px;
    padding: 14px;
    box-sizing: border-box;

    .info {
      font-size: 16px;
      margin-top: 25px;
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;

      .c2 {
        text-align: end;
        font-size: 12px;
        color: #828488;

        &.depart {
          align-items: flex-start;
          text-align: left;
          margin-top: 20rpx;
        }

        &.flex_start {
          text-align: left;
          align-items: flex-start;
        }
      }

      .time {
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
        color: #737373;
      }

      .pic {
        align-items: center;
        margin-top: 12px;

        image {
          width: 90rpx;
          height: 90rpx;
          min-width: 90rpx;
          border-radius: 50%;
          margin-right: 12px;
        }
      }
    }
  }
}
.follow-giveLike-main{
  display: flex;
  flex-direction: row;
  align-items: center;
  // justify-content: space-between;
  // padding: 8rpx 16rpx;
  margin-top: 24rpx;
  font-weight: 400;
  color: #FF7D00;
  // width: 20%;
font-size: 28rpx;
  border-radius: 22%;
// background: #FFF7E8;
}
.follow-card {
  &-item {
    margin-bottom: 24px;
  }

  &-content {
    // background: #fff;
    border-radius: 5px;
    // margin-top: 14px;
    // padding: 14px;
    font-size: 28rpx;
    width: 100%;
    color: #828488;
    line-height: 20px;
    white-space: normal; // 规定段落中的文本不进行换行
  word-break: break-all; // 允许单词中换行，在容器的最右边进行断开不会浪费控件
  word-wrap: break-word;
    span {
      // color: #000;
    }

    .order {
      display: block;
      margin-right: 4rpx;

      .order_c {
        display: inline-block;
        padding: 0 8rpx;
        font-size: 24rpx;
        background: #e15762;
        color: #fff;
        border-radius: 2rpx;
      }
    }
  }


}
.follow-giveLike-zan {
          width: 32rpx;
          height: 32rpx;

          image {
            width: 100%;
            height: 100%;
          }}
.lijibtn {
  border-radius: 5px;
  background: #3172f6;
  box-shadow: 0px 4px 10px 0px #3172f67f;
  position: fixed;
  color: #fff;
  line-height: 40px;
  text-align: center;
  left: 50%;
  bottom: 10%;
  transform: translate(-50%, 0);
  width: 300px;
}
.follow-card-picture {
    padding: 0 0rpx 0 120rpx;
    background-color: #fff;
    position: relative;



    .follow-giveLike-box {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .follow-giveLike-main {
        display: flex;
        flex-direction: row;
        align-items: center;
        border-radius: 12px;
        padding: 5px 14px;
        box-sizing: border-box;
        background-color: #ebebeb;

        .follow-giveLike-zan {
          width: 14px;
          height: 14px;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .follow-giveLike-user {
          display: flex;
          flex-direction: row;
          padding-left: 16rpx;
          margin-left: 16rpx;
          color: #8a929f;
          font-size: 24rpx;
          border-left: 1px solid #8a929f;
        }
      }

      .follow_giveLike-controls {
        display: flex;
        flex-direction: row;
        flex: 1;
        justify-content: flex-end;
        position: relative;

        .follow_giveLike_icon {
          width: 28rpx;
          height: 28rpx;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .follow_giveLike_more {
          height: 28rpx;
          padding: 10rpx 0 0 10rpx;
          margin-left: 18rpx;

          // line-height: 16rpx;
          image {
            width: 28rpx;
            height: 8rpx;
          }
        }

        .follow-Pinned-box {
          // display: none;
          padding: 10rpx 0;
          border-radius: 5px;
          background: #ffffff;
          box-shadow: 0px 0px 8px 0px #0000003f;
          position: absolute;
          top: 40rpx;
          right: 0;
          z-index: 1;

          .Pinned-text {
            color: #6c6f74;
            font-size: 28rpx;
            padding: 18rpx 28rpx;
            word-break: keep-all;
            +.Pinned-text{
              border-top: 1px solid #e9e9e9;
            }
          }
        }
      }
    }
  }
.isborder {
  animation: glow 800ms ease-out infinite alternate;
}

@keyframes glow {
  0% {
    border-color: #2d84fb;
    box-shadow: 0 0 5px #2d84fb, inset 0 0 5px rgba(221, 225, 233, 1), 0 1px 0 #2d84fb;
  }

  100% {
    border-color: rgba(221, 225, 233, 1);
    box-shadow: 0 0 20px #2d84fb, inset 0 0 10px rgba(221, 225, 233, 1), 0 1px 0 #2d84fb;
  }
}
.follow-picture-box {
    flex-direction: row;
    flex-wrap: wrap;
    margin-top: 10px;

      image {
        width: 113rpx;
        height: 113rpx;
        border-radius: 4rpx;
        margin-right: 16rpx;
        margin-bottom: 16rpx;
      }
    }
.footer_btn_group {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9;
  background-color: #fff;
  padding: 12px 24px;

  .option_btn {
    display: block;
    text-align: center;

    .more_con {
      align-items: center;
      padding: 0 20rpx;
    }

    .more {
      display: block;
      width: 6rpx;
      font-size: 44rpx;
      transform: rotate(90deg);
      // line-height: 10rpx;
      // overflow: hidden;
      // word-wrap: break-word;
      // line-height: 18rpx;
      // padding: 6rpx 0;
    }

    &.option_btn_cloumn {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 32rpx;
      color: #8a929f;

      image {
        width: 45rpx;
        height: 34rpx;
      }
    }

    ~.option_btn {
      margin-left: 24rpx;
    }

    ::v-deep .my-btn.big {
      padding: 0 10rpx;
    }
  }
}
.follow-giveLike-user{
  margin-left: 10rpx;
}

.l-title {
  font-size: 24rpx;
  color: #2e3c4e;
}

.top-card.fixed {
  position: sticky;
  z-index: 8;
  top: 0;
}

.tag_input,
.top-card-label-list {
  // margin-top: 20px;
  // border: 1px solid #dde1e9;
  border-radius: 4px;
  align-items: center;
  // padding: 9px 12px;
  flex-wrap: wrap;

  .tag_item {
    margin-bottom: 16rpx;
    padding: 0px 16rpx;
    border-radius: 8rpx;
    height: 48rpx;
    line-height: 48rpx;
    background: #e8f1ff;
    color: #2f6aff;
    margin-right: 16rpx;
    // padding: 8px 20px;
    // text-align: center;
    // margin-right: 10px;
    // // margin-left: 10px;
    // margin-bottom: 12px;
    // background: #fff;
    // border-radius: 4px;
    // border: 1px solid #eee;
    // min-width: 80px;
    // color: #8d9099;
    position: relative;
    overflow: hidden;

    &.checked {
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
      background: #fff;
    }

    .tag_t {
      font-size: 14px;
      margin-bottom: 10rpx;
    }

    .tag_b {
      font-size: 14px;
    }

    .checked {
      position: absolute;
      right: 0;
      top: 0;
      width: 32rpx;
      height: 24rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }

  input {
    width: 100%;
    font-size: 14px;
  }
}

.top-card-label-lists {
  // margin-top: 20px;
  // border: 1px solid #dde1e9;
  border-radius: 4px;
  align-items: center;
  // padding: 9px 12px;
  flex-wrap: wrap;

  // margin-left: 40rpx;
  .tag_item {
    // margin-bottom: 4px;
    padding: 0 16rpx;
    border-radius: 8rpx;
    background: #e8f1ff;
    color: #2f6aff;
    height: 48rpx;
    line-height: 48rpx;
    margin-right: 16rpx;
    margin-bottom: 16rpx;
    // padding: 8px 20px;
    // text-align: center;
    // margin-right: 10px;
    // // margin-left: 10px;
    // margin-bottom: 12px;
    // background: #fff;
    // border-radius: 4px;
    // border: 1px solid #eee;
    // min-width: 80px;
    // color: #8d9099;
    position: relative;
    overflow: hidden;

    &.checked {
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
      background: #fff;
    }

    .tag_t {
      font-size: 14px;
      margin-bottom: 10rpx;
    }

    .tag_b {
      font-size: 14px;
    }

    .checked {
      position: absolute;
      right: 0;
      top: 0;
      width: 32rpx;
      height: 24rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }
  }

  input {
    width: 100%;
    font-size: 14px;
  }
}

.reason {
  width: 80vw;
  height: 400rpx;
  margin: calc(100% - 200rpx) auto;
  padding: 40rpx;
  border-radius: 20rpx;
  background: #fff;
  position: relative;

  .close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    font-size: 48rpx;
  }

  textarea {
    margin-top: 8px;
    padding: 6px 16px;
    box-sizing: border-box;
    background: #f5f7fa;
    border: 1px solid #e8e8e8;
    height: 280rpx;
    width: 100%;
  }

  .btn {
    display: inline-block;
    padding: 20rpx 20rpx;
    background: #2d84fb;
    color: #fff;
    margin: 20rpx auto 0;
  }
}

.follow-config-main {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
  justify-content: space-between;

  .InstalledTop {
    padding: 0px 4px;
    border-radius: 2px;
    background: #2d84fb;
    color: #ffffff;
    font-size: 12px;
  }

  .follow-config-titel {
    display: flex;
    flex-direction: row;
    color: #2e3c4e;
    font-size: 14px;
    font-weight: bold;
  }
}

.follow-box-content {
  margin-bottom: 10px;
}

.redInfo {
  color: #ff0000;
}

.blueInfo {
  color: #3d91ff;
}

.voice {
  background: #2d84fb;
  width: 300rpx;

  image {
    width: 40rpx;
    height: 40rpx;
  }

  text {
    color: #fff;
    margin-left: 10rpx;
  }
}

.setting_level {
  width: 80rpx;
  height: 40rpx;
  position: absolute;
  right: 16rpx;
  top: 44rpx;
}

.setting_levels {
  width: 80rpx;
  height: 40rpx;
  position: absolute;
  right: 0rpx;
  top: 22px;
}

.un_level {
  background: #bcbcbc;
  color: #8a929f;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  position: absolute;
  right: 28rpx;
}

.nameText {
  // margin-left: 50rpx;
  max-width: 160rpx;
  overflow: hidden;
  text-overflow: ellipsis; //text-overflow: clip|ellipsis|string;
  overflow: hidden;
  white-space: nowrap;
}

.top-card-content-act {
  width: 140rpx;
  // margin-left: 40rpx;
  font-size: 22rpx;
  margin-top: 10rpx;
  padding: 4rpx 8rpx;
  border: 1rpx solid #2d84fb;
  color: #2d84fb;

  &.address {
    color: #828488;
    border: none;
    width: auto;
  }
}

.tel_flex {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
}

.top-card-content-last_follow {
  color: #828488;
  margin-top: 10rpx;
  margin-left: 40rpx;
  line-height: 1.5;
}

.p_con {
  padding: 40rpx;
  background: #fff;
  border-radius: 8rpx;
  position: relative;
  padding-top: 140rpx;
  width: 80vw;

  /* height: 50vh; */
  .title {
    font-size: 40rpx;
    font-weight: 600;
    padding: 40rpx 0;
    text-align: center;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    text-align: center;
  }

  .p_content {
    .p_item {
      .label {
        margin-right: 5rpx;
      }
    }

    .btns {
      margin-top: 200rpx;

      .btn {
        background: #2d84fb;
        padding: 30rpx 0;
        color: #fff;
        border-radius: 10rpx;
      }
    }
  }
}

.option_btn.disabled {
  ::v-deep .my-btn.primary {
    background: rgba(45, 132, 251, 0.3);
    color: rgba(45, 132, 251, 0.8);
  }
}
</style>
