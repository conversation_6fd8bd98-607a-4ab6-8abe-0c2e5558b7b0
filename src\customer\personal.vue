<template>
  <view class="edit">
    <view class="user-box row">
      <view class="input-box row">
        <view class="right">新密码：</view>
        <view class="row input-value">
          <input type="password" class="uni-input"
            placeholder="请输入新密码"
            v-model.trim="params.password"
            placeholder-style="color:#C9CDD4"
          />
        </view>
      </view>
    </view>
    <view class="user-box row">
      <view class="input-box row">
        <view class="right">确认密码：</view>
        <view class="row input-value">
          <input type="password" class="uni-input"
            placeholder="请输入确认密码"
            v-model.trim="params.password_confirmation"
            placeholder-style="color:#C9CDD4"
          />
        </view>
      </view>
    </view>

    <button @click="onSubmit" class="btn" :loading="submiting">确认修改</button>
   
  </view>
</template>

<script>
export default {
  components: {

  },
  data () {
    return {
      submiting: false,
      params: {
        password: "",
        password_confirmation: "",
      },
    };
  },
  methods: {
    onSubmit () {
      if (this.params.password === '') {
        uni.showToast({
          title: "请输入新密码",
          icon: "none",
        });
        return;
      }
      if (this.params.password.length < 6) {
        uni.showToast({
          title: "密码长度至少6位",
          icon: "none",
        });
        return;
      }
      if (this.params.password_confirmation === '') {
        uni.showToast({
          title: "请输入确认密码",
          icon: "none",
        });
        return;
      }
      if(this.params.password != this.params.password_confirmation){
        uni.showToast({
          title: "新密码与确认密码不一致",
          icon: "none",
        });
        return;
      }

      if(this.submiting){
        return;
      }
      try{
        this.submiting = true;
        this.$ajax.post("/admin/personnelMatters/editPassword", this.params, (res) => {
          this.submiting = false;
          if (res.statusCode === 200) {
              uni.showToast({
                  title: res?.data?.msg || "保存成功"
              });
              setTimeout(()=>{
                this.$navigateBack();
              }, 1500)
          } else {
            uni.showToast({
              title: res?.data?.message || '保存失败',
              icon: "none",
            });
          }
        }, (err) => {
          this.submiting = false;
        });
      }catch(e){
        console.error(e);
        this.submiting = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
page {
  background: #f5f5f5;
}
.uni-input {
  margin-right: 24rpx;
}
.edit {
  .user-box {
    background: #fff;
    padding: 24rpx 48rpx;
    align-items: center;
    border-bottom: 1rpx solid #f2f3f5;
    .right {
      min-width: 200rpx;
      text-align: right;
      color: #4E5969;
      padding: 10rpx 0;
      font-size: 32rpx;
    }
    .left {
      flex: 1;
      align-items: center;
    }
    .input-box {
      width: 100%;
      align-items: center;
      .input-value {
        align-items: center;
      }
      input {
        margin-left: 40rpx;
        font-size: 28rpx;
      }
    }
  }
}
button::after {
  border: none;
}
.btn {
  margin-top: 40rpx;
  padding: 10rpx;
  align-items: center;
  color: #fff;
  font-size: 34rpx;
  width: 630rpx;
  height: 104rpx;
  box-shadow: 0 2px 10px 0 rgba(1, 116, 255, 0.2);
  background: #0174ff;
  border-radius: 50rpx;
  &:last-child {
    margin-bottom: 80rpx;
  }
}
::v-deep .picker-input-label,
::v-deep .phone-code-label{
  min-width: 56px;
  text-align: center;
  color: #666;
  padding: 5px;
  font-size: 16px;
}
</style>
