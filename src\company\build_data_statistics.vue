<template>
  <view class="list">
    <view class="search row">
      <view class="title">报备数据</view>
      <view class="right">
        <myIcon
          class="icon"
          type="ic_sousuo3x1"
          color="#999"
          size="40rpx"
        ></myIcon>
        <input
          v-model="company_name"
          class="uni-input"
          type="text"
          placeholder="公司查找"
          @confirm="onConfirm"
        />
      </view>
    </view>
    <view class="search-list " v-if="isSearch">
      <view
        class="line row"
        v-for="item in company_list"
        :key="item.id"
        @click="handleSearch(item)"
      >
        <view class="left">{{ item.name }}</view>
        <view class="right"
          ><myIcon type="you" color="#999" size="28rpx"></myIcon
        ></view>
      </view>
      <loadMore :status="load_status_company"></loadMore>
    </view>
    <view class="desc row">
      <block v-for="(item, index) in reported_list" :key="index">
        <view class="top-desc row">
          <view class="top-desc-box">
            <view class="top-ctn">{{ item.description }}</view>
            <view class="top-amount">{{ item.amount }}</view>
          </view>
        </view>
      </block>
    </view>
    <view class="filter row">
      <picker
        mode="multiSelector"
        class="picker-box "
        @change="bindPickerChange"
        :value="index"
        :range="array"
        range-key="desc"
      >
        <view class="uni-input"
          >{{ array[0][index[0]].desc }}-{{ array[1][index[1]].desc }}-{{
            array[2][index[2]].desc
          }}</view
        >
        <view class="filter-btn">筛选</view>
      </picker>
    </view>
    <view class="tables">
      <t-table>
        <t-tr class="back">
          <t-th v-for="(item, index) in tables_th" :key="index">{{
            item
          }}</t-th>
        </t-tr>
        <t-tr v-for="(item, index) in company_statistics_list" :key="index">
          <t-td>{{ item.user_name || item.phone }}</t-td>
          <t-td>{{ item.total_0 }}</t-td>
          <t-td>{{ item.total_1 }}</t-td>
          <t-td>{{ item.total_2 }}</t-td>
          <t-td>{{ item.total_3 }}</t-td>
          <t-td>{{ item.total_4 }}</t-td>
          <t-td>{{ item.total_5 }}</t-td>
          <t-td>{{ item.total_10 }}</t-td>
        </t-tr>
      </t-table>
      <load-more :status="load_status"></load-more>
    </view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import tTable from "@/components/t-table/t-table.vue";
import tTh from "@/components/t-table/t-th.vue";
import tTr from "@/components/t-table/t-tr.vue";
import tTd from "@/components/t-table/t-td.vue";
import loadMore from "@/components/loadMore";
import myIcon from "@/components/my-icon";
export default {
  components: {
    tTable,
    tTh,
    tTr,
    tTd,
    loadMore,
    myIcon,
  },
  data() {
    return {
      params: {
        build_id: "",
        date_str: "day",
        page: 1,
        order: "total_0",
        sort: "desc",
        all: 1,
        company_id: "",
      },
      load_status: "",
      load_status_company: "",
      reported_list: [],
      array: [
        [
          { desc: "今天", value: "day", id: 1 },
          { desc: "昨天", value: "yesterday", id: 2 },
          { desc: "本周", value: "week", id: 3 },
          { desc: "本月", value: "month", id: 4 },
          { desc: "上月", value: "last_month", id: 5 },
          { desc: "季度", value: "quarter", id: 6 },
          { desc: "今年", value: "year", id: 7 },
        ],
        [
          { desc: "报备量", value: "total_0", id: 1 },
          { desc: "已报备", value: "total_1", id: 2 },
          { desc: "已带看", value: "total_2", id: 3 },
          { desc: "已认筹", value: "total_3", id: 4 },
          { desc: "已认购", value: "total_4", id: 5 },
          { desc: "已成交", value: "total_5", id: 6 },
          { desc: "已失效", value: "total_10", id: 7 },
        ],
        [
          { desc: "倒序", value: "desc", id: 1 },
          { desc: "正序", value: "asc", id: 2 },
        ],
      ],
      company_list: [],
      index: [0, 0, 0],
      tables_th: [
        "经纪人",
        "报备量",
        "已报备",
        "已带看",
        "已认筹",
        "已认购",
        "已成交",
        "已失效",
      ],
      company_statistics_list: [],
      company_name: "",
      isSearch: false,
    };
  },
  onLoad(options) {
    this.params.build_id = options.id;
    this.params.company_id = options.company_id;
    this.$getDictionaryList("REPORTED_STATUS", {}, (res) => {
      this.reported_list = res.data.data;
      this.getBuildStatistics();
    });
  },
  methods: {
    getBuildStatistics() {
      this.$ajax.get(
        "/client/build/statistics/reported/overview",
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            let arr = [];
            for (let i in res.data) {
              for (let j in this.reported_list) {
                if (this.reported_list[j].value == i) {
                  if (this.reported_list[j].description === "待审核") {
                    this.reported_list[j].description = "报备量";
                  }
                  arr.push(
                    Object.assign({}, this.reported_list[j], {
                      amount: res.data[i],
                    })
                  );
                }
              }
            }
            this.reported_list = arr;
            this.getBuildComapnyStatistics();
          }
        }
      );
    },
    getBuildComapnyStatistics() {
      if (this.params.page === 1) {
        this.company_statistics_list = [];
      }
      this.load_status = "loading";
      this.$ajax.get(
        "/client/build/statistics/reported/store/v3",
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.company_statistics_list = this.company_statistics_list.concat(
              res.data.data
            );
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
              uni.showToast({
                title: "没有更多数据了",
                icon: "none",
              });
            }
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        }
      );
    },
    onConfirm(e) {
      let value = e.detail.value;
      if (value) {
        this.load_status_company = "loading";
        this.$ajax.get(
          `/client/company/search?category=2&name=${value}`,
          {},
          (res) => {
            this.load_status_company = "loadend";
            if (res.statusCode === 200) {
              this.company_list = res.data.data;
              if (this.company_list.length === 0) {
                this.load_status_company = "nomore";
              }
              this.isSearch = true;
            }
          }
        );
      } else if (!value) {
        this.isSearch = false;
        // delete this.params.company_id;
        this.getBuildStatistics();
      }
    },
    handleSearch(item) {
      this.params.company_id = item.id;
      this.getBuildStatistics();
      this.isSearch = false;
    },
    bindPickerChange: function(e) {
      this.index = e.detail.value;
      this.params.date_str = this.array[0][this.index[0]].value;
      this.params.order = this.array[1][this.index[1]].value;
      this.params.sort = this.array[2][this.index[2]].value;
      this.params.page = 1;
      this.getBuildStatistics();
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.getBuildComapnyStatistics();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getBuildComapnyStatistics();
  },
};
</script>

<style scoped lang="scss">
page {
  background: #eee;
}
.list {
  .title {
    margin: 24rpx;
    font-size: 32rpx;
    padding-left: 8rpx;
    border-left: 6rpx solid #3566ff;
  }
  .desc {
    background: #fff;
    width: 100%;
    flex-wrap: wrap;
    padding: 0 48rpx;
    justify-content: space-between;
    .top-desc {
      width: 43%;
      margin: 20rpx;
      border-bottom: 2rpx solid #eee;
      justify-content: space-around;
      .top-desc-box {
        line-height: 60rpx;
        align-items: center;
        .top-ctn {
          color: #999;
        }
        .top-amount {
          font-weight: bold;
          font-size: 36rpx;
          color: #3566ff;
        }
      }
    }
  }
  .filter {
    margin: 20rpx 0;
    line-height: 60rpx;
    font-size: 32rpx;
    .picker-box {
      display: flex;
      width: 100%;
      height: 60rpx;
      position: relative;
      .uni-input {
        align-items: center;
        position: absolute;
        left: 0;
        width: 80%;
        background: #fff;
      }
      .filter-btn {
        margin-left: 20rpx;
        background: #fff;
        align-items: center;
        width: 18%;
        position: absolute;
        right: 0;
      }
    }
  }
  .back {
    background: #3566ff;
  }
  .search {
    padding: 16rpx 48rpx;
    align-items: center;
    justify-content: space-between;
    height: 96rpx;
    background: #fff;
    width: 100%;
    z-index: 10;
    .left {
      font-size: 28rpx;
      color: #333;
      align-items: center;
      text {
        margin-right: 6rpx;
      }
    }
    .right {
      position: relative;
      width: auto;
      .icon {
        position: absolute;
        top: 14rpx;
        left: 28rpx;
      }
      input {
        font-size: 28rpx;
        padding-left: 96rpx;
        background: #eee;
        height: 64rpx;
        // width: 530rpx;
        border-radius: 32rpx;
      }
    }
  }
  .search-list {
    top: 96rpx;
    width: 88%;
    background: #fff;
    position: fixed;
    margin: 0 48rpx;
    .line {
      color: #999;
      background: #fff;
      justify-content: space-between;
      padding: 20rpx 0;
      border-bottom: 2rpx solid #d8d8d8;
    }
  }
}
</style>
