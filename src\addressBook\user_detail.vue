<template>
  <view class="user-detail">
    <view class="detail-t">
      <view class="detail-t-icon">
        <text>{{ userData.user_name ? userData.user_name[0] : '' }}</text>
      </view>
      <view class="detail-t-info">
        <view class="info-basic">
          <view class="status">
            <text>{{ userData.user_name }}</text>
            <text :style="userData.status == 0 ? 'color:#f65248;background: #********' : ''">{{ userData.status == 1 ?
          '启用' :
          '禁用' }}</text>
          </view>
          <view class="edit" @click="toEdit" v-if="isAdmin || (!isAdmin && isMe)">
            <text>编辑</text>
            <myIcon style="padding-left: 15rpx;" type="jinrujiantou" color="#808080" size="22rpx">
            </myIcon>
          </view>
        </view>
        <view class="info-role">
          <text class="role-item" v-for="(item, index) in roleSelectedLable" :key="index">{{ item.name }}</text>
        </view>
      </view>
    </view>
    <view class="detail-b">
      <text class="title">成员信息</text>
      <view class="detail-b-box" @click="toEdit">
        <view class="item">
          <text class="item-l">姓名</text>
          <text class="item-r">{{ userData.user_name }}</text>
          <myIcon style="padding-left: 20rpx;" type="jinrujiantou" color="#808080" size="22rpx">
          </myIcon>
        </view>
        <view class="item">
          <text class="item-l">部门</text>
          <view style="display: flex;flex-direction: row;width: 100%; overflow-x: auto;">
            <text class="item-r" style="white-space: nowrap;margin-right: 20rpx;"
              v-for="(item, index) in userData.department" :key="index">{{
          item.name }}</text>
          </view>
          <myIcon style="padding-left: 15rpx;" type="jinrujiantou" color="#808080" size="22rpx">
          </myIcon>
        </view>
        <view class="item">
          <text class="item-l">职位</text>
          <text class="item-r">{{ userData.post }}</text>
          <myIcon style="padding-left: 15rpx;" type="jinrujiantou" color="#808080" size="22rpx">
          </myIcon>
        </view>
        <view class="item">
          <text class="item-l">手机号</text>
          <text class="item-r">{{ userData.phone }}</text>
          <myIcon style="padding-left: 15rpx;" type="jinrujiantou" color="#808080" size="22rpx">
          </myIcon>
        </view>
        <view class="item">
          <text class="item-l">设置密码</text>
          <text class="item-r">{{ userData.is_pwd == 1 ? '已设置' : '未设置' }}</text>
          <myIcon style="padding-left: 15rpx;" type="jinrujiantou" color="#808080" size="22rpx">
          </myIcon>
        </view>
        <view class="item">
          <text class="item-l">状态</text>
          <text class="item-r">{{ userData.status == 1 ? '已启用' : '已禁用' }}</text>
          <myIcon style="padding-left: 15rpx;" type="jinrujiantou" color="#808080" size="22rpx">
          </myIcon>
        </view>
      </view>
    </view>
    <view class="detail-btm" v-if="isAdmin">
      <button type="default" @click="del">删除</button>
      <button type="primary" @click="setRole">绑定角色</button>
    </view>
    <tDepartmentPicker multiple :visible.sync="dialogs.isRolePopup" :type="2" v-model="role_id" @confirm="roleChange">
    </tDepartmentPicker>
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
import tDepartmentPicker from '@/components/tplus/tDepartmentPicker.vue';
export default {
  components: {
    myIcon,
    tDepartmentPicker
  },
  data() {
    return {
      role_id: [],
      roleSelectedLable: [],
      param: {
        phone: "",
      },
      dialogs: {
        isRolePopup: false
      },

      userData: {}
    }
  },
  computed: {
    isAdmin() {
      let userInfo = JSON.parse(uni.getStorageSync('userInfo'))
      return userInfo.roles.some(item => item.name == '站长')
    },
    isMe() {
      let userInfo = JSON.parse(uni.getStorageSync('userInfo'))
      return userInfo.id == this.userData.id
    }
  },
  onLoad(options) {
    if (options.phone) {
      this.param.phone = options.phone
    }
    this.getUserDetail()
    uni.$on('refreshData', () => {
      this.userData = {}
      this.getUserDetail()
    })
  },
  methods: {
    getUserRole() {
      this.roleSelectedLable = []
      this.$ajax.get(`/admin/admin_user/role/all/${this.userData.id}`, {}, (res) => {
        if (res.statusCode == 200) {
          this.roleSelectedLable = res.data.roles
          if (res.data.roles.length) {
            res.data.roles.forEach(item => {
              this.role_id.push(item.id)
            })
          }
        }
      })
    },
    getUserDetail() {
      this.$ajax.get('/admin/personnelMatters/memberList', this.param, (res) => {
        if (res.statusCode == 200) {
          this.userData = res.data.data[0]
          this.getUserRole()
        }
      })
    },
    setRole() {
      this.dialogs.isRolePopup = true
    },
    toEdit() {
      if (!this.isAdmin && !this.isMe) return
      this.$navigateTo(`/addressBook/edit_member?phone=${this.param.phone}`)
    },
    del() {
      uni.showModal({
        title: '删除提示',
        content: `确定要删除 ${this.userData.user_name} 吗？`,
        success: (res) => {
          if (res.confirm) {
            this.$ajax.get(`/admin/personnelMatters/deleteMember/${this.userData.id}`, {}, (res) => {
              if (res.statusCode == 200) {
                uni.showToast({
                  title: `成员已删除`,
                  icon: 'success'
                })
              }
            })
          }
        }
      });
    },
    roleChange(data) {
      if (data.length) {
        let param = {
          id: this.userData.id + '',
          role_names: []
        }
        this.roleSelectedLable = []
        data.forEach(item => {
          let obj = {
            id: item.value[0],
            name: item.label[0],
          }
          param.role_names.push(item.label[0])
          this.roleSelectedLable.push(obj)
        })
        this.$ajax.post('/admin/admin_user/role/reset/all', param, (res) => {
          if (res.statusCode == 200) {
            this.getUserRole()
          }
        })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
page {
  background-color: #f6f6f6;
}

.user-detail {
  .detail-t {
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 32rpx;
    background-color: #fff;

    .detail-t-icon {
      margin-right: 20rpx;

      text {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        font-size: 32rpx;
        color: #fff;
        text-align: center;
        line-height: 60rpx;
        background-color: #488AF6;
      }
    }

    .detail-t-info {
      width: 100%;

      .info-basic {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        view {
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        .status {
          &>text:nth-child(1) {
            max-width: 300rpx;
            overflow-x: auto;
            font-size: 36rpx;
            font-weight: 500;
            color: #292C39;
            margin-right: 15rpx;
          }

          &>text:nth-child(2) {
            border-radius: 4px;
            padding: 6rpx 16rpx;
            font-size: 24rpx;
            color: #13A834;
            background: #13A83433;
          }
        }

        .edit {
          text {
            font-size: 24rpx;
            color: #292C3966;
          }
        }
      }

      .info-role {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        width: 100%;
        margin-top: 30rpx;

        text {
          border-radius: 4px;
          padding: 8rpx 16rpx;
          font-size: 24rpx;
          background: #488AF633;
          color: #488AF6;
          margin: 0 20rpx 20rpx 0;
        }
      }
    }
  }

  .detail-b {
    margin-top: 30rpx;
    width: 100%;
    padding: 32rpx;
    background-color: #fff;

    .title {
      font-size: 36rpx;
      font-weight: 500;
      color: #292C39;
    }

    .detail-b-box {
      margin-top: 30rpx;

      .item {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;
        padding: 24rpx 0;

        text {
          font-size: 32rpx;
        }

        .item-l {
          min-width: 160rpx;
          color: #292C3966;
        }

        .item-r {
          font-size: 32rpx;
          color: #292C39;
          width: 100%;
        }
      }
    }
  }

  .detail-btm {
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    width: 100%;
    padding: 30rpx 20rpx 40rpx;
    border-top: 1px solid #f6f6f6;
    background-color: #fff;

    button {
      width: 50%;
    }

    &>button:nth-child(1) {
      margin-right: 30rpx;
    }

    &>button:nth-child(2) {
      background-color: #488AF6;
    }
  }
}
</style>