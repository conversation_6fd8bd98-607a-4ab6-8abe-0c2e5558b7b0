<template>
  <view class="giveup">
    <view class="remark">
      <textarea
        placeholder="请输入放弃原因"
        maxlength="40"
        v-model="form_info.content"
        @input="getNumber"
        :rows="10"
      ></textarea>
      <view>{{ reciprocal }}/40</view>
    </view>
    <view class="btn-bottom">
      <view class="btn-box row">
        <view class="btn plain" @click="$navigateBack()">取消</view>
        <view class="btn" @click="onCreateData">提交</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form_info: {
        ids: "",
        content: "",
      },
      reciprocal: 0,
    };
  },
  onLoad(options) {
    this.form_info.ids = options.id;
  },
  methods: {
    getNumber() {
      //备注字数
      this.reciprocal = this.form_info.content.length;
    },
    onCreateData() {
      if (!this.form_info.content) {
        uni.showToast({
          title: "请检查内容",
          icon: "none",
        });
        return;
      }
      this.$ajax.post("/qywx/client/discard", this.form_info, (res) => {
        if (res.statusCode === 200) {
          this.$navigateTo(`/customer/detail?id=${this.form_info.ids}&form=2`);
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.giveup {
  padding: 12px;
}
.remark {
  position: relative;
  margin: 12px 0;
  background: #f8f8f8;
  textarea {
    border: 1px solid #dde1e9;
    border-radius: 4px;
    width: 100%;
    padding: 10px 12px;
    font-size: 14px;
  }
  view {
    position: absolute;
    bottom: 12px;
    right: 12px;
    font-size: 11px;
  }
}
.btn-bottom {
  height: 100px;
  .btn-box {
    background: #fff;
    justify-content: space-between;
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 24px;
    .btn {
      height: 42px;
      width: 100%;
      line-height: 42px;
      text-align: center;
      color: #fff;
      background: #2d84fb;
      border-radius: 6px;
    }
    .plain {
      background: #fff;
      color: #333;
      border: 1px solid #999;
      margin-right: 20px;
    }
  }
}
</style>
