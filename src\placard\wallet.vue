<template>
    <view>
        <view class="background">
            <view class="background_shade">
            </view>
        </view>
        <view class="billfold">
            <view class="billfold_head">
                <image src="./photo/close.png"></image>
                <view class="head_move_about">
                    <text class="move_about">活动奖金池</text>
                    <text class="remaining_sum">———— &nbsp; 余额：43316.81 &nbsp; ————</text>
                </view>
            </view>
            <view class="countdown">
                <view class="on_line_top">
                    <view class="count_down">活动倒计时</view>
                    <view class="line_top_time">
                        <view>5</view>
                        <text>天</text>
                        <view>17</view>
                        <text>时</text>
                        <view>17</view>
                        <text>分</text>
                    </view>
                </view>
            </view>
            <view class="Red_envelope-list">
                <view>
                    <image src="./photo/red_envelopes.png"></image>
                    <text>阅读红包</text>
                    <text class="highest">最高3.8元</text>
                </view>
                <view>
                    <image src="./photo/red_packet.png"></image>
                    <text>分享红包</text>
                    <text class="highest">最高88元</text>
                </view>
                <view>
                    <image src="./photo/or_kickback.png"></image>
                    <text>榜单奖励</text>
                    <text class="highest">最高50元</text>
                </view>
            </view>
            <view class="my-wallet">
                <view class="wallet_balance">
                    <text>我的钱包</text>
                    <text class="balance">账户余额：</text>
                    <text>￥ 0</text>
                </view>
                <view class="Cash">
                    <view class="Cash-out">
                        <view>去提现</view>
                    </view>
                    <view>
                        <text>(满1元)</text>
                    </view>
                </view>
            </view>
            <view class="Withdrawal">
                <view class="share">
                    <text>分享明细</text>
                    <image src="./photo/arrowhead.png"></image>
                </view>
                <view class="share">
                    <text>提现明细</text>
                    <image src="./photo/arrowhead.png"></image>
                </view>
            </view>
            <view class="foot">
                <view class="footwallet">
                    <text>
                        红包领取数量,数显有没人限制
                        注册后可无限领取,在上方提现
                    </text>
                </view>
                <view class="footwallet aaa" style="display: flex; flex-direction: row;align-items: center">
                    <text style="color:#858A8E;font-size: 18rpx;text-align: right;">
                        长按识别二维码
                        添加客服进行注册
                    </text>
                    <view class="QR_code">

                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {

        }
    },
}
</script>
<style scoped lang="scss">
.background {
    width: 100%;
    height: 1000rpx;
    background-image: url(./photo/picture.png);
    background-repeat: no-repeat;
    background-size: cover;

    .background_shade {
        width: 100%;
        height: 1000rpx;
        background-color: rgba(0, 0, 0, 0.6);

        image {
            width: 100%;
            height: 1000rpx;
        }
    }
}

.billfold {
    width: 100%;
    height: 1090rpx;
    background-color: #fff;
    margin-top: -550rpx;

    .billfold_head {
        width: 100%;
        height: 300rpx;
        background-color: #EB4836;
        background-image: url(./photo/Wallet_background.png);
        border-bottom-left-radius: 80px;
        border-bottom-right-radius: 80px;
        display: flex;
        flex-direction: row-reverse;

        image {
            width: 50rpx;
            height: 50rpx;
            margin-right: 20rpx;
            margin-top: 20rpx;
        }

        .head_move_about {
            width: 450rpx;
            height: 200rpx;
            // background-color: palegoldenrod;
            margin: 0 auto;
            margin-left: 150rpx;
        }

        .move_about {
            text-align: center;
            margin-top: 40rpx;
            color: #fff;
            font-size: 40rpx;
            font-weight: 500;
        }

        .remaining_sum {
            font-size: 25rpx;
            color: #fff;
            text-align: center;
            margin-top: 20rpx;
        }
    }

    .countdown {
        // width: 90%;
        // height: 50rpx;
        // background-color: #fff;
        // margin: 0 auto;
        margin-top: -150rpx;
        // border-top: 0px solid transparent;
        // border-right: 50px solid transparent;
        // border-bottom: 50px solid #fff;
        // border-left: 50px solid transparent;
        // transform: skew(odeg);


        .on_line_top {
            width: 60%;
            height: 80rpx;
            background-color: #FFFFFF;
            // border-radius: 10px;
            border-top-left-radius: 30rpx;
            border-top-right-radius: 30rpx;
            // border: 1px solid #fff;
            // border-bottom-color: #D1D1D1;
            margin: 0 auto;
            display: flex;
            flex-direction: row;

            .count_down {
                margin-top: 20rpx;
                margin-left: 40rpx;
            }

            .line_top_time {
                display: flex;
                flex-direction: row;
                margin-left: 20rpx;
                margin-top: 20rpx;

                view {
                    width: 40rpx;
                    height: 35rpx;
                    background-color: #EB4836;
                    text-align: center;
                    line-height: 35rpx;
                    border-radius: 4rpx;
                    color: #fff;
                    margin-left: 5rpx;
                }

                text {
                    color: #EB4836;
                    margin-left: 5rpx;
                    font-size: 30rpx;
                }

            }
        }
    }

    .Red_envelope-list {
        width: 90%;
        height: 250rpx;
        background-color: #fff;
        margin: 0 auto;
        margin-top: -10rpx;
        border-radius: 10rpx;
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.10);
        display: flex;
        flex-direction: row;
        justify-content: space-around;

        image {
            width: 120rpx;
            height: 120rpx;
        }

        .highest {
            margin-top: 30rpx;
            color: #eb4836;
            font-size: 28rpx;
        }
    }

    .my-wallet {
        width: 90%;
        height: 270rpx;
        background-color: #FEEFDB;
        border-radius: 10rpx;
        margin: 30rpx auto;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .wallet_balance {
            margin-left: 30rpx;
            font-size: 40rpx;
            color: #974800;
            font-weight: 600;
            line-height: 70rpx;

            .balance {
                font-weight: 500;
                font-size: 30rpx;
            }
        }

        .Cash {
            .Cash-out {
                width: 170rpx;
                height: 60rpx;
                background-color: #EB4836;
                margin-right: 100rpx;
                margin-top: 150rpx;
                border-radius: 40rpx;
                text-align: center;
                color: #fff;
                font-size: 25rpx;
                line-height: 50rpx;

            }

            text {
                color: #8C8C8C;
                margin-left: 180rpx;
                font-size: 20rpx;
            }
        }
    }

    .Withdrawal {
        width: 90%;
        margin: 0 auto;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .share {
            width: 48%;
            height: 80rpx;
            background-color: #FEEFDB;
            border-radius: 10rpx;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            padding-left: 20rpx;
            padding-right: 20rpx;

            image {
                width: 35rpx;
                height: 35rpx;
            }
        }
    }

    .foot {
        width: 90%;
        margin: 20rpx auto;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .footwallet {
            width: 48%;
            height: 180rpx;
            background-color: #EB4836;
            border-radius: 10rpx;
            padding: 20rpx;

            text {
                // font-size: 20rpx;
                color: #ffffff;
                text-align: center;
                // line-height: 60rpx;
                margin-top: 10rpx;
            }

            .QR_code {
                width: 130rpx;
                height: 130rpx;
                background-color: palevioletred;
            }
        }

        .aaa {
            padding: 0rpx;
            background-color: #fff;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }

    }

}
</style>