<template>
  <view>
    <!-- 客户-->
    <view style="
        padding: 0 32rpx 32rpx;
        width: 100%;
        background-color: #fff;
      ">
      <view class="search-box" @click="goSearchPage">
        <myIcon class="icon" type="ic_sousuo3x1" color="#bbb3b3" size="32rpx"></myIcon>
        <text style="text-wrap: nowrap;overflow-x: auto;padding-left: 16rpx;">如: 部门 / 成员 / 手机号 / 客户编号等</text>
      </view>

      <!-- 我的客户公海客户的统计 -->
      <view class="rectangle_image">
        <view style="width: 50%; height:168rpx ;margin-top: 20rpx;" class="rectangle_image_left">
          <view class="rectangle_text">
            <view class="rectangle_text_header">
              <view style="margin-right: 16rpx;">我的客户</view>
              <view style="margin-right: 8rpx;" v-if="getcardLists.my_client_day_num > 0 ? true : false">
                +{{ getcardLists.my_client_day_num }}</view>
              <view v-if="getcardLists.my_client_day_num > 0 ? true : false">
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path d="M7.5 6V10H4.5V6H2L6 2L10 6H7.5Z" fill="white" />
                </svg>
              </view>
            </view>
            <view class="rectangle_text_center" @click="jumpBtnwork">{{ getcardLists.my_client_num }}</view>
          </view>
        </view>
        <!-- 公海客户 -->
        <view style="width: 50%; height:168rpx ;" class="rectangle_image_right">
      <view class="rectangle_texts">
        <view class="rectangle_text_header">
          <view v-if="getcardLists.public_client_day_num > 0 ? true : false">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
              <path d="M7.5 6V10H4.5V6H2L6 2L10 6H7.5Z" fill="white" />
            </svg>
          </view>
          <view style="margin-right: 8rpx;" v-if="getcardLists.public_client_day_num > 0 ? true : false">
            +{{ getcardLists.public_client_day_num }}</view>
          <view>公海客户</view>
        </view>
        <view class="rectangle_text_center" @click="jumpBtnall">{{ getcardLists.public_client_num }}</view>
      </view>
        </view>
        <view class="huojian"></view>
      </view>



      <view style="
          display: flex;
          flex-direction: row;
          margin-top: 40rpx;
        ">
        <!-- tabs切换 -->
        <view v-for="(item, index) in myhouseList" :key="item.id" @tap="changeAct(item)" class="my_style_all">
          <view class="my-style-one" :class="{ 'active': act === item.id }">
            {{ item.name }}
          </view>
        </view>
        <!-- 本周 -->
        <view class="data_all" @click="weekerBtn">
          <view>
            <image src="@/static/icon/index/data.png" style="width:32rpx;height:32rpx"></image>
          </view>
          <view class="weeker_data">
            {{ weekrName }}
          </view>
          <view>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path
                d="M3.52864 5.52864C3.78899 5.26829 4.2111 5.26829 4.47145 5.52864L8.00004 9.05723L11.5286 5.52864C11.789 5.26829 12.2111 5.26829 12.4714 5.52864C12.7318 5.78899 12.7318 6.2111 12.4714 6.47145L8.47145 10.4714C8.2111 10.7318 7.78899 10.7318 7.52864 10.4714L3.52864 6.47145C3.26829 6.2111 3.26829 5.78899 3.52864 5.52864Z"
                fill="#488AF6" />
            </svg>
          </view>
        </view>
      </view>
      <!-- 列表 -->
      <view>
        <view class="mywork_box" v-for="item in houseLists" :key="item.id" @click="itemFn(item.location)">
          <view style="display: flex; flex-direction: row; align-items: center">
            <view>
              <image :src="item.icon" style="width: 100rpx; height: 100rpx"></image>
            </view>
            <view style="margin-left: 24rpx">{{ item.name }}</view>
          </view>
          <view style="display: flex; flex-direction: row">
            <view>{{ item.number }}</view>
            <view>
              <image src="../../../static/icon/index/箭头 .png" style="width: 32rpx; height: 32rpx"></image>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-if="searchshow">
      <view>
        <image src="../../../static/icon/index/Polygon 1.png" class="search_box_image"></image>
      </view>
      <view class="search_box">
        <view v-for="(item, index) in range" :key="item.value" class="search_text"
          :class="{ 'activee': item.value == value }" @tap="textFn(item.value, item.text)">{{ item.text }}</view>
      </view>
    </view>
    <!-- 本周下拉框 -->
    <!-- v-if="weekerShow" -->
    <view class="zhezao" v-if="weekerShow">
      <view class="popbox">
        <view class="popbox_item" v-for="(item, index ) in navs" :key="item.id"
          :class="{ 'popbox_item_active': navIndex == item.id }" @click="checkIndex(index, item.name)">{{ item.name }}</view>
      </view>
    </view>
  </view>
</template>
<script>
import getDate from "./getDate .js";
import myIcon from "@/components/my-icon";
export default {
  components: {
    myIcon
  },
  props: {
    myhouseList: { Array },
  },
  data() {
    return {
      searchshow: false,
      navIndex: 2,
      weekrName: '今天',
      weekerShow: false,// 控制本周的气泡框
      act: 2,
      navs: [
        { id: 1, name: '全部', date_type: '' },
        { id: 2, name: "今天", date_type: 1 },
        { id: 3, name: "昨天", date_type: 2 },
        { id: 4, name: "本周", date_type: 3 },
        { id: 5, name: "上周", date_type: 4 },
        { id: 6, name: "本月", date_type: 5 },
        { id: 7, name: "上月", date_type: 6 },
      ],
      houseLists: {},
      // 列表
      dataList: {
        parent_id: "",
        start_date: "",
        end_date: "",
        mobile: "1",
        keywords: "",
      },
      myList: {},
      value: "0",
      mobiles: '',
      keywords: '',
      getcardLists: {},// 卡片
      range: [
        { value: 0, text: "电话", name: 'mobile' },
        { value: 1, text: "姓名", name: 'cname' },
        { value: 2, text: "线索", name: 'keywords' },
        { value: 3, text: "编号", name: 'number' },
      ],
    };
  },
  created() {
    uni.showLoading({
      title: "加载中",
    });
    this.getcardList()
  },
  watch: {
    keywords: {
      handler(naval) {
      //  uni.setStorageSync('keywords', naval);
      },
      immediate: true,
    },
    mobiles: {
      handler(naval) {
        if (naval == '') {
          naval = '电话'
        }
        uni.setStorageSync('mobiles', naval);
      },
      immediate: true,
    },
    act: {
      handler(naval) {
        console.log(naval, '000000');
        // 昨天
        // 昨天开始时间
        let endtime = getDate.getToday().endtime;
        //昨天结束时间
        let starttime = getDate.getToday().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        this.getUserHouse();
      },
      immediate: true,
    },
  },
  methods: {
    // 点击遮罩关闭弹框
    zhezaofn() {
      this.weekerShow = !this.weekerShow
    },
    // 点击我的客户卡片跳转我的客户列表
    jumpBtnwork() {
      this.$navigateTo("/customer/my_list");
    },
    // 点击公海客户卡片跳转公海客户列表
    jumpBtnall() {
      this.$navigateTo("/customer/seas_list");
    },
    deldate() {
      this.mobiles = ''
      this.keywords = ''
    },
    keywordsBtn() {
      if (this.keywords != '') {
        let item = this.range.find(e => e.value == this.value),
            searchType = item ? item.name : 'mobile';
        this.$navigateTo('/customer/search?'+searchType+'='+this.keywords)
      }
    },
    goSearchPage(){
      this.$navigateTo('/customer/search')
    },
    inputFn() {
      this.searchshow = !this.searchshow
    },
    textFn(item, text) {
      this.searchshow = !this.searchshow
      this.value = item
      this.mobiles = text
      //uni.setStorageSync('mobiles', this.mobiles);
      if (text != '') {
        this.keywords = ''
      }
    },
    change(e) {
      console.log("e:", e);
    },
    // tab互斥效果
    changeAct(item) {
      // console.log(item.id);
      // 激活样式是当前点击的对应下标--list中对应id
      this.act = item.id;
      if (this.act == item.id) {
        this.weekrName = '今天'
        this.navIndex = 2
      }
    },
    // 控制本周气泡框
    weekerBtn() {
      this.weekerShow = !this.weekerShow
    },
    checkIndex(index, name) {
      this.navIndex = index + 1;
      this.weekrName = name
      this.weekerShow = !this.weekerShow


      if (index === 0) {
        this.dataList.start_date = "";
        this.dataList.end_date = "";
        this.getUserHouse();
      } else if (index === 1) {
        // 今天
        // 今天开始时间
        let endtime = getDate.getToday().endtime;
        //今天结束时间
        let starttime = getDate.getToday().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        // console.log(this.dataList.start_date, '昨天开始时间');
        this.getUserHouse();
      } else if (index === 2) {
        // 昨天
        // 昨天开始时间
        let endtime = getDate.getYesterday().endtime;
        //昨天结束时间
        let starttime = getDate.getYesterday().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        // console.log(this.dataList.start_date, '昨天开始时间');
        this.getUserHouse();
      } else if (index === 3) {
        // 本周
        // 本周开始时间
        let endtime = getDate.getCurrWeekDays().endtime;
        //本周结束时间
        let starttime = getDate.getCurrWeekDays().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        // console.log(this.dataList.start_date, '本周开始时间');
        this.getUserHouse();
      } else if (index === 4) {
        // 上周
        // 上周开始时间
        let endtime = getDate.getLastWeekDays().endtime;
        //上周结束时间
        let starttime = getDate.getLastWeekDays().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        // console.log(this.dataList.start_date, '上周开始时间');
        this.getUserHouse();
      } else if (index === 5) {
        // 本月
        // 本月开始时间
        let endtime = getDate.getCurrMonthDays().endtime;
        //本月结束时间
        let starttime = getDate.getCurrMonthDays().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        // console.log(this.dataList.start_date, '本月开始时间');
        this.getUserHouse();
      } else if (index === 6) {
        // 上月
        // 上月开始时间
        let endtime = getDate.getLastMonthDays().endtime;
        //上月结束时间
        let starttime = getDate.getLastMonthDays().starttime;
        this.dataList.start_date = starttime;
        this.dataList.end_date = endtime;
        console.log(this.dataList.start_date, "上月开始时间");
        this.getUserHouse();
      }
    },
    scroll: function (e) {
      // console.log(e)
      // this.old.scrollTop = e.detail.scrollTop
    },
    // onConfirm() {
    //     this.getUserHouse();
    // },
    // 我的客户
    getUserHouse() {
      this.dataList.parent_id = this.act
      let params = Object.assign({}, this.dataList);
      if (!params.start_date) {
        delete params.start_date;
      }
      if (!params.end_date) {
        delete params.end_date;
      }

      this.$ajax.get(
        `/qywx/welcome/get_fixed_menu`,
        params,
        (res) => {
          console.log(res.data, "11");
          if (res.statusCode === 200) {
            this.houseLists = res.data;
            uni.hideLoading();
          } else {
            uni.hideLoading();
          }
        },
        () => {
          uni.hideLoading();
        }
      );
    },
    // 卡片
    getcardList() {
      this.$ajax.get(
        `/qywx/welcome/my_census`,
        {},
        (res) => {
          console.log(res.data, "112222");
          if (res.statusCode === 200) {
            this.getcardLists = res.data
            uni.hideLoading();
          } else {
            uni.hideLoading();
          }
        },
        () => {
          uni.hideLoading();
        }
      );
    },
    // 点击跳转·1
    locationFn(item) {
      console.log(111);
      this.$navigateTo(item);
    },
    itemFn(item) {
      let curNav = this.navs.find(e => e.id === this.navIndex),
          dateType = curNav ? curNav.date_type : '';
      if(dateType){
        item += (item.includes('?') ? '&' : '?') + 'date_type=' + dateType
      }
      this.$navigateTo(item);
    },
  },
};
</script>
<style lang="scss" scoped>
.search-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 80rpx;
  border-radius: 16rpx;
  padding: 0 24rpx;
  background-color: #f3f3f3;

  text {
    color: rgba(41, 44, 57, 0.4);
    margin-left: 3px;
  }
}

.zhezao {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  // z-index: 777;
}

.popbox {
  position: absolute;
  top: 720rpx;
  left: 466rpx;
  background: #fff;
  width: 260rpx;
  // padding: 0 32rpx;
  border-radius: 16rpx;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.05);
}

.popbox_item {
  padding: 24rpx 0;
  background: #ffff;
  text-align: center;
  // border-bottom: 2rpx solid #F6F6F6;
}

.popbox_item_active {
  background: #e4e3e3;
}

.mywork_box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #F6F6F6;

  &.mywork_box:last-child {
    border: none;
  }
}

.weeker_data {
  margin: 0 10rpx;
  color: #488AF6;
}

.data_all {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  // margin-left: 150rpx;
  width: 50%;
  position: relative;
}

.my_style_all {
  text-align: center;
  line-height: 64rpx;
  padding: 8rpx;
  border-radius: 8rpx;
  background: #F6F6F6;
  font-size: 28rpx;
  color: rgba(41, 44, 57, 0.40);
}

.rectangle_text_center {
  color: #FFF;
  font-family: PingFang SC;
  font-size: 48rpx;
  margin-top: 36rpx;
}
.huojian{
  background-image: url('@/static/icon/index/huojian.png');
  background-size: cover;
  position: absolute;
  top: 42rpx;
  left: 294rpx;
}
.rectangle_image_left {
  background-image: url('https://img.tfcs.cn/icon1/Rectangle4952.png');
  background-size: cover
}
.rectangle_image_right {
  background-image: url('https://img.tfcs.cn/icon1/Rectangle4951.png');
  background-size: cover
}

.rectangle_text {
  width: 352rpx;
  height: 168rpx;
  padding-top: 32rpx;
  padding-left: 32rpx;
}

.rectangle_texts {
  width: 352rpx;
  height: 168rpx;
  padding-top: 32rpx;
  padding-right: 40rpx;
  align-items: flex-end
}

.rectangle_text_header {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  color: #FFF;
  font-size: 28rpx;
}

.huojian {
  width: 104rpx;
  height: 104rpx;
}



.rectangle_image {
  position: relative;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  margin-top: 40rpx;
  width: 100%;
  // text-align: center;
}

.activee {
  background: rgba(0, 0, 0, 0.03);
}

.search_box_image {
  width: 36rpx;
  height: 30rpx;
  position: absolute;
  top: 366rpx;
  left: 150rpx;
}

.search_text {
  padding: 20rpx 32rpx;
  width: 100%
}

.search_box {
  position: absolute;
  top: 372rpx;
  left: 54rpx;
  background: #fff;
  width: 180rpx;
  margin-top: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0px 0px 40rpx 0px rgba(0, 0, 0, 0.10);
}

.whole_shu {
  color: rgba(41, 44, 57, 0.40);
  font-size: 24rpx;
  font-weight: 400;
  line-height: 28rpx;

}

.whole_bai {
  width: 70%;
  height: 32rpx;
  line-height: 32rpx;
  color: rgba(41, 44, 57, 0.40);
}

.whole_input_left_name {
  color: rgba(41, 44, 57, 0.70);
  font-size: 28rpx;
  font-weight: 400;
  line-height: 32rpx;
  margin-right: 16rpx;
}

.whole_input_left {
  display: flex;
  flex-direction: row;

}

.whole_input {
  position: relative;
  overflow: auto;
  width: 100%;
  // height: 80rpx;
  padding: 24rpx;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  // margin: 0 16rpx;
  background: #F6F6F6;
  border-radius: 16rpx;
}

.my-style-one {
  box-sizing: border-box;
  // padding: 16rpx 20rpx;
  width: 160rpx;
  height: 64rpx;
  text-align: center;
  background: #F6F6F6;
  font-size: 28rpx;
  border-radius: 8rpx;
  border: none;

}

// 我的房客
.active {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-content: center;
  box-sizing: border-box;
  // padding: 16rpx 20rpx;
  height: 64rpx;
  color: #488AF6;
  font-size: 28rpx;
  border-radius: 8rpx;
  background: #FFF;
  border: none;
}

// 本周
.weeked {
  box-sizing: border-box;
  // padding: 6rpx 24rpx;
  width: 96rpx;
  height: 48rpx;
  border-radius: 8rpx;
  margin: 0 6rpx;
  font-size: 24rpx;
  line-height: 48rpx;
  background: #f8f8f8;
  display: inline-block;
  /* 必要，导航栏才能横向*/
}

.activite {
  box-sizing: border-box;
  // padding: 6rpx 24rpx;
  width: 96rpx;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  background: rgba(72, 138, 246, 0.2);
}

::v-deep .uni-input-placeholder {
  top: -6rpx !important;
}

::v-deep .uni-input-input {
  top: -6rpx !important;
}

// 下拉
.updata {
  width: 160rpx;
  background-color: #fff;
}</style>
