<template>
  <view class="subscribe-list">
    <view class="top-search">
      <view class="search-input row ">
        <view class="search-name" @click="showAction">
          {{ search_name }}
        </view>
        <myIcon type="xiala" size="24rpx"></myIcon>
        <input
          type="text"
          placeholder-style="font-size:28rpx"
          placeholder="请选择搜索类型并输入内容"
          v-model="model_value"
          @confirm="onConfirm"
        />
      </view>
    </view>
    <!-- 内容列表 -->
    <block v-for="(item, index) in project_list" :key="index">
      <view class="box-list">
        <view class="title">认购订单：{{ item.sale_order_sn }}</view>
        <view class="line row">
          <view class="label">认购项目</view>
          <view class="content">{{ item.project_name }}</view>
        </view>
        <view class="line row">
          <view class="label">认购客户</view>
          <view class="content">{{ item.customer_name }}</view>
          <view class="label">{{ item.customer_phone }}</view>
        </view>
        <view class="line row">
          <view class="label">认购日期</view>
          <view class="content">{{ item.project_name }}</view>
        </view>
        <view class="line row">
          <view class="label">成交日期</view>
          <view class="content">{{ item.deal_at }}</view>
        </view>
        <view class="line row">
          <view class="label">业绩归属</view>
          <view class="content">{{
            item.su_name || item.su_nickname || item.su_user_name
          }}</view>
          <view class="label">{{ item.su_phone }}</view>
        </view>
        <view class="line row">
          <view class="label">收入确认</view>
          <view
            class="content"
            :class="{
              success: item.confirm_status == 1,
              warning: item.confirm_status == 0,
              danger: item.confirm_status == 2,
            }"
            >{{ changeValue(item.confirm_status) }}</view
          >
        </view>
        <view class="line row">
          <view class="label" style="width:112rpx">客户会员服务费</view>
          <view class="content"
            >应收{{ item.member_service_charge }}元，已收{{
              item.earning_member_service_charge
            }}元</view
          >
        </view>
        <view class="line row">
          <view class="label" style="width:112rpx">开发商佣金</view>
          <view class="content"
            >应收{{ item.brokerage_amount }}元，已收{{
              item.earning_brokerage_amount
            }}元</view
          >
        </view>
      </view>
    </block>
    <load-more :status="load_status"></load-more>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore";
import myIcon from "@/components/my-icon";
export default {
  components: { loadMore, myIcon },
  data() {
    return {
      project_list: [],
      params: {
        page: 1,
        company_id: "",
      },
      load_status: "",
      dictionary_list: [],
      search_value: ["客户姓名", "客户电话", "经纪人电话", "订单编号"],
      search_name: "客户姓名",
      model_value: "",
    };
  },
  onLoad(options) {
    this.params.company_id = options.company_id;
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "SALE_ORDER_CONFIRM_STATUS_CATEGORY":
            this.dictionary_list = item.childs;
            break;
        }
      });
    });
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.project_list = [];
      }
      this.$ajax.get(`/client/sale_order/search`, this.params, (res) => {
        if (res.statusCode === 200) {
          this.load_status = "loadend";
          this.project_list = this.project_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        } else {
          this.load_status = "loadend";
          uni.showToast({
            title: res.data.message || "获取列表数据失败",
            icon: "none",
          });
        }
      });
    },
    changeValue(value) {
      var desc = this.dictionary_list.find((item) => {
        if (item.value == value) {
          return item.description;
        }
      });
      return desc.description;
    },
    showAction() {
      uni.showActionSheet({
        itemList: this.search_value,
        success: (res) => {
          this.search_name = this.search_value[res.tapIndex];
          this.model_value = "";
        },
        fail: (res) => {
          console.log(res.errMsg);
        },
      });
    },
    onConfirm(e) {
      switch (this.search_name) {
        case "客户姓名":
          this.params.page = 1;
          this.params.customer_name = e.detail.value;
          break;
        case "客户电话":
          this.params.page = 1;
          this.params.customer_phone = e.detail.value;
          break;
        case "经纪人电话":
          this.params.page = 1;
          this.params.broker_user_phone = e.detail.value;
          break;
        case "订单编号":
          this.params.page = 1;
          this.params.sale_order_sn = e.detail.value;
          break;
      }
      this.getDataList();
      this.params = {
        page: 1,
        company_id: this.params.company_id,
      };
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.getDataList();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>

<style scoped lang="scss">
page {
  background: #eee;
}
.subscribe-list {
  .top-search {
    padding: 24rpx;
    background: #fff;
    .search-input {
      background: #eee;
      padding: 12rpx 24rpx;
      border-radius: 8rpx;
      align-items: center;
      .search-name {
        margin-right: 12rpx;
      }
      input {
        margin-left: 24rpx;
      }
    }
  }
  .box-list {
    margin-top: 24rpx;
    background: #fff;
    padding: 24rpx;
    .title {
      font-size: 28rpx;
      font-weight: 600;
    }
    .line {
      margin-top: 24rpx;
      .label {
        color: #999;
      }
      .content {
        margin: 0 20rpx;
      }
      .success {
        color: #67c23a;
      }
      .warning {
        color: #e6a23c;
      }
      .danger {
        color: #f56c6c;
      }
    }
  }
}
</style>
