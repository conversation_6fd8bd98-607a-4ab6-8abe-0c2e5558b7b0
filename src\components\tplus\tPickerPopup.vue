<template>
<myPopup :show="show"  @close="cancle">
    <view class="filter-wrapper">
        <view class="header" :class="{'no-filter':!filterable}">
            <view class="action cancle" @click.stop="cancle">取消</view>
            <view class="title">{{placeholder}}</view>
            <view class="action confirm" :class="{loading: submiting}" @click.stop="confirm">确认</view>
        </view>
        <view class="body">
            <tCustomPickerView v-model="selectedId" :datas="memberList" ref="picker"  v-if="show" :multiple="multiple"
                :filter-placeholder="filterPlaceholder"
                :filterable="filterable" :map="map" :loading="loading">
            </tCustomPickerView>
        </view>
    </view>
</myPopup>
</template>

<script>
import myPopup from '@/components/myPopup';
import tCustomPickerView from '@/components/tplus/tCustomPickerView';
export default {
    components: {
        myPopup, tCustomPickerView
    },
    props: {
        value: { type: [ Number, String, Array ], default:'' },
        visible: { type: Boolean, default: false },
        multiple: { type: Boolean, default: false },
        placeholder: { type: String, default: '' },
        filterPlaceholder: { type: String, default: '' },
        submiting: { type: Boolean, default: false },
        filterable: { type: Boolean, default: false },
        filterId: { type: [Number, Array], default: 0 },
        api: { type: Function, default(){} },
        map: { type: Object, default: ()=>{ return {children: 'children', value: 'value', label: 'label'}}},
    },
    data() {
        return {
            loading: false,
            show: false,
            isInited: false,
            curValue: '',
            list: [],
        }
    },
    computed: {
        memberList(){
            if(filterIds === 0){
                return this.list;
            }
            const filterIds = Array.isArray(this.filterId) ? this.filterId : [this.filterId];
            return this.list.filter(item => !filterIds.includes(item.id));
        },
        selectedId: {
            get() {
                if(this.multiple){
                    return this.curValue || [];    
                }
                return this.curValue || '';
            },
            set(val) {
                console.log(val);
                this.curValue = val;
            }
        }
    },
    watch: {
        value: {
            handler(val){
                this.selectedId = val
            },
            immediate: true
        },
        visible(val){
            this.show = val;
            if(val && !this.isInited){
                this.isInited = true;
                this.getList();
            }
        },
        show(val){
            this.$emit('update:visible', val)
        }
    },
    created() {
        
    },
    methods: {
        async getList(){
            this.loading = true;
            this.list =  await this.api().catch(e=>{}) || [];
            this.loading = false;
        },
        cancle(){
            this.show = false;
            this.selectedId = this.value;
        },
        confirm(){
            if(this.submiting){
                return;
            }
            this.$emit('input', this.selectedId);
    
            this.$emit('confirm', this.$refs.picker.getCheckedItem());
        },
    }
}

</script>

<style scoped lang="scss"> 
.filter-wrapper{
    background-color: #fff;
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
    overflow: hidden;
}
.header{
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 46px;
    
    &.no-filter{
        height: 50px;
        border-bottom: 1px solid #f9f9f9;
    }
    .title{
        flex: 1;
        color: #999;
        text-align: center;
        display: inline-block;
        max-width: 50%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .action{
        padding: 10px 16px;
        font-size: 17px;
        &.cancle{
            color: #888;
        }
        &.confirm{
            position: relative;
            color: #007aff;
            &.loading{
                opacity: .6;
            }
        }
    }
}
</style>