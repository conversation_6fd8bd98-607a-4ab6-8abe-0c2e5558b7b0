<template>
  <view class="picker-box">
    <view class="selecter" @click="showPicker">
      <slot>
        <view class="selecter-info">
          <view class="contents" v-if="all_selected && all_selected.length > 0">
            <text class="selecter_content" v-for="(item, index) in all_selected" :key="index">{{
              item | formatSelectedOk
            }}</text>
          </view>
          <view class="contents" v-else>
            <text class="selecter_placeholder">请选择</text>
          </view>
        </view>
      </slot>
    </view>
    <my-popup ref="picker" :show="show" @close="cancelPickerSel">
      <view class="my-picker" v-if="options.length > 0">
        <view class="operation flex-row">
          <view class="tip">请选择{{ options[current].label }}</view>
          <view class="btn" @click="cancelPickerSel">取消</view>
        </view>
        <scroll-view scroll-y class="content">
          <slot name="content">
            <template v-for="it in options">
              <view class="item_info" v-for="(items, idx) in it.items" :key="idx">
                <view class="title">{{ items.title }}</view>
                <view class="item">
                  <view
                    class="item_item flex-row"
                    v-for="(item, index) in items.range"
                    :key="index"
                    :class="{
                      active: item.isChecked,
                    }"
                    @click.stop.prevent="onClickSelect(item, idx, index)"
                  >
                    <text class="text"> {{ item.name }}</text>
                  </view>
                </view>
              </view>
              <!-- <text class="text">{{ item[label_name] }}</text> -->
            </template>
            <view class="submit">
              <my-button @click="handleSelectted" :block="true" size="big">确定</my-button>
            </view>
          </slot>
        </scroll-view>
      </view>
    </my-popup>
  </view>
</template>

<script>
import myPopup from './myPopup'
import myButton from './myButton'
export default {
  components: {
    myPopup,
    myButton,
  },
  data() {
    return {
      show: false,
      all_selected: [],
      current: 0,
      current_value: [],
      options: [],
    }
  },
  watch: {
    option: {
      deep: true,
      immediate: true,
      handler(val) {
        this.options = Object.assign([],val)
        // if (this.watch_once) {
        //   return
        // }
        this.initValue(() => {
          console.log(this.options);
          this.watch_once = true
        })
      },
    },
  },
  props: {
    option: {
      type: Array,
      default: () => [],
    },
  },
  filters: {
    formatSelected(option) {
      let value = option.value
        .map((item, index) => {
          var _current = option.items[index].range.find((_item) => _item.value === item)
          if (_current) {
            return _current.name
          }
        })
        .join('')
      if (value) {
        return value
      } else {
        return '请选择'
      }
    },
    formatSelectedOk(selected) {
      if (!selected) {
        return ''
      }
      const res = selected.name
      return res || ''
    },
  },
  created() {
    this.initValue()
  },
  methods: {
    initValue(callback) {
      this.option.forEach((item, index) => {
        if (item.value.length > 0) {
          this.options[index].selected = this.getSelected(item)
        }
      })
      this.all_selected = this.getAllselected()
      callback && callback()
    },
    getAllselected() {
      return this.options.filter((item) => item.selected).map((item) => item.selected)[0]
    },
    cancelPickerSel() {
      this.initValue(() => {
        this.show = false
      })
    },
    showPicker() {
      this.show = true
      console.log(this.options, 22)
    },
    // 获取某项选中的值
    getSelected(e) {
      let selected = e.items.map((item, idx) => {
        let select_item = e.items[idx].range.find((_item) => _item.isChecked === true)
        if (select_item) {
          select_item.identifier = e.items[idx].identifier
          return select_item
        } else {
          return {}
        }
      })

      // selected = selected.filter((item) => item.value)
      // console.log(selected, 'sel')
      return selected
    },
    onClickSelect(e, idx, index) {
      this.options[0].items[idx].range.map((item) => {
        item.isChecked = false
        return item
      })
      e.isChecked = !e.isChecked
      this.$set(this.options[0].items[idx].range[index],'isChecked',e.isChecked)
      //this.$forceUpdate()
      console.log(this.option)
    },
    /**
     * 点击确定
     */
    handleSelectted() {
      this.all_selected = this.getSelected(this.options[0])
      const noValIndex = this.all_selected.findIndex((item) => !item.value)
      const valArr = ['室', '厅', '卫']
      if (noValIndex >= 0 && valArr[noValIndex]) {
        uni.showToast({
          title: '请选择' + valArr[noValIndex],
          icon: 'none',
        })
        // this.all_selected = []
        return
      }
      // this.all_selected = []
      this.show = false
      this.$emit('input', this.getSelected(this.options[0]))
      this.$emit('change', this.getSelected(this.options[0]))
    },
  },
}
</script>

<style scoped lang="scss">
.picker-box {
  background-color: #fff;
}

.selecter {
  width: 100%;
  justify-content: space-between;
  // align-items: center;
  .contents {
    flex-direction: row;
    .selecter_placeholder {
      line-height: 1;
      // padding: 0 10rpx;
      font-size: 36rpx;
      // font-weight: bold;
      color: #8a929f;
    }
    .selecter_content {
      line-height: 1;
      padding-right: 16rpx;
      font-size: 36rpx;
      // font-weight: bold;
      color: #333;
      ~ .selecter_content {
        // padding-left: 16rpx;
        // border-left: 4rpx solid #333;
      }
    }
  }
}

.my-picker {
  background-color: #fff;
  .content {
    box-sizing: border-box;
    max-height: 60vh;
    padding: 0 48rpx;
  }
  .item_info {
    // padding: 20rpx 0;
    .title {
      color: #8a929f;
      padding: 20rpx 0;
    }
  }
  .item {
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;

    // padding: 24rpx 0;
    .item_item:nth-child(4n) {
      margin-right: 0;
    }
    .item_item {
      padding: 20rpx 0;
      margin-right: 24rpx;
      width: calc((100% - 72rpx) / 4);
      text-align: center;
      justify-content: center;
      font-size: 22rpx;
      color: #8a929f;
      background: #f8f8f8;
      border-radius: 8rpx;
      // margin-right: 3.2%;
      margin-bottom: 18rpx;

      &.active {
        color: #2d84fb;
      }
    }
  }
}
.label-list {
  .label {
    flex: 1;
    text-align: center;
    padding: 20rpx;
    border-top: 4rpx solid #fff;
    .label_text {
      font-size: 22rpx;
      margin-bottom: 6rpx;
      color: #666;
    }
    &.active {
      border-top: 4rpx solid #2d84fb;
      background-image: linear-gradient(0deg, rgba(246, 246, 246, 0) 0%, rgba(#2d84fb, 0.1) 100%);
      .value_name {
        color: #2d84fb;
      }
    }
  }
}

.operation {
  padding: 24rpx 32rpx;
  align-items: center;
  background-color: #f5f5f5;
  .tip {
    flex: 1;
    text-align: center;
    font-size: 22rpx;
    color: #999;
  }
  .btn {
    color: #2d84fb;
  }
}
picker-view {
  height: 400rpx;
}
.picker-view-column-item {
  line-height: 70rpx;
  text-align: center;
}
.submit {
  margin: 48rpx;
}
</style>
