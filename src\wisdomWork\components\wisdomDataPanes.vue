<template>
    <view class="panes">
        <view class="panes-item" v-for="(item,index) in data" :key="index">
            <view class="panes-item-inner" :style="{background: item.bg}">
                <view class="panes-item-bg"></view>
                <view class="panes-item-label">{{ item.label }}</view>
                <view class="panes-item-value">{{ item.value }}</view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: {
        data: { type: Array, default: ()=>[] }
    }
}
</script>

<style lang="scss" scoped>
.panes{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    .panes-item{
        width: 50%;
        padding-bottom: 24rpx;
        
        &:nth-child(odd){
            padding-right: 12rpx;
        }
        &:nth-child(even){
            padding-left: 12rpx;
        }
        .panes-item-inner{
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 188rpx;
            padding: 32rpx;
            border-radius: 16rpx;
            background: linear-gradient(180deg, #48EEE8 0%, #37A5D4 100%);
            .panes-item-bg{
                position: absolute;
                top: 0;
                right: 0;
                width: 188rpx;
                height: 100%;
                background-image: url('https://img.tfcs.cn/backup/icons/wisdom-work/data-panes-bg.png');
                background-size: cover;
            }
            .panes-item-label{
                color: #FFFFFF;
                font-size: 24rpx;
                font-weight: 400;
            }
            .panes-item-value{
                color: #FFFFFF;
                font-size: 48rpx;
                padding-bottom: 11rpx;
            }
        }
    } 
}
</style>