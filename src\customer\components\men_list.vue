<template>
  <view class="menlist">
    <block v-for="item in arr" :key="item.id">
      <view class="menitem">
        <view class="men-title row">
          <view class="l-title">
            {{ item.type_name }}
          </view>
          <view class="r-time">
            {{ item.created_at }}
          </view>
        </view>
        <view class="men-con row">
          <text class="l">成交类型：</text>
          <text class="r">{{ item.tracking_name }}</text>
        </view>
        <view class="men-con row">
          <text class="l">客户名称：</text>
          <text class="r">{{ item.cname }}</text>
        </view>
        <view class="men-con row">
          <text class="l">事项：</text>
          <text class="r">{{ item.remark }}</text>
        </view>
        <view class="men-btm row">
          <view class="btm-l row">
            <image :src="item.img"></image>
            <text>由{{ item.user_name }}提交</text>
          </view>
          <view :class="'btm-r' + item.type" class="btm-r">
            {{ item.type | filterType }}
          </view>
        </view>
      </view>
    </block>
  </view>
</template>

<script>
export default {
  data() {
    return {};
  },
  filters: {
    filterType(e) {
      let arr = [
        { id: 0, name: "待同意" },
        { id: 1, name: "已同意" },
        { id: 2, name: "审批中" },
        { id: 3, name: "已拒绝" },
      ];
      const form = arr.find((item) => {
        return item.id == e;
      });
      return form.name;
    },
  },
  props: {
    arr: Array,
  },
};
</script>

<style scoped lang="scss">
.menlist {
  .menitem {
    background: #ffffff;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 12px;
    .men-title {
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      .l-title {
        font-size: 16px;
        color: #2e3c4e;
      }
      .r-time {
        font-size: 11px;
        color: #8a929f;
      }
    }
    .men-con {
      font-size: 12px;
      line-height: 20px;
      color: #8a929f;
      .l {
        width: 60px;
      }
      .r {
        flex: 1;
      }
    }
    .men-btm {
      align-items: center;
      justify-content: space-between;
      .btm-l {
        align-items: center;
        image {
          width: 24px;
          height: 24px;
          border-radius: 50%;
        }
        text {
          font-size: 11px;
          color: #8a929f;
          margin-left: 8px;
        }
      }
      .btm-r {
        padding: 4px 17px;
        font-size: 11px;
        border-radius: 4px;
        &.btm-r0 {
          background: rgba(45, 132, 251, 0.15);
          color: #2d84fb;
        }
        &.btm-r1 {
          background: rgba(13, 218, 105, 0.15);
          color: #0cd967;
        }
        &.btm-r2 {
          background: rgba(245, 111, 107, 0.15);
          color: #f56f6b;
        }
        &.btm-r3 {
          background: rgba(167, 201, 237, 0.15);
          color: #a7c9ed;
        }
      }
    }
  }
}
</style>
