<template>
  <view class="login">
    <view class="title">欢迎登录</view>
    <view class="login-box">
      <view class="login-item row">
        <input
          type="text"
          placeholder="请输入手机号"
          placeholder-class="pla-css"
          v-model="form_info.mobile"
        />
        <view
          v-if="form_info.mobile"
          @click="form_info.mobile = ''"
          class="right"
        >
          <image
            class="close"
            src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5index/login/close.png"
          ></image>
        </view>
      </view>
      <view class="login-item row">
        <input
          v-if="isPassword"
          type="password"
          placeholder="请输入密码"
          placeholder-class="pla-css"
          v-model="form_info.password"
        />
        <input
          v-else
          type="text"
          placeholder="请输入密码"
          placeholder-class="pla-css"
          v-model="form_info.password"
        />
        <view class="right row">
          <image
            v-if="form_info.password"
            @click="form_info.password = ''"
            class="close"
            src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5index/login/close.png"
          ></image>
          <image
            class="yan"
            @click="isPassword = !isPassword"
            :src="
              `https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5index/login/${
                isPassword ? 'by' : 'zy'
              }.png`
            "
          ></image>
        </view>
      </view>
    </view>
    <view class="login-desc row">
      登录即表示接受 <text>《用户协议》</text>
    </view>
    <view class="login-btn">登录</view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form_info: {
        mobile: "",
        password: "",
      },
      isPassword: true,
    };
  },
};
</script>

<style scoped lang="scss">
.login {
  padding: 30px;
  background: #fff;
  .title {
    font-size: 28px;
  }
  .login-box {
    margin-top: 70px;
    .login-item {
      border-bottom: 0.5px solid #d3d3d3;
      margin-bottom: 20px;
      padding: 10px 0;
      justify-content: space-between;
      .pla-css {
        font-size: 20px;
        color: #cbcdcf;
      }
      input {
        border: none;
      }
      .right {
        align-items: center;
        margin-right: 10px;
        .close {
          width: 20px;
          height: 20px;
        }
        .yan {
          margin-left: 30px;
          width: 18px;
          height: 10px;
        }
      }
    }
  }
  .login-desc {
    font-size: 12px;
    color: #7c889b;
    text {
      color: #3355ff;
    }
  }
  .login-btn {
    color: #fff;
    height: 50px;
    line-height: 50px;
    background: #3355ff;
    text-align: center;
    margin-top: 60px;
  }
}
</style>
