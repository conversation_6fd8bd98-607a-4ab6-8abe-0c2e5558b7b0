<template>
    <view class="container">
        <view class="bg"></view>
        <view class="body">
            <slot></slot>
        </view>
    </view>
</template>
<script>
export default {
    
}
</script>
<style lang="scss" scoped>
.container{
    min-height: 100vh;
    background: #F7F8FA;
    position: relative;
    .bg{
        position: absolute;
        z-index: 1;
        width: 100%;
        height: 360rpx;
        background: linear-gradient(180deg, rgba(99, 165, 255, 0.4) 0%, #F7F8FA 100%);
        top: -120rpx;
    }
    .body{
        position: relative;
        z-index: 2;
        padding: 24rpx 32rpx 72rpx;
    }
}
</style>