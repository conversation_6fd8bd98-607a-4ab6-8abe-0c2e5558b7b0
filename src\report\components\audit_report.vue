<template>
<myPopup :show="show"  @close="show = false">
    <view class="container">
        <view class="header">
            <view class="action cancle" @click.stop="cancle">取消</view>
            <view class="title">审核状态</view>
            <view class="action confirm" @click.stop="confirm">确认</view>
        </view>
        <view class="body" v-if="show">
            <view class="form-row">
                <view class="row-cont status-wrapper">
                    <view v-for="v in statusList" :key="v.value" class="status-item" :class="{active:v.value===params.status}" @click="params.status=v.value">{{v.label}}</view>
                </view>
            </view>
            
        </view>
    </view>
</myPopup>
</template>

<script>
import myPopup from '@/components/myPopup';
import upload from "@/components/customer/upload.vue";
export default {
    components: {
        myPopup, upload
    },
    props: {
        visible: { type: Boolean, default: false },
		report: { type: Object, default: () => ({})}
	},
    data(){
        return {
            show: false,
            submiting: false,
            params: {
                status: '',
            },
            statusList: [
                { value: 0, label: '已抄送' },
                { value: 1, label: '已报备' },
                { value: 2, label: '已驳回' },
                { value: 3, label: '已到访' },
                { value: 4, label: '未到访' },
            ]
        }
    },
    computed: {
        qrcodeUrl(){
            return this.params.url ? [this.params.url]: []
        }
    },
    watch: {
        visible(val){
            this.show = val;
        },
        show(val){
            val != this.visible && this.$emit('update:visible', val);
            if(val){
                const data = this.report;
                this.params.id = data.id;
                this.params.status = data.status;
            }  
        },
    },
    methods: {
        cancle(){
            this.show = false;
        },
        async confirm(){
            if(this.params.status === ''){
                uni.showToast({
                    title: '请选择报备状态',
                    icon: 'none',
                });
                return;
            }

            this.submiting = true;
            try{
                const data = await this.submit()
                uni.showToast({
                    title: data && data.msg ? data.msg : '更改状态成功',
                    icon: 'none',
                });
                this.show = false;
                this.$emit('success');
            }catch(e){}
            this.submiting = false;
        },
        handleUploadSuccess(e){
            this.params.url = e[0] || '';
        },
        submit () {
            return new Promise((resolve, reject) => {
                this.$ajax.post('/admin/crm/report/update_status', this.params, res => {
                    if (res.statusCode == 200) {
                        resolve(res.data);
                    }else{
                        uni.showToast({
                            title: res?.data?.message || '更改状态失败',
                            icon: 'none'
                        });
                    }
                    reject();
                }, er => {
                    reject();
                })
            })
        }
    },
}
</script>

<style scoped lang="scss"> 
.container{
    background: #fff;
    line-height: 1;
    
    .header{
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 46px;
        :after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            height: 1px;
            clear: both;
            border-bottom: 1px solid #e5e5e5;
            color: #e5e5e5;
            -webkit-transform-origin: 0 100%;
            transform-origin: 0 100%;
            -webkit-transform: scaleY(.5);
            transform: scaleY(.5);
        }
        .title{
            flex: 1;
            color: #999;
            text-align: center;
            display: inline-block;
            max-width: 50%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .action{
            padding: 0 14px;
            font-size: 17px;
            &.cancle{
                color: #888;
            }
            &.confirm{
                color: #007aff;
            }
        }
    }
}


.body{
    padding: 12rpx 32rpx 32rpx;
    .form-row{
        padding: 8rpx 0;
        margin-top: 28rpx;
        overflow: hidden;
        flex-direction: column;
        .row-label{
            color: #3c3c3c;
            white-space: nowrap;
            padding-right: 24rpx;
            font-size: 32rpx;
            font-weight: 600;
        }
        .row-cont{
            margin-top: 24rpx;
            flex-direction: row;
            flex-wrap: wrap;
            &.upload-wrapper{
                margin-top: 14rpx;
            }
            &.status-wrapper{
                margin-top: 0;   
            }
            .status-item{
                display: inline-flex;
                flex-direction: row;
                height: 66rpx;
                align-items: center;
                font-size: 28rpx;
                color: #4E5969;
                background-color: #f6f6f6;
                border: 1rpx solid #f1f2f3;
                padding: 0 28rpx;
                margin: 24rpx 24rpx 0 0;
                &.active{
                    color: #2d84fb;
                    background-color: #e5eeff;
                    border-color: #d9e8ff;
                }
            }
        }
    }
    .textarea-wrapper{
        background: #F8F8F8;
        padding: 12px 12px 0px 12px;
    }
    .reason-textarea{
        height: 200rpx;
    }
}
</style>