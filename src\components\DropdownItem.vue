<template>
  <view class="dropdown-item" :class="{ disabled: disabled }" @click.prevent.stop="onClick">
    <text class="text">
      <slot></slot>
    </text>
  </view>
</template>

<script>
export default {
  name: 'DropdownItem',
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    onClick () {
      if (this.disabled) {
        return
      }
      let parent = this.$getParent.call(this, 'Dropdown')
      parent.show = false
      this.$emit('click')
    },
  },
}
</script>

<style scoped lang="scss">
.dropdown-item {
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #2e3c4e;
  &.disabled {
    color: rgba($color: #2e3c4e, $alpha: 0.3);
  }
}
</style>
