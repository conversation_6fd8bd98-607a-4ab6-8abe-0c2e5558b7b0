<template>
    <myPopup :show="show"  @close="cancle">
        <view class="filter-wrapper">
        <view class="header">
                <view class="action cancle" @click.stop="cancle">取消</view>
                <view class="title">{{checkedLabel}}</view>
                <view class="action confirm" :class="{loading: submiting}" @click.stop="confirm">确认</view>
            </view>
            <view class="body">
                <tCustomPickerView v-model="selectedId" :datas="memberList" ref="picker"  v-if="show" remote @filter="onFilter"
                    filter-placeholder="搜索企微用户"
                    filterable :map="{label: 'name', value: 'id'}" :loading="loading">
                </tCustomPickerView>
            </view>
        </view>
    </myPopup>
</template>

<script>
import myPopup from '@/components/myPopup';
import tCustomPickerView from '@/components/tplus/tCustomPickerView';
export default {
    components: {
        myPopup, tCustomPickerView
    },
    props: {
        value: { type: [ Number, String, Array ], default:'' },
        visible: { type: Boolean, default: false },
        placeholder: { type: String, default: '' },
        filterId: { type: [Number, Array], default: 0 },
        customerId: { type: [String, Number], default: '' },
    },
    data() {
        return {
            loading: false,
            show: false,
            isInited: false,
            curValue: '',
            list: [],
            submiting: false,
            checkMember: null,
            isSearch: false,
        }
    },
    computed: {
        memberList(){
            if(filterIds === 0){
                return this.list;
            }
            const filterIds = Array.isArray(this.filterId) ? this.filterId : [this.filterId];
            return this.list.filter(item => !filterIds.includes(item.id));
        },
        selectedId: {
            get() {
                return this.curValue || '';
            },
            set(val) {
                this.curValue = val;
            }
        },
        checkedLabel(){
            return this.checkMember ? '已选择：'+this.checkMember.name : this.placeholder;
        },
    },
    watch: {
        value: {
            handler(val){
                this.selectedId = val;
            },
            immediate: true
        },
        visible(val){
            this.show = val;
            if(val && !this.isInited){
                this.isInited = true;
                this.getList();
            }
        },
        show(val){
            this.$emit('update:visible', val)
            if(val === false){
                this.selectedId = val;
                if(this.isSearch){
                    this.isSearch = false;
                    this.isInited = false;
                }
            }
        },
        selectedId(val){
            if(val){
                this.checkMember = this.memberList.find(e => e.id === this.selectedId);
            }else{
                this.checkMember = null;
            }
        }
    },
    created() {
        
    },
    methods: {
        getList(keywords = ''){
            this.loading = true;
            this.$ajax.get("/admin/crm/client/qw_search", { type: 2, keywords }, (res) => {
                this.loading = false;
                if (res.statusCode === 200) {
                    this.list = res.data.data;
                } else {
                    uni.showToast({
                        title: res.data.message,
                        icon: "none",
                    });
                }
            }, er=>{
                console.log(er)
                this.loading = false;
            })
        },
        onFilter(keyword){
            this.isSearch = keyword !== '';
            this.getList(keyword);
        },
        cancle(){
            this.show = false;
            this.selectedId = this.value;
        },
        async confirm(){
            if(!this.selectedId){
                uni.showToast({
                    title: '请选择要绑定的企微用户',
                    icon: 'none',
                });
                return;
            }
            if(this.submiting){
                return;
            }
            this.submiting = true;
            try{
                const data = await this.postBindQywx(this.customerId, this.checkMember ? this.checkMember.openid : '')
                uni.showToast({
                    title: data && data.msg ? data.msg : '绑定企微成功',
                    icon: 'none',
                });
                this.show = false;
                this.$emit('success');
            }catch(e){}
            this.submiting = false;
            
        },
        postBindQywx(client_id, openid){
            return new Promise((resolve, reject) => {
                this.$ajax.post('/admin/crm/client/bind', {client_id, openid}, res => {
                    if (res.statusCode == 200) {
                        resolve(res.data);
                    }else{
                        uni.showToast({
                            title: res?.data?.message || '绑定企微失败',
                            icon: 'none'
                        });
                    }
                    reject();
                }, er => {
                    reject();
                })
            })
        }
    }
}

</script>

<style scoped lang="scss"> 
.filter-wrapper{
    background-color: #fff;
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
    overflow: hidden;
}
.header{
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 46px;
        
    .title{
        flex: 1;
        color: #999;
        text-align: center;
        display: inline-block;
        max-width: 50%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .action{
        padding: 10px 16px;
        font-size: 17px;
        &.cancle{
            color: #888;
        }
        &.confirm{
            position: relative;
            color: #007aff;
            &.loading{
                opacity: .6;
            }
        }
    }
}
</style>