<template>
  <view class="" @touchstart="setHide">
    <view v-if="is_show_content">
      <mySkeleton
        v-if="is_show_skeleton"
        :showAvatar="false"
        :row="9"
        :showTitle="true"
      ></mySkeleton>
      <!--  v-if="!is_show_skeleton" -->
      <view class="top" v-if="!is_show_skeleton">
        <view class="top_img">
          <div class="top_img_main">
            <div class="top_img_follow">
              <!-- 客户数据 -->
              <div class="top_img_cus">
                <span v-if="user_detail.get_time != '' && user_detail.get_time != undefined">
                  跟客{{ user_detail.get_time | getFollowDay(user_detail.get_time) }}
                </span>
                <span 
                  v-if="(user_detail.take_num != '' && user_detail.take_num != undefined) && (user_detail.get_time != '' && user_detail.get_time != undefined)" 
                  style="margin: 0 5px;">
                  |
                </span>
                <span v-if="user_detail.take_num != '' && user_detail.take_num != undefined">
                  {{ user_detail.take_num | getTakeLook(user_detail.take_num) }}
                </span>
              </div>
              <!-- 时间数据 -->
              <div class="top_img_time">
                {{ user_detail.last_follow_time }}
              </div>
            </div>
          </div>
        </view>
        <view class="top-card" style="margin-top: -100rpx">
          <view class="row">
            <view class="top-card-left">
              <image
                class="pic"
                :src="`https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/${
                  user_detail.sex == 1 ? 'nan2' : 'nv2'
                }.png`"
                mode="aspectFill"
              />
            </view>
            <view class="top-card-right">
              <view class="top-card-right-top row">
                <text class="top-card-right-top_name">{{ user_detail.cname }}</text>
                <image
                  v-if="user_detail.wxqy_id"
                  class="top-card-right-top_qw"
                  src="@/static/customer/qw.png"
                  mode="widthFix"
                />
                <text class="top-card-right-top_tracking">{{
                  user_detail.tracking ? user_detail.tracking.title : ''
                }}</text>
                <text
                  :style="{
                    backgroundColor: user_detail.level.color,
                  }"
                  class="top-card-right-top_level"
                  v-if="user_detail.level"
                  >{{ user_detail.level ? user_detail.level.title : '--' }}</text
                >
              </view>
              <image
                @click="isShowSetting = true"
                class="setting"
                src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/setting.png"
              ></image>
              <view class="top-card-right-bottom">
                客户性别：{{ user_detail.sex == 1 ? '男' : user_detail.sex == 2 ? '女' : '--' }}
              </view>
            </view>
          </view>
          <view class="top-card-bot-bottom row">
            <text>跟进{{ user_detail.follow_num > 0 ? '（已跟进）' : '（未跟进）' }}</text>
            <text>创建时间：{{ user_detail.created_at }}</text>
          </view>
        </view>
        <view class="top-card">
          <view class="top-card-title row"
            >用户详情
            <!-- <myIcon type="you"></myIcon> -->
          </view>
          <view class="top-card-content row">
            <view class="top-card-content-label">客户电话</view>
            <view class="top-card-content-right" v-if="!is_view_tel">{{
              user_detail.mobile | mobileFilter
            }}</view>
            <view class="top-card-content-right" v-if="is_view_tel">{{ user_detail.mobile }}</view>
            <view class="chakan" @click="setViewTel">查看</view>
          </view>
          <view class="top-card-content row" v-if="user_detail.source">
            <view class="top-card-content-label">客户来源</view>
            <view class="top-card-content-right">{{
              user_detail.source ? user_detail.source.title : '--'
            }}</view>
          </view>
          <view class="top-card-content row" v-if="user_detail.type">
            <view class="top-card-content-label">客户来源</view>
            <view class="top-card-content-right">{{ typeFilter(user_detail.type) }}</view>
          </view>
          <view class="top-card-content row" v-if="user_detail.intention_community">
            <view class="top-card-content-label">客户意向</view>
            <view class="top-card-content-right">{{ user_detail.intention_community }}</view>
          </view>
          <view class="top-card-content">
            <view class="top-card-content-label">备注信息</view>
            <textarea
              placeholder-class="placeholderClass"
              placeholder="一句话描述客户需求"
              class="textarea"
              :disabled="!has_roles"
              @blur="onConfirmRemark"
            ></textarea>
          </view>
        </view>
        <view class="top-card" v-if="is_from == 2">
          <view class="top-card-title row" @click="onClickTag"
            >客户标签
            <myIcon type="you"></myIcon>
          </view>

          <view
            class="top-card-label-list row"
            v-if="user_detail.label_name && user_detail.label_name.length > 0"
          >
            <block v-for="(item, index) in user_detail.label_name" :key="index">
              <block v-for="(item1, index1) in item.son" :key="index1">
                <view class="tag_item" :key="item1.id">
                  <view class="tag_b">
                    {{ item1.name }}
                  </view>
                </view>
              </block>
            </block>
          </view>
        </view>
        <view class="top-card" v-if="is_from == 2">
          <view class="top-card-title">客户等级</view>
          <text class="level" v-if="user_detail.level">{{ user_detail.level.title }}</text>
        </view>
        <view
          :class="{ isborder: is_view_tel_desc }"
          class="top-card"
          @click="onGoDemand"
          v-if="is_from == 2"
        >
          <view class="top-card-title row"
            >填写跟进内容
            <myIcon type="you"></myIcon>
          </view>
        </view>
        <view class="top-card">
          <view class="top-card-tabs row">
            <view
              class="top-card-tabs-item"
              v-for="item in tabs_list"
              :key="item.id"
              @click="onClickTabs(item)"
              :class="{ isactive: item.id == is_tabs }"
              >{{ item.name }}</view
            >
          </view>
        </view>
        <!-- 动态 -->
        <view class="top-card" v-if="is_tabs == 1" style="margin-bottom: 100px">
          <view class="top-card-title card-border-line row">2022年01月01日 </view>
        </view>
        <view class="pic_box top-card" v-if="is_tabs == 2">
          <view class="basic-info-box">
            <view class="basic-info-title">基本信息</view>
            <!-- 客户行为 -->
            <view class="basic-info-content">
              <text class="content-title">客户行为</text>
              <text :class="user_detail.last_behavior_day != '' && user_detail.last_behavior_day != undefined ? 'blueInfo' : ''">
                {{ user_detail.last_behavior_day != '' && user_detail.last_behavior_day != undefined ? user_detail.last_behavior_day : '--' }}
              </text>
              <text v-if="user_detail.last_behavior_day != '' && user_detail.last_behavior_day != undefined">天内</text>
            </view>
            <!-- 活跃时间 -->
            <view class="basic-info-content">
              <text class="content-title">活跃时间</text>
              <template v-if="user_detail.start_date || user_detail.end_date">
                <text style="color: #3d91ff;" v-if="user_detail.start_date">{{ user_detail.start_date }}</text>
                <text style="color: #3d91ff;" v-if="user_detail.start_date && user_detail.end_date">/</text>
                <text style="color: #3d91ff;" v-if="user_detail.end_date">{{ user_detail.end_date }}</text>
              </template>
              <template v-else> -- </template>
              <text v-if="user_detail.start_date || user_detail.end_date">点</text>
            </view>
            <!-- 客户偏好 -->
            <view class="basic-info-content">
              <text class="content-title">客户偏好</text>
              <text>{{ user_detail.like || "--" }}</text>
            </view>
            <!-- 线索 -->
            <view class="basic-info-content">
              <text class="content-title">线      索</text>
              <text :class="user_detail.operation_log != '' && user_detail.operation_log != undefined ? 'redInfo' : ''">
                {{ user_detail.operation_log || 0 }}
              </text>
              <text v-if="user_detail.operation_log != '' && user_detail.operation_log != undefined">条</text>
            </view>
            <!-- 社群 -->
            <view class="basic-info-content">
              <text class="content-title">社      群</text>
              <text>{{ user_detail.group_list || "0" }}</text>个
            </view>
            <!-- 好友 -->
            <view class="basic-info-content">
              <text class="content-title">好      友</text>
              <text>
                {{
                  user_detail.qywx_info && user_detail.qywx_info.name
                    ? user_detail.qywx_info.name
                    : "--"
                }}
              </text>
            </view>
            <!-- 报备 -->
            <view class="basic-info-content">
              <text class="content-title">报      备</text>
              <text :class="user_detail.customer_list != '' && user_detail.customer_list != undefined ? redInfo : ''">
                {{ user_detail.customer_list || "--" }}
              </text>
              <text v-if="user_detail.customer_list != '' && user_detail.customer_list != undefined">条</text>
            </view>
            <!-- 报备状态 -->
            <view class="basic-info-content">
              <text class="content-title">报备状态</text>
              {{ user_detail.customer_status || "--" }}
                <template v-if="user_detail.customer_day">
                  ({{ user_detail.customer_day }}天前)
                </template>
            </view>
            <!-- 最近报备 -->
            <view class="basic-info-content">
              <text class="content-title">最近报备</text>
              {{ user_detail.customer_build_name || "--" }}
            </view>
          </view>
          <view class="list" style="margin-bottom: 100px">
            <view class="info">
              <view class="time row">
                <text>接待录入人</text>
              </view>
              <view class="pic row">
                <image
                  src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png"
                  mode="aspectFill"
                />
                <text class="c2" v-if="user_detail.create_user_name || user_detail.create_user">{{
                  user_detail.create_user_name || user_detail.create_user.user_name
                }}</text>
              </view>
              <text class="c2" v-if="user_detail.created_at"
                >创建时间：{{ user_detail.created_at }}</text
              >
            </view>
            <view class="info">
              <view class="time row">
                <text>跟进维护人</text>
              </view>
              <view class="pic row">
                <image
                  src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png"
                  mode="aspectFill"
                />
                <text class="c2" v-if="user_detail.follow_user_name || user_detail.follow_user">{{
                  user_detail.follow_user_name || user_detail.follow_user.user_name
                }}</text>
              </view>
              <text class="c2">创建时间：{{ user_detail.operation_at }}</text>
            </view>
            <view class="info" v-if="user_detail.deal_user_name">
              <view class="time row">
                <text>客源成交人</text>
              </view>
              <view class="pic row">
                <image
                  src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png"
                  mode="aspectFill"
                />
                <text class="c2" v-if="user_detail.deal_user_name || user_detail.deal_user">{{
                  user_detail.deal_user_name || user_detail.deal_user.user_name
                }}</text>
              </view>
              <text class="c2">创建时间：{{ user_detail.deal_at }}</text>
            </view>
          </view>
        </view>
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 3">
          <view class="follow-card" style="margin-bottom: 100px">
            <view class="follow-card-item" v-for="(item, index) in follow_list" :key="item.id">
              {{ item.created_at | captureTime1 }}
              <view class="follow-card-content row" style="padding-bottom: 0;">
                <view class="left" style="margin-right: 10px">
                  {{ item.created_at | captureTime }}
                </view>
                <view class="right" style="width: 100%;">
                  <view class="follow-config-main">
                    <view class="follow-config-titel">
                      客户跟进 
                      <text 
                        v-if="item.type_title && item.type_title.title" 
                        style="margin: 0 5px; line-height: 16px;">|</text>
                      <text v-if="item.type_title && item.type_title.title">
                        {{ item.type_title.title }}
                      </text>
                    </view>
                    <view class="InstalledTop">已置顶</view>
                    <!-- <view class="follow-config-box">
                      <text class="follow_config-Pinned">已置顶</text>
                      <view class="follow-config-like" @click="">点赞</view>
                      <view class="follow-config-copy" @click="followCopy(item)">复制</view>
                      <view class="follow-config-top" @click="followSetTop(item)">设为置顶</view> 
                    </view>-->
                  </view>
                  <text class="follow-box-content">
                    {{ item.content }}
                  </text>
                  <text class="username" v-if="item.admin">由「{{ item.admin.user_name }}」跟进</text>
                </view>
              </view>
              <view class="follow-card-picture">
                <view class="follow-picture-box">
                  <image
                    v-for="(img, index) in item.file_path_list" 
                    :key="index"
                    @click="preFollowImgs(item.file_path_list, index)"
                    :src="img"
                    mode="aspectFill"
                  />
                </view>
                <view class="follow-giveLike-box">
                  <view class="follow-giveLike-main">
                    <view class="follow-giveLike-zan">
                      <image src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/<EMAIL>" />
                    </view>
                    <view class="follow-giveLike-user">张三， 李四</view>
                  </view>
                  <view class="follow_giveLike-controls">
                    <!-- 点赞 -->
                    <view class="follow_giveLike_icon" @click="followGiveLike(item.id)">
                      <image src="@/static/customer/<EMAIL>" />
                    </view>
                    <!-- 复制 -->
                    <view 
                      style="margin: 0 20px; box-sizing: content-box;" 
                      class="follow_giveLike_icon"
                      @click="followCopy(item)">
                      <image src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/<EMAIL>" />
                    </view>
                    <!-- 置顶 -->
                    <view class="follow_giveLike_more" @click="showPinned(item, index)">
                      <image src="@/static/customer/<EMAIL>" />
                    </view>
                    <view class="follow-Pinned-box" ref="tops" v-if ="item.show">
                      <text class="Pinned-text">设置置顶</text>
                      <text class="Pinned-text">取消置顶</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <loadMore :status="is_f_loading"></loadMore>
          </view>
        </view>
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 4">
          <view class="follow-card" style="margin-bottom: 100px">
            <view class="follow-card-item" v-for="item in logs_list" :key="item.id">
              <text v-if="item.created_at">
                {{ item.created_at | captureTime1 }}
              </text>
              <view class="follow-card-content row">
                <view class="left" v-if="item.created_at" style="margin-right: 10px">
                  {{ item.created_at | captureTime }}
                </view>
                <view class="right">
                  <text>
                    {{ item.content || '' }}
                  </text>
                  <text class="username" v-if="item.admin_user"
                    >由「{{ item.admin_user.user_name || '--' }}」跟进</text
                  >
                </view>
              </view>
            </view>
            <loadMore :status="is_l_loading"></loadMore>
          </view>
        </view>
        <view class="top-card" style="background: none; padding: 0" v-if="is_tabs == 5">
          <view class="follow-card" style="margin-bottom: 100px">
            <view class="follow-card-item" v-for="item in guiji_list" :key="item.id">
              <text v-if="item.ctime">
                {{ item.ctime | captureTime1 }}
              </text>
              <view class="follow-card-content row">
                <view class="left" v-if="item.ctime" style="margin-right: 10px">
                  {{ item.ctime | captureTime }}
                </view>
                <view class="right">
                  <text>
                    {{ item.content || '' }}
                  </text>
                </view>
              </view>
            </view>
            <loadMore :status="is_guiji_loading"></loadMore>
          </view>
        </view>
      </view>
      <!-- 底部菜单 -->
      <view v-if="is_from == 2" class="container top-line flex-row footer_btn_group">
        <view class="option_btn flex-3">
          <my-button @click="concatOwner" type="primary" :round="false" size="big">电话</my-button>
        </view>
        <view class="option_btn flex-3">
          <my-button @click="onGoDemand" type="primary" :round="false" size="big" :plain="true"
            >跟进</my-button
          >
        </view>
        <view class="option_btn flex-3" @click="isShowSetting = true">
          <my-button type="primary" :round="false" size="big" :plain="true">
            <view class="more_con row">更多 ⋮ </view></my-button
          >
        </view>
        <!-- 底部菜单 -->
      </view>
    </view>
    <myPopup :show="isShowSetting" @hide="isShowSetting = false">
      <view class="setting-pop">
        <view class="setting-pop-top row">
          <view
            @click="onClickSetting1(item)"
            class="item"
            v-for="item in setting_1"
            :key="item.id"
          >
            <image class="item-img" :src="item.icon"></image>
            <text>{{ item.name }}</text>
          </view>
        </view>
        <view class="setting-pop-bottom">
          <view
            class="item row"
            v-for="item in setting_2"
            :key="item.id"
            @click="onClickSetting2(item)"
          >
            <view class="left row">
              <image class="item-img" :src="item.icon"></image>
              <text>{{ item.name }}</text>
            </view>
            <myIcon type="you"></myIcon>
          </view>
        </view>
      </view>
    </myPopup>
    <my-popup ref="remind" :show="isShow" @hide="isShow = false">
      <remind
        :id="user_detail.id"
        class="remind"
        v-if="isShow"
        @onClick="onClickRemind"
        @cancel="isShow = false"
      ></remind>
    </my-popup>
    <image
      v-if="false"
      class="istotop"
      src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/hdb.png"
      @click="ongoTop"
    ></image>
    <!-- 公海详情进入显示立即认领 -->
    <view
      class="lijibtn"
      v-if="is_from == 3 && !user_detail.follow_id && is_load_end"
      @click="getCustomer"
      >立即认领</view
    >
  </view>
</template>
<script>
import myIcon from "@/components/my-icon.vue";
import myPopup from "@/components/myPopup.vue";
import remind from "@/components/remind";
import Dropdown from "../house/components/Dropdown";
import DropdownItem from "../house/components/DropdownItem";
import myButton from "../house/components/myButton";
import loadMore from "@/components/loadMore"
export default {
  components: { myIcon, myPopup, remind, Dropdown, DropdownItem, myButton, loadMore },
  data () {
    return {
      client_id: "",
      user_detail: {},
      follow_params: {
        page: 1,
        per_page: 10,
        client_id: "",
      },
      log_params: {
        page: 1,
        per_page: 10,
        client_id: "",
      },
      is_guiji_loading: false,
      guiji_params: {
        page: 1,
        per_page: 10,
        client_id: "",
      },
      guiji_list: [],
      follow_list: [],
      is_f_loading: '',
      logs_list: [],
      is_l_loading: '',
      // 查看电话
      is_view_tel: false,
      view_tel: "",
      is_from: "",
      is_show_skeleton: false,
      is_show_content: false, // 用于从侧边栏进入隐藏内容
      type_list: [],
      tabs_list: [
        // { id: 1, name: "动态" },
        { id: 3, name: "跟进" },
        { id: 2, name: "画像" },
        { id: 4, name: "线索" },
      ],
      is_tabs: 3,
      isShowSetting: false,
      setting_1: [
        {
          id: 1,
          name: "维护资料",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/whzl.png",
        },
        {
          id: 2,
          name: "提醒跟进",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/txtz.png",
        },
      ],
      setting_2: [
        {
          id: 1,
          name: "标无效",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/bwx.png",
        },
        {
          id: 2,
          name: "转资料",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/zzl.png",
        },
        {
          id: 4,
          name: "绑定企业微信",
          icon:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/zzl.png",
        },
        // {
        //   id: 3,
        //   name: "审批",
        //   icon:
        //     "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/sp.png",
        // },
      ],
      isShow: false,
      is_view_tel_desc: false,
      from: '',    //进入来源  默认为空  如果侧边栏进入不携带id  from=‘fromOther’
      login_user_info: {},//当前登录用户的信息
      has_roles: true, //是否有操作权限 也就是 当前的客户还是不是登陆者的客户 如果不是只能查看不能操作
      is_load_end: false, //定义变量 加载完成以后显示认领按钮
      token: "",
      is_refresh: true, // 重置提交跟进后请求跟进列表
      // 置顶操作框参数
      PinnedStyle: {
        id: "", // 选中的跟进id
        is_top: true, // 是否显示置顶操作框
      }
    };
  },
  onLoad (options) {
    uni.$on("getDataAgain", () => {
      this.is_show_content = true
      this.getDataDetail();
      this.getTypeData();
    })
    console.log(options.website_id);
    // if (options.website_id) {
    //   if (options.website_id == 176) {
    this.is_show_skeleton = true;

    // 未登录中断请求

    this.is_from = options.form || 2; // 来源公海/我的
    uni.showLoading({
      title: "加载中",
      mask: true
    })
    if (options.id) {
      this.client_id = options.id;
      this.getDataDetail();
      this.getTypeData();
    } else {
      this.from = "fromOther"
      this.getWxQyWxConfig(["agentConfig", 'getCurExternalContact'], wx => {
        this.wx = wx
        this.getDataDetail();
        this.getTypeData();
      })
    }
  },
  onShow() {
    if(this.is_refresh) {
      uni.$on('refresh', (data) => {
        if(data.refresh) {
          this.follow_params.page == 1; // 重置页码
          this.follow_list = []; // 清空跟进记录列表
          this.getFollowData(); // 获取跟进记录
        }
      }) 
      this.is_refresh = false;
    }
  },
  onUnload () {
    uni.$off("getDataAgain")
    uni.$off("refresh"); // 解绑自定义事件
  },
  filters: {
    mobileFilter (val) {
      let reg = /^(.{3}).*(.{3})$/;
      if (val) {
        return val.replace(reg, "$1*****$2");
      }
    },
    captureTime1 (fullTime) {
      var timeArr = fullTime
        .replace(" ", ":")
        .replace(/\:/g, "-")
        .split("-");
      var nian = timeArr[0];
      var yue = timeArr[1];
      var ri = timeArr[2];
      var shi = timeArr[3];
      var fen = timeArr[4];
      var miao = timeArr[5];

      return yue + "月" + ri + "日";
    },
    captureTime (fullTime) {
      var timeArr = fullTime
        .replace(" ", ":")
        .replace(/\:/g, "-")
        .split("-");
      var nian = timeArr[0];
      var yue = timeArr[1];
      var ri = timeArr[2];
      var shi = timeArr[3];
      var fen = timeArr[4];
      var miao = timeArr[5];

      return shi + ":" + fen;
    },
    // 计算过去时间天数
    getPastDay(val) {
      if(val == 0) {
        return "今天";
      } else {
        return val+"天前";
      }
    },
    // 计算跟客时间
    getFollowDay(val) {
      const currentDate  = new Date();
      const specifiedDate = new Date(val);
      // 计算时间差
      const timeDiff = currentDate.getTime() - specifiedDate.getTime();
      // 将时间差转换为天数
      const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      if(daysDiff == 0) {
        return "今天"; 
      } else {
        return daysDiff + "天";
      }
    },
    // 计算带看次数
    getTakeLook(val) {
      switch(val) {
        case 1: 
          val = "首看";
          break;
        case 2:
          val = "二看";
          break;
        default:
          if(val > 3) {
            return "多次带看";
          }
          break;
      }
      return val;
    },
    // 是否是新客留资(24小时内新增的)
    getNewCustomer(val) {
      const currentDate  = new Date();
      const specifiedDate = new Date(val);
      // 计算时间差
      const timeDiff = currentDate.getTime() - specifiedDate.getTime();
      // 将时间差转换为天数
      const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      if(daysDiff == 0) {
        return "新客留资"; 
      } else {
        return null;
      }
    }
  },
  methods: {
    getTypeData () {
      this.$ajax.get("/qywx/type/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.type_list = res.data;
        }
      });
    },
    typeFilter (val) {
      let arr = this.type_list.filter((item) => {
        if (item.id == val) {
          return item;
        }
      })[0];
      if (arr) {
        return arr.title;
      }
    },
    onClickTabs (e) {
      this.is_tabs = e.id;
      if (e.id == 5) {
        this.getGuijiList()
      }
    },
    getGuijiList () {
      if (this.guiji_params.page === 1) {
        this.guiji_list = [];
      }
      this.guiji_params.id = this.client_id
      this.is_guiji_loading = "loading"
      this.$ajax.get("/qywx/tfy_client_track/search", this.guiji_params, (res) => {
        if (res.statusCode === 200) {
          this.guiji_list = this.guiji_list.concat(res.data.list);
          if (res.data.list.length === 0 || res.data.list.length < this.guiji_list.per_page) {
            this.is_guiji_loading = "nomore";
          } else {
            this.is_guiji_loading = "loadend"
          }
        }
      });
    },
    getDataDetail () {
      var url = "";
      if (this.client_id) {
        url = `/qywx/client/info/${this.client_id}`;
        this.getUserData(url);
      } else {
        var _this = this
        this.wx.invoke("getCurExternalContact", {}, function (res) {
          // this.user_id = 'wm-VQJYQAABaPDlf4UPTMNqm40Rq5WXw'
          if (res.err_msg == "getCurExternalContact:ok") {
            _this.user_id = res.userId;
            url = `/qywx/client/qw_info/${_this.user_id}`;
            _this.getUserData(url);
          } else {
            console.log(res);
          }
        });
      }
    },
    getUserData (url) {
      this.is_show_skeleton = false;
      this.$ajax.get(url, {}, (res) => {
        if (res.statusCode === 200) {
          this.user_detail = res.data;
          this.follow_params.client_id = res.data.id;
          this.log_params.client_id = res.data.id;
          this.client_id = res.data.id;
          if (this.user_detail.access_id) {
            if (this.is_from == 3) {
              // 公海客户详情
              this.tabs_list = this.tabs_list.filter((item) => item.id == 4);
              this.tabs_list.push(
                { id: 5, name: "轨迹" },
              )
              this.is_tabs = 4;
            } else {
              this.tabs_list = [
                { id: 3, name: "跟进" },
                { id: 2, name: "画像" },
                { id: 4, name: "线索" },
                { id: 5, name: "轨迹" },
              ]
            }
          }
          if (this.user_detail.wxqy_id) {
            this.setting_2 = this.setting_2.filter(item => item.id != 4)
          }
          uni.hideLoading()
          this.is_load_end = true
          uni.setStorageSync("crm_client_id", res.data.id);
          console.log("触发02");
          this.getFollowData();
          this.getUserInfo()
          this.is_show_content = true
        } else {
          uni.hideLoading()
          let website_id = uni.getStorageSync("website_id")
          uni.redirectTo({
            url: '/customer/default_crm?website_id=' + website_id
          })
          // this.$navigateTo(`/customer/default_crm`);
        }
      });
    },
    getUserInfo () {
      var that = this;
      this.$ajax.get("/qywx/common/query", {}, (res) => {
        if (res.statusCode === 200) {
          this.login_user_info = res.data
          if (this.user_detail.follow_id != this.login_user_info.id) {
            this.has_roles = false
          }
        }
      });
    },
    getFollowData () {
      if (this.follow_params.page === 1) {
        this.follow_list = [];
      }
      this.is_f_loading = "loading"
      this.$ajax.get(
        "/qywx/client_follow/search",
        this.follow_params,
        (res) => {
          if (res.statusCode === 200) {
            if (res.data.data.length === 0 || res.data.data.length < this.follow_params.per_page) {
              this.is_f_loading = 'nomore';
            } else {
              this.is_f_loading = ''
            }
            this.follow_list = this.follow_list.concat(res.data.data);
            this.getLogsData();
          }
        }
      );
    },
    setViewTel () {
      if (!this.user_detail.follow_id || this.user_detail.follow_id == 0) {
        // 如果没有跟进人 无法查看电话 提示 请先认领客户
        uni.showToast({
          title: "请先认领客户",
          icon: "none",
        });
        return;
      }
      if (this.is_view_tel) {
        return;
      }
      this.$ajax.get(`/qywx/client/look_tel/${this.client_id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.is_view_tel = true;
          uni.showToast({
            title: "不要忘记写跟进哦",
            icon: "none",
          });
          this.view_tel = res.data[0];
          this.is_view_tel_desc = true;
          setTimeout(() => {
            this.is_view_tel_desc = false;
          }, 5000);
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    getLogsData () {
      if (this.log_params.page === 1) {
        this.logs_list = [];
      }
      this.is_l_loading = "loading"
      this.$ajax.get("/qywx/client_clue/search", this.log_params, (res) => {
        if (res.statusCode === 200) {
          this.logs_list = this.logs_list.concat(res.data.data);
          if (res.data.data.length === 0 || res.data.data.length < this.log_params.per_page) {
            this.is_l_loading = "nomore";
          } else {
            this.is_l_loading = "loadend"
          }
        }
      });
    },
    loadmoreFollow () {
      if (this.is_f_loading == "nomore") {
        // uni.showToast({
        //   title: "没有更多了",
        //   icon: "none",
        // });
        return;
      }
      this.follow_params.page++;
      console.log("触发01");
      this.getFollowData();
    },
    LoadMoreLogs () {
      if (this.is_l_loading == 'nomore') {
        return;
      }
      this.log_params.page++;
      this.getLogsData();
    },
    loadMoreGuiji () {
      if (this.is_guiji_loading == 'nomore') {
        return;
      }
      this.guiji_params.page++;
      this.getGuijiList();
    },
    onClickTag () {
      if (!this.has_roles) {
        uni.showToast({
          title: '暂无权限操作',
          icon: 'none'
        })
        return
      }
      this.$navigateTo(`/customer/tag_list?id=${this.user_detail.id}&from=${this.from}`);
    },
    onConfirmRemark (e) {
      if (!e.detail.value) {
        // uni.showToast({
        //   title: "请检查备注内容",
        //   icon: "none",
        // });
        return
      }
      let form = {
        id: this.user_detail.id,
        remark: e.detail.value,
      };
      this.$ajax.post("/qywx/client/update_remark", form, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "操作成功",
            icon: "none",
          });
          this.log_params.page = 1;
          this.getLogsData();
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    onClickSetting1 (e) {
      if (!this.has_roles) {
        uni.showToast({
          title: '暂无权限操作',
          icon: 'none'
        })
        return
      }
      if (e.id == 1) {
        this.$navigateTo(`uphold?type=2&id=${this.user_detail.id}`);
      } else {
        this.isShowSetting = false;
        this.isShow = true;
      }
    },
    onClickSetting2 (e) {
      if (!this.has_roles) {
        uni.showToast({
          title: '暂无权限操作',
          icon: 'none'
        })
        return
      }
      if (e.id == 1) {
        let type = '19_2'
        this.$navigateTo("/house/applyApprove?id=1&house_id=" + this.user_detail.id + "&type=" + type)
        // uni.showModal({
        //   title: "提示",
        //   content: `是否设置为无效？`,
        //   success: function (res) {
        //     if (res.confirm) {
        //       let form = {
        //         content: "",
        //         client_id: that.user_detail.id,
        //       };
        //       that.$ajax.post("/qywx/client/update_invalid", form, (res) => {
        //         if (res.statusCode === 200) {
        //           that.getDataDetail();
        //         } else {
        //           uni.showToast({
        //             title: res.data.message,
        //             icon: "none",
        //           });
        //         }
        //       });
        //     }
        //   },
        // });
      }
      if (e.id == 2) {
        // this.$navigateTo(`/customer/choose_friend?id=${this.user_detail.id}`)
        this.$navigateTo(
          `/customer/transfer_customer?id=${this.user_detail.id}`
        );
      }
      if (e.id == 3) {
        this.$navigateTo(`/customer/create_audit?id=${this.user_detail.id}`);
      }
      if (e.id == 4) {
        this.$navigateTo(`/customer/qw_friend_list?id=${this.user_detail.id}`);
      }
      this.isShowSetting = false;
    },
    onGoDemand () {
      this.$navigateTo(`/customer/demand?id=${this.user_detail.id}&from=${this.from}`);
    },
    onClickRemind (e) {
      this.$ajax.post("/qywx/client_remind/create", e, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "操作成功",
            icon: "none",
          });
          this.getDataDetail();
          this.isShow = false;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    ongoTop () {
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300,
      });
    },
    getCustomer () {
      var that = this;
      uni.showModal({
        title: "提示",
        content: `是否认领该客户？`,
        success: function (res) {
          if (res.confirm) {
            let form = {
              ids: that.user_detail.id + "",
            };
            that.$ajax.post("/qywx/client/get", form, (res) => {
              if (res.statusCode === 200) {
                uni.showToast({
                  title: "领取成功",
                  icon: "none",
                });
                that.$navigateTo(
                  `/customer/detail?id=${that.client_id}&form=2`
                );
                // that.getDataDetail();
              } else {
                uni.showToast({
                  title: res.data.message,
                  icon: "none",
                });
              }
            });
          }
        },
      });
    },
    goBack () {
      this.$navigateBack();
    },
    concatOwner () {
      if (this.from == "fromOther") return
      uni.makePhoneCall({
        phoneNumber: this.user_detail.mobile,
        success: () => {
          console.log("拨打经纪人电话");
        }, //仅为示例
      });
    },
    // 预览图片
    preFollowImgs(filePath, index) {
      uni.previewImage({
        current: index,
        urls: filePath,
        indicator: 'number',
      })
    },
    // 跟进列表点赞功能
    followGiveLike(id) {
      console.log("点赞触发")
      this.$ajax.get(`/qywx/client_follow/click/${id}`, {}, (res) => {
        if(res.statusCode === 200) {
          this.getFollowData(); // 获取最新数据
        }
      })
    },
    // 跟进列表赋值文本功能
    followCopy(item) {
      console.log(item, "复制");
      let contents = item.type_title.title + '，' + item.content;
      this.$copyText(contents, () => {
        uni.showToast({
          title: '复制成功',
          icon: "none", 
        });
      })
    },
    // 跟进列表设置为置顶
    followSetTop(item) {
      console.log(item,"item");
      this.$ajax.get(`/qywx/client_follow/top/${item.id}`, {}, (res) => {
        if(res.statusCode === 200) {
          uni.showToast({
            title: '置顶成功',
            icon: "none",
          });
          this.follow_params.page = 1;
          this.getFollowData(); // 获取最新数据
        }
      })
    },
    setHide(){
      this.follow_list.map(item => {
        item.show = false 
        return item
      })
      this.$forceUpdate()
    },
    // 显示置顶操作框
    showPinned(item, index) {
      console.log(item, index);
      this.follow_list.map(item=>{
        item.show =false 
        return item
      })
      item.show =true
      this.$forceUpdate()
      // this.$set(this.follow_list[index])
      // 如果id不同就全部隐藏操作框
      // if(this.PinnedStyle.id != item.id) {
      //   this.$refs.tops.map((item) => {
      //     item.$el.style.display = "none";
      //   })
      //   this.PinnedStyle.is_top = true;
      //   this.PinnedStyle.id = item.id; // 复制id
      // }
      // if(this.PinnedStyle.is_top) {
      //   this.$refs.tops[index].$el.style.display = "flex";
      //   this.PinnedStyle.is_top = false;
      //   document.body.addEventListener('click', this.eventPinned);
      // } else {
      //   this.$refs.tops[index].$el.style.display = "none";
      //   this.PinnedStyle.is_top = true;
      //   document.body.removeEventListener('click', this.eventPinned);
      // }
    },
    eventPinned() {
      console.log("123");
    }
  },
  onReachBottom () {
    if (this.is_tabs == 3) {
      this.loadmoreFollow();
    }
    if (this.is_tabs == 4) {
      this.LoadMoreLogs();
    }
    if (this.is_tabs == 5) {
      this.loadMoreGuiji();
    }
  },
};
</script>
<style scoped lang="scss">
page {
  background: #f6f6f6;
  color: #2e3c4e;
}

.top {
  display: block;
  .top_img {
    background: #2f6aff;
    height: 274rpx;
    position: relative;
    .top_img_main {
      width: 100%;
      height: 41px;
      padding: 0 18px;
      box-sizing: border-box;
      position: absolute;
      bottom: 67px;
      left: 0px;
      .top_img_follow {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 100%;
        background-color: #5F8CFF;
        border-radius: 5px;
        padding: 0 14px;
        box-sizing: border-box;
        .top_img_cus, .top_img_time {
          font-size: 14px;
          color: #FFFFFF;
        }
      }
    }
  }
  &-card {
    padding: 14px;
    background: #fff;
    border-radius: 5px;
    margin: 0 18px 10px;
    display: block;
    position: relative;
    &-title {
      justify-content: space-between;
      font-size: 14px;
    }
    &-left {
      .pic {
        width: 40px;
        height: 40px;
      }
    }
    &-right {
      margin-left: 12px;
      .setting {
        width: 20px;
        height: 20px;
        position: absolute;
        right: 14px;
      }
      &-top {
        align-items: center;
        &_name {
          font-size: 14px;
          color: #282a2f;
        }
        &_qw {
          height: 18px;
          width: 18px;
          margin-left: 4px;
        }
        &_tracking {
          color: #3e8afd;
          margin: 0 6px;
          font-size: 12px;
        }
        &_level {
          color: #fff;
          padding: 2px 12px;
          border-radius: 2px;
        }
      }
      &-bottom {
        margin-top: 10px;
        color: #828488;
        font-size: 12px;
      }
    }
    &-bot-bottom {
      margin-top: 16px;
      width: 100%;
      border-top: 1px solid #eeeeee;
      padding-top: 12px;
      font-size: 12px;
      justify-content: space-between;
      color: #828488;
    }
    &-content {
      margin-top: 16px;
      &-label {
        color: #737373;
      }
      &-right {
        margin-left: 20px;
      }
      .textarea {
        margin-top: 8px;
        padding: 6px 16px;
        background: #f5f7fa;
        border: 1px solid #e8e8e8;
        height: 63px;
        width: 100%;
      }
      .placeholderClass {
        font-size: 13px;
        color: #bcbcbc;
      }
    }
    &-label-list {
      margin-top: 8px;
      flex-wrap: wrap;
      .item {
        margin-bottom: 4px;
        padding: 4px 12px;
        border-radius: 4px;
        background: #e8f1ff;
        color: #2f6aff;
        margin-right: 10px;
      }
    }
    .chakan {
      font-size: 13px;
      color: #2f6aff;
      position: absolute;
      right: 14px;
    }
    .level {
      width: 60px;
      display: inline-block;
      margin-top: 10px;
      text-align: center;
      border-radius: 4px;
      background: #f1f4fa;
      color: #737373;
      height: 28px;
      line-height: 28px;
      font-size: 14px;
    }
    &-tabs {
      font-size: 14px;
      justify-content: space-around;
      &-item {
        position: relative;
        &.isactive {
          color: #2f6aff;
          &::after {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            content: '';
            height: 4px;
            background: #2d84fb;
            width: 30px;
            display: block;
            margin-top: 18px;
          }
        }
      }
    }
  }
}
.setting-pop {
  height: 327px;
  border-radius: 20px 20px 0px 0px;
  background: #f6f6f6;
  padding: 14px 18px 42px;
  &-top {
    align-items: center;
    background: #fff;
    justify-content: space-around;
    border-radius: 5px;
    padding: 22px 0;
    .item {
      align-items: center;
      font-size: 16px;
    }
    .item-img {
      width: 20px;
      margin-bottom: 6px;
      height: 20px;
    }
  }
  &-bottom {
    background: #fff;
    border-radius: 5px;
    padding: 22px 20px;
    margin-top: 16px;
    .item {
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      .left {
        align-items: center;
        font-size: 16px;
        .item-img {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
.istotop {
  width: 42px;
  height: 42px;
  position: fixed;
  right: 24px;
  bottom: 100px;
}
.content-box {
  background: #fff;
  padding: 14px;
  border-radius: 5px;
}
.card-border-line {
  padding-bottom: 14px;
  border-bottom: 1px solid #eeeeee;
}

.pic_box {
  display: flex;
  flex-direction: column;
  margin-bottom: 100px;
  background: none;
  padding: 0;
  .basic-info-box {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 14px;
    box-sizing: border-box;
    background-color: #FFFFFF;
    margin-top: 4px;
    margin-bottom: 20px;
    .basic-info-title {
      color: #2e3c4e;
      font-weight: bold;
      font-size: 14px;
    }
    .basic-info-content {
      display: flex;
      flex-direction: row;
      margin-top: 20px;
      .content-title {
        font-size: 13px;
        color: #737373;
        margin-right: 20px;
        white-space: pre;
      }
    }
  }
  .list {
    background: #fff;
    border-radius: 6px;
    padding: 14px;
    box-sizing: border-box;
    .info {
      font-size: 16px;
      margin-top: 25px;
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;
      .c2 {
        text-align: end;
        font-size: 12px;
        color: #828488;
      }
      .time {
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
        color: #737373;
      }
      .pic {
        align-items: center;
        margin-top: 12px;
        image {
          width: 45px;
          height: 45px;
          border-radius: 50%;
          margin-right: 12px;
        }
      }
    }
  }
}
.follow-card {
  &-item {
    margin-bottom: 24px;
  }
  &-content {
    background: #fff;
    border-radius: 5px;
    margin-top: 14px;
    padding: 14px;
    font-size: 14px;
    color: #828488;
    line-height: 20px;
    span {
      color: #000;
    }
    .username {
      font-size: 12px;
    }
  }
  .follow-card-picture {
    padding: 20rpx 28rpx 28rpx 120rpx;
    background-color: #fff;
    position: relative;
    .follow-picture-box {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      image {
        width: 113rpx;
        height: 113rpx;
        border-radius: 4rpx;
        margin-right: 16rpx;
        margin-bottom: 16rpx;
      }
    }
    .follow-giveLike-box {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-top: 6px;
      .follow-giveLike-main {
        display: flex;
        flex-direction: row;
        align-items: center;
        border-radius: 12px;
        padding: 5px 14px;
        box-sizing: border-box;
        background-color: #EBEBEB;
        .follow-giveLike-zan {
          width: 14px;
          height: 14px;
          image {
            width: 100%;
            height: 100%;
          }
        }
        .follow-giveLike-user {
          display: flex;
          flex-direction: row;
          padding-left: 16rpx;
          margin-left: 16rpx;
          color: #8A929F;
          font-size: 24rpx;
          border-left: 1px solid #8A929F;
        }
      }
      .follow_giveLike-controls {
        display: flex;
        flex-direction: row;
        .follow_giveLike_icon {
          width: 28rpx;
          height: 28rpx;
          image {
            width: 100%;
            height: 100%;
          }
        }
        .follow_giveLike_more {
          width: 28rpx;
          height: 8rpx;
          line-height: 16rpx;
          image {
            width: 100%;
            height: 100%;
          }
        }
        .follow-Pinned-box {
          // display: none;
          padding: 16rpx;
          border-radius: 5px;
          background: #FFFFFF;
          box-shadow: 0px 0px 8px 0px #0000003F;
          position: absolute;
          bottom: -45px;
          right: 10px;
          .Pinned-text {
            color: #6C6F74;
            font-size: 28rpx;
            &:first-child {
              padding-bottom: 5px;
              border-bottom: 1px solid #6C6F74;
              margin-bottom: 5px;
            }
          }
        }
      }
    }
  }
}
.lijibtn {
  border-radius: 5px;
  background: #3172f6;
  box-shadow: 0px 4px 10px 0px #3172f67f;
  position: fixed;
  color: #fff;
  line-height: 40px;
  text-align: center;
  left: 50%;
  bottom: 10%;
  transform: translate(-50%, 0);
  width: 300px;
}

.isborder {
  animation: glow 800ms ease-out infinite alternate;
}
@keyframes glow {
  0% {
    border-color: #2d84fb;
    box-shadow: 0 0 5px #2d84fb, inset 0 0 5px rgba(221, 225, 233, 1), 0 1px 0 #2d84fb;
  }
  100% {
    border-color: rgba(221, 225, 233, 1);
    box-shadow: 0 0 20px #2d84fb, inset 0 0 10px rgba(221, 225, 233, 1), 0 1px 0 #2d84fb;
  }
}

.footer_btn_group {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9;
  background-color: #fff;
  padding: 12px 24px;
  .option_btn {
    display: block;
    text-align: center;
    .more_con {
      align-items: center;
      padding: 0 20rpx;
    }
    .more {
      display: block;
      width: 6rpx;
      font-size: 44rpx;
      transform: rotate(90deg);
      // line-height: 10rpx;
      // overflow: hidden;
      // word-wrap: break-word;
      // line-height: 18rpx;
      // padding: 6rpx 0;
    }
    &.option_btn_cloumn {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 32rpx;
      color: #8a929f;
      image {
        width: 45rpx;
        height: 34rpx;
      }
    }

    ~ .option_btn {
      margin-left: 24rpx;
    }
    ::v-deep .my-btn.big {
      padding: 0 16rpx;
    }
  }
}
.l-title {
  font-size: 16px;
  color: #2e3c4e;
}
.tag_input,
.top-card-label-list {
  margin-top: 20px;
  // border: 1px solid #dde1e9;
  border-radius: 4px;
  align-items: center;
  // padding: 9px 12px;
  flex-wrap: wrap;
  .tag_item {
    margin-bottom: 4px;
    padding: 4px 12px;
    border-radius: 4px;
    background: #e8f1ff;
    color: #2f6aff;
    margin-right: 10px;
    // padding: 8px 20px;
    // text-align: center;
    // margin-right: 10px;
    // // margin-left: 10px;
    // margin-bottom: 12px;
    // background: #fff;
    // border-radius: 4px;
    // border: 1px solid #eee;
    // min-width: 80px;
    // color: #8d9099;
    position: relative;
    overflow: hidden;
    &.checked {
      border: 1px solid rgba(45, 132, 251, 1);
      color: #2d84fb;
      background: #fff;
    }
    .tag_t {
      font-size: 14px;
      margin-bottom: 10rpx;
    }
    .tag_b {
      font-size: 14px;
    }

    .checked {
      position: absolute;
      right: 0;
      top: 0;
      width: 32rpx;
      height: 24rpx;
      image {
        width: 100%;
        height: 100%;
      }
    }
  }
  input {
    width: 100%;
    font-size: 14px;
  }
}
.follow-config-main {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 10px;
  justify-content: space-between;
  .InstalledTop {
    padding: 0px 4px;
    border-radius: 2px;
    background: #2D84FB;
    color: #FFFFFF;
    font-size: 12px;
  }
  .follow-config-titel {
    display: flex;
    flex-direction: row;
    color: #2e3c4e;
    font-size: 14px;
    font-weight: bold;
  }
}
.follow-box-content {
  margin-bottom: 10px;
}
.redInfo {
  color: #FF0000;
}
.blueInfo {
  color: #3d91ff;
}
</style>
