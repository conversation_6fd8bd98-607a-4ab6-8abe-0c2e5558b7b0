<template>
  <view>
    <mySkeleton v-if="is_show_skeleton" :showAvatar="false" :row="9" :showTitle="true"></mySkeleton>
    <view class="pad-12" v-if="!is_show_skeleton">
      <view class="sort row">
        <swiper class="swiper" :indicator-dots="true">
          <swiper-item v-for="item in top_menu_list" :key="item.id">
            <view class="row">
              <view class="info">
                <view class="num row">
                  <text>{{ (item.list && item.list.to_day_num) || 0 }}</text>
                  <image
                    v-if="item.list && item.list.to_day_num"
                    class="sheng"
                    src="../static/customer/sheng.png"
                    mode="widthFix"
                  />
                </view>
                <text>今日新增</text>
              </view>
              <view class="info">
                <view class="num row">
                  <text>{{ (item.list && item.list.month_num) || 0 }}</text>
                  <image
                    v-if="item.list && item.list.month_num"
                    class="sheng"
                    src="../static/customer/sheng.png"
                    mode="widthFix"
                  />
                </view>
                <text>本月新增</text>
              </view>
              <view class="info">
                <view class="num row">
                  <text>{{ (item.list && item.list.total_num) || 0 }}</text>
                  <image
                    v-if="item.list && item.list.total_num"
                    class="sheng"
                    src="../static/customer/sheng.png"
                    mode="widthFix"
                  />
                </view>
                <text>{{ item.desc }}</text>
              </view>
            </view>
          </swiper-item>
        </swiper>
      </view>
      <view v-for="item1 in menu_list" :key="item1.menu_mode_id">
        <block v-if="item1.data">
          <view class="title c2">{{ item1.title }}</view>
          <view class="list row">
            <view
              class="info"
              v-for="item in item1.data"
              :key="item.id"
              @click="clickUphold(item, item1)"
            >
              <image
                :src="`https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5crm/${item.icon}.png`"
                mode="widthFix"
              />
              <view>{{ item.name }}</view>
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>
</template>
<script>
// import mySkeleton from '@/components/my_skeleton/J-skeleton';
export default {
  // components: {
  //   mySkeleton
  // },
  data () {
    return {
      index_data: {},
      customer_data: [
        { id: 1, icon: "lr", name: "录入客户" },
        {
          id: 2,
          icon: "gh",
          name: "公海客户",
          type: 1,
          isname: "seas_customer",
        },
        { id: 3, icon: "wd", name: "我的客户", type: 2, isname: "my_customer" },
        // { id: 4, icon: "sj", name: "数据统计" },
      ],
      house_data: [
        {
          id: 1,
          icon: "gs",
          name: "房源",
          isname: "house",
          path: "/house/list?trade_type=0",
        },
        {
          id: 2,
          icon: "wdfy",
          name: "我的房源",
          isname: "my_house",
          path: "/house/list?is_owner=1",
        },
        {
          id: 3,
          icon: "lrfy",
          name: "录入房源",
          isname: "add_house",
          path: "/house/add",
        },
      ],
      new_data: [
        {
          id: 1,
          icon: "bbkh",
          name: "报备客户",
          path: "/report/report_client?currentTel=1",
        },
        {
          id: 2,
          icon: "wdkh",
          name: "我的报备",
          path: "/client/list?type=-1",
        },
      ],
      outbound_data: [
        {
          id: 1,
          icon: "myoutbound",
          name: "我的外呼",
          path: "/outbound/index",
        },
      ],
      top_menu_list: [],
      menu_list: [],
      website_id: "",
      menu_click_name: "",
      is_show_skeleton: false, // 是否显示骨架屏
    };
  },
  onLoad (options) {
    if (this.$isWxWork() == 'wxwork') {
      this.token = uni.getStorageSync("wxwork_token");
      if (!this.token) {
        return
      }
    } else {
      let website_id = options.website_id
      this.token = uni.getStorageSync("token" + website_id);
      if (!this.token) {
        localStorage.setItem('backUrl', location.href)
        location.href = "https://yun.tfcs.cn"
        // this.$router.push("https://yun.tfcs.cn")
        return
      }
      // 未登录中断请求
    }
    // if (this.$isWxWork() == 'wxwork') {
    //   if (isLoadEnd == 0) return
    // }
    if (options.website_id) {
      this.website_id = options.website_id;
    }
    this.is_show_skeleton = false;
    this.getUserInfo();
    this.getWebsiteMenu();
    // this.getCrmIndexData();
  },
  methods: {
    // 查询登录用户是否完善资料
    getUserInfo () {
      var that = this;
      this.$ajax.get("/qywx/common/query", {}, (res) => {
        if (res.statusCode === 200) {
          uni.setStorageSync("userInfo", JSON.stringify(res.data))

          // 没有联系方式，部门，角色弹出完善资料
          if (
            !res.data.user_name ||
            !res.data.phone ||
            !res.data.wx_work_department_id ||
            (res.data.roles && !res.data.roles.length) || !res.data.roles
          ) {
            this.$ajax.get("/admin/personnelMatters/checkDefaultDepartmentAndRole", {}, res => {

              console.log(res);
            })
            uni.showModal({
              title: "提示",
              content: "请完善资料",
              showCancel: false,
              success: function (res) {
                if (res.confirm) {
                  that.$navigateTo("/customer/perfect_data");
                }
              },
            });

          }
        }
      });
    },
    // 获取是否开启菜单
    getWebsiteMenu () {
      uni.showLoading();
      this.$ajax.get("/qywx/home/<USER>", {}, (res) => {
        uni.hideLoading();
        if (res.statusCode === 200) {
          let arr = res.data

          arr.map((item) => {
            item.data = "";
            if (item.menu_mode_id == 1) {
              item.data = this.new_data;
              item.desc = "报备总量";
            }
            if (item.menu_mode_id == 2) {
              item.data = this.customer_data;
              item.desc = "客户总量";
            }
            if (item.menu_mode_id == 3) {
              item.data = this.house_data;
              item.desc = "房源总量";
            }
            if (item.menu_mode_id == 4) {
              item.desc = "企微客户总量";
            }
            if (item.menu_mode_id == 5) {
              item.desc = "外呼总量";
              // if (this.website_id != 176) {
              //   item.is_show = 0
              // }
              item.data = this.outbound_data;
            }
            if (item.menu_mode_id == 6) {
              item.desc = "抖音总量";
              // if (this.website_id != 176) {
              //   item.is_show = 0
              // }
              // item.data = this.douyin_data;
            }
          });
          this.top_menu_list = arr.filter(item => item.is_top_show)
          this.menu_list = arr.filter(item => item.is_show);
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    getCrmIndexData () {
      this.$ajax.get("/qywx/home/<USER>", {}, (res) => {
        if (res.statusCode === 200) {
          this.index_data = res.data;
        } else {
          console.log(res.data.message);
        }
      });
    },
    clickUphold (e, e1) {
      //录入客户
      if (e1.menu_mode_id == 2) {
        this.menu_click_name = e.isname;
        if (e.id == 1) {
          this.$navigateTo("uphold?type=1");
          return;
        }
        this.getMenuClick((cal) => {
          if (cal === 200) {
            if (e.type == 1) {
              this.$navigateTo("seas_list");
            } else {
              this.$navigateTo("my_list");
            }
          }
        });
      } else if (e1.menu_mode_id == 3) {
        this.menu_click_name = e.isname;
        this.getMenuClick((cal) => {
          if (cal === 200) {
            this.clickHouse(e);
          }
        });
      } else if (e1.menu_mode_id == 1) {
        this.getClientToken(e);
      } else if (e1.menu_mode_id == 5) {
        this.$navigateTo(e.path);
      }
    },
    // 获取当前菜单是否可以点击
    getMenuClick (cal) {
      this.$ajax.get(
        `/admin/permission/checkPermission`,
        { name: this.menu_click_name },
        (res) => {
          if (res.statusCode === 200) {
            cal(200);
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        }
      );
    },
    getClientToken (e) {
      this.$ajax.get("/admin/admin_user/is_assistant", {}, (res) => {
        if (res.statusCode === 200) {
          uni.setStorageSync("token" + this.website_id, res.data.token);
          this.clickHouse(e);
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    clickHouse (e) {
      // if (!e.path) {
      //   uni.showToast({
      //     title: "暂未开放 敬请期待",
      //     icon: "none",
      //   });
      //   return;
      // }
      this.$navigateTo(e.path);
    },
  },
};
</script>
<style scoped lang="scss">
page {
  background: #f6f6f6;
}
.pad-12 {
  padding: 12px;
  color: #2e3c4e;
}
.c2 {
  color: #8a929f;
}
.ent {
  display: inline-block;
  position: fixed;
  right: 10px;
  bottom: 150px;
  image {
    width: 80px;
  }
}
.list {
  background: #fff;
  border-radius: 6px;
  padding: 12px 20px;
  .info {
    align-items: center;
    margin-right: 23px;
    image {
      width: 80rpx;
      height: 80rpx;
      border-radius: 10px;
    }
    view {
      margin-top: 5px;
      font-size: 11px;
      color: #8a929f;
    }
  }
}
.title {
  margin: 12px 0;
  font-size: 16px;
}
.sort {
  background: #fff;
  justify-content: space-between;
  padding-top: 24px;
  border-radius: 6px;
  uni-swiper {
    width: 100% !important;
    height: 100px;
  }
  .info {
    width: 33%;
    align-items: center;
    border-right: 1px solid #fff6f6f6;
    > text {
      font-size: 11px;
      color: #8a929f;
    }
    .num {
      margin-bottom: 12px;
      font-size: 24px;
      font-weight: 500;
      align-items: center;
    }
    .sheng {
      width: 12px;
      margin-left: 5px;
      height: 16px;
    }
  }
  .info:last-child {
    border: none;
  }
}
</style>
