<template>
  <view class="list">
    <view class="title-bar">
      <view class="search-tab">
        <my-search
          width="287px"
          :placeholder="
            is_select_index == 0 ? '请输入客户手机号码' : '请输入客户姓名'
          "
          @input="onInput"
        >
          <template v-slot:left>
            <picker
              style="font-size:24rpx;color:#0174ff;margin-right:20rpx"
              @change="bindPickerChange"
              @cancel="is_select = false"
              :value="is_select_index"
              :range="is_select_arr"
              range-key="description"
            >
              <view class="uni-input row" @click="is_select = true"
                >{{ is_select_arr[is_select_index].description }}
                <myIcon
                  :type="is_select ? 'xiala' : 'shangla'"
                  size="24rpx"
                  color="#0174ff"
                  style="margin-left:10rpx"
                ></myIcon
              ></view>
            </picker>
            <myIcon type="ic_sousuo3x1" color="#999"></myIcon>
          </template>
          <template v-slot:right>
            <text class="xinzeng" @click="newAdd">消息</text>
            <unibadge
              type="error"
              class="unibadge"
              v-if="parseInt(unread_msg) > 0"
              :text="unread_msg"
            ></unibadge>
          </template>
        </my-search>
      </view>
    </view>
    <view class="bottom-line tips row">
      <view class="row bottom-line-left">
        <text class="icon-baobei-ic_guanyu3x1 icon-baobei"></text>
        <text>审核经纪人报备的客户是否有效</text>
      </view>
      <view class="row" @click="showContent = !showContent">
        选择时间
        <myIcon
          :type="showContent ? 'xiala' : 'shangla'"
          size="30rpx"
          color="#0174ff"
          style="margin-left:10rpx"
        ></myIcon>
      </view>
    </view>
    <view class="time-box row" v-if="showContent">
      <view class="input-box row">
        <input
          id="start"
          class="input"
          disabled="true"
          type="text"
          v-model="deal_at_date_start"
          placeholder="开始时间"
          @click="$refs.picker_start.show()"
        />
        <text>至</text>
        <input
          id="end"
          class="input"
          type="text"
          disabled="true"
          v-model="deal_at_date_end"
          placeholder="结束时间"
          @click="$refs.picker_end.show()"
        />
      </view>
      <view class="search" @click="searchTime">
        搜索
      </view>
    </view>
    <tab-bar
      :tabs="report_cates"
      :nowIndex="
        report_cates.findIndex((item) => parseInt(item.value) == data_type)
      "
      fixed_top
      top="110"
      @click="onClickCate"
    ></tab-bar>

    <view class="not_reported row">
      <picker
        class="bottom-line-right"
        :range="range_project_list"
        mode="multiSelector"
        range-key="build_name"
        :value="pIndex"
        @change="filterProject"
        ref="picker"
        @columnchange="columnChange"
      >
        <!-- {{ range_project_list[pIndex].build_name }} -->
        <text>{{ rang_cate_name }}</text>
        <text>{{ rang_build_name }}</text>
        <text>{{ rang_follow_name }}</text>
      </picker>
      <picker
        class="bottom-line-right"
        :range="regionList"
        :mode="multiSelector"
        range-key="name"
        :value="rIndex"
        @columnchange="columnchangeRegion"
        @change="filterRegion"
        ref="picker"
      >
        <text> {{ range_region_name }}</text>
      </picker>
      <view class="myReceive" @click="myReceive" v-if="is_case !== 1">
        我领取的
      </view>
    </view>
    <view class="report-list">
      <my-customer
        v-for="(item, index) in customer_list"
        :key="index"
        :customer_item="item"
        :is_case="is_case"
        :build_type_list="build_type_list"
        @setInvalid="setInvalid"
        @setEffective="setEffective"
        @createFillPhone="createFillPhone"
        @getCustomer="getCustomer"
        @status="followStatus"
        @isVisit="isVisit"
        @sendMsg="sendMsg"
      ></my-customer>
    </view>
    <load-more :status="load_status"></load-more>
    <backTop :scrollTop="backTop.scrollTop"></backTop>
    <myPopup position="center" :show="isShowHide" @hide="isShowHide = false">
      <view class="popup-box">
        <textarea
          type="textarea"
          v-model="cancel_form.cancel_reason"
          placeholder="请填写无效原因（非必填）"
        >
        </textarea>
        <view class="row btn-box">
          <view class="btn" @click="uploadCancelReason(1)">
            提交
          </view>
          <view
            class="btn"
            style="background:#2fbef7"
            @click="uploadCancelReason(2)"
            >提交并短信提醒</view
          >
        </view>
        <view class="close" @click="close">
          x
        </view>
      </view>
    </myPopup>
    <!-- 弹出列表 -->
    <!-- 开始 -->
    <timePicker
      end="2030-12-30"
      mode="YMD"
      ref="picker_start"
      @confirm="confirmTimeStart"
    ></timePicker>
    <!-- 结束 -->
    <timePicker
      end="2030-12-30"
      mode="YMD"
      ref="picker_end"
      @confirm="confirmTimeEnd"
    ></timePicker>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import timePicker from "@/components/time-picker";
import mySearch from "@/components/my-search";
import myIcon from "@/components/my-icon";
import myCustomer from "@/components/customer_item";
import loadMore from "@/components/loadMore";
import backTop from "../components/backtop/components/back-top/back-top";
import myPopup from "@/components/myPopup";
import tabBar from "@/components/tabBar";
import unibadge from "@/components/uni-badge/uni-badge";
import { mapActions, mapState } from "vuex";
export default {
  components: {
    mySearch,
    myIcon,
    myCustomer,
    loadMore,
    backTop,
    myPopup,
    tabBar,
    timePicker,
    unibadge,
  },
  data() {
    return {
      project_aid: "",
      customer_list: [],
      data_type: "",
      params: {
        page: 1,
        customer_phone: "",
        status: -1,
        build_id: "",
        is_follow_up: "",
        project_user_id: "",
      },
      load_status: "",
      backTop: {
        src: "../components/backtop/static/back-top/top.png",
        scrollTop: 0,
      },
      customer_id: "",
      user_status: "",
      isShowHide: false,
      cancel_form: {
        customer_reported_id: "",
        cancel_reason: "",
        status: "",
        send_sms: "",
      },
      report_cates: [],
      statistics: [],
      report_tatol: 0,
      // 筛选项目的数组
      range_project_list: [],
      range_cate: [{ build_name: "", project_id: 0 }],
      range_build: [{ build_name: "", project_id: 0 }],
      range_follow: [{ build_name: "", project_id: 0 }],
      pIndex: [0, 0, 0],
      rIndex: [0, 0],
      rang_cate_name: "客户状态",
      rang_build_name: "项目名称",
      rang_follow_name: "领取状态",
      range_region_name: "公司区域",
      picker_params: {
        page: 1,
      },
      reach_bottom: "",
      deal_at_date_start: "",
      deal_at_date_end: this.$getTime("YMD"),
      url: "/client/customer/reported/search/project",
      showContent: false,
      path_type: "", // 是否是扫码报备路径
      is_select: false, // 点击切换选择搜索内容
      is_select_index: 0,
      is_select_arr: [
        {
          value: 0,
          description: "手机号码",
        },
        {
          value: 1,
          description: "客户姓名",
        },
      ],
      build_type_list: [],
      regionList: [],
      regionListAll: [],
      multiSelector: "selector",
      is_case: "",
    };
  },
  computed: {
    ...mapState(["unread_msg"]),
  },
  onLoad(options) {
    if (options.type) {
      this.path_type = options.type;
    }
    this.getRegionData(options.website_id);
    // 刚进入页面存入本地数据防止进入跟进不显示
    uni.setStorageSync("page_from", "project");

    this.getUserInfo({
      success: (res) => {
        this.project_aid = res.data.id;
        this.is_case = res.data.is_case; // 是否是案场方
        if (this.is_case === 1) {
          this.rang_follow_name = "分配状态";
        }
      },
    });
    if (options.customer_id) {
      this.customer_id = options.customer_id;
      this.getUserStatus();
    }
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "BUILD_CATEGORY":
            this.build_type_list = item.childs;
            break;
        }
      });
    });
    this.getCates();
    this.getDataList();
  },
  onShow() {},
  onPageScroll(e) {
    this.backTop.scrollTop = e.scrollTop;
  },
  methods: {
    ...mapActions(["getUserInfo", "getImToken"]),
    // 获取城市分类
    getRegionData(website_id) {
      this.$ajax.get(
        `/common/region/all?website_id=${website_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.regionListAll = res.data;
            var arr1 = res.data.filter((item) => {
              return item.pid == 0;
            });
            var arr2 = res.data.filter((item) => {
              return item.pid == arr1[0].id;
            });
            this.regionList = [arr1, arr2];
            this.multiSelector = "multiSelector";
          } else {
            uni.showToast({
              title: res.data.message || "获取区域失败",
              icon: "none",
            });
          }
        }
      );
    },
    filterRegion(e) {
      var region_0 = this.regionList[0][e.detail.value[0]].id,
        region_1 = this.regionList[1][e.detail.value[1]].id;
      if (!region_0) {
        this.params.region_0 = region_0;
      } else {
        this.params.region_0 = region_0;
        this.params.region_1 = region_1;
      }
      this.getDataList();
    },
    columnchangeRegion(e) {
      if (e.detail.column === 0) {
        var regin_0 = this.regionList[0][e.detail.value].id;
        this.params.region_0 = regin_0;
        var arr = this.regionListAll.filter((item) => {
          return item.pid === regin_0;
        });
        this.regionList[1] = arr;
      } else if (e.detail.column === 1) {
        this.range_region_name = this.regionList[1][e.detail.value].name;
      }
    },
    // getRegionData(website_id) {
    //   this.$ajax.get(
    //     `/common/region/all?website_id=${website_id}`,
    //     {},
    //     (res) => {
    //       if (res.statusCode === 200) {
    //         var arr = [];
    //         res.data.forEach((item, index) => {
    //           arr[index] = {
    //             id: item["id"],
    //             name: item["name"],
    //             pid: item["pid"],
    //             level: item["level"],
    //           };
    //         });
    //         this.regionList = this.formatRegion(arr).filter((item) => {
    //           return item.level == 0;
    //         });
    //       } else {
    //         uni.showToast({
    //           title: res.data.message || "获取区域失败",
    //           icon: "none",
    //         });
    //       }
    //     }
    //   );
    // },
    // // 格式化数组结构
    // formatRegion(data) {
    //   var map = {};
    //   data.forEach((item) => {
    //     map[item.id] = item;
    //   });
    //   var val = [];
    //   data.forEach((item) => {
    //     var parent = map[item.pid]; // 父级pid
    //     if (parent) {
    //       (parent.children || (parent.children = [])).push(item);
    //     } else {
    //       val.push(item);
    //     }
    //   });
    //   return val;
    // },

    // 客户扫码报备状态
    getUserStatus() {
      this.$ajax.get(
        `/client/customer/reported/query/project/id/${this.customer_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            let status = parseInt(res.data.status);
            if (res.data.length === 0) {
              uni.showToast({
                title: "您无权管理该项目的报备客户",
                icon: "none",
              });
              return;
            }
            /**
             * @date 2020/10/17
             * @description 扫码提示
             * 循环字典数据对比客户状态 0（待审核），1（已报备）调用到访方法
             * 其它状态直接输出显示不调用到访方法
             * */

            if (status === 0 || status === 1) {
              this.user_status = 2;
              this.changeStatus();
            } else {
              // REPORTED_STATUS
              this.$getDictionaryList("REPORTED_STATUS", {}, (res) => {
                if (res.statusCode === 200) {
                  var arr = res.data.data.find((item) => {
                    return parseInt(item.value) === status;
                  });
                  uni.showToast({
                    title: "该客户状态" + arr.description,
                    icon: "none",
                  });
                }
              });
            }
          } else {
            uni.showToast({
              title: res.data.message || "获取失败",
              icon: "none",
            });
          }
        }
      );
    },
    changeStatus() {
      if (!this.customer_id || !this.user_status) {
        uni.showToast({
          title: "扫码失败",
          icon: "none",
        });
      } else {
        this.$ajax.post(
          "/client/customer/reported/audit/status",
          {
            customer_reported_id: this.customer_id,
            status: this.user_status,
          },
          (res) => {
            var that = this;
            if (res.statusCode === 200) {
              if (that.is_case === 1) {
                // 案场方助理进行扫码确认提示是否分配
                uni.showModal({
                  title: "提示",
                  content: "是否需要分配客户",
                  confirmText: "确认",
                  success: (res) => {
                    if (res.confirm) {
                      that.$navigateTo(
                        `/project_broker/set_customer_dispatch?customer_id=${that.customer_id}`
                      );
                    } else {
                      uni.showToast({
                        title: "到访确认",
                      });
                      that.getDataList();
                    }
                  },
                });
              } else {
                uni.showToast({
                  title: "到访确认",
                });
                that.getDataList();
              }
            } else {
              uni.showToast({
                title: res.data.message || "扫码失败",
                icon: "none",
              });
            }
          }
        );
      }
    },
    getDataList() {
      // 判断地址栏类型 type == 2 显示已到访列表
      if (!this.path_type) {
        this.data_type = this.params.status;
      } else {
        this.data_type = this.path_type;
        this.params.status = this.data_type;
      }
      this.load_status = "loading";
      if (this.params.status === -1) {
        delete this.params.status;
      }
      if (this.params.build_id === "") {
        delete this.params.build_id;
      }
      if (this.params.page === 1) {
        this.customer_list = [];
      }
      if (this.params.is_follow_up === "") {
        delete this.params.is_follow_up;
      }
      if (this.params.is_dispatch === "") {
        delete this.params.is_dispatch;
      }
      if (this.params.project_user_id === "") {
        delete this.params.project_user_id;
      }
      if (this.params.customer_phone === "") {
        delete this.params.customer_phone;
      }
      if (this.params.customer_name === "") {
        delete this.params.customer_name;
      }

      this.$ajax.get(this.url, this.params, (res) => {
        this.load_status = "loadend";
        if (res.statusCode === 200) {
          let arr = res.data.data;
          this.statistics = res.data.statistics;
          // 计算客户总数
          this.statistics.forEach((item) => {
            this.report_tatol = this.report_tatol + parseInt(item.total);
          });
          this.customer_list = this.customer_list.concat(arr);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        } else {
          uni.showToast({
            title: res.data.message || "获取失败",
            icon: "none",
          });
        }
      });
    },
    isVisit(customer) {
      this.$navigateTo(
        `/report/add_schedule?id=${customer.id}&customer_status=2&is_visit=1`
      );
    },
    followStatus(status, id) {
      this.$navigateTo(`/project_broker/status_step?id=${id}&status=${status}`);
    },
    newAdd() {
      // this.$navigateTo("/report/report_client?currentTel=1");
      uni.switchTab({
        url: "/index/message",
      });
    },
    getCates() {
      this.$getDictionaryList("REPORTED_STATUS", {}, (res) => {
        if (res.statusCode === 200) {
          setTimeout(() => {
            this.report_cates = [
              { value: -1, description: "全部" },
              ...res.data.data,
            ];
            this.getFilterProject();
            for (var i = 0; i < this.report_cates.length; i++) {
              this.report_cates[i].statistics = "";
              this.statistics.map((e) => {
                if (this.report_cates[i].value == e.status) {
                  this.report_cates[i].statistics = e.total;
                }
                // if (this.report_cates[i].value === -1) {
                //   this.report_cates[i].statistics = this.report_tatol;
                // }
              });
            }
          }, 500);
        }
      });
    },
    onClickCate(e) {
      this.params.status = parseInt(e.value);
      this.params.is_follow_up = "";
      this.params.project_user_id = "";
      this.params.page = 1;
      this.getDataList();
    },
    // 设置客户报备无效
    setInvalid(status, id) {
      if (id) {
        this.isShowHide = true;
        this.cancel_form.customer_reported_id = id;
        this.cancel_form.status = 10;
      }
    },
    close() {
      this.isShowHide = false;
    },
    uploadCancelReason(type) {
      this.cancel_form.send_sms = type == 1 ? "" : 1;
      this.$ajax.post(
        "/client/customer/reported/audit/status",
        this.cancel_form,
        (res) => {
          if (res.statusCode === 200) {
            uni.showToast({
              title: "修改成功",
            });
            this.getDataList();
            this.isShowHide = false;
          } else {
            uni.showToast({
              title: res.data.message || "修改失败",
              icon: "none",
            });
          }
        }
      );
    },
    // 设置客户报备有效
    setEffective(status, id) {
      if (id) {
        this.$ajax.post(
          "/client/customer/reported/audit/status",
          {
            customer_reported_id: id,
            status: 1,
          },
          (res) => {
            if (res.statusCode === 200) {
              uni.showToast({
                title: "修改成功",
              });
              this.getDataList();
            } else {
              uni.showToast({
                title: res.data.message || "修改失败",
                icon: "none",
              });
            }
          }
        );
      }
    },
    onInput(e) {
      this.$debounce(this.onInputDebounce, 500)(e);
    },
    onInputDebounce(e) {
      this.params.page = 1;
      this.is_select_index === 0
        ? ((this.params.customer_phone = e), (this.params.customer_name = ""))
        : ((this.params.customer_name = e), (this.params.customer_phone = ""));
      if (e.length === 0) {
        this.getCates();
      }
      this.$ajax.get(
        "/client/customer/reported/search/project",
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.statistics = res.data.statistics;
            // 计算客户总数
            this.statistics.forEach((item) => {
              this.report_tatol = this.report_tatol + parseInt(item.total);
            });
            this.customer_list = res.data.data;
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
            }
          }
        }
      );
    },
    createFillPhone(e) {
      this.$ajax.post(
        "/client/customer/reported/fill/customer_phone",
        e,
        (res) => {
          if (res.statusCode === 200) {
            uni.showToast({
              title: "已补全",
            });
            this.params.page = 1;
            this.getDataList();
          } else {
            uni.showToast({
              title: res.data.message || "补全失败",
              icon: "none",
            });
          }
        }
      );
    },
    // 筛选项目
    filterProject(e) {
      this.pIndex = e.detail.value;
      this.params.status = this.range_project_list[0][this.pIndex[0]].value;
      this.params.build_id = this.range_project_list[1][
        this.pIndex[1]
      ].build_id;
      if (this.is_case === 1) {
        this.params.is_dispatch = this.range_project_list[2][
          this.pIndex[2]
        ].build_id;
      } else {
        this.params.is_follow_up = this.range_project_list[2][
          this.pIndex[2]
        ].build_id;
      }
      this.rang_build_name = this.range_project_list[1][
        this.pIndex[1]
      ].build_name;
      this.rang_follow_name = this.range_project_list[2][
        this.pIndex[2]
      ].build_name;
      this.params.project_user_id = "";
      this.params.page = 1;
      this.getCates();
      this.getDataList();
    },
    getFilterProject() {
      this.$ajax.get("/client/project/list/my", {}, (res) => {
        if (res.statusCode === 200) {
          let arr = this.report_cates;
          this.range_cate = JSON.parse(
            JSON.stringify(arr).replace(/description/g, "build_name")
          );
          this.range_build = [
            { build_name: "全部", build_id: "" },
            ...res.data.data,
          ];
          this.range_follow =
            this.is_case === 1
              ? [
                  { build_name: "全部", build_id: "" },
                  { build_name: "未分配", build_id: "0" },
                  { build_name: "已分配", build_id: "1" },
                ]
              : [
                  { build_name: "全部", build_id: "" },
                  { build_name: "未领取", build_id: "0" },
                  { build_name: "已领取", build_id: "1" },
                ];
          this.range_project_list = [
            this.range_cate,
            this.range_build,
            this.range_follow,
          ];
        } else {
          uni.showToast({
            title: res.data.message || "获取项目列表失败",
            icon: "none",
          });
        }
      });
    },
    // 领取客户
    getCustomer(id) {
      this.$ajax.get(`/client/customer/reported/follow_up/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "领取成功",
          });
          this.getDataList();
        } else {
          uni.showToast({
            title: res.data.message || "领取失败",
            icon: "none",
          });
        }
      });
    },
    myReceive() {
      this.params.project_user_id = this.project_aid;
      this.params.status = -1;
      this.params.page = 1;
      this.getDataList();
    },
    columnChange(e) {
      if (e.detail.column === 1) {
        if (e.detail.value === this.range_build.length - 1) {
          if (this.reach_bottom === 0) {
            uni.showToast({
              title: "没有更多了",
              icon: "none",
            });
            return;
          }
          this.picker_params.page++;
          this.$ajax.get(
            "/client/project/list/my",
            this.picker_params,
            (res) => {
              if (res.statusCode === 200) {
                this.range_build = this.range_build.concat(res.data.data);
                this.reach_bottom = res.data.data.length;
              }
            }
          );
        }
      }
    },
    // 选择开始时间
    confirmTimeStart(e) {
      this.deal_at_date_start = e.result;
    },
    //选择结束时间
    confirmTimeEnd(e) {
      this.deal_at_date_end = e.result;
    },
    searchTime() {
      if (!this.deal_at_date_start) {
        uni.showToast({
          title: "请选择开始日期",
          icon: "none",
        });
        return;
      } else {
        if (this.deal_at_date_start && this.deal_at_date_end) {
          this.url = `/client/customer/reported/search/project?updated_date[start]=${this.deal_at_date_start}&updated_date[end]=${this.deal_at_date_end}`;
        }
      }
      this.params.status = -1;
      this.getCates();
      this.getDataList();
    },
    sendMsg(item) {
      this.getImToken();
      this.$navigateTo(`/im_list/msg_detail?to_id=${item.u_id}`);
    },
    bindPickerChange(e) {
      this.is_select_index = e.detail.value;
      this.is_select = false;
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.params.status = -1;
    this.getCates();
    this.getDataList();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getCates();
    this.getDataList();
  },
};
</script>

<style scoped lang="scss">
.icon-baobei-ic_guanyu3x1 {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22icon-baobei-ic_guanyu3x1%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M512%2056.888889a455.111111%20455.111111%200%201%201%200%20910.222222A455.111111%20455.111111%200%200%201%20512%2056.888889z%20m0%20113.777778a341.333333%20341.333333%200%201%200%200%20682.666666A341.333333%20341.333333%200%200%200%20512%20170.666667z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M512%20341.333333m-56.888889%200a56.888889%2056.888889%200%201%200%20113.777778%200%2056.888889%2056.888889%200%201%200-113.777778%200Z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M455.111111%20455.111111h113.777778v284.444445H455.111111z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
page {
  background: #eee;
}
.list {
  .time-box {
    background: #fff;
    padding: 0 48rpx;
    line-height: 100rpx;
    height: 100rpx;
    justify-content: space-between;
    border-top: 1rpx solid #eee;
    .input-box {
      width: 250px;
      margin: 20rpx 0;
      border-radius: 4px;
      align-items: center;
      background-color: #eee;
      padding: 0 16rpx;
      input {
        font-size: 28rpx;
        text-align: center;
        background-color: #eee;
        border: 1rpx solid #f3f3f3;
        height: 32rpx;
        width: 284rpx;
        border: none;
      }
    }
    .search {
      color: #0174ff;
    }
  }
  .title-bar {
    background: #fff;
    .xinzeng {
      position: absolute;
      right: 48rpx;
      color: #0174ff;
    }
    .unibadge {
      position: absolute;
      right: 20rpx;
      top: 10rpx;
    }
  }
  .search-tab {
    background: #fff;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 9;
  }
  .screen-tab {
    flex-direction: row;
    width: 100%;
    height: 80rpx;
    background-color: #fff;
    .screen-tab-item {
      flex: 1;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      text {
        margin-right: 10rpx;
        transition: 0.3s;
      }
    }
  }
  .bottom-line {
    margin-top: 20rpx;
    padding: 24rpx 48rpx;
    background: #fff;
    justify-content: space-between;
    .bottom-line-left {
      align-items: center;
      justify-content: flex-start;
    }
    .bottom-line-right {
      color: #fa6a6e;
    }
  }
  .bottom-line::after {
    height: 0;
  }
  .tips {
    align-items: center;
    margin-top: 180rpx;
    color: #0174ff;
  }

  // 弹出
  .popup-box {
    width: 600rpx;
    height: 600rpx;
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    textarea {
      width: 100%;
      height: 400rpx;
      background: #f3f2f7;
      padding: 24rpx;
    }
    .btn-box {
      justify-content: space-between;
    }
    .btn {
      margin-top: 40rpx;
      width: 46%;
      border-radius: 10rpx;
      padding: 20rpx 20rpx;
      align-items: center;
      color: #fff;
      font-size: 28rpx;
      background: #708efc;
    }
    .close {
      color: #999;
      width: 50rpx;
      height: 50rpx;
      background: #fff;
      border-radius: 50%;
      font-size: 38rpx;
      position: absolute;
      align-items: center;
      top: -10px;
      right: -10px;
    }
  }
}
.not_reported {
  margin-top: 20rpx;
  padding: 0 48rpx;
  align-items: center;
  // justify-content: flex-end;
  text {
    border-radius: 15px;
    color: #0174ff;
    text-align: center;
    line-height: 50rpx;
    width: auto;
    margin-right: 30rpx;
    &:nth-child(2) {
      color: #fa6a6e;
    }
    &:nth-child(3) {
      color: #708efc;
    }
  }
  .myReceive {
    color: #00ad65;
  }
}
</style>
