
export  const formatDate = (date,params)=>{
    let  dateNew   = new Date(date)
    let year = dateNew.getFullYear()
    let month = (dateNew.getMonth()+1)+''
    let day = dateNew.getDate() +""
    let hour = dateNew.getHours()+''
    let minute = dateNew.getMinutes()+''
    let second = dateNew.getSeconds()+''
    if (params.length==19){
        return  year+'-' +month.padStart(2) +'-' +day.padStart(2)+' '+ hour.padStart(2)+':'+minute.padStart(2)+':'+second.padStart(2)
    }
    else if (params.length>=12){
        return  year+'-' +month.padStart(2) +'-' +day.padStart(2)+' '+ hour.padStart(2)+':'+minute.padStart(2)
    }else {
            return  year+'-' +month.padStart(2) +'-' +day.padStart(2)
        
    }


    // console.log(dateNew);
    // console.log(dateNew.getFullYear());

}