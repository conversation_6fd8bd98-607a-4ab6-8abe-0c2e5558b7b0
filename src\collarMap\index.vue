<template>
    <view class="container">
        <view class="main flex-box">
            <!-- 头部背景图 -->
            <view style="margin: -15px -15px 15px; z-index: 80;" id="top_image">
                <image :src="cusMapDetail.top_pic" mode="widthFix" @load="loadTopImg" style="width: 100%; height: 100%;" />
            </view>
            <movable-area class="movable-area"  :style="{height:top_img_height}">
                <movable-view
                    class="movable-view"
                    :style="{height:(map_box_height)+'px'}"
                    :scale="cusMapDetail.receive_type == 3 ? true : false"
                    :scale-min="1"
                    :scale-value="scaleValue"
                    :scale-max='scaleMax'
                    direction="all"
                    @scale="scales"
                    :disabled="cusMapDetail.receive_type != 3 ? true : false"
                >
                    <!-- 地图图片 -->
                    <view id="maps-box" :style="cusMapDetail.receive_type != 3 ? 'padding-bottom: 200rpx;' : ''">
                        <view 
                            v-for="(item, index) in cusMapDetail.maps" 
                            :key="item.id"
                            style="margin-bottom: 10rpx;"
                            @touchstart="touchStart(item.url, item.id)"
                            @touchmove="touchMove"
                            @touchend="touchEnd"
                        >
                            <image :id="'maps-img'+index" :src="item.url" @load="loadImg" mode="widthFix" style="width: 100%" />
                        </view>
                    </view>
                </movable-view>
            </movable-area>
        </view>
        <view class="footer" id="footer-box">
            <view class="flex-row footer-box">
                <view class="footer-left flex-row">
                    <!-- 榜单 -->
                    <view class="flex-box bank" @click="toRank">
                        <image class="msg" :src="'/yidongduan/collarMap/<EMAIL>' | imageFilter('m_80')" />
                        <text class="footer-text">榜单</text>
                    </view>
                </view>
                <!-- 获取  || _this.cusMapDetail.receive_type == 3-->
                <view class="footer-right flex-1 flex-row" v-if = '(sid > 0 && cusMapDetail.receive_type==3) ||  cusMapDetail.receive_type!=3'>
                    <button
                        class="get flex-1"
                        @click="getStatus"
                    >
                        获取
                    </button>
                </view>
            </view>
        </view>
        <view class="keep-time" v-if="show_time">
            <view class="schedule">
                <progress :percent="progressPercent" stroke-width="12" activeColor="#2D84FB" backgroundColor="#fff"></progress>
            </view>
            <view class="text-top">
                <text>浏览{{ nowResidue_time }}秒</text>
            </view>
            <view class="text-bottom">
                <text>获取领地图</text>
            </view>
        </view>
        <myPopup :show="isShowModal" position="top" @hide="isShowModal = false">
            <view class="model-wraper">
                <view class="modal-title">
                    网盘地址：{{ cusMapDetail.netdisk_url }}
                </view>
                <view class="modal-body">
                    提取码：{{ cusMapDetail.netdisk_code }}
                </view>
                <view class="flex-row">
                    <view class="btn cancel" @click="handleCancel">取消</view>
                    <view class="btn confirm" @click="handleConfirm">复制</view>
                </view>
            </view>
        </myPopup>
        <myPopup :show="showScalePop" position='center' height="420rpx" @hide="showScalePop = false">
            <view class="model-wraper1 scale_pop">
                <view class="modal-title">
                    {{shareInfo.msg}}
                </view>
                <view class="modal-body no_bottom">
                   <view class="modal-body-item flex-row">
                        <view class="modal-body-item-name">姓名</view>
                        <view class="modal-body-item-value">{{shareInfo.admin_user&&shareInfo.admin_user.user_name ||''}}</view>
                   </view>
                   <view class="modal-body-item flex-row">
                        <view class="modal-body-item-name">电话</view>
                        <view class="modal-body-item-value">{{shareInfo.admin_user&&shareInfo.admin_user.phone ||''}}</view>
                   </view>
                </view>
                <view class="flex-row flex-1">
                    <view class="btns confirm" @click="makePhoneCall">拨打电话</view>
                </view>
            </view>
        </myPopup>
    </view>
</template>
<script>
import myPopup from "@/components/myPopup";
export default {
    components: {
        myPopup,
    },
    data() {
        return {
            website_id: '', // 当前站点id
            nowmap_id: '', // 当前要下载地图图片的id
            nowplugin_id: 6, // 当前地图插件id
            progressPercent: 0, // 进度条初始百分比
            nowResidue_time: '', // 剩余等待时间
            interval: null, // 用于保存setInterval的ID
            // 获取获客记录接口传参
            recoed_params: {
                plugin_id: '', // 地图插件id
                name: '', // 客户名称
                phone: '', // 客户手机号
            },
            cusMapDetail: {}, // 客户端获取地图详情
            // 更新用户离开和停留时间接口参数
            timeUpdate: {
                browse_time: 0, // 停留时间，多少秒
                login_out_time: '', // 离开时间，时间戳，单位秒
                plugin_id: '', // 地图插件id
                access_time: '', // 用户访问时间
            },
            qrcodeUrl: 'https://t7.baidu.com/it/u=2405382010,1555992666&fm=193&f=GIF',
            timer: '', // 用户点击获取后的计时器
            wait_timer: '', // 用户在限制浏览时间中的计时器
            try_timer: '', // 用户在授权登录后重新进入页面的计时器
            // inner_user_id: '', // 内部成员id
            downLoadUrl: '', // 当前要保存图片的url
            is_download: false, // 控制浏览限制,图片是否可以下载
            sid: 0, // 分享者id
            isShowModal: false, // 显示网盘地址
            allow_type: '', // 当前领取状态 -1：授权用户信息，还是填写表单 1：可以领取地图 2：已经领取过了
            is_close: false,
            browse_time:0,
            isLoad: false,
            scaleValue: 1,
            offset_x: 0,
            offset_y: 0,
            map_box_height: '',
            top_img_height:0,
            footer_box_height: 0,
            count: 0,
            showScalePop:false,
            shareInfo:{
                admin_user:{},
                msg:""
            },
            top_box_height:0,
            allow_type3 :true
        }
    },
    onLoad(options) {
        this.website_id = this.$store.state.website_id; 
        let currentDate = Date.now();
        let currentDateSeconds = Math.floor(currentDate / 1000)
        uni.setStorageSync('currentDateSeconds', currentDateSeconds); // 添加当前登录时间
        this.isLoad =true
        const getSceneParams = function(scene){
            var sceneParams = {};
            var params = scene.split("&");
            for(var i = 0; i < params.length; i ++) {
                sceneParams[params[i].split("=")[0]] = unescape(params[i].split("=")[1]);
            }
            return sceneParams;
        }
        if (options.scene) {
            console.log(options.scene,"scene");
            const params = getSceneParams(decodeURIComponent(options.scene))
            // if(params.sId){
            //     let share_params = params.sId.split('_')
            //         this.sid = share_params[0]
            //     }
            if (params.id) {
                let idAndShareIdArr  = params.id.split("_")
                this.nowplugin_id = idAndShareIdArr[0]; // 赋值当前地图插件id
                if(idAndShareIdArr.length>1){
                   this.sid =  idAndShareIdArr[1] 
                }
            }
           
            this.init();
            return
        }
        if(options.shareId){
             this.sid = options.shareId
             
        }
        if(options.id){
             this.nowplugin_id = options.id
        }
        if(options.ftime){
             this.ftime = options.ftime
        }
        this.init();
    },
    onReady() {
        this.onImageTouchStart();  // 开启长按菜单限制
    },
    mounted() {
     
    },
    beforeDestroy() {
        
        if(!this.isLoad) {
            return 
        }
        this.stopCountdown(); // 页面销毁前停止计时
       
    },
    computed:{
        show_time(){
            return  (this.browse_time < parseInt(this.cusMapDetail.browse_time) * 60 && this.cusMapDetail.browse_auth == 1) && !this.is_close
        },
        scaleMax(){
            if (this.cusMapDetail.receive_type==3 && this.allow_type3==1){
                return 20
            }else {
                return 1
            }
        },
    },
    onHide() {
        // this.getleaveTime(); // 获取离开页面时间戳
    },
    onUnload() {
        clearTimeout(this.timer); // 清空定时器
        document.removeEventListener('contextmenu', this.prevent); // 解除长按菜单限制
    },
    methods: {
        // 初始化
        init() {
            this.getMapDetail(this.nowplugin_id); // 客户端获取地图详情
        },
        // 控制浏览时间
        startCountdown() {
            let waiting_time = parseInt(this.cusMapDetail.browse_time) * 60; // 需要浏览等待的时间
            this.interval = setInterval(() => {
                if(this.browse_time < waiting_time) {
                    this.browse_time++; // 减少所需浏览时间
                    this.nowResidue_time = waiting_time - parseInt(this.browse_time); // 剩余等待时间
                    this.progressPercent = (this.browse_time / waiting_time) * 100; // 更新进度条百分比
                } else {
                    this.stopCountdown(); // 浏览时间为0时停止计时
                    this.is_download = true; // 关闭浏览限制
                }
            }, 1000);
        },
        // 停止浏览计时
        stopCountdown() {
            clearInterval(this.interval);
        },
        // 阻止显示长按菜单默认行为
        prevent(event) {
            console.log("阻止默认行为")
            event.preventDefault(); // 阻止默认的上下文菜单
        },
        checkStatus(callback, callback1,callback2) {
            console.log("触发长按事件")
            // is_download: 是否还在浏览限制
            if(this.is_download) {
                // 判断当前领取状态
                this.$ajax.get(`/client/map_plugin/is_allow_receive/${this.nowplugin_id}`, {}, (res) => {
                    if(res.statusCode == 200) {
                        this.allow_type=this.allow_type3 = res.data.allow_type;
                        // -1 去授权 1 可以领取 2 领取过了 
                        if(res.data.allow_type == -1){
                            console.log("去登录")
                        } else if (res.data.allow_type == 1) {
                            callback&&callback()
                        } else if (res.data.allow_type == 2) {
                            this.saveMapClickRecode(); // 保存地图点击记录
                            callback2&& callback2(res.data)
                            
                        }
                    //    return  this.allow_type = res.data.allow_type; // 赋值当前领取状态
                    }else if (res.statusCode==401) {
                        console.log("去登录")
                    }
                }, (err) => {
                    this.is_clicking = false;
                })
            } else {
                callback1 && callback1()
            } 
        },
       
        // 客户端获取地图详情
        getMapDetail(id) {
            this.$ajax.get(`/client/map_plugin/get_maps/${id}`, {share_id: this.sid}, (res) => {
                if(res.statusCode == 200) {
                    this.cusMapDetail = res.data;
                    uni.setNavigationBarTitle({
                        title: this.cusMapDetail.title
                    })
                    // var pages = getCurrentPages() //获取加载的页面
                    // var currentPage = pages[pages.length - 1] //获取当前页面的对象
                    // var url = '';
                    // if(currentPage) {
                    //     url = currentPage.route; //当前页面url
                    // } else {
                    //     url = 'collarMap/index';
                    // }
                    // let is_manger = 0
                    // if(this.$store.state.user_info.erp_user_id>0 || this.$store.state.user_info.inner_user_id>0){
                    //     is_manger =1 
                    // }
                    // url+=`?id=${this.nowplugin_id}&shareId=${is_manger==1?this.$store.state.user_info.id:(this.sid||0)}`
                    // this.share ={
                    //     title: this.cusMapDetail.share_title,
                    //     content: this.cusMapDetail.share_desc,
                    //     pic: this.cusMapDetail.share_pic,
                    //     path: url
                    // }
                    // browse_auth浏览限制：0:关闭，1:开启
                    let show_time = uni.getStorageSync("colllarMap"+this.nowplugin_id) && !this.is_new_login
                    if(this.cusMapDetail.browse_auth == 1 && !show_time) {
                        this.startCountdown(); // 控制浏览时间
                    } else {
                        this.is_close = true;
                        this.is_download = true; // 关闭浏览限制
                    }
                    if(this.sid>0){
                        this.getShareInfo()
                    }
                    this.$nextTick(() => {
                        this.mapInit(); // 初始化地图
                    })
                    uni.setStorageSync("colllarMap"+this.nowplugin_id,1)
                } else if(res.statusCode == 401) {
                    console.log("去登录")
                }
            }, (err) => {})
        },
        // 更新用户离开和停留时间
        updateUserOperation() {
            this.timeUpdate.plugin_id = this.nowplugin_id; // 赋值当前地图id
            let params = Object.assign({},this.timeUpdate)
            const browse_time = (params.login_out_time - params.access_time) * 1000;
            params.browse_time = Math.floor(browse_time / 1000);
            console.log(params,"params时间")
            if (params.access_time > params.login_out_time ) return 
            this.$ajax.post("/client/map_plugin/update_times",params, (res) => {
                if(res.statusCode == 200) {
                    console.log(res.data, "更新用户离开和停留时间")
                }
            }, (err) => {})
        },
        // 获取离开页面时间戳，单位秒
        getleaveTime() {
            const currentTime = Date.now();
            this.timeUpdate.login_out_time = Math.floor(currentTime / 1000);
            let currentDateSeconds = uni.getStorageSync('currentDateSeconds');
            this.timeUpdate.access_time = currentDateSeconds.toString();
            this.updateUserOperation(); // 更新用户离开和停留时间
        },
        // 检测地图领取状态
        getStatus() {
            let that = this;
            // is_download: 是否还在浏览限制
            if(this.is_download) {
                if(this.is_clicking) { // 防抖
                    return
                }
                this.is_clicking = true;
                // 判断当前领取状态
                this.$ajax.get(`/client/map_plugin/is_allow_receive/${this.nowplugin_id}`, {}, (res) => {
                    if(res.statusCode == 200) {
                        console.log(res.data,"领取状态");
                        uni.setStorageSync('tryGet', true); // 记录用户领取
                        this.allow_type=this.allow_type3 = res.data.allow_type; // 赋值当前领取状态
                        if(this.allow_type == -1) {
                            console.log("去登录")
                        }
                        if(this.allow_type == 1) {
                            if(this.cusMapDetail.receive_type == 1 ) {
                                uni.showToast({
                                    title: "长按图片即可保存",
                                    icon: "none",
                                    duration: 2000,
                                })
                                // this.customerGetMap(); // 客户以领取地图
                            } else if(this.cusMapDetail.receive_type == 3) {
                                console.log(12333);
                                this.showScalePop =true
                            } else {
                                that.isShowModal = true;
                                that.customerGetMap(); // 客户以领取地图
                            }
                        } else if(this.allow_type == 2) {
                            if (this.cusMapDetail.receive_type == 3) {
                                 this.showScalePop =true
                            }else {
                                uni.showToast({
                                    title: res.data.msg,
                                    icon: "none"
                                })
                            }
                            
                        }
                        this.is_clicking = false;
                    }else if (res.statusCode==401) {
                        console.log("去登录")
                    }
                }, (err) => {
                    this.is_clicking = false;
                })
            } else {
                uni.showToast({
                    title: `再浏览${this.nowResidue_time}秒可以操作哦`,
                    icon: "none",
                    duration: 2500
                })
            }
        },
        getShareInfo(){
            this.$ajax.get(`/client/map_plugin/get_share_info/${this.sid}`,{},res=>{
                console.log(res,"share");
                if(res.statusCode==200){
                    if(Array.isArray(res.data.admin_user)){
                       res.data.admin_user = {} 
                    }
                   this.shareInfo = res.data
                //    this.showScalePop =true
                }else if(res.statusCode==401) {
                    console.log("去登录")
                }
                
            },()=>{

            })
        },
        // 领取地图
        GetMap() {
            this.recoed_params.plugin_id = this.nowplugin_id; // 赋值当前地图插件id
            this.$ajax.post("/client/map_plugin/receive_map", this.recoed_params, (res) => {
                if(res.statusCode == 200) {
                    console.log(res.data,"领取地图接口")
                }
            }, (err) => {})
        },
        // 开始按下触碰
        touchStart(url, id) {
            let _this = this;
            _this.nowmap_id = id;
            clearTimeout(_this.timer); //再次清空定时器，防止重复注册定时器
            _this.timer = setTimeout(() => {
                this.checkStatus(function() {
                    if((_this.is_download && _this.allow_type == 1) && (_this.cusMapDetail.receive_type == 1 )) {
                        // 赋值当前要下载图片的id;
                        _this.saveImage(url)
                        _this.saveMapClickRecode(); // 保存地图点击记录
                    }else if ( _this.cusMapDetail.receive_type == 3){
                         _this.saveMapClickRecode(); // 保存地图点击记录
                    } else {
                        _this.saveMapClickRecode(); // 保存地图点击记录
                    }
                }, function() {
                    _this.saveMapClickRecode(); // 保存地图点击记录
                },function(res){
                    if(_this.cusMapDetail.receive_type == 3 ){
                        if (_this.sid){
                            _this.showScalePop = true
                        }else if(_this.shareInfo.msg) {
                           uni.showToast({
                                title:_this.shareInfo.msg,
                                icon:'none'
                           })
                        } else {
                            uni.showToast({
                                title: res.msg,
                                icon:'none'
                           })
                        }
                        return 
                    }
                    uni.showToast({
                        title: res.msg,
                        icon: "none"
                    })
                })
            }, 1000);
        },
        // 结束触碰
        touchEnd() {
            clearTimeout(this.timer); //手指离开
        },
        // 触碰滑动
        touchMove() {
            clearTimeout(this.timer);
        },
        makePhoneCall(){
            uni.makePhoneCall({
                phoneNumber:this.shareInfo&& this.shareInfo.admin_user&& this.shareInfo.admin_user.phone
            })
            setTimeout(() => {
                this.showScalePop =false
            }, 300);
        },
        getCode (){
            uni.login({
                success: (res) => {
                    console.log(res);
                    this.weixinCode = res.code;
                },
                fail: (err) => {
                    console.log(err);
                }
            })
        },
        // 保存图片
        saveImage(Url) {
            uni.showActionSheet({
                itemList: ['保存图片'],
                success: function () {
                    var blob=new Blob([''], {type:'application/octet-stream'}); //二进制大型对象blob
                    var url = URL.createObjectURL(blob); //创建一个字符串路径空位
                    var a = document.createElement('a'); //创建一个 a 标签
                    a.href = Url;  //把路径赋到a标签的href上
                    a.download = Url.replace(/(.*\/)*([^.]+.*)/ig,"$2").split("?")[0]; // 正则表达式 图片文件名分离
                    var e = new MouseEvent('click', ( true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null)); // 创建鼠标事件并初始化
                    a.dispatchEvent(e); // 执行保存到本地
                    URL.revokeObjectURL(url); //释放路径
                },
                fail: function () {
                    return
                }
            });
		},
        back(){
            this.$navigateTo("/build/detail?id=1")
        },
        // 保存图片到系统相册
        downLoadImg() {
            let that = this;
			uni.showLoading({
				title: '加载中'
			});
			uni.downloadFile({
				url: this.downLoadUrl,
				success: (res) => {
					uni.hideLoading();
					if (res.statusCode === 200) {
						uni.saveImageToPhotosAlbum({
							filePath: res.tempFilePath,
							success: function() {
								uni.showToast({
									title: "保存成功",
									icon: "none"
								});
                                that.customerGetMap(); // 客户以领取地图
							},
							fail: function() {
								uni.showToast({
									title: "保存失败，请稍后重试",
									icon: "none"
								});
							}
						});
					}
				},
				fail: (err) => {
					uni.showToast({
						title: "失败啦",
						icon: "none"
					});
				}
			})
		},
        // 保存地图点击记录
        saveMapClickRecode() {
            this.$ajax.post("/client/map_plugin/map_click", {map_id: this.nowmap_id, plugin_id: this.nowplugin_id}, (res) => {
                if(res.statusCode == 200) {
                    console.log('触发点击地图记录');
                }
            }, (err) => {})
        },
        // 客户领取地图接口
        customerGetMap() {
            this.$ajax.get(`/client/map_plugin/receive_map/${this.nowplugin_id}`, {}, (res) => {
                if(res.statusCode == 200) {
                    console.log(res.data);
                }
            }, (err) => {})
        },
        // 关闭网盘地址模态框
        handleCancel() {
            this.isShowModal = false;
        },
        // 复制网盘地址
        handleConfirm() {
            let copy_value = '网盘地址' + this.cusMapDetail.netdisk_url + '提取码' + this.cusMapDetail.netdisk_code;
            this.$copyText(copy_value, () => {
                uni.showToast({
                    title: "复制成功",
                    icon: "none",
                });
                this.isShowModal = false; // 关闭网盘地址模态框
            });
        },
        toRank(){
            console.log("查看榜单");
            this.$navigateTo(`/collarMap/bankList?id=${this.nowplugin_id}`)
        },
        scales(){
            if(this.scale) return 
            this.scale =true
            if(this.cusMapDetail.receive_type==3){
                this.$ajax.get(`/client/map_plugin/is_allow_receive/${this.nowplugin_id}`, {}, res=>{
                     if(res.statusCode == 200) {
                        this.allow_type= this.allow_type3 = res.data.allow_type;
                     }else if(res.statusCode == 401){
                        console.log("去登录")
                     }
                     setTimeout(() => {
                         this.scale =false
                     }, 2000);
                },()=>{setTimeout(() => {
                    this.scale =false
                }, 2000);})
            }

        },
        mapInit() {
            this.$nextTick(() => {
                // 获取底部操作按钮高度
                const Footer_BOX = uni.createSelectorQuery().in(this).select("#footer-box");
                Footer_BOX.boundingClientRect((data) => {
                    this.footer_box_height = data.height; // 赋值容器高度
                }).exec()
            })
        },
        // checkAllImagesLoaded(imagePaths) {
        //     const promises = imagePaths.map((imagePath) => {
        //         return new Promise((resolve, reject) => {
        //         uni.getImageInfo({
        //             src: imagePath,
        //             success: () => {
        //             resolve();
        //             },
        //             fail: (error) => {
        //             console.error('Image load failed:', error);
        //             resolve(); // Resolve even on fail, so Promise.all won't get stuck if one image fails
        //             },
        //         });
        //         });
        //     });

        //     Promise.all(promises)
        //     .then(() => {
        //     // 所有图片加载完成
        //     this.isLoading = false;
        //     })
        //     .catch((error) => {
        //     console.error('Error loading images:', error);
        //     // 处理加载失败情况
        //     this.isLoading = false;
        //     });
        // },
        // 当地图加载完成
        loadImg() {
            this.count++;
            if(this.count == this.cusMapDetail.maps.length) {
                console.log('获取整体高度');
                this.getMovableViewHeight();
            }
        },
        // 当顶部背景图加载完成
        loadTopImg() {
            const TOP_BOX = uni.createSelectorQuery().in(this).select("#top_image"); // 获取顶部背景图高度
            TOP_BOX.boundingClientRect((data) => {
                let gap = 24; // 图片底部间隔
                this.top_box_height = data.height; // 赋值容器高度
                let num  = this.top_box_height + this.footer_box_height  + gap;
                this.top_img_height = `calc(100vh - ${num}px)`
                this.$forceUpdate()
                // console.log(this.top_box_height, this.footer_box_height,"减去的总高度",this.top_img_height)
            }).exec()
        },
        // 获取地图容器整体的高度
        getMovableViewHeight() {
            let IMG_BOX = uni.createSelectorQuery().in(this).select('#maps-box');
            IMG_BOX.boundingClientRect((data) => {
                this.map_box_height = Math.floor(data.height);
                console.log(this.map_box_height,"this.map_box_height")
            }).exec()
        },
        // 长按菜单限制
        onImageTouchStart() {
            document.addEventListener('contextmenu', this.prevent); // 开启长按菜单限制
        },
    }
}
</script>
<style lang="scss" scoped>
.container {
    background: #fff;
    min-height: 100vh;
    .main {
        width: 100%;
        padding: 24rpx 24rpx 200rpx 24rpx;
        box-sizing: border-box;
        .movable-area {
            // position: fixed;
            // top: 0;
            // left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        .movable-view {
            width: 100%;
            // height: 1520rpx;
            // padding-bottom: 280rpx;
            pointer-events: auto;
        }
    }
    .footer {
        width: 100%;
        position: fixed;
        bottom: 0px;
        background: #fff;
        padding: 40rpx 24rpx;
        .footer-box {
            justify-content: space-around;
            .footer-text {
                font-size: 24rpx;
                color: #657081;
                margin-top: 16rpx;
            }
            .footer-right {
                .get {
                    text-align: center;
                    font-size: 36rpx;
                    padding: 0rpx 100rpx;
                    color: #fff;
                    background-color: #2D84FB;
                    border-radius: 8rpx;
                }
            }
            .footer-left {
               margin-right: 20rpx;
                justify-content: space-around;
                .bank{
                    padding: 0 20rpx;
                }
                .msg {
                    width: 40rpx;
                    height: 40rpx;
                }
            }
        }
    }
    .keep-time {
        width: 146rpx;
        height: 126rpx;
        padding: 6rpx;
        box-sizing: border-box;
        background-color: rgba(0, 0, 0, .6);
        position: fixed;
        top: 80px;
        right: 15px;
        border-radius: 8rpx;
        z-index: 1001;
        .schedule {
            ::v-deep .uni-progress {
                .uni-progress-bar {
                    border-radius: 15px;
                    .uni-progress-inner-bar {
                        border-radius: 15px;
                    }
                }
            }
        }
        .text-top, .text-bottom {
            color: #fff;
            font-size: 24rpx;
            text-align: center;
        }
        .text-top {
            padding: 16rpx 0 16rpx;
        }
    }
    .masking {
		height: 100vh;
		width: 100vw;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 999;
	}

	.model-wraper {
		width: 568rpx;
        height: 320rpx;
		background-color: #fff;
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
        border-radius: 8rpx;
        &.scale_pop{
            height: 420rpx;
            .modal-body  {
               padding-bottom: 60rpx; 
            }
        }
        .modal-title {
            height: 90rpx;
            line-height: 90rpx;
            width: 100%;
            text-align: center;
            font-size: 32rpx;
        }
        .modal-body {
            padding: 30rpx 30rpx 80rpx 30rpx;
            text-align: center;
            .modal-body-item {
                .modal-body-item-name {
                    margin-right: 20rpx;
                }
                ~.modal-body-item {
                    margin-top: 40rpx;
                }
            }
        }
        .btn {
            width: 300rpx;
            height: 90rpx;
            text-align: center;
            line-height: 90rpx;
            font-size: 32rpx;
            float: left;
            border-top: 1rpx solid #ddd;
        }
        .btns {
           margin: 48rpx ; 
           width: 100%;
           padding: 24rpx ;
           text-align: center;
           border-radius: 10rpx;
           background: #2D84FB;
           color: #fff;
        } 
        .btn.cancel {
            width: 299rpx;
            border-right: 1rpx solid #dcdcdc;
        }
        .btn.confirm {
            color: #65A3FF;
        }
	}
    .model-wraper1 {
        width: 80vw;
        margin: 0 auto;
        background-color: #fff;
        border-radius: 8rpx;
        &.scale_pop{
            .modal-body  {
               padding-bottom: 60rpx; 
            }
        }
        .modal-title {
            height: 90rpx;
            line-height: 90rpx;
            width: 100%;
            text-align: center;
            font-size: 32rpx;
        }
        .modal-body {
            padding: 30rpx 30rpx 80rpx 30rpx;
            text-align: center;
            .modal-body-item {
                .modal-body-item-name {
                    margin-right: 20rpx;
                }
                ~.modal-body-item {
                    margin-top: 40rpx;
                }
            }
        }
        .btn {
            width: 300rpx;
            height: 90rpx;
            text-align: center;
            line-height: 90rpx;
            font-size: 32rpx;
            float: left;
            border-top: 1rpx solid #ddd;
        }
        .btns {
           margin: 48rpx ; 
           width: 100%;
           padding: 24rpx ;
           text-align: center;
           border-radius: 10rpx;
           background: #2D84FB;
           color: #fff;
        } 
        .btn.cancel {
            width: 299rpx;
            border-right: 1rpx solid #dcdcdc;
        }
        .btn.confirm {
            color: #65A3FF;
        }
	}
}
</style>