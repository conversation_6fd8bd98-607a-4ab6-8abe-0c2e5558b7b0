<template>
    <wisdomContainer>
        <wisdomPersonalBrief :loading.sync="loading"></wisdomPersonalBrief>
    </wisdomContainer>
</template>

<script>
import wisdomContainer from './components/wisdomContainer';
import wisdomPersonalBrief from './components/wisdomPersonalBrief';
export default {
    components: {
        wisdomContainer,
        wisdomPersonalBrief
    },
    data(){
        return {
            loading: true
        }
    },
    created(){
        uni.showLoading();
        const unwatch = this.$watch('loading', (val)=>{
            uni.hideLoading();
            unwatch();
        })
    }
}
</script>