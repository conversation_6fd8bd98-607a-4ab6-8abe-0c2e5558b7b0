<template>
  <view class="apply">
    <view class="flex-row">
      <text class="title">修改信息</text>
    </view>

    <view
      class="flex-row space"
      v-if="
        apply_params.trade_type === 1 ||
        apply_params.trade_type === 3 ||
        (apply_params.trade_type === '' &&
          (apply_options.trade_type === 1 || apply_options.trade_type === 3))
      "
    >
      <view class="flex-row">
        <text class="label">调整售价</text>
        <text>将</text>
        <text class="input" :class="{ disabled: true }">{{ apply_options.sale_price }}</text>
      </view>
      <view class="flex-row">
        <view class="center_text flex-row">
          <text>万元</text>
          <text>调整为</text>
        </view>
        <input class="input" type="number" v-model="apply_params.sale_price" />
        <text>万元</text>
      </view>
    </view>
    <view
      class="flex-row space"
      v-if="
        apply_params.trade_type === 2 ||
        apply_params.trade_type === 3 ||
        (apply_params.trade_type === '' &&
          (apply_options.trade_type === 2 || apply_options.trade_type === 3))
      "
    >
      <view class="flex-row">
        <text class="label">调整租价</text>
        <text>将</text>
        <text class="input" :class="{ disabled: true }">{{ apply_options.rent_price }}</text>
      </view>
      <view class="flex-row">
        <view class="center_text flex-row">
          <text>元/月</text>
          <text>调整为</text>
        </view>
        <input class="input" type="number" v-model="apply_params.rent_price" />
        <text>元/月</text>
      </view>
    </view>
    <view class="flex-row space">
      <view class="flex-row">
        <text class="label">变更面积</text>
        <text>将</text>
        <text class="input" :class="{ disabled: true }">{{ apply_options.mianji }}</text>
      </view>
      <view class="flex-row">
        <view class="center_text flex-row">
          <text>m²</text>
          <text>变更为</text>
        </view>
        <input class="input" type="number" v-model="apply_params.mianji" />
        <text>m²</text>
      </view>
    </view>
    <myButton
      style="margin-left: 160rpx; margin-top: 24rpx"
      type="primary"
      :round="false"
      :loading="loading"
      @click="submitApply"
      >提交申请</myButton
    >
  </view>
</template>

<script>
import myButton from './myButton'
// import mySelect from '@/components/ui/mySelect'
// import myRadio from '@/components/ui/myRadio'
export default {
  name: 'apply',
  components: {
    myButton,
    // mySelect,
    // myRadio,
  },
  data() {
    return {
      // apply_options: {
      //   currentAgent: {},
      //   house: {},
      //   releaseAgent: {},
      //   tradeStatus: [],
      //   tradeType: [],
      // },
      apply_params: {
        sale_price: '',
        rent_price: '',
        trade_type: '',
        mianji: '',
      },
      loading: false,
    }
  },
  props: {
    house_id: [String, Number],
    apply_options: [Object],
  },
  created() {
    this.getApplyOptions()
  },
  filters: {},
  methods: {
    getApplyOptions() {
      this.apply_params = {
        sale_price: '',
        rent_price: '',
        trade_type: '',
        mianji: '',
      }
    },
    submitApply() {
      this.loading = true
      var params = {}
      for (let key in this.apply_params) {
        if (this.apply_params[key] !== '') {
          params[key] = this.apply_params[key]
        }
      }
      if (params.sale_price) {
        params.sale_price = (Number(params.sale_price) * 10000).toFixed()
      }
      params.id = this.house_id
      this.$ajax
        .post('/v1/wapLm/editPrivateHouse', params)
        ,(res) => {
          this.loading = false
          if (res.data.status === 200) {
            this.$emit('success')
          }
          uni.showToast({
            title: res.data.message,
            icon: 'none',
            mask: true,
          })
        }  
        ,(err) => {
          console.log(err)
          this.loading = false
        }
    },
  },
}
</script>

<style scoped lang="scss">
.apply {
  width: 100vw;
  padding: 48rpx 24rpx;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  background-color: #fff;
  .flex-row {
    align-items: center;
    font-size: 24rpx;
  }
  .title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 48rpx;
    padding-left: 12rpx;
    line-height: 1;
    border-left: 6rpx solid #2d84fb;
  }
  .space {
    margin-bottom: 24rpx;
  }
  .label {
    width: 120rpx;
    margin-right: 12rpx;
    text-align: right;
    color: #8a929f;
  }
  .center_text {
    justify-content: space-between;
    width: 140rpx;
  }
  .input {
    width: 160rpx;
    padding: 6rpx 12rpx;
    height: 48rpx;
    line-height: 34rpx;
    border-radius: 8rpx;
    border: 1rpx solid #dde1e9;
    margin: 0 6rpx;
    font-size: 24rpx;
    &.disabled {
      background-color: #f8f8f8;
    }
  }
  ::v-deep .select-box {
    width: 160rpx;
    margin: 0 6rpx;
  }
}
</style>
