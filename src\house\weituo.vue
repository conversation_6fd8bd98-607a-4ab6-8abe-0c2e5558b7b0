<template>
  <view class="weituo">
    <!-- 钥匙 -->
    <template v-if="type == 2">
      <view class="add_key">
        <view class="title">添加钥匙</view>
        <view class="form">
          <view class="form_item bottom-line">
            <view class="left flex-row">
              <view class="label">钥匙编码</view>
              <my-input
                v-model="key_num"
                size="big"
                class="flex-1"
                placeholder="请输入钥匙编码"
              ></my-input>
            </view>
          </view>
          <view class="btn_box">
            <my-button
              block
              type="primary"
              size="big"
              :round="false"
              :loading="subming"
              @click="handleSubmit"
              :disabled="
                detail.protect == 1 ||
                (detail.unilateral_agent == 1 && detail.unilateral_agent_auth == 0)
              "
              >提交</my-button
            >
          </view>
        </view>
      </view>
      <view class="key_log">
        <view class="title"> 钥匙添加记录 </view>
        <timeLine v-if="key_list.length > 0" :list="key_list" v-slot="{ prop }">
          <view class="timeline_item">
            <view class="time flex-row items-center space-between"
              ><text>{{ prop.ctime }}</text
              ><text> {{ prop.user_name }}</text></view
            >
            <view class="user_info">
              <!-- <view class="flex-1">
                <view class="name"> 添加人：{{ prop.user_name }}</view>
                
              </view> -->
              <!-- <view class="tel" v-if="prop.agent.tel" @click="mkPhone(prop.agent.tel)">
                <icons type="dianhua" size="30" color="#2d84fb"></icons>
                {{ prop.agent.tel }}</view
              > -->
            </view>
            <view class="content">
              <text>钥匙编号：{{ prop.key_num }}</text>
            </view>
          </view>
        </timeLine>
      </view>
    </template>
    <!-- 委托 -->
    <template v-if="type == 1">
      <view class="add_weituo">
        <template v-if="detail.auth_view_wt == 1 || detail.wtr_id == 0">
          <view class="title inblock">
            <text class ="title_con">请上传房源委托凭证： 委托书、不动产证、委托人身份证等</text>
            <text class ="red tips">（仅维护人可查看）</text>
          </view>
          <view class="upload">
            <view class="form flex-row flex-wrap">
              <view class="uploadImg add_content">
                <myUpload
                  :imgs="weituo_img"
                  :del="false"
                  :action="action"
                  :upInfo="upInfo"
                  max-count="10"
                  @uploadDone="uploadDoneWeituo"
                  :disabled="
                    detail.protect == 1 ||
                    detail.wtr_id > 0 ||
                    (detail.unilateral_agent == 1 && detail.unilateral_agent_auth == 0)
                  "
                  :disabledText="
                    detail.protect == 1
                      ? '当前房源处于保护期内'
                      : detail.wtr_id != 0
                      ? '已有委托人不能重复添加'
                      : detail.unilateral_agent == 1 && detail.unilateral_agent_auth == 0
                      ? '请联系维护人'
                      : ''
                  "
                ></myUpload>
                <view class="tip" v-if="weituo_img.length == 0">凭证</view>
              </view>
            </view>
          </view>
          <view class="btn_box">
            <my-button
              block
              type="primary"
              size="big"
              :round="false"
              :loading="subming"
              @click="handleSubmit"
              :disabled="
                detail.protect == 1 ||
                detail.wtr_id > 0 ||
                (detail.unilateral_agent == 1 && detail.unilateral_agent_auth == 0)
              "
              >提交</my-button
            >
          </view>
        </template>
        <template v-else>
          <view class="key_log">
            <view class="title"> 委托记录 </view>
            <timeLine v-if="weituoData.length > 0" :list="weituoData" v-slot="{ prop }">
              <view class="timeline_item">
                <view class="time">{{ prop.ctime }}</view>
                <view class="user_info">
                  <view class="flex-1">
                    <view class="name"> 委托人:{{ prop.cname }}</view>
                    <!-- <view class="name tname">{{ prop.agent.tname }}</view> -->
                  </view>
                  <!-- <view class="tel" v-if="prop.agent.tel" @click="mkPhone(prop.agent.tel)">
                    <icons type="dianhua" size="30" color="#2d84fb"></icons>
                    {{ prop.agent.tel }}</view
                  > -->
                </view>
                <!-- <view class="content">
                  <text>钥匙编号：{{ prop.key_num }}</text>
                </view> -->
              </view>
            </timeLine>
          </view>
        </template>
      </view>
    </template>
    <!-- 独家委托 -->
    <template v-if="type == 3">
      <view class="add_weituo">
        <template v-if="detail.auth_view_vip == 1 || detail.djwtr_id == 0">
          <view class="title inblock">
            <text class ="title_con">VIP委托</text>
            <text class ="red tips">（仅维护人可查看）</text>
          </view>
          <view class="form">
            <view class="form_item">
              <view class="left flex-row">
                <view class="label">时间</view>
                <view class="flex-row items-center">
                  <input
                    id="start"
                    class="input"
                    type="text"
                    v-model="startTime"
                    placeholder="开始时间"
                    @click="$refs.picker_start.show()"
                  />
                  <text>至</text>
                  <input
                    id="end"
                    class="input"
                    type="text"
                    disabled="true"
                    v-model="endTime"
                    placeholder="结束时间"
                    @click="$refs.picker_end.show()"
                  />
                </view>
              </view>
            </view>
            <div class="form_item">
              <view class="left flex-row">
                <view class="label">VIP总价</view>
                <my-input
                  v-model="vip_price"
                  size="big"
                  class="flex-1"
                  placeholder="请输入VIP总价"
                  unit="万元"
                ></my-input>
              </view>
            </div>
          </view>
          <view class="upload">
            <view class="form flex-row flex-wrap">
              <view class="uploadImg add_content">
                <!-- :class="{
                'items-center': pic.length == 0,
                'justify-center': pic.length == 0,
                'flex-row ': pic.length > 0,
                uploadImg_right: pic.length == 0,
              }" -->
                <myUpload
                  :imgs="dj_weituo_img"
                  :del="false"
                  :action="action"
                  :upInfo="upInfo"
                  max-count="10"
                  :disabled="
                    detail.protect == 1 ||
                    detail.djwtr_id > 0 ||
                    (detail.unilateral_agent == 1 && detail.unilateral_agent_auth == 0)
                  "
                  :disabledText="
                    detail.protect == 1
                      ? '当前房源处于保护期内'
                      : detail.djwtr_id > 0
                      ? '已有独家委托人不能重复添加'
                      : detail.unilateral_agent == 1 && detail.unilateral_agent_auth == 0
                      ? '请联系维护人'
                      : ''
                  "
                  @uploadDone="uploadDonedjweituo"
                ></myUpload>
                <view class="tip" v-if="dj_weituo_img.length == 0">独家委托书</view>
              </view>
            </view>
          </view>
          <view class="btn_box">
            <my-button
              block
              type="primary"
              size="big"
              :round="false"
              :loading="subming"
              :disabled="
                detail.protect == 1 ||
                detail.djwtr_id > 0 ||
                (detail.unilateral_agent == 1 && detail.unilateral_agent_auth == 0)
              "
              @click="handleSubmit"
              >提交</my-button
            >
          </view>
        </template>
        <template>
          <view class="key_log">
            <view class="title"> vip委托记录 </view>
            <timeLine v-if="vipData.length > 0" :list="vipData" v-slot="{ prop }">
              <view class="timeline_item">
                <!-- <view class="time">{{ prop.ctime }}</view> -->
                <view class="user_info">
                  <view class="flex-1">
                    <view class="name"> 委托人:{{ prop.user_name }}</view>
                    <!-- <view class="name tname">{{ prop.agent.tname }}</view> -->
                  </view>
                  <!-- <view class="tel" v-if="prop.agent.tel" @click="mkPhone(prop.agent.tel)">
                    <icons type="dianhua" size="30" color="#2d84fb"></icons>
                    {{ prop.agent.tel }}</view
                  > -->
                </view>
                <view class="content">
                  <text>委托开始时间:{{ prop.dj_btime }}</text>
                </view>
                <view class="content">
                  <text>委托结束时间:{{ prop.dj_etime }}</text>
                </view>
              </view>
            </timeLine>
          </view>
        </template>
      </view>
    </template>
    <!-- 开始 -->
    <timePicker
      end="2030-12-30"
      mode="YMD"
      ref="picker_start"
      @confirm="confirmTimeStart"
    ></timePicker>
    <!-- 结束 -->
    <timePicker end="2030-12-30" mode="YMD" ref="picker_end" @confirm="confirmTimeEnd"></timePicker>
  </view>
</template>

<script>
import timeLine from "./components/timeLine.vue"
import myInput from './components/myInput'
import myUpload from './components/upload'
import myButton from './components/myButton'
import timePicker from "@/components/time-picker";
export default {
  components: {
    timeLine,
    myInput,
    myButton,
    myUpload,
    timePicker
  },
  data () {
    return {
      type: 1,
      protect: 0,
      key_list: [],
      key_num: '',
      subming: false,
      id_card: [],
      weituo_img: [],
      dj_weituo_img: [],
      budongchan_img: [],
      weituoData: [],
      vipData: [],
      action: "/common/file/upload/admin?category=6",
      upInfo: {
        category: 3,
      },
      detail: {},
      vip_price: '',
      startTime: '',
      endTime: ""
    }
  },
  onLoad (options) {
    if (options.house_id) {
      this.id = options.house_id
    }
    if (options.type) {
      this.type = options.type
    }
    this.getDetail()
    if (this.type == 2) {
      this.getKeyList()
    }
    if (this.type == 3) {
      this.getVipLog()
    }
  },
  methods: {
    getDetail () {
      this.$ajax.get(
        `/admin/house/privateHousesDetail/${this.id}`,
        {},
        (res) => {

          if (res.statusCode === 200) {

            this.detail = res.data
            if (this.detail.wtr && this.detail.wtr.cname) {
              this.id_card = this.detail.wtr.house_id_card || [];
              this.weituo_img = this.detail.wtr.house_wtxy || [];
              this.budongchan_img = this.detail.wtr.house_cert || []
              if (this.id_card.length) {
                this.weituo_img = this.weituo_img.concat(this.id_card)
              }
              if (this.budongchan_img.length) {
                this.weituo_img = this.weituo_img.concat(this.budongchan_img)
              }
            }
            if (this.detail.djwtr && this.detail.djwtr.cname) {
              this.dj_weituo_img = this.detail.djwtr.house_wtxy;
            }
            if (this.detail.wtr_id > 0) {
              this.weituoData = [
                {
                  cname: this.detail.wtr.cname,
                  ctime: this.formatDate(this.detail.wtr_ctime),
                  id: 1,
                },
              ];
            }
            if (this.detail.djwtr_id > 0) {
              this.startTime = this.detail.djwtr.ctime + " 00:00:00"
              this.endTime = this.detail.djwtr.etime + " 00:00:00"
              // this.vip_date = [
              //   this.detail.djwtr.ctime + " 00:00:00",
              //   this.detail.djwtr.etime + " 00:00:00",
              // ];
              this.vip_price = this.detail.djwtr.lowest_price / 10000;
              // this.vipData = [
              //   {
              //     cname: this.detail.djwtr.cname,
              //     ctime: this.detail.djwtr.ctime,
              //     etime: this.detail.djwtr.etime,
              //     id: 1,
              //   },
              // ];
            }

            // this.handleFocusData(this.detail.pic, this.detail.video, this.detail.vr)
          } else {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
              mask: true,
            })
          }
        },
        (err) => {
          console.log(err)
          this.loading = false
        }
      )
    },
    getKeyList () {
      this.$ajax.get(`/admin/house/keyList/${this.id}`, {}, res => {
        console.log(res);
        if (res.statusCode == 200) {
          this.key_list = res.data.list
        }
      })
    },
    getVipLog () {
      this.$ajax.get(`/admin/house/vipList/${this.id}`, {}, res => {
        console.log(res);
        if (res.statusCode == 200) {
          this.vipData = res.data.list
        }
      })
    },
    // 提交钥匙编码
    handleSubmit () {
      let params = {
        id: this.id,
        type: this.type,
        // key_num: this.key_num
      }
      if (this.type == 2) {
        params.key_num = this.key_num;
        // this.keyLoading = true;
      }
      if (this.type == 1) {
        // wtxy_dj wtxy  id_card
        // params.wtxy_dj = this.dj_weituo_img.join(",");
        params.wtxy = this.weituo_img.join(",");
        params.id_card = this.id_card.join(",");
        params.house_cert = this.budongchan_img.join(",");
        // this.weituoLoading = true;
      }
      if (this.type == 3) {
        params.wtxy_dj = this.dj_weituo_img.join(",");
        params.dj_etime = this.endTime;
        params.dj_btime = this.startTime
        params.dj_lowest_price = this.vip_price * 10000;
        // this.djweituoLoading = true;
      }
      this.subming = true
      this.$ajax.post("/admin/house/privateHousesExtends", params, res => {
        console.log(res);
        if (res.statusCode == 200) {
          this.subming = false
          uni.showToast({
            title: "添加成功",
            icon: "none"
          })
          if (this.type == 2) {
            this.getKeyList()
          }
        } else {
          this.subming = false
          uni.showToast({
            title: res.data.message || '添加失败',
            icon: "none"
          })
        }
      }, () => {
        this.subming = false
      })
    },
    uploadDone (e) {
      this.id_card = e
    },
    uploadDoneWeituo (e) {
      this.weituo_img = e
    },
    uploadDoneBudongchan (e) {
      this.budongchan_img = e
    },
    uploadDonedjweituo (e) {
      this.dj_weituo_img = e
    },
    formatDate (time) {
      let timestr = new Date(time * 1000);
      let year = timestr.getFullYear();

      let month = (timestr.getMonth() + 1 + "").padStart(2, "0");
      let day = (timestr.getDate() + "").padStart(2, "0");
      let hour = (timestr.getHours() + "").padStart(2, "0");
      let min = (timestr.getMinutes() + "").padStart(2, "0");
      let sec = (timestr.getSeconds() + "").padStart(2, "0");
      return (
        year + "-" + month + "-" + day + " " + hour + ":" + min + ":" + sec
      );
    },
    // 选择开始时间
    confirmTimeStart (e) {
      this.startTime = e.result;
    },
    //选择结束时间
    confirmTimeEnd (e) {
      this.endTime = e.result;
    },

  }
}
</script>

<style scoped lang="scss">
.weituo {
  padding: 24rpx 48rpx;
  .title {
    // display: none;
    // padding: 0 48rpx;
    font-size: 28rpx;
    font-weight: 600;
    flex-wrap: wrap;
    &.inblock{
      display: block;
      line-height:1.5;
    }
    .title_con{
      font-weight: normal;

    }
    .tips{
      font-weight: normal;
      color: #fb656a;
    }
  }
  .key_log {
    line-height: 1.5;
  }
}
.form {
  margin-bottom: 24rpx;
  padding-top: 12rpx;
  background-color: #fff;
  &.customer_write {
    .form_item {
      > .left {
        > .label {
          font-weight: 500;
        }
      }
    }
    .opt_btns {
      margin-top: 20rpx;
      .customer_sub {
        padding: 20rpx 30rpx;
        color: #fff;
        border-radius: 8rpx;
        background: #2d84fb;
        margin-right: 20rpx;
        &.cancel {
          background: #fff;
          color: #666;
        }
      }
    }
  }
  &.mb0 {
    margin-bottom: 0;
  }
  .form_item {
    flex-direction: row;
    // margin-bottom: 12rpx;
    padding: 34rpx 48rpx;
    &.popup_form_item {
      .popup_input {
        margin-right: 10rpx;
      }
      ::v-deep .select-box {
        width: auto;
      }
    }
    .level_list {
      .level_item {
        background: #dedede;
        border: 2rpx solid #dedede;
        border-radius: 4rpx;
        text-align: center;
        padding: 8rpx 0;
        &.active {
          border: 2rpx solid #2d84fb;
          .level_item_name {
            color: #2d84fb;
          }
          .level_item_tip {
            color: #2d84fb;
          }
        }
        ~ .level_item {
          margin-left: 10rpx;
        }
        .level_item_name {
          font-size: 28rpx;
          color: #333;
          font-weight: 700;
        }
        .level_item_tip {
          font-size: 22rpx;
          color: #999;
        }
      }
    }
    .my_inp {
      &.border {
        padding: 8rpx;
        border: 2rpx solid #d9cccc;
        border-radius: 8rpx;
      }
    }
    .icon {
      &.border {
        margin-left: 8rpx;
        padding: 12rpx;
        min-width: 160rpx;
        border-radius: 8rpx;
        border: 2rpx solid #d9cccc;
        position: relative;
        .novalue {
          color: #999999;
        }
        .sanjiao {
          position: absolute;
          right: 12rpx;
          width: 0;
          height: 0;
          margin-left: 8rpx;
          border-right: 10rpx solid transparent;
          border-left: 10rpx solid transparent;
          border-top: 10rpx solid #d9cccc;
        }
      }
    }
    > .left {
      flex: 1;
      align-items: center;
      justify-content: space-between;
      position: relative;
      > .label {
        // line-height: 1;
        margin-right: 30rpx;
        font-size: 32rpx;
        font-weight: 600;
        color: #2e3c4e;
        min-width: 128rpx;
      }
      .owner {
        font-size: 36rpx;
        color: #999;
      }

      .mask {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        z-index: 10;
      }
      .tip {
        font-size: 24rpx;
        color: #fe6c17;
      }
      .date_row {
        justify-content: space-between;

        .select {
          font-size: 30rpx;
          flex-wrap: nowrap;
          white-space: nowrap;
        }
        .text-center {
          text-align: center;
        }
        .placeholder {
          color: #8a929f;
        }
      }
    }
    ::v-deep .icon-jinrujiantou {
      padding: 12rpx;
    }
    .input-group {
      .input-item {
        align-items: center;
        height: 40rpx;
        font-size: 26rpx;
        ~ .input-item {
          margin-left: 16rpx;
        }
        .label {
          margin-right: 12rpx;
        }
        input {
          line-height: 1;
          height: 36rpx;
          flex: 1;
        }
      }
    }
    .custom_row {
      padding: 16rpx 16rpx;
      .flex-row {
        align-items: center;
      }
    }
  }
}
.submit_type {
  padding: 0 48rpx 24rpx;
  font-size: 22rpx;
  color: #8a929f;
}
.btn_box {
  padding: 48rpx;
  background-color: #fff;
  // position: absolute;
  // right: 0;
  // bottom: 0;
  // left: 0;
}

.uploadImg {
  &.uploadImg_right {
    margin-top: 100rpx;
    // margin-right: -196rpx;
  }
  .tip {
    margin-top: 10px;
    // margin-left: -196rpx;
  }
}
</style>