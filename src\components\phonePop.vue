<template>
  <view class="list">
    <view class="bind-box">
      <view class="top-tips">
        <view class="top">温馨提示</view>
        <view class="bottom">当前用户状态需绑定手机号</view>
      </view>
      <view class="input_item">
        <view class="input_box row">
          <myIcon type="shouji" class="icon" color="#333"></myIcon>
          <input
            v-model="form.phone"
            placeholder-class="plh-class"
            maxlength="11"
            type="number"
            placeholder="请输入您的手机号"
            @input="onInput"
          />
        </view>
        <view class="input_box row">
          <myIcon type="yanzhengma" class="icon" color="#333"></myIcon>
          <input
            v-model="form.captcha"
            placeholder-class="plh-class"
            maxlength="6"
            type="number"
            placeholder="请输入验证码"
          />
          <view class="code" @click="showVer">{{
            time ? time + "s后获取" : "获取验证码"
          }}</view>
        </view>
        <view v-if="isVerify" class="verify-box">
          <move-verify @result="verifyResult" ref="verifyElement"></move-verify>
        </view>
      </view>
      <button class="btn" @click="onSubmit">立即绑定</button>
    </view>
    <view class="close" @click="close">x</view>
  </view>
</template>

<script>
import myIcon from "./my-icon";
import moveVerify from "./moveVerify";
export default {
  components: {
    myIcon,
    moveVerify,
  },
  data() {
    return {
      form: {
        phone: "",
        captcha: "",
      },
      // 验证码
      sending: false,
      time: "",
      phone_token: "",
      isVerify: false,
    };
  },
  methods: {
    // 监听输入框值，没有数据隐藏滑动验证
    onInput(e) {
      let value = e.detail.value;
      if (!value) {
        this.isVerify = false;
      }
    },
    showVer() {
      if (!this.checkPhone(this.form.phone)) {
        return;
      }
      if (!this.time) {
        this.isVerify = true;
      }
      if (this.time > 0) {
        this.isVerify = false;
      }
    },
    /* 校验结果回调函数 */
    // https://ext.dcloud.net.cn/plugin?id=573  插件地址
    verifyResult(res) {
      if (res.flag == true) {
        if (this.sending) {
          return;
        }
        this.sending = true;

        this.getCode();
      }
    },
    // 判断手机号码输入
    checkPhone(tel) {
      if (!tel) {
        uni.showToast({
          title: "请输入手机号码",
          icon: "none",
        });
        return false;
      }
      if (!/^1[3456789]\d{9}$/.test(tel)) {
        uni.showToast({
          title: "手机号码格式错误",
          icon: "none",
        });
        return false;
      }
      return true;
    },
    checkCode(code) {
      // 检测验证码
      if (!code) {
        uni.showToast({
          title: "请输入验证码",
          icon: "none",
        });
        return false;
      }
      return true;
    },
    //获取验证码按钮点击计时事件
    getCode() {
      let website_id = this.$store.state.website_id || 1;
      this.isVerify = false;
      this.$ajax.post(
        "/auth/client/send/sms/captcha",
        {
          website_id: website_id,
          phone: this.form.phone,
        },
        (res) => {
          if (res.statusCode === 200) {
            uni.showToast({
              title: "发送成功",
              icon: "none",
            });
            this.time = 60;
            this.timerDown();
            this.sending = true;
          } else {
            uni.showToast({
              title: res.data.message || "网络错误",
              icon: "none",
            });
            this.refCode();
          }
        }
      );
    },
    timerDown() {
      // 倒计时
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = setInterval(() => {
        if (this.time <= 0) {
          clearInterval(this.timer);
          this.sending = false;
          return;
        }
        this.time--;
      }, 1000);
    },
    refCode() {
      this.img_code += "?";
    },
    onSubmit() {
      if (!this.form.phone || !this.form.captcha) {
        uni.showToast({
          title: "请输入内容后提交",
          icon: "none",
        });
        return;
      }
      this.$ajax.post("/client/my/bind/phone", this.form, (res) => {
        if (res.statusCode === 200) {
          if (res.data.token) {
            uni.setStorageSync("token", res.data.token);
            uni.setStorageSync(
              "token" + this.$store.state.website_id,
              res.data.token
            );
          }
          uni.showToast({
            title: "绑定成功",
          });
          this.close();
        } else {
          uni.showToast({
            title: res.data.message || "绑定失败",
            icon: "none",
          });
        }
      });
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  .bind-box {
    width: 600rpx;
    height: 718rpx;
    background: #fff;
    border-radius: 16rpx;
    .top-tips {
      text-align: center;
      margin: 64rpx 0;
      .top {
        font-size: 36rpx;
        font-weight: bold;
      }
      .bottom {
        margin-top: 31rpx;
      }
    }
    .input_item {
      width: 100%;
      margin: 0 auto;
      .input_box {
        width: 508rpx;
        height: 86rpx;
        background: #f5f5f5;
        align-items: center;
        margin: 0 auto;
        input {
          border-radius: 8rpx;
          font-size: 24rpx;
          color: #333;
          margin-left: 20rpx;
          .plh-class {
            color: #999;
          }
        }
        .icon {
          margin: 26rpx 0;
          margin-left: 32rpx;
        }
        .code {
          font-size: 24rpx;
          color: #999;
          border-left: 1rpx solid #999;
          padding-left: 22rpx;
        }
      }
      .input_box:first-child {
        margin-bottom: 48rpx;
      }
    }
    button::after {
      border: none;
    }
    .btn {
      margin-top: 100rpx;
      color: #fff;
      width: 508rpx;
      height: 72rpx;
      background: #0174ff;
      border-radius: 36rpx;
      line-height: 72rpx;
      font-size: 32rpx;
    }
  }
  .verify-box {
    margin: 20rpx 48rpx 0;
  }
  .close {
    color: #999;
    width: 50rpx;
    height: 50rpx;
    background: #fff;
    border-radius: 50%;
    font-size: 38rpx;
    align-items: center;
    margin: 50rpx auto 0;
  }
}
</style>
