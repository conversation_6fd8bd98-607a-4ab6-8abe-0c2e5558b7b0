<template>
  <view class="list">
    <view class="search row">
      <view class="right flex-1">
        <myIcon class="icon" type="ic_sousuo3x1" color="#D1D1D1" size="36rpx"></myIcon>
        <input
          class="uni-input"
          placeholder="请输入"
          v-model="params.keywords"
          placeholder-style="font-size:26rpx"
          @confirm="search"
        />
      </view>
    </view>
    <view class="uni-list">
      <radio-group @change="checkboxChange">
        <!-- <checkbox-group @change="checkboxChange"> -->
        <label
          class="uni-list-cell uni-list-cell-pd flex-1 flex-row bottom-line"
          v-for="(item, index) in memberList"
          :key="item.id"
        >
          <view>
            <!-- <checkbox :value="item.id + ''" :checked="item.checked" /> -->
            <radio :value="'' + item.openid" :checked="index === current" />
          </view>
          <view class="prelogo">
            <view class="name_first">
              {{ item.name && item.name[0] }}
            </view>
            <!-- <image :src="item.avatar"></image> -->
          </view>
          <view class="cname flex-1">
            <view class="cname_top"> {{ item.name }} </view>
            <!-- <view class="cname_bottom flex-row">
              <text class="c_label"> 平台网站 </text>
              <text class="c_label"> 抖音 </text>
            </view> -->
            <!-- {{ item.cname }} -->
          </view>
          <view class="souce" v-if="item.add_user">
            由{{ item.add_user ? item.add_user.user_name : '' }}添加
          </view>
        </label>
      </radio-group>
      <load-more :status="load_status"></load-more>
      <view class="btn-bottom">
        <view class="btn-box row">
          <view class="btn c2" @click="$navigateBack()">取消</view>
          <view class="btn" @click="onCreateData">提交</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore.vue";
import myIcon from "@/components/my-icon";
export default {
  components: {
    loadMore,
    myIcon
  },
  data () {
    return {
      memberList: [],
      current: -1,
      id: '',
      selected: '',
      load_status: "",
      params: {
        page: 1,
        per_page: 10,
        type: 2,
        keywords: ""
      },
      form_info: {

      }
    }
  },
  onLoad (options) {
    if (options.id) {
      this.id = options.id
    }
    this.getMemberList()
  },
  methods: {
    checkboxChange (e) {
      this.selected = e.detail.value
      console.log(e);
    },
    search () {
      this.params.page = 1
      this.getMemberList()
    },
    getMemberList () {
      if (this.params.page === 1) {
        this.memberList = [];
      }
      this.$ajax.get(`/qywx/client/bind_search`, this.params, (res) => {
        console.log(res);
        if (res.statusCode == 200) {
          this.memberList = this.memberList.concat(res.data.data);
          if (res.data.data.length === 0 || res.data.data.length < this.params.per_page) {
            this.load_status = "nomore";
          }
          // this.memberList = res.data.data
        } else {
          uni.showToast({
            title: res.data.message,
            icon: 'none',
          })
        }
      })
    },
    onCreateData () {
      this.form_info.openid = this.selected
      this.form_info.client_id = this.id
      if (!this.form_info.openid) {
        uni.showToast({
          title: "请选择用户",
          icon: "none",
        });
        return;
      }

      this.$ajax.post("/qywx/client/bind", this.form_info, (res) => {
        if (res.statusCode === 200) {
          this.$navigateTo(
            `/customer/detail?id=${this.form_info.client_id}&form=2`
          );
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    submit () {
      if (this.selected.length == 0) {
        uni.showToast({
          title: '请选择成员',
          icon: 'none',
        })
        return
      }
      // let agent_id = this.selected.join(',')
      // this.$ajax.post('/v1/wapLm/addAgentsByDepartment', { id: this.id, agent_id }).then((res) => {
      //   if (res.data.status == 200) {
      //     uni.showToast({
      //       title: res.data.message,
      //       icon: 'none',
      //     })
      //     setTimeout(() => {
      //       this.$navigateBack()
      //       setTimeout(() => {
      //         uni.$emit('addMemberSuccess')
      //       }, 300)
      //     }, 1000)
      //   }
      // })
    },
  },
}
</script>

<style scoped lang="scss">
page {
  color: #2e3c4e;
  padding-bottom: 80rpx;
}
.list {
  padding: 0 24px;
}
.uni-list {
  padding-top: 60px;
  checkbox-group {
    // padding-left: 24rpx;
    ::v-deep uni-label {
      width: 100% !important;
    }
    uni-checkbox {
      ::v-deep .uni-checkbox-input {
        margin-right: 0;
        border-radius: 50%;
      }
    }
  }
  radio-group {
    // padding-left: 24rpx;
    ::v-deep uni-label {
      width: 100% !important;
    }
    uni-radio {
      ::v-deep .uni-radio-input {
        margin-right: 0;
        border-radius: 50%;
      }
    }
  }
  .uni-list-cell {
    padding: 10px 0;
  }
  .prelogo {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin: 0 10px;
    background: #2d84fb;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    .name_first {
      color: #fff;
      font-size: 16px;
    }
    image {
      width: 100%;
      height: 100%;
    }
  }
  .cname {
    font-size: 16px;
    // font-weight: bolder;
    color: 414346;
    .cname_bottom {
      margin-top: 5px;
    }
    .c_label {
      background: #f4f4f4;
      padding: 3px 5px;
      border-radius: 2px;
      color: #959a9d;
      font-size: 10px;
      &:not(last-child) {
        margin-right: 5px;
      }
    }
  }
  .souce {
    font-size: 14px;
    color: #868686;
  }

  .uni-list-cell {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 20rpx 0;
  }
  .footer {
    padding: 20rpx;
    background: #f8f8f8;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    max-width: 1500rpx;
    .confirm {
      font-size: 28rpx;
      padding: 10rpx 20rpx;
      color: #fff;
      background: #2d84fb;
      border-radius: 8rpx;
    }
  }
}

.search {
  padding: 22rpx 48rpx;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  height: 100rpx;
  top: 0;
  left: 0;
  background: #fff;
  width: 100%;
  z-index: 10;
  .right {
    position: relative;
    .icon {
      position: absolute;
      top: 14rpx;
      left: 28rpx;
    }
    input {
      font-size: 28rpx;
      padding-left: 96rpx;
      background: #eee;
      height: 64rpx;
      // width: 530rpx;
      border-radius: 32rpx;
    }
  }
}
.btn-bottom {
  height: 100px;
  .btn-box {
    background: #fff;
    justify-content: space-between;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    // width: 100%;
    padding: 24px;
    .btn {
      width: 48%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      border-radius: 6px;
      border: 1px solid #2d84fb;
      color: #fff;
      background: #2d84fb;
      font-weight: 500;
      &.c2 {
        border: 1px solid #dde1e9;
        background: #fff;
        color: #8a929f;
      }
    }
  }
}
</style>
