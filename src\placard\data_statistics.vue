<template>
    <view class="root">
        <view class="data_head">
            <!-- <view></view> -->
            <text>绿城618线上章三直播说房</text>
            <image src="./photo/Dropdown Arrow.png"></image>
        </view>
        <view class="date">
            <view class="today">今日</view>
            <view class="yesterday">昨日</view>
            <view class="yesterday">近7日</view>
            <view class="yesterday">近30日</view>
        </view>
        <view class="chart">
            <view class="chart_head">
                <view class="user">用户画像</view>
                <view class="number">参与人数：549</view>
            </view>
            <view class="Bar_chart">
                <view class="charts-box">
                    <qiun-data-charts type="bar" :opts="opts" :chartData="chartData" />
                </view>
            </view>
        </view>
        <view class="chart">
            <view class="chart_head">
                <view class="user">置业顾问推广数</view>
                <view class="number">推广总数：549</view>
            </view>
            <view class="Bar_chart">
                <view class="charts-box">
                    <qiun-data-charts type="bar" :opts="opts1" :chartData="chartData1" />
                </view>
            </view>
        </view>
        <view class="chart">
            <view class="chart_head">
                <view class="user">置业顾问报备数</view>
                <view class="number">报备总数：549</view>
            </view>
            <view class="Bar_chart">
                <view class="charts-box">
                    <qiun-data-charts type="bar" :opts="opts2" :chartData="chartData2" />
                </view>
            </view>
        </view>
        <view class="Total_data">
            总数据
        </view>
        <view class="gross_data">
            <view class="gross_data_all">
                <view class="data_all">
                    <text class="data_all_sum">活动总金额(元)</text>
                    <text class="data_all_figure">9747.0
                        <image src="./photo/Data_Arrow.png"></image>
                    </text>
                </view>
            </view>
            <view class="gross_data_all">
                <view class="data_all">
                    <text class="data_all_sum">活动剩余金额(元)</text>
                    <text class="data_all_figure">1.02
                        <image src="./photo/Data_Arrow.png"></image>
                    </text>
                </view>
            </view>
            <view class="gross_data_all">
                <view class="data_all">
                    <text class="data_all_sum">参与人数(人)</text>
                    <text class="data_all_figure">9747
                        <image src="./photo/Data_Arrow.png"></image>
                    </text>
                </view>
            </view>
            <view class="gross_data_all">
                <view class="data_all">
                    <text class="data_all_sum">单人传播成本(元)</text>
                    <text class="data_all_figure">1.02
                        <image src="./photo/Data_Arrow.png"></image>
                    </text>
                </view>
            </view>
            <view class="gross_data_all">
                <view class="data_all">
                    <text class="data_all_sum">围观数(人)</text>
                    <text class="data_all_figure">3175
                        <image src="./photo/Data_Arrow.png"></image>
                    </text>
                </view>
            </view>
            <view class="gross_data_all">
                <view class="data_all">
                    <text class="data_all_sum">单人深度传播成本(元)</text>
                    <text class="data_all_figure">3.09
                        <image src="./photo/Data_Arrow.png"></image>
                    </text>
                </view>
            </view>
            <view class="gross_data_all">
                <view class="data_all">
                    <text class="data_all_sum">线索数(人)</text>
                    <text class="data_all_figure">1055
                        <image src="./photo/Data_Arrow.png"></image>
                    </text>
                </view>
            </view>
            <view class="gross_data_all">
                <view class="data_all">
                    <text class="data_all_sum">单个留电成本(元)</text>
                    <text class="data_all_figure">9.30
                        <image src="./photo/Data_Arrow.png"></image>
                    </text>
                </view>
            </view>
        </view>
    </view>
</template>
<script>
export default {
    data() {
        return {
            chartData: {},
            chartData1: {},
            chartData2: {},
            opts: {
                color: ["#C3DBFD"],
                // padding: [15, 30, 0, 5],
                enableScroll: true,
                legend: {},
                xAxis: {
                    boundaryGap: "justify",
                    disableGrid: false,
                    min: 0,
                    axisLine: false,
                    max: 70
                },
                yAxis: {},
                extra: {
                    bar: {
                        type: "group",
                        width: 30,
                        meterBorde: 1,
                        meterFillColor: "#FFFFFF",
                        activeBgColor: "#000000",
                        activeBgOpacity: 0.08,
                        categoryGap: 4,
                    }
                }
            },
            opts1: {
                color: ["#FDDFC3"],
                // padding: [15, 30, 0, 5],
                enableScroll: true,
                legend: {},
                xAxis: {
                    boundaryGap: "justify",
                    disableGrid: false,
                    min: 0,
                    axisLine: false,
                    max: 70
                },
                yAxis: {},
                extra: {
                    bar: {
                        type: "group",
                        width: 30,
                        meterBorde: 1,
                        meterFillColor: "#FFFFFF",
                        activeBgColor: "#000000",
                        activeBgOpacity: 0.08,
                        categoryGap: 4,
                    }
                }
            },
            opts2: {
                color: ["#FDC3C3"],
                // padding: [15, 30, 0, 5],
                enableScroll: true,
                legend: {},
                xAxis: {
                    boundaryGap: "justify",
                    disableGrid: false,
                    min: 0,
                    axisLine: false,
                    max: 70
                },
                yAxis: {},
                extra: {
                    bar: {
                        type: "group",
                        width: 30,
                        meterBorde: 1,
                        meterFillColor: "#FFFFFF",
                        activeBgColor: "#000000",
                        activeBgOpacity: 0.08,
                        categoryGap: 4,
                    }
                }
            }
        }
    },
    onReady() {
        this.getServerData();
        this.consultant();
        this.Property()
    },
    methods: {
        getServerData() {
            let res = {
                categories: ["善南街道", "北辛街道", "荆河街道", "善南街道", "龙泉街道", "龙泉街道"],
                series: [
                    {
                        name: "目标值",
                        data: [60, 61, 64, 33, 13, 34]
                    },
                    // {
                    //     name: "完成量",
                    //     data: [18, 27, 21, 24, 6, 28]
                    // }
                ]
            };
            this.chartData = JSON.parse(JSON.stringify(res));
            console.log(this.chartData);
        },
        consultant() {
            let res = {
                categories: ["张三", "李四", "王小五", "红衣", "晓芬", "成一"],
                series: [
                    {
                        name: "目标值",
                        data: [60, 61, 64, 33, 13, 34]
                    },
                    // {
                    //     name: "完成量",
                    //     data: [18, 27, 21, 24, 6, 28]
                    // }
                ]
            };
            this.chartData1 = JSON.parse(JSON.stringify(res));
            console.log(this.chartData);
        },
        Property() {
            let res = {
                categories: ["工藤新一", "张默笑", "陈世美", "张帆", "李鑫亿", "吴开强"],
                series: [
                    {
                        name: "目标值",
                        data: [60, 61, 64, 33, 13, 34]
                    },
                    // {
                    //     name: "完成量",
                    //     data: [18, 27, 21, 24, 6, 28]
                    // }
                ]
            };
            this.chartData2 = JSON.parse(JSON.stringify(res));
            console.log(this.chartData);
        },
    }
}
</script>

<style scoped lang="scss">
.root {
    width: 100%;
    height: 3220rpx;
    background-color: #F5F6F8;

    .data_head {
        width: 90%;
        height: 80rpx;
        background-color: #fff;
        margin: 0 auto;
        margin-top: 30rpx;
        border-radius: 5px;
        padding: 20rpx;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        text {
            margin-top: 6rpx;
        }

        image {
            width: 30rpx;
            height: 20rpx;
            margin-top: 13rpx;
        }
    }

    .date {
        width: 90%;
        height: 60rpx;
        // background-color: blueviolet;
        margin: 20rpx auto;
        display: flex;
        flex-direction: row;
        justify-content: space-between;

        .today {
            width: 160rpx;
            height: 60rpx;
            background-color: #2D84FB;
            text-align: center;
            line-height: 30px;
            color: #fff;
            border-radius: 7px;
        }

        .yesterday {
            width: 160rpx;
            height: 60rpx;
            background-color: #fff;
            text-align: center;
            line-height: 30px;
            border-radius: 7px;
        }
    }

    .chart {
        width: 90%;
        height: 600rpx;
        background-color: #fff;
        margin: 0 auto;
        margin-top: 30rpx;

        .chart_head {
            width: 90%;
            height: 70rpx;
            // background-color: peru;
            margin: 0 auto;
            margin-top: 20rpx;
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            .user {
                font-size: 20px;
                font-weight: 600;
            }

            .number {
                color: #2E3C4E;
                font-size: 12px;
            }
        }

        .Bar_chart {
            width: 90%;
            height: 470rpx;
            // background-color: palegreen;
            margin: 0 auto;

            .charts-box {
                width: 100%;
                height: 300px;
            }
        }
    }

    .Total_data {
        width: 90%;
        height: 70rpx;
        // background-color: #2D84FB;
        margin: 0 auto;
        margin-top: 30rpx;
        font-size: 20px;
        font-weight: 600;
    }

    .gross_data {
        width: 90%;
        height: 1000rpx;
        // background-color: plum;
        margin: 0 auto;
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: space-between;

        .gross_data_all {
            width: 47%;
            height: 210rpx;
            background-color: #fff;
            border-radius: 5px;

            .data_all {
                width: 70%;
                height: 120rpx;
                // background-color: palevioletred;
                margin: 30rpx auto;

                text {
                    text-align: left;
                    margin-top: 30rpx;
                }

                .data_all_sum {
                    color: #6d6d6d;
                    font-size: 14px;
                }

                .data_all_figure {
                    color: #232323;
                    font-weight: 600;
                    font-size: 20px;
                }

                image {
                    width: 30rpx;
                    height: 30rpx;
                    margin-left: 20rpx;
                }
            }
        }
    }
}</style>