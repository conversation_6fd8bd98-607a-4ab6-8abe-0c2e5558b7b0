<template>
  <view class="reg">
    <view class="form">
      <!-- <view class="form-item bottom-line">
        <my-input v-model="form.cname" placeholder="请输入用户名"> </my-input>
      </view> -->
      <view class="form-item bottom-line">
        <!-- -->
        <my-input
          v-model="form.tel"
          :disabled="this.id != 0 ? true : false"
          type="number"
          placeholder="请输入手机号"
        >
          <text
            v-if="this.id != 0"
            class="highlight"
            :class="{ disabled: time_down }"
            @click="getVerifyImg"
            >{{ time_down ? `${second}秒后重新获取` : '获取验证码' }}</text
          >
        </my-input>
      </view>
      <view class="form-item bottom-line" v-if="this.id != 0">
        <my-input
          :isfocus="code_focus"
          v-model="form.captcha"
          @blur="code_focus = false"
          placeholder="请输入验证码"
        >
        </my-input>
      </view>
    </view>
    <!-- </template> -->

    <view class="btns">
      <!-- <view class="btn" v-if="step == 1" @click="step = 2"> 下一步 </view> -->
      <view class="btn" @click="handleReg">立即绑定</view>
    </view>
  </view>
</template>

<script>
import myInput from './components/myInput'
import myPopup from './components/myPopup'
// import dragVerify from '@/components/dragVerify'
// import { mapMutations } from 'vuex'
// import mySelect from '@/components/ui/mySelect'
export default {
  components: {
    myInput,
    // myRadio,
    myPopup,
    // dragVerify,
    // mySelect,
  },
  data: () => ({
    current_site: {},
    form: {
      // invited_code: '',
      tel: process.env.NODE_ENV === 'development' ? '15589222712' : '',
      captcha: '',
      cname: '',
    },
    time_down: false,
    second: 0,
    loging: false,
    verify_img: '',
    show_verify: false,
    verify_success: false,
    verify_fail: false,
    code_focus: false,
    bind_phone: '',
    area_list: [],
  }),
  computed: {},

  onLoad (options) {
    if (options.id) {
      this.id = options.id
    }
    if (options.website_id) {
      this.website_id = options.website_id
    }
    if (options.house_id) {
      this.house_id = options.house_id
    }

    this.protect = options.protect || 0

    if (options.bind_phone) {
      this.bind_phone = options.bind_phone
      this.form.tel = options.bind_phone
      uni.setNavigationBarTitle({
        title: '绑定手机号',
      })

    }
  },
  onShow () {
  },
  onUnload () {
  },
  methods: {
    timeDown (s) {
      if (this.time_down) {
        return
      }
      this.timer && clearInterval(this.timer)
      this.second = s
      this.time_down = true
      this.timer = setInterval(() => {
        if (this.second <= 0) {
          this.time_down = false
          clearInterval(this.timer)
          return
        }
        this.second--
      }, 1000)
    },
    async getVerifyImg () {
      if (!this.form.tel) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none',
        })
        return
      }
      if (this.time_down) {
        return
      }
      this.sendSms()
      // let domain = this.$config.apiDomain
      // // #ifdef H5
      // domain = window.location.origin
      // // #endif
      // const res = await this.$ajax.get(
      //   `${domain}/api/verification/slideToken`,
      //   {},
      //   {
      //     header: {
      //       Sitealias: this.current_site.alias || uni.getStorageSync('lm_city'),
      //     },
      //   }
      // )
      // if (res.data.status === 200) {
      //   this.verify_code = res.data.data.imageCode
      //   this.verify_img = res.data.data.url
      //   this.show_verify = true
      // } else {
      //   uni.showToast({
      //     title: res.data.message,
      //     icon: 'none',
      //     mask: true,
      //   })
      // }
    },
    onDragend (distance) {
      // console.log(distance)
      this.sendSms(distance)
    },
    onRefreshend () {
      this.verify_success = false
      this.verify_fail = false
    },
    async sendSms () {
      if (!this.form.tel) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none',
        })
        return
      }
      uni.showLoading({
        title: '发送中...',
        mask: true,
      })
      let api = '/admin/house/captcha'
      // if (this.reg_type == 2 || this.reg_type == 3) {
      //   api = '/v1/wapLm/sendCodeByCompanyRegister'
      // }
      // if (this.bind_phone) {
      //   api = '/v1/wapLm/sendCodeByBindAgent'
      // }
      this.$ajax
        .get(
          api,
          {
            // tel: this.form.tel,
            // imageCode: this.verify_code,
            // userCode: distance,
          },

          (res) => {
            uni.hideLoading()
            if (res.statusCode === 200) {
              uni.showToast({
                title: '发送成功！',
                icon: 'success',
              })
              this.code_focus = true
              // this.form.codeToken = res.data.data.codeToken
              // this.verify_success = true
              // this.show_verify = false
              this.timeDown(60)
            } else {
              // this.verify_fail = true
              // this.getVerifyImg()
              uni.showToast({
                title: res.data.message || '发送失败',
                icon: 'none',
                mask: true,
              })
            }
          }, () => {
            uni.hideLoading()
            uni.showToast({
              title: '发送失败，请重试',
              icon: 'none',
              mask: true,
            })
          })
      // .catch((err) => {
      //   this.verify_fail = true
      //   this.getVerifyImg()
      //   console.log(err)
      //   uni.hideLoading()
      //   uni.showToast({
      //     title: '发送失败，请重试',
      //     icon: 'none',
      //     mask: true,
      //   })
      // })
    },
    handleReg () {
      this.loging = true
      let params = Object.assign({}, this.form)
      let api = '/admin/house/agentBindingSite/' + this.id
      this.$ajax
        .post(api, params, (res) => {
          this.loging = false
          if (res.statusCode === 200) {
            uni.showToast({
              title: res.data.message || (this.bind_phone ? '绑定成功' : '注册成功'),
              icon: 'success',
              mask: true,
            })
            setTimeout(() => {
              // 注册完成跳转到我的页面  如果没有登录回直接跳转
              if (this.bind_phone) {
                // uni.navigateBack()
                uni.redirectTo({
                  url: '/house/house_send?website_id=' + (this.website_id || 1) + '&house_id=' + this.house_id + "&protect=" + this.protect
                })
                // uni.$emit("bindSiteSuccess")
                return
              }

            }, 1000)
          } else {
            uni.showToast({
              title: res.data?.message || '绑定失败',
              icon: 'none',
              mask: true,
            })
          }
        }, () => {
          this.loging = false
        })
      // .catch((err) => {
      //   this.loging = false
      //   console.log(err)
      // })
    },

  },
}
</script>

<style scoped lang="scss">
.reg {
  padding: 48rpx;
  .reg_title {
    font-size: 48rpx;
    margin-top: 64rpx;
    font-weight: 600;

    color: #2e3c4e;
    .step {
      font-size: 28rpx;
      color: #8a929f;
      font-weight: normal;
      .active {
        color: #2d84fb;
      }
    }
  }
  .sub_title {
    margin-top: 24rpx;
    font-size: 28rpx;
    color: #8a929f;
  }
  .agent_type {
    margin-top: 64rpx;
    .agent_type_item {
      padding: 36rpx 52rpx;
      border-radius: 8rpx;
      border: 2rpx solid rgba(221, 225, 233, 1);
      color: #8a929f;
      &.active {
        border: 2rpx solid rgba(45, 132, 251, 1);
        color: #2d84fb;
      }
    }
  }
  .form {
    margin-top: 64rpx;
    .form-item {
      padding: 12rpx 12rpx;
      margin-bottom: 24rpx;
      .tip {
        font-size: 22rpx;
        color: #dde1e9;
      }
    }
  }
  .btns {
    position: absolute;
    bottom: 24rpx;
    background: #fff;
    left: 48rpx;
    right: 48rpx;
    .btn {
      flex: 1;
      padding: 20rpx 0;
      align-items: center;
      background-image: linear-gradient(135deg, #4daaff 0%, #2671ff 100%);
      border-radius: 8rpx;
      font-size: 36rpx;
      color: #ffffff;
    }
  }
}

.reg_success {
  background: #fff;
  border-radius: 24rpx;
  width: 80vw;
  .top {
    width: 100rpx;
    height: 100rpx;
    margin-top: 72rpx;
    border-radius: 50%;
    background: #2d84fb;
  }
  .message {
    font-size: 32rpx;
    color: #2e3c4e;
    margin: 24rpx 0;
    font-weight: 600;
  }
  .tips {
    font-size: 22rpx;
    background: #fff;
    color: #2d84fb;
  }
  .btn {
    font-size: 32rpx;
    background: #2d84fb;
    border-radius: 12rpx;
    color: #ffffff;
    padding: 24rpx 68rpx;
    &.submit {
      margin-top: 96rpx;
    }
    &.cancel {
      font-size: 14px;
      color: #8a929f;
      background: #fff;
      margin: 24rpx 0 72rpx;
    }
  }
}
</style>
