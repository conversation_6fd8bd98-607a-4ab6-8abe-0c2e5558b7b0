<template>
      <view class="follow-card-item" v-for="(item, index) in follow_list_new" :key="item.id + '_' + index">
              {{ item.created_at | captureTime1 }}
              <view class="follow-card-content row" style="padding-bottom: 0">
                <view class="left" style="margin-right: 10px">
                  {{ item.created_at | captureTime }}
                </view>
                <view class="right text_right">
                  <view class="row text_rowLIST">
                    <view class="order" v-if="item.order">
                      <view class="order_c"> 顶</view>
                    </view>
                    <!-- {{ item.content }} -->
                    <view v-if="!item.url" class="contenturkl">{{ item.content }}
                    </view>
                    <view v-else class="voice row" @click.prevent.stop="playVoice(item, index)">
                      <image v-if="!item.playing" :src="'/static/icon/voice/voice_icon.png' | imageFilter('w_80')">
                      </image>
                      <image v-if="item.playing" :src="'/static/icon/voice/play_voice.gif' | imageFilter('w_80')"></image>
                      <text>{{ parseInt(item.time / 1000) }}"</text>
                    </view>
                  </view>

                  <text class="username" v-if="item.admin">由「{{ item.admin.user_name }}」跟进</text>
                </view>
              </view>
              <view class="follow-card-picture">
                <view class="follow-picture-box">
                  <image v-for="(img, i) in item.file_path_list" :key="i"
                    @click.prevent.stop="preFollowImgs(item.file_path_list, i)" :src="img" mode="aspectFill" />
                </view>
                <view class="follow-giveLike-box">
                  <view class="follow-giveLike-main" v-if="item.top_list && item.top_list.length">
                    <view class="follow-giveLike-zan">
                      <image
                        src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/<EMAIL>" />
                    </view>
                    <template v-if="item.top_list && item.top_list.length">
                      <view class="follow-giveLike-user" v-for="(it, i) in item.top_list" :key="it">
                        <template v-if="i < 2">
                          {{ it }}
                        </template>
                        <template v-if="i >= 2">
                          {{ '等' + item.top_list.length + '人' }}
                        </template>
                      </view>
                    </template>
                  </view>

                  <view class="follow_giveLike-controls">
                    <!-- 点赞 -->
                    <view class="follow_giveLike_icon" @click.prevent.stop="followGiveLike(item.id)">
                      <image :src="'/static/admin/customer/<EMAIL>' | imageFilter('w_80')" />
                    </view>
                    <!-- 复制 -->
                    <view style="margin: 0 20px; box-sizing: content-box" class="follow_giveLike_icon"
                      @click.prevent.stop="followCopy(item)">
                      <image
                        src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/<EMAIL>" />
                    </view>
                    <!-- 置顶 -->
                    <view class="follow_giveLike_more" @click.prevent.stop="showPinned(item, index)">
                      <image :src="'/static/admin/customer/<EMAIL>' | imageFilter('w_80')" />
                    </view>
                    <view class="follow-Pinned-box" ref="tops" v-if="item.show">
                      <text class="Pinned-text" v-if="item.order == 0" @click.stop="setTop(item)">设置置顶</text>
                      <text class="Pinned-text" v-if="item.order == 1" @click.stop="setTop(item)">取消置顶</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
</template>