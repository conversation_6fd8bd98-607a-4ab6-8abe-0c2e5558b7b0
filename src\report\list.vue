<template>
	<view class="container">
		<view class="top">
			<tSearchInput placeholder="快捷手机号搜索" v-model="params.mobile" @search="search"/>
			<tabBar v-model="params.is_receipt" @change="search"/>
		</view>
		<template v-if="isInited">
			<auditList :params="searchParams" v-if="isAuditor" ref="reportList"></auditList>
			<applyList :params="searchParams" v-else ref="reportList"></applyList>
		</template>
	</view>
</template>
<script>
import tabBar from './components/list_tabs.vue';
import applyList from './components/report_apply_list.vue';
import auditList from './components/report_audit_list.vue';
import tSearchInput from '@/components/tplus/tSearchInput.vue';
import { checkIsReportAuditor } from '@/common/utils/report';
export default {
	components: {
		applyList,
		auditList, tabBar,
		tSearchInput
	},
	data(){
		return {
			params: {
				is_receipt: '',
				mobile: ''
			},
			isInited: false,
			isAuditor: false,		//是否审核员
		}
	},
	computed: {
		searchParams(){
			const params = {...this.params};
			params.is_receipt === '' && delete params.is_receipt;
			return params;
		}
	},
	onLoad(){
		this.init();
		uni.$on("refreshReprotList", () => {
			this.search();
		})
	},
	onUnload() {
		uni.$off("refreshReprotList");
	},
	methods: {
		async init(){
			let res = await checkIsReportAuditor();
			this.isAuditor = res ? true: false;
			this.isInited = true;
		},
		search(){
			this.$refs.reportList.search();
		}
	},
	async onPullDownRefresh(){
		this.search();
		await this.$Utils.sleep(600)
		uni.stopPullDownRefresh();
	},
	onReachBottom () {
		this.$refs.reportList.getList();
	}
}
</script>

<style lang="scss" scoped>
.container{
	min-height: 100vh;
	background-color: #f6f6f6;
	.top{
		position: sticky;
		top: 0;
		z-index: 1;
	}
}
</style>