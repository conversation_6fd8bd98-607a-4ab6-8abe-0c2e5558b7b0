<template>
  <view class="page">
    <view>
      <!--  -->
      <view v-if="showBackgroundImage"
        style="background: linear-gradient(180deg, #bcd5f7 0%, #c7e0ff 100%);width: 100%;height: 160rpx; color: #ffff;font-size: 30rpx; text-align: center; line-height: 240rpx;"
        class="titlebgc">工作台</view>
      <!-- 背景 -->
      <view class="headerbgc"></view>
      <!-- 主体内容 -->
      <!-- 主体内容 -->
      <view class="whole">
        <view class="imgs">
          <view>
            <view class="rad">{{ name }}</view>
          </view>
          <view class="text">你好，{{ user.user_name || '' }}</view>
        </view>
        <view style="margin-bottom: 200rpx">
          <block>
            <view class="block" v-for="(item, index) in indextitleLidst" :key="item.id + '_' + index">
              <view class="title">{{ item.name }}</view>
              <messageListwork :workList="item.children"></messageListwork>
            </view>
          </block>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import messageListwork from "@/components/messageList/messageListwork.vue";
import share from "@/common/mixin/share";
export default {
  components: {
    messageListwork,
  },
  mixins: [share],
  data() {
    return {
      currentTabIndex: 0,
      indextitleLidst: [], // 工作台数据
      workList: [], // 数据
      showPop: false,
      imgCode: "",
      is_show_skeleton: false, // 是否显示骨架屏
      user: {}, // 获取用户消息
      name: "",
      showBackgroundImage: false, // 控制背景图片显示与隐藏
    };
  },
  onLoad(options) {
    if (options.website_id) {
      this.website_id = uni.getStorageSync("website_id");
    }
    this.is_show_skeleton = false;
    uni.showLoading({
      title: "加载中",
    });
    this.getUserInfo();
  },
  onShow() {
    if (this.$store.state.needRefreshWhenShow) {
      this.$store.commit('setNeedRefreshWhenShow', false);
      this.getUserInfo();
    }
    uni.stopPullDownRefresh();
  },
  onPageScroll(e) {
    // console.log(e,"eeeeeeeee");
    // 当页面滚动时触发该事件
    const scrollTop = e.scrollTop;
    // 根据滚动高度来设置背景图片的显示与隐藏
    this.showBackgroundImage = scrollTop > 60; // 滚动到200的高度时显示背景图片
  },
  async onPullDownRefresh() {
    try {
      this.getUserInfo();
      await this.$Utils.sleep(500)
    } catch (e) { }
    uni.stopPullDownRefresh();
  },
  methods: {
    // 底部tabber栏
    switchTab(index, item) {
      console.log(index, item);
      if (this.currentTabIndex == index) {
        return;
      }
      uni.navigateTo({
        url: item.path,
      });
    },
    // 查询登录用户是否完善资料
    getUserInfo() {
      var that = this;
      this.$ajax.get(
        "/qywx/common/query",
        {},
        (res) => {
          console.log(res, "教师");
          if (res.statusCode === 200) {
            uni.setStorageSync("userInfo", JSON.stringify(res.data));
            uni.hideLoading();
            // 没有联系方式，部门，角色弹出完善资料
            if (
              !res.data.user_name ||
              !res.data.phone ||
              !res.data.wx_work_department_id ||
              (res.data.roles && !res.data.roles.length) ||
              !res.data.roles
            ) {
              this.$ajax.get(
                "/admin/personnelMatters/checkDefaultDepartmentAndRole",
                {},
                (res) => {
                  console.log(res);
                }
              );
              uni.showModal({
                title: "提示",
                content: "请完善资料",
                showCancel: false,
                success: function (res) {
                  if (res.confirm) {
                    that.$navigateTo("/customer/perfect_data");
                  }
                },
              });
            }
            this.getUsersInfo();
            this.getInforUser();
          } else {
            uni.hideLoading();
          }
        },
        () => {
          uni.hideLoading();
        }
      );
    },
    // 获取用户信息
    getInforUser() {
      this.user = JSON.parse(uni.getStorageSync("userInfo"));
      console.log(this.user.phone, "我的用户");
      let name = this.user.user_name.split("");
      console.log(name, "名字");
      this.name = name[0];
    },
    //  数据
    getUsersInfo() {
      var that = this;
      this.$ajax.get("/qywx/welcome/census", {}, (res) => {
        this.indextitleLidst = res.data;
        console.log(res.data, "3333");
        uni.hideLoading();
        if (res.statusCode === 200) {
          // uni.showToast({
          //   title: '加载成功',
          //   icon: "none"
          // });
          let arr = res.data;
          arr.map((item) => {
            if (item.id == 1) {
              this.workList = item.children;
              // console.log(this.workList, '客户');
            }
            if (item.id == 2) {
              console.log(item.id, "999");
              this.newList = item.children;
              // console.log(this.newList, '新房');
            }
            if (item.id == 3) {
              this.houseList = item.children; // 房源
            }
            if (item.id == 4) {
              this.outsideList = item.children; // 外呼
            }
            if (item.id == 5) {
              this.peopleList = item.children; // 人事
            }
          });
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    checkMap() {
      this.$navigateTo("/index/findMap");
    },
  },
};
</script>
<style scoped lang="scss">
.ns {
  width: 100%;
  height: 135rpx;
  text-align: center;
  line-height: 155rpx;
  color: white;
  font-weight: bold;
  background: linear-gradient(to right, rgb(173, 225, 255), #614AF8);
}

.page {
  background-color: #F6F6F6;
  min-height: 100vh;
}

.headerbgc {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 600rpx;
  flex-shrink: 0;
  background: linear-gradient(180deg, rgba(99, 165, 255, 0.40) 0%, rgba(255, 255, 255, 0.00) 100%);

}

.titlebgc {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
}

.imgs {
  margin-top: 16rpx;
  margin-bottom: 48rpx;
  display: flex;
  flex-direction: row;
  // background-color: aqua;
  align-items: center;
}

.rad {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: #3172f6;
  text-align: center;
  color: #f6f6f6;
  line-height: 56rpx;
  font-weight: 500;
  font-size: 26rpx;
}

.text {
  color: #292c39;
  font-family: PingFang SC;
  margin-left: 24rpx;
  font-size: 36rpx;
  font-weight: 500;
  line-height: normal;
  letter-spacing: -0.36px;
}

// 整体大盒子
.whole {
  padding: 0 32rpx;
  margin-top: 145rpx;
  z-index: 9;
}

.pop_c {
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 80vw;
  margin: 0 auto;
  padding: 40rpx;
  border-radius: 20rpx;

  .pop_title {
    text-align: center;
    font-size: 32rpx;
  }

  .pop_img {
    width: 520rpx;
    height: 520rpx;
    margin: 30rpx auto;

    image {
      width: 100%;
    }
  }

  .pop_b {
    font-size: 24rpx;

    ~.pop_b {
      margin-top: 15rpx;
    }
  }
}

.list {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx 40rpx;

  .info {
    align-items: center;
    margin-right: 46rpx;

    image {
      width: 96rpx;
      height: 96rpx;
      border-radius: 20rpx;
    }

    view {
      margin-top: 10rpx;
      font-size: 22rpx;
      color: #8a929f;
    }
  }
}

.title {
  color: rgba(41, 44, 57, 0.7);
  font-size: 28rpx;
  margin-bottom: 24rpx;
}
</style>
