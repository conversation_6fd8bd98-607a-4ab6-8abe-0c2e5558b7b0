<template>
    <view>
        <!-- <myPopup :show="show" @hide="show = false">
            <view class="popup-contianer">
                <view class="top row">
                    <view @click="handleItemClick(item)" class="item" v-for="item in topList" :key="item.name">
                        <image class="item-img" :src="item.icon | imgDomain"></image>
                        <view>{{ item.title }}</view>
                    </view>
                </view>
                <view class="bottom">
                    <view class="item row" v-for="item in currentList" :key="item.name" @click="handleItemClick(item)">
                        <image class="item-img" :src="item.icon | imgDomain"></image>
                        <view class="title">{{ item.title }}</view>
                        <myIcon type="you" size="28rpx" color="#b5b5b6"></myIcon>
                    </view>
                </view>
            </view>
        </myPopup> -->

        <myPopup :show="show" @hide="show = false">
            <view class="more_box">
                <view class="more_title row"> 更多操作 </view>
                <view class="more_list row">
                    <view class="more_item" v-for="(item,index) in currentList" :key="index" @click="handleItemClick(item)">
                        <view class="icon">
                            <image :src="item.icon | imageFilter('w_80')"></image>
                        </view>
                        <view class="name"> {{item.title}} </view>
                    </view>
                </view>
            </view>
        </myPopup>
    
        <transToMember :visible.sync="dialogs.transToMember" :customerId="customerId" @success="handleTransToMemberSuccess" :current="current"></transToMember>
        <transToSeas :visible.sync="dialogs.transToSeas" :customerId="customerId" @success="handleTransToSeasSuccess"></transToSeas>
        <copyToMember :visible.sync="dialogs.copyToMember" :customerId="customerId" @success="handleCopyToMemberSuccess"></copyToMember>
        <bindQywx :visible.sync="dialogs.bindQywx" :customerId="customerId"  @success="handleBindQywxSuccess"></bindQywx>
        <my-popup ref="remind" :show="dialogs.remind" @hide="dialogs.remind = false">
            <remind :id="customerId" class="remind" v-if="dialogs.remind" @onClick="handleReminder" @cancel="dialogs.remind = false"></remind>
        </my-popup>
    </view>
</template>

<script>
import { transToPrivate, crmReminder, transReminder, setCrmCustomerTop, setTransCustomerTop,getCrmDetailMoreOptions,delTransCustomer } from '@/common/utils/customer.js';
import utils from '@/common/utils/customer-options.js';
import myIcon from "@/components/my-icon.vue";
import myPopup from '@/components/myPopup';
import copyToMember from '@/components/customer/copyToMember';
import transToMember from '@/components/customer/transToMember';
import transToSeas from '@/components/customer/transToSeas';
import bindQywx from '@/components/customer/bindQywx';
import remind from "@/components/remind";
export default {
    props: {
        current: { type: String, default: 'my' },
        visible: { type: Boolean, default: false },
        customer: { type: Object, default: ()=>({}) },
        hasRole: { type: Boolean, default: true },
    },
    components: {
        myIcon, myPopup, remind,
        copyToMember, transToMember, transToSeas, bindQywx
    },
    data() {
        return {
            show: false,
            moreOptions: [],
            list: [],
            customerStatusList: null,
            dialogs: {
                copyToMember: false,
                transToMember: false,
                transToSeas: false,
                remind: false,
            },
            remindering: false,
            setToping: false
        }
    },
    computed: {
        customerId(){
            return this.customer.id;
        },
        isQywxBinded(){
            return this.customer.wxqy_id;
        },
        isTrans(){
            return this.current === 'trans';
        },
        isListTop(){
            return this.customer ? this.customer.order == 1 : false;
        },
        currentList(){
            const maintain = { title: '维护资料', name: 'maintain', icon: '/icons/user-square.png' }
            const reminder = { title: '提醒跟进', name: 'reminder', icon: '/icons/bell.png' }
            const approve = { title: '发起审批', name: 'approve', icon: '/icons/user-info.png' }
            const setTop  = { title: '列表置顶', name: 'setTop', icon: '/icons/top-set.png' }
            const setInvalid = { title: '标无效', name: 'setInvalid', icon: '/icons/user-del.png' };
            const transToMember = { title: '转交到同事', name: 'transToMember', icon: '/icons/user-to.png' };
            const transToSeas = { title: '转交到公海', name: 'transToSeas', icon: '/icons/user-to.png' };
            const bindQywx = { title: '绑定企业微信', name: 'bindQywx', icon: '/icons/qywx.png' };
            const copyToMember = { title: '复制给同事', name: 'copyToMember', icon: '/icons/user-copy.png' }
            const transToPrivate = { title: '转为私客', name: 'transToPrivate', icon: '/icons/user-to.png' }
            const delCustomer = { title: '删除', name: 'delCustomer', icon: '/yidongduan/customer/<EMAIL>' }

            const list = [];
            for(const name of this.moreOptions){
                switch(name){
                    case 'maintain':
                        list.push(maintain);
                        break;
                    case 'reminder':
                        list.push(reminder);
                        break;
                    case 'approve':
                        list.push(approve);
                        break;
                    case 'setTop':
                        setTop.title = this.isListTop ? '取消列表置顶' : '列表置顶';
                        list.push(setTop);
                        break;
                    case 'setInvalid':
                        list.push(setInvalid);
                        break;
                    case 'transToMember':
                        list.push(transToMember);
                        break;
                    case 'transToSeas':
                        list.push(transToSeas);
                        break;
                    case 'bindQywx':
                        list.push(bindQywx);
                        break;
                    case 'copyToMember':
                        list.push(copyToMember);
                        break;
                    case 'transToPrivate':
                        list.push(transToPrivate);
                        break;
                    case 'delCustomer':
                        list.push(delCustomer);
                        break;
                }
            }

            if(this.isQywxBinded){
                return list.filter(e => e.name !== 'bindQywx');
            }

            return list;
        }
    },
    watch: {
        visible(val){
            this.show = val;
        },
        show(val){
            this.$emit('update:visible', val)
        },
        customerId: {
            handler(id){
                id && this.getCrmDetailMoreOptions(id)
            },
            immediate: true
        }
    },
    methods: {
        async getCrmDetailMoreOptions(){
            if(this.isTrans){
                this.moreOptions = ['maintain', 'reminder', 'transToPrivate', 'copyToMember', 'transToMember', 'approve', 'delCustomer'];
            }else{
                let options = await getCrmDetailMoreOptions(this.customerId);
                this.moreOptions = options || [];
            }
        },
        getList(){
            switch (this.current) {
                case 'trans':
                    const transToPrivate = { title: '转为私客', name: 'transToPrivate', icon: '/icons/user-to.png' }
                    const copyToMember = { title: '复制给同事', name: 'copyToMember', icon: '/icons/user-copy.png' }
                    return [ maintain, reminder, transToPrivate, copyToMember, approve, setTop ];
                case 'my':
                    return [
                        transToMember,
                        transToSeas,
                        approve, setTop
                    ];
                default:
                    return [
                        transToMember,
                        transToSeas,
                        approve
                    ];
            }
        },
        handleItemClick(item){
            if (!this.hasRole) {
                uni.showToast({
                    title: this.customer.follow_id > 0 ? "暂无权限" : "请先认领客户",
                    icon: 'none'
                })
                return
            }
            this[item.name]();
        },
        /**
         * 维护资料
         */
        maintain () {
            this.show = false;
            this.$navigateTo(`uphold?type=2&id=${this.customerId}&current=${this.current}`);
        },
        /**
         * 标无效
         */
        setInvalid(){
            if (this.customer.is_state == 1) {
                uni.showToast({
                    title: "该客户正在审核中",
                    icon: 'none'
                })
                return
            }
            this.approve();
        },
        /**
         * 转交到同事
         */
        transToMember(){
            this.show = false;
            this.dialogs.transToMember = true;
        },
        handleTransToMemberSuccess(){
            if(this.isTrans){
                this.$store.commit('onBackRefresh', true);
                setTimeout(() => {
                    this.$navigateBack(1, true);
                }, 300)
            }else{
                uni.$emit('getDataAgain');
            }
        },
        /**
         * 转交到公海
         */
        transToSeas(){
            this.show = false;
            this.dialogs.transToSeas = true;
        },
        handleTransToSeasSuccess(){
            uni.$emit('getDataAgain');
        },
        /**
         * 复制给同事
         */
        copyToMember(){
            this.show = false;
            this.dialogs.copyToMember = true;
        },
        handleCopyToMemberSuccess(){
            uni.$emit('getDataAgain');
        },
        /**
         * 绑定企微
         */
        bindQywx(){
            this.show = false;
            this.dialogs.bindQywx = true;
        },
        handleBindQywxSuccess(){
            uni.$emit('getDataAgain');
        },
        /**
         * 审批
         */
        async approve(){
            this.show = false;
            if(this.isTrans){
                this.$navigateTo("/house/applyApprove?id=5&house_id=" + this.customerId)
            }else{
                if(!this.customerStatusList){
                    this.customerStatusList = await utils.getStatusList();
                }
                //审批页读的这些存储数据，原代码传参如下
                uni.setStorageSync("shenpi", JSON.stringify({
                    is_del: this.customer.is_del,
                    is_state: this.customer.is_state,
                    state_list: this.customer.state_list,
                    stateList: this.customerStatusList
                }))
                let type = '19_2'  
                this.$navigateTo("/house/applyApprove?id=1&house_id=" + this.customerId + "&type=" + type)
            }
        },
        /**
         * 转为私客
         */
        async transToPrivate(){
            this.show = false;
            try{
                const data = await transToPrivate(this.customerId);
                this.show = false;
                uni.showToast({
                    title: '转为私客成功',
                    icon: 'success'
                })
                setTimeout(() => {
                    uni.redirectTo({
                        url: `/customer/detail?id=${data.client_id}&website_id=${this.$store.state.website_id}`
                    });
                }, 300)
            }catch(e){}
        },
        //置顶
        async setTop(){
            if(this.setToping){
                return;
            }
            this.setToping = true;
            try{
                const res = this.isTrans ? await setTransCustomerTop(this.customerId) : await setCrmCustomerTop(this.customerId);
                this.show = false;
                uni.$emit('getDataAgain');
                uni.showToast({
                    title: res && res.msg ? res.msg : '设置成功',
                    icon: 'success',
                });
            }catch(e){}
            this.setToping = false;
        },
        /**
         * 提醒跟进
         */
        reminder(){
            this.show = false;
            this.dialogs.remind = true;
        },
        async handleReminder(e){
            if(this.remindering == true){
                return
            }
            this.remindering = true;
            try{
                e.id = this.customerId
                this.isTrans ? await transReminder(e) : await crmReminder(e);
                this.dialogs.remind = false;
                uni.showToast({
                    title: "操作成功",
                    icon: "none",
                });
            }catch(e){}
            this.remindering = false;
        },
        //删除客户
        delCustomer(){
            uni.showModal({
                title: '提示',
                content: '确定删除该客户吗？',
                success: async (res) => {
                    if (res.confirm) {
                        try{
                            this.show = false;
                            const res = await delTransCustomer(this.customerId);
                            uni.showToast({
                                title: res && res.msg ? res.msg : '删除成功',
                                icon: 'success',
                            });
                            this.$store.commit('onBackRefresh', true);
                            setTimeout(() => {
                                this.$navigateBack(1, true);
                            }, 300)
                        }catch(e){}
                    }
                }
            })
        }
    }
        
}

</script>
    
<style scoped lang="scss"> 
.more_box {
  border-radius: 40rpx 40rpx 0px 0px;
  background: #ffffff;

  .more_title {
    justify-content: center;
    color: #8a929f;
    font-size: 32rpx;
    padding: 24rpx;
    border-bottom: 2rpx solid #f2f2f2;
  }
  .more_list {
    padding: 24rpx;
    flex-wrap: wrap;
    .more_item {
      width: 25%;
      justify-content: center;
      align-items: center;
      padding: 24rpx 0;
      .icon {
        width: 40rpx;
        height: 40rpx;
        overflow: hidden;
        image {
          width: 100%;
          height: 100%;
        }
      }
      .name {
        color: #8a929f;
        margin-top: 24rpx;
        font-size: 24rpx;
      }
    }
  }
}
.popup-contianer {
    border-radius: 32rpx 32rpx 0px 0px;
    background: #fff;
    padding: 36rpx 36rpx 160rpx;
    color: #292C39;
    .top {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        height: 140rpx;
        .item {
            flex: 1;
            height: 108rpx;
            flex-direction: row;
            align-items: center;
            font-size: 32rpx;
            position: relative;
            +.item::before{
                content: " ";
                position: absolute;
                left: 0;
                width: 1px;
                height: 34rpx;
                background-color: #b2b2b3;
            }
            &:last-child{
                justify-content: flex-end;
            }
        }
        .item-img {
        width: 40rpx;
        height: 40rpx;
        margin-right: 16rpx;
        }
    }

    .bottom {
    .item {
        display: flex;
        flex-direction: row;
        align-items: center;
        height: 108rpx;
        .item-img {
            width: 40rpx;
            height: 40rpx;
            margin-right: 16rpx;
        }
        .title{
            flex: 1;
            font-size: 32rpx;
        }
        
    }
    }
}
</style>