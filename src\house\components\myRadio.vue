<template>
  <label class="radio-item flex-row" @click="onChange">
    <radio class="radio" :value="value" :checked="true_label === value" color="#2d84fb" />
    <slot></slot>
  </label>
</template>

<script>
export default {
  props: {
    value: {
      type: [String, Number],
      default: '',
    },
    true_label: [String, Number],
  },
  model: {
    event: 'change',
    prop: 'value',
  },
  data() {
    return {}
  },
  methods: {
    onChange() {
      var value = ''
      if (!this.value || this.value == 0) {
        value = this.true_label
      }
      this.$emit('input', value)
      this.$emit('change', value)
    },
  },
}
</script>

<style lang="scss">
.radio-item {
  align-items: center;
  .radio {
    margin-right: 10rpx;
    transform: scale(0.7);
  }
}
</style>
