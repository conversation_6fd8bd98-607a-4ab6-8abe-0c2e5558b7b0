<template>
    <view :class="[(isFixedTop) ? 'fixedTop' : '']" id="box" class="box bgwhite">
	<view class="m-lr-30 row just-btw p-tb-30">
		<view class="row alignitems">
			<view class="f30 weight500 m-l-20">全部品牌</view>
		</view>
	</view>
</view>
</template>
<script>
export default {
data(){
	return{
		isFixedTop:false,
	}
},
onLoad(){
	uni.pageScrollTo({
		scrollTop:0,
		duration:0
	})
	setTimeout(()=>{
		this.GetTop()
		// this.aaa()
	},1000)
},
//监测页面滑动
onPageScroll(e) {
	if(e.scrollTop > this.Topdistance){
		this.isFixedTop = true
	}else{
		this.isFixedTop = false
	}
},
methods:{
	GetTop(){
		// 获取元素距离顶部的距离
		var _this=this
		uni.getSystemInfo({
			success:(resu)=>{
				const query = uni.createSelectorQuery()
				query.select('#box').boundingClientRect()
				query.selectViewport().scrollOffset()
				query.exec(function(res){
					_this.Topdistance=res[0].top
					
				})
			},
			fail:(res)=>{}
		})
	},
}
}
</script>
<style>
.fixedTop{
	position: fixed;
	width:100%;
	top:0;
	left: 0;
	z-index: 999;
}

</style>