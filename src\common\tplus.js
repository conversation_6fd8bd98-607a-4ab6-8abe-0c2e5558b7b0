import Utils from './utils.js';


export default {
	install(Vue)  {
		let isCrmConfigInited = false, isLoading = false;
		Vue.prototype.$Utils = Utils;
		Vue.mixin({
			onShow(options){
				//保存配置项
				try{
					let pages = getCurrentPages(),
						length = pages.length,
						currentPage = length > 0 ? pages[length - 1] : null;
					if(currentPage && currentPage.route == 'pages/user/phone_login'){
						isCrmConfigInited = false;
						this.$store.state.crmConfig = {};
					}else if(!isCrmConfigInited && !isLoading){
						isLoading = true;
						let isCrmApp = true;
						// #ifdef H5
						if(location.pathname === '/fenxiao/' || location.pathname.startsWith('/fenxiao/index/')){
							isCrmApp = false;
						}
						// #endif

						if(isCrmApp){
							//检查是否报备专员
							this.$ajax.get('/admin/crm/report/is_auditor', {}, (res) => {
								if (res.statusCode === 200) {
									this.$store.state.isReportAuditor = res.data == 1 ? true : false;
								}
								},()=>{}, {disableAutoHandle: true}
							);

							let website_id = options && options?.query?.website_id || uni.getStorageInfoSync('website_id') || uni.getStorageInfoSync('wxwork_id');
							this.$ajax.get(`/admin/crm/config/get_crm_config`, { website_id }, (res) => {
								if (res.statusCode === 200) {
									isCrmConfigInited = true;
									let crmConfig = res.data;
									if(crmConfig){
										this.$store.state.crmConfig = crmConfig;
										//报备专员
										console.log(uni.getStorageInfoSync('userInfo'),'报备专员报备专员报备专员');
										//uni.hideTabBar();
									}
								}
							},()=>{}, {disableAutoHandle: true});

							this.$ajax.get("/qywx/common/query",{},(res) => {
								if (res.statusCode === 200) {
									uni.setStorageSync("userInfo", JSON.stringify(res.data));
								}
							},()=>{}, {disableAutoHandle: true})
						}
						setTimeout(()=>{
							isLoading = false;
						},300)
					}
				}catch(e){}
				
				if(this.$store.state.backRefresh){
					this.$store.commit('onBackRefresh', false);
					if(this.$options.onBackRefresh){
						this.$options.onBackRefresh.call(this);
					}
				}
			}
		});
	}
}