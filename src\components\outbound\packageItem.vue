<template>
  <view class="list-item" @click="toDetail">
    <view
      class="list_item_top flex-1 flex-row items-center"
      :class="[
        item.status == 1 ? 'green' : '',
        item.status == 0 ? 'primary' : '',
        item.status == 2 ? 'gray' : '',
      ]"
    >
      <view class="item_title"> {{ item.name }} </view>
      <view class="item_status"> {{ item.status | filterStatus }} </view>
    </view>
    <view class="list_item_center flex-row items-center">
      <view class="list_item_item flex-1">
        <view class="list_item_item_num"> {{ item.count }} </view>
        <view class="list_item_item_name">总量 </view>
      </view>
      <view class="list_item_item list_item_item_c flex-1">
        <view class="list_item_item_num"> {{ item.callNumber }} </view>
        <view class="list_item_item_name">已拨 </view>
      </view>
      <view class="list_item_item flex-1">
        <view class="list_item_item_num"> {{ item.unCallNumber }} </view>
        <view class="list_item_item_name">未拨 </view>
      </view>
    </view>
    <view class="list_item_bottom">
      <view class="list_item_bottom_item flex-row">
        <view class="label"> 分配来源： </view>
        <view class="value"> {{ item.channel_name }} </view>
      </view>
      <view class="list_item_bottom_item flex-row">
        <view class="label"> 分配人： </view>
        <view class="value"> {{ item.user_name }} </view>
      </view>
      <view class="list_item_bottom_item flex-row">
        <view class="label"> 分配时间： </view>
        <view class="value"> {{ item.created_at }} </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      default: () => { }
    }
  },
  data () {
    return {

    }
  },
  filters: {
    filterStatus (val) {
      let name = ''
      switch (+val) {
        case 0:
          name = '未进行'
          break;
        case 1:
          name = '进行中'
          break;
        case 2:
          name = '已完成'
          break;

        default:
          name = '未进行'
          break;
      }
      return name
    }
  },
  methods: {
    toDetail () {
      this.$emit('toDetail', this.item)
    }
  }

}
</script>

<style lang="scss" scoped>
.list-item {
  margin-bottom: 24rpx;
  background: #ffffff;
  .list_item_top {
    font-size: 36rpx;
    padding: 18rpx 24rpx;
    border-radius: 12rpx 12rpx 0 0;
    color: #ffffff;
    &.green {
      background: #11d060;
    }
    &.primary {
      background: #2d84fb;
    }
    &.gray {
      background: #e3e8f3;
    }
    .item_title {
      flex: 1;
    }
    .item_status {
      font-size: 22rpx;
    }
  }
  .list_item_center {
    padding: 48rpx 24rpx;
    .list_item_item {
      text-align: center;
      &.list_item_item_c {
        // background: #F6F6F6;
        border-right: 2rpx solid #f6f6f6;
        border-left: 2rpx solid #f6f6f6;
      }
      .list_item_item_num {
        font-size: 48rpx;
        color: #2e3c4e;
        font-weight: 500;
      }
      .list_item_item_name {
        font-size: 22rpx;
        margin-top: 24rpx;
        color: #8a929f;
      }
    }
  }
  .list_item_bottom {
    padding: 0 24rpx 24rpx;
    .list_item_bottom_item {
      margin-bottom: 24rpx;
    }
  }
}
</style>