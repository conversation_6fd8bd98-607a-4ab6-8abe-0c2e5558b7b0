<template>
    <index :current="current" ref="customerList"></index>
</template>
<script>
import index from '@/customer/components/customer_list/index.vue';
import customerListMixins from '../common/mixis/customer_list.js';
export default {
	components: {
		index
	},
	mixins: [customerListMixins],
	data(){
		return {
			current: 'my'
		}
	},
	onLoad(options) {
		this.current = options.current || 'my'
	},
};
</script>