<template>
    <view class="list">
        <view v-for="item in list" :key="item.id" @click="goReportDetail(item.id)">
            <reportListItem :item="item" show-admin></reportListItem>
        </view>
        <loadMore :status="loadStatus"/>   
    </view>
</template>

<script>
import reportListItem from '@/report/components/reportListItem'
import loadMore from "@/components/loadMore.vue";
export default {
    components: {
        loadMore,
        reportListItem
    },
    props: {
		params: { type: Object, default: () => ({})}
	},
    data(){
        return {
            loading: false,             //是否加载中
            isEmpty: false,             //是否空数据
            isNoMore: false,            //是否有更多
            page: 1,
            list: []
        }
    },
    computed: {
        loadStatus(){
            if(this.loading) return 'loading';
            if(this.isEmpty) return 'empty';
            if(this.isNoMore) return 'nomore';
            return '';
        }
    },
    filters: {
        
    },
    created(){
        this.getList();
    },
    methods: {
        async getList(){
            if(this.isNoMore){
                return;
            }
            this.loading = true;
            let params = {...this.params, page: this.page};
            this.page === 1 && (this.list = []);
            this.$ajax.get('/admin/crm/report/apply_search', params, res => {
                if (res.statusCode === 200) {
                    this.page++;
                    const data = (res.data?.data || []);
                    this.list = params.page === 1 ? data : this.list.concat(data);
                    if (data.length === 0) {
                        if(params.page === 1){
                            this.isEmpty = true;
                        }
                        this.isNoMore = true;
                    }
                }else{
                    uni.showToast({
                        title: res?.data?.message || '获取列表数据失败',
                        icon: 'none'
                    })
                }
                this.loading = false;
            }, er => {
                this.loading = false;
                console.log(er)
            });
        },
        search(){
            this.page = 1;
			this.isEmpty = false;
            this.isNoMore = false;
			this.getList();
        },
        goReportDetail(id){
            this.$navigateTo(`/report/detail?id=${id}`);
        }
    },
}
</script>