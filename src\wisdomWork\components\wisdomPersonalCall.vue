<template>
    <view>
        <wisdomTitleBar>
            我的外呼
            <template #right>
                <wisdomFilters v-model="params.times" :dates.sync="params.dates" @filter="getStatistics"></wisdomFilters>
            </template>
        </wisdomTitleBar>
        <wisdomDataPanes :data="statistics"></wisdomDataPanes>
    </view>    
</template>

<script>
import wisdomTitleBar from './wisdomTitleBar';
import wisdomFilters from './wisdomFilters';
import wisdomDataPanes from './wisdomDataPanes';
import { myCallStatistics } from '@/common/utils/wisdom-work.js'
export default {
    props: {
        loading: { type: Boolean, default: false }
    },
    components: {
        wisdomTitleBar,
        wisdomFilters,
        wisdomDataPanes
    },
    data() {
        return {
            params: {
                times: '',
                dates: []
            },
            statistics: [],
        }
    },
    created(){
        this.getStatistics();
    },
    methods: {
        //获取我的拨打统计
        async getStatistics(){
            const params = {...this.params};
            if(params.times == 'now_week'){
                params.times = 'this_week';
            }else if(params.times == 'now_month'){
                params.times = 'this_month';
            }
            const data = await myCallStatistics({ times: params.times }) || {};
            this.$emit('update:loading', false);
            this.statistics = [
                { label: '拨打总量', value: data.call_count || 0, bg: 'linear-gradient(180deg, #5E9AFD 0%, #5E81FD 100%)' },
                { label: '拨通总量', value: data.on_call_count || 0, bg: 'linear-gradient(180deg, #FFA371 0%, #F96A57 100%)' },
                { label: '接通率', value: data.on_call_percentage || 0, bg: 'linear-gradient(180deg, #59EC77 0%, #23C329 100%)' },
                { label: '平均时长', value: this.$Utils.formatDuration(data.avg_duration || 0), bg: 'linear-gradient(180deg, #48EEE8 0%, #37A5D4 100%)' },
                { label: '通话时长', value: this.$Utils.formatDuration(data.duration || 0), bg: 'linear-gradient(180deg, #FF9C3D 0%, #FF7D00 100%)' },
            ];
        }
    }
}
</script>