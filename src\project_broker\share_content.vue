<template>
  <view class="share-box">
    <textarea
      v-model="form_info.content"
      placeholder="请在这里填写详细内容"
      cols="30"
      rows="10"
      maxlength="-1"
    ></textarea>
    <view class="img-box">
      <view class="title row"> 是否开启<text>*</text> </view>
      <switch :checked="form_info.enable" @change="onSwitch" />
    </view>
    <view class="img-box">
      <view class="title row"> 上传照片/视频<text>*</text> </view>
      <myUpload
        :imageList="img_list"
        :upload_category="9"
        :is_visit="false"
        :is_video="true"
      ></myUpload>
    </view>
    <view class="btn" @click="onCreate">立即发布</view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import myUpload from "../components/upload";
import { mapState } from "vuex";
export default {
  components: { myUpload },
  data() {
    return {
      form_info: {
        project_id: "",
        enable: true,
        img_1: "",
        img_2: "",
        img_3: "",
        img_4: "",
        img_5: "",
        img_6: "",
        img_7: "",
        img_8: "",
        img_9: "",
        content: "",
      },
      img_list: [],
      is_edit: false,
    };
  },
  onLoad(options) {
    this.form_info.project_id = options.id;
    if (options.is_edit) {
      this.getQueryData(options.is_edit);
      this.is_edit = true;
    }
  },
  computed: {
    ...mapState(["user_info"]),
  },
  methods: {
    onSwitch(e) {
      this.form_info.enable = e.detail.value;
    },
    getQueryData(id) {
      this.$ajax.get(`/common/project/share/content/query/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.form_info.id = res.data.id;
          this.form_info.enable = res.data.enable === 1 ? true : false;
          this.form_info.project_id = res.data.project_id;
          let imgs = [];
          imgs.push(
            res.data.img_1,
            res.data.img_2,
            res.data.img_3,
            res.data.img_4,
            res.data.img_5,
            res.data.img_6,
            res.data.img_7,
            res.data.img_8,
            res.data.img_9
          );
          this.img_list = imgs.filter(function(s) {
            return s && s.trim(); // 注：IE9(不包含IE9)以下的版本没有trim()方法  （移除收尾空格）
          });
          this.form_info.content = res.data.content;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    onCreate() {
      uni.showLoading({ title: "请稍后。。。" });
      this.form_info.img_1 = this.img_list[0];
      this.form_info.img_2 = this.img_list[1];
      this.form_info.img_3 = this.img_list[2];
      this.form_info.img_4 = this.img_list[3];
      this.form_info.img_5 = this.img_list[4];
      this.form_info.img_6 = this.img_list[5];
      this.form_info.img_7 = this.img_list[6];
      this.form_info.img_8 = this.img_list[7];
      this.form_info.img_9 = this.img_list[8];
      let url = this.is_edit
        ? "/common/project/share/content/update"
        : "/common/project/share/content/create";
      this.$ajax.post(url, this.form_info, (res) => {
        if (res.statusCode === 200) {
          this.$navigateTo(
            `/project_broker/get_customer?id=${this.form_info.project_id}&isPro=${this.user_info.category}`
          );
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.share-box {
  padding: 48rpx;
  textarea {
    width: 100%;
    padding: 24rpx 0;
    height: 280rpx;
  }
  .img-box {
    .title {
      font-size: 32rpx;
      color: #333;
      margin: 48rpx 0;
      text {
        color: #ff6666;
      }
    }
  }
  .btn {
    width: 100%;
    height: 88rpx;
    color: #fff;
    background: #0174ff;
    box-shadow: 0 4px 16px 0 rgba(1, 116, 255, 0.4);
    border-radius: 44rpx;
    text-align: center;
    line-height: 88rpx;
    margin-top: 80rpx;
  }
}
</style>
