<template>
	<view class="container">
		<scroll-view  class="body" scroll-y="true">
			<view class="reprot-info">
				<reportListItem :item="detail" show-admin show-channel show-copay>
					<template #op>&nbsp;</template>
				</reportListItem>
			</view>
			<template v-if="isInited">
				<view class="project-item" v-for="(item,index) in projectList" :key="index">
					<view class="project-name">{{item.project_name}} 报备回执 <text v-if="isAuditor" class="must">*</text> </view>
					<view class="project-content">
						<template  v-if="!isAuditor">
							<!-- <view class="form-row" v-if="!item.id">
								<view class="row-label">暂无回执信息</view>
							</view> -->
							<template>
								<view class="form-row">
									<view class="row-cont upload-wrapper" v-if="item.url.length">
										<view v-for="(v, i) in item.url" :key="i" class="img-box"  @click="previewImg(item.url, i)">
											<image :src="v" mode="aspectFill"></image>
										</view>
										<view class="img-box perch"></view>
									</view>
									<view class="row-cont" v-else>
										<text class="empty-text">暂无</text>	
									</view>
								</view>
								<view class="form-row vertical-form-row">
									<view class="row-label">备注</view>
									<view class="row-cont" v-if="item.remarks">
										<view>{{item.remarks}}</view>
									</view>
									<view class="row-cont" v-else>
										<text class="empty-text">暂无</text>	
									</view>
								</view>
							</template>
						</template>
						<template v-else>
							<view class="form-row">
								<view class="row-label"><!-- 二维码 --></view>
								<view class="row-cont upload-wrapper">
									<upload :chooseType="1" action="/common/file/upload/admin" :upInfo="{category:6}" :max-count="9"
										:imgs="item.url"
										@delImg="e=>handleUploadSuccess(index, e)"
										@uploadDone="e=>handleUploadSuccess(index, e)"
									></upload>
								</view>
							</view>
							<view class="form-row">
								<view class="row-label">备注</view>
								<view class="row-cont textarea-wrapper">
									<textarea v-model="item.remarks" placeholder="请输入备注信息" maxlength="-1" adjust-position :show-confirm-bar="false"
										confirm-type="确认" @confirm="confirm" class="reason-textarea"/>
								</view>
							</view>
						</template>
						
					</view>
				</view>
				<view class="project-item">
					<view class="form-row">
						<view class="row-label">状态</view>
						<view class="row-cont status-wrapper">
							<view v-for="v in statusList" :key="v.value" class="status-item" :class="{active:v.value===params.status}" @click="params.status=v.value">{{v.label}}</view>
						</view>
					</view>
					<view class="form-row form-rows-row"  v-if="isAuditor">
						<view class="row-label">抄送成员</view>
						<view class="row-cont" @click="dialogs.ccUserPicker = true">
							<view class="select-wrapper">
								<view class="select-text">
									<text v-if="params.cc_to.length">
										{{ccUserNames}}
									</text>
									<text class="placeholder" v-else>请选择</text>
								</view>
								<icons type="jinrujiantou" color="#aaa" size="28rpx"></icons>
							</view>
						</view>
					</view>

				</view>
			</template>
		</scroll-view>

		<view class="footer" v-if="isAuditor">
            <button type="primary" :loading="submiting" @click="handleSubmit">确认</button>
        </view>

		<tMemberPicker :visible.sync="dialogs.ccUserPicker" v-model="params.cc_to" multiple @confirm="confirmSelectCcUser" api="/admin/crm/report/admin_user/list"></tMemberPicker>
	</view>
</template>
<script>
import reportListItem from '@/report/components/reportListItem'
import upload from "@/components/customer/upload.vue";
import { checkIsReportAuditor, getReportDetail } from '@/common/utils/report';
import icons from "@/components/my-icon";
import tMemberPicker from '@/components/tplus/tMemberPicker.vue';
export default {
	components: {
		upload,
		reportListItem,
		icons,
		tMemberPicker
	},
	data(){
		return {
			isInited: false,
			submiting: false,
			detail: {},
			projectList: [],
            statusList: [
                { value: 0, label: '已抄送' },
                { value: 1, label: '已报备' },
                { value: 2, label: '已驳回' },
                { value: 3, label: '已到访' },
                { value: 4, label: '未到访' },
            ],
			params: {
				id: '',
				status: '',
				cc_to: []
			},
			ccUserNames: '',
			dialogs: {
				ccUserPicker: false,
			},
			isAuditor: false,		//是否审核员
		}
	},
	onLoad(options){
		this.params.id = options.id;
		this.init();
	},
	methods: {
		async init(){
			uni.showLoading({
				title: "加载中"
			});
			try{
				await this.getReportDetail();
			
				let temp = uni.getStorageSync("userInfo");
				const userInfo = temp ? JSON.parse(uni.getStorageSync("userInfo")) : {};
				
				if(userInfo.id){
					let channel_uids = this.detail.channel_uid ? this.detail.channel_uid.split(',').map(e=>e*1) : [];
					console.log(channel_uids, userInfo.id, channel_uids.includes(userInfo.id*1));
					if(channel_uids.includes(userInfo.id)){
						let res = await checkIsReportAuditor();
						this.isAuditor = res ? true: false;
					}
				}
			}catch(e){}
			this.$nextTick(()=>{
				this.isInited = true;
				uni.hideLoading();
			})
		},
		async getReportDetail(){
			let data = await getReportDetail(this.params.id)
			if(data){
				this.detail = data;
				//报备项目列表
                const projectNames = data.report_project ? data.report_project.split(',') : [];
                this.projectList = data.project_id ? data.project_id.split(',').map((project_id, index) => {
                    const projectData = (data.project || []).find(e=>e.project_id == project_id) || {};
                    return {
                        id: projectData.id || '',
                        report_id: data.id,
                        project_id,
                        project_name: projectNames[index] || '--',
                        url: projectData.url ? projectData.url.split(',') : [],
                        remarks: projectData.remarks || ''
                    }
                }) : [];
				//报备审核状态
				this.params.status = data.status;
			}
		},
		handleUploadSuccess(index, e){
			this.projectList[index].url = e || [];
		},
		previewImg(urls, current){
			uni.previewImage({
                urls,
				current
            });
		},
		async handleSubmit(){
			const params = { 
				report_id: this.params.id,
				status: this.params.status,
				handle: this.projectList.filter(e => {
					return e.id || e.remarks || e.url.length
				}).map(e => {
					let obj = {
						project_id: e.project_id,
						url: e.url.join(','),
						remarks: e.remarks
					}
					e.id && (obj.id = e.id)
					return obj;
				}),
				cc_to: this.params.cc_to.join(',')
			};

			//至少有一个项目上传回执
			/* let isUpload = params.handle.find(e=>e.url!='') ? true : false;
			if(!isUpload){
				uni.showToast({
					title: '请上传报备回执',
					icon: 'none'
				});
				return;
			} */
			
			this.submiting = true;
			this.$ajax.post('/admin/crm/report/update',params, async (res)=>{
				if(res.statusCode == 200){
					uni.showToast({
						title: res.data?.msg || "提交成功",
						icon: "success",
					});
					uni.$emit("refreshReprotList")
					await this.getReportDetail();
					this.submiting = false;
				}else{
					this.submiting = false;
					uni.showToast({
						title: res.data.message,
						icon: "none",
					});
				}
			}, er=>{
				this.submiting = false;
			})
		},

		confirmSelectCcUser(data){
			this.ccUserNames = (data || []).map(e=>e.label[0]).join('、')
			this.dialogs.ccUserPicker = false;
		}
	},
}
</script>

<style lang="scss" scoped>
.container{
	height: 100vh;
	display: flex;
	flex-direction: column;
}
.body{
	flex: 1;
	overflow: auto;
	background-color: #f7f7f7;
	.reprot-info{
		margin: 0 0 28rpx;
		background-color: #fff;
		::v-deep{
			.list-item{
				margin: 0;
				padding-top: 14rpx;
			}
		}
		.title{
			font-size: 34rpx;
			text-align: center;
			padding-top: 38rpx;
		}
	}
	.project-item{
		background-color: #fff;
		margin-bottom: 28rpx;
		padding: 32rpx;
	}
	.project-name{
		flex-direction: row;
		color: #999;
		color: #3c3c3c;
		font-size: 32rpx;
        font-weight: 600;
		.must{
			display: none;
			color: #f40;
			padding-left: 8rpx;
		}
	}
	.project-content{
		margin-top: 28rpx;
	}
    .form-row{
        padding: 8rpx 0;
        overflow: hidden;
        flex-direction: column;
		&.form-rows-row{
			flex-direction: row;
    		align-items: center;
			margin-top: 16rpx;
			.row-cont{
				flex: 1;
				display: flex;
				flex-direction: row;
				align-items: center;
				margin: 0;
				justify-content: flex-end;
				.select-wrapper{
					flex-direction: row;
					align-items: center;
					line-height: 1.5;
					.placeholder{
						opacity: .6;
					}
				}
			}
		}
		+.form-row{
			margin-top: 28rpx;
		}
        .row-label{
            white-space: nowrap;
            padding-right: 24rpx;
			color: rgba(41, 44, 57, 0.6);
        }
        .row-cont{
            margin-top: 24rpx;
            flex-direction: row;
            flex-wrap: wrap;
            &.upload-wrapper{
                margin-top: 14rpx;
            }
            &.status-wrapper{
                margin-top: 0;   
            }
            .status-item{
                display: inline-flex;
                flex-direction: row;
                height: 66rpx;
                align-items: center;
                font-size: 28rpx;
                color: #4E5969;
                background-color: #f6f6f6;
                border: 1rpx solid #f1f2f3;
                padding: 0 28rpx;
                margin: 24rpx 24rpx 0 0;
                &.active{
                    color: #2d84fb;
                    background-color: #e5eeff;
                    border-color: #d9e8ff;
                }
            }
        }

		&.vertical-form-row{
			flex-direction: row;
			line-height: 1.5;
			.row-cont{
				flex: 1;
				margin-top: 0;
    			word-break: break-all;
			}
		}
    }

    .textarea-wrapper{
        background: #F8F8F8;
        padding: 12px 12px 0px 12px;
		&.readonly{
			padding-bottom: 24rpx;
			line-height: 1.5;
			word-break: break-all;
		}
    }
    .reason-textarea{
        height: 200rpx;
    }
}

.footer{
	display: flex;
	flex-direction: row;
	align-items: center;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	z-index: 1;
	padding: 28rpx 32rpx;
	padding-bottom: calc(constant(safe-area-inset-bottom) + 28rpx);
    padding-bottom: calc(env(safe-area-inset-bottom) + 28rpx);
    button{
        width: 100%;
        &[type=primary]{
            background-color: #2d84fb;
        }
    }
}

.upload-wrapper {
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
}
.upload-btn,.img-box {
  width: 30%;
  margin: 3% 0;
  height: 0;
  padding-bottom: 30%;
  text-align: center;
  box-sizing: border-box;
  background-color: #f3f3f3;
  position: relative;
  display: flex;
  &.perch {
    height: 0;
    padding: 0;
    border: 0;
    font-size: 0;
    margin: 0;
  }
  image{
	height: 100%;
    width: 100%;
	position: absolute;
  }
}
</style>