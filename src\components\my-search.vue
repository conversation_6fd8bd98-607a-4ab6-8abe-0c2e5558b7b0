<template>
  <view class="my-search" @click="$emit('click')">
    <slot name="left" />
    <input
      class="input"
      type="text"
      :placeholder="placeholder"
      :disabled="disabled"
      :focus="focus"
      confirm-type="search"
      v-model="now_value"
      @input="handleInput"
      @confirm="confirm"
      :style="{
        width: width,
      }"
    />
    <slot name="right" />
  </view>
</template>

<script>
export default {
  components: {},
  props: {
    placeholder: {
      type: String,
      default: "请在这里输入",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    focus: {
      type: Boolean,
      default: false,
    },
    value: {
      default: "",
    },
    width: String,
  },
  model: {
    event: "input",
    prop: "value",
  },
  computed: {
    now_value: {
      get: function() {
        return this.value;
      },
      set: function() {},
    },
  },
  data() {
    return {};
  },
  methods: {
    handleInput(e) {
      this.$emit("input", e.detail.value);
    },
    confirm(e) {
      this.$emit("confirm", e.detail.value);
    },
  },
};
</script>

<style scoped lang="scss">
.my-search {
  width: 287px;
  flex-direction: row;
  align-items: center;
  margin: 16rpx 48rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  height: 32px;
  border-radius: 4px;
  background-color: #eee;
  border: 1rpx solid #f3f3f3;
  .input {
    flex: 1;
    padding: 0 20rpx;
    height: 40rpx;
    font-size: 26rpx;
  }
}
</style>
