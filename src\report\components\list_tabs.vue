<template>
<view class="tabs">
    <tabBar :tabs="list" :fixed-top="false" :equispaced="false" :now-index="curTabIndex" @click="handleTabClick"></tabBar>
</view>
</template>

<script>
import tabBar from '@/components/tabBar.vue';
export default {
    props: {
        value: { type: [String, Number], default: 0 },
    },
    components: {
        tabBar
    },
    data() {
        return {
            curTabIndex: 0,         //当前选中的tab索引
            list: []                //tabs 数据
        }
    },
    watch: {
        curTabIndex: {
            handler(val) {
                let tab = this.list.find(e => e.index === val);
                this.$emit('input', tab ? tab.value : '');
            },
            immediate: true
        },
    },
    mounted() {
        //tabs 数据
        this.list = this.getTabs().map( (e, index) => {
            e.index = index;
            return e;
        });

        this.$watch('value', {
            handler(val) {
                if(val === ''){
                    this.$emit('input', this.list[0]?.value);
                }else{
                    let tab = this.list.find(e => e.value === val);
                    this.curTabIndex = tab ? tab.index : 0;
                }
            },
            immediate: true
        })
    },
    methods: {
        /**
         * 获取 tabs 数据
         */
        getTabs(){
            return [
                {description:'全部', value: ''},
                {description:'已回执', value: 1},
                {description:'未回执', value: 0},
            ];
        },
		handleTabClick(e){
            if(this.curTabIndex !== e.index){
                this.curTabIndex = e.index;
                this.$nextTick(()=>{
                    this.$emit('change', e);
                })
            }
		}
    }
}
</script>

<style scoped lang="scss"> 
::v-deep .nav-box{
    .nav-list{
        height: 88rpx;
        .nav-item {
            width: auto;
            padding: 0 32rpx;
            margin: 0;
            height: 100%;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            &.active {
                color: #007aff;
                &:before{
                    height: 6rpx;
                    width: 32rpx;
                }
            }
        }
    }
}
</style>