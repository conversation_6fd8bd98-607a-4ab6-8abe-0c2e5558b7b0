<template>
  <view class="approval" :class="{ pdb70: showFooter }" v-if="showContent">
    <view class="title common_style flex-row items-center">
      <!-- <view class="prelogo">
        <image :src="detail.agent.head_image" mode="widthFix"></image>
      </view> -->
      <view class="title_box flex-row align-center flex-1">
        <view class="title_con"> {{ detail.type_info && detail.type_info.title }} </view>
        <view class="status_box flex-row align-center">
          <view class="status" :class="'status' + detail.status">
            {{ formatStatus(detail.status) }}
          </view>
        </view>
      </view>
    </view>
    <view class="info common_style">
      <view class="info_name flex-row items-center" v-if="detail.type == 2">
        <view class="flex-row flex-1"> <text class="name"> 房源：</text>{{ detail.house }} </view>
        <view class="house_btn" @click="toHouse(detail.sys_hid)">查看</view>
      </view>
      <view class="info_name flex-row" v-for="(item, index) in detail.template" :key="index"
        ><text class="name"> {{ item.label }}：</text>{{ item.value }}
      </view>
      <view class="info_name flex-row"><text class="name"> 备注：</text>{{ detail.remarks }} </view>
      <view
        class="info_name flex-row"
        v-if="detail.recipients && detail.recipients.length == 0 && detail.status == 0"
      >
        <text class="name"> 抄送人：</text>
        <selectDown
          valueName="name"
          :list="recipientList"
          :multiple="true"
          @input="changeSelect"
          defaultValue=""
          placeholder="请选择抄送人"
        >
        </selectDown>
      </view>
      <view class="info_name flex-row">
        <text class="name">凭证</text>
        <view class="imgs flex-1 flex-wrap flex-row">
          <view class="img" v-for="(item, index) in detail.attachment" :key="index">
            <image :src="item"></image>
          </view>
        </view>
      </view>
    </view>
    <view class="time_lines">
      <timeLine :list="approval_list" v-slot="{ prop }">
        <!-- v-for="prop in approval_list" :key="prop.id" -->
        <view class="item">
          <view class="dot"></view>
          <view class="timeline_item">
            <view class="time flex-row items-center space-between">
              <view class="time_title">
                {{ prop.title }}
                <template v-if="prop.type != 1"> · {{ formatStatus(detail.status) }} </template>
              </view>
              <view class="time_con" v-if="prop.ctime && prop.type">
                {{ prop.ctime }}
              </view>
            </view>
            <view class="user_info">
              <view class="img">
                {{ prop.agent && prop.agent.user_name && prop.agent.user_name[0] }}
              </view>
              <!-- <image
                class="avatar"
                v-if="(prop.agent && prop.agent.head_image) || prop.head_image"
                :src="
                  ((prop.agent && prop.agent.head_image) || prop.head_image) | imageFilter('w80')
                "
                mode="aspectFill"
              />
              <image
                v-else
                class="avatar"
                src="/static/img/<EMAIL>"
                mode="aspectFill"
              /> -->
              <view class="flex-1">
                <view class="name"
                  >{{ prop.agent && prop.agent.user_name }}({{
                    prop.agent && prop.agent.department
                  }})</view
                >
                <!-- <view class="name tname">{{ (prop.agent && prop.agent.tname) || prop.tname }}</view> -->
              </view>
            </view>
            <view class="content" v-if="from == 'approve' && prop.type == 2">
              <!-- 0|| 2 可以填审批意见 -->
              <text v-if="detail.status == 1 || detail.status == 2">{{ prop.content }}</text>
            </view>
          </view>
        </view>
      </timeLine>
    </view>

    <view class="info_name pad" v-if="detail.status == 0">
      <text class="name">审批意见（留言内容）</text>
      <view class="textarea">
        <textarea
          type="text"
          v-model="approval.content"
          rows="3"
          placeholder="请输入审批意见（留言内容）"
        />
      </view>
    </view>

    <!-- -->
    <view class="footers flex-row items-center" v-if="showFooter && from == 'approve'">
      <view
        class="refuse flex-1 flex-row items-center justify-center"
        v-if="detail.status == 0"
        @click="submit(3)"
      >
        留言
      </view>
      <view class="refuse flex-1 flex-row items-center justify-center" @click="submit(2)">
        拒绝
      </view>
      <view class="agree flex-1 flex-row items-center justify-center" @click="submit(1)">
        同意
      </view>
    </view>
  </view>
</template>

<script>
import timeLine from './components/timeLine'
import selectDown from './components/w-select'
// import { getUserInfo } from '@/page_outside/utils/user.js'
export default {
  data () {
    return {
      detail: {
        agent: {},
      },
      id: '',
      currentId: '', //当前用户id
      approversIds: [], //审批者ids
      approval_list: [],
      approval: {
        id: '',
        content: '',
      },
      statusList: [],
      showFooter: false,
      from: 'approve',
      recipientList: [],
      showContent: false
    }
  },
  components: {
    timeLine,
    selectDown
  },
  computed: {
    statusLists () {
      return this.statusList || []
    }
  },
  filters: {
    formatStatus (val) {
      let current = (this.statusLists || []).find(item => item.values == val)
      console.log(current);
      if (current) {
        return current.name
      }
      return ''
    },
  },
  onLoad (options) {
    let token = uni.getStorageSync("wxwork_token");
    if (!token) {
      if (this.$isWxWork() == 'wxwork') {
        return
      } else {
        localStorage.setItem('backUrl', location.href)
        this.$router.push("https://yun.tfcs.cn")
      }
      // 未登录中断请求
    }
    if (options.id) {
      this.id = options.id
      this.approval.id = options.id
    }
    if (options.from) {
      this.from = options.from
    }
    uni.showLoading({
      title: "加载中",
      mask: true
    })
    // this.getUserInfo()
    this.getData()
    this.getStatusList()
    this.getRecipientList()
    // setTimeout(() => {
    //   this.showContent = true
    // }, 100);

  },
  methods: {
    getData () {
      this.$ajax.get(`/admin/house/approveDetail/${this.id}`, {}, (res) => {
        if (res.statusCode == 200) {
          this.detail = res.data
          this.setApprovalList()
          uni.hideLoading()
          this.showContent = true
        } else {
          uni.hideLoading()
          uni.showToast({
            title: res.message || '数据加载失败',
            icon: "none"
          })
        }
      })
    },
    formatStatus (val) {
      let statusName = ''
      let current = (this.statusLists || []).find(item => item.values == val)
      if (current) {
        return current.name
      }
      return ''
    },
    getStatusList () {
      this.$ajax.get(`/admin/house/approveStatus`, {}, (res) => {
        if (res.statusCode == 200) {
          this.statusList = res.data
        }
      })
    },
    getRecipientList () {
      this.$ajax.get("/admin/house/userListDepartment", {}, res => {
        if (res.statusCode == 200) {
          this.recipientList = res.data
        }
      })
    },
    changeSelect (e) {
      this.approval.recipients = []
      if (e.length) {
        e.map(item => {
          this.approval.recipients.push(item.values)
        })
      }
    },
    getUserInfo () {
      getUserInfo().then((res) => {
        this.user_info = res
        this.currentId = this.user_info.id //当前用户 id
        this.getData()
      })
    },
    setApprovalList () {
      let faqiInfo = {
        // id: this.detail.agent_id,
        ctime: this.detail.ctime,
        title: '发起人',
        content: '',
        type: '1',
        agent: this.detail.applicant,
      }
      let approversInfo = [],
        approversIds = [],
        recipientInfo = [],
        recipientIds = [],
        approvals = this.detail.approver_uid
      // if (this.detail.status == 0 ) {
      //   approvals = this.detail.approvers
      // } else {
      //   approvals = [this.detail.approver]
      // }
      if (this.detail.status != 0) {
        [this.detail.real_approver_uid].map(item => {
          let obj = {
            id: item.id,
            title: '审批人',
            ctime: this.detail.real_approver_time,
            agent: {
              user_name: item.user_name || item.name,
              department: item.department,
              // head_image: item.head_image,
              id: item.id,
            },
            content: this.detail.sys_memo,
            type: (this.detail.status == 0 || (this.detail.status != 0 && item.id == this.detail.real_approver_uid.id)) ? 2 : '',
          }
          approversIds.push(item.id)
          approversInfo.push(obj)
        })
      } else {
        approvals.map((item) => {
          let obj = {
            id: item.id,
            title: '审批人',
            ctime: this.detail.real_approver_time,
            agent: {
              user_name: item.user_name || item.name,
              department: item.department,
              // head_image: item.head_image,
              id: item.id,
            },
            content: this.detail.sys_memo,
            type: (this.detail.status == 0 || (this.detail.status != 0 && item.id == this.detail.real_approver_uid.id)) ? 2 : '',
          }
          approversIds.push(item.id)
          approversInfo.push(obj)
        })
      }

      this.detail.recipients.map(item => {
        let obj = {
          id: item.id,
          title: '抄送人',
          ctime: '',
          agent: {
            user_name: item.user_name || item.name,
            department: item.department,
            // head_image: item.head_image,
            id: item.id,
          },
          content: '',
          type: '',
        }
        recipientInfo.push(obj)
      })
      this.approval_list = [faqiInfo, ...approversInfo, ...recipientInfo,
      ]
      this.approversIds = approversIds
      if (this.detail.status == 0) {
        this.showFooter = true
      }
    },
    leaveMes () {
      let params = {
        id: this.id,
        sys_memo: this.approval.content,
        recipients: this.approval.recipients && this.approval.recipients.length ? this.approval.recipients.join(",") : ""
      }
      if (this.is_loading) return
      this.is_loading = true
      this.$ajax.post('/admin/house/approveMsg', params, res => {
        if (res.statusCode == 200) {
          uni.showToast({
            title: '操作成功',
            icon: 'none'
          })
          setTimeout(() => {
            this.is_loading = false
          }, 500);
          // this.$message.success("操作成功");
        }
      }, err => {
        this.is_loading = false
      })
    },
    toHouse (id) {
      this.$navigateTo("/house/detail?id=" + id)
    },

    submit (status) {
      if (status == 3) {
        this.leaveMes()
        return
      }
      if (this.is_loading) return
      this.is_loading = true
      this.$ajax
        .post('/admin/house/dealApprove', { id: this.id, status, sys_memo: this.approval.content, recipients: this.approval.recipients && this.approval.recipients.length ? this.approval.recipients.join(",") : "" }, (res) => {

          if (res.statusCode == 200) {
            uni.showToast({
              title: res.data || '操作成功',
              icon: 'none',
            })
            this.getData()
          }
          setTimeout(() => {
            this.is_loading = false
          }, 500);
        }, () => {
          this.is_loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.approval {
  min-height: calc(100vh - 44px);
  padding-top: 20rpx;
  &.pdb70 {
    padding-bottom: 140rpx;
  }
  // background: #f7f7f7;
  .common_style {
    padding: 0 24rpx;
    background: #fff;
  }
  .title {
    .prelogo {
      width: 96rpx;
      height: 96rpx;
      margin-right: 24rpx;
      overflow: hidden;
      border-radius: 50%;
      image {
        width: 100%;
        height: 100%;
      }
    }
    .title_box {
      .title_con {
        font-size: 32rpx;
        color: #2e3c4e;
        font-weight: bolder;
        margin-right: 10rpx;
      }
      .status {
        // display: inline-block;
        // margin-top: 24rpx;
        padding: 8rpx 36rpx;
        background: rgba(45, 132, 251, 0.15);
        border-radius: 4px;
        font-size: 22rpx;
        color: #2d84fb;
      }
    }
  }
  .info_name {
    margin-bottom: 24rpx;
    font-size: 22rpx;
    color: #8a929f;
    &.pad {
      padding: 10rpx 24rpx;
      .name {
        // width: 120rpx;
        margin-right: 24rpx;
        margin-bottom: 20rpx;
      }
    }
  }
  .info {
    margin: 0 0 24rpx;
    padding-top: 24rpx;
    border-bottom: 24rpx solid #f8f8f8;
    .info_name {
      margin-bottom: 40rpx;
      font-size: 22rpx;
      color: #8a929f;
      .house_btn {
        background: #2d84fb;
        color: #ffffff;
        border-radius: 4rpx;
        padding: 10rpx 20rpx;
      }

      .name {
        width: 120rpx;
        margin-right: 24rpx;
      }
      .imgs {
        .img {
          width: calc((100% - 48rpx) / 3);
          height: 176rpx;
          border-radius: 10rpx;
          overflow: hidden;
          &:nth-child(2) {
            margin: 0 24rpx;
          }
          image {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }

  .textarea {
    color: #8a929f;
    padding: 24rpx;
    height: 150rpx;
    background: #f8f8f8;
    border: 2rpx solid rgba(221, 225, 233, 1);
    border-radius: 8rpx;
    textarea {
      font-size: 22rpx;
      width: 100%;
    }
  }
  .time_lines {
    .time {
      margin-bottom: 16rpx;
      margin-top: -8rpx;
      font-size: 24rpx;
      font-weight: bold;
      color: #999;
      .time_title {
        font-size: 32rpx;
        color: #2e3c4e;
      }
    }
    .user_info {
      margin-bottom: 16rpx;
      flex-direction: row;
      align-items: center;
      .img {
        width: 62rpx;
        height: 62rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        color: #fff;
        background: #2d84fb;
        margin-right: 12rpx;
      }
      .avatar {
        margin-right: 12rpx;
        width: 62rpx;
        height: 62rpx;
        border-radius: 50%;
      }
      .name {
        font-size: 24rpx;
        color: #555555;
      }
      .tname {
        margin-top: 6rpx;
        font-size: 23rpx;
        color: $color-paragraph;
      }
      .tel {
        margin-left: 12rpx;
        flex-direction: row;
        align-items: center;
        font-size: 26rpx;
        color: #2d84fb;
      }
    }
    .content {
      margin-bottom: 16rpx;
      font-size: 28rpx;
      line-height: 1.5;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      display: -webkit-box;

      .status {
        margin-right: 12rpx;
        font-weight: bold;
        &.status1 {
          color: #3cc53c;
        }
        &.status2 {
          color: #8a929f;
        }
        &.status3 {
          color: #2d84fb;
        }
        &.status4 {
          color: #fe6c17;
        }
      }
    }
    .img_list {
      flex-direction: row;
      > image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 4rpx;
        margin-right: 16rpx;
        margin-bottom: 16rpx;
      }
    }
  }
  .footers {
    padding: 24rpx 48rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    .refuse {
      border: 2rpx solid rgba(221, 225, 233, 1);
      border-radius: 12rpx;
      font-size: 28rpx;
      color: #8a929f;
      margin-right: 24rpx;
      padding: 20rpx 0;
    }
    .agree {
      background: #2d84fb;
      border-radius: 12rpx;
      font-size: 28rpx;
      color: #fff;
      padding: 20rpx 0;
    }
  }
}
</style>
