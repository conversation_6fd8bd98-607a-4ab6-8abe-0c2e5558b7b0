<template>
  <scroll-view class="list row" scroll-y @scrolltolower="scrollToLower">
    <view
      class="cancel row"
      v-if="stepList.length > 0 && stepList[0].cancel_reason"
    >
      <text style="color:#333">无效原因：</text>
      {{ stepList[0].cancel_reason }}</view
    >
    <view class="box" v-for="item in stepList" :key="item.id">
      <view class="left"></view>
      <view class="right row">
        <view class="time">{{ item.created_at }}</view>
        <view class="content"
          >{{
            item.category === 1
              ? "【" +
                item.u_name +
                "-" +
                item.u_phone +
                "】" +
                item.description
              : item.description
          }}
        </view>
        <view class="img-box row" v-if="JSON.parse(item.data).length > 0">
          <image
            @click="$previewImage(img)"
            v-for="(img, index) in JSON.parse(item.data)"
            :key="index"
            :src="img"
            mode="aspectFill"
          ></image>
        </view>
      </view>
    </view>
    <loadMore :status="loadStatus"></loadMore>
  </scroll-view>
</template>

<script>
import loadMore from "@/components/loadMore";
export default {
  components: { loadMore },
  props: {
    stepList: Array,
    loadStatus: String,
  },
  methods: {
    scrollToLower(e) {
      this.$emit("scrollToLower");
    },
  },
};
</script>

<style scoped lang="scss">
.cancel {
  margin-bottom: 24rpx;
  color: #999;
}
.list {
  margin-top: 40rpx;
  flex-direction: column;
  line-height: 50rpx;
  margin-bottom: 100rpx;
  // height: 570rpx;
  .box {
    position: relative;
    margin-left: 20rpx;
    border-left: 2rpx dashed #999;
  }
  .left {
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    border: 8rpx solid #999;
    position: absolute;
    left: -20rpx;
    background: #fff;
  }
  .right {
    margin-bottom: 40rpx;
    flex-direction: column;
    margin-left: 30rpx;
    .time {
      font-size: 32rpx;
    }
    .content {
      color: #999;
      font-size: 26rpx;
    }
    .img-box {
      justify-content: flex-start;
      flex-wrap: wrap;
      image {
        width: 200rpx;
        height: 180rpx;
        padding: 20rpx;
      }
      &::after {
        content: "";
        width: auto;
      }
    }
  }
}
</style>
