<template>
  <text
    class="iconfont"
    :class="'icon' + type"
    :style="{
      color: color,
      fontSize: size,
      lineHeight: lineHeight,
      fontWeight: fontWeight,
    }"
    @click="$emit('click')"
  ></text>
</template>

<script>
export default {
  name: "myIcon",
  props: {
    type: {
      type: String,
      default: "",
    },
    color: {
      type: String,
      default: "#333333",
    },
    size: {
      type: [String],
      default: "38rpx",
    },
    lineHeight: {
      type: [Number, String],
      default: 1,
    },
    fontWeight: {
      type: [String],
      default: "initial",
    },
  },
};
</script>

<style>
@font-face {
  font-family: "iconfont"; /* project id 2083496 */
  src: url("//at.alicdn.com/t/font_2083496_4uyo0cg8ya.eot");
  src: url("//at.alicdn.com/t/font_2083496_4uyo0cg8ya.eot?#iefix")
      format("embedded-opentype"),
    url("//at.alicdn.com/t/font_2083496_4uyo0cg8ya.woff2") format("woff2"),
    url("//at.alicdn.com/t/font_2083496_4uyo0cg8ya.woff") format("woff"),
    url("//at.alicdn.com/t/font_2083496_4uyo0cg8ya.ttf") format("truetype"),
    url("//at.alicdn.com/t/font_2083496_4uyo0cg8ya.svg#iconfont") format("svg");
}
.iconfont {
  font-family: "iconfont" !important;
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  text-decoration: none;
  -webkit-font-smoothing: antialiased;
}
.iconhuifang:before {
  content: "\e67f";
}

.iconzhibo:before {
  content: "\e680";
}

.iconyugao:before {
  content: "\e681";
}

.iconbiaozhi:before {
  content: "\e67e";
}

.icontuwenxinximoban:before {
  content: "\e67c";
}

.iconbeitoutupianmoban:before {
  content: "\e67d";
}

.iconzuixindongtai:before {
  content: "\e678";
}

.icondujiayouhui:before {
  content: "\e679";
}

.iconxiangmuzixun:before {
  content: "\e67a";
}

.iconzhuanshufuwu:before {
  content: "\e67b";
}
.iconxiaoxi-bianji:before {
  content: "\e676";
}
.iconxiaoxi-shanchu:before {
  content: "\e677";
}
.iconyishoucang:before {
  content: "\e675";
}
.iconbaobeikehu:before {
  content: "\e674";
}

.iconsousuo:before {
  content: "\e672";
}

.iconguanbi:before {
  content: "\e671";
}

.iconbiaoqing:before {
  content: "\e66d";
}

.icontianjia:before {
  content: "\e66e";
}

.iconyuyin:before {
  content: "\e66f";
}

.iconjianpan:before {
  content: "\e670";
}

.iconfangdaijisuanqi-wenhao:before {
  content: "\e66c";
}

.icondengluchahua:before {
  content: "\e66b";
}

.icon1fenxiang:before {
  content: "\e66a";
}

.icon2kefudianhua:before {
  content: "\e664";
}

.icon1loupanxinxi:before {
  content: "\e665";
}

.icon4guanyuwomen:before {
  content: "\e666";
}

.iconwodexiaoxi:before {
  content: "\e667";
}

.icon3wentifankui:before {
  content: "\e668";
}

.iconwodeshoucang:before {
  content: "\e669";
}

.iconyou2:before {
  content: "\e673";
}

.iconyou:before {
  content: "\e60e";
}

.iconquanjingkanfang:before {
  content: "\e663";
}

.iconfangdaijisuan:before {
  content: "\e662";
}

.icontouxiangshenglve:before {
  content: "\e661";
}

.icon2zixun:before {
  content: "\e65f";
}

.icon1dianhua:before {
  content: "\e660";
}

.icon2zhidianshoulouchu:before {
  content: "\e65d";
}

.icon1pinpaijieshao:before {
  content: "\e65e";
}

.icon1shouye:before {
  content: "\e658";
}

.icon2loupan:before {
  content: "\e659";
}

.icon3dongtai:before {
  content: "\e65a";
}

.icon5wode:before {
  content: "\e65b";
}

.icon4loushu:before {
  content: "\e65c";
}
</style>
