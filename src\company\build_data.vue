<template>
  <view class="list">
    <view class="title-bar">
      <view class="search-tab">
        <my-search
          style="width:654rpx"
          placeholder="请输入楼盘名称"
          @input="onInput"
        >
          <template v-slot:left>
            <myIcon type="ic_sousuo3x1" color="#999"></myIcon>
          </template>
          <!-- <template v-slot:right>
            <text class="xinzeng" @click="newAdd">新增</text>
          </template> -->
        </my-search>
      </view>
    </view>
    <view class="build-list">
      <my-build
        v-for="(item, index) in build_list"
        :key="index"
        :item="item"
        @click="
          $navigateTo(
            `/company/build_data_statistics?id=${item.build_id}&company_id=${params.company_id}`
          )
        "
        ><template v-slot:yong v-if="display_brokerage">
          <view class="price-right row" v-if="item.brokerage_rule">
            <text class="yong">佣</text>
            <text class="jiage">{{
              item.store_brokerage_rule ||
                item.brokerage_rule | formatBrokerageRule
            }}</text>
          </view>
        </template></my-build
      >
    </view>
    <load-more :status="load_status"></load-more>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import mySearch from "@/components/my-search";
import myIcon from "@/components/my-icon";
import loadMore from "@/components/loadMore";
import myBuild from "@/components/build-item";
import { mapActions, mapState } from "vuex";
export default {
  components: { mySearch, myIcon, myBuild, loadMore },
  data() {
    return {
      build_list: [],
      build_status_list: [],
      build_category_list: [],
      params: {
        page: 1,
        build_name: "",
        company_id: "",
      },
      load_status: "",
      display_brokerage: false,
    };
  },
  onLoad(options) {
    this.params.company_id = options.company_id;
    this.params.all = options.all || "";
    this.init();
  },
  computed: {
    ...mapState(["city"]),
  },
  methods: {
    ...mapActions(["getSetting"]),
    init() {
      // 佣金是否显示
      this.getSetting((e) => {
        let display = e.login_display_brokerage_rule;
        let token = uni.getStorageSync("token" + this.$store.state.website_id);
        if (display == 1 || token) {
          this.display_brokerage = true;
        } else if (display == 0) {
          this.display_brokerage = false;
        }
        if (e.city_type == 2) {
          this.params.region_0 = this.city.region_0;
          this.params.region_1 = this.city.region_1;
        }
      });
      this.$setDictionary((e) => {
        e.find((item) => {
          switch (item.name) {
            case "BUILD_STATUS":
              this.build_status_list = item.childs;
              break;
            case "BUILD_CATEGORY":
              this.build_category_list = item.childs;
              this.getDataList();
              break;
            default:
              break;
          }
        });
      });
    },
    getDataList() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.build_list = [];
      }
      this.$ajax.get("/client/project/list", this.params, (res) => {
        this.load_status = "loadend";
        if (res.statusCode === 200) {
          this.build_list = this.build_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
            uni.showToast({
              title: "没有更多数据了",
              icon: "none",
            });
          }
          this.$matchBuildType(
            this.build_list,
            this.build_status_list,
            this.build_category_list
          );
        } else {
          uni.showToast({
            title: "请求错误",
            icon: "none",
          });
        }
      });
    },
    newAdd() {
      this.$navigateTo("/report/report_client?currentTel=1");
    },
    onInput(e) {
      this.$debounce(this.onInputDebounce, 500)(e);
    },
    onInputDebounce(e) {
      let params = {
        page: 1,
        build_name: e,
        region_0: this.city.region_0,
        region_1: this.city.region_1,
      };
      this.load_status = "loading";
      this.$ajax.get("/client/project/list", params, (res) => {
        if (res.statusCode === 200) {
          this.load_status = "loadend";
          this.build_list = res.data.data;
          this.$matchBuildType(
            this.build_list,
            this.build_status_list,
            this.build_category_list
          );
          if (res.data.data.length == 0) {
            this.load_status = "nomore";
            uni.showToast({
              title: "没有更多数据了",
              icon: "none",
            });
          }
        }
      });
    },
  },
  onPullDownRefresh() {
    this.getDataList();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>

<style scoped lang="scss">
.list {
  .title-bar {
    background: #fff;
    .xinzeng {
      position: absolute;
      right: 48rpx;
      color: #0174ff;
    }
  }
  .search-tab {
    background: #fff;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 9;
  }
  .build-list {
    margin-top: 120rpx;
    padding: 24rpx 48rpx;
    .price-right {
      align-items: center;
      .yong {
        line-height: 40rpx;
        text-align: center;
        width: 40rpx;
        height: 40rpx;
        color: #fff;
        background: #fec923;
        border-radius: 4px;
      }
      .jiage {
        font-size: 24rpx;
        color: #333;
      }
    }
  }
}
</style>
