<!DOCTYPE html>
<html lang="zh-CN">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" >
        <title>
            <!-- <%= htmlWebpackPlugin.options.title %> -->
        </title>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                document.documentElement.style.fontSize = document.documentElement.clientWidth / 20 + 'px'
            })
            var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
            document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '') + '" />')
        </script>
        <link rel="stylesheet" href="<%= BASE_URL %>static/index.<%= VUE_APP_INDEX_CSS_HASH %>.css" />
        <script src="https://res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
        <!-- <script src="http://api.tianditu.gov.cn/api?v=4.0&tk=14a2fe5c2a2b43a22e4367b906e75edd" type="text/javascript"></script> -->
        <!-- <script src="https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js" type="text/javascript"></script> -->
        <!-- <script src="https://res.wx.qq.com/wwopen/js/jsapi/jweixin-1.0.0.js" type="text/javascript"></script> -->
    </head>

    <body>
        <noscript>
            <strong>Please enable JavaScript to continue.</strong>
        </noscript>
        <div id="app"></div>
        <!-- built files will be auto injected -->
    </body>

</html>