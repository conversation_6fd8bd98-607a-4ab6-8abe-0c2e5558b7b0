import { formatImg } from '@/page_outside/tools/index'
export default {
  onShareAppMessage(res) {
    if (res.from === 'button') {
      console.log('点击按钮')
    }
    if (this.share) {
      return {
        title: this.share.title || '',
        path: this.share.path,
        imageUrl: this.share.pic ? formatImg(this.share.pic, 'w_6401') : '',
        success: function(res) {
          console.log('成功', res)
        }
      }
    }
  }
}
