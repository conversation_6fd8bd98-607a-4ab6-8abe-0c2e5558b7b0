<template>
<view class="container">
    <view class="top" @touchmove.stop.prevent="stopMove">
        <view @tap="handleTopTap">
            <!-- <tabs :current="current" :name="tabName"></tabs> -->
            <searchInput :current="current" :params="params"></searchInput>
        </view>
        <filterBar :popupVisible.sync="filterPopupVisible" :current="current" @filter="handleFilter" :params="params" ref="filterBar"></filterBar>
    </view>
    <list :current="current" :params="params" :auto-load="false" ref="customerList"></list>
</view>
</template>
<script>
import tabs from '@/customer/components/customer_list/tabs.vue';
import searchInput from '@/customer/components/customer_list/search_input.vue';
import filterBar from '@/customer/components/customer_list/filter_bar.vue';
import list from "@/customer/components/customer_list/list.vue"
export default {
    props: {
        current: { type: String, default:'my' },
        tabName: { type: String, default:'' },
    },
    components: {
        tabs,
        searchInput,
        filterBar,
        list
    },
    data () {
        return {
            filterPopupVisible: false,           //弹出面板是否显示
            params: {},
        };
    },
    mounted(){
        const pages = getCurrentPages();
        const options = pages[pages.length - 1].options || {}

        const params = {};
        if(options.c_type2){
            params.c_type2 = parseInt(options.c_type2) || '';
        }
        
        if(options.shared){
            params.shared = parseInt(options.shared) || '';
        }
        if(options.c_type6){
            let c_type6 = parseInt(options.c_type6) || '';
            if(c_type6 == 7){
                params.shared = 1
            }else{
                params.c_type6 = c_type6
            }
        }
        

        if(options.c_type4){
            params.c_type4 = parseInt(options.c_type4) || '';
        }
        
        if(options.date_style){
            params.date_style = parseInt(options.date_style) || '';
        }
        if(options.date_type){
            params.date_type = parseInt(options.date_type) || '';
            if(params.date_type){
                //我掉公的-掉公时间
                if(params.c_type6 === 6){
                    params.date_style = 5;
                }else if(params.c_type6 === 5){    //我掉公的-转公时间
                    params.date_style = 6;
                }else if(params.c_type2 === 2){     //已跟进-跟进时间
                    params.date_style = 2;
                }
            }
        }

        
        

        this.params = params;
        
        this.$nextTick(()=>{
            setTimeout(()=>{
                this.$refs.filterBar.emitFilter();
            })
        })
    },
    methods: {
        search(){
            console.log('------------加载列表数据入口--------------');
            this.$refs.customerList.search();
        },
        upCustomerFollowContent(data){
            this.$refs.customerList.upCustomerFollowContent(data);
        },
        getList(){
            this.$refs.customerList.getList();
        },
        handleFilter(params){
            this.params = params;
            this.$nextTick(()=>{
                this.search();
            })
        },
        handleTopTap(){
            if(this.filterPopupVisible){
                this.filterPopupVisible = false;
            }
        },
        stopMove(){
            return false;
        }
    },
    
};
</script>
<style scoped lang="scss">
page {
    background: #f6f6f6;
    color: #2e3c4e;
}
.container{
    min-height: 100vh;
    background: #f6f6f6;
}
.top{
    position: sticky;
    top: 0;
    z-index: 2;
}
</style>
    