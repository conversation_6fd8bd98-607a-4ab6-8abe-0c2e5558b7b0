<template>
  <view class="">
    <my-city-list
      :hot="false"
      @bindCity="bindCity"
      @onClick="onClick"
      @allCity="allCity"
      :location="city"
    ></my-city-list>
  </view>
</template>

<script>
import myCityList from "../components/city/nyn-city-list";
import { mapState, mapMutations } from "vuex";
import location from "../page_outside/tools/get_location.js";
export default {
  mixins: [location],
  components: {
    myCityList,
  },
  data() {
    return {};
  },
  computed: {
    ...mapState(["city"]),
  },
  onLoad() {},
  methods: {
    ...mapMutations(["setCityData"]),
    bindCity(e) {
      var that = this;
      uni.showModal({
        title: "提示",
        content: `是否切换定位到${e.name}？`,
        success: function(res) {
          if (res.confirm) {
            let city = {
              name: e.name,
              region_0: e.pid !== 0 ? e.pid : e.id,
              region_1: e.pid !== 0 ? e.id : e.pid,
              location_name: that.city.location_name,
            };
            that.changeCity(city);
          }
        },
      });
    },
    changeCity(city) {
      this.setCityData(city);
      uni.setStorageSync("city", city);
      uni.navigateBack({ delta: 1 }); // 返回上一页
      uni.$emit("refesh", city);
    },
    allCity() {
      let city = {
        name: "全部",
        region_0: 0,
        region_1: 0,
      };
      this.changeCity(city);
    },
    onClick(location) {
      this.getLocation();
      // setTimeout(() => {
      //   this.changeCity(this.city);
      uni.navigateBack({ delta: 1 }); // 返回上一页
      // }, 500);
    },
  },
};
</script>

<style></style>
