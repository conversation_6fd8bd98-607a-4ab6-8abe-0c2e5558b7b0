<template>
<view>
    <tCustomPickerView ref="picker" :multiple="multiple" v-model="selectedId" :datas="list" :map="{label: 'name', value: 'id', children: 'label'}" :loading="loading"></tCustomPickerView>
</view>
</template>

<script>
import tCustomPickerView from '@/components/tplus/tCustomPickerView.vue'
export default {
    props: {
        value: { type: [ Number, String, Array ], default:'' },
        data: { type: Array, default:() => [] },
        multiple: { type: Boolean, default: false },
    },
    components: {
        tCustomPickerView
    },
    data() {
        return {
            loading: false,
            list: [],               //列表数据
        }
    },
    computed: {
        selectedId: {
            get() {
                if(this.multiple){
                    if(this.value.length){
                        return this.value;
                    }
                    return this.list?.[0]?.id ? [this.list?.[0]?.id] : [];
                }else{
                    return this.value || this.list?.[0]?.id || '';
                }
            },
            set(val) {
                if(val !== this.value){
                    this.$nextTick(()=>{
                        this.$emit('input', val);
                    });
                }
            }
        }
    },
    watch: {
        data: {
            handler(val) {
                this.list = val || [];
            },
            immediate: true
        },
        value(){
            let label = this.multiple ? [] : '';
            if(this.value){
                label = this.getCheckedLabel();
                console.log(label);
            }
            this.$emit('update:label', label);
        }
    },
    methods: {
        getCheckedLabel(){
            const list =  this.$refs.picker.getCheckedItem();
            if(this.multiple){
                return list ? list.map(e => e.label.pop()) : [];
            }
            return list ? list.label.pop() : '';
        },
    }
}
</script>

<style scoped lang="scss">
::v-deep .picker {
    .picker-column:not(:last-child){
        background-color: #f9f9f9;
        .picker-column-item{
            &.checked{
                color: #2d84fb;
                background-color: #fff;
                .icon-checked{
                    display: none;
                }
            }
        }
    }
    .picker-column-item{
        height: 76rpx;
        font-size: 28rpx;
        padding: 0 48rpx;
        &::after{
            display: none;
        }
    }
}
</style>