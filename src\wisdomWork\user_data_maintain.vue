<template>
    <view>
        <view class="top">
            <view class="filters">
                <view class="filters-item">
                    <wisdomSearchSelect placeholder="日期" v-model="params.date_value" :datas="dateGroups" :params.sync="params" auto-popup @confirm="search" @clear="search"></wisdomSearchSelect>
                </view>
                <view class="filters-item">
                    <wisdomMemberSelect placeholder="成员" v-model="params.admin_id" @select="search"></wisdomMemberSelect>
                </view>
                <view class="filters-item">
                    <wisdomSearchSelect placeholder="排序" v-model="params.sort" :datas="sortGroups" :params.sync="params" auto-popup @confirm="search" @clear="search"></wisdomSearchSelect>
                </view>
            </view>
        </view>

        <wisdomTable :api="getList" :headers="headers" ref="table"></wisdomTable>
    </view>
</template>

<script>
import wisdomSearchSelect from './components/wisdomSearchSelect';
import wisdomMemberSelect from './components/wisdomMemberSelect';
import wisdomTable from './components/wisdomTable';
import { getMaintainUserData } from '@/common/utils/wisdom-work.js';
export default {
    components: {
        wisdomSearchSelect,
        wisdomMemberSelect,
        wisdomTable
    },
    data(){
        return {
            dateGroups: [{
                title: ' ', field: 'date_value', options: [
                    { label: '全部', value: '' },
                    { label: '今天', value: 'today' },
                    { label: '昨天', value: 'yestoday' },
                    { label: '本周', value: 'now_week' },
                    { label: '上周', value: 'last_week' },
                    { label: '本月', value: 'now_month' },
                    { label: '上月', value: 'last_month' },
                ]
            }],
            sortGroups: [{
                title: ' ', field: 'sort', options: [
                    { label: '维护量降序', value: 'service_num' },
                    { label: '有效客户降序', value: 'effective_num' },
                    { label: '无效客户降序', value: 'invalid_num' },
                    { label: '暂缓客户', value: 'suspend_num' },
                    { label: 'A级客户', value: 'a_num' },
                    { label: 'B级客户', value: 'b_num' },
                    { label: 'C级客户', value: 'c_num' },
                    { label: '查看电话数量', value: 'see_tel_num' },
                ]   
            }],
            params: {
                date_value: '',
                start_date: '',
                end_date: '',
                admin_id: '',
                sort: ''
            },
            headers: [
                { label: '姓名', field: 'user_name', width: 130, fixed: true },
                { label: '维护量', field: 'service_num', width: 110 },
                { label: '有效客户', field: 'effective_num', width: 110 },
                { label: '无效客户', field: 'invalid_num', width: 110 },
                { label: '暂缓客户', field: 'suspend_num', width: 110 },
                { label: 'A级客户', field: 'a_num', width: 110 },
                { label: 'B级客户', field: 'b_num', width: 110 },
                { label: 'C级客户', field: 'c_num', width: 110 },
                { label: 'D级客户', field: 'd_num', width: 110 },
                { label: '掉公次数', field: 'dg_num', width: 110 },
                { label: '转公次数', field: 'zg_num', width: 110 },
                { label: '查看电话数量', field: 'see_tel_num', width: 110 },
            ],
            list: [],
        }
    },
    onLoad(options){
        this.params.date_value = options.date || '';
    },
    methods: {
        async getList(page){
            const [start_date, end_date ] = this.$Utils.getDateRange(this.params.date_value, 'YYYY-MM-DD');
            const params = { ...this.params, start_date, end_date, page };
            delete params.date_value;
            const res = await getMaintainUserData(params);
            return {
                list: (res.data || []),
                pageSize: res.per_page || 0
            }
        },
        async search(){
            await this.$refs.table.search();
        }
    },
    async onPullDownRefresh(){
		await this.search();
		uni.stopPullDownRefresh();
	},
	onReachBottom () {
		this.$refs.table.getList();
	},
}
</script>

<style lang="scss" scoped>
@import "@/common/style/wisdom_work/wisdom_work_top_filters.scss";
</style>