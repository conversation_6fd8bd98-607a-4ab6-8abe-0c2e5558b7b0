<template>
  <view class="transfer">
    <view class="client-list">
      <view
        class="client-item row"
        :class="{ isactive: item.id === form_info.be_transfer_id }"
        v-for="item in admin_list"
        :key="item.id"
        @click="form_info.be_transfer_id = item.id"
      >
        <view class="left">
          {{ item.user_name[0] }}
        </view>
        <view class="right">
          <view class="t">{{ item.user_name }}</view>
        </view>
      </view>
    </view>
    <view class="btn-bottom">
      <view class="btn-box row">
        <view class="btn plain" @click="$navigateBack()">取消</view>
        <view class="btn" @click="onCreateData">提交</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data () {
    return {
      form_info: {
        ids: "",
        be_transfer_id: "",
      },
      admin_list: [],
      is_select_id: "",
    };
  },
  onLoad (options) {
    this.form_info.ids = options.id;
    this.getAdminList();
  },
  methods: {
    getAdminList () {
      this.$ajax.get("/qywx/home/<USER>", {}, (res) => {
        if (res.statusCode === 200) {
          this.admin_list = res.data;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    onCreateData () {
      if (!this.form_info.be_transfer_id) {
        uni.showToast({
          title: "请选择用户",
          icon: "none",
        });
        return;
      }
      this.$ajax.post("/qywx/client/transfer", this.form_info, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "操作成功",
          });
          this.$navigateBack()
          setTimeout(() => {
            uni.$emit('getDataAgain')
          }, 200);
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.transfer {
  .client-list {
    .client-item {
      text-align: center;
      align-items: center;
      padding: 10px 12px;
      border-bottom: 1px solid #f6f6f6;
      .left {
        width: 30px;
        line-height: 30px;
        height: 30px;
        border-radius: 50%;
        color: #fff;
        background: #2d84fb;
        margin-right: 12px;
      }
      .right {
        align-items: flex-start;
        line-height: 20px;
      }
      &.isactive {
        background: #f6f6f6;
      }
    }
  }
  .btn-bottom {
    height: 100px;
    .btn-box {
      background: #fff;
      justify-content: space-between;
      position: fixed;
      bottom: 0;
      width: 100%;
      padding: 24px;
      .btn {
        height: 42px;
        width: 100%;
        line-height: 42px;
        text-align: center;
        color: #fff;
        background: #2d84fb;
        border-radius: 6px;
      }
      .plain {
        background: #fff;
        color: #333;
        border: 1px solid #999;
        margin-right: 20px;
      }
    }
  }
}
</style>
