<template>
  <view class="project">
    <block v-for="(item, index) in project_list" :key="index">
      <view class="project-box">
        <view class="title row">
          <view class="title-left">{{ item.project_name }}</view>
          <view class="title-right row" @click="onClickSubscribe">
            总应收<text>{{ item.all_amount }} 元</text>
            <myIcon
              type="you"
              size="32rpx"
              color="#999"
              fontWeight="bold"
            ></myIcon>
          </view>
        </view>
        <view class="project-id">项目ID：{{ item.project_id }}</view>
        <view class="bottom row">
          <view class="content row"
            >应收客户 <text>{{ item.member_service_charge }}元</text>
          </view>
          <view class="content row"
            >应收开发商 <text>{{ item.brokerage_amount }}元</text>
          </view>
        </view>
        <view class="bottom row">
          <view class="content row"
            >财务已确认 <text>{{ item.confirmed_amount }}元</text></view
          >
          <view class="content row"
            >财务未确认 <text>{{ item.unconfirmed_amount }}元</text>
          </view>
        </view>
      </view>
    </block>
    <load-more :status="load_status"></load-more>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore";
import myIcon from "@/components/my-icon";
export default {
  components: { loadMore, myIcon },
  data() {
    return {
      company_id: "",
      project_list: [],
      params: {
        page: 1,
      },
      load_status: "",
    };
  },
  onLoad(options) {
    this.company_id = options.company_id;
    this.getDataList(options.company_id);
  },
  methods: {
    getDataList(company_id) {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.project_list = [];
      }
      this.$ajax.get(
        `/client/sale_order/statistics/project?company_id=${company_id}`,
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            this.project_list = this.project_list.concat(res.data.data);
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
            }
          } else {
            this.load_status = "loadend";
            uni.showToast({
              title: res.data.message || "获取列表数据失败",
              icon: "none",
            });
          }
        }
      );
    },
    onClickSubscribe() {
      this.$navigateTo(`/company/subscribe_list?company_id=${this.company_id}`);
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.getDataList(this.company_id);
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList(this.company_id);
  },
};
</script>

<style scoped lang="scss">
page {
  background: #eee;
}
.project {
  .project-box {
    margin: 24rpx 0;
    padding: 24rpx;
    background: #fff;
    .title {
      justify-content: space-between;
      align-items: center;
      .title-left {
        font-size: 32rpx;
        font-weight: 600;
      }
      .title-right {
        align-items: center;
        text {
          color: #d0665d;
        }
      }
    }
    .project-id {
      font-size: 24rpx;
      margin-top: 20rpx;
    }
    .bottom {
      margin-top: 20rpx;
      justify-content: space-between;
      .content {
        color: #999;
        text {
          margin-left: 20rpx;
          color: #333;
        }
      }
    }
  }
}
</style>
