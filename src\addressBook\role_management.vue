<template>
  <view class="role">
    <view class="role-t">
      <view class="search-box">
        <view class="search">
          <input v-model="params.name" placeholder-class="my-input" confirm-type="search" placeholder="请输入角色名称"
            @input="inputChange" />
        </view>
      </view>
      <view class="role-nav">
        <text>角色名称</text>
        <text>创建时间</text>
      </view>
      <view :class="['role-item', isSelectRole == index ? 'select-item' : '']" v-for="(item, index) in roleList"
        :key="index" @click="selectRole(item, index)">
        <view class="info-role">
          <text>{{ item.name }}</text>
        </view>
        <text>{{ item.created_at }}</text>
      </view>
      <loadMore :status="load_status" />
    </view>
    <view class="detail-btm">
      <button type="default" @click="del">删除</button>
      <button type="default" @click="openPermission">分配</button>
      <button type="default" @click="edit">修改</button>
    </view>
    <view class="add" @click="addRole">
      <view class="icon">
        <image src="https://img.tfcs.cn/tfyfxbb/jueseguanli.png"></image>
      </view>
      <view class="add_name">新增</view>
    </view>
    <my-popup position="bottom" ref="show_edit_pop" @close="show_edit_pop = false" :show="show_edit_pop">
      <view class="role-set">
        <text class="title">{{ addOfeditRole == 1 ? '添加角色' : '修改角色' }}</text>
        <view class="set-body">
          <view class="name">
            <text>角色名称</text>
            <input type="text" v-model="roleName" placeholder="请输入角色名称" />
          </view>
          <view class="name">
            <text>企微登录</text>
            <view class="status" @click="statusChange('qw')">
              <text>{{ qwStatus ? '已启用' : '已禁用' }}</text>
              <switch style="transform:scale(0.7)" color="#488AF6" :checked="qwStatus == 1" />
            </view>
          </view>
          <view class="name">
            <text>浏览器登录</text>
            <view class="status" @click="statusChange('wb')">
              <text>{{ webStatus ? '已启用' : '已禁用' }}</text>
              <switch style="transform:scale(0.7)" color="#488AF6" :checked="webStatus == 1" />
            </view>
          </view>
        </view>
        <button @click="updateRole">{{ addOfeditRole == 1 ? '添加角色' : '修改角色' }}</button>
      </view>
    </my-popup>
    <my-popup position="bottom" ref="show_edit_pop" @close="show_edit_permission_pop = false"
      :show="show_edit_permission_pop">
      <view class="permission" :style="'height:' + (windowHeight * 0.8) + 'px'">
        <text class="title">{{ roleData.name }} 权限分配</text>
        <view class="permission-title-list">
          <view :class="['title-list-item', menu_mode_id == item.menu_mode_id ? 'select-title-item' : '']"
            v-for="(item, index) in permissionTitleList" :key="index" @click="titleChange(item)">
            <text>{{ item.title }}</text>
            <text>{{ '（' + count[index] + '）' }}</text>
          </view>
        </view>
        <scroll-view class="permission-box-list" :style="'height:' + (windowHeight * 0.56) + 'px'" scroll-y>
          <view class="list-item" v-for="(item, index) in newPermissionList" :key="index">
            <text class="item-nav" @click="selectPermission(item)">{{ item.title }}</text>
            <view class="item-sub">
              <text :class="['item', subItem.isSelect ? 'seleect-permission' : '']"
                v-for="(subItem, subIndex) in item.children" :key="subIndex" @click="selectPermission(subItem)">
                {{ subItem.title }}
              </text>
            </view>
          </view>
        </scroll-view>
        <view class="btn">
          <button @click="updatePermission">分配权限</button>
        </view>
      </view>
    </my-popup>
  </view>
</template>
<script>
import myPopup from '@/components/myPopup'
import loadMore from '@/components/loadMore'
export default {
  components: {
    myPopup,
    loadMore
  },
  data() {
    return {
      windowHeight: 800,
      addOfeditRole: 1,//1添加 2编辑
      load_status: 'loading',
      menu_mode_id: 1,
      permissionTitleId: '1',
      permissionList: [],
      permissionTitleList: [],
      role_permission_list: [],
      roleData: {},
      qwStatus: 1,
      webStatus: 1,
      roleName: '',
      show_edit_permission_pop: false,
      show_edit_pop: false,
      isSelectRole: null,
      params: {
        page: 1,
        per_page: 20,
        name: ''
      },
      form_create: {
        allow_qywx_login: 1,//1开启 0关闭
        allow_wap_login: 1,
        name: "",
      },
      form_permission: {},
      roleList: []
    }
  },
  computed: {
    newPermissionList() {
      let per1 = this.permissionList.filter((item) => { return item.type.includes(this.menu_mode_id) });
      return per1
    },
    count() {
      let per = []
      this.permissionList.forEach(item => {
        item.children.forEach(item2 => {
          per.push(item2)
        })
      })
      let arr = this.permissionTitleList.map((item) => {
        return per.filter(f => {
          return f.isSelect && f.type.split(',').includes(item.menu_mode_id + '')
        }).length
      })
      return arr
    }
  },
  onLoad() {
    uni.getSystemInfo({
      success: (res) => {
        this.windowHeight = res.windowHeight
      }
    })
    this.getRoleList()
  },
  onReachBottom() {
    if (this.load_status = 'nomore') return
    this.params.page++
    this.getRoleList()
  },
  methods: {
    updatePermission() {
      let permission_names = []
      this.permissionList.forEach(item => {
        item.children.forEach(item2 => {
          if (item2.isSelect) {
            permission_names.push(item2.name)
          }
        })
      })
      this.form_permission.id = this.roleData.id
      this.form_permission.permission_names = permission_names
      this.$ajax.post('/admin/role/permission/reset/all', this.form_permission, (res) => {
        if (res.statusCode == 200) {
          uni.showToast({
            title: '权限重置成功',
            icon: 'success',
            success: () => {
              this.show_edit_permission_pop = false
            }
          })
        }
      })
    },
    getRolePermission() {
      this.permissionTitleList = []
      this.permissionList = []
      this.$ajax.get("/admin/permission/all", {}, (res) => {
        if (res.statusCode == 200) {
          this.permissionTitleList = res.data.modes
          let arr = this.toTree(res.data.permissions);
          arr.forEach(item => {
            if (!item.children) {
              let children = []
              children.push(JSON.parse(JSON.stringify(item)))
              item.children = children
            }
          })
          arr.forEach(item => {
            item.isSelect = false
            item.children.forEach(item2 => {
              item2.isSelect = false
            })
          })
          this.permissionList = JSON.parse(JSON.stringify(arr))
          this.queryPermissionData()
        }
      })
    },
    arrFilter(arr) {
      var newarr = [];
      arr.forEach((item) => {
        if (item !== undefined) {
          newarr.push(item);
        }
      });
      return newarr;
    },
    toTree(data) {
      let result = [];
      if (!Array.isArray(data)) {
        return result;
      }
      data.forEach((item) => {
        delete item.children;
      });
      let map = {};
      data.forEach((item) => {
        map[item.id] = item;
      });
      data.forEach((item) => {
        let parent = map[item.pid];
        if (parent) {
          (parent.children || (parent.children = [])).push(item);
        } else {
          result.push(item);
        }
      });
      return result;
    },
    queryPermissionData() {
      this.role_permission_list = []
      this.$ajax.get(`/admin/role/permission/all/${this.roleData.id}`, {}, (res) => {
        if (res.statusCode == 200) {
          this.role_permission_list = res.data;
          // const pids = [];
          const ids = []
          this.role_permission_list.forEach((item) => {
            // if (item.pid != 0) {
            //   ids.push(item.id)
            //   pids.push(item.pid);
            // }
            ids.push(item.id)
          })
          //独立的一级权限
          // this.role_permission_list.forEach((item) => {
          //   if (item.pid == 0) {
          //     ids.push(item.id)
          //   }
          // })
          this.permissionList.forEach(item => {
            item.children.forEach(item2 => {
              ids.forEach(id => {
                if (item2.id === id) {
                  item2.isSelect = true
                }
              })
            })
          })
        }
      });
    },
    getRoleList() {
      this.$ajax.get('/admin/role/search', this.params, (res) => {
        if (res.statusCode == 200) {
          if (res.data.data.length < this.params.per_page) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'loadend'
          }
          this.roleList = this.roleList.concat(res.data.data)
        } else {
          this.load_status = 'nomore'
        }
      })
    },
    updateRole() {
      if (!this.roleName) {
        uni.showToast({
          title: '请输入角色名',
          icon: 'none'
        })
        return
      } else {
        this.form_create.name = this.roleName
      }
      let url = '', name = ''
      if (this.addOfeditRole == 1) {
        this.name = '角色添加成功'
        url = `/admin/role/create`
        this.form_create.init_permission = 0
      } else if (this.addOfeditRole == 2) {
        delete this.form_create.init_permission
        this.name = '角色修改成功'
        url = `/admin/role/update`
      }
      this.form_create.id = this.roleData.id
      this.$ajax.post(url, this.form_create, (res) => {
        if (res.statusCode == 200) {
          uni.showToast({
            title: name,
            icon: 'success'
          })
          this.show_edit_pop = false
          this.isSelectRole = null
          this.roleList = []
          this.params.page = 1
          this.getRoleList()
        } else {
          uni.showToast({
            title: res.data.message,
            icon: 'none'
          })
        }
      })
    },
    inputChange(e) {
      this.roleList = []
      this.getRoleList()
    },
    selectRole(item, index) {
      if (this.isSelectRole == index) {
        this.roleData = {}
        this.isSelectRole = null
        return
      }
      this.roleData = {}
      this.roleData = item
      this.isSelectRole = index
    },
    statusChange(type) {
      if (type == 'qw') {
        this.qwStatus == 1 ? this.qwStatus = 0 : this.qwStatus = 1
        this.form_create.allow_qywx_login = this.qwStatus
      }
      if (type == 'wb') {
        this.webStatus == 1 ? this.webStatus = 0 : this.webStatus = 1
        this.form_create.allow_wap_login = this.webStatus
      }
    },
    edit() {
      if (JSON.stringify(this.roleData) != '{}') {
        this.addOfeditRole = 2
        this.qwStatus = this.roleData.allow_qywx_login
        this.webStatus = this.roleData.allow_wap_login
        this.roleName = this.roleData.name
        this.show_edit_pop = true
      } else {
        uni.showToast({
          title: '请选择角色',
          icon: 'none'
        })
      }
    },
    addRole() {
      this.addOfeditRole = 1
      this.show_edit_pop = true
      this.roleData = {}
      this.isSelectRole = null
    },
    del() {
      if (JSON.stringify(this.roleData) != '{}') {
        uni.showModal({
          title: '删除提示',
          content: `确定要删除 ${this.roleData.name} 吗？`,
          success: (res) => {
            if (res.confirm) {
              this.$ajax.get(`/admin/role/delete/${this.roleData.id}`, {}, (res) => {
                if (res.statusCode == 200) {
                  uni.showToast({
                    title: `角色已删除`,
                    icon: 'success'
                  })
                  this.params.page = 1
                  this.roleList = []
                  this.roleData = {}
                  this.isSelectRole = null
                  this.getRoleList()
                }
              })
            }
          }
        });
      } else {
        uni.showToast({
          title: '请选择角色',
          icon: 'none'
        })
      }
    },
    openPermission() {
      if (JSON.stringify(this.roleData) != '{}') {
        this.getRolePermission()
        this.show_edit_permission_pop = true
      } else {
        uni.showToast({
          title: '请选择角色',
          icon: 'none'
        })
      }
    },
    titleChange(item) {
      if (item.menu_mode_id == this.menu_mode_id) return
      this.menu_mode_id = item.menu_mode_id
    },
    selectPermission(subItem) {
      subItem.isSelect = !subItem.isSelect
      // if (!subItem.children) {
      console.log(subItem);
      // }
    },
  },
}
</script>
<style lang="scss" scoped>
page {
  background-color: #f6f6f6;
}

.add {
  position: fixed;
  right: 32rpx;
  bottom: 160rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-image: linear-gradient(135deg, #4daaff 0%, #2671ff 100%);
  box-shadow: 0px 4px 10px 0px rgba(46, 124, 255, 0.4);
  z-index: 3;

  .icon {
    width: 60rpx;
    height: 60rpx;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .add_name {
    color: #fff;
    font-size: 24rpx;
  }
}

.permission {
  width: 100%;
  padding: 32rpx;
  height: 900rpx;
  background-color: #fff;

  .title {
    font-size: 36rpx;
    color: #292C39;
    width: 100%;
    text-align: center;
  }

  .permission-title-list {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    justify-content: space-between;
    margin-top: 40rpx;
    font-size: 28rpx;
    color: #292C39;

    .title-list-item {
      display: flex;
      flex-direction: row;
      align-items: flex-start;

      text {
        text-align: center;
        padding: 10rpx 0;
      }

      &>text:nth-child(1) {
        font-size: 32rpx;
        font-weight: 500;
      }

      &>text:nth-child(2) {
        font-size: 24rpx;
        font-weight: 500;
        color: #a1a1a1;
      }
    }

    .select-title-item {
      text {
        color: #488AF6 !important;
      }

      &>text:nth-child(1) {
        font-size: 40rpx !important;
        font-weight: 500;
      }
    }
  }

  .permission-box-list {
    margin-top: 40rpx;
    width: 100%;
    // overflow-y: auto;

    .list-item {
      display: flex;
      flex-direction: row;
      margin-bottom: 30rpx;

      .item-nav {
        width: 200rpx;
        font-size: 28rpx;
        color: #292C39;
        margin-top: 8rpx;
        overflow-x: auto;
      }

      .item-sub {
        width: 100%;
        display: flex;
        flex-direction: row;
        align-items: center;
        flex-wrap: wrap;

        .item {
          font-size: 24rpx;
          color: #999;
          padding: 10rpx 20rpx;
          margin: 0 20rpx 20rpx 0;
          border: 1px solid #f3f3f3;
          border-radius: 4px;
        }

        .seleect-permission {
          color: #488AF6;
          border: 1px solid #488AF6;
        }
      }

    }
  }

  .btn {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 30rpx 32rpx;
    border-top: 1px solid #f3f3f3;
    background-color: #fff;

    button {
      width: 100%;
      color: #fff;
      background-color: #488AF6;
    }
  }

}

.role-set {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  padding: 32rpx;
  background-color: #fff;

  .title {
    font-size: 36rpx;
    color: #292C39;
    width: 100%;
    text-align: center;
  }

  .set-body {
    margin-top: 40rpx;

    .name {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin: 20rpx 0;

      .status {
        display: flex;
        flex-direction: row;
        align-items: center;
      }

      input {
        margin-right: 20rpx;
        text-align: right;
      }
    }
  }

  button {
    width: 100%;
    margin: 40rpx 0;
    color: #fff;
    background-color: #488AF6;
  }
}

.role {
  width: 100%;

  .role-t {
    padding-bottom: 160rpx;

    .search-box {
      position: sticky;
      top: 0;
      width: 100%;
      padding: 32rpx;
      background-color: #fff;
      z-index: 9;

      .search {
        padding: 24rpx 32rpx;
        border-radius: 16rpx;
        background-color: #f6f6f6;
      }
    }

    .role-nav {
      position: sticky;
      top: 120rpx;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx;
      font-size: 32rpx;
      font-weight: 500;
      color: #292C39;
      background-color: #fff;
      border-bottom: 1px solid #f6f6f6;
    }

    .role-item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 40rpx 32rpx;
      background-color: #fff;
      font-size: 28rpx;

      .info-role {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;

        text {
          border-radius: 4px;
          padding: 8rpx 16rpx;
          font-size: 28rpx;
          background: #488AF633;
          color: #488AF6;
        }

      }
    }

    .select-item {
      border-radius: 16rpx;
      border: 2px solid #488AF6;
    }
  }

  .detail-btm {
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 30rpx 0 40rpx;
    border-top: 1px solid #f6f6f6;
    background-color: #fff;

    button {
      width: 50%;
      background-color: #fff;
      border: 0 !important;
    }

    &>button:nth-child(2) {
      margin: 0 30rpx;
      // color: #488AF6;
    }

    &>button:nth-child(1) {
      margin: 0 30rpx;
      color: red;
    }

    &>button:nth-child(3) {
      margin: 0 30rpx;
      color: rgb(255, 153, 0);
    }
  }
}
</style>