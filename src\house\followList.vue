<template>
  <view class="follow_list">
    <view class="tab-box">
      <tabs
        :options="tab_list"
        :format="{ name: 'name', value: 'type' }"
        :equispaced="false"
        v-model="tab_type"
      ></tabs>
    </view>
    <view class="container">
      <!-- #ifdef H5 -->
      <timeLine v-if="follow_list.length > 0" :list="follow_list" v-slot="{ prop }">
        <view class="timeline_item">
          <view class="time flex-row items-center space-between">
            <text>{{ prop.ctime }}</text>
            <text class="agent_name" v-if="prop.crm_user">
              {{ prop.crm_user && prop.crm_user.user_name }}/{{
                prop.crm_user && prop.crm_user.department
              }}
            </text>
          </view>

          <view class="content">
            <text>{{ prop.content }}</text>
          </view>
          <view class="img_list" v-if="prop.images && prop.images.length">
            <image
              @click="preFollowImgs(prop.images, index)"
              v-for="(img, index) in prop.images"
              :key="index"
              :src="img | imageFilter('w_80')"
              mode="aspectFill"
            />
          </view>
        </view>
      </timeLine>
      <!-- #endif -->
      <!-- #ifndef H5 -->
      <timeLine v-if="follow_list.length > 0">
        <view class="item" v-for="prop in follow_list" :key="prop.id">
          <view class="dot"></view>
          <view class="timeline_item">
            <view class="time flex-row items-center space-between">
              <text>{{ prop.ctime }}</text>
              <text class="agent_name" v-if="prop.crm_user">
                {{ prop.crm_user && prop.crm_user.user_name }}/{{
                  prop.crm_user && prop.crm_user.department
                }}
              </text>
            </view>
            <view class="content">
              <text
                v-if="prop.type_name"
                class="status"
                :class="[
                  { status1: prop.type_name === '有效房源' },
                  { status2: prop.type_name === '无效房源' },
                ]"
                >[{{ prop.type_name }}]</text
              >
              <text v-if="prop.take_look_status" class="status status3"
                >[{{ prop.take_look_status }}]</text
              >
              <text v-if="prop.house_deal_status" class="status status4"
                >[{{ prop.house_deal_status }}]</text
              >
              <text>{{ prop.content }}</text>
            </view>
            <view class="img_list" v-if="prop.images && prop.images.length">
              <image
                @click="preFollowImgs(prop.images, index)"
                v-for="(img, index) in prop.images"
                :key="index"
                :src="img | imageFilter('w_80')"
                mode="aspectFill"
              />
            </view>
          </view>
        </view>
      </timeLine>
      <!-- #endif -->
      <loadMore :status="follow_loading" @reload="getList()" />
    </view>
  </view>
</template>

<script>
import timeLine from './components/timeLine'
import loadMore from '@/components/loadMore'
import Tabs from './components/Tabs'
import { formatImg } from '@/page_outside/tools/index'
export default {
  onLoad (options) {
    if (options.from) {
      this.from = options.from
    }

    if (options.is_system) {
      if (options.is_system == 2) {
        this.params.is_system = 0
        this.tab_list = [
          {
            type: 2,
            name: '跟进记录',
          },

        ]
      } else {
        this.params.is_system = 1
        this.tab_list = [

          {
            type: 2,
            name: '维护记录',
          },

        ]
      }
    }
    if (options.id) {
      this.id = options.id
      this.getList()
    }
  },
  components: {
    loadMore,
    timeLine,
    Tabs,
  },
  data () {
    return {
      params: {
        page: 1,
        rows: 20,
        is_system: 1
      },
      id: '',
      follow_loading: 'loadend',
      follow_list: [],
      tab_type: 2,
      tab_list: [
        // {
        //   type: 1,
        //   name: '业务信息',
        // },
        {
          type: 2,
          name: '跟进记录',
        },
        // {
        //   type: 3,
        //   name: '访客浏览',
        // },
        // {
        //   type: 4,
        //   name: '房源信息',
        // },
        // {
        //   type: 5,
        //   name: '周边配套',
        // },
      ],
      from: '',
    }
  },
  methods: {
    getList () {
      if (this.params.page === 1) {
        this.follow_list = []
      }
      let api = '/admin/house/privateHouseFollows/'
      // if (this.from == 'private') {
      //   api = '/v1/wapLm/privateHouseFollows/'
      // }
      // if (this.from == 'custom') {
      //   api = '/v1/wapLm/customerFollows/'
      // }
      this.follow_loading = 'loading'
      this.$ajax
        .get(`${api}${this.id}`, this.params, (res) => {
          this.follow_loading = 'loadend'
          if (res.statusCode === 200) {

            this.follow_list = this.follow_list.concat(res.data)
            if (res.data.length < this.params.rows) {
              this.follow_loading = 'nomore'
            }

          } else {
            this.follow_loading = 'nomore'
          }
        }, () => {
          this.follow_loading = 'loaderror'
        })

    },
    preFollowImgs (image_list, index) {
      uni.previewImage({
        current: index,
        urls: image_list.map((item) => formatImg(item, 'w8601')),
        indicator: 'number',
      })
    },
  },
  onReachBottom () {
    if (this.tab_type === 2 && this.follow_loading === 'loadend') {
      this.params.page++
      this.getList()
    }
  },
}
</script>

<style lang="scss" scoped>
.follow_list {
  padding: 48rpx;
}
.item {
  position: relative;
  padding: 0 20rpx 36rpx 32rpx;
  border-left: 2rpx solid $border-color;
}
.dot {
  content: '';
  height: 14rpx;
  width: 14rpx;
  border-radius: 50%;
  background-color: #3399ff;
  position: absolute;
  left: -8rpx;
  top: 0;
  z-index: 2;
}
.timeline_item {
  .time {
    margin-bottom: 16rpx;
    margin-top: 8rpx;
    font-size: 24rpx;
    font-weight: bold;
    color: #999;
  }
  .user_info {
    margin-bottom: 16rpx;
    flex-direction: row;
    align-items: center;
    .avatar {
      margin-right: 12rpx;
      width: 62rpx;
      height: 62rpx;
      border-radius: 50%;
    }
    .name {
      font-size: 24rpx;
      color: #555555;
    }
    .tname {
      margin-top: 6rpx;
      font-size: 23rpx;
      color: $color-paragraph;
    }
    .tel {
      margin-left: 12rpx;
      flex-direction: row;
      align-items: center;
      font-size: 26rpx;
      color: #2d84fb;
    }
  }
  .content {
    margin-bottom: 16rpx;
    font-size: 28rpx;
    line-height: 1.5;
    word-break: break-all;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // -webkit-line-clamp: 2;
    // -webkit-box-orient: vertical;
    // display: -webkit-box;
    .status {
      margin-right: 12rpx;
      font-weight: bold;
      &.status1 {
        color: #3cc53c;
      }
      &.status2 {
        color: #8a929f;
      }
      &.status3 {
        color: #2d84fb;
      }
      &.status4 {
        color: #fe6c17;
      }
    }
  }
  .img_list {
    flex-direction: row;
    > image {
      width: 120rpx;
      height: 120rpx;
      border-radius: 4rpx;
      margin-right: 16rpx;
      margin-bottom: 16rpx;
    }
  }
}
</style>
