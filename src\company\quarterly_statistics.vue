<template>
  <view class="list">
    <view class="top-title">
      <view class="top-title-ranking">
        <view class="paihang row">
          <view class="pai-box">
          <image src="../static/company/<EMAIL>" mode="aspectFill">
          </image>
          <view
            class="paihang-list"
            v-if="
              second_place.name ||
                second_place.nickname ||
                second_place.user_name
            "
            >{{
              second_place.name ||
                second_place.nickname ||
                second_place.user_name
            }}</view
          >
          <view class="paihang-list" v-else>暂无数据</view>
          </view>
          <view class="pai-box">
          <image src="../static/company/<EMAIL>" mode="aspectFill">
          <view
            class="paihang-list "
            v-if="
              first_place.name || first_place.nickname || first_place.user_name
            "
            >{{
              first_place.name || first_place.nickname || first_place.user_name
            }}</view
          >
          <view class="paihang-list" v-else>暂无数据</view>
          </view>
          <view class="pai-box">
          <image src="../static/company/<EMAIL>" mode="aspectFill">
          <view
            class="paihang-list"
            v-if="
              third_place.name || third_place.nickname || third_place.user_name
            "
            >{{
              third_place.name || third_place.nickname || third_place.user_name
            }}</view
          >
          <view class="paihang-list" v-else>暂无数据</view>
          </view>
        </view>
        <view class="bottom-desc">恭喜{{first_place.name || first_place.nickname || first_place.user_name}}获得当前佣金榜第一</view>
      </view>
    </view>
    <view class="filter row">
      <picker
        class="picker-box "
        @change="bindPickerChange"
        :value="index"
        :range="array"
        range-key="desc"
      >
        <view class="uni-input">{{ array[index].desc }}</view>
        <view class="filter-btn">筛选</view>
      </picker>
    </view>
    <view class="ranking-list" v-if="user_list.length > 0">
      <view
        class="rankpeople row"
        v-for="(item, index) in user_list"
        :key="item.id"
      >
        <view class="rank-name row"
          ><text class="rank-number">{{ index + 1 }}</text
          >{{ item.name }}</view
        >
        <view class="rank-amount">已结算佣金{{ item.brokerage_amount }}元</view>
      </view>
    </view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
export default {
  data() {
    return {
      array: [
        { desc: "今天", value: "day", id: 1 },
        { desc: "昨天", value: "yesterday", id: 2 },
        { desc: "本周", value: "week", id: 3 },
        { desc: "本月", value: "month", id: 4 },
        { desc: "上月", value: "last_month", id: 5 },
        { desc: "季度", value: "quarter", id: 6 },
        { desc: "今年", value: "year", id: 7 },
      ],
      index: 0,
      user_list: [],
      params: {
        date_str: "day",
      },
      first_place: {},
      second_place: {},
      third_place: {},
      company_id:''
    };
  },
  onLoad(options) {
    this.company_id = options.company_id
    this.params.all = options.all || ''
    this.getUserList();
  },
  methods: {
    bindPickerChange: function(e) {
      this.index = e.target.value;
      this.params.date_str = this.array[this.index].value;
      this.getUserList();
    },
    getUserList() {
      this.$ajax.get(
        `/client/company/statistics/performance?company_id=${this.company_id}`,
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.user_list = res.data;
            let length = res.data.length
            if(length > 0){
              length === 1 ? (this.first_place = res.data[0],this.second_place={},this.third_place = {}) 
              :length === 2 ? (this.first_place = res.data[0],this.second_place = res.data[1],this.third_place = {}) 
              :(this.first_place = res.data[0],this.second_place = res.data[1],this.third_place = res.data[2])
            }else{
              uni.showToast({
                title:"暂无数据",
                icon:'none'
              })
              this.first_place = {};
              this.second_place = {};
              this.third_place = {};
            }
          } else {
            uni.showToast({
              title: res.data.message || "请求数据失败",
              icon: "none",
            });
          }
        }
      );
    },
  },
  onPullDownRefresh(){
    this.getUserList()
    uni.stopPullDownRefresh();
  }
};
</script>

<style scoped lang="scss">
page {
  background: #eee;
}
.list {
  .top-title {
    height: 500rpx;
    width: 100%;
    background: #6c8af8;
    position: relative;
    .top-title-ranking {
      border-top-left-radius: 10rpx;
      border-top-right-radius: 10rpx;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translate(-50%, 0);
      height: 300rpx;
      background: #fff;
      width: 680rpx;
      .paihang {
        height: 300rpx;
        .pai-box {
          width: 33.3%;
          align-items: center;
          image{
            width:120rpx;
            height: 120rpx;
          }
          .paihang-list{
            font-size: 32rpx;
            margin-top: 10rpx;
            color: #6c8af8;
          }
        }
        .pai-box:nth-child(2) {
          border-top-left-radius: 10rpx;
          border-top-right-radius: 10rpx;
          top: -100rpx;
          background: #fff;
          width: 33.3%;
          position: relative;
          height: 400rpx;
          align-items: center;
        }
      }
      .bottom-desc {
        font-size: 26rpx;
        position: absolute;
        font-weight: bold;
        bottom: 14rpx;
        left: 50%;
        transform: translate(-50%, 0);
      }
    }
  }
  .filter {
    margin: 20rpx 10rpx;
    line-height: 60rpx;
    font-size: 32rpx;
    .picker-box {
      display: flex;
      width: 100%;
      height: 60rpx;
      position: relative;
      .uni-input {
        align-items: center;
        position: absolute;
        left: 0;
        width: 80%;
        background: #fff;
      }
      .filter-btn {
        margin-left: 20rpx;
        background: #fff;
        align-items: center;
        width: 18%;
        position: absolute;
        right: 0;
      }
    }
  }
  .ranking-list {
    width: auto;
    margin: 10rpx;
    background: #fff;
    .rankpeople {
      padding: 24rpx;
      justify-content: space-between;
      align-items: center;
      .rank-name {
        align-items: center;
        font-size: 32rpx;
        .rank-number {
          width: 60rpx;
          height: 60rpx;
          background: #fc5954;
          color: #fff;
          line-height: 60rpx;
          text-align: center;
          border-radius: 50%;
          margin-right: 20rpx;
        }
      }
      .rank-amount {
        font-size: 32rpx;
        color: #999;
      }
    }
  }
}
</style>
