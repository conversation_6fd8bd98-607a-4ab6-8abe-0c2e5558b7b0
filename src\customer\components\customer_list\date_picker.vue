<template>
<view>
    
        <view class="picker-selector">
            <view class="picker-selector-header">
                <view class="picker-selector-action cancle" @click.stop="cancle">取消</view>
                <view class="picker-selector-title">{{placeholder}}</view>
                <view class="picker-selector-action confirm" @click.stop="confirm">确定</view>
            </view>
            <picker-view :value="columnSelectedIndexs" @change="onSelectChange">
                <picker-view-column v-for="(range, index) in rangeList" :key="index">
                    <view class="picker-selector-column-item" v-for="(item, key) in range" :key="key">
                        {{item[map.label]}}
                    </view>
                </picker-view-column>
            </picker-view>
        </view>
</view>
</template>

<script>
import tCustomPickerView from '@/components/tplus/tCustomPickerView.vue'
export default {
    props: {
        data: { type: Array, default:() => [] }
    },
    components: {
        tCustomPickerView
    },
    data() {
        return {
            loading: false,
            list: [],               //列表数据
            selectedId: ''          //选中值 
        }
    },
    computed: {
        
    },
    watch: {
        data: {
            handler(val) {
                this.list = val || [];
                this.selectedId = this.list?.[0]?.id || '';
            },
            immediate: true
        }
    },
    methods: {
    
    }
}
</script>

<style scoped lang="scss">
::v-deep .picker {
    .picker-column{
        flex: 1;
    }
    .picker-column:not(:last-child){
        background-color: #f9f9f9;
        .picker-column-item{
            &.checked{
                color: #488af6;
                background-color: #fff;
                .icon-checked{
                    display: none;
                }
            }
        }
    }
    .picker-column-item{
        
        font-size: 28rpx;
        padding: 0 48rpx;
        &::after{
            display: none;
        }
    }
}
</style>