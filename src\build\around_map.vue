<template>
  <view class="content-list">
    <view class="mapbox">
      <map
        :markers="markers"
        :circles="circles"
        :latitude="lat"
        :longitude="long"
      ></map>
      <view class="map-nav row">
        <view
          class="navs-box row"
          v-for="item in map_navs"
          :key="item.id"
          @click="surrounding(item.name, item.bgColor)"
        >
          <image :src="item.path"></image>
          <text>{{ item.name }}</text>
        </view>
      </view>
    </view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import { mapState } from "vuex";
export default {
  components: {},
  data() {
    return {
      map_navs: [
        {
          id: 1,
          name: "商业",
          path: "https://img.tfcs.cn/static/img/shangye.png",
          bgColor: "#f87d7e",
        },
        {
          id: 2,
          name: "教育",
          path: "https://img.tfcs.cn/static/img/jiaoyu.png",
          bgColor: "#38cfb1",
        },
        {
          id: 3,
          name: "医疗",
          path: "https://img.tfcs.cn/static/img/yiliao.png",
          bgColor: "#f88383",
        },
        {
          id: 4,
          name: "交通",
          path: "https://img.tfcs.cn/static/img/jiaotong.png",
          bgColor: "#67cbf8",
        },
      ],
      markers: [],
      circles: [],
      params: {},
      lat: "",
      long: "",
    };
  },

  onLoad(options) {
    this.params.id = options.id;
    this.getDetailData();
  },

  computed: {
    ...mapState(["qqmapkey"]),
  },
  methods: {
    getDetailData() {
      this.$ajax.get(
        `/common/project/query/build/${this.params.id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.build = res.data;
            // 获取地图操作
            this.lat = parseFloat(res.data.build_location_lat);
            this.long = parseFloat(res.data.build_location_long);
            this.markers = [
              {
                id: this.params.id,
                latitude: this.lat,
                longitude: this.long,
                width: 1,
                height: 1,
                iconPath: "https://images.zaodaoxiao.com/icons/xiaoxuequ.png",
                callout: {
                  content: res.data.build_name,
                  display: "ALWAYS",
                  textAlign: "center",
                  borderRadius: 4,
                },
              },
            ];
            //  地图圆圈
            this.circles = [
              {
                latitude: this.lat,
                longitude: this.long,
                color: "#0178f7",
                fillColor: "#a0c7f3AA",
                radius: 30,
                strokeWidth: 2,
              },
            ];
          } else {
            uni.showToast({
              title: "数据请求错误",
              icon: "none",
              mask: true,
            });
          }
        }
      );
    },
    // 点击查询周边
    surrounding(keyword, bgColor) {
      this.$jsonp("https://apis.map.qq.com/ws/place/v1/search", {
        keyword,
        boundary: `nearby(${this.lat},${this.long},1000)`,
        key: this.qqmapkey,
        output: "jsonp",
      })
        .then((res) => {
          this.markers = res.data.map((item) => {
            return {
              id: item.id,
              longitude: item.location.lng,
              latitude: item.location.lat,
              width: 1,
              height: 1,
              iconPath: "https://images.zaodaoxiao.com/icons/xiaoxuequ.png",
              callout: {
                color: "#fff",
                content: item.title,
                display: "ALWAYS",
                textAlign: "center",
                borderRadius: 4,
                padding: 10,
                bgColor: bgColor,
              },
            };
          });
          this.circles = [];
        })
        .catch((err) => {
          console.log(err);
        });
    },
  },
};
</script>

<style scoped lang="scss">
.mapbox {
  height: 100%;
  width: 100%;
  map {
    position: fixed;
    height: 100%;
    width: 100%;
  }
  .map-nav {
    line-height: 80rpx;
    justify-content: space-evenly;
    left: 50%;
    bottom: 24rpx;
    transform: translate(-50%);
    background: #fff;
    position: absolute;
    width: 600rpx;
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.2);
    height: 80rpx;
    .navs-box {
      align-items: center;
    }
    image {
      margin: 0 10rpx;
      width: 48rpx;
      height: 48rpx;
    }
    text {
      font-size: 24rpx;
      color: #999;
    }
  }
}
</style>
