<template>
  <view class="list">
    <view class="files_list row" v-for="item in files_list" :key="item.id">
      <view class="left">
        {{ item.name }}
      </view>
      <view class="right" @click="viewFile(item.file)">
        预览
      </view>
    </view>
    <load-more :status="load_status"></load-more>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore";
export default {
  components: { loadMore },
  data() {
    return {
      files_list: [],
      buildID: "",
      params: {
        page: 1,
      },
      load_status: "",
    };
  },
  onLoad(options) {
    this.buildID = options.build_id;
    this.getDataList();
  },
  methods: {
    getDataList() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.files_list = [];
      }
      this.$ajax.get(
        `/common/build/attached_file/search?build_id=${this.buildID}`,
        this.params,
        (res) => {
          this.load_status = "loadend";
          if (res.statusCode === 200) {
            this.files_list = this.files_list.concat(res.data.data);
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
              uni.showToast({
                title: "没有更多数据了",
                icon: "none",
              });
            }
          }
        }
      );
    },
    viewFile(file) {
      window.open(file);
    },
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.getDataList();
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>

<style scoped lang="scss">
.list {
  padding: 24rpx 48rpx;
  .files_list {
    justify-content: space-between;
    padding: 24rpx 0;
    border-bottom: 2rpx solid #eee;
    .right {
      color: #0174ff;
    }
  }
}
</style>
