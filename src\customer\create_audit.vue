<template>
  <view class="default">
    <view class="default-content">
      <view class="desc">基础信息</view>
      <view class="default-box">
        <view class="client-box">
          <view class="c-t row">
            <image
              class="sex"
              :src="
                `https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/${
                  client_info.sex == 1 ? 'nan2' : 'nv2'
                }.png`
              "
            ></image>
            <text class="name">{{ client_info.cname }}</text>
            <text class="level" v-if="client_info.level">{{
              client_info.level.title
            }}</text>
            <image
              class="qw"
              v-if="client_info.wxqy_id"
              src="../static/customer/qw.png"
              mode="widthFix"
            />
          </view>
          <view class="c-b row">
            <text> 性别：{{ client_info.sex == 1 ? "男" : "女" }} </text>
            <text style="margin-left:24px" v-if="client_info.source"
              >客户来源：{{ client_info.source.title }}</text
            >
          </view>
        </view>
        <view class="input-box " @click="is_show_admin = true">
          <view class="label">审批人</view>
          <view class="input row">
            <input disabled type="text" placeholder="请选择" />
            <myIcon type="xiala" color="#999" size="16px"></myIcon>
          </view>
        </view>
        <view class="input-box ">
          <view class="label">收入费用</view>
          <view class="input row">
            <input type="text" v-model="form_info.price" placeholder="请输入" />
            <text class="">元</text>
          </view>
        </view>
        <view class="input-box ">
          <view class="label">补充备注</view>
          <view class="input row">
            <input
              type="text"
              v-model="form_info.remarks"
              placeholder="请输入"
            />
          </view>
        </view>
      </view>
      <view class="desc">佣金分成</view>
      <view class="default-box">
        <view class="input-box" style="margin-bottom:10px;margin-top:0">
          <view class="label">审批类型</view>
          <view class="type_list row">
            <view
              v-for="item in status_list"
              :key="item.id"
              :class="{ isactive: form_info.tracking_id == item.id }"
              class="type_item"
              @click="form_info.tracking_id = item.id"
            >
              {{ item.title }}
            </view>
          </view>
        </view>
        <view class="input-box" style="margin-top:0">
          <view class="label">销售分成</view>
          <view class="type_list row">
            <view
              v-for="item in type_list"
              :key="item.id"
              :class="{ isactive: form_info.type == item.id }"
              class="type_item"
              @click="form_info.type = item.id"
            >
              {{ item.title }}
            </view>
          </view>
        </view>
        <view class="audit-box">
          <view
            class="audit-ctn row"
            v-for="(item, i) in form_info.list"
            :key="i"
          >
            <view class="label row" v-if="admin_list.length > 0">
              <text class="l-l">{{ item.admin_name }}：</text>
              <picker
                v-if="admin_list"
                :disabled="form_info.type == 1"
                @change="bindPickerChange($event, i)"
                :value="index"
                range-key="user_name"
                :range="admin_list"
                style="width:60px;height:14px"
              >
                <text v-if="item.user_name">
                  {{ item.user_name }}
                </text>
                <text v-else style="color:#8a929f">请选择</text>
              </picker>
            </view>
            <view class="label row"
              ><text class="l-l">{{ item.radio_name }}：</text
              ><input
                :disabled="form_info.type == 1"
                v-model="item.radio"
                type="text"
                placeholder="比例"
              />
              %
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="btn-bottom">
      <view class="btn-box row">
        <view class="btn" @click="onCreateData">提交</view>
      </view>
    </view>
    <multiple-select
      v-model="is_show_admin"
      :data="admin_list"
      :allShow="false"
      labelName="user_name"
      valueName="id"
      @confirm="onConfirmAdmin"
    ></multiple-select>
  </view>
</template>

<script>
import myIcon from "@/components/my-icon.vue";
import multipleSelect from "@/components/multipleSelect/momo-multipleSelect.vue";
export default {
  components: {
    myIcon,
    multipleSelect,
  },
  data() {
    return {
      client_info: {},
      client_id: "",
      type_list: [
        { id: 1, title: "独立成交" },
        { id: 2, title: "合作分成" },
      ],
      form_info: {
        type: 1,
        tracking_id: "",
        list: [
          {
            admin_name: "",
            admin_id: "",
            radio_name: "",
            radio: "",
          },
        ],
      },
      status_list: [],
      admin_list: [],
      is_show_admin: false,
      index: 0,
    };
  },
  onLoad(options) {
    this.client_id = options.id;
    this.getUserDetail();
    this.getTrackingData();
    this.getAdminList();
  },
  watch: {
    "form_info.type"(value) {
      if (value === 1) {
        this.form_info.list = [
          {
            admin_name: "成交人",
            admin_id: this.client_info.create_user.id || "",
            user_name: this.client_info.create_user.user_name || "",
            radio_name: "分佣比例",
            radio: 100,
          },
        ];
      } else {
        this.form_info.list = [
          {
            admin_name: "录入人",
            admin_id: this.client_info.create_user
              ? this.client_info.create_user.id
              : "",
            user_name: this.client_info.create_user
              ? this.client_info.create_user.user_name
              : "",
            radio_name: "分佣比例",
            radio: 30,
          },
          {
            admin_name: "跟进人",
            admin_id: this.client_info.follow_user
              ? this.client_info.follow_user.id
              : "",
            user_name: this.client_info.follow_user
              ? this.client_info.follow_user.user_name
              : "",
            radio_name: "分佣比例",
            radio: 30,
          },
          {
            admin_name: "成交人",
            admin_id: this.client_info.deal_user
              ? this.client_info.deal_user.id
              : "",
            user_name: this.client_info.deal_user
              ? this.client_info.deal_user.user_name
              : "",
            radio_name: "分佣比例",
            radio: 40,
          },
        ];
      }
    },
  },
  methods: {
    getAdminList() {
      this.$ajax.get("/qywx/home/<USER>", {}, (res) => {
        if (res.statusCode === 200) {
          this.admin_list = res.data;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    getTrackingData() {
      this.$ajax.get("/qywx/tracking/list", { type: 3 }, (res) => {
        if (res.statusCode === 200) {
          this.status_list = res.data;
          this.form_info.tracking_id = res.data[0].id;
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    getUserDetail() {
      this.$ajax.get(`/qywx/client/info/${this.client_id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.client_info = res.data;
          this.form_info.name = res.data.cname;
          this.form_info.mobile = res.data.mobile;
          this.form_info.id = res.data.id;
          if (this.form_info.type === 1) {
            this.form_info.list[0].admin_name = "成交人";
            this.form_info.list[0].admin_id = res.data.create_user.id;
            this.form_info.list[0].user_name = res.data.create_user.user_name;
            this.form_info.list[0].radio_name = "独立成交";
            this.form_info.list[0].radio = 100;
          }
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    onConfirmAdmin(e) {
      var arr = e.map((item) => {
        return item.id;
      });
      this.form_info.examine_uid = arr.join(",");
    },
    bindPickerChange(e, index) {
      this.index = e.detail.value;
      this.form_info.list[index].admin_id = this.admin_list[this.index].id;
      this.form_info.list[index].user_name = this.admin_list[
        this.index
      ].user_name;
    },
    onCreateData() {
      this.$ajax.post("/qywx/client/update_tracking", this.form_info, (res) => {
        if (res.statusCode === 200) {
          this.$navigateTo(`/customer/detail?id=${this.client_info.id}&form=2`);
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
page {
  background: #f6f6f6;
}
.default {
  .default-content {
    padding: 14px 12px;
  }
  .desc {
    font-size: 16px;
    color: #2e3c4e;
    margin: 12px 0;
  }
  .default-box {
    background: #fff;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    padding: 12px;
    justify-content: space-around;
    .client-box {
      .c-t {
        align-items: center;
        .name {
          font-size: 16px;
          color: #2e3c4e;
        }
        .level {
          width: 16px;
          height: 16px;
          line-height: 16px;
          text-align: center;
          color: #fff;
          font-weight: 500;
          font-size: 11px;
          background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
          border-radius: 2px;
          margin: 0 8px;
        }
        .qw {
          width: 16px;
          margin-left: 12px;
        }
      }
      .c-b {
        font-size: 14px;
        color: #8a929f;
        margin-top: 9px;
      }
      .sex {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 12px;
      }
    }
  }
}
.btn-bottom {
  height: 100px;
  .btn-box {
    background: #fff;
    justify-content: space-between;
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 24px;
    .btn {
      height: 42px;
      width: 100%;
      line-height: 42px;
      text-align: center;
      color: #fff;
      background: #2d84fb;
      border-radius: 6px;
    }
  }
}
.input-box {
  margin: 24px 0 0;
  .label {
    font-size: 16px;
    color: #2e3c4e;
    font-weight: bold;
    margin-bottom: 12px;
  }
  .input {
    justify-content: space-between;
    background: #ffffff;
    padding: 10px 12px;
    align-items: center;
    border: 0.5px solid rgba(221, 225, 233, 1);
    border-radius: 4px;
    input {
      width: 100%;
    }
  }
  .type_list {
    justify-content: space-between;
    .type_item {
      padding: 10px 33px;
      width: 48%;
      text-align: center;
      font-size: 14px;
      background: #f8f8f8;
      border-radius: 4px;
      color: #8a929f;
      border: 1px solid #fff;
      &.isactive {
        background: #ffffff;
        color: #2d84fb;
        border: 1px solid rgba(45, 132, 251, 1);
      }
    }
  }
}
.audit-box {
  background: #f8f8f8;
  border-radius: 4px;
  padding: 12px;
  margin-top: 12px;
  .audit-ctn {
    margin-bottom: 10px;
    align-items: center;
    font-size: 14px;
    justify-content: space-between;
    .label {
      align-items: center;
      color: #8a929f;
      text {
      }
      input,
      picker {
        font-size: 14px;
        color: #2e3c4e;
        width: 50px;
        overflow: hidden;
      }
    }
  }
}
</style>
