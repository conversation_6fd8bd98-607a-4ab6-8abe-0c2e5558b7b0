// import { statistics } from './statistics'
export default {
  methods: {
    getWxConfig (jsApiList, callback, siteID) {
      // statistics()
      let url;
      url = window.location.href;
      let params = { url };
      if (siteID) {
        params.website_id = website_id;
      }
      this.$ajax.get("/common/wx_open/query/js_sdk/wx_conf", params, (res) => {
        if (res.statusCode === 200) {
          res.data.jsApiList = jsApiList || [
            // "updateAppMessageShareData",
            // "updateTimelineShareData",
            "onMenuShareAppMessage",
            "scanQRCode",
          ];
          res.data.debug = false;
          this.$wx.config(res.data);
          this.wxInit(callback);
        } else {
          // uni.showToast({
          //   title: res.data.message,
          //   icon: "none",
          // });
          callback(this.$wx);
        }
      });
    },
    wxInit (callback) {
      console.log("jssdk开始初始化");
      this.$wx.ready(() => {
        this.wx_init = true;
        console.log("jssdk初始化完成");
        if (callback) {
          callback(this.$wx);
        }
        if (this.share) {
          this.initShare();
        } else if (this.openScan) {
          this.scan();
        } else if (this.jumpWx) {
          this.jumpWxApp();
        }
      });
    },
    // 扫码
    scan () {
      const _this = this;
      this.$wx.scanQRCode({
        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
        scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
        success: function (res) {
          let str = res.resultStr; // 当needResult 为 1 时，扫码返回的结果
          let str2 = str.indexOf("/project_broker/all_customer") != -1;
          if (str2) {
            _this.$navigateTo(str);
          } else {
            uni.showToast({
              title: "该地址无效",
              icon: "none",
            });
          }
        },
      });
    },
    GetQueryString (name) {
      var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
      var r = window.location.search.substr(1).match(reg);
      if (r != null) return unescape(r[2]);
      return null;
    },
    initShare () {
      let link =
        this.share.link || window.location.origin + window.location.pathname;
      if (!uni.getStorageSync("website_id" + this.$store.state.website_id)) {
        uni.setStorageSync("website_id", getApp().globalData.website_id);
      }
      if (
        link.indexOf("?website_id=") === -1 &&
        link.indexOf("&website_id=") === -1
      ) {
        // 判断链接是否有其他参数
        const reg = /\?.+=.{0,}/;
        if (reg.test(link)) {
          link += `&website_id=${uni.getStorageSync(
            "website_id" + this.$store.state.website_id
          ) || 1}`;
        } else {
          link += `?website_id=${uni.getStorageSync(
            "website_id" + this.$store.state.website_id
          ) || 1}`;
        }
      }
      // if (link)
      // 需在用户可能点击分享按钮前就先调用
      if (this.share.forward_recommend_code) {
        // this.$wx.updateAppMessageShareData({
        //   title: this.share.forward_title || "", // 分享标题
        //   desc: this.share.forward_desc, // 分享描述
        //   link:
        //     link +
        //     `share=from_share&recommend_code=${this.share.forward_recommend_code}`, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
        //   imgUrl: this.share.forward_pic, // 分享图标
        //   success: function() {
        //     // 设置成功
        //   },
        // });
        // this.$wx.updateTimelineShareData({
        //   title: this.share.forward_title || "", // 分享标题
        //   desc: this.share.forward_desc,
        //   link:
        //     link +
        //     `&share=from_share&recommend_code=${this.share.forward_recommend_code}`, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
        //   imgUrl: this.share.forward_pic, // 分享图标
        //   success: () => {
        //     // 设置成功
        //   },
        // });
        this.$wx.onMenuShareAppMessage({
          title: this.share.forward_title || "", // 分享标题
          desc: this.share.forward_desc,
          link:
            link +
            `&share=from_share&recommend_code=${this.share.forward_recommend_code}`, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          imgUrl: this.share.forward_pic, // 分享图标
          success: () => {
            // 设置成功
          },
        });
      } else {
        // this.$wx.updateAppMessageShareData({
        //   title: this.share.forward_title || "", // 分享标题
        //   desc: this.share.forward_desc, // 分享描述
        //   link:
        //     link +
        //     `&buildID=${this.GetQueryString(
        //       "buildID"
        //     )}&share=from_share&share_id=${this.share.forward_id}`, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
        //   imgUrl: this.share.forward_pic, // 分享图标
        //   success: function() {
        //     // 设置成功
        //   },
        // });
        // this.$wx.updateTimelineShareData({
        //   title: this.share.forward_title || "", // 分享标题
        //   desc: this.share.forward_desc,
        //   link:
        //     link +
        //     `&buildID=${this.GetQueryString(
        //       "buildID"
        //     )}&share=from_share&share_id=${this.share.forward_id}`, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
        //   imgUrl: this.share.forward_pic, // 分享图标
        //   success: () => {
        //     // 设置成功
        //   },
        // });
        this.$wx.onMenuShareAppMessage({
          title: this.share.forward_title || "", // 分享标题
          desc: this.share.forward_desc,
          link:
            link +
            `&buildID=${this.GetQueryString(
              "buildID"
            )}&share=from_share&share_id=${this.share.forward_id}`, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
          imgUrl: this.share.forward_pic, // 分享图标
          success: () => {
            // 设置成功
          },
        });
      }
    },
    jumpWxApp (applets_url) {
      var ua = window.navigator.userAgent.toLowerCase();
      //先判断是否微信浏览器
      if (ua.match(/MicroMessenger/i) == "micromessenger") {
        //再判断一下是否在小程序里
        this.$wx.miniProgram.getEnv((res) => {
          if (res.miniprogram) {
            // 设置跳转回首页路径
            if (applets_url) {
              wx.miniProgram.navigateTo({ url: applets_url });
            } else {
              var url = "/index/mine";
              wx.miniProgram.switchTab({ url });
            }
          }
        });
      }
    },
    previewImage (arr, index) {
      if (typeof arr === "object") {
        this.$wx.previewImage({
          urls: arr,
          current: arr[index],
        });
      } else {
        var imgArr = [];
        imgArr.push(arr);
        this.$wx.previewImage({
          urls: imgArr,
          current: arr[index],
        });
      }
    },
  },
};
