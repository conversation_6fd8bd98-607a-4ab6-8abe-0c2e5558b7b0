<template>
  <view class="list-box">
    <view class="flex1">
    <view class="bgcolor">
      <view class="headeritle">
<view>
  <!-- <image src="../static/icon/index/fanhui.png" style="width: 12px;height: 24px"></image> -->
</view>
<!-- <view style="color: #FFF;text-align: center;font-size: 48rpx;width: 100%;">T+</view> -->
      </view>
      <view class="image">
        <image src="https://img.tfcs.cn/backup/images/new_icon/20230921105831.png" style="width: 188rpx; height: 182rpx; "></image>
    </view>
      </view>
 
   <view>
    <view style="padding: 48rpx 48rpx 0  48rpx" v-if="inputShow">
        <view class="input-container" >
          <uni-easyinput class="textInput" trim="all"   v-model="ruleForm2.phone" @input="checkTel" placeholder="请输入手机号" :inputBorder="false"></uni-easyinput>
        </view>
     <view class="passwordInput">
      <uni-easyinput  type="password"   v-model="ruleForm2.password" placeholder="请输入密码" :inputBorder="false"></uni-easyinput>
     </view>
     <view v-show="show_wesite">
      <uni-data-select
      v-model="website_id"
      :localdata="webList"
      @change="change"
    ></uni-data-select>
     </view>
      </view>
    <view class="list">
      <view class="login-box">
        <button v-if="inputShow" type="default" style="color:#ffffff;border-color:#3399ff;width: 100%; background-color: #3399ff;" :round="false" size="big"
           @click="handleSubmit1">
          <!-- <myIcon type="weixin" style="margin-right: 10rpx" color="#fff"></myIcon> -->
          登录账号
        </button>
        <button v-if="!is_phone_login && !showqiye" type="default" style="color:#3399ff;border-color:#3399ff;width: 100%;margin-top: 48rpx;" :round="false" size="big" :plain="true"
          open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" @click="inputShow = false">
          <!-- <myIcon type="weixin" style="margin-right: 10rpx" color="#fff"></myIcon> -->
          本机号码一键登录
        </button>
        <view class="radio-box row" v-if="showqiye">
          <picker @change="bindPickerChange" mode='selector' :value="listindex" :range="arrlist" class='Picker'>
            <view class="text">{{ index == -1 ? '请选择企业' : arrlist[index] }}</view>
          </picker>
        </view>
        <button v-if="login"  class="btn btn-left" @click="Confirm_login" style="margin-top: 48rpx;">
          确认登录
        </button>
      </view>
    </view>
   </view>
  </view>
    <view class="radio-box row">
          <view class="radio-content row" @click="changeRadio">
            <radio class="radio-form" :checked="checked"  />
            <text class="row">
              我已阅读并同意<text style="text-decoration: underline;color:#3399ff" @click="openContent(3)"
                >《隐私政策》</text
              >及<text style="text-decoration: underline;color:#3399ff" @click="openContent(4)"
                >《用户服务协议》</text
              >
            </text>
          </view>
        </view>
  </view>
</template>

<script>
import { mapState } from "vuex";
import myIcon from "@/components/my-icon.vue";
export default {
  components: {
    myIcon,
  },
  data() {
    return {
      inputShow:true,// 控制手机号登录input框
      checked: false,
      // 手机号
      form: {
        phone: "",
        captcha: "",
      },
      params1: {
        code: "",
        encrypted_data: "",
        iv: "",
        // nickname:"",
        // headimgurl:"",
      },
      array: [],
      index: -1,
      listindex: "",
      arrlist: [],
      params2: {
        website_id: "",
        phone: "",
        token: "",
      },
      //验证码按钮
      sending: false,
      time: "",
      phone_token: "",
      code_value: false,
      loginUrl: "",
      params: {
        website_id: "",
        category: 1,
        code: "",
      },
      is_phone_login: false,
      showqiye: false,
      login: false,
      value: 0,
      range: [
        { value: 0, text: "篮球" },
        { value: 1, text: "足球" },
        { value: 2, text: "游泳" },
      ],
      ruleForm2: {
        // 测试账号
        phone: "",
        // 测试密码
        password: "",
        // captcha: "",
      },
      show_wesite: false, // 控制手机号企业选择
      website_id:'',
      webList:[]
    };
  },
  computed: {
    ...mapState(["siteConfig"]),
  },
  onLoad(options) {
    // this.params.website_id = options.website_id || 1;
    let url = uni.getStorageSync("loginUrl");
    this.loginUrl = url.split("fenxiao/")[1];
    this.getInfo()
    // if (options.code) {
    //   console.log(options.code);

    //   this.params.code = options.code;
    //   this.postCode();
    // }
  },
  onUnload() {
    uni.removeStorageSync("is_logining")
  },
  methods: {
    onLoginDone(){
      uni.showToast({
        title: '登录成功'
      });
      this.login = false


      /* const currentPages = getCurrentPages();
      const backPage = currentPages[currentPages.length - 2],
            onLoads = backPage ? backPage.$vm?.$options?.onLoad : null;
      if(onLoads){
        for(const fn of onLoads){
          fn.call(backPage.$vm, backPage.options);
        }
      } */
      uni.navigateBack({
          fail: ()=>{
            this.$store.commit('setNeedRefreshWhenShow', true);
            uni.switchTab({
              url: '/pages/customer/index',
              fail(e){
                console.error(e);
              }
            });    
          }
      });

      /* if(currentPages.length > 1){
        uni.navigateBack({
          success: ()=>{
          }
        });
      }else{
        this.$store.commit('setNeedRefreshWhenShow', true);
        uni.switchTab({
          url: '/pages/customer/index',
          fail(e){
            console.log(e);
          }
        });
      } */
    },
    // 提示
    checkedBtn(){
      uni.showToast({
        title:"请勾选隐私协议",
        icon: "none",
       })
    },
    getConfigCode() { },
    changeRadio(e) {
      this.checked = !this.checked;
    },
    inputValue(e) {
      this.code_value = e.target.value ? true : false;
    },
    clearCode() {
      this.form.captcha = "";
      this.code_value = false;
    },
    // 点击登录
    handleSubmit1() {

      // 判断是否为开发模式
      if (process.env.NODE_ENV === 'development') {
        // 开发模式下使用固定的 website_id 和 token
        const website_id = 626
        const token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wveXVuLnRmY3MuY25cL2FwaVwvc3VwZXJcL2FkbWluXC93ZWJzaXRlXC90b2tlblwvNjI2IiwiaWF0IjoxNzQ4NDg4NTM1LCJleHAiOjE3NDk3ODQ1MzUsIm5iZiI6MTc0ODQ4ODUzNSwianRpIjoiNEpvVXBJTDhqOUF6bVJBUSIsInN1YiI6MTc3MCwicHJ2IjoiY2M5MTZkOWY2ZTNhMzAyN2MwOGM0MGZmMDcwZjAxMzNhMGY0ZjQ1ZSIsInRpZCI6MSwid2lkIjo2MjYsInNpZCI6IjEiLCJsX2YiOjAsInNhYWkiOjB9.a3J7wXb4w4fwu9DASopuGuLOAQxZnkPHSEvdGjfQzPE"

        // 设置缓存
        uni.setStorageSync('website_id', website_id)
        uni.setStorageSync('wxwork_id', website_id)
        uni.setStorageSync('wxwork_token', token)
        uni.setStorageSync("token" + website_id, token);
        uni.setStorageSync("isLoadEnd", 1);
        uni.showToast({
          title: '开发模式登录成功'
        });
        this.onLoginDone();
        return;
      }




      if(!this.checked){
        uni.showToast({ title: "请阅读并同意隐私协议", icon: "none" });
        return;
      }
      if (!this.website_id) {
        this.ruleForm2.phone = "" 
        // 测试密码
        this.ruleForm2.password = ""
       uni.showToast({
        title:"未查询到该手机号信息",
        icon: "none",
       })
        return
      }
      uni.setStorageSync("website_id", this.website_id)
      if (
        // 测试账号
        this.ruleForm2.phone === "" ||
        // 测试密码
        this.ruleForm2.password === ""
      ) {
        uni.showToast({
        title:"用户名或密码输入错误",
        icon: "none",
       })
        return
      }
      let paramsshon = {
          user_name: this.ruleForm2.phone,
          password: this.ruleForm2.password
      }
      // console.log(website_id,"\\\\\\");
      this.$ajax.post("/auth/admin/login/user_name",paramsshon,(res) => {
          console.log(res,'88888000900');
          if (res.statusCode === 200) {
            uni.setStorageSync("website_id", this.website_id);
            uni.setStorageSync("wxwork_id", this.website_id);
            uni.setStorageSync("token" + this.website_id, res.data.token);
            uni.setStorageSync("wxwork_token", res.data.token);
            uni.setStorageSync("isLoadEnd", 1);
     
        this.onLoginDone();
        
      }else{
        uni.showToast({
          title:res.data.message,
          icon:"none" 
        })
      }
          })
    },
    onSubmit() {
      if (!this.checked) {
        uni.showToast({
          title: "请阅读并同意相关内容",
          icon: "none",
        });
        return;
      }
      if (!this.form.phone || !/^1[3456789]\d{9}$/.test(this.form.phone)) {
        uni.showToast({
          title: "请检查手机号码格式",
          icon: "none",
        });
        return;
      }
      if (!this.form.captcha) {
        uni.showToast({
          title: "请输入验证码",
          icon: "none",
        });
        return;
      }
      this.$ajax.post(
        "/auth/client/login/phone",
        {
          website_id: this.params.website_id,
          phone: this.form.phone,
          captcha: this.form.captcha,
        },
        (res) => {
          if (res.statusCode === 200) {
            this.phone_token = res.data.token;
            uni.showToast({
              title: "登录成功",
            });
            // uni.setStorageSync("token", this.phone_token);
            uni.setStorageSync(
              "token" + this.$store.state.website_id,
              this.phone_token
            );
            uni.reLaunch({
              url: "/" + this.loginUrl,
            });
          } else {
            uni.showToast({
              title: res.data.message || "登录失败",
              icon: "none",
            });
          }
        }
      );
    },
    //获取验证码按钮点击计时事件
    getCode() {
      if (this.sending) {
        return;
      }
      if (!this.form.phone || !/^1[3456789]\d{9}$/.test(this.form.phone)) {
        uni.showToast({
          title: "请检查手机号码格式",
          icon: "none",
        });
        return;
      }
      this.sending = true;
      this.$ajax.post(
        "/auth/client/send/sms/captcha",
        {
          website_id: this.params.website_id,
          phone: this.form.phone,
        },
        (res) => {
          if (res.statusCode === 200) {
            uni.showToast({
              title: "发送成功",
              icon: "none",
            });
            this.time = 60;
            this.timerDown();
            this.sending = true;
          } else {
            uni.showToast({
              title: res.data.message || "网络错误",
              icon: "none",
            });
            // this.refCode();
          }
        }
      );
    },
    // 手机号
    checkTel() {
      this.website_id = ''
      this.webList = []

      if (!this.ruleForm2.phone || !/^1[3456789]\d{9}$/.test(this.ruleForm2.phone)) {
        return;
      }

      this.$ajax.post("/auth/admin/login/findSiteByPhone",this.ruleForm2 ,(res) => {
        if (res.statusCode == 200) {
          console.log(1111);
          if (res.data.count > 1) {
            for(let item of res.data.list){
              item.text = item.name
              item.value =item.id
              delete item.name
              delete item.id
              console.log(item,"item,item");
            }

            this.webList = res.data.list

            console.log(this.webList,"this.webList9999");
            this.show_wesite = true
          } else if (res.data.count == 1) {
            this.website_id = res.data.list[0].id
            console.log(this.website_id,"999999");
            this.show_wesite = false
            uni.setStorageSync("website_id", res.data.list[0].id)
          } else {
            this.show_wesite = false
          }
        }
      });
    },
    timerDown() {
      // 倒计时
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = setInterval(() => {
        if (this.time <= 0) {
          clearInterval(this.timer);
          this.sending = false;
          return;
        }
        this.time--;
      }, 1000);
    },
    refCode() {
      this.img_code += "?";
    },
    openContent(type) {
      this.$navigateTo(`/user/agreement?type=${type}`);
    },
    bindPickerChange(e) {
      uni.removeStorageSync('wxwork_id');
      console.log(e);
      console.log('picker发送选择改变，携带值为', e.detail.value)
      this.index = e.detail.value
      this.listindex = e.detail.value
      var matchingData = this.array[e.detail.value];
      this.params2.website_id1 = matchingData.id
      if (!uni.getStorageSync("wxwork_id") && this.params2.website_id1) {
        uni.setStorageSync("wxwork_id", this.params2.website_id1)
      }
      this.login = true
    },
    getPhoneNumber(e) {
      console.log(11111111111);

      let list = []
      if (e.detail.errMsg === "getPhoneNumber:fail user deny") {
        this.inputShow = true
        console.log('用户拒绝授权');
      } else {
        console.log('授权成功', e);
        this.params1.code = this.weixincode
        this.params1.encrypted_data = e.detail.encryptedData
        this.params1.iv = e.detail.iv
        // this.params1.wxwork_id = 176
        console.log(this.params1, "this.params1");
        this.$ajax.post("/auth/admin/login/auth_mini_wechat", this.params1, (res) => {
          console.log(res, 'res');
          if (res.statusCode == 200) {
            if (res.data.list.length == 1) {
              this.params2.website_id1 = res.data.list[0].id
              this.params2.phone = res.data.phone
              this.params2.token = res.data.token
              if (!uni.getStorageSync("wxwork_id") && this.params2.website_id1) {
                uni.setStorageSync("wxwork_id", this.params2.website_id1)
              }
              this.empower(this.params2)
            } else {
              this.inputShow = false
              this.showqiye = true
              this.array = res.data.list
              this.array.map(item => {
                console.log(item);
                this.arrlist.push(item.name)
              })
              this.params2.phone = res.data.phone
              this.params2.token = res.data.token
            }
          }
        })
      }
    },
    empower(val) {
      let token = ""
      console.log(val, '12345');

      let objA = {
        website_id: JSON.parse(JSON.stringify(val.website_id1)),
        phone: val.phone,
        token: val.token
      }
      console.log(objA.website_id, 'objA');
      uni.setStorageSync('website_id', objA.website_id)
      uni.setStorageSync('wxwork_id', objA.website_id)
      this.$ajax.post("/auth/admin/login/login_auth_mini_wechat", objA, (res) => {
        console.log(res, 'res12211');
        if (res.statusCode == 200) {
          token = res.data.token
          uni.setStorageSync('wxwork_token', token)
          uni.setStorageSync("token" + objA.website_id, token);

          this.onLoginDone();

          

        };
      })
    },
    // 下拉框
    change(e) {
      console.log("e:", e);
    },
    Confirm_login() {
      if(!this.checked){
        uni.showToast({ title: "请阅读并同意隐私协议", icon: "none" });
        return;
      }
      this.empower(this.params2)
    },
    getInfo() {
      console.log(123);
      uni.login({
        provider: "weixin",
        success: (loginRes) => {
          if (loginRes.code) {
            // console.log(loginRes.code, "111111111");
            // return 
            this.weixincode = loginRes.code
            console.log(loginRes.code, 'loginRes.code');
          }
        }
      })
      // if (!this.checked) {
      //   uni.showToast({
      //     title: "请阅读并同意相关内容",
      //     icon: "none",
      //   });
      //   return;
      // }
      // if (this.$isWxWork() === "wxwork" || this.$isWxWork() === "com-wx-pc") {
      //   // 判断当前环境是否是企业微信
      //   this.$ajax.post(
      //     "/common/wx_work/auth/get/link/user/login/3rd",
      //     {
      //       redirect_uri: window.location.href,
      //       scope: "snsapi_userinfo",
      //       state: "state",
      //     },
      //     (res) => {
      //       if (res.statusCode === 200) {
      //         window.location.href = res.data.link;
      //       } else {
      //         uni.showToast({
      //           title: res.data.message || "获取登录跳转路径失败",
      //           icon: "none",
      //         });
      //       }
      //     }
      //   );
      // } else {
      //   //反之走微信公众号网页登录流程
      //   this.$ajax.get(
      //     `/common/config/query/wx_public_web_login/client/${this.params.website_id}`,
      //     {},
      //     (res) => {
      //       if (res.statusCode === 200) {
      //         window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${res.data.app_id}&redirect_uri=${window.location.href}&response_type=code&scope=snsapi_userinfo&state=%7b%22w_id%22%3a1%2c%22xx%22%3a11%2c%22b%22%3a%22c%22%7d&component_appid=${res.data.component_app_id}#wechat_redirect`;
      //       } else {
      //         uni.showToast({
      //           title: res.data.message,
      //           icon: "none",
      //         });
      //       }
      //     }
      //   );
      // }
    },
    postCode() {
      var url;
      // if (this.$isWxWork() === "wxwork" || this.$isWxWork() === "com-wx-pc") {
      //   //判断当前环境切换链接
      //   url = "/auth/client/login/wx_work/3rd";
      // } else {
      url = "/auth/client/login/wx_public";
      // }
      this.$ajax.post(url, this.params, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "登录成功",
          });
          // uni.setStorageSync("token", res.data.token);
          uni.setStorageSync("token" + this.params.website_id, res.data.token);
          if (!res.data.phone) {
            uni.showModal({
              content: "您还没有绑定手机号 是否绑定手机号",
              cancelText: "暂不绑定",
              confirmText: "立即绑定",
              success: res => {
                if (res.confirm) {
                  // uni.redirectTo({
                  //   url: '/user/change_tel'
                  // })
                  this.$navigateTo('/user/change_tel?is_bind=1')
                } else {
                  setTimeout(() => {
                    uni.switchTab({
                      url: `/index/mine`,
                    });
                  }, 1200);
                }
              }
            })
          } else {
            // uni.switchTab({
            //   url: `/index/mine`,
            // });
            setTimeout(() => {
              uni.switchTab({
                url: `/index/mine`,
              });
              // history.go(-2);
            }, 1200);
          }
          // uni.switchTab({
          //   url: `/index/mine`,
          // });
          // let loginUrl = uni.getStorageSync("loginUrl");
          // if (loginUrl) {
          //   window.open(loginUrl);
          // }
        } else {
          uni.showToast({
            title: res.data.message || "登录失败",
            icon: "none",
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.list-box{
  display: flex;
  height: 100vh;
  flex-direction: column;
  .flex1{
    flex: 1;
  }
  .radio-box{
    padding-bottom: 50rpx;
  }
}
::v-deep uni-radio .uni-radio-input {
  border-radius: 50% !important;
}
 .radio-content {
  /* position: fixed;
  bottom:  225rpx;
  left: 0; */
      font-size: 28rpx;
      color: #d8d8d8;
      margin: 48rpx 45rpx;
      align-items: center;

      .radio-form {
        transform: scale(0.7);
      }
      //     background-color: #007aff
    }
::v-deep .uni-easyinput__content-input{
height: 88rpx;
}
::v-deep .uni-easyinput__content{

background-color: #f7f7f7 !important;
border-radius: 20rpx !important;  
}
::v-deep .uni-stat__select {
  background-color: #f7f7f7 !important;
border-radius: 20rpx !important; 
height: 88rpx;
margin-top: 48rpx;
}
.input-container {
  width: 659rpx;
  height: 44px;
  overflow: hidden;

}

.textInput {
  width: 100%;
  height: 100%;
  background-color: #999; /* 设置背景为透明以去除默认的背景色 */
}
.passwordInput{
  padding-top: 48rpx;
}
.headeritle{
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 120rpx;
 margin-left: 48rpx;
}
.bgcolor{
  position: relative;
  background: #2C7CFF;
  width: 750rpx;
height: 560rpx;
}
.image{
  position: absolute;
  top: 240rpx;
  left: 290rpx;
}
.list {
  padding:48rpx;

  .title {
    font-size: 46rpx;
    justify-content: space-between;
    color: #333;
    margin: 96rpx 0;
    align-items: center;
  }

  .login-box {
    .login_phone {
      justify-content: space-between;
      padding: 24rpx 0;
      width: 100%;
      border-bottom: 2rpx solid #eee;
      position: relative;

      .clearCode {
        position: absolute;
        right: 0;
        top: 40rpx;
        font-size: 28rpx;
        color: #999;
      }
    }

    .radio-content {
      font-size: 28rpx;
      color: #d8d8d8;
      margin: 48rpx 0;
      align-items: center;

      .radio-form {
        transform: scale(0.7);
      }

      //     background-color: #007aff
    }

    .btn {
      text-align: center;
      font-size: 36rpx;
      width: 100%;
      // height: 112rpx;
      // opacity: 0.6;
      color:#3399ff;
      border-color:#3399ff;
      width: 100%;
      margin-top: 48rpx
    }
.btns{
    color:#ffffff;
  border-color:#3399ff;
  width: 100%;
   background-color: #3399ff;
   text-align: center;
      font-size: 36rpx;
}
    .hava-value {
      opacity: 0.5;
    }

    .radio-box {
      .text {
        margin-top: 25rpx;
      }

      .Picker {
        width: 100%;
        height: 80rpx;
        border: 2rpx solid #8a929f;
        border-radius: 50rpx;
        margin-top: 48rpx;
        text-align: center;

        .text {
          margin-top: 25rpx;
        }
      }
    }

  }
}

::v-deep uni-radio .uni-radio-input {
  border-radius: 50% !important;
}

.btn-left {
  // margin-top: 20rpx;
}

.login-box-1 {
  width: 568rpx;
  height: 680rpx;
  background: #fff;
  border-radius: 8rpx;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);

  .title {
    margin: 60rpx auto 0;
    font-size: 36rpx;
    font-weight: 500rpx;
    color: #333;
  }

  image {
    height: 310rpx;
    width: 310rpx;
    display: block;
    margin: 75rpx auto 67rpx;
  }

  .login {
    color: #fff;
    height: 72rpx;
    line-height: 72rpx;
    margin: 0 auto;
    width: 400rpx;
    background: #2e8cef;
    border-radius: 36px;
    position: relative;
  }
}

.imgbg {
  height: 280rpx;
  width: 100%;
  border-radius: 24rpx;
  margin-bottom: 88rpx;
}

.phonelogin {
  font-size: 24rpx;
  color: #999;
  height: 64rpx;
  width: 244rpx;
  background: #f8f8f8;
  border-radius: 32rpx;
  text-align: center;
  line-height: 64rpx;
  position: fixed;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
}
</style>
