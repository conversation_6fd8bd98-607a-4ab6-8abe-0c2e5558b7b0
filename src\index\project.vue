<template>
  <view>
    <view class="list" style="min-height: 100vh">
      <!-- 单城市模式 -->
      <view class="screen-tab row sel-tab" id="tab_top" v-if="site_info.city_type == 1">
        <view class="screen-tab-item row" @click="switchTab(1)">
          <text>{{ areaName }}</text>
          <my-icon type="xiala" color="#d8d8d8" size="12rpx"></my-icon>
        </view>
        <view class="screen-tab-item row" @click="switchTab(2)">
          <text>{{ typeName }}</text>
          <my-icon type="xiala" color="#d8d8d8" size="12rpx"></my-icon>
        </view>
        <scroll-view
          scroll-y
          class="screen-panel"
          :class="nowTab == 1 ? 'show' : ''"
          @touchmove.stop.prevent="stopMove"
          v-if="showTab"
        >
          <addressd :addressd="area" ref="showArea" @changes="changeArea"></addressd>
          <!-- <block v-for="item in area" :key="item.areaid">
        <uni-list-item :title="item.areaname" show-arrow="false" @click="selectArea(item.areaid, item.areaname)"></uni-list-item>
      </block> -->
        </scroll-view>
        <scroll-view
          scroll-y
          class="screen-panel more-panel"
          :class="nowTab == 2 ? 'show' : ''"
          @touchmove.stop.prevent="stopMove"
        >
          <view class="more-screen-item">
            <view class="options row">
              <view
                class="options-item"
                @click="selectOption({ category_id: item.value }, 'category_id')"
                :class="params.build_category == item.value ? 'active' : ''"
                v-for="(item, index) in build_cates"
                :key="index"
                >{{ item.description }}</view
              >
            </view>
          </view>
        </scroll-view>
      </view>
      <!-- 多城市模式 -->
      <view
        class="search row"
        v-if="site_info.city_type == 2"
        @click="$navigateTo('/index/search-build')"
      >
        <view class="new-search row">
          <view class="left-n row right-line" @click.stop="$navigateTo('/build/city_list')">
            {{ city.name || '获取定位' }}
            <image
              src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5index/xialajiantou.png"
            ></image>
          </view>
          <view class="line"></view>
          <view class="right-n row">
            <myIcon class="icon" type="ic_sousuo3x1" color="#D1D1D1" size="32rpx"></myIcon>
            <input
              class="uni-input"
              :disabled="true"
              type="text"
              placeholder="请输入楼盘名称"
              placeholder-style="font-size:26rpx"
            />
          </view>
        </view>
      </view>
      <tab-bar
        v-if="site_info.city_type == 2"
        :fixedTop="false"
        :nowIndex="params.build_category"
        :tabs="build_cates"
        @click="handelBar"
      >
      </tab-bar>
      <view class="build-list">
        <!-- <my-build
        v-for="item in build_list"
        :key="item.id"
        :item="item"
        @click="
          $navigateTo(`/build/detail?id=${item.id}&buildID=${item.build_id}`)
        "
        ><template v-slot:yong v-if="display_brokerage">
          <view class="price-right row" v-if="item.brokerage_rule">
            <text class="yong">佣</text>
            <text class="jiage">{{ item.brokerage_rule }}</text>
          </view>
        </template></my-build
      > -->
        <myBuild
          v-if="build_list.length"
          @click="onBuildDetail"
          :list="build_list"
          :is_display="display_brokerage"
        ></myBuild>
      </view>
      <myLoading ref="loading" :custom="false" :shadeClick="true" :type="1"></myLoading>
      <load-more :status="load_status"></load-more>
      <view
        class="mask"
        :class="nowTab > 0 ? 'show' : ''"
        @click="closeMask"
        @touchmove.stop.prevent="stopMove"
      ></view>
    </view>
    <BottomBar @click="switchTabBottom" :current="currentTabIndex"></BottomBar>
  </view>
</template>

<script>
// import myBuild from "@/components/build-item";
import myBuild from "@/components/new_build_list";
import myLoading from "@/components/my-loading";
import tabBar from "@/components/tabBar";
import { mapActions, mapMutations, mapState } from "vuex";
import loadMore from "@/components/loadMore";
import addressd from "@/components/jm-address.vue";
import myIcon from "@/components/my-icon.vue";
import location from "../page_outside/tools/get_location.js";
export default {
  mixins: [location],
  components: {
    myBuild,
    myLoading,
    tabBar,
    loadMore,
    myIcon,
    addressd,
  },
  data () {
    return {
      build_list: [],
      build_status_list: [],
      build_category_list: [],
      noData: false,
      build_cates: [],
      params: {
        page: 1,
        row: 10,
        build_category: 0,
        list_recommend: 1,
        region_0: 0,
        region_1: 0,
      },
      display_brokerage: false,
      load_status: "",
      areaName: "区域",
      typeName: "类型",
      nowTab: 0,
      showPannel: false,
      showTab: false,
      area: [],
      category_id: "0",
      site_info: {},
      currentTabIndex: 1,
    };
  },
  onReady () {
    // this.$refs.loading.open();
  },
  onShow () {
    this.setUrlWebsiteId();
    // uni.$off("refesh");
    // uni.$on("refesh", (res) => {
    //   this.params.region_0 = res.region_0;
    //   this.params.region_1 = res.region_1;
    //   this.params.page = 1;
    //   this.getDataList();
    // });
  },
  watch: {
    city (val, oval) {
      if (this.site_info.city_type == 1) {
        return;
      }
      this.params.page = 1;
      this.params.region_0 = val.region_0;
      this.params.region_1 = val.region_1;
      this.getDataList();
    },
  },
  computed: {
    ...mapState(["city"]),
  },
  onLoad () {
    this.init();
  },
  methods: {
    ...mapActions(["getSetting"]),
    ...mapMutations(["setCityData"]),
    switchTabBottom (index, item) {
      if (this.currentTabIndex == index) {
        return;
      }
      uni.switchTab({
        url: item.path,
      });
    },
    stopMove () { },
    getRegionData () {
      this.$ajax.get("/common/website/region", {}, (res) => {
        if (res.statusCode === 200) {
          res.data.push({ id: "", pid: 0, name: "全部" });
          this.area = this.getJiedao(res.data, "id", "pid", "city");

          this.showTab = true;
        }
      });
    },
    getJiedao (a, idStr, pIdStr, chindrenStr) {
      var r = [],
        hash = {},
        id = idStr,
        pId = pIdStr,
        children = chindrenStr,
        i = 0,
        j = 0,
        len = a.length;
      for (; i < len; i++) {
        a[i].label = a[i].name;
        delete a[i].name;
        hash[a[i][id]] = a[i];
      }
      for (; j < len; j++) {
        var aVal = a[j],
          hashVP = hash[aVal[pId]];
        if (hashVP) {
          !hashVP[children] && (hashVP[children] = []);

          hashVP[children].unshift(aVal);
        } else {
          r.unshift(aVal);
        }
      }
      return r;
    },
    switchTab (index) {
      this.scroppTo(() => {
        let timeout = setTimeout(() => {
          if (this.nowTab == index) {
            this.nowTab = 0;
            this.showPannel = false;
          } else {
            this.nowTab = index;
            this.showPannel = true;
            if (index == 1) {
              this.$refs.showArea.showAddress();
            }
          }
        }, 500);
      });
    },
    selectOption (obj, type) {
      this.params.build_category = obj.category_id;
      this.nowTab = 0;
      this.params.page = 1;
      this.getDataList();
    },
    handelBar (e) {
      this.params.build_category = e.value;
      this.params.page = 1;
      this.getDataList();
    },
    scroppTo (fun) {
      const query = uni.createSelectorQuery().in(this);
      query
        .select("#tab_top")
        .fields({ rect: true, scrollOffset: true }, (data) => {
          if (data.top <= this.status_top + 44) {
            fun && fun();
            return;
          }
          // #ifdef H5
          (this.scrollTopOffset =
            (this.scrollTop || 0) + data.top - uni.upx2px(80)),
            // #endif
            // #ifndef H5
            (this.scrollTopOffset =
              (this.scrollTop || 0) + data.top - uni.upx2px(80)),
            // #endif
            uni.pageScrollTo({
              duration: 120,
              // #ifdef H5
              scrollTop: this.scrollTopOffset,
              // #endif
              // #ifndef H5
              scrollTop: this.scrollTopOffset,
              // #endif
              success: () => {
                if (fun) {
                  fun();
                }
              },
            });
        })
        .exec();
    },
    init () {
      // 佣金是否显示
      this.getSetting((e) => {
        this.site_info = e;
        let display = e.login_display_brokerage_rule;
        let token = uni.getStorageSync("token" + this.$store.state.website_id);
        if (display == 1 || token) {
          this.display_brokerage = true;
        } else if (display == 0) {
          this.display_brokerage = false;
        }
        // 总控开启是否是多城市模式
        if (e.city_type == 2) {
          this.getLocation(); // 获取位置
          this.params.page = 1;
          this.params.region_0 = this.city.region_0;
          this.params.region_1 = this.city.region_1;
          this.areaName = this.city.name;
        } else {
          this.getRegionData();
          this.getDataList();
        }
        this.$setDictionary((e) => {
          e.find((item) => {
            switch (item.name) {
              case "BUILD_STATUS":
                this.build_status_list = item.childs;
                break;
              case "BUILD_CATEGORY":
                this.build_category_list = item.childs;
                this.build_cates = [
                  { value: "0", description: "全部" },
                  ...item.childs,
                ];
              default:
                break;
            }
          });
        });
      });
    },
    changeArea (e) {
      this.areaName = e.district
        ? e.district
        : e.city
          ? e.city
          : e.province
            ? e.province
            : "";
      this.nowTab = 0;
      this.params.region_0 = e.province_id || 0;
      this.params.region_1 = e.city_id || 0;
      this.params.page = 1;
      this.getDataList();
    },
    getDataList () {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.build_list = [];
      }
      // this.$refs.loading.open();
      this.showPannel = false;
      this.$ajax.get("/common/project/list", this.params, (res) => {
        this.load_status = "loadend";
        if (res.statusCode === 200) {
          // this.$refs.loading.close();
          this.build_list = this.build_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
            uni.showToast({
              title: "没有更多数据了",
              icon: "none",
            });
          }
          this.$matchBuildType(
            this.build_list,
            this.build_status_list,
            this.build_category_list
          );
        } else {
          if (this.site_info.city_type == 2) {
            uni.showToast({
              title: "当前定位城市不存在，即将显示全部城市",
              icon: "none",
              success: () => {
                this.setCityData({
                  name: "全部",
                  region_0: 0,
                  region_1: 0,
                  location_name: this.city.name,
                });
                this.params.page = 1;
                this.params.region_0 = 0;
                this.params.region_1 = 0;
                this.getDataList();
              },
            });
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        }
      });
    },
    // 点击tabbar添加参数
    setUrlWebsiteId () {
      let nowlink = window.location.href;
      const reg = /\?.+=.{0,}/;
      if (
        nowlink.indexOf("?website_id=") === -1 &&
        nowlink.indexOf("&website_id=") === -1
      ) {
        if (reg.test(nowlink)) {
          nowlink += `&website_id=${this.$store.state.website_id || 1}`;
        } else {
          nowlink += `?website_id=${this.$store.state.website_id || 1}`;
        }
        history.replaceState(null, " ", nowlink);
      }
    },
    // 关闭遮罩层
    closeMask () {
      this.nowTab = 0;
      this.showPannel = false;
    },
    onBuildDetail (item) {
      this.$navigateTo(`/build/detail?id=${item.id}&buildID=${item.build_id}`);
    },
  },
  onPageScroll (e) {
    this.scrollTop = e.scrollTop;
    if (this.showPannel) {
      uni.pageScrollTo({
        scrollTop: this.scrollTopOffset,
        duration: 0,
      });
    }
  },
  onPullDownRefresh () {
    this.params.page = 1;
    this.getDataList();
    uni.stopPullDownRefresh();
  },
  onReachBottom () {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
  onTabItemTap () {
    this.setUrlWebsiteId();
    uni.$emit("closeChat");
  },
};
</script>

<style scoped lang="scss">
.search {
  padding: 22rpx 48rpx;
  align-items: center;
  justify-content: space-between;
  height: 100rpx;
  background: #fff;
  width: 100%;
  .left {
    font-size: 28rpx;
    color: #333;
    align-items: center;
    text {
      margin-right: 6rpx;
    }
  }
  .right {
    position: relative;
    width: 100%;
    .icon {
      position: absolute;
      top: 14rpx;
      left: 28rpx;
    }
    input {
      font-size: 28rpx;
      padding-left: 96rpx;
      background: #eee;
      height: 64rpx;
      // width: 530rpx;
      border-radius: 32rpx;
    }
  }
  .new-search {
    height: 80rpx;
    width: 100%;
    background: #f3f3f3;
    border-radius: 40rpx;
    align-items: center;
    .left-n {
      padding: 0 30rpx;
      font-size: 28rpx;
      color: #191c2f;
      align-items: center;
      image {
        margin-left: 10rpx;
        width: 14rpx;
        height: 14rpx;
      }
    }
    .line {
      width: 2rpx;
      height: 30rpx;
      background: #d1d1d1;
    }
    .right-n {
      align-items: center;
      padding-left: 20rpx;
      .uni-input {
        margin-left: 10rpx;
      }
    }
  }
}
.build-list {
  padding: 24rpx 48rpx;
  .price-right {
    align-items: center;
    .yong {
      line-height: 40rpx;
      text-align: center;
      width: 40rpx;
      height: 40rpx;
      color: #fff;
      background: #fec923;
      border-radius: 4px;
    }
    .jiage {
      font-size: 24rpx;
      color: #333;
    }
  }
}
.sel-tab {
  box-shadow: 0 0 0 #fff;
  z-index: 99;
  background: #fff;
  border-bottom: 2px solid #eee;
}
.screen-tab {
  position: sticky;
  top: 0;
  box-sizing: border-box;
  align-items: center;
  height: 44px;
  line-height: 44px;
}
.screen-panel {
  position: absolute;
  top: 80rpx;
  display: none;
  line-height: 1;
}
.screen-panel.show {
  display: block;
  left: 0;
}
.screen-tab-item {
  align-items: center;
  flex: 1;
  text-align: center;
  justify-content: center;
  text {
    margin-right: 12rpx;
  }
}

// 列表样式
.more-screen-item {
  padding: 24px 48rpx;
  background: #fff;
}
.options {
  flex-wrap: wrap;
}
.options .options-item {
  width: 22%;
  margin: 1.5%;
  text-align: center;
  padding: 14upx 10upx;
  border: 1upx solid #f3f3f3;
  box-sizing: border-box;
  border-radius: 6upx;
  font-size: $uni-font-size-sm;
  color: #666;
  background-color: #f3f3f3;
}
.options .options-item.active {
  background-color: #fff;
  color: #0174ff;
  border: 1upx solid #0174ff;
}
.options .options-item {
  line-height: 1;
}
.mask {
  position: fixed;
  top: 0;
  bottom: 0;
  width: 100%;
  background-color: rgba($color: #000000, $alpha: 0);
  z-index: -1;
  transition: 0.2s;
}
.mask.show {
  background-color: rgba($color: #000000, $alpha: 0.36);
  z-index: 90;
}
</style>
