<template>
  <view>
    <!-- <mySkeleton v-if="is_show_skeleton" :showAvatar="false" :row="9" :showTitle="true"></mySkeleton> -->
    <view>
      <!-- 背景 -->
      <view class="headerbgc"></view>
      <!-- 主体内容 -->
      <view class="whole">
        <view class="imgs">
          <view>
            <view class="rad">{{ name }}</view>
          </view>
          <view class="text">你好，{{ user.user_name }}</view>
        </view>
        <view style="margin-bottom: 200rpx">
          <block>
            <view
              class="block"
              v-for="(item, index) in indextitleLidst"
              :key="item.id + '_' + index"
            >
              <view class="title">{{ item.name }}</view>
              <messageListwork :workList="item.children"></messageListwork>
            </view>
          </block>
        </view>
      </view>

      <tabber @click="switchTab" :current="currentTabIndex"></tabber>
    </view>
    <!-- <my-popup
        ref="remind"
        :show="showPop"
        height="500px"
        @hide="showPop = false"
      >
        <view class="pop_c">
          <view class="pop_title">
            成员专属码
          </view>
          <view class="pop_img">
            <image :src= 'imgCode' mode="widtFix">

            </image>
          </view>
          <view class="pop_b">
            长按识别进入分享获客通道
          </view>
          <view class="pop_b">
            （授权手机号后分享给客户）
          </view>
        </view>
      </my-popup> -->
  </view>
</template>
<script>
import tabber from "@/customer/components/tabber/tabber";
import messageListwork from "@/customer/components/messageList/messageListwork";
export default {
  components: {
    tabber,
    messageListwork,
  },
  data() {
    return {
      currentTabIndex: 0,
      indextitleLidst: [], // 工作台数据
      workList: [], // 数据
      showPop: false,
      imgCode: "",
      is_show_skeleton: false, // 是否显示骨架屏
      user: {}, // 获取用户消息
      name: "",
    };
  },
  onLoad(options) {
    if (this.$isWxWork() == "wxwork") {
      this.token = uni.getStorageSync("wxwork_token");
      if (!this.token) {
        return;
      }
    } else {;
      let website_id = uni.getStorageSync("website_id");
      console.log(website_id,'website_id');
      
      // let website_id = options.website_id;
      // if(website_id == 0 || website_id== null || website_id == 'undefined'){
      //         alert('该站点不存在')
      //         return
      //       }else{
      //         alert('欢迎进入')
      //       }
      this.token = uni.getStorageSync("token" + website_id);
      if (!this.token) {
        localStorage.setItem("backUrl", location.href);
        location.href = "https://yun.tfcs.cn";
        return;
      }
      // 未登录中断请求
    }
    if (options.website_id) {
      this.website_id =uni.getStorageSync("website_id");
    }
    this.is_show_skeleton = false;
    uni.showLoading({
      title: "加载中",
    });
    this.getUserInfo();
  },
  onShow(){
    if(this.$store.state.needRefreshWhenShow){
      this.$store.commit('setNeedRefreshWhenShow', false);
      this.getUserInfo();
    }
  },
  methods: {
    // 底部tabber栏
    switchTab(index, item) {
      console.log(index, item);
      if (this.currentTabIndex == index) {
        return;
      }
      uni.navigateTo({
        url: item.path,
      });
    },
    // 查询登录用户是否完善资料
    getUserInfo() {
      var that = this;
      this.$ajax.get(
        "/qywx/common/query",
        {},
        (res) => {
          console.log(res, "教师");
          if (res.statusCode === 200) {
            uni.setStorageSync("userInfo", JSON.stringify(res.data));
            uni.hideLoading();
            // 没有联系方式，部门，角色弹出完善资料
            if (
              !res.data.user_name ||
              !res.data.phone ||
              !res.data.wx_work_department_id ||
              (res.data.roles && !res.data.roles.length) ||
              !res.data.roles
            ) {
              this.$ajax.get(
                "/admin/personnelMatters/checkDefaultDepartmentAndRole",
                {},
                (res) => {
                  console.log(res);
                }
              );
              uni.showModal({
                title: "提示",
                content: "请完善资料",
                showCancel: false,
                success: function (res) {
                  if (res.confirm) {
                    that.$navigateTo("/customer/perfect_data");
                  }
                },
              });
            }
            // console.log(111,'');
            this.getUsersInfo();
            this.getInforUser();
          } else {
            uni.hideLoading();
          }
        },
        () => {
          uni.hideLoading();
        }
      );
    },
    // 获取用户信息
    getInforUser() {
      this.user = JSON.parse(uni.getStorageSync("userInfo"));
      console.log(this.user.phone, "我的用户");
      let name = this.user.user_name.split("");
      console.log(name, "名字");
      this.name = name[0];
    },
    //  数据
    getUsersInfo() {
      var that = this;
      this.$ajax.get("/qywx/welcome/census", {}, (res) => {
        this.indextitleLidst = res.data;
        console.log(res.data, "3333");
        uni.hideLoading();
        if (res.statusCode === 200) {
          // uni.showToast({
          //   title: '加载成功',
          //   icon: "none"
          // });
          let arr = res.data;
          arr.map((item) => {
            if (item.id == 1) {
              this.workList = item.children;
              // console.log(this.workList, '客户');
            }
            if (item.id == 2) {
              console.log(item.id, "999");
              this.newList = item.children;
              // console.log(this.newList, '新房');
            }
            if (item.id == 3) {
              this.houseList = item.children; // 房源
            }
            if (item.id == 4) {
              this.outsideList = item.children; // 外呼
            }
            if (item.id == 5) {
              this.peopleList = item.children; // 人事
            }
          });
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    checkMap() {
      this.$navigateTo("/customer/findMap");
      // this.$ajax.get('/admin/map_plugin/share_code', {}, res => {
      //   console.log(res);
      //   if (res.statusCode == 200) {
      //     this.imgCode = res.data
      //     this.showPop = true
      //   }
      // })
    },
  },
};
</script>
<style scoped lang="scss">
page {
  background: #f6f6f6;
}

.headerbgc {
  width: 100%;
  height: 480rpx;
  flex-shrink: 0;
  background: linear-gradient(
    180deg,
    rgba(99, 165, 255, 0.4) 0%,
    rgba(255, 255, 255, 0) 100%
  );
}

.imgs {
  margin-top: 16rpx;
  margin-bottom: 48rpx;
  display: flex;
  flex-direction: row;
  // background-color: aqua;
}
.rad {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: #3172f6;
  text-align: center;
  color: #f6f6f6;
  line-height: 56rpx;
  font-weight: 500;
  font-size: 26rpx;
}
.text {
  color: #292c39;
  font-family: PingFang SC;
  margin-left: 24rpx;
  font-size: 36rpx;
  font-weight: 500;
  line-height: normal;
  letter-spacing: -0.36px;
}

// 整体大盒子
.whole {
  padding: 0 32rpx;
  margin-top: -470rpx;
}
.pop_c {
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 80vw;
  margin: 0 auto;
  padding: 40rpx;
  border-radius: 20rpx;
  .pop_title {
    text-align: center;
    font-size: 32rpx;
  }
  .pop_img {
    width: 520rpx;
    height: 520rpx;
    margin: 30rpx auto;
    image {
      width: 100%;
    }
  }
  .pop_b {
    font-size: 24rpx;
    ~ .pop_b {
      margin-top: 15rpx;
    }
  }
}

.list {
  background: #fff;
  border-radius: 12rpx;
  padding: 24rpx 40rpx;
  .info {
    align-items: center;
    margin-right: 46rpx;
    image {
      width: 96rpx;
      height: 96rpx;
      border-radius: 20rpx;
    }
    view {
      margin-top: 10rpx;
      font-size: 22rpx;
      color: #8a929f;
    }
  }
}
.title {
  color: rgba(41, 44, 57, 0.7);
  font-size: 28rpx;
    margin-bottom: 24rpx;
}
</style>
