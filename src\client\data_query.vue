<template>
  <view class="list">
    <view class="title-bar">
      <view class="search-tab">
        <my-search
          width="287px"
          :placeholder="
            is_select_index == 0 ? '请输入客户手机号码' : '请输入客户姓名'
          "
          @input="onInput"
        >
          <template v-slot:left>
            <picker
              style="font-size:24rpx;color:#0174ff;margin-right:20rpx"
              @change="bindPickerChange"
              @cancel="is_select = false"
              :value="is_select_index"
              :range="is_select_arr"
              range-key="description"
            >
              <view class="uni-input row" @click="is_select = true"
                >{{ is_select_arr[is_select_index].description }}
                <myIcon
                  :type="is_select ? 'xiala' : 'shangla'"
                  size="24rpx"
                  color="#0174ff"
                  style="margin-left:10rpx"
                ></myIcon
              ></view>
            </picker>
            <myIcon type="ic_sousuo3x1" color="#999"></myIcon>
          </template>
          <template v-slot:right>
            <text class="xinzeng" @click="newAdd">消息</text>
            <unibadge
              type="error"
              class="unibadge"
              v-if="parseInt(unread_msg) > 0"
              :text="unread_msg"
            ></unibadge>
          </template>
        </my-search>
        <view class="bottom-line tips row">
          <view class="row bottom-line-left">
            <text class="icon-baobei-ic_guanyu3x1 icon-baobei"></text>
            <text style="margin-left:10rpx"
              >输入客户手机号，查询客户是否存在报备记录</text
            >
          </view>
        </view>
      </view>

      <view class="report-list">
        <my-customer
          v-for="(item, index) in customer_list"
          :key="index"
          :customer_item="item"
          :build_type_list="build_type_list"
          :btn="false"
          :isGet="false"
        ></my-customer>
        <load-more :status="load_status"></load-more>
      </view>
    </view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import mySearch from "@/components/my-search";
import myIcon from "@/components/my-icon";
import myCustomer from "@/components/customer_item";
import loadMore from "@/components/loadMore";
import { mapState } from "vuex";
export default {
  components: {
    mySearch,
    myIcon,
    myCustomer,
    loadMore,
  },
  computed: {
    ...mapState(["unread_msg"]),
  },
  data() {
    return {
      is_select: false, // 点击切换选择搜索内容
      is_select_index: 0,
      is_select_arr: [
        {
          value: 0,
          description: "手机号码",
        },
        {
          value: 1,
          description: "客户姓名",
        },
      ],
      customer_list: [],
      build_type_list: [],
      params: {
        page: 1,
        customer_phone: "",
        customer_name: "",
        is_searcher: 1,
      },
      load_status: "",
    };
  },
  onLoad() {
    this.$setDictionary((e) => {
      e.find((item) => {
        switch (item.name) {
          case "BUILD_CATEGORY":
            this.build_type_list = item.childs;
            break;
        }
      });
    });
  },
  methods: {
    bindPickerChange(e) {
      this.is_select_index = e.detail.value;
      this.is_select = false;
    },
    onInput(e) {
      this.$debounce(this.onInputDebounce, 500)(e);
    },
    newAdd() {
      // this.$navigateTo("/report/report_client?currentTel=1");
      uni.switchTab({
        url: "/index/message",
      });
    },
    onInputDebounce(e) {
      if (!e) {
        // 清空输入内容时清除数据
        this.customer_list = [];
        return;
      }
      this.params.page = 1;
      this.is_select_index === 0
        ? ((this.params.customer_phone = e), (this.params.customer_name = ""))
        : ((this.params.customer_name = e), (this.params.customer_phone = ""));
      this.getDataList();
    },
    getDataList() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.customer_list = [];
      }
      this.$ajax.get(
        "/client/customer/reported/search/project",
        this.params,
        (res) => {
          if (res.statusCode === 200) {
            this.load_status = "loadend";
            // 计算客户总数
            this.customer_list = this.customer_list.concat(res.data.data);
            if (res.data.data.length === 0) {
              this.load_status = "nomore";
              uni.showToast({
                title: "查询客户暂无报备信息",
                icon: "none",
              });
            }
          } else {
            uni.showToast({
              title: res.data.messgae,
              icon: "none",
            });
          }
        }
      );
    },
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>

<style scoped lang="scss">
page {
  background: #eee;
}
.icon-baobei-ic_guanyu3x1 {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22icon-baobei-ic_guanyu3x1%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M512%2056.888889a455.111111%20455.111111%200%201%201%200%20910.222222A455.111111%20455.111111%200%200%201%20512%2056.888889z%20m0%20113.777778a341.333333%20341.333333%200%201%200%200%20682.666666A341.333333%20341.333333%200%200%200%20512%20170.666667z%22%20fill%3D%22%2340465D%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M512%20341.333333m-56.888889%200a56.888889%2056.888889%200%201%200%20113.777778%200%2056.888889%2056.888889%200%201%200-113.777778%200Z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M455.111111%20455.111111h113.777778v284.444445H455.111111z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
}
.bottom-line {
  padding: 12rpx 48rpx;
  background: #fff;
  justify-content: space-between;
  .bottom-line-left {
    align-items: center;
    justify-content: flex-start;
  }
}
.bottom-line::after {
  height: 0;
}
.tips {
  align-items: center;
  color: #0174ff;
}
.title-bar {
  background: #fff;
  .xinzeng {
    position: absolute;
    right: 48rpx;
    color: #0174ff;
  }
  .unibadge {
    position: absolute;
    right: 20rpx;
    top: 10rpx;
  }
}
.search-tab {
  background: #fff;
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 9;
}
.report-list {
  background: #eee;
  margin-top: 140rpx;
}
</style>
