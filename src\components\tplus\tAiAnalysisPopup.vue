<template>
  <view class="ai-analysis-popup" :class="{'prevent-scroll': show}" @touchmove.stop.prevent="disabledScroll">
    <view
      class="popup-box"
      :class="{ show: show }"
      :style="{height: height}"
      @touchmove.stop.prevent="disabledScroll"
    >
      <view class="popup-header" @touchmove.stop.prevent="disabledScroll">
        <view class="title-container">
          <image class="ai-icon" src="https://img.tfcs.cn/backup/static/admin/customer/AI.png"></image>
          <text class="title">智能分析</text>
        </view>
        <view class="close-btn" @click="handleClose">
          <text class="close-icon">×</text>
        </view>
      </view>

      <scroll-view
        scroll-y
        class="popup-content"
        :style="{ height: contentHeight }"
        :show-scrollbar="false"
        @touchmove.stop="handleScroll"
      >
        <!-- 录音质检 -->
        <view class="audio-player" v-if="AIdata.record_url && parseInt(AIdata.duration)>0">
          <tVoicePlayer
            ref="voicePlayer"
            :path="AIdata.record_url"
            :duration="AIdata.duration || 0"
            :name="AIdata.id"
            :healthdata="AIdata.health"
          ></tVoicePlayer>
        </view>

        <!-- 成交意向等级 -->
        <view class="deal-level" :class="dealLevelClass">
          <view class="level-value" :class="dealValueClass">
            {{ extractedLetter }}
          </view>
          <view class="level-info">
            <view class="level-title">{{ AIdata.deal_level && AIdata.deal_level.name }} ：</view>
            <view class="level-desc">{{ AIdata.deal_level && AIdata.deal_level.desc }}</view>
          </view>
        </view>

        <!-- 流失风险等级 -->
        <view class="risk-level" :class="riskLevelClass">
          <view class="level-value" :class="riskValueClass">
            {{ AIdata.loss_level && AIdata.loss_level.value }}
          </view>
          <view class="level-info">
            <view class="level-title">{{ AIdata.loss_level && AIdata.loss_level.name }} ：</view>
            <view class="level-desc">{{ AIdata.loss_level && AIdata.loss_level.desc }}</view>
          </view>
        </view>

        <!-- 通话摘要 -->
        <view class="section">
          <view class="section-header">
            <view class="dot"></view>
            <view class="section-title">{{ AIdata.call && AIdata.call.name }}</view>
          </view>
          <view class="section-content">
            {{ AIdata.call && AIdata.call.value }}
          </view>
        </view>

        <!-- 客户需求等级 -->
        <view class="demand-level" v-if="AIdata.demand_level && AIdata.demand_level.desc">
          <view class="demand-header">
            {{ AIdata.demand_level && AIdata.demand_level.name }}
            <view class="demand-tag" :class="AIdata.demand_level && getDemandTagClass">
              {{ AIdata.demand_level && AIdata.demand_level.value }}
            </view>
          </view>
          <view class="section-content">
            {{ AIdata.demand_level && AIdata.demand_level.desc }}
          </view>
        </view>

        <!-- 客户标签 -->
        <view class="section">
          <view class="section-header">
            <view class="dot"></view>
            <view class="section-title">{{ AIdata.label && AIdata.label.name }}</view>
          </view>
          <view class="tags-container">
            <view class="tag" v-for="(label, index) in labelValues" :key="index">
              {{ label }}
            </view>
          </view>
        </view>

        <!-- 关键词 -->
        <view class="section">
          <view class="section-header">
            <view class="dot"></view>
            <view class="section-title">{{ AIdata.keywords && AIdata.keywords.name }}</view>
          </view>
          <view class="tags-container">
            <view class="tag tag-blue" v-for="(keyword, index) in keywordsValues" :key="index">
              {{ keyword }}
            </view>
          </view>
        </view>

        <!-- 客户画像 -->
        <view class="section">
          <view class="section-header">
            <view class="dot"></view>
            <view class="section-title">{{ AIdata.portrait && AIdata.portrait.name }}</view>
          </view>
          <view class="portrait-container">
            <block v-if="AIdata.portrait && AIdata.portrait.value">
              <view v-for="(value, key) in AIdata.portrait.value" :key="key" class="portrait-item">
                <text class="portrait-key">{{ key }}:</text>
                <text class="portrait-value">{{ value }}</text>
              </view>
            </block>
          </view>
        </view>

        <view class="ai-footer">
          以上内容由AI大模型智能生成
        </view>
      </scroll-view>

      <!-- 底部固定按钮 -->
      <view class="popup-footer">
        <view class="footer-tip">
          <text class="tip-icon">!</text>
          <text class="tip-text">一键采纳更新到客户资料</text>
        </view>
        <view class="adopt-btn" @click="handleAdopt" :class="{'loading-btn': loading}">
          <text v-if="loading" class="loading-text">提交中...</text>
          <text v-else>一键采纳</text>
        </view>
      </view>
    </view>

    <view
      class="mask"
      :class="{ show: show }"
      @click="handleClose"
      @touchmove.stop.prevent="disabledScroll"
    ></view>
  </view>
</template>

<script>
import tVoicePlayer from './tVoicePlayer.vue';

export default {
  name: 'tAiAnalysisPopup',
  components: {
    tVoicePlayer
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    height: {
      type: String,
      default: '80vh'
    },
    callData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      AIdata: {},
      loading: false
    };
  },
  computed: {
    extractedLetter() {
      return this.AIdata.deal_level && this.AIdata.deal_level.value
        ? this.AIdata.deal_level.value.match(/[A-Za-z]/)?.[0] || ''
        : '';
    },
    labelValues() {
      return this.AIdata.label && this.AIdata.label.value
        ? this.AIdata.label.value.split(',')
        : [];
    },
    keywordsValues() {
      if (!this.AIdata.keywords || !this.AIdata.keywords.value) return [];

      // 处理多种可能的分隔符：逗号、顿号、空格等
      const value = this.AIdata.keywords.value;
      return value.split(/[,，、\s]+/).filter(item => item.trim() !== '');
    },
    getDemandTagClass() {
      if (!this.AIdata.demand_level || !this.AIdata.demand_level.value) return '';

      const value = this.AIdata.demand_level.value;
      if (value === '有效') return 'tag-success';
      if (value === '无效') return 'tag-gray';
      return 'tag-warning';
    },
    contentHeight() {
      // 计算内容区域高度
      return 'calc(' + this.height + ' - 120rpx)';
    },
    dealLevelClass() {
      if (this.extractedLetter === 'A') return 'level-a';
      if (this.extractedLetter === 'B') return 'level-b';
      if (this.extractedLetter === 'C') return 'level-c';
      if (this.extractedLetter === 'D') return 'level-d';
      return '';
    },
    dealValueClass() {
      if (this.extractedLetter === 'A') return 'value-a';
      if (this.extractedLetter === 'B') return 'value-b';
      if (this.extractedLetter === 'C') return 'value-c';
      if (this.extractedLetter === 'D') return 'value-d';
      return '';
    },
    riskLevelClass() {
      if (!this.AIdata.loss_level) return '';
      const value = this.AIdata.loss_level.value;
      if (value === '高') return 'level-a';
      if (value === '中') return 'level-b';
      if (value === '低') return 'level-c';
      return '';
    },
    riskValueClass() {
      if (!this.AIdata.loss_level) return '';
      const value = this.AIdata.loss_level.value;
      if (value === '高') return 'value-a';
      if (value === '中') return 'value-b';
      if (value === '低') return 'value-c';
      return '';
    }
  },
  watch: {
    show(val) {
      if (val) {
        // 每次显示弹窗时，重新获取数据
        if (this.callData.call_record_id) {
          this.getData();
        }

        // 禁用页面滚动
        this.preventPageScroll();
      } else {
        // 当弹窗关闭时，确保录音停止播放
        if (this.$refs.voicePlayer) {
          this.$refs.voicePlayer.stop();
        }

        // 恢复页面滚动
        this.restorePageScroll();
      }
    },
    callData(val) {
      if (this.show && val.call_record_id) {
        this.getData();
      }
    }
  },
  methods: {
    disabledScroll() {
      // 阻止滑动穿透
      return false;
    },
    stopMove() {
      // 保留原方法，但使用 disabledScroll 实现
      return this.disabledScroll();
    },
    handleScroll(e) {
      // 处理滚动事件，防止滚动穿透
      e.stopPropagation();
    },
    handleClose() {
      // 如果录音正在播放，停止播放并重置进度
      if (this.$refs.voicePlayer) {
        this.$refs.voicePlayer.stop();
      }

      // 关闭弹窗
      this.$emit('update:show', false);
      this.$emit('close');

      // 清空数据，避免下次打开时显示旧数据
      this.AIdata = {};
    },

    // 禁用页面滚动
    preventPageScroll() {
      // 通过事件通知父组件禁用滚动
      this.$emit('prevent-scroll', true);
    },

    // 恢复页面滚动
    restorePageScroll() {
      // 通过事件通知父组件恢复滚动
      this.$emit('prevent-scroll', false);
    },
    getData() {
      // 先清空旧数据，确保每次都是最新数据
      this.AIdata = {};

      // 使用传入的分析数据
      if (this.callData.analysisData) {
        this.AIdata = {
          ...this.callData,
          ...this.callData.analysisData
        };
        console.log("this.callData");
        console.log(this.callData);
        console.log("this.AIdata");
        console.log(this.AIdata);
      } else {
        // 确保AIdata至少有基本结构，防止undefined错误
        this.AIdata = {
          ...this.callData,
          deal_level: { value: '', name: '成交意向等级', desc: '' },
          loss_level: { value: '', name: '流失风险等级', desc: '' },
          call: { name: '通话摘要', value: '' },
          demand_level: { value: '', name: '客户需求等级', desc: '' },
          label: { name: '客户标签', value: '' },
          keywords: { name: '关键词', value: '' },
          portrait: { name: '客户画像', value: {} },
          health: {},
          record_url: '',
          duration: 0
        };
      }

      // 确保如果有录音播放器实例，重置其状态
      this.$nextTick(() => {
        if (this.$refs.voicePlayer) {
          // 确保播放器重置状态
          this.$refs.voicePlayer.playCurrentTime = 0;
          this.$refs.voicePlayer.playedToEnd = false;
          this.$refs.voicePlayer.playing = false;
        }
      });
    },

    handleAdopt() {
      // 一键采纳功能实现
      uni.showModal({
        title: '提示',
        content: '此操作将根据AI助理分析的客户等级、意向标签、客户画像，智能覆盖原有的客户资料, 是否继续?',
        confirmText: '确定',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.loading = true;
            // 调用接口提交数据
            this.$ajax.get(`/admin/call_phone/cover_client`, { call_record_id: this.callData.call_record_id }, (res) => {
              this.loading = false;
              if (res.statusCode === 200) {
                uni.showToast({
                  title: '采纳成功！',
                  icon: 'success'
                });

                // 通知父组件刷新数据
                // 触发父组件中的刷新方法
                uni.$emit("refreshCustomerData");

                // 关闭弹窗
                this.handleClose();
              } else {
                uni.showToast({
                  title: res.data?.message || '操作失败',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-analysis-popup {
  position: relative;
  z-index: 999;

  /* 当弹窗显示时，防止滚动穿透 */
  &.prevent-scroll {
    touch-action: none; /* 禁止所有触摸操作 */
    overscroll-behavior: contain; /* 防止滚动穿透 */
  }

  .popup-box {
    position: fixed;
    left: 0;
    width: 100%;
    bottom: 0;
    background-color: #fff;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    transform: translateY(100%);
    transition: transform 0.3s;
    z-index: 999;
    display: flex;
    flex-direction: column; /* 保持列布局，因为内部元素需要垂直排列 */

    &.show {
      transform: translateY(0);
    }
  }

  .popup-header {
    display: flex;
    flex-direction: row; /* 强制行布局 */
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f5f5f5;

    .title-container {
      display: flex;
      flex-direction: row; /* 强制行布局 */
      align-items: center;

      .ai-icon {
        width: 40rpx;
        height: 40rpx;
        margin-right: 10rpx;
        flex-shrink: 0; /* 防止缩小 */
      }

      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .close-btn {
      width: 50rpx;
      height: 50rpx;
      display: flex;
      flex-direction: row; /* 强制行布局 */
      justify-content: center;
      align-items: center;
      flex-shrink: 0; /* 防止缩小 */

      .close-icon {
        font-size: 40rpx;
        color: #999;
      }
    }
  }

  .popup-content {
    padding: 0 30rpx 100rpx 30rpx;
    box-sizing: border-box;
    overflow-y: auto;
    /* 防止滚动穿透 */
    overscroll-behavior: contain;
    touch-action: pan-y; /* 只允许垂直方向的触摸操作 */
    /* 隐藏滚动条 */
    ::-webkit-scrollbar {
      display: none;
      width: 0;
      height: 0;
      color: transparent;
      -webkit-appearance: none;
      background: transparent;
    }
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .audio-player {
    margin: 30rpx 0;
  }

  .deal-level, .risk-level {
    display: flex;
    flex-direction: row; /* 强制行布局 */
    border-radius: 16rpx;
    padding: 20rpx;
    margin-bottom: 30rpx;
    align-items: center; /* 确保子元素垂直居中 */

    .level-value {
      width: 80rpx;
      height: 80rpx;
      display: flex;
      justify-content: center;
      align-items: center; /* 确保垂直居中 */
      border-radius: 16rpx;
      margin-right: 20rpx;
      font-size: 50rpx;
      color: white;
      font-weight: bold;
      flex-shrink: 0; /* 防止缩小 */
      line-height: 1; /* 确保文字垂直居中 */
    }

    .level-info {
      flex: 1;
      display: flex;
      flex-direction: column; /* 内部保持列布局 */

      .level-title {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 10rpx;
      }

      .level-desc {
        font-size: 26rpx;
        color: #666;
        line-height: 1.5;
        word-wrap: break-word;
        word-break: break-all;
        white-space: normal;
      }
    }
  }

  .level-a {
    background: linear-gradient(to right, rgba(254, 228, 229, 1), white);
  }

  .level-b {
    background: linear-gradient(to right, #FFE7CF, white);
  }

  .level-c {
    background: linear-gradient(to right, #5cda464d, white);
  }

  .level-d {
    background: linear-gradient(to right, #F2F2F2, #FFFFFF);
  }

  .value-a {
    background: linear-gradient(to left, #f13131, #f131313d);
  }

  .value-b {
    background: linear-gradient(to left, #FF7D00, #FFB166);
  }

  .value-c {
    background: linear-gradient(to left, #36bc1ef7, #2fed425c);
  }

  .value-d {
    background: #C9CDD4;
  }

  .section {
    margin-bottom: 30rpx;
    display: flex;
    flex-direction: column; /* 保持列布局 */

    .section-header {
      display: flex;
      flex-direction: row; /* 强制行布局 */
      align-items: center;
      margin-bottom: 15rpx;

      .dot {
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
        background-color: #409EFF;
        margin-right: 15rpx;
        flex-shrink: 0; /* 防止缩小 */
      }

      .section-title {
        font-size: 28rpx;
        color: #333;
      }
    }

    .section-content {
      font-size: 26rpx;
      color: #666;
      line-height: 1.5;
      padding-left: 35rpx;
      word-wrap: break-word;
      word-break: break-all;
      white-space: normal;
    }
  }

  .demand-level {
    background: #f7f8fa;
    border-radius: 16rpx;
    padding: 20rpx;
    margin-bottom: 30rpx;
    display: flex;
    flex-direction: column; /* 改为列布局，避免文字溢出 */

    .demand-header {
      display: flex;
      flex-direction: row; /* 强制行布局 */
      align-items: center;
      font-size: 28rpx;
      color: #333;
      margin-bottom: 15rpx;

      .demand-tag {
        margin-left: 15rpx;
        padding: 4rpx 12rpx;
        border-radius: 8rpx;
        font-size: 22rpx;
      }

      .tag-success {
        background-color: #f0f9eb;
        color: #67c23a;
      }

      .tag-warning {
        background-color: #fdf6ec;
        color: #e6a23c;
      }

      .tag-gray {
        background-color: #f4f4f5;
        color: #909399;
      }
    }
  }

  .tags-container {
    display: flex;
    flex-direction: row; /* 强制行布局 */
    flex-wrap: wrap; /* 允许标签换行 */
    padding-left: 35rpx;
    width: 100%; /* 确保容器占满整个宽度 */
    box-sizing: border-box;

    .tag {
      background-color: #fdf6ec;
      color: #e6a23c;
      padding: 6rpx 16rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
      margin-right: 15rpx;
      margin-bottom: 15rpx;
      flex-shrink: 0; /* 防止缩小 */
      display: inline-block;
    }

    .tag-blue {
      background-color: #e8f1ff;
      color: #2f6aff;
      display: inline-block;
      padding: 0 16rpx;
      height: 48rpx;
      line-height: 48rpx;
      border-radius: 8rpx;
      margin-right: 16rpx;
      margin-bottom: 16rpx;
      position: relative;
      overflow: hidden;
      word-break: break-all; /* 允许在任意字符间断行 */
      white-space: normal; /* 正常换行 */
      max-width: 100%; /* 确保不超出容器 */
    }
  }

  .portrait-container {
    padding-left: 35rpx;
    display: flex;
    flex-direction: column; /* 保持列布局 */

    .portrait-item {
      font-size: 26rpx;
      margin-bottom: 15rpx;
      display: flex;
      flex-direction: row; /* 强制行布局 */
      flex-wrap: wrap; /* 允许换行 */

      .portrait-key {
        color: #333;
        margin-right: 10rpx;
      }

      .portrait-value {
        color: #666;
        word-wrap: break-word;
        word-break: break-all;
        white-space: normal;
        flex: 1;
      }
    }
  }

  .ai-footer {
    text-align: center;
    font-size: 24rpx;
    color: #999;
    margin: 30rpx 0 150rpx;
  }

  .popup-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    flex-direction: row; /* 强制行布局 */
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    background-color: #fff;
    border-top: 1rpx solid #f5f5f5;
    box-sizing: border-box;
    z-index: 1000; /* 确保在最上层 */

    .footer-tip {
      display: flex;
      flex-direction: row; /* 强制行布局 */
      align-items: center;

      .tip-icon {
        width: 30rpx;
        height: 30rpx;
        line-height: 30rpx;
        text-align: center;
        background-color: #f56c6c;
        color: #fff;
        border-radius: 50%;
        font-size: 22rpx;
        margin-right: 10rpx;
        flex-shrink: 0; /* 防止缩小 */
      }

      .tip-text {
        font-size: 24rpx;
        color: #999;
        white-space: nowrap; /* 防止文字换行 */
        overflow: hidden;
        text-overflow: ellipsis; /* 超出显示省略号 */
      }
    }

    .adopt-btn {
      background-color: #409eff;
      color: #fff;
      padding: 15rpx 30rpx;
      border-radius: 8rpx;
      font-size: 28rpx;
      flex-shrink: 0; /* 防止缩小 */
      display: flex;
      justify-content: center;
      align-items: center;

      &.loading-btn {
        background-color: #a0cfff;
      }

      .loading-text {
        display: inline-block;
        position: relative;

        &:after {
          content: '';
          position: absolute;
          right: -20rpx;
          top: 50%;
          transform: translateY(-50%);
          width: 10rpx;
          height: 10rpx;
          border-radius: 50%;
          background-color: #fff;
          animation: loading-dot 1.5s infinite;
        }
      }

      @keyframes loading-dot {
        0%, 100% {
          opacity: 0;
        }
        50% {
          opacity: 1;
        }
      }
    }
  }

  .mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s;

    &.show {
      opacity: 1;
      visibility: visible;
    }
  }
}
</style>
