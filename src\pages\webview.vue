<template>
	<web-view :src="url" :webview-styles="webviewStyles"></web-view>
</template>

<script>
export default {
    props: {
        webviewStyles: {type: Object, default: { progress: { color: '#f65354' } }},
    },
    data() {
        return {
            url: ''
        }
    },
    onLoad(options){
        let url = options.url ? decodeURIComponent(options.url) : '';
        url.startsWith('https://') || (url = 'https://yun.tfcs.cn/fenxiao/' + url.replace(/^\/(.*)$/, '$1'));

        
        this.url = url + (url.includes('?') ? '&' : '?') +this.$Utils.buildHttpQuery({
            website_id: uni.getStorageSync("website_id"),
            headerFrom: 'weixinxiaochengxuCrm',
            token: uni.getStorageSync("wxwork_token")
        })
        console.log(this.url);
    },
    methods: {
      
    }
}
</script>
