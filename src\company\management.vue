<template>
  <view class="list">
    <view class="list_back">
      <view class="company_name">{{ company_info.name }}</view>
      <view class="company_card">
        <view class="card_title">本月佣金金额</view>
        <view class="money">{{
          statistics_info.brokerage_amount > 0
            ? statistics_info.brokerage_amount + "元"
            : "当月暂无佣金信息"
        }}</view>
        <view class="top-line row">
          <view class="box row">
            <view class="top">{{ statistics_info.employee_total }}</view>
            <view class="bottom">员工数</view>
          </view>
          <view class="box row">
            <view class="top">{{ statistics_info.customer_total }}</view>
            <view class="bottom">客户数</view>
          </view>
          <view class="box row">
            <view class="top">{{ statistics_info.visit_total }}</view>
            <view class="bottom">到访量</view>
          </view>
          <view class="box row">
            <view class="top">{{ statistics_info.deal_total }}</view>
            <view class="bottom">成交数</view>
          </view>
        </view>
      </view>
    </view>
    <view class="menu row">
      <view
        v-for="item in click_category"
        :key="item.id"
        class="image-box row"
        @click="onEmployee(company_info.id, item.id)"
        ><image :src="item.icon" mode="aspectFill"></image>
        <view class="menu_list">{{ item.desc }}</view></view
      >
    </view>
    <view class="companyReported">
      <view class="title">
        报备详情
      </view>
      <view class="title-bar">
        <view class="search-tab">
          <my-search
            style="margin:0;width:654rpx"
            :placeholder="
              is_select_index == 0 ? '请输入客户手机号码' : '请输入客户姓名'
            "
            @input="onInput"
          >
            <template v-slot:left>
              <picker
                style="font-size:24rpx;color:#0174ff;margin-right:20rpx"
                @change="bindPickerChange"
                @cancel="is_select = false"
                :value="is_select_index"
                :range="is_select_arr"
                range-key="description"
              >
                <view class="uni-input row" @click="is_select = true"
                  >{{ is_select_arr[is_select_index].description }}
                  <myIcon
                    :type="is_select ? 'xiala' : 'shangla'"
                    size="24rpx"
                    color="#0174ff"
                    style="margin-left:10rpx"
                  ></myIcon
                ></view>
              </picker>
            </template>
          </my-search>
        </view>
        <view class="time-box row">
          <view class="input-box row">
            <input
              id="start"
              class="input"
              type="text"
              v-model="deal_at_date_start"
              placeholder="开始时间"
              @click="$refs.picker_start.show()"
            />
            <text>至</text>
            <input
              id="end"
              class="input"
              type="text"
              disabled="true"
              v-model="deal_at_date_end"
              placeholder="结束时间"
              @click="$refs.picker_end.show()"
            />
          </view>
          <view class="search" @click="searchTime">
            搜索
          </view>
        </view>
        <tab-bar
          :tabs="report_cates"
          :nowIndex="
            report_cates.findIndex((item) => parseInt(item.value) == data_type)
          "
          :fixedTop="false"
          @click="onClickCate"
        ></tab-bar>
      </view>
      <view class="reportList" v-for="item in reported_list" :key="item.id">
        <view class="title-top row">
          <text class="left">{{ item.customer_name }}</text>
          <text class="right">{{ item.customer_phone }}</text>
        </view>
        <view class="">
          <view class="ctn-box row">
            <view class="label">报备楼盘</view>
            <view class="content">{{ item.build_name }}</view>
          </view>
          <view class="ctn-box row">
            <view class="label">客户性别</view>
            <view class="content">{{ item.customer_sex | formatSex }}</view>
          </view>
          <view class="ctn-box row">
            <view class="label">报备会员</view>
            <view class="content"
              ><text style="line-height:60rpx"
                >{{ item.u_name || item.u_nickname }} {{ item.u_phone }}</text
              ></view
            >
          </view>
          <view class="ctn-box row">
            <view class="label">报备时间</view>
            <view class="content">{{ item.created_at }}</view>
          </view>
          <view class="ctn-box row">
            <view class="label">最后更新</view>
            <view class="content">{{ item.updated_at }}</view>
          </view>
          <view class="ctn-box row">
            <view class="label">报备状态</view>
            <view
              class="content txt"
              :class="{
                report: item.status == 0,
                visite: item.status == 1,
                subscribe: item.status == 2,
                isBuy: item.status == 3,
                isbuy4: item.status == 4,
                isDeal: item.status == 5,
                failure: item.status == 10,
              }"
              >{{ changeStr(item.status) }}
            </view>
          </view>
        </view>
      </view>
    </view>
    <load-more :status="load_status"></load-more>
    <gmyFloatTouch></gmyFloatTouch>
    <!-- 开始 -->
    <timePicker
      end="2030-12-30"
      mode="YMD"
      ref="picker_start"
      @confirm="confirmTimeStart"
    ></timePicker>
    <!-- 结束 -->
    <timePicker
      end="2030-12-30"
      mode="YMD"
      ref="picker_end"
      @confirm="confirmTimeEnd"
    ></timePicker>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore";
import mySearch from "@/components/my-search";
import timePicker from "@/components/time-picker";
import myIcon from "@/components/my-icon";
import tabBar from "@/components/tabBar.vue";
export default {
  components: { loadMore, mySearch, myIcon, timePicker, tabBar },
  data() {
    return {
      statistics_info: {},
      click_category: [
        {
          id: 1,
          desc: "员工管理",
          icon: "../static/company/yuangong.png",
          path: "/company/employee_list",
        },
        {
          id: 2,
          desc: "公司统计",
          icon: "../static/company/gongsi.png",
          path: "/company/company_tables?type=company",
        },
        {
          id: 3,
          desc: "业绩排行",
          icon: "../static/company/yeji.png",
          path: "/company/quarterly_statistics",
        },
        {
          id: 4,
          desc: "客户分析",
          icon: "../static/company/kehu.png",
          path: "/company/company_tables?type=reported",
        },
        {
          id: 5,
          desc: "收入管理",
          icon: "../static/company/shouru.png",
          path: "/company/company_tables?type=reported",
        },
        {
          id: 6,
          desc: "楼盘分析",
          icon: "../static/company/fenxi.png",
          path: "/company/build_data",
        },
      ],
      reported_list: [],
      params: {
        page: 1,
        customer_phone: "",
        customer_name: "",
        company_id: "",
        status: "-1",
      },
      load_status: "",
      load_text: [],
      company_info: {},
      company_id: "",
      is_select: false, // 点击切换选择搜索内容
      is_select_index: 0,
      is_select_arr: [
        {
          value: 0,
          description: "手机号码",
        },
        {
          value: 1,
          description: "客户姓名",
        },
      ],
      deal_at_date_start: "",
      deal_at_date_end: this.$getTime("YMD"),
      get_url: `/client/customer/reported/search/broker`,
      report_cates: [],
      data_type: "-1",
    };
  },
  computed: {},
  onLoad(options) {
    this.getCates();
    this.company_id = options.id;
    this.params.company_id = options.id;
    this.getCompanyInfo(options.id);
  },
  methods: {
    getCates() {
      this.$getDictionaryList("REPORTED_STATUS", {}, (res) => {
        if (res.statusCode === 200) {
          this.report_cates = [
            { value: -1, description: "全部" },
            ...res.data.data,
          ];
        }
      });
    },
    onEmployee(company_id, id) {
      var actions = {
        1: [`/company/employee_list?company_id=${company_id}`],
        2: [`/company/company_tables?type=company&company_id=${company_id}`],
        3: [`/company/quarterly_statistics?company_id=${company_id}`],
        4: [`/company/company_tables?type=reported&company_id=${company_id}`],
        5: [`/company/income_management?company_id=${company_id}`],
        6: [`/company/build_data?company_id=${company_id}`],
      };
      this.checkStatus(id, actions);
    },
    checkStatus(status, actions) {
      let action = actions[status];
      this.$navigateTo(action[0]);
    },
    getStatisticsInfo(id) {
      this.$ajax.get(
        `/client/company/statistics/home?company_id=${id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.statistics_info = res.data;
            this.getDataInfo();
          } else {
            uni.showToast({
              title: res.data.message || "获取数据失败",
              icon: "none",
            });
          }
        }
      );
    },
    getDataInfo() {
      this.load_status = "loading";
      if (this.params.status == -1) {
        delete this.params.status;
      }
      if (this.params.page === 1) {
        this.reported_list = [];
      }
      this.$ajax.get(this.get_url, this.params, (res) => {
        if (res.statusCode === 200) {
          this.load_status = "loadend";
          this.reported_list = this.reported_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        } else {
          this.load_status = "loadend";
          uni.showToast({
            title: res.data.message || "获取失败",
            icon: "none",
          });
        }
      });
    },
    changeStr(e) {
      let txt = "";
      this.load_text.find((item) => {
        if (parseInt(item.value) === e) {
          txt = item.description;
        }
      });
      return txt;
      // let load_text = {
      //   0: "待审核",
      //   1: "已报备",
      //   2: "已到访",
      //   3: "已认筹",
      //   4: "已认购",
      //   5: "已成交",
      //   10: "已无效",
      // };
      // return load_text[e];
    },
    bindPickerChange(e) {
      this.is_select_index = e.detail.value;
      this.is_select = false;
    },
    onInput(e) {
      this.$debounce(this.onInputDebounce, 500)(e);
    },
    onInputDebounce(e) {
      this.params.page = 1;
      this.is_select_index === 0
        ? ((this.params.customer_phone = e), (this.params.customer_name = ""))
        : ((this.params.customer_name = e), (this.params.customer_phone = ""));
      this.getDataInfo();
    },
    getCompanyInfo(id) {
      this.$ajax.get(`/client/company/query/${id}`, {}, (res) => {
        if (res.statusCode === 200) {
          this.company_info = res.data;
          this.getStatisticsInfo(id);
          this.$getDictionaryList("REPORTED_STATUS", {}, (res) => {
            if (res.statusCode === 200) {
              this.load_text = res.data.data;
            }
          });
        } else {
          uni.showToast({
            title: res.data.message || "获取数据失败",
            icon: "none",
          });
        }
      });
    },
    // 选择开始时间
    confirmTimeStart(e) {
      this.deal_at_date_start = e.result;
    },
    //选择结束时间
    confirmTimeEnd(e) {
      this.deal_at_date_end = e.result;
    },
    searchTime() {
      if (!this.deal_at_date_start) {
        uni.showToast({
          title: "请选择开始日期",
          icon: "none",
        });
        return;
      }
      if (this.deal_at_date_start && this.deal_at_date_end) {
        this.get_url = `/client/customer/reported/search/broker?updated_date[start]=${this.deal_at_date_start}&updated_date[end]=${this.deal_at_date_end}`;
      }
      this.params.page = 1;
      this.getDataInfo();
    },
    onClickCate(e) {
      this.params.page = 1;
      this.params.status = e.value;
      this.data_type = parseInt(e.value);
      this.getDataInfo();
    },
  },
  onPullDownRefresh() {
    this.getStatisticsInfo(this.company_id);
    uni.stopPullDownRefresh();
  },
  onReachBottom() {
    if (this.load_status === "nomore" || this.load_status === "loading") {
      return;
    }
    this.params.page++;
    this.getStatisticsInfo(this.company_id);
  },
};
</script>

<style scoped lang="scss">
.title-bar {
  position: sticky;
  top: 0;
  margin: 0 -48rpx;
  background: #fff;
  .search-tab {
    padding: 0 48rpx;
  }
}
.list_back {
  background: #5371f3;
  padding: 0 24rpx;
  height: 500rpx;
  .company_name {
    color: #fff;
    font-size: 32rpx;
    margin: 50rpx auto;
  }
  .company_card {
    height: 300rpx;
    background: #fff;
    border-radius: 10rpx;
    padding: 24rpx;
    .money {
      margin: 24rpx 0;
      color: #5371f3;
      font-size: 40rpx;
    }
  }
  .top-line {
    justify-content: space-around;
    padding: 24rpx;
    .box {
      flex-direction: column;
      align-items: center;
      .top {
        font-size: 32rpx;
        font-weight: bold;
      }
      .bottom {
        margin-top: 24rpx;
        font-size: 28rpx;
        color: #999;
      }
    }
  }
}
.menu {
  padding: 48rpx;
  justify-content: flex-start;
  flex-wrap: wrap;
  .image-box {
    flex-direction: column;
    align-items: center;
    width: 25%;
    image {
      width: 96rpx;
      height: 96rpx;
    }
    .menu_list {
      font-size: 24rpx;
      height: 45rpx;
      width: 100rpx;
      align-items: center;
      color: #333;
      margin-top: 10rpx;
    }
  }
}
.companyReported {
  margin: 0 48rpx;
  .title {
    font-size: 32rpx;
    margin-bottom: 24rpx;
  }
  .reportList {
    padding: 48rpx 24rpx 30rpx;
    width: 100%;
    background: #fff;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.04);
    margin: 40rpx 0;
    .title-top {
      font-size: 32rpx;
      margin-bottom: 30rpx;
      .left {
        margin-right: 10rpx;
      }
    }
    .ctn-box {
      font-size: 28rpx;
      line-height: 44rpx;
      color: #999;
      align-items: center;
      .label {
        margin-right: 30rpx;
      }
      .content {
        text {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .txt {
        &.report {
          color: #ff0000;
        }
        &.visite {
          color: #638ff9;
        }
        &.subscribe {
          color: #39becd;
        }
        &.isBuy {
          color: #ff8062;
        }
        &.isBuy4 {
          color: #ffa53a;
        }
        &.isDeal {
          color: #33be85;
        }
        &.failure {
          color: #808080;
        }
      }
    }
  }
}

.time-box {
  background: #fff;
  line-height: 100rpx;
  height: 100rpx;
  padding: 0 48rpx;
  .input-box {
    width: 250px;
    margin: 20rpx 0;
    border-radius: 4px;
    align-items: center;
    background-color: #eee;
    padding: 0 16rpx;
    input {
      font-size: 28rpx;
      text-align: center;
      background-color: #eee;
      border: 1rpx solid #f3f3f3;
      height: 32rpx;
      width: 284rpx;
      border: none;
    }
  }
  .search {
    margin-left: 40rpx;
    color: #0174ff;
  }
}
</style>
