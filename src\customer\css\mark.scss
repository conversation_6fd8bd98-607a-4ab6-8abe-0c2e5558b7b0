.top {
  margin: 24rpx;
  padding: 0 24rpx 24rpx 24rpx;
  background: #fff;
  border-radius: 12rpx;
  .tag {
    flex-wrap: wrap;
    text {
      background: #f1f4fa;
      border-radius: 12px;
      margin-right: 24rpx;
      font-size: 22rpx;
      padding: 12rpx 36rpx;
      margin-top: 24rpx;
    }
  }
  .tag_input {
    margin-top: 24rpx;
    justify-content: space-between;
    border: 2rpx solid #dde1e9;
    border-radius: 8rpx;
    align-items: center;
    padding: 18rpx 24rpx;
    input {
      width: 100%;
      font-size: 28rpx;
    }
  }
  .type {
    text {
      margin-top: 24rpx;
    }
  }
  .content {
    margin-top: 24rpx;
    > text {
      line-height: 40rpx;
    }
  }
  .tit {
    margin: 24rpx 0;
    font-size: 32rpx;
    font-weight: 500;
  }
  .pers {
    align-items: center;
    .level {
      width: 32rpx;
      height: 32rpx;
      line-height: 32rpx;
      text-align: center;
      color: #fff;
      font-weight: 500;
      font-size: 22rpx;
      background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
      border-radius: 4rpx;
    }
    .name {
      font-weight: 500;
      font-size: 32rpx;
    }
    text {
      margin-left: 24rpx;
    }
    .pic {
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
    }
    .qw {
      width: 32rpx;
      margin-left: 24rpx;
    }
  }
}
page {
  background: #f6f6f6;
  color: #2e3c4e;
}
.c2 {
  color: #8a929f;
}
