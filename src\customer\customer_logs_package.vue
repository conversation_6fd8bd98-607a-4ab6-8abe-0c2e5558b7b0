<template>
  <view class="logs">
    <view class="log_item" v-for="item in logList" :key="item.id" @click="toDetail(item)">
      <logItem :item="item"></logItem>
    </view>
    <load-more :status="load_status"></load-more>
  </view>
</template>

<script>
import logItem from "@/customer/components/logItem"
import loadMore from "@/components/loadMore";
export default {
  components: {
    logItem,
    loadMore
  },
  data () {
    return {
      logList: [],
      params: {
        page: 1,
        per_page: 10,
        package_id: ""
      },
      load_status: ''
    }
  },
  onLoad (options) {
    if (options.id) {
      this.params.package_id = options.id
      this.getData()
    }
  },
  methods: {
    getData () {
      if (this.params.page == 1) {
        this.logList = []
      }
      this.load_status = "loading"
      this.$ajax.get("/admin/map_plugin/package_users", this.params, res => {
        console.log(res);
        if (res.statusCode == 200) {
          this.logList = this.logList.concat(res.data.data)
          if (res.data.data.length < this.params.per_page) {
            this.load_status = "nomore"
          } else {
            this.load_status = 'more'
          }

        } else {
          this.load_status = 'nomore'
        }
      })
    },
    toDetail (item) {
      this.$navigateTo(`/customer/detail?id=${item.client_id}&form=2&source=2`)
    }
  }
}
</script>

<style lang ="scss" scoped>
.logs {
  padding: 24rpx 48rpx;
  min-height: 100vh;
  background: #f8f8f8;
  box-sizing: border-box;
  .log_item {
    background: #fff;
    padding: 24rpx;
    margin-bottom: 24rpx;
    border-radius: 15rpx;
  }
}
</style>