<template>
  <view class="chat-list">
    <view class="chat" v-for="(item, index) in chat_list" :key="index">
      <!-- 对方 -->
      <view class="send-time" v-if="isNewMinute(transdate(item.updated_at))">
        {{ getTimer(item.updated_at) }}
      </view>
      <view class="to-user pad-btm" v-if="user_info.id !== item.from_user_id">
        <view class="left row">
          <view class="img-box">
            <image mode="aspectFill" :src="item.to_headimage"></image>
          </view>
          <!-- 
            v-if="JSON.parse(item.content).type === 'apply_wx'"
           -->
          <view
            class="apply-wx "
            v-if="JSON.parse(item.content).type === 'apply_wx'"
          >
            对方申请查看您的微信是否
            <text class="apply-wx-text" @click="agreeSendWx(item)">同意</text>
          </view>
          <!-- 同意分享wx -->
          <view
            class="agree-box"
            v-else-if="JSON.parse(item.content).type === 'agreeSendWx'"
          >
            <view class="top row">
              <view class="top-left"
                ><image src="https://img.tfcs.cn/static/img/wx.png"></image
              ></view>
              <view class="top-right">
                <view class="t-r">{{
                  JSON.parse(item.content).content.wechatName + "的微信号"
                }}</view>
                <view class="t-b">{{
                  JSON.parse(item.content).content.wechatNumber
                }}</view>
              </view>
            </view>
            <view class="bottom top-line row ">
              <view class="b-left " @click="copyWechat(item.content)"
                >复制微信号</view
              >
              <view class="b-left r-left" @click="viewQrcode(item.content)"
                >查看二维码</view
              >
            </view>
          </view>
          <view
            class="img-box-send-build"
            v-else-if="
              JSON.parse(item.content).type === 'issue' &&
                JSON.parse(item.content).category === 1
            "
            @click="
              toBuild(item)
            "
          >
            <image
              mode="aspectFill"
              :src="JSON.parse(item.content).content.build_img"
            ></image>
            <view class="send-build-content">
              <view class="build-name">{{
                JSON.parse(item.content).content.build_name
              }}</view>
              <view class="build-address">{{
                JSON.parse(item.content).content.build_address
              }}</view>
            </view>
          </view>
          <view
            class="img-box-send-build"
            v-else-if="
              JSON.parse(item.content).type === 'issue' &&
              JSON.parse(item.content).category == 1000
            "
            @click="toERP(item.content)"
          >
            <image
              mode="aspectFill"
              :src="JSON.parse(item.content).content.build_img"
            ></image>
            <view class="send-build-content">
              <view class="build-name">{{
                JSON.parse(item.content).content.build_name
              }}</view>
              <view class="build-address">{{
                JSON.parse(item.content).content.build_address
              }}</view>
            </view>
          </view>
          <view
            class="content t-content"
            v-else-if="
              JSON.parse(item.content).type === 'issue' &&
                JSON.parse(item.content).category === 2
            "
          >
            <text>
              客户姓名：{{ JSON.parse(item.content).content.customer_name }} \n
              联系方式：{{ JSON.parse(item.content).content.customer_phone }} \n
              报备项目：{{ JSON.parse(item.content).content.build_name }} \n
            </text>
          </view>
          <view
            class="content t-content voice row"
            v-else-if="JSON.parse(item.content).type === 'voice'"
            @click="
              $emit('playvoice', {
                content: JSON.parse(item.content),
                index: index,
              })
            "
          >
            <text>{{ formatVoice(JSON.parse(item.content)) }}''</text>
            <image
              class="play_vioce_icon"
              :src="
                index === paly_voice
                  ? '../static/voice/play_voice_black.gif'
                  : '../static/voice/voice_icon_black.png'
              "
            ></image>
          </view>
          <view
            class="img-box-send"
            v-else-if="JSON.parse(item.content).type === 'img'"
          >
            <image
              @click="viewQrcode(item.content)"
              mode="aspectFill"
              :src="JSON.parse(item.content).content"
            ></image>
          </view>
          <view v-else class="content t-content">
            <view v-html="fomatText(item, user_info)"> </view>
            <text
              @click="viewLocation(item.content)"
              v-if="JSON.parse(item.content).type === 'map'"
              style="color:#ff656b"
              >[查看位置]</text
            >
          </view>
        </view>
      </view>
      <view class="from-user pad-btm" v-if="user_info.id === item.from_user_id">
        <view class="left row">
          <view
            class="apply-wx"
            v-if="JSON.parse(item.content).type === 'apply_wx'"
            v-html="fomatText(item, user_info)"
          ></view>
          <view
            class="apply-wx"
            v-else-if="JSON.parse(item.content).type === 'agreeSendWx'"
            >您已将微信号发送给对方</view
          >
          <view
            class="img-box-send"
            v-else-if="JSON.parse(item.content).type === 'img'"
          >
            <image
              @click="viewQrcode(item.content)"
              mode="aspectFill"
              :src="JSON.parse(item.content).content"
            ></image>
          </view>
          <view
            class="img-box-send-build"
            v-else-if="
              JSON.parse(item.content).type === 'issue' &&
                JSON.parse(item.content).category === 1
            "
            @click="
              toBuild(item)
            "
          >
            <image
              mode="aspectFill"
              :src="JSON.parse(item.content).content.build_img"
            ></image>
            <view class="send-build-content">
              <view class="build-name">{{
                JSON.parse(item.content).content.build_name
              }}</view>
              <view class="build-address">{{
                JSON.parse(item.content).content.build_address
              }}</view>
            </view>
          </view>
          <view
            class="content f-content voice row"
            v-else-if="JSON.parse(item.content).type === 'voice'"
            @click="
              $emit('playvoice', {
                content: JSON.parse(item.content),
                index: index,
              })
            "
          >
            <text>{{ formatVoice(JSON.parse(item.content)) }}''</text>
            <image
              class="play_vioce_icon"
              :src="
                index === paly_voice
                  ? '../static/voice/play_voice.gif'
                  : '../static/voice/voice_icon.png'
              "
            ></image>
          </view>
          <view
            class="content f-content"
            v-else-if="
              JSON.parse(item.content).type === 'issue' &&
                JSON.parse(item.content).category === 2
            "
          >
            <text>
              客户姓名：{{ JSON.parse(item.content).content.customer_name }} \n
              联系方式：{{ JSON.parse(item.content).content.customer_phone }} \n
              报备项目：{{ JSON.parse(item.content).content.build_name }} \n
            </text>
          </view>
          <view
            class="img-box-send-build"
            v-else-if="
              JSON.parse(item.content).type === 'issue' &&
              JSON.parse(item.content).category == 1000
            "
            @click="toERP(item.content)"
          >
            <image
              mode="aspectFill"
              :src="JSON.parse(item.content).content.build_img"
            ></image>
            <view class="send-build-content">
              <view class="build-name">{{
                JSON.parse(item.content).content.build_name
              }}</view>
              <view class="build-address">{{
                JSON.parse(item.content).content.build_address
              }}</view>
            </view>
          </view>
          <view v-else class="content f-content">
            <view v-html="fomatText(item, user_info)"> </view>
            <text
              @click="viewLocation(item.content)"
              v-if="JSON.parse(item.content).type === 'map'"
              style="color:#ff656b"
              >[查看位置]</text
            >
          </view>
          <view class="img-box">
            <image mode="aspectFill" :src="item.from_headimage"></image>
          </view>
        </view>
      </view>
    </view>
    <view  class ="disn" @click ="toMap"></view>
  </view>
</template>

<script>
export default {
  props: {
    chat_list: Array,
    user_info: Object,
    paly_voice: Number,
  },
  data() {
    return {
      face_list: [
        "微笑",
        "大笑",
        "笑哭",
        "开心",
        "呲牙",
        "坏笑",
        "欣慰",
        "鄙视",
        "白眼",
        "飞吻",
        "鬼脸",
        "酷",
        "爱财",
        "调皮",
        "惊讶",
        "无表情",
        "思考",
        "亲亲",
        "喜欢",
        "低沉",
        "怒",
        "生气",
        "超爱",
        "大哭",
        "小声",
        "惊恐",
        "爱心",
        "心碎",
        "偷看",
        "OK",
        "耶",
        "大拇指",
        "握拳",
        "强壮",
      ],
    };
  },
  methods: {
    fomatText(item, user_info) {
      let send_msg = JSON.parse(item.content);
      switch (send_msg.type) {
        case "text":
          return this.formatFace(send_msg.content);
          break;
        case "map":
          return send_msg.address;
          break;
        case "apply_wx":
          return "您已申请查看对方微信，请等待对方同意";
          break;
      }
    },
    // 处理表情
    formatFace(val) {
      if (typeof val !== "string") {
        return val;
      }
      if (!val) {
        return " ";
      }
      const regex = new RegExp(/\[(.+?)\]/, "gi");
      let html = val.replace(regex, (item, face_name, index) => {
        let face_index = this.face_list.indexOf(face_name);
        if (face_index > -1) {
          return `<img width="20" style="position:relative;top:5px;margin-left:3px" src="https://img.tfcs.cn/static/img/face${face_index}.png" />`;
        } else {
          return item;
        }
      });
      return html;
    },
    formatVoice(content) {
      return parseInt(content.duration / 1000);
    },
    toBuild(item){
      if(this.$store.state.configInfo.mode == 3){
        this.$navigateTo(
                `/build/single_detail?buildID=${
                  JSON.parse(item.content).content.build_id
                }`
              )
              return 
      }
      this.$navigateTo(
                `/build/detail?buildID=${
                  JSON.parse(item.content).content.build_id
                }`
              )
    },

    toERP(content){
      let id =`${JSON.parse(content).content.build_id}`
      let url =this.$store.state.configInfo.mode == 3?`/fenxiao/ershou/single_detail?id=${id}`:`/fenxiao/ershou/detail?id=${id}`
      if (this.$store.state.configInfo.version>1){
        this.$navigateTo(url.replace("/fenxiao/","/build/"))
      }else {
        this.$navigateTo(url)
      }
    },
    // 查看位置
    viewLocation(content) {
      let send_msg = JSON.parse(content);
      uni.openLocation({
        name: send_msg.name,
        address: send_msg.address,
        latitude: parseFloat(send_msg.lat),
        longitude: parseFloat(send_msg.lng),
      });
    },
    // 同意发送微信
    agreeSendWx(item) {
      this.$emit("agreeSendWx", item);
    },
    copyWechat(content) {
      let msg = JSON.parse(content);
      uni.setClipboardData({
        data: msg.content.wechatNumber,
        success: (res) => {
          uni.showToast({
            title: "复制成功",
            icon: "none",
          });
        },
      });
    },
    viewQrcode(content) {
      let msg = JSON.parse(content);
      this.$previewImage(msg.content.wechatQrcode || msg.content);
    },

    //日期转时间戳
    transdate(time) {
      var date = new Date();
      date.setFullYear(time.substring(0, 4));
      date.setMonth(time.substring(5, 7) - 1);
      date.setDate(time.substring(8, 10));
      date.setHours(time.substring(11, 13));
      date.setMinutes(time.substring(14, 16));
      date.setSeconds(time.substring(17, 19));
      return Date.parse(date) / 1000;
    },
    // 时间转换
    getTimer(stringTime) {
      var minute = 1000 * 60;
      var hour = minute * 60;
      var day = hour * 24;
      var week = day * 7;
      var month = day * 30;
      var time1 = new Date().getTime(); //当前的时间戳
      var time2 = Date.parse(new Date(stringTime)); //指定时间的时间戳
      var time = time1 - time2;
      var result = null;
      if (time < 0) {
        uni.showToast({
          title: "设置的时间不能早于当前时间！",
          icon: "none",
        });
      } else if (time / month >= 1) {
        result = parseInt(time / month) + "月前";
      } else if (time / week >= 1) {
        result = parseInt(time / week) + "周前";
      } else if (time / day >= 1) {
        result = parseInt(time / day) + "天前";
      } else if (time / hour >= 1) {
        result = parseInt(time / hour) + "小时前";
      } else if (time / minute >= 1) {
        result = parseInt(time / minute) + "分钟前";
      } else {
        let time = new Date();
        let msg_time =
          (time.getHours() < 10 ? "0" + time.getHours() : time.getHours()) +
          ":" +
          (time.getMinutes() < 10
            ? "0" + time.getMinutes()
            : time.getMinutes());
        result = msg_time;
      }
      return result;
    },
    toMap(){
      this.$navigateTo("/collarMap/index")
    },
    //判断时间是否是一分钟之内
    isNewMinute(timestamp) {
      const date = new Date(timestamp * 1000);
      const isNew = timestamp * 1000 - this.latestMinute >= 60000;
      this.latestMinute = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        date.getHours(),
        date.getMinutes()
      );
      return isNew;
    },
  },
};
</script>

<style scoped lang="scss">
.chat-list {
  padding: 48rpx;
  .send-time {
    margin: 20rpx 0;
    color: #999;
    font-size: 24rpx;
    text-align: center;
  }
  .content {
    background: #fff;
    border-radius: 8rpx;
    padding: 28rpx;
    margin-left: 32rpx;
    position: relative;
    line-height: 1.5;
    max-width: 500rpx;
    text-align: justify;
    text-justify: newspaper;
    word-break: break-all;
  }
  .img-box {
    width: 80rpx;
    height: 80rpx;
    image {
      width: 80rpx;
      height: 80rpx;
      display: inline-block;
      border-radius: 50%;
    }
  }
  .pad-btm {
    margin-bottom: 60rpx;
  }
  .to-user {
    .t-content {
      margin-left: 110rpx;
    }
    .t-content::before {
      color: #fff;
      content: "";
      width: 0px;
      height: 0px;
      border-top: 10rpx solid transparent;
      border-bottom: 10rpx solid transparent;
      border-right: 10px solid #fff;
      position: absolute;
      left: -20rpx;
    }
    .img-box {
      position: absolute;
      left: 0;
      top: 0;
    }
  }
  .left {
    align-items: center;
    position: relative;
  }
  .apply-wx {
    color: #666;
    margin: 0 32rpx;
    align-items: center;
    margin: 24rpx 110rpx 0;
    text-align: center;
    line-height: 1.5;
    .apply-wx-text {
      margin-top: 20rpx;
      width: 100%;
      color: #2e8cef;
      text-align: center;
    }
  }
  .from-user {
    align-items: flex-end;
    .f-content {
      color: #fff;
      background: #2e8cef;
      margin-right: 110rpx;
    }
    .f-content::before {
      content: "";
      width: 0px;
      height: 0px;
      border-top: 10rpx solid transparent;
      border-bottom: 10rpx solid transparent;
      border-left: 10px solid #2e8cef;
      position: absolute;
      right: -20rpx;
    }
    .img-box {
      position: absolute;
      right: 0;
      top: 0;
    }
  }
  .agree-box {
    background: #fff;
    margin-left: 32rpx;
    width: 400rpx;
    border-radius: 24rpx;
    margin: 0 110rpx;
    .top {
      align-items: center;
      padding: 24rpx;
      .top-left {
        image {
          width: 80rpx;
          height: 80rpx;
          border-radius: 8rpx;
        }
      }
      .top-right {
        line-height: 44rpx;
        font-size: 28rpx;
        margin-left: 24rpx;
        .t-r {
          color: #999;
        }
        .t-b {
          font-weight: 500rpx;
        }
      }
    }
    .bottom {
      align-items: center;
      justify-content: space-around;
      padding: 24rpx;
      .r-left {
        color: #2e8cef;
      }
    }
  }
  .img-box-send {
    margin: 0 32rpx;
    width: 400rpx;
    height: 288rpx;
    margin: 0 110rpx;
    image {
      width: 100%;
      border-radius: 24rpx;
    }
  }
  .voice {
    display: flex;
    align-items: center;
  }
  .play_vioce_icon {
    width: 40rpx;
    height: 40rpx;
  }
  .img-box-send-build {
    margin: 0 32rpx;
    width: 400rpx;
    height: 500rpx;
    margin: 0 110rpx;
    background: #fff;
    padding: 20rpx;
    border-radius: 20rpx;
    image {
      width: 100%;
      border-radius: 24rpx;
    }
    .send-build-content {
      margin-top: 30rpx;
      .build-name {
        margin-bottom: 20rpx;
        font-size: 32rpx;
      }
      .build-address {
        font-size: 24rpx;
      }
    }
  }
}
.disn {
  position: absolute;
  width: 0;
  height: 0;
  top: -1001%;
  right: -1000%;
}
</style>
