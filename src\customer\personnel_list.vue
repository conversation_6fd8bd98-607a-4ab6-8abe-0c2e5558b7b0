<template>
  <view class="personnel">
    <view class="search row">
      <myIcon
        class="icon"
        type="ic_sousuo3x1"
        color="#D1D1D1"
        size="24px"
      ></myIcon>
      <input
        class="uni-input"
        :disabled="true"
        type="text"
        placeholder="请输入标题关键字"
        placeholder-style="font-size:14px"
      />
    </view>
    <view class="list">
      <view class="item row" v-for="item in tableData" :key="item.id">
        <image class="avatar" :src="item.img"></image>
        <view class="right">
          <view class="topname row">
            <view class="lname">{{ item.name }}</view>
            <view class="position">{{ item.position }}</view>
          </view>
          <view class="bottom row">
            <view class="b-c row">
              <image
                src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/mobile.png"
              ></image>
              <text>{{ item.mobile }}</text> </view
            ><view class="b-c row">
              <image
                src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/emil.png"
              ></image>
              <text>{{ item.emil }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <image
      src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/kslk.png"
      class="fixedimg"
    ></image>
  </view>
</template>

<script>
import myIcon from "@/components/my-icon.vue";
export default {
  components: {
    myIcon,
  },
  data() {
    return {
      corp_name: "腾房科技",
      tableData: [
        {
          id: 1,
          name: "研发部",
          number: 10,
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png",
          position: "前端开发",
          mobile: "19012341234",
          emil: "<EMAIL>",
        },
        {
          id: 2,
          name: "研发部",
          number: 10,
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png",
          position: "前端开发",
          mobile: "19012341234",
          emil: "<EMAIL>",
        },
        {
          id: 3,
          name: "研发部",
          number: 10,
          img:
            "https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/nan2.png",
          position: "前端开发",
          mobile: "19012341234",
          emil: "<EMAIL>",
        },
      ],
    };
  },
};
</script>

<style scoped lang="scss">
.personnel {
  padding: 0 24px;
}
.search {
  padding: 8px 12px;
  align-items: center;
  background: #f8f8f8;
  border-radius: 22px;
  .uni-input {
    margin-left: 12px;
  }
}
.list {
  margin-top: 12px;
}
.item {
  margin-bottom: 20px;
  .avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
  }
  .right {
    border-bottom: 1px solid #dde1e9;
    flex: 1;
    margin-left: 14px;
    .topname {
      margin-bottom: 11px;
      align-items: center;
      .lname {
        font-size: 16px;
        color: #2e3c4e;
      }
      .position {
        margin-left: 12px;
        font-size: 11px;
        color: #8a929f;
      }
    }
    .bottom {
      justify-content: space-between;
      .b-c {
        align-items: center;
        margin-bottom: 12px;
        text {
          color: #8a929f;
          font-size: 11px;
        }
        image {
          width: 16px;
          height: 16px;
        }
      }
    }
  }
}
.fixedimg {
  width: 54px;
  height: 54px;
  position: fixed;
  right: 24px;
  bottom: 45%;
}
</style>
