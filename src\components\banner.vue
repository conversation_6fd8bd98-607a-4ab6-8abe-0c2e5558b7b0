<template>
  <view class="banner">
    <myIcon
      v-if="customIcon"
      class="iconfont"
      type="ic_vr3x"
      color="#fff"
    ></myIcon>
    <view class="banner-box" :style="{ height: height + 'px' }">
      <swiper
        :indicator-dots="indicatorDots"
        :indicator-color="indicatorColor"
        :indicator-active-color="indicatorActiveColor"
        autoplay
        class="swiper"
        @animationfinish="onAnimationfinish"
      >
        <swiper-item
          v-for="(item, index) in focus"
          :key="index"
          @click="previewImg(item, index)"
        >
          <view class="swiper-item">
            <image
              :class="{ img_bor: radius == true }"
              mode="aspectFill"
              :src="item.img | imageFilter('w_6401')"
              :style="{ height: height + 'px' }"
            ></image>
          </view>
        </swiper-item>
      </swiper>
      <text class="indicato" v-if="customIndicator && focus.length > 0"
        >{{ current }}/{{ focus.length }}</text
      >
    </view>
    <slot name="bottom" />
  </view>
</template>

<script>
import myIcon from "@/components/color-icon";
export default {
  components: { myIcon },
  props: {
    customIndicator: {
      type: Boolean,
      default: true,
    },
    customIcon: {
      type: Boolean,
      default: true,
    },
    focus: Array,
    indicatorDots: {
      type: Boolean,
      default: false,
    },
    indicatorColor: {
      type: String,
      default: "#fff",
    },
    indicatorActiveColor: {
      type: String,
      default: "#0174ff",
    },
    // 圆角
    radius: {
      type: Boolean,
      default: false,
    },
    height: {
      type: [String, Number],
      default: "210",
    },
  },
  data() {
    return {
      current: 1,
    };
  },
  onLoad() {},
  methods: {
    onAnimationfinish(e) {
      this.current = e.detail.current + 1;
    },
    previewImg(item, index) {
      if (item.link_url) {
        this.$navigateTo(item.link_url);
      } else {
        // this.previewImage(item.img, index);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
page {
}
.banner {
  position: relative;
  .iconfont {
    border-radius: 50%;
    z-index: 10;
    position: absolute;
    width: 120rpx;
    height: 120rpx;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.5;
    background: #000000;
  }
  .banner-box {
    height: 420rpx;
    width: 100%;
    uni-swiper {
      height: 100% !important;
    }
    .swiper-item image {
      height: 420rpx;
      width: 100%;
      &.img_bor {
        border-radius: 24rpx;
      }
    }

    .indicato {
      position: absolute;
      right: 20rpx;
      bottom: 24rpx;
      height: 30rxp;
      line-height: 44rpx;
      padding: 0 20rpx;
      border-radius: 22rpx;
      font-size: $uni-font-size-sm;
      background-color: rgba($color: #000000, $alpha: 0.5);
      color: #fff;
    }
  }

  // // 导航
  // .nav-house {
  //   position: absolute;
  //   left: 50%;
  //   bottom: 24rpx;
  //   transform: translate(-50%);
  //   width: auto;
  //   align-items: center;
  //   height: 48rpx;
  //   background: #fff;
  //   justify-content: space-evenly;
  //   border-radius: 6rpx;
  //   .nav-item-house {
  //     height: 100%;
  //     width: 80rpx;
  //     padding: 10rpx;
  //     text-align: center;
  //     font-size: 22rpx;
  //     color: #000;
  //     &.active {
  //       color: #fff;
  //       background: #0174ff;
  //     }
  //     &:first-child {
  //       border-radius: 3px 0 0 3px;
  //     }
  //     &:last-child {
  //       border-radius: 0 3px 3px 0;
  //     }
  //   }
  // }
}
</style>
