<template>
  <view class="shadow-img">
    <view
      class="img colorfulshadow"
      :style="{
        width: img_width,
        height: img_height,
        background: 'url(' + img_background + ')',
      }"
    ></view>
  </view>
</template>

<script>
export default {
  props: {
    img_width: {
      // 设置图片宽、高、地址等样式
      type: String,
      default: "200rpx",
    },
    img_height: {
      type: String,
      default: "200rpx",
    },
    img_background: {
      type: String,
      default: "",
    },
  },
};
</script>

<style scoped lang="scss">
.img {
  height: 200rpx;
  background-repeat: no-repeat;
  background-size: contain;
}
.colorfulshadow {
  position: relative;
}
.colorfulshadow::after {
  content: "";
  width: 100%;
  height: 100%;
  position: absolute;
  background: inherit;
  background-position: center center;
  filter: drop-shadow(0px 0px 10rpx rgba(0, 0, 0, 0.5)) blur(20rpx);
  z-index: -1;
}
</style>
