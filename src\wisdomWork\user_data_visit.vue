<template>
    <view>
        <view class="top">
            <view class="filters">
                <view class="filters-item">
                    <wisdomSearchSelect placeholder="日期" v-model="params.date_value" :datas="dateGroups" :params.sync="params" auto-popup @confirm="search" @clear="search"></wisdomSearchSelect>
                </view>
                <view class="filters-item">
                    <wisdomMemberSelect placeholder="成员" v-model="params.admin_id" @select="search"></wisdomMemberSelect>
                </view>
                <view class="filters-item">
                    <wisdomSearchSelect placeholder="排序" v-model="params.sort" :datas="sortGroups" :params.sync="params" auto-popup @confirm="search" @clear="search"></wisdomSearchSelect>
                </view>
            </view>
        </view>

        <wisdomTable :api="getList" :headers="headers" ref="table"></wisdomTable>
    </view>
</template>

<script>
import wisdomSearchSelect from './components/wisdomSearchSelect';
import wisdomMemberSelect from './components/wisdomMemberSelect';
import wisdomTable from './components/wisdomTable';
import { getVisitUserData } from '@/common/utils/wisdom-work.js';
export default {
    components: {
        wisdomSearchSelect,
        wisdomMemberSelect,
        wisdomTable
    },
    data(){
        return {
            dateGroups: [{
                title: ' ', field: 'date_value', options: [
                    { label: '全部', value: '' },
                    { label: '今天', value: 'today' },
                    { label: '昨天', value: 'yestoday' },
                    { label: '本周', value: 'now_week' },
                    { label: '上周', value: 'last_week' },
                    { label: '本月', value: 'now_month' },
                    { label: '上月', value: 'last_month' },
                ]
            }],
            sortGroups: [{
                title: ' ', field: 'sort', options: [
                    { label: '回访量', value: 'follow_up_num' },
                    { label: '外呼接通降序', value: 'on_call_num' },
                    { label: '外呼未接通降序', value: 'un_call_num' },
                ]   
            }],
            params: {
                date_value: '',
                start_date: '',
                end_date: '',
                admin_id: '',
                sort: ''
            },
            headers: [
                { label: '姓名', field: 'user_name', width: 130, fixed: true },
                { label: '回访量', field: 'follow_up_num', width: 110 },
                { label: '外呼接通量', field: 'on_call_num', width: 110 },
                { label: '外呼未接通量', field: 'un_call_num', width: 110 },
                { label: '最近回访内容', field: 'last_hf_content', width: 160, align: 'left', showOverflowPopup: true },
                { label: '最近回访时间', field: 'last_hf_date', width: 160 },
                { label: '最后一次跟进时间', field: 'last_follow_date', width: 160 },
            ],
            list: [],
        }
    },
    onLoad(options){
        this.params.date_value = options.date || '';
    },
    methods: {
        async getList(page){
            const [start_date, end_date ] = this.$Utils.getDateRange(this.params.date_value, 'YYYY-MM-DD');
            const params = { ...this.params, start_date, end_date, page };
            delete params.date_value;
            const res = await getVisitUserData(params);
            return {
                list: (res.data || []),
                pageSize: res.per_page || 0
            }
        },
        async search(){
            await this.$refs.table.search();
        }
    },
    async onPullDownRefresh(){
		await this.search();
		uni.stopPullDownRefresh();
	},
	onReachBottom () {
		this.$refs.table.getList();
	},
}
</script>

<style lang="scss" scoped>
@import "@/common/style/wisdom_work/wisdom_work_top_filters.scss";
</style>