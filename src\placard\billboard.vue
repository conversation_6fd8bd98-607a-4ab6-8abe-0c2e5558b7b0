<template>
    <view>
        <view class="cephalosome">
            <text class="Event_Launch">活动首推TOP100</text>
            <text class="ranking">用户总排名</text>
        </view>
        <view class="List_body">
            <view class="restrain">
                <text>清盘特惠 购房送10万元居....</text>
                <image src="./photo/Dropdown Arrow.png"></image>
            </view>
        </view>
        <view class="List_ranking">
            <view class="Ranking_List">
                <view class="User_Information">
                    <image src="./photo/accessit.png"></image>
                    <view class="User_profile"></view>
                    <text>Sain李</text>
                </view>
                <view class="User_Ranking">
                    <text>90</text>
                </view>
            </view>
            <view class="Ranking_List">
                <view class="User_Information">
                    <image src="./photo/third.png"></image>
                    <view class="User_profile"></view>
                    <text>张小三</text>
                </view>
                <view class="User_Ranking">
                    <text>89</text>
                </view>
            </view>
            <view class="Ranking_List">
                <view class="User_Information">
                    <image src="./photo/first.png"></image>
                    <view class="User_profile"></view>
                    <text>三小凤</text>
                </view>
                <view class="User_Ranking">
                    <text>78</text>
                </view>
            </view>
            <view class="Ranking_List">
                <view class="User_Information">
                    <view class="numeral">
                        <text class="numeral_num">4</text>
                    </view>
                    <view class="User_profile"></view>
                    <text>李四</text>
                </view>
                <view class="User_Ranking">
                    <text>65</text>
                </view>
            </view>
            <view class="Ranking_List">
                <view class="User_Information">
                    <view class="numeral">
                        <text class="numeral_num">5</text>
                    </view>
                    <view class="User_profile"></view>
                    <text>成一风景</text>
                </view>
                <view class="User_Ranking">
                    <text>45</text>
                </view>
            </view>
            <view class="Ranking_List">
                <view class="User_Information">
                    <view class="numeral">
                        <text class="numeral_num">6</text>
                    </view>
                    <view class="User_profile"></view>
                    <text>A张1233</text>
                </view>
                <view class="User_Ranking">
                    <text>33</text>
                </view>
            </view>
            <view class="Ranking_List">
                <view class="User_Information">
                    <view class="numeral">
                        <text class="numeral_num">7</text>
                    </view>
                    <view class="User_profile"></view>
                    <text>六丑、、</text>
                </view>
                <view class="User_Ranking">
                    <text>22</text>
                </view>
            </view>
            <view class="Ranking_List">
                <view class="User_Information">
                    <view class="numeral">
                        <text class="numeral_num">8</text>
                    </view>
                    <view class="User_profile"></view>
                    <text>小孩子</text>
                </view>
                <view class="User_Ranking">
                    <text>11</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {

        }
    },
}
</script>

<style scoped lang="scss">
.cephalosome {
    width: 100%;
    height: 300rpx;
    background-image: url(./photo/List_Background.png);
    background-repeat: no-repeat;
    background-size: cover;

    text {
        margin-left: 50rpx;
    }

    .Event_Launch {
        margin-top: 90rpx;
        font-size: 24px;
        color: #ffffff;
        font-weight: 500;
    }

    .ranking {
        margin-top: 30rpx;
        color: #ffffff;
    }
}

.List_body {
    width: 100%;
    height: 150rpx;
    background-color: #fff;
    margin-top: -40rpx;
    border-radius: 20px 20px 0px 0px;

    .restrain {
        width: 90%;
        height: 70rpx;
        background: #F5F6F8;
        border-radius: 5px;
        margin: 30rpx auto;
        display: flex;
        flex-direction: row;
        padding: 20rpx 30rpx;
        justify-content: space-between;

        text {
            font-size: 12px;
        }

        image {
            width: 30rpx;
            height: 22rpx;
        }
    }
}

.List_ranking {
    width: 90%;
    height: 1000rpx;
    // background-color: palegreen;
    margin: 0 auto;

    .Ranking_List {
        width: 100%;
        height: 100rpx;
        // background-color: paleturquoise;
        margin-top: 30rpx;
        display: flex;
        flex-direction: row;

        .User_Information {
            width: 60%;
            height: 90rpx;
            // background-color: pink;
            display: flex;
            flex-direction: row;

            image {
                width: 70rpx;
                height: 70rpx;
                margin-top: 5rpx;
            }

            .User_profile {
                width: 80rpx;
                height: 80rpx;
                background-color: palegreen;
                border-radius: 50%;
                margin-left: 20rpx;
            }

            text {
                margin-top: 20rpx;
                margin-left: 30rpx;
                font-size: 16px;
                font-weight: 400;
            }
        }

        .User_Ranking {
            width: 40%;
            height: 90rpx;
            // background-color: peru;
            display: flex;
            flex-direction: row-reverse;

            text {
                font-size: 16px;
                margin-top: 20rpx;
                font-weight: 400;
            }
        }

        .numeral {
            width: 70rpx;
            height: 70rpx;
            background-image: url(./photo/Secondary.png);
            background-repeat: no-repeat;
            .numeral_num{
                margin-left: 28rpx;
                font-size: 11px;
                color: #ffdca6;
            }
        }
    }
}</style>