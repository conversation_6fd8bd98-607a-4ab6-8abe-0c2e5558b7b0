<template>
<view>
    <tNotice :visible.sync="dialogs.notice">已找到 {{count}} 个客户</tNotice>
<view class="list">

 
    <view 
        v-for="(item,index) in list" 
        :key="item.id" 
        class="list-item" 
        :class="{
                 'brief': isListBrief, 
                 'remove-animation': item._isRemoving,
                 'remove-animation-top': item._isRemovingTop,
                 'update-animation': item._isUpdating,
                 'update-border': item._isUpdatingBorder
                 }" 
        @click="goDetailPage(item)" 
        @longpress="handleLongPress(item,index)"
        >
    
        <image :src="'/icons/top-mark.png' | imgDomain" class="mark-top" v-if="(isMy || isTrans) && item.order == 1"/>
        <view class="header">
            <view class="left">
                <headImg :customer="item" :useWord="isHeadBrief"/>
            </view>
            <view class="right">
                <view class="info">
                    <text class="cname">{{item.cname}}</text>
                    <view class="tag">
                        <image src="@/static/customer/qw.png" mode="widthFix" class="qw-icon" v-if="item.wxqy_id"/>
                        <image v-if="!isHeadBrief && (item.sex==1 || item.sex==2)" :src="item.sex == 2 ? '/static/admin/customer/nv3.png': '/static/admin/customer/nan.png' | imgDomain" mode="widthFix" class="sex-icon"/>
                        <image v-if="isHeadBrief && item.level && item.level.title" :src="item.level.title | levelIcon | imgDomain" mode="widthFix" class="level-icon"/>
                        <view class="label" v-if="item.public2_status == 1">已转公</view>
                        <view class="label" v-else-if="item.public2_status == 2">已掉公</view>
                    </view>
                </view>
                <view class="tags">
                    <view class="tag-item" v-if="item.tracking && item.tracking.title">{{item.tracking.title}}</view>
                    <view class="tag-item tag-red" v-if="item.is_has_share_follow == 1">共享</view>
                    <view class="tag-item tag-grey" v-if="item.source && item.source.title">{{item.source.title}}</view>
                    <view class="tag-item tag-grey" v-if="item.client_type && item.client_type.title">{{item.client_type.title}}</view>
                </view>
            </view>
            <view class="btn-claim" :class="{disabled:!isClaimAbled(item)}" v-if="claimAbled">认领</view>
        </view>
        <view class="content" v-if="!isListBrief">
            <view class="row">
                <view class="row-label">手机号码</view>
                <view class="row-cont">
                    <view class="tel-cont">
                        <template v-if="item.mobile">
                            <view class="mobile" :class="{
                                    dot: item.last_call_follow && item.last_call_follow.client_id > 0,
                                    dotGreen: item.last_call_follow && item.last_call_follow.call_status > 0}">
                                <view v-if="item.last_call_follow && item.last_call_follow.content" @click.stop="toggleCallFollowContent(item)">
                                    {{ item.mobile | hideMobileNumber(isTrans) }}
                                </view>
                                <view v-else>
                                    {{ item.mobile | hideMobileNumber(isTrans) }}
                                </view>
                            </view>
                            <view class="mobile-place" v-if="item.mobile_place">{{item.mobile_place}}</view>
                            <view class="btn-mobile-place" @click.stop="queryMobilePlace(item, index)" v-else>归属地查询</view>
                            <view class="btn-mobile" v-if="item.follow_id > 0" @click.stop="handleSeeTel(item, index)">
                                <image mode="widthFix" src="@/static/customer/tel.png" class="mobile-icon"/>
                            </view>
                        </template>
                        <template v-else>无</template>
                    </view>
                    <view class="desc" v-if="item._showCallFollowContent">
                        {{ item.last_call_follow.content }}
                    </view>
                </view>
                
            </view>
            <view class="row" v-if="item.intention_community">
                <view class="row-label">客户意向</view>
                <view class="row-cont">{{ item.intention_community }}</view>
            </view>
            <view class="row" v-if="item.remark && item.remark != 'undefined'">
                <view class="row-label">客户线索</view>
                <view class="row-cont">{{ item.remark || '--' }}</view>
            </view>
            <view class="row">
                <view class="row-label">创建时间</view>
                <view class="row-cont">{{ item.created_at }}</view>
            </view>
            <view class="row">
                <view class="row-label">最后更新</view>
                <view class="row-cont">{{ item.operation_at }}</view>
            </view>
            <view class="row">
                <view class="row-label">
                    <text v-if="current == 'my'">维护人</text>
                    <text v-else>跟进人</text>
                </view>
                <view class="row-cont">
                    <text v-if="current == 'my'">{{ item.follow_user ? item.follow_user.user_name : '--' }}</text>
                    <text v-else>{{ item.last_follow && item.last_follow.admin ? item.last_follow.admin.user_name : '--' }}</text>
                    <view class="desc" v-if="item.last_follow && item.last_follow.content">
                        <text class="time">{{ item.last_follow && item.last_follow.created_at | substrTime }}</text>
                        <text class="cont">{{ item.last_follow && item.last_follow.content }}</text>
                    </view>
                </view>
            </view>
        </view>

        <view class="block" v-if="!isListBrief">
            <view class="labels" v-if="item.label && item.label.length > 0">
                <view class="label-item" v-for="(v, i) in item.label" :key="i">{{ v }}</view>
            </view>
        </view>
        
        <view class="op" v-if="!isListBrief">
            <view class="btn-more" @click.stop="openMoreOp(item,index)">
                更多<image :src="'/yidongduan/icon/more-fill.png' | imgDomain" class="icon-more"/>
            </view>
        </view>
    </view>

    <slot name="empty" v-if="$slots.empty && isEmpty"></slot>
    <loadMore :status="loadStatus" v-else/>    

    <view @click="goAddPage" class="add-btn">
      <image :src="'/yidongduan/customer/lu.png' | imgDomain" mode="widthFix"></image>
    </view>
    <backTopStore :zIndex="96"></backTopStore>
    <moreOp 
        :visible.sync="dialogs.more" 
        :current="current" 
        :customer="customer" 
        :customer-id="customerId"
        :customer-index="customerIndex"
        @remove-customer="removeCustomer"
        />
</view>
</view>
</template>

<script>
import { queryMobilePlace, queryPrivateMobilePlace, seeTel, seePrivateTel, isClaimAbled, checkForceFollow } from '@/common/utils/customer.js';
import loadMore from "@/components/loadMore.vue";
import moreOp from './more_op.vue';
import headImg from '@/customer/components/customer/headImg.vue';
import { mapState } from 'vuex';
import tNotice from '@/components/tplus/tNotice.vue';
import backTopStore from '@/components/backtop/components/back-top/back-top-store';
import { currentTime } from '@/components/utils/date.js';
export default {
    props: {
        current: { type: String, default:'my' },
        autoLoad: { type: Boolean, default: true },     //在created阶段加载列表
        params: { type: Object, default: ()=>({}) }, 
    },
    components: {
        loadMore,
        moreOp, 
        headImg, 
        tNotice,
        backTopStore,
    },
    data() {
        return {
            loading: false,             //是否加载中
            isEmpty: false,             //是否空数据
            isNoMore: false,            //是否有更多
            curUserInfo: {},            //当前登录会员信息
            queryMobiling: false,       //查询手机号归属地中
            seeTeling: false,           //查看电话中
            page: 1,
            count: 0,
            list: [],
            dialogs: {
                more: false,
                notice: false
            },
            customerId: 0,          //当前操作的客户id
            customer: null,         //当前选中的客户,
            customerIndex: -1,        //当前选中的客户在列表中的索引
        }
    },
    computed: {
        ...mapState(['crmConfig']),

        //获取配置 列表操作 转公 转交后是否刷新列表
        isRefreshListByTransfer(){
            let isRefresh = this.crmConfig.refresh_by_transfer && typeof this.crmConfig.refresh_by_transfer === 'number' && this.crmConfig.refresh_by_transfer == 1 ? true : false;
            console.log(isRefresh,'------------转公转交后是否刷新列表------------');
            return isRefresh;
        },

        //1、简洁首字，2图片头像
        isHeadBrief(){
            return this.crmConfig.list_headimage_style != 2;
        },
        //1卡片，2简洁
        isListBrief(){
            return this.crmConfig.wap_console_layout == 2;
        },
        currentParams(){
            switch(this.current){
                case 'my':
                    return {
                        form: 2
                    }
                case 'seas':
                    return {
                        form: 3
                    }
                case 'potential':
                    return {
                        form: 3,
                        c_type1: 4
                    }
                case 'useless':
                    return {
                        form: 3,
                        c_type1: 4,
                        c_type4: 6
                    }
                case 'my_trader':
                    return {
                        form: 2,
                        c_type6: 4
                    }
                case 'seas_trader':
                    return {
                        form: 3,
                        c_type6: 4
                    }
            }
            return {};
        },
        //管理员ids数组
        adminIds(){
            return (this.list.length ? this.list[0]?.admin_list || [] : []).map( e => e*1);
        },
        //是否客户管理员
        isManager(){
            if(this.curUserInfo.id && this.adminIds.length){
                return this.adminIds.includes(this.curUserInfo.id);
            }
            return false;
        },
        isMy(){
            return this.current === 'my';
        },
        isTrans(){
            return this.current === 'trans';
        },
        isUseless(){
            return this.current === 'useless';
        },
        isBelongPotential(){
            return this.current == 'potential' || this.isUseless
        },
        //可认领(公海客户及潜在客户显示)、我掉公的、我转公的可认领
        claimAbled(){
            return this.current == 'seas' || this.isBelongPotential || this.isMyClaimAbled;
        },
        isMyClaimAbled(){
            return this.params.c_type6 == 6 || this.params.c_type6 == 5
        },
        loadStatus(){
            if(this.loading) return 'loading';
            if(this.isEmpty) return 'nomore';
            if(this.isNoMore) return 'nomore';
            return '';
        },
        api(){
            if(this.isTrans){
                return "/admin/private_client/search" 
            }else{
                //shared
                if(this.params.shared == 1){
                    return '/admin/crm/share_follow/share_list';
                }else if(this.params.shared == 2){
                    return '/admin/crm/share_follow/share_my_list';
                }
                return "/admin/crm/client/search" 
            }
        },
    },
    watch: {
      
    },
    created() {
        this.getCurUserInfo();
        if(this.autoLoad){
            this.getList();
        }
    },
    filters: {
        levelIcon(level){
            switch(level){
                case 'A':
                    return '/yidongduan/customer/level/Frame1400.png';
                case 'B':
                    return '/yidongduan/customer/level/Frame1401.png';
                case 'C':
                    return '/yidongduan/customer/level/Frame1402.png';       
                case 'D':
                    return '/yidongduan/customer/level/Frame1403.png';
                default:
                    return '/yidongduan/customer/level/Frame1403.png';
            }
        },
        hideMobileNumber (val, isTrans) {
            if(isTrans){
                return val;
            }
            let reg = /^(.{3}).*(.{3})$/;
            return val.replace(reg, "$1*****$2");
        },
        substrTime (val) {
            return val ? val.substring(11, 16) : '';
        },
    },
    
    methods: {
        /**
         * 获取客户列表
         */
        getList(){
            if(this.isNoMore){
                return;
            }
            this.loading = true;
            let params = {...this.params, page: this.page, ...this.currentParams};
            if(Array.isArray(params.label)){
                params.label = params.label.length ? params.label.join(',') : '';
            }
            if(Array.isArray(params.tracking_id)){
                params.tracking_id = params.tracking_id.length ? params.tracking_id.join(',') : '';
            }
            if(Array.isArray(params.level_id)){
                params.level_id = params.level_id.length ? params.level_id.join(',') : '';
            }
            if(Array.isArray(params.source_id)){
                params.source_id = params.source_id.length ? params.source_id.join(',') : '';
            }

            //自定义tab搜索
            if(params.custom_tab){
                const querys = this.$Utils.parseUrlQuery(params.custom_tab.substring(params.custom_tab.indexOf('?')+1));
                for(const key in querys){
                    if(key == 'end_date' || key == 'start_date'){
                        if(params.date_type){
                            continue;
                        }
                    }
                    if(params[key] === '' || params[key] == null){
                        params[key] = querys[key];
                    }
                }
                delete params.custom_tab;
                delete params.client_type
            }

            for(const key in params){
                if(params[key] === '' || params[key] == null){
                    delete params[key];
                }
            }
            

            this.page === 1 && (this.list = []);
            this.$ajax.get(this.api, params, async res => {
                if (res.statusCode === 200) {
                    this.count = res.data?.total || 0;
                    if(this.page === 1 && this.count > 0){
                        this.dialogs.notice = true;
                        await this.$nextTick();
                    }

                    this.page++;
                    const data = (res.data?.data || []).map( item => {
                        //用于点击显示电话跟进内容
                        item._showCallFollowContent = false;
                        //是否被移除 移除时加载动画效果
                        item._isRemoving = false;
                        //放大 缩小VIEW  提示内容修改
                        item._isUpdating = false;
                        //更新时 增加边框
                        item._isUpdatingBorder = false;
                        //置顶时
                        item._isRemovingTop = false;
                        return item;
                    });
                    this.list = params.page === 1 ? data : this.list.concat(data);
                    if (data.length === 0) {
                        if(params.page === 1){
                            this.isEmpty = true;
                        }
                        this.isNoMore = true;
                    }
                }else{
                    uni.showToast({
                        title: res?.data?.message || '获取列表数据失败',
                        icon: 'none'
                    })
                }
                this.loading = false;
            }, er => {
                this.loading = false;
                console.log(er)
            });
        },
        search(){
            this.page = 1;
            this.isEmpty = false;
            this.isNoMore = false;
            this.getList();
        },
        /**
         * 显示更多操作
         */
        openMoreOp(item, index){
            this.customer = item;
            this.customerId = item.id;
            this.customerIndex = index;
            console.log(this.customerIndex,'-----------------当前操作的索引ID---------------------')
            this.dialogs.more = true;
        },
        /**
         * 获取当前登录会员信息
         */
        getCurUserInfo(){
            this.$ajax.get("/qywx/common/query", {}, (res) => {
                if (res.statusCode === 200) {
                    this.curUserInfo = res.data
                }
            });
        },
        /**
         * 切换显示最后一条通话跟进内容
         */
        toggleCallFollowContent(item){
            if(item.last_call_follow?.content){
                item._showCallFollowContent = !item._showCallFollowContent
            }
        },
        /**
         * 查看电话
         */
        async handleSeeTel(item){
            if(this.seeTeling){
                return;
            }
            this.seeTeling = true;
            try{
                if(!await this.isNeedFollow()){
                    const data = this.isTrans ? await seePrivateTel(item.id) : await seeTel(item.id);
                    let url = `/customer/demand?id=${item.id}&tel=1&current=${this.current}&name=${encodeURIComponent(item.cname)}&telType=${item.call_open_crm}&has_roles=${this.hasManageAuth(item) ? 1 : 0}`
                    this.$navigateTo(url)
                }
            }catch(e){
                console.log(e);
            }
            this.seeTeling = false;
        },
        /**
         * 查询手机号归属地
         */
        async queryMobilePlace(item, index){
            if(this.queryMobiling){
                return;
            }
            this.queryMobiling = true;
            try{
                if(!await this.isNeedFollow()){
                    const data =  this.isTrans ? await queryPrivateMobilePlace(item.id) : await queryMobilePlace(item.id);
                    this.$set(this.list[index], 'mobile_place', data)
                }
            }catch(e){}
            this.queryMobiling = false;
        },
        //是否需要强制跟进
        isNeedFollow(){
            return new Promise(async resolve => {
                if(this.isTrans){
                    resolve(false);
                    return
                }
                try{
                    let form = (this.current == 'my') ? 2 : 3
                    let source = (this.current == 'my') ? 2 : 1
                    const data = await checkForceFollow({
                        params: { form, source }
                    });
                    resolve(true)
                }catch(e){
                    resolve(false)
                }
            })
        },
        //是否有客户管理权限
        hasManageAuth(item){
            if(this.isTrans){
                return true
            }
            if(this.isManager){
                return true
            }
            //是否客户所有者
            if (this.curUserInfo.id == item.follow_id) {
                return true;
            }
            //是否跟进人
            if (item.follow_id > 0 && item.follow_id == this.login_user_info.id) {
                return true;
            }
            return false
        },
        //客户是否可认领
        isClaimAbled(item){
            if(!this.claimAbled){
                return false
            }
            return isClaimAbled(item, this.current);
        },
        goDetailPage(item){
            //跳转到详情时新增sys_source表示页面的来源
            if(this.isTrans){
                //流转客详情
                this.$navigateTo(`/customer/trans_detail?id=${item.id}&sys_source=customer_list`);
            }else{
                this.$navigateTo(`/customer/detail?id=${item.id}&sys_source=customer_list&current=${this.current}&un_renling_status=${!this.isClaimAbled(item)}`);
            }
        },
        goAddPage(){
            this.$navigateTo("/customer/uphold?type=1&current=" + this.current);
        },
        handleLongPress(item,index){
            if(this.isListBrief){
                this.openMoreOp(item,index)
            }
        },
        //20240820 新增功能  我的客户列表 转交 转让 到公海或同事时 操作成功后移除列表中该客户
        removeCustomer(args) {
            if(this.isRefreshListByTransfer){
                //如果配置了强制刷新列表
                console.log("开启了强制刷新列表");
                this.getList();
                return ;
            }else{
                console.log("未开启强制刷新列表");
            }
            if (typeof args !== 'object' || !args.hasOwnProperty('customer') || !args.hasOwnProperty('index')) {
                console.error('参数错误: args必须是对象且包含customer和index');
                return;
            }
            const { customer, index} = args;
            if (!customer || typeof index !== 'number' || index < 0) {
                console.error('参数错误: customer不能为空且index必须是数字且最小值是0');
                return;
            }
            //是否删除标志
            const is_del = args.is_del === 0 ? 0 : (args.is_del || 1);
            const position = args.position || 'left';

            //我的客户 和 流转客 
            if ( (this.current == 'my' || this.current =='trans') && is_del==1 ) {
                if(position=='left'){
                    console.log('当前移除客户');
                    this.$set(this.list[index], '_isRemoving', true);
                    setTimeout(() => {
                        this.list.splice(index, 1);
                    }, 1000); // 动画持续时间
                }
                if(position=='top'){
                    if(customer.order == 1){
                        //原先已经是置顶 那么此时是取消置顶
                        console.log("当前是取消置顶客户操作");
                        this.$set(this.list[index], 'order', 0);
                        return false;
                    }else{
                        console.log('当前是置顶客户操作');
                        if(index==0){
                            this.$set(this.list[index], 'order', 1);
                            return false;
                        }
                        this.$set(this.list[index], '_isRemovingTop', true);
                        setTimeout(() => {
                            this.$set(this.list, index, {
                                ...this.list[index],
                                order: 1,
                                _isRemovingTop: false
                            });
                            const [removedItem] = this.list.splice(index, 1);
                            this.list.unshift(removedItem);
                        }, 500); // 动画持续时间
                    }
                }
            }else{
                console.log('当前不移除客户 更新 list 中 operation_at');

                const { date, time } = currentTime();
                const currentDateTime = `${date} ${time}`;
                this.$set(this.list[index], 'operation_at', currentDateTime);
                this.$set(this.list[index], '_isUpdating', true);
                this.$set(this.list[index], '_isUpdatingBorder', true);
                setTimeout(() => {  
                    this.$set(this.list[index], '_isUpdating', false);
                }, 1500); // 动画持续时间

            }
        },
        upCustomerFollowContent(data){
            console.log("开始更新列表跟进的内容......");
            console.log(data);
            const index = data.customer_index===0 ? 0 : (data.customer_index || -1);
            console.log(index);
            if(!data.content || !data.client_id){
                return false;
            }
            if (index >= 0 && index < this.list.length) {  
                const { date, time } = currentTime();
                const currentDateTime = `${date} ${time}`;

                this.$set(this.list[index], 'operation_at', currentDateTime);

                if (!this.list[index].last_follow) {
                    this.$set(this.list[index], 'last_follow', {});
                }

                this.$set(this.list[index], 'last_follow', Object.assign({}, this.list[index].last_follow, {
                    admin_id: data.admin_id,
                    client_id: data.client_id,
                    content: data.content,
                    id: data.id,
                    type: data.type,
                    type_title: data.type_title,
                    created_at: currentDateTime,
                    admin: data.admin_info
                }));

            } else {
                console.error('索引超出范围');
            }
        }
    }
}

</script>

<style scoped lang="scss"> 
.list{
    padding: 32rpx;
    .list-item{
        position: relative;
        background-color: #fff;
        border-radius: 8rpx;
        padding: 24rpx;
        margin-bottom: 32rpx;
        overflow: hidden;

        &.remove-animation {
            animation: slideOutLeft 1s forwards;
        }

        &.remove-animation-top {
            animation: slideOutTop 0.5s forwards;
        }

        &.update-animation {
            animation: pulseScale 1.5s ease-in-out 1;
        }

        &.update-border{
            border: 2rpx solid #3172f6;
        }

        .mark-top{
            position: absolute;
            top: 0;
            right: 0;
            z-index: 1;
            width: 64rpx;
            height: 64rpx;
        }
        .header{
            display: flex;
            flex-direction: row;
            .left{
                width: 96rpx;
                height: 96rpx;
            }
            .right{
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                padding-left: 24rpx;
                overflow: hidden;
                .info{
                    display: flex;
                    flex-direction: row;
                    align-items: center;
                    overflow: hidden;
                    .cname{
                        display: inline-block;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        font-size: 32rpx;
                    }
                    .tag{
                        display: flex;
                        flex-direction: row;
                        white-space: nowrap;
                        .qw-icon{
                            height: 39rpx;
                            width: 39rpx;
                            margin-left: 8rpx;
                        }
                        .level-icon{
                            height: 36rpx;
                            width: 58rpx;
                            margin-left: 10rpx;
                        }
                        .sex-icon{
                            height: 28rpx;
                            width: 28rpx;
                            margin-left: 8rpx;
                        }
                        .label{
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                            color: #f56c6c;
                            height: 36rpx;
                            font-size: 22rpx;
                            background-color: #fef0f0;
                            border: 1px solid #fbc4c4;
                            padding: 0 8rpx;
                            border-radius: 4rpx;
                            margin-left: 10rpx;
                        }
                    }
                    
                }
                .tags{
                    display: inline-block;
                    margin-left: -16rpx;
                    .tag-item{
                        &.tag-red{
                            color: #e28181;
                            background-color: #fef0f0;
                        }
                        &.tag-grey{
                            color: #86909C;
                            background-color: #F2F3F5;
                        }
                    }
                }
            }
            .btn-claim{
                display: flex;
                flex-direction: row;
                align-items: center;
                height: 56rpx;
                padding: 0 28rpx;
                color: #2d84fb;
                background-color: #eff4fc;
                border: 1px solid #eaeaea;
                border-radius: 40rpx;
                margin: 20rpx 0 0 20rpx;
                &.disabled{
                    color: #adb5c1;
                    background-color: #f5f5f6;
                    border-color: #f5f5f6
                }
            }
        }

        .content{
            margin-top: 30rpx;
            .row{
                padding: 12rpx 0;
                line-height: 1.5;
                overflow: hidden;
                .row-label{
                    color: rgba(41, 44, 57, 0.4);
                    white-space: nowrap;
                    padding-right: 24rpx;
                }
                .row-cont{
                    flex: 1;
                    word-break: break-all;
                    .tel-cont{
                        
                        position: relative;
                        display: flex;
                        flex-direction: row;
                        flex-wrap: wrap;
                        white-space: nowrap;
                        padding-right: 70rpx;
                        overflow: hidden;
                        .mobile{
                            position: relative;
                            margin-right: 24rpx;
                            &.dot::after,&.dot-green::after{
                                content: '';
                                position: absolute;
                                right: -12rpx;
                                top: 0;
                                background-color: #f56c6c;
                                width: 10rpx;
                                height: 10rpx;
                                border-radius: 50%;
                            }
                            &.dot-green::after{
                                background-color: #9edf2e;
                            }
                        }
                        .mobile-place{
                            font-size: 24rpx;
                            color: rgba(41, 44, 57, 0.4);
                        }
                        .btn-mobile-place{
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                            height: 40rpx;
                            font-size: 24rpx;
                            color: #2d84fb;
                            background-color: #e8f1ff;
                            border: 1rpx solid #2d84fb;
                            border-radius: 4rpx;
                            padding: 0 10rpx;
                        }
                        .btn-mobile{
                            position: absolute;
                            right: 0;
                            width: 60rpx;
                            height: 42rpx;

                            .mobile-icon{
                                width: 36rpx;
                                margin: 0 auto;   
                            }
                        }
                    }
                    .desc{
                        display: inline-block;
                        margin-top: 8rpx;
                        color: #86909C;
                        .time{
                            padding-right: 12rpx;
                        }
                    }
                }
            }
        }
        .block{
            margin-top: 8rpx;
            border-top: 1rpx solid #f1f2f3;
        }
        .labels{
            margin-left: -16rpx;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
        }
        .op{
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            margin-top: 8rpx;
            .btn-more{
                display: inline-flex;
                flex-direction: row;
                align-items: center;
                color: rgba(41, 44, 57, 0.4);
                font-size: 26rpx;
                padding: 12rpx 6rpx 12rpx 18rpx;
                .icon-more{
                    width: 32rpx;
                    height: 32rpx;
                    margin-left: 6rpx;
                }
            }
        }

        &.brief{
            .btn-claim{
                display: none;
            }
            .tags{
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }

    .tag-item,.label-item{
        display: inline-flex;
        flex-direction: row;
        align-items: center;
        height: 40rpx;
        font-size: 24rpx;
        color: #2d84fb;
        background-color: #e5eeff;
        padding: 0 16rpx;
        border-radius: 8rpx;
        margin: 16rpx 0 0 16rpx;
    }
}

.add-btn{
    display: inline-block;
    position: fixed;
    right: 20rpx;
    bottom: 300rpx;
    z-index: 1;
    image {
        width: 160rpx;
        height: 160rpx;
    }
}


@keyframes slideOutLeft {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(-100%);
        opacity: 0;
    }
}

@keyframes slideOutTop {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(-100%);
        opacity: 0;
    }
}

@keyframes pulseScale {
  0%, 100% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.05);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(0.95);
  }
}

</style>