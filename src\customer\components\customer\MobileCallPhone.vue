<template>
  <view class="mobile-call-phone">
    <!-- 步骤1：选择外显号码和线路 -->
    <template v-if="step === 1">
      <view class="select-section">
        <view class="section-title">选择外显号码</view>
        <view class="select-wrapper">
          <selectDown 
            valueName="phone" 
            :multiple="false" 
            v-model="selectedPhone" 
            :localdata="phoneList"
            @change="handlePhoneChange"
            defaultValue="" 
            placeholder="请选择外显号码">
          </selectDown>
        </view>
        
        <view class="section-title" v-if="routeList.length">选择线路</view>
        <view class="select-wrapper" v-if="routeList.length">
          <selectDown 
            valueName="name" 
            :multiple="false" 
            v-model="selectedRoute" 
            :localdata="routeList"
            @change="handleRouteChange"
            defaultValue="" 
            placeholder="请选择线路">
          </selectDown>
        </view>
        
        <view class="action-buttons">
          <button 
            class="confirm-btn" 
            type="primary" 
            @click="handleConfirmCall"
            :disabled="!canCall"
            :loading="isCalling">
            {{ isCalling ? '拨打中...' : '确认拨打' }}
          </button>
          <button class="cancel-btn" @click="handleCancel">取消</button>
        </view>
      </view>
    </template>

    <!-- 步骤2：转接等待中 -->
    <template v-if="step === 2">
      <view class="calling-section">
        <view class="avatar-section">
          <image 
            class="avatar" 
            src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" 
            mode="aspectFill">
          </image>
        </view>
        
        <view class="phone-number">{{ formatPhoneNumber(phoneNumber) }}</view>
        <view class="location">{{ phonePlace || '未知归属地' }}</view>
        
        <view class="status-text waiting">
          运营商转接等待中...
        </view>
        
        <view class="loading-spinner">
          <view class="spinner"></view>
        </view>
      </view>
    </template>

    <!-- 步骤3：转接成功 -->
    <template v-if="step === 3">
      <view class="calling-section">
        <view class="avatar-section">
          <image 
            class="avatar" 
            src="https://img.tfcs.cn/backup/static/admin/default.jpg?x-oss-process=style/w_80" 
            mode="aspectFill">
          </image>
        </view>
        
        <view class="phone-number">{{ formatPhoneNumber(phoneNumber) }}</view>
        <view class="location">{{ phonePlace || '未知归属地' }}</view>
        
        <view class="status-text success">
          运营商转接成功
        </view>
        
        <view class="call-steps">
          <view class="step-title">通话步骤</view>
          <view class="steps-container">
            <view class="step-item completed">
              <image 
                class="step-icon" 
                src="https://img.tfcs.cn/backup/static/admin/waihu/zhuanjiechenggong.png?x-oss-process=style/w_80">
              </image>
              <text class="step-text">线路转接成功</text>
            </view>
            
            <view class="step-arrow">→</view>
            
            <view class="step-item active">
              <image 
                class="step-icon" 
                src="https://img.tfcs.cn/backup/static/admin/waihu/laidian.png?x-oss-process=style/w_80">
              </image>
              <text class="step-text">手机来电</text>
            </view>
            
            <view class="step-arrow">→</view>
            
            <view class="step-item">
              <image 
                class="step-icon" 
                src="https://img.tfcs.cn/backup/static/admin/waihu/jieting.png?x-oss-process=style/w_80">
              </image>
              <text class="step-text">客户接听通话</text>
            </view>
          </view>
        </view>
      </view>
    </template>

    <!-- 隐私号拨打模式 -->
    <template v-if="step === 4">
      <view class="private-number-section">
        <view class="instruction">
          请使用
          <text class="caller-number">{{ callerNumber }}</text>
          拨打
        </view>
        
        <view class="privacy-number">
          <text class="number">{{ privacyNumber }}</text>
          <text class="label">隐私号码</text>
        </view>
        
        <view class="countdown">
          <image class="timer-icon" src="/static/images/timer.png"></image>
          <text class="countdown-text">
            隐私号码 
            <text class="time">{{ countdownTime }}s</text>
            后失效
          </text>
        </view>
        
        <view class="action-buttons">
          <button 
            class="direct-call-btn" 
            type="primary" 
            @click="handleDirectCall">
            一键拨号
          </button>
          <button class="cancel-btn" @click="handleCancel">取消</button>
        </view>
      </view>
    </template>
  </view>
</template>

<script>
import selectDown from '@/outbound/components/uni-data-select'

export default {
  name: 'MobileCallPhone',
  components: {
    selectDown
  },
  props: {
    // 真实号码
    phoneNumber: {
      type: String,
      required: true
    },
    // 客户姓名
    customerName: {
      type: String,
      default: ''
    },
    // 客户ID
    clientId: {
      type: [String, Number],
      required: true
    },
    // 归属地
    phonePlace: {
      type: String,
      default: ''
    },
    // 线路数据
    routeList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      step: 1, // 1:选择 2:转接等待 3:转接成功 4:隐私号拨打模式
      selectedPhone: '',
      selectedRoute: '',
      phoneList: [],
      isCalling: false,
      callerNumber: '',
      privacyNumber: '',
      countdownTime: 0,
      countdownInterval: null,
      callInfo: {}
    }
  },
  computed: {
    canCall() {
      return this.selectedPhone && (this.routeList.length === 0 || this.selectedRoute)
    }
  },
  watch: {
    routeList: {
      handler(newVal) {
        if (newVal.length > 0) {
          const defaultRoute = newVal.find(item => item.is_default === 1)
          if (defaultRoute) {
            this.selectedRoute = defaultRoute.multi_route
          }
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.getPhoneList()
  },
  methods: {
    // 获取外显号码列表
    getPhoneList() {
      this.$ajax.get('/admin/call_clue/getSeatsPhone', {}, res => {
        if (res.statusCode === 200) {
          this.phoneList = res.data.map(item => ({
            value: item.show_id,
            text: item.phone,
            ...item
          }))
          if (this.phoneList.length > 0) {
            this.selectedPhone = this.phoneList[0].value
          }
        }
      })
    },
    
    // 处理外显号码变化
    handlePhoneChange(e) {
      this.selectedPhone = e
    },
    
    // 处理线路变化
    handleRouteChange(e) {
      this.selectedRoute = e
    },
    
    // 确认拨打
    handleConfirmCall() {
      if (!this.canCall) return
      
      this.isCalling = true
      const phone = this.phoneNumber.replace(/\s*/g, '')
      const selectedPhoneData = this.phoneList.find(item => item.value === this.selectedPhone)
      
      const params = {
        phone: phone,
        show_id: this.selectedPhone,
        client_id: this.clientId,
        multi_route: this.selectedRoute || ''
      }
      
      this.$ajax.post('/admin/call_clue/directCallPhone', params, res => {
        this.isCalling = false
        
        if (res.statusCode === 200) {
          this.callInfo = res.data
          
          // 根据call_route决定显示模式
          const callRoute = res.data.call_route
          
          if ([2, 4, 8].includes(callRoute)) {
            // 隐私号模式
            this.callerNumber = res.data.caller
            this.privacyNumber = res.data.telX
            this.countdownTime = Math.floor(res.data.expires / 2)
            this.step = 4
            this.startCountdown()
          } else {
            // 正常转接模式
            this.step = 2
            
            // 2秒后进入步骤3
            setTimeout(() => {
              this.step = 3
              
              // 30秒后自动关闭
              setTimeout(() => {
                this.handleClose()
              }, 30000)
            }, 2000)
          }
          
          // 发送通话信息给父组件
          this.$emit('callStarted', {
            callId: res.data.call_id,
            customerName: this.customerName,
            phoneNumber: phone,
            showNumber: selectedPhoneData?.text || ''
          })
          
        } else {
          uni.showToast({
            title: res.data.message || '拨打失败',
            icon: 'none'
          })
        }
      })
    },
    
    // 一键拨号
    handleDirectCall() {
      uni.makePhoneCall({
        phoneNumber: this.privacyNumber,
        success: () => {
          this.handleClose()
        },
        fail: () => {
          uni.showToast({
            title: '拨号失败',
            icon: 'none'
          })
        }
      })
    },
    
    // 开始倒计时
    startCountdown() {
      this.countdownInterval = setInterval(() => {
        if (this.countdownTime > 1) {
          this.countdownTime--
        } else {
          clearInterval(this.countdownInterval)
          this.handleClose()
        }
      }, 1000)
    },
    
    // 取消
    handleCancel() {
      this.handleClose()
    },
    
    // 关闭组件
    handleClose() {
      if (this.countdownInterval) {
        clearInterval(this.countdownInterval)
      }
      this.$emit('close')
    },
    
    // 格式化电话号码
    formatPhoneNumber(phone) {
      if (!phone) return ''
      phone = phone.replace(/\s*/g, '')
      if (phone.length === 11) {
        return `${phone.slice(0, 3)} ${phone.slice(3, 7)} ${phone.slice(7)}`
      }
      return phone
    }
  },
  
  beforeDestroy() {
    if (this.countdownInterval) {
      clearInterval(this.countdownInterval)
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-call-phone {
  width: 100%;
  min-height: 400rpx;
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-sizing: border-box;
}

.select-section {
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .select-wrapper {
    margin-bottom: 30rpx;
  }
  
  .action-buttons {
    margin-top: 60rpx;
    
    .confirm-btn, .cancel-btn {
      width: 100%;
      height: 88rpx;
      border-radius: 44rpx;
      font-size: 32rpx;
      margin-bottom: 20rpx;
    }
    
    .confirm-btn {
      background: #007aff;
      color: #fff;
      
      &[disabled] {
        opacity: 0.6;
      }
    }
    
    .cancel-btn {
      background: #f5f5f5;
      color: #666;
    }
  }
}

.calling-section {
  text-align: center;
  padding: 40rpx 0;
  
  .avatar-section {
    margin-bottom: 40rpx;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
    }
  }
  
  .phone-number {
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
  }
  
  .location {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 40rpx;
  }
  
  .status-text {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 60rpx;
    
    &.waiting {
      color: #007aff;
    }
    
    &.success {
      color: #07c160;
    }
  }
  
  .loading-spinner {
    .spinner {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #f3f3f3;
      border-top: 4rpx solid #007aff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  .call-steps {
    margin-top: 60rpx;
    
    .step-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 40rpx;
    }
    
    .steps-container {
      display: flex;
      align-items: center;
      justify-content: center;
      
      .step-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .step-icon {
          width: 60rpx;
          height: 60rpx;
          margin-bottom: 10rpx;
        }
        
        .step-text {
          font-size: 24rpx;
          color: #666;
        }
        
        &.completed .step-text {
          color: #07c160;
        }
        
        &.active .step-text {
          color: #007aff;
          font-weight: bold;
        }
      }
      
      .step-arrow {
        margin: 0 20rpx;
        color: #999;
        font-size: 24rpx;
      }
    }
  }
}

.private-number-section {
  text-align: center;
  padding: 40rpx 0;
  
  .instruction {
    font-size: 32rpx;
    color: #333;
    margin-bottom: 40rpx;
    
    .caller-number {
      color: #007aff;
      font-weight: bold;
    }
  }
  
  .privacy-number {
    margin-bottom: 40rpx;
    
    .number {
      display: block;
      font-size: 48rpx;
      font-weight: bold;
      color: #ff3d3d;
      margin-bottom: 10rpx;
    }
    
    .label {
      font-size: 24rpx;
      color: #999;
      background: #f1f4fa;
      padding: 4rpx 16rpx;
      border-radius: 20rpx;
    }
  }
  
  .countdown {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 60rpx;
    
    .timer-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 10rpx;
    }
    
    .countdown-text {
      font-size: 28rpx;
      color: #666;
      
      .time {
        color: #ff3d3d;
        font-weight: bold;
      }
    }
  }
  
  .action-buttons {
    .direct-call-btn, .cancel-btn {
      width: 100%;
      height: 88rpx;
      border-radius: 44rpx;
      font-size: 32rpx;
      margin-bottom: 20rpx;
    }
    
    .direct-call-btn {
      background: #07c160;
      color: #fff;
    }
    
    .cancel-btn {
      background: #f5f5f5;
      color: #666;
    }
  }
}
</style>