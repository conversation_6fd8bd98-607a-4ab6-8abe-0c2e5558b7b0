<template>
<view class="picker-wrapper">
    <view class="search-input-wrapper" v-if="filterable">
		<view class="search-input">
			<myIcon class="icon" type="ic_sousuo3x1" color="#bbb3b3" size="32rpx"></myIcon>
			<view class="search-input-view">
				<input type="text" placeholder-style="font-size:15px;color: rgba(41, 44, 57, 0.4)" @input="handleSearchInput"
                    confirm-type="done" :placeholder="filterPlaceholder" />
			</view>
		</view>
	</view>

    <view class="picker" :style="{height: height}" :class="{strictly: checkStrictly}">
        <view v-if="loading" class="loading-mask">
			<view class="loading-loader"></view>
		</view>
        <template v-else-if="currentColumnList.length == 1 && currentColumnList[0].length == 0">
            <view class="picker-column">
                <slot name="empty"></slot>
            </view>
        </template>
        <template v-else>
            <view class="picker-column" v-for="(col, index) in currentColumnList" :key="index">
                <scroll-view scroll-y>
                    <view class="picker-column-item" v-for="(item, key) in col" :key="key" @click="selectItem(index, item)" :class="{
                        checked: allCurCheckedValues.includes(item[map.value]),
                        active: allCheckedValues.includes(item[map.value]),
                    }">
                        <view class="label">{{item[map.label]}}</view>
                        <view class="icon-checked">
                            <uni-icons type="checkmarkempty" size="20" color="#488AF6"></uni-icons>
                        </view>
                    </view>
                </scroll-view>
            </view>
            
            
        </template>
    </view>
</view> 
</template>
<script>
import Utils from '@/common/utils';
import myIcon from "@/components/my-icon";
export default {
    name: 'tCustomPickerView',
    components: {
        myIcon
    },
    props: {
        value: { type: [String, Number, Array], default: ''},
        datas: {type: Array, default:()=>[]},
        map: { type: Object, default: ()=>{ return {children: 'children', value: 'value', label: 'label'}}},
        multiple: {type: Boolean, default:false},
        height: { type: String, default: '40vh'},
        loading: {type: Boolean, default:false},
        filterable: {type: Boolean, default:false},
        filterPlaceholder: { type: String, default: '请输入关键词搜索' },
        checkStrictly : { type: Boolean, default: true },
        remote : { type: Boolean, default: false },
        multipleLimit: {type: Number, default: 0},
        allowCreate : { type: Boolean, default: false },
        /* expandValues: { type: Array, default:()=>[] }, */
    },
    model: {
        prop: 'value',
        event: 'change',
    },
    data(){
        return {
            keywords: '',
            isFilterMode: false,
            checkedValues: [],
            currentColumnList: [],
            curCheckedValues: [],
            curCheckedValue: '',
            curCheckedValueMulti: [],
            ranges: [],
            checkedValueFormated: false
        }
    },
    computed: {
        allCheckedValues(){
            if(this.multiple){
                
                return this.checkedValues.reduce((cur, next)=>{
                    return cur.concat(next[next.length -1] === '' ? [] : next);
                }, [...this.curCheckedValues])

            }
            return this.curCheckedValues;
        },
        allCurCheckedValues(){
            if(this.multiple){
                let allValues = [],
                    last = this.curCheckedValues.length -1,
                    curValues = JSON.stringify(this.curCheckedValues.slice(0, last - 1));
                for(const values of this.checkedValues){
                    if(values.length === last+1){
                        if(curValues === JSON.stringify(values.slice(0, last-1))){
                            allValues.push(values[last]);
                        }
                    }
                }
                return allValues.concat(this.curCheckedValues);
            }
            return this.curCheckedValues;
        }
    },
    watch: {
        value: {
            handler(val){
                if(this.multiple){
                    if(JSON.stringify(val) === JSON.stringify(this.curCheckedValueMulti)){
                        return
                    }
                }else if(val === this.curCheckedValue){
                    return;
                }
                
                if(this.multiple){
                    this.checkedValues = val.length === 0  ? [] : val.map(e => Array.isArray(e) ? e : [e]);
                }else{
                    this.checkedValues = val === ''  ? [] : [val];
                }
                
                this.setCheckedValues();
            },
            immediate: true
        },
        datas: {
            handler(d){
                this.initDatas(d);
                this.transDataRanges(d);
                this.setCheckedValues();
            },
            immediate: true
        }
    },
    created(){
        
    },
    mounted(){
       /*  const unwatch = this.$watch('checkedValueFormated', {
            handler(val){
                if(val){
                    this.setCurCloumns();
                    setTimeout(()=>{
                        unwatch();
                    })
                }
            },
            immediate: true
        }) */
    },
    methods: {
        
        //设置选中值
        setCheckedValues: Utils.debounce(function(){
            if(this.datas.length == 0){
                return;
            }
            if(this.checkedValues.length == 0){
                this.curCheckedValues = [];
                !this.isFilterMode && (this.currentColumnList = [this.datas]);
                return;
            }
            

            let ranges = this.ranges, child = this.map.children, valueKey = this.map.value;
            if(this.multiple){
                const checkedValues = this.checkedValues.map(values => {
                    for(const value of [...values].reverse()){
                        for(let i = ranges.length-1; i >=0; i-- ){
                            let range = ranges[i];
                            const item = range.find(e => e[valueKey] === value);
                            if(item){
                                if(this.hasChildren(item)){
                                    return item._parentKey.concat(value).concat('');
                                }
                                return item._parentKey.concat(value);
                            }
                        }
                    }
                    return values;
                })
                
                this.checkedValues = checkedValues;
            }else{
                const checkedValues = this.checkedValues.map(value => {
                    for(let i = ranges.length-1; i >=0; i-- ){
                        let range = ranges[i];
                        const item = range.find(e => e[valueKey] === value);
                        if(item){
                            if(this.hasChildren(item)){
                                return item._parentKey.concat(value).concat('');
                            }
                            return item._parentKey.concat(value);
                        }
                    }
                    return [value];
                })
                this.checkedValues = checkedValues[0];
            }

            this.setCurCloumns();
            this.checkedValueFormated = true;
        }, 0),
        setCurCloumns(){
            this.curCheckedValues = this.multiple ? this.checkedValues[0] : this.checkedValues;

            let ranges = this.ranges, child = this.map.children, valueKey = this.map.value;
            this.currentColumnList.splice(1);
            for(const [index, value] of this.curCheckedValues.entries()){
                let range = ranges[index];
                if(range && value !== ''){
                    const item = range.find(e => e[valueKey] === value)
                    if(item && this.hasChildren(item)){
                        this.currentColumnList.push(item[child]);
                    }
                }
            }
        },
        
        initDatas(datas){
            this.currentColumnList = [datas];
        },
        hasChildren(item){
            const child = this.map.children;
            return Array.isArray(item[child]) && item[child].length !== 0;
        },
        selectItem(colIndex, item){
            if(this.multiple){
                this.selectItemMultiple(colIndex, item);
                return;
            }
            const valueKey = this.map.value,
                child = this.map.children,
                curValue = item[valueKey],
                hasNextData = this.hasChildren(item);

            if(this.multiple){
                //TODO
            }else{
                let checkedValues = this.curCheckedValues.slice(this.isFilterMode ? -1: 0),
                    length = checkedValues.length;
                    
                
                if(length > 0 && checkedValues[length-1] === ''){
                    checkedValues.pop();
                    length--;
                };
                

                //选中的为下一级
                if(length == 0 || length == colIndex){
                    checkedValues.push(curValue);
                }else{
                    //当前已选中
                    if(checkedValues[colIndex] === curValue){
                        if(hasNextData){
                            return;
                        }
                        //当前为最后一级时清空
                        checkedValues[colIndex] = '';
                    }else{
                        checkedValues.splice(colIndex, length-colIndex, curValue );
                        this.currentColumnList.splice(colIndex+1, length-colIndex);
                    }
                }
                if(hasNextData){
                    checkedValues.push('')
                    this.currentColumnList.push(item[child]);
                }

                this.curCheckedValues = checkedValues;
                if(!hasNextData){
                    this.checkedValues = checkedValues;
                    if(this.isFilterMode){
                        if(checkedValues.length && checkedValues[0] !== ''){
                            this.curCheckedValues = [...item._parentKey, curValue]
                        }else{
                            this.curCheckedValues = [...item._parentKey.slice(0, 1), '']
                        }
                    }
                }else{
                    let isCurPath = true;
                    for(let i = 0, length = checkedValues.length-1; i < length; i++){
                        if(checkedValues[i] !== this.checkedValues[0]){
                            isCurPath = false;
                            break;
                        }
                    }
                    if(isCurPath){
                        this.curCheckedValues = this.checkedValues;
                        let i = this.currentColumnList.length, len = this.curCheckedValues.length;
                        if(i < len){
                            for(; i < len; i++){
                                let val = this.checkedValues[i-1],
                                    pathItem = this.currentColumnList[i - 1].find( e =>e[valueKey] === val);
                                if(this.hasChildren(pathItem)){
                                    this.currentColumnList.push(pathItem[child]);
                                }
                            }
                        }
                    }
                }
            }


            if(!hasNextData){
                this.curCheckedValue = this.getCheckedValue();
                this.$emit('input', this.curCheckedValue);
                this.$emit('change', this.curCheckedValue);
            }
        },
        selectItemMultiple(colIndex, item){
            const valueKey = this.map.value,
                child = this.map.children,
                curValue = item[valueKey],
                hasNextData = this.hasChildren(item);

            let checkedIndex = this.checkedValues.findIndex(e => e.includes(curValue)),
                checkedValues = [],
                length = 0;
            if(colIndex === 0 && checkedIndex !== -1 && !this.curCheckedValues.some(e => e === curValue)){
                this.curCheckedValues = this.checkedValues[checkedIndex].slice();
            }
            checkedValues =  this.curCheckedValues.slice(this.isFilterMode ? -1: 0);
            length = checkedValues.length;

      
            
            if(length > 0 && checkedValues[length-1] === ''){
                checkedValues.pop();
                length--;
            }; 

            if(this.multipleLimit > 0){
                if(!this.allCheckedValues.includes(curValue)){
                    const seledLength = this.checkedValues.filter(e => e.length && e[e.length-1] !== '').length;
                    if(seledLength >= this.multipleLimit){
                        this.$emit('exceed');
                        return;
                    }
                }
            }
            
            if(length == 0){
                checkedValues.push(curValue);
            }else{
                //当前已选中
                if(this.allCheckedValues.includes(curValue)){
                    if(hasNextData){
                        this.currentColumnList.splice(colIndex+1, length-colIndex);
                    }else{
                        //当前为最后一级时清空
                        checkedValues[colIndex] = '';
                    }
                }else{
                    checkedValues.splice(colIndex, length-colIndex, curValue );
                    this.currentColumnList.splice(colIndex+1, length-colIndex);
                }
            }
      
            if(hasNextData){
                checkedValues.push('')
                this.currentColumnList.push(item[child]);
            }
            
            
            
            if(!hasNextData){
                if(checkedValues[0] !== ''){
                    if(checkedIndex === -1){
                        this.checkedValues.push(checkedValues);
                    }else{
                        this.checkedValues[checkedIndex] = checkedValues;    
                    }
                }else if(checkedIndex !== -1){
                    this.checkedValues.splice(checkedIndex, 1)
                }
                
                if(this.isFilterMode){
                    if(checkedValues.length && checkedValues[0] !== ''){
                        this.curCheckedValues = [...item._parentKey, curValue]
                    }else{
                        this.curCheckedValues = [...item._parentKey.slice(0, 1), '']
                    }
                }else{
                    this.curCheckedValues = checkedValues;
                }
            }else{
                let temp = this.checkedValues.find(e => {
                    return e.includes(curValue) && e[e.length -1] !== ''
                })
                if(temp){
                    checkedValues = temp;
                }
                
                this.curCheckedValues = checkedValues;
                /* let i = this.currentColumnList.length, len = this.curCheckedValues.length;
                if(i < len){
                    for(; i < len; i++){
                        let val = checkedValues[i-1],
                            pathItem = this.currentColumnList[i - 1].find( e =>e[valueKey] === val);
                        if(this.hasChildren(pathItem)){
                            this.currentColumnList.push(pathItem[child]);
                        }
                    }
                } */
            }


            if(!hasNextData){
                this.curCheckedValueMulti = this.getCheckedValue();
                this.$emit('input', this.curCheckedValueMulti);
                this.$emit('change', this.curCheckedValueMulti);
            }
        },
        getCheckedValue(){
            if(this.multiple){
                return this.checkedValues.map(e => {
                    return e.length ? e[e.length -1]: '';
                }).filter(e=>e!=='')    
            }else{
                let len = this.checkedValues.length;
                return len ?  this.checkedValues[len - 1] : ''
            }
        },
        transDataRanges(datas){
            let ranges = [], child = this.map.children, valueKey = this.map.value, labelKey = this.map.label;
            const _trans = (cols, parentKey, parentLabel, columnIndex = 0) =>{
                if(!ranges[columnIndex]){
                    ranges[columnIndex] = [];
                }
                for(const item of cols){
                    item._parentKey = parentKey;
                    item._parentLabel = parentLabel;
                    item._hasChild = this.hasChildren(item);
                    item._level = columnIndex;
                    ranges[columnIndex].push(item);
                    if(item._hasChild){
                        _trans(item[child], parentKey.concat(item[valueKey]), parentLabel.concat(item[labelKey]), columnIndex+1);
                    }
                }
            } 
            _trans(datas, [], []);
            this.ranges = ranges;
        },
        handleSearchInput: Utils.debounce(function({ detail }){
            let keywords = detail.value.trim();
            if(this.remote){
                this.$emit('filter', keywords);
                return;
            }

            if(keywords === ''){
                this.currentColumnList = [this.datas];

                let i = 1, len = this.curCheckedValues.length, valueKey = this.map.value, child = this.map.children;
                if(i < len){
                    for(; i < len; i++){
                        let val = this.curCheckedValues[i-1],
                            pathItem = this.currentColumnList[i - 1].find( e =>e[valueKey] === val);
                        if(this.hasChildren(pathItem)){
                            this.currentColumnList.push(pathItem[child]);
                        }
                    }
                }
                this.isFilterMode = false;
            }else{
                
                let list = [], labelKey = this.map.label, valueKey = this.map.value;
                for(const range of this.ranges){
                    for(const item of range){
                        if(!item._hasChild){
                            let labels = item._parentLabel.concat(item[labelKey]);
                            
                            if(labels.some(e => e.includes(keywords))){
                                list.push({...item, [labelKey]: labels.join('/')})
                            }
                        }
                    }
                }

                if(this.allowCreate && list.length === 0){
                    list.push({[valueKey]: String(keywords), [labelKey]: keywords})
                }

                this.currentColumnList = [list];
                this.isFilterMode = true;
            }

        }, 200),
        getCheckedItem(){
            let val = this.getCheckedValue(), valueKey = this.map.value;
            const items = [];

            if(this.multiple ? val.length: val !== ''){
                for(const range of this.ranges){
                    for(const item of range){
                        if(!item._hasChild){
                            if(this.multiple){
                                if(val.includes(item[valueKey])){
                                    items.push({
                                        value: item._parentKey.concat(item[valueKey]),
                                        label: item._parentLabel.concat(item[this.map.label]),
                                    });
                                }
                            }else{
                                if(item[valueKey] === val){
                                    return {
                                        value: item._parentKey.concat(val),
                                        label: item._parentLabel.concat(item[this.map.label]),
                                    };
                                }
                            }
                        }
                    }
                }
            }
            return items.length ? items : null;
        }
    }
}
</script>
<style lang="scss" scoped>
.picker-wrapper{
    background-color: #fff;
    .search-input-wrapper{
        padding: 32rpx 32rpx 32rpx;
        border-bottom: 1px solid #f9f9f9;
        .search-input{
            height: 76rpx;
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: #F6F6F6;
            border-radius: 76rpx;
            padding: 0 24rpx;
            .search-input-view{
                flex: 1;
                display: inline-block;
                padding-left: 16rpx;
            }
        }
    }
}
.picker{
    height: 100%;
    display: flex;
    flex-direction: row;
    position: relative;
    justify-content: center;
    
    .picker-column{
        flex: 1;
        overflow: hidden;
        height: 100%;
        uni-scroll-view{
            height: 100%;
        }
    }
    .picker-column-item{
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 0 15px;
        height: 42px;
        font-size: 16px;
        position: relative;
        &::after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            height: 1px;
            clear: both;
            border-bottom: 1px solid #e5e5e5;
            color: #e5e5e5;
            -webkit-transform-origin: 0 100%;
            transform-origin: 0 100%;
            -webkit-transform: scaleY(.5);
            transform: scaleY(.5);
        }
        .label{
            flex: 1;
            display: inline-block;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            text-align: left;
        }
        .icon-checked{
            display: none;
        }
        &.checked{
            .icon-checked{
                display: flex;
            }
        }
    }
}

.picker.strictly{
   
    .picker-column:not(:last-child){
        background-color: #f9f9f9;
        .picker-column-item{
            &.checked{
                color: #2d84fb;
                background-color: #fff;
                .icon-checked{
                    display: none;
                }
            }
            &.active{
                color: #2d84fb;
            }
        }
    }
    .picker-column-item{
        &::after{
            display: none;
        }
    }

}

scroll-view{
    height: 100%;
}

.loading-mask {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: rgba(255, 255, 255, 0.8);
	z-index: 99;
	/* #ifndef APP-NVUE */
	display: flex;
	margin: auto;
	transition: all 0.5s;
	/* #endif */
	justify-content: center;
	align-items: center;
}

.loading-loader {
	width: 30px;
	height: 30px;
	border: 2px solid #aaa;
	// border-bottom-color: transparent;
	border-radius: 50%;
	/* #ifndef APP-NVUE */
	animation: 2s uni-table--loader linear infinite;
	/* #endif */
	position: relative;
}

@keyframes uni-table--loader {
	0% {
		transform: rotate(360deg);
	}

	10% {
		border-left-color: transparent;
	}

	20% {
		border-bottom-color: transparent;
	}

	30% {
		border-right-color: transparent;
	}

	40% {
		border-top-color: transparent;
	}

	50% {
		transform: rotate(0deg);
	}

	60% {
		border-top-color: transparent;
	}

	70% {
		border-left-color: transparent;
	}

	80% {
		border-bottom-color: transparent;
	}

	90% {
		border-right-color: transparent;
	}

	100% {
		transform: rotate(-360deg);
	}
}
</style>