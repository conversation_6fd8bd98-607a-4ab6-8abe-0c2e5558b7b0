<!--企微侧边栏客户跟进，从企微接口获取客户信息--20231010从customer/detail.vue分离-->
<template>
  <view></view>
</template>
<script>
export default {
  data() {
    return {
      source: 1,
      website_id: '',
      qwUserId: '',   //当前联系人企业微信userId
    };
  },
  onLoad(options) {
    if(this.$isWxWork() != 'wxwork' && this.$isWxWork() != 'com-wx-pc'){
      uni.showToast({ title: '请在企微环境中打开'});
      return  
    }
    
    this.token = localStorage.getItem("wxwork_token");
    this.website_id = options.website_id || localStorage.getItem("wxwork_id");
    if (!this.token) {
      uni.showToast({ title: '登录状态不正确'});
      return
    }

    this.source = options.source || 1
    this.init();
  },
  mounted() {

  },
  methods: {
    init(){
      //获取当前联系人企微userID
      this.getWxQyWxConfig(["agentConfig", 'getCurExternalContact'], wx => {
        wx.invoke("getCurExternalContact", {}, (res) => {
            if (res.err_msg == "getCurExternalContact:ok") {
              this.qwUserId = res.userId;
            } else {
              console.log(res);
              uni.showToast({ title: 'getCurExternalContact 获取客户失败'});
            }
        });
      })
    },
    //通过企微userId 获取联系人信息
    getUserData(){
        url = `/qywx/client/qw_info/${this.qwUserId}`;
        this.$ajax.get(url, { type: this.source }, (res) => {
            if (res.statusCode === 200) {
                const client_id = res.data.id || 0;
                uni.redirectTo({
                    
                    url: '/customer/detail?website_id=' + this.website_id+'&client_id='+client_id
                })
            }else{
                uni.hideLoading()
                uni.redirectTo({
                    url: '/customer/default_crm?website_id=' + this.website_id
                })
            }
        })
    },
  }
};
</script>