const wx = window.jWeixin;
export default {
  methods: {
    getWxQyWxConfig(jsApiList, callback, siteID, type) {
      const _this = this;
      let url = window.location.href;
      let params = { url };
      if (siteID) {
        params.website_id = siteID;
      }
      // 测试用
      // let par = url.split("?")[1]
      // let p = par.split("&")
      // let website_id = ''
      // p.map(item => {
      //   if (item.includes("website_id")) {
      //     website_id = item.split("=")[1]
      //   }
      //   // obj[item.split("=")[0]] = item.split("=")[1]

      // })

      this.$ajax.get(
        // "/common/wx_work/auth/get/js_agent_config/app",
        // "/common/wx_agent/jssdk/config_get",
        // "/common/qywx/self/jssdk",
        "/qywx/home/<USER>",
        params,
        (res) => {
          if (res.statusCode === 200) {
            wx.config({
              beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
              debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
              appId: res.data.corpid, // 必填，企业微信的corpID
              timestamp: res.data.timestamp, // 必填，生成签名的时间戳
              nonceStr: res.data.nonceStr, // 必填，生成签名的随机串
              signature: res.data.signature, // 必填，签名，见 附录-JS-SDK使用权限签名算法
              jsApiList: jsApiList || [
                "agentConfig",
                "onMenuShareWechat",
                "scanQRCode", //  企业微信扫码
                "onMenuShareAppMessage", // 企业微信自定义‘网页内容分享’
              ], // 必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来
            });
            setTimeout(() => {
              wx.ready(() => {
                this.getWxAgentConfig(res, jsApiList, callback, type);
                // openScan 1:扫码  2：分享 3:直播
                switch (_this.openScan) {
                  case 1:
                    wx.scanQRCode({
                      needResult: 1, // 默认为0，企业微信处理，1直接返回扫描结果
                      scanType: ["qrCode", "barCode"], // 指定扫码类型
                      success: (res) => {
                        let str = res.resultStr; // 当needResule = 1 返回扫码结果
                        let str2 = str.indexOf(
                          "/project_broker/all_customer" != -1
                        );
                        if (str2) {
                          _this.$navigateTo(str);
                        } else {
                          uni.showToast({
                            title: "该地址无效",
                            icon: "none",
                          });
                        }
                      },
                    });
                    break;
                  case 2:
                    _this.getQyShare();
                    break;
                }
              });
              wx.error((err) => {
                alert(JSON.stringify("wx.error", err));
              });
            }, 200);
          } else {
            // uni.showToast({
            //   title: res.data.message || "获取企业微信jssdk失败",
            //   icon: "none",
            // });
            console.log("获取企业微信jssdk报错", res.data.message);
            callback();
          }
        }
      );
    },
    getWxAgentConfig(res, jsApiList, callback, type) {
      // window.wx = window.jWeixin
      wx.checkJsApi({
        jsApiList: jsApiList || [
          "agentConfig",
          "selectExternalContact",
          "navigateToAddCustomer",
        ],
        success: () => {
          // console.log(res)
          console.log("wxcongfig",'-------------------------');
          if (type == 1) {
            callback && callback(wx);
            return;
          }
          console.log(res.data);
          alert('wx.checkJsApi', checkJsApires)
          wx.agentConfig({
            agentid: res.data.agent_id,
            corpid: res.data.corpid,
            debug: false,
            jsApiList: jsApiList || [
              "selectExternalContact",
              "navigateToAddCustomer", //进入添加客户界面
            ],
            nonceStr: res.data.nonceStr,
            signature: res.data.agent_signature,
            timestamp: res.data.timestamp,
            success: function() {
              if (callback) {
                callback(wx);
              }
              // alert("agentConfigres：" + JSON.stringify(agentConfigres));
              // wx.invoke(
              //   "selectExternalContact",
              //   {
              //     filterType: 0, //0表示展示全部外部联系人列表，1表示仅展示未曾选择过的外部联系人。默认值为0；除了0与1，其他值非法。在企业微信2.4.22及以后版本支持该参数
              //   },
              //   function(res) {
              //     if (res.err_msg == "selectExternalContact:ok") {
              //       let userIds = res.userIds; //返回此次选择的外部联系人userId列表，数组类型
              //       console.log(userIds);
              //     } else {
              //       //错误处理
              //     }
              //   }
              // );
            },
            fail: function(checkJsApires) {
              alert("agentConfigres：" + JSON.stringify(checkJsApires));
              if (checkJsApires.errMsg.indexOf("function not exist") > -1) {
                alert("版本过低请升级");
              } else {
                alert("error:" + JSON.stringify(checkJsApires));
              }
            },
          });
        },
        fail: (err) => {
          uni.showToast({
            title: "err" + JSON.stringify(err),
            icon: "none",
          });
        },
      });
    },
    getQyShare() {
      // 分享
      switch (this.share.forward_config) {
        case "index":
          wx.onMenuShareAppMessage({
            title: this.share.forward_title, // 分享标题
            desc: this.share.forward_desc, // 分享描述
            link: window.location.href, // 分享链接；在微信上分享时，该链接的域名必须与企业某个应用的可信域名一致
            imgUrl: this.share.forward_pic, // 分享图标
          });
          break;
        case "recommend":
          wx.onMenuShareAppMessage({
            title: this.share.forward_title, // 分享标题
            desc: this.share.forward_desc, // 分享描述
            link:
              window.location.href +
              `&share=from_share&recommend_code=${this.share.forward_recommend_code}`, // 分享链接；在微信上分享时，该链接的域名必须与企业某个应用的可信域名一致
            imgUrl: this.share.forward_pic, // 分享图标
          });
          break;
        case "company":
          wx.onMenuShareAppMessage({
            title: this.share.forward_title, // 分享标题
            desc: this.share.forward_desc, // 分享描述
            link:
              window.location.href +
              `&company_code=${obj.user.company_store_code}`, // 分享链接；在微信上分享时，该链接的域名必须与企业某个应用的可信域名一致
            imgUrl: this.share.forward_pic, // 分享图标
          });
          break;
        case "build":
          wx.onMenuShareAppMessage({
            title: this.share.forward_title, // 分享标题
            desc: this.share.forward_desc, // 分享描述
            link:
              window.location.href +
              `&buildID=${this.GetQueryString(
                "buildID"
              )}&share=from_share&share_id=${this.share.forward_id}`, // 分享链接；在微信上分享时，该链接的域名必须与企业某个应用的可信域名一致
            imgUrl: this.share.forward_pic, // 分享图标
          });
          break;
      }
    },
  },
};
