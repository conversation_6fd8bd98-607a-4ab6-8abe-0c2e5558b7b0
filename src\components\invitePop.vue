<template>
  <view class="box ">
    <view class="box_invite" v-if="invite"> </view>
    <view class="box_code" v-else>
      <view class="title">
        <text>实时接收</text>
        <text>报备状态通知</text>
      </view>
      <!-- 取消 -->
      <view class="cancel" @click="onCancel">
        <!-- <myIcon type="guanbi" color="#fff" size="56rpx"></myIcon> -->
        <myIcon class="guanbi" type="guanbi" lineHeight="56rpx"></myIcon>
        <!-- <view class="guanbi">X</view> -->
      </view>
      <view class="code">
        <view class="img">
          <image :src="image" mode="aspectFill"></image>
        </view>
      </view>
      <view class="msg">{{ tip }}</view>
    </view>
  </view>
</template>

<script>
import myIcon from "./my-icon";
export default {
  components: { myIcon },
  props: {
    invite: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
    image: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
    tip: {
      type: String,
      default: "长按识别二维码关注公众号",
    },
  },
  data() {
    return {
      show_share_tip: false,
    };
  },
  methods: {
    // 点击跳转到邀请界面
    onSubmit() {
      console.log("正在跳转");
      // this.$navigateTo(`/user/service`)
    },
    // 点击取消
    onCancel() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.box {
  height: 842rpx;
  background-image: url("https://img.tfcs.cn/static/img/invit.png");
  background-size: 600rpx 842rpx;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  border-radius: 10rpx;
  .box_invite {
    align-items: center;
    justify-content: space-evenly;
  }
  .box_code {
    align-items: center;
    justify-content: space-evenly;
    .title {
      position: absolute;
      top: 49rpx;
      left: 46rpx;
      line-height: 60rpx;
      font-size: 48rpx;
      color: #fdfaf9;
    }
    .code {
      position: absolute;
      bottom: 106rpx;
      width: 512rpx;
      height: 512rpx;
      background: #fff;
      border-radius: 10rpx;
      box-shadow: 0 10rpx 10rpx #f5f5f5;
      .img {
        width: 504rpx;
        height: 512rpx;
        margin: auto;
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
    .msg {
      position: absolute;
      bottom: 40rpx;
      margin-top: 40rpx;
      font-size: 28rpx;
      color: #333;
    }
  }
  .cancel {
    width: 80rpx;
    height: 80rpx;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    position: absolute;
    right: -30rpx;
    .guanbi {
      width: 56rpx;
      height: 56rpx;
      background: #fff;
      text-align: center;
      line-height: 56rpx;
      border-radius: 50%;
    }
  }
}
</style>
