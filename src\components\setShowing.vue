<template>
  <view class="form set_showing">
    <view class="house_title">
      <text class="text">{{ house.title }}</text>
    </view>
    <view class="form_item">
      <view class="left">
        <view class="label">看房福利</view>
        <my-input placeholder="请输入" size="big" v-model="params.welfare"></my-input>
      </view>
    </view>
    <view class="form_item">
      <view class="left">
        <view class="label">看房时间</view>
        <view class="flex-row date_row">
          <picker
            class="flex-3"
            mode="date"
            :value="dateStr"
            :start="startDate"
            :end="endDate"
            @change="onDateChange"
          >
            <view class="select">
              <text class="value" v-if="dateStr">{{ dateStr }}</text>
              <text class="placeholder" v-else>选择日期</text>
            </view>
          </picker>
          <picker
            class="flex-2"
            mode="time"
            :value="timeStart"
            start="08:00"
            end="22:00"
            @change="onStartTimeChange"
          >
            <view class="select text-center">
              <text class="value" v-if="timeStart">{{ timeStart }}</text>
              <text class="placeholder" v-else>开始时间</text>
            </view>
          </picker>
          <view style="padding: 0 12rpx; font-size: 36rpx; color: #999">~</view>
          <picker
            class="flex-2"
            mode="time"
            :value="timeEnd || timeStart"
            :start="timeStart"
            end="22:00"
            @change="onEndTimeChange"
          >
            <view class="select text-center">
              <text class="value" v-if="timeEnd">{{ timeEnd }}</text>
              <text class="placeholder" v-else>结束时间</text>
            </view>
          </picker>
        </view>
      </view>
      <icons type="jinrujiantou" size="32" color="#c0c0c0"></icons>
    </view>
    <view class="btn_box">
      <my-button
        block
        type="primary"
        size="big"
        :round="false"
        :loading="subming"
        @click="handleSubmit"
        >提交设置</my-button
      >
    </view>
  </view>
</template>

<script>
import myInput from './myInput'
import icons from '@/components/my-icon'
import myButton from './myButton'
export default {
  name: 'setShowing',
  components: {
    myInput,
    icons,
    myButton,
  },
  data() {
    return {
      dateStr: '',
      timeStart: '',
      timeEnd: '',
      subming: false,
      params: {
        welfare: '',
      },
    }
  },
  props: {
    house: {
      type: Object,
      default: () => {},
    },
    is_status: {
      type: [String, Number, Boolean],
      default: false, // true私盘，false联卖盘
    },
  },
  computed: {
    startDate() {
      return this.getDate('start')
    },
    endDate() {
      return this.getDate('end')
    },
  },
  created() {},
  methods: {
    getDate(type) {
      const date = new Date()
      let year = date.getFullYear()
      let month = date.getMonth() + 1
      let day = date.getDate()

      if (type === 'start') {
        year = year + 0
      } else if (type === 'end') {
        year = year + 1
      }
      month = month > 9 ? month : '0' + month
      day = day > 9 ? day : '0' + day
      return `${year}-${month}-${day}`
    },
    onDateChange(e) {
      this.dateStr = e.detail.value
    },
    onStartTimeChange(e) {
      this.timeStart = e.detail.value
      this.params.showing_stime = `${this.dateStr} ${this.timeStart}`
    },
    onEndTimeChange(e) {
      this.timeEnd = e.detail.value
      this.params.showing_etime = `${this.dateStr} ${this.timeEnd}`
    },
    handleSubmit() {
      this.subming = true
      if (this.is_status) {
        this.params.phid = this.house.id
      } else {
        this.params.id = this.house.id
      }
      this.$ajax.post('/v1/wapLm/setHouseFocus', this.params).then((res) => {
        this.subming = false
        if (res.data.status === 200) {
          this.$emit('success', res)
          uni.showToast({
            title: '设置成功',
            icon: 'success',
            mask: true,
          })
        } else {
          this.$emit('fail', res)
          uni.showToast({
            title: res.data.message,
            icon: 'none',
            mask: true,
          })
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
.form {
  border-top-left-radius: 18rpx;
  border-top-right-radius: 18rpx;
  padding: 24rpx 0;
  background-color: #fff;
  > .label {
    padding: 24rpx 48rpx;
    font-size: 32rpx;
    font-weight: bold;
    .tip {
      font-weight: initial;
      font-size: 24rpx;
      color: #999;
    }
  }
  .house_title {
    padding: 24rpx 48rpx;
    .text {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
  .form_item {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12rpx;
    padding: 18rpx 48rpx;
    > .left {
      flex: 1;
      position: relative;
      > .label {
        line-height: 1;
        margin-bottom: 16rpx;
        font-size: 22rpx;
        color: #8a929f;
      }
      .mask {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        z-index: 10;
      }
      .tip {
        font-size: 24rpx;
        color: #fe6c17;
      }
      .date_row {
        justify-content: space-between;
        .select {
          font-size: 36rpx;
        }
        .text-center {
          text-align: center;
        }
        .placeholder {
          color: #8a929f;
        }
      }
    }
    ::v-deep .icon-jinrujiantou {
      padding: 12rpx;
    }
    .input-group {
      .input-item {
        align-items: center;
        height: 40rpx;
        font-size: 26rpx;
        ~ .input-item {
          margin-left: 16rpx;
        }
        .label {
          margin-right: 12rpx;
        }
        input {
          line-height: 1;
          height: 36rpx;
          flex: 1;
        }
      }
    }
    .custom_row {
      padding: 16rpx 16rpx;
      .flex-row {
        align-items: center;
      }
    }
  }
  .btn_box {
    padding: 24rpx 48rpx;
  }
}
</style>
