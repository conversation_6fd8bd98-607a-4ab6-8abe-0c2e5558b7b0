<template>
  <view :class="{ padb: from != '' }">
    <view class="top">
      <view class="search row c2">
        手机号码
        <myIcon type="xiala" size="10px" style="margin-left: 10px"></myIcon>
        <span style="margin: 0 10px">|</span>
        <myIcon class="icon" type="ic_sousuo3x1" color="#8A929F" size="32rpx"></myIcon>
        <input
          type="text"
          v-model="params.mobile"
          @confirm="onSearch"
          placeholder="请输入手机号码搜索"
        />
      </view>
    </view>
    <view class="list">
      <myList :arr="client_list" @onClick="onClickDetail" type="my"></myList>
    </view>
    <load-more :status="load_status"></load-more>
    <view class="btn-bottom">
      <view class="btn-box row items-center">
        <view class="btn plain flex-1" @click="$navigateBack()">返回</view>
        <view
          class="btn flex-1"
          v-if="client_list && client_list.length && !client_list[0].follow_id"
          @click="onCreateData"
          >关联</view
        >
      </view>
    </view>
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
import loadMore from "@/components/loadMore.vue";
import myList from "./components/my_list";
export default {
  components: {
    myIcon,
    // tabBar,
    loadMore,
    myList,
  },
  data () {
    return {
      // source_list: [],
      type: "",
      params: {
        page: 1,
        form: 1, // 1:所有，2我的，3公海
        mobile: "",

      },

      load_status: "",
      client_list: [],
      from: '',
      user_id: ''
    };
  },
  onLoad (options) {
    let token = uni.getStorageSync("wxwork_token");
    if (!token) {
      if (this.$isWxWork() == 'wxwork') {
        return
      } else {
        localStorage.setItem('backUrl', location.href)
        this.$router.push("https://yun.tfcs.cn")
      }
      // 未登录中断请求
    }
    if (options.tel) {
      this.params.mobile = options.tel;
      this.getDataList();
    } else {
      this.load_status = "nomore";
    }
    if (options.from) {
      this.from = options.from
    }
    // TODO  
    // this.user_id = 'wm-VQJYQAABaPDlf4UPTMNqm40Rq5WXw' 测试关联客户用 
    if (["wxwork", "com-wx-pc"].includes(this.$isWxWork())) {
      this.getWxQyWxConfig(["agentConfig", 'getCurExternalContact'], wx => {
        this.wx = wx
        this.getDataDetail()
        if (this.params.mobile) {
          this.getDataList();
        }
      })
    } else {
      if (this.params.mobile) {
        this.getDataList();
      }
    }

  },
  methods: {
    onSearch () {
      this.params.page = 1;
      this.getDataList();
    },
    getDataDetail () {
      var _this = this
      this.wx.invoke("getCurExternalContact", {}, function (res) {
        if (res.err_msg == "getCurExternalContact:ok") {
          _this.user_id = res.userId;

        } else {
          console.log(res);
        }
      });
    },
    getDataList () {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.client_list = [];
      }
      this.$ajax.get("/qywx/client/search", this.params, (res) => {
        this.load_status = "loadend";
        if (res.statusCode === 200) {
          this.client_list = this.client_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        }
      });
    },
    onClickDetail (item) {
      this.$navigateTo(
        `/customer/detail?id=${item.id}&form=${this.params.form}`
      );
    },
    onClickPost () {
      this.$navigateTo("uphold?type=1");
    },

    onCreateData () {
      uni.showModal({
        title: "提示",
        content: `是否关联该客户？`,
        success: (res) => {
          if (res.confirm) {
            this.subForm()
          }
        },
      });

    },
    subForm () {
      let client_id = this.client_list.length ? this.client_list[0].id : ''
      if (!client_id) {
        uni.showToast({
          title: "客户id不能为空",
          icon: "none",
        })
        return
      }
      if (this.subing) return
      this.subing = true
      this.$ajax.post("/qywx/client/bind", { client_id, openid: this.user_id }, res => {
        console.log(res);
        if (res.statusCode == 200) {
          uni.showToast({
            title: res.data.message || "关联成功",
            icon: "none"
          })
        } else {
          uni.showToast({
            title: res.data.message || "关联失败",
            icon: "none"
          })
        }
        this.subing = false
      }, () => {
        this.subing = false
      })
    }
  },
  onReachBottom () {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>
<style scoped lang="scss">
page {
  background: #f6f6f6;
  color: #2e3c4e;
}
.c2 {
  color: #8a929f;
}
.list {
  padding: 12px;
  .info {
    background: #fff;
    border-radius: 12px;
    padding: 12px;
    margin-bottom: 12px;
    .claim {
      text-align: center;
      height: 40px;
      line-height: 40px;
      font-weight: 500;
      margin: 12px 0;
      border-radius: 6px;
      &.c1 {
        background: #eaf3ff;
        color: #2d84fb;
      }
      &.c2 {
        color: #eaf3ff;
        background: #2d84fb;
      }
    }
    .content {
      > text {
        line-height: 20px;
      }
    }
    .type {
      margin: 12px 0;
      line-height: 14px;
      text {
        font-size: 11px;
        margin-right: 24px;
      }
    }
    .pers {
      align-items: center;
      justify-content: space-between;
      .time {
        font-size: 11px;
      }
      .left {
        align-items: center;
        .level {
          width: 16px;
          height: 16px;
          line-height: 16px;
          text-align: center;
          color: #fff;
          font-weight: 500;
          font-size: 11px;
          background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
          border-radius: 2px;
        }
        .name {
          font-weight: 500;
        }
        text {
          margin-left: 12px;
        }
        .pic {
          width: 25px;
          height: 25px;
          border-radius: 50%;
        }
        .qw {
          width: 16px;
          margin-left: 12px;
        }
      }
    }
  }
}
.ent {
  display: inline-block;
  position: fixed;
  right: 10px;
  bottom: 150px;
  image {
    width: 80px;
    height: 80px;
  }
}
.sort {
  align-items: center;
  justify-content: space-between;
}
.top {
  background: #fff;
  padding: 6px 16px 0 16px;
  .search {
    background: #f6f6f6;
    border-radius: 6px;
    align-items: center;
    padding: 10px;
    input {
      font-size: 14px;
      margin-left: 12px;
      flex: 1;
    }
  }
}
.second_tab {
  padding: 24rpx;
  position: sticky;
  top: 200rpx;
  z-index: 6;
  background: #fff;
  justify-content: space-between;
  .second_tab_item {
    max-width: 20%;
    color: #282a2f;
    .item_name {
      font-size: 28rpx;
      margin-right: 8rpx;
      color: #8a929f;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
    }
    .sanjiao {
      margin-top: 10rpx;
      width: 0;
      height: 0;
      border: 13rpx solid;
      border-color: #d8d8d8 transparent transparent transparent;
    }
  }
  .second_tab_con {
    position: absolute;
    top: 80rpx;
    left: 0;
    right: 0;
    z-index: 5;
    // padding: 0 48rpx;
    margin-left: -16px;
    margin-right: -16px;
    background: #fff;
    .second_tab_con_i {
      background: #fff;
      padding: 0 48rpx;
      max-height: 60vh;
      overflow-y: auto;
    }
    .second_tab_con_item {
      padding: 20rpx 0;
      &_title {
        margin: 10px 0;
        font-size: 16px;
      }
      &_list {
        flex-wrap: wrap;
        margin-bottom: 150px;
        .timebox {
          justify-content: space-between;
          .placls {
            font-size: 12px;
          }
          input {
            height: 35px;
            padding: 4px 10px;
            width: 80px;
            border-radius: 5px;
            border: 1px solid #eeeeee;
            text-align: center;
          }
        }
        .zhi {
          text-align: center;
          margin: 15px 0;
        }
        &_item {
          width: 30%;
          text-align: center;
          line-height: 35px;
          border-radius: 5px;
          margin: 2px 1.66%;
          border: 1px solid #eeeeee;
          &.isactive {
            border-radius: 5px;
            background: #f3f7fe;
            border: 1px solid #3172f6;
            color: #3172f6;
          }
        }
      }
      &_left {
        width: 50%;
        background: #fff;
        margin: 0 20px;
      }
      &_right {
        background: #f9f9f9;
        margin: -12px 0;
        line-height: 40px;
        padding-left: 24px;
        padding-top: 12px;
        .lab-list {
          line-height: 40px;
          align-items: center;
          justify-content: space-between;
          .lab-item {
            &.active {
              color: #3172f6;
            }
          }
          .ischeck {
            margin-right: 24px;
            width: 14px;
            height: 14px;
          }
        }
      }
    }
    .btn_group {
      min-height: 64px;
      box-shadow: 0 -3px 5px 0 #f1f1f1;
      padding: 12px 24px;
      margin-left: -24px;
      margin-right: -24px;
      .btn {
        text-align: center;
        width: 30%;
        border-radius: 3px;
        background: #e5eeff;
        color: #3172f6;
        line-height: 40px;
        &.bg_highlight {
          margin-left: 10px;
          background-color: $color-primary;
          width: 70%;
          color: #fff;
        }
      }
    }
    .mask {
      position: fixed;
      z-index: -1;
      width: 100%;
      height: 100vh;
      left: 0;
      background-color: #000;
      opacity: 0.5;
      transition: 0.26s;
    }
  }
}
.filter_list {
  overflow: hidden;
  .filter_labels {
    font-size: 16px;
    font-size: 13px;
    padding-left: 24px;
    line-height: 40px;
    &.active {
      color: #3172f6;
    }
  }
}
.claim {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  height: 40px;
  line-height: 40px;
  font-weight: 500;
  margin: 12px 0;
  border-radius: 6px;
  background: #3172f6;
  color: #fff;
}
.padb {
  padding-bottom: 100px;
}

.btn-bottom {
  height: 100px;
  .btn-box {
    background: #fff;
    justify-content: space-between;
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 24px;
    .btn {
      height: 42px;
      width: 100%;
      line-height: 42px;
      text-align: center;
      color: #fff;
      background: #2d84fb;
      border-radius: 6px;
    }
    .plain {
      background: #fff;
      color: #333;
      border: 1px solid #999;
      margin-right: 20px;
    }
  }
}
</style>
