<template>
    <view class="search-select">
        <wisdomSearchSelect :placeholder="placeholder" :label="label" v-model="isSeled" @open="open" @clear="clear"></wisdomSearchSelect>
        <tDepartmentPicker :visible.sync="visible" v-model="dept_id" @confirm="confirm"></tDepartmentPicker>
    </view>
</template>

<script>
import wisdomSearchSelect from './wisdomSearchSelect';
import tDepartmentPicker from '@/components/tplus/tDepartmentPicker';
export default {
    props: {
        value: { type: [ String, Number ], default: false },
        placeholder: { type: String, default: '请选择' },
    },
    components: {
        wisdomSearchSelect,
        tDepartmentPicker
    },
    data(){
        return {
            visible: false,
            label: '',
            dept_id: ''
        }
    },
    computed: {
        isSeled(){
            return this.dept_id !== '';
        }
    },
    watch: {
        dept_id(val){
            this.$emit('input', val)
        }
    },
    methods: {
        confirm(e){
            this.visible = false;
            this.label = e? e.label[e.label.length - 1] : '';
            this.$nextTick(()=>{
                this.$emit('select')
            })
        },
        open(){
            this.visible = true;
        },
        clear(){
            this.dept_id = '';
            this.$nextTick(()=>{
                this.$emit('select')
            })
        }
    }
}
</script>
<style lang="scss" scoped>

</style>