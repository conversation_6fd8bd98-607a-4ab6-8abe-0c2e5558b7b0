.top {
  margin: 12px;
  padding: 0 12px 12px 12px;
  background: #fff;
  border-radius: 6px;
  .tag {
    flex-wrap: wrap;
    text {
      background: #f1f4fa;
      border-radius: 6px;
      margin-right: 12px;
      font-size: 11px;
      padding: 6px 18px;
      margin-top: 12px;
    }
  }
  .tag-box {
    .loadmore {
      text-align: center;
      color: #2d84fb;
    }
  }
  .tag_input {
    margin-top: 12px;
    // border: 1px solid #dde1e9;
    border-radius: 4px;
    align-items: center;
    padding: 9px 12px;
    flex-wrap: wrap;
    .tag_item {
      padding: 6px 16px;
      margin-right: 4px;
      margin-bottom: 5px;
      background: #f1f4fa;
      &.checked {
        background: #e8f1ff;
        color: #2d84fb;
      }
    }
    input {
      width: 100%;
      font-size: 14px;
    }
  }
  .btn-box {
    justify-content: flex-end;
  }
  .btn {
    margin-top: 12px;
    color: #2d84fb;
  }
  .type {
    text {
      margin-top: 12px;
    }
  }
  .content {
    margin-top: 12px;
    > text {
      line-height: 20px;
    }
  }
  .tit {
    margin: 12px 0;
    font-size: 16px;
    font-weight: 500;
  }
  .pers {
    align-items: center;
    .level {
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      color: #fff;
      font-weight: 500;
      font-size: 11px;
      background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
      border-radius: 2px;
    }
    .name {
      font-weight: 500;
      font-size: 16px;
    }
    text {
      margin-left: 12px;
    }
    .pic {
      width: 25px;
      height: 25px;
      border-radius: 50%;
    }
    .qw {
      width: 16px;
      margin-left: 12px;
    }
  }
}
page {
  background: #f6f6f6;
  color: #2e3c4e;
}
.c2 {
  color: #8a929f;
}
