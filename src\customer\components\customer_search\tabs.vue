<template>
<view class="tabs">
    <tabBar :tabs="list" :fixed-top="false" :equispaced="false" :now-index="curTabIndex" @click="handleTabClick"></tabBar>
</view>
</template>

<script>
import tabBar from '@/components/tabBar.vue';
export default {
    props: {
        value: { type: String, default: '' },
        current: { type: String, default: 'my' },
    },
    components: {
        tabBar
    },
    data() {
        return {
            curTabIndex: 0,         //当前选中的tab索引
            list: []                //tabs 数据
        }
    },
    watch: {
        curTabIndex: {
            handler(val) {
                let tab = this.list.find(e => e.index === val);
                this.$emit('input', tab ? tab.name : '');
            },
            immediate: true
        },
    },
    mounted() {
        //tabs 数据
        this.list = this.getTabs().map( (e, index) => {
            e.index = index;
            return e;
        });

        this.$watch('value', {
            handler(val) {
                if(val === ''){
                    this.$emit('input', this.list[0]?.name);
                }else{
                    let tab = this.list.find(e => e.name === val);
                    this.curTabIndex = tab ? tab.index : 0;
                }
            },
            immediate: true
        })
    },
    methods: {
        /**
         * 获取 tabs 数据
         */
        getTabs(){
            let mobile = {description:'手机号', name: 'mobile'},
                number = {description:'客户编号', name: 'number'},
                cname = {description:'客户姓名', name: 'cname'},
                keywords = {description:'线索内容', name: 'keywords'},
                follow_keywords = {description:'跟进内容', name: 'follow_keywords'},
                department = {description:'部门', name: 'department_id'},
                admin = {description:'成员', name: 'admin_id'};
            switch(this.current){
                case 'my':
                case 'my_trader':
                case 'trans':
                    return [ mobile, number, cname, keywords, follow_keywords ];
                default:
                    return [ mobile, number, cname, keywords, follow_keywords, department, admin ];
            }
        },
		handleTabClick(e){
            if(this.curTabIndex !== e.index){
                this.curTabIndex = e.index;
                this.$nextTick(()=>{
                    this.$emit('change', e);
                })
            }
		}
    }
}
</script>

<style scoped lang="scss"> 
::v-deep .nav-box{
    .nav-list{
        height: 88rpx;
        .nav-item {
            width: auto;
            padding: 0 32rpx;
            margin: 0;
            height: 100%;
            display: inline-flex;
            justify-content: center;
            align-items: center;
            &.active {
                color: #007aff;
                &:before{
                    height: 6rpx;
                    width: 32rpx;
                }
            }
        }
    }
}
</style>