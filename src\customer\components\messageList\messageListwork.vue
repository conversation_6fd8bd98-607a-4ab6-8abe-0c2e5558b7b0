<template>
  <view>
    <view class="total flex-row items-center">
      <view class="list" v-for="item in workList" :key="item.id">
        <view class="info" @click="workFN(item.location)">
          <image :src="item.icon" class="image-gh"></image>
          <view
            class="text-gh"
            style="margin-left: 24rpx; width: 159rpx; margin-top: 8rpx"
          >
            <text
              style="
                color: black;
                font-size: 32rpx;
                font-weight: 500;
                font-style: normal;
                margin-bottom: 24rpx;
              "
              >{{ item.name }}</text
            >
            <view style="display: flex; flex-direction: row; flex-wrap: nowrap">
              <view style="color: rgba(41, 44, 57, 0.4); font-size: 26rpx"
                >{{ item.desc }}
              </view>
              <text
                style="color: #fb6c2a; font-size: 24rpx; padding-left: 14rpx"
                >{{ item.number === "" ? "" : "+" + item.number }}</text
              >
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    workList: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    workFN(location) {
      if (location === "" || location === '/customer/examineApprove' ) {
        uni.showToast({
          title: "暂未开放",
          icon: "none",
        });
      }
      // else if(location ==='/outbound/myCall'){
      //   uni.showToast({
      //     title: "更新中...",
      //     icon: "none",
      //   });
      // }
       else {
        this.$navigateTo(location);
      }
    },
  },
};
</script>
<style lang="scss">
.imgs {
  margin-top: 190rpx;
  margin-bottom: 48rpx;
  display: flex;
  flex-direction: row;
  background-color: aqua;
}

.text {
  color: #292c39;
  font-family: PingFang SC;
  margin-left: 24rpx;
  font-size: 36rpx;
  font-weight: 500;
}

.whole {
  padding: 0 32rpx;
}

.title {
  color: rgba(41, 44, 57, 0.7);
  margin-bottom: 24rpx;
  font-family: PingFang SC;
  font-size: 28rpx;
  font-style: normal;
  font-weight: 400;
  line-height: 36rpx;
  /* 128.571% */
}

.info {
  display: flex;
  flex-direction: row;
  //
}

.list {
  // width: 327rpx;
  width: calc((100vw - 64rpx - 32rpx) / 2);
  height: 144rpx;
  margin: 0 32rpx 32rpx 0;
  padding: 24rpx;
  background-color: #fff;
  border-radius: 16rpx;
  &.list:nth-child(2n) {
    margin-right: 0;
  }
}

.image-gh {
  width: 96rpx;
  height: 96rpx;
}

.total {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
</style>
