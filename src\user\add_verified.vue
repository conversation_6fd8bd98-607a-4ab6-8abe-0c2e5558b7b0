<template>
  <view class="list">
    <view v-if="next_step === false">
      <view class="top-title">个人信息</view>
      <view class="form">
        <view class="input-box">
          <input
            @input="onInput"
            type="text"
            class="uni-input"
            placeholder-class="uni-placeholder"
            v-model="form.name"
            placeholder="真实姓名"
          />
        </view>
        <view class="input-box row input-bank" @click="showCertificate">
          <input
            disabled="true"
            type="text"
            class="uni-input"
            v-model="certificate_type"
            placeholder="证件类型"
          />
          <myIcon type="you" size="20rpx"></myIcon>
        </view>
        <view class="input-box">
          <input
            placeholder-class="uni-placeholder"
            type="text"
            class="uni-input"
            v-model="form.credentials_number"
            placeholder="证件号码"
          />
        </view>
        <view class="input-box row input-bank" @click="selectTime">
          <input
            type="text"
            class="uni-input"
            placeholder="有效期开始时间"
            v-model="form.start_valid_at"
          />
          <myIcon type="you" size="20rpx"></myIcon>
        </view>
        <view class="input-box row input-bank" @click="selectTimeEnd">
          <input
            type="text"
            class="uni-input"
            placeholder="有效期结束时间"
            v-model="form.end_valid_at"
          />
          <myIcon type="you" size="20rpx"></myIcon>
        </view>
        <view :class="{ submit: disabled }" class="btn" @click="nextStep"
          >下一步</view
        >
      </view>
      <!-- 弹出选择框 -->
      <VuePicker
        :data="pickDataCer"
        title="请选择证件类型"
        cancelText="取消"
        confirmText="确认"
        :showToolbar="true"
        @cancel="cancelCer"
        @confirm="confirmCer"
        :visible.sync="pickerVisibleCer"
      />
      <timePicker mode="YMD" ref="picker" @confirm="confirm"></timePicker>
      <timePicker
        end="2500-12-30"
        mode="YMD"
        ref="pickerEnd"
        @confirm="confirmEnd"
      ></timePicker>
    </view>
    <view class="_list" v-else>
      <view class="top-title row">
        <text>证件上传</text
        ><text class="back" @click="next_step = false">返回</text></view
      >
      <view class="upload-box" @click="uploadZ">
        <text class="title">身份证正面</text>
        <view class="img-box" v-if="form.img_front === ''">
          <image
            class="zhengmian"
            src="https://img.tfcs.cn/static/img/zhengmian.png"
          ></image>
          <view class="icon-baobei icon-baobei-tianjia_w"></view>
        </view>
        <view class="img-box" v-else>
          <image style="width:100%" :src="form.img_front"></image>
        </view>
      </view>
      <view class="upload-box" @click="uploadB">
        <text class="title">身份证背面</text>
        <view class="img-box" v-if="form.img_back === ''">
          <image
            class="beimian"
            src="https://img.tfcs.cn/static/img/beimian.png"
          ></image>
          <view class="icon-baobei icon-baobei-tianjia_w"></view>
        </view>
        <view class="img-box" v-else>
          <image style="width:100%" :src="form.img_back"></image>
        </view>
      </view>
      <view class="btn" @click="onSubmit">提交审核</view>
    </view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import config from "@/page_outside/config/index";
import VuePicker from "vue-pickers";
import myIcon from "@/components/my-icon";
import timePicker from "@/components/time-picker";
export default {
  components: { myIcon, VuePicker, timePicker },
  data() {
    return {
      form: {
        name: "",
        credentials_number: "",
        credentials_category: "",
        start_valid_at: "",
        end_valid_at: "",
        img_front: "",
        img_back: "",
      },
      pickDataCer: [],
      pickerVisibleCer: false,
      certificate_type: "",
      time: "",
      next_step: false,
      disabled: false,
    };
  },
  onLoad() {
    this.init();
  },
  methods: {
    init() {
      this.$getDictionaryList("CREDENTIALS_CATEGORY", {}, (res) => {
        if (res.statusCode === 200) {
          var arr = res.data.data.map((item) => {
            return {
              value: item.value,
              label: item.description,
            };
          });
          this.pickDataCer.push(arr);
        }
      });
    },
    nextStep() {
      // this.$navigateTo("/user/upload_card");
      this.next_step = true;
    },
    showCertificate() {
      this.pickerVisibleCer = true;
    },
    cancelCer() {},
    confirmCer(res) {
      res.map((item) => {
        this.certificate_type = item.label;
        this.form.credentials_category = item.value;
      });
    },
    selectTime() {
      this.$refs.picker.show();
    },
    confirm(e) {
      this.form.start_valid_at = e.result;
    },
    selectTimeEnd() {
      this.$refs.pickerEnd.show();
    },
    confirmEnd(e) {
      this.form.end_valid_at = e.result;
    },
    onInput() {
      this.disabled = this.form.name ? true : false;
    },
    uploadZ() {
      let baseURL = config.appApi;
      uni.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"], //original 原图，compressed 压缩图，默认二者都有
        success: (res) => {
          // console.log(res.tempFilePaths[0]); //成功则返回图片的本地文件路径列表 tempFilePaths
          const uploadTask = uni.uploadFile({
            //将本地资源上传到开发者服务器
            url: baseURL + "/common/file/upload/client", //接口地址
            filePath: res.tempFilePaths[0], //图片地址
            header: {
              Authorization:
                "Bearer " +
                uni.getStorageSync("token" + this.$store.state.website_id),
            },
            name: "file",
            formData: { category: "12" },
            success: (res) => {
              if (res.statusCode == 200) {
                uni.showToast({
                  title: "上传成功",
                });
                let data = JSON.parse(res.data);
                this.form.img_front = data.url;
              } else {
                uni.showToast({
                  title: res.data.message || "上传失败",
                  icon: "none",
                });
              }
            },
          });
          uploadTask.onProgressUpdate((res) => {
            uni.showLoading({
              title: "正在上传" + res.progress + "%",
            });
          });
        },
      });
    },
    uploadB() {
      let baseURL = config.appApi;
      uni.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"], //original 原图，compressed 压缩图，默认二者都有
        success: (res) => {
          // console.log(res.tempFilePaths[0]); //成功则返回图片的本地文件路径列表 tempFilePaths
          const uploadTask = uni.uploadFile({
            //将本地资源上传到开发者服务器
            url: baseURL + "/common/file/upload/client", //接口地址
            filePath: res.tempFilePaths[0], //图片地址
            header: {
              Authorization:
                "Bearer " +
                uni.getStorageSync("token" + this.$store.state.website_id),
            },
            name: "file",
            formData: { category: "12" },
            success: (res) => {
              if (res.statusCode == 200) {
                uni.showToast({
                  title: "上传成功",
                });
                let data = JSON.parse(res.data);
                this.form.img_back = data.url;
              } else {
                uni.showToast({
                  title: res.data.message || "上传失败",
                  icon: "none",
                });
              }
            },
          });
          uploadTask.onProgressUpdate((res) => {
            uni.showLoading({
              title: "正在上传" + res.progress + "%",
            });
          });
        },
      });
    },
    onSubmit() {
      var rules = {
        img_back: "请选择背面证件照片",
        img_front: "请选择正面证件照片",
        credentials_category: "请选择证件类型",
        end_valid_at: "请选择证件结束日期",
        start_valid_at: "请选择证件开始日期",
        credentials_number: "请输入证件号码",
        name: "请输入姓名",
      };
      for (var key in this.form) {
        if (!this.form[key]) {
          uni.showToast({
            title: rules[key],
            icon: "none",
          });
          return;
        }
      }
      this.$ajax.post("/client/user/real_name/put", this.form, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "上传成功",
          });
          setTimeout(() => {
            uni.switchTab({
              url: "/",
            });
          }, 1000);
        } else {
          uni.showToast({
            title: res.data.message || "上传失败",
            icon: "none",
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.list {
  .uni-placeholder {
    color: #999;
  }
  padding: 24rpx 48rpx;
  .top-title {
    font-size: 40rpx;
    color: #333333;
  }
  .form {
    width: 100%;
    margin-top: 24rpx;
    .input-box {
      border-radius: 8rpx;
      padding: 20rpx 24rpx;
      margin: 10rpx 0;
      background: #f3f3f3;
      height: 80rpx;
      .uni-input {
        font-size: 28rpx;
        color: #333;
        width: 100%;
        height: 80rpx;
      }
    }
    .input-bank {
      align-items: center;
      justify-content: space-between;
    }
    .btn {
      margin-top: 64rpx;
      align-items: center;
      opacity: 0.6;
      background: #0174ff;
      padding: 30rpx;
      border-radius: 22px;
      color: #fff;
    }
  }
}

._list {
  padding: 24rpx 48rpx;
  .top-title {
    font-size: 40rpx;
    color: #333;
    justify-content: space-between;
    align-items: center;
    .back {
      font-size: 28rpx;
      color: #fff;
      background: #0174ff;
      padding: 10rpx;
      border-radius: 10rpx;
    }
  }
  .upload-box {
    margin-top: 40rpx;
    align-items: center;
    .title {
      margin-bottom: 24rpx;
    }
    .img-box {
      position: relative;
      width: 100%;
      height: 320rpx;
      background: rgba(0, 115, 255, 1);
    }
  }
  .btn {
    margin-top: 60rpx;
    align-items: center;
    color: #fff;
    padding: 30rpx;
    background: #0174ff;
    box-shadow: 0 4px 16px 0 rgba(1, 116, 255, 0.4);
    border-radius: 22px;
  }
}
.zhengmian {
  width: 400rpx;
  height: 240rpx;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.icon-baobei-tianjia_w {
  background: url(data:image/svg+xml,%3C%3Fxml%20version%3D%221.0%22%20encoding%3D%22utf-8%22%3F%3E%3Csvg%20version%3D%221.1%22%20width%3D%27100%25%27%20height%3D%27100%25%27%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20id%3D%22icon-baobei-tianjia_w%22%20viewBox%3D%220%200%201024%201024%22%3E%3Cpath%20d%3D%22M512%20512m-512%200a512%20512%200%201%200%201024%200%20512%20512%200%201%200-1024%200Z%22%20fill%3D%22%23FFFFFF%22%20%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M554.666667%20298.666667v170.645333L725.333333%20469.333333v85.333334h-170.666666v170.666666h-85.333334v-170.666666h-170.666666v-85.333334h170.666666v-170.666666h85.333334z%22%20fill%3D%22%230174FF%22%20%3E%3C%2Fpath%3E%3C%2Fsvg%3E);
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 96rpx;
  height: 96rpx;
}
.beimian {
  width: 400rpx;
  height: 240rpx;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.submit {
  background: #0174ff;
  opacity: 1 !important;
  box-shadow: 0 4px 16px 0 rgba(1, 116, 255, 0.4);
}
</style>
