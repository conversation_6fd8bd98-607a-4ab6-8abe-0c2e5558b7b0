<template>
  <view class="tag-box">
    <view class="direction_tag">
        <view>已选：</view>
        <view :class="choose.length != 0 ? 'active' :'clack'">
          {{ choose.length }} 
        </view >
        <view class="choose"> / </view>
       <view>{{ num }}</view>
      </view>
    <block v-for="(item, index) in tag_list" :key="index">

  <view class="l-title">{{ item.name }}</view>

      <view class="tag_input row">
        <block v-for="(item1, index1) in item.label" :key="index1">
          <view
            class="tag_item"
            @click="onClickTags(item1.id)"
            :class="{ checked: choose.includes(item1.id) || choose.includes(item1.id + '') }"
            :key="item1.id"
          >

            {{ item1.name }}
          </view>
        </block>
      </view>
    </block>
 <view style="padding: 48rpx 24rpx;background-color: #fff; width: 100%;   position: fixed; bottom: 0;left: 12rpx;">
  <view v-if="!from"  class="claim" @click="onCreateLabels">确认</view>
 </view>
    <view v-if="from" class="foot row">
      <view class="c2" @click="$navigateBack()">取消</view>
      <view @click="onCreateLabels">确定</view>
    </view>
  </view>
</template>

<script>
export default {
  data () {
    return {
      tag_list: [],
      form_info: {
        client_id: "",
        label: ""
      },
      detail: {},
      choose: [],
      from: '',
      labelArray:[],
      num:0,
      current: 'my'
    };
  },
  computed: {
    isTrans(){
      return this.current === 'trans';
    }
  },
  onLoad (options) {
    if (options.from) {
      this.from = options.from
    }
    this.form_info.client_id = options.id;
    this.current = options.current || 'my';
    this.getCustomerDetail()
  },
  methods: {
    onClickTags (id) {
      // console.log(id,'123456');
      if (this.choose.includes(id)) {
        let indexex = this.choose.indexOf(id);
        this.choose.splice(indexex, 1);
        // console.log( this.choose.splice(indexex, 1),'this.choose');
      } else {
        this.choose.push(id);
      }

    },
    getCustomerDetail () {
      let url = `/qywx/client/info/${this.form_info.client_id}`
      if(this.isTrans){
        url = `/admin/private_client/info/${this.form_info.client_id}`
      }

      this.$ajax.get(url, {}, (res) => {
        console.log(res);
        if (res.statusCode === 200) {
          this.detail = res.data
          let choose = []
          if (this.detail.label) {
            choose = (this.detail.label + '').split(',')
          }

          for (let index = 0; index < choose.length; index++) {
            const element = choose[index];
            this.choose[index] = +element
          }
          // this.choose.map(item => {
          //   return +item
          // })
          console.log(this.choose);
          this.getTagList();
        } else {
          this.getTagList();
        }
      }, () => {
        this.getTagList();
      });
    },
    getTagList () {
      this.$ajax.get("/qywx/tag/search", {}, (res) => {
        if (res.statusCode === 200) {
          let arr = res.data.qiwei_tag.map((item) => {
            return {
              id: item.id,
              name: item.name,
              label: item.taggroup,
            };
          });
          this.tag_list = arr.concat(res.data.system_tag);
          // console.log(this.tag_list,'////');
        this.num =  this.tag_list.reduce((prev,cur)=>{
            return prev+cur.label.length
          },0)
          // let num = 0
          // for(const tag of this.tag_list){
          //     num += tag.label.length
          // }
          // console.log(num);
        }
      });
    },
    onCreateLabels () {
      this.form_info.label = this.choose.join(",");
      console.log(this.form_info.label,'ididididididid');
      let url = "/qywx/client/update_tag";
      if(this.isTrans){
        url = '/admin/private_client/update_tag'
      }
      this.$ajax.post(url, this.form_info, (res) => {
        if (res.statusCode === 200) {
          uni.$emit("getDataAgain")
          setTimeout(() => {
            this.$navigateBack()
          }, 300);
          // this.$navigateTo(
          //   `/customer/detail?id=${this.form_info.client_id}&form=2`
          // );
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      }, (err) => {
        uni.showToast({
          title: err?.data?.message || '提交失败',
          icon: "none",
        });
      });
    },
    findArrDiff (oldValue, newValue) {
      let set;
      let diff;
      let type;
      if (newValue.length > oldValue.length) {
        console.log("add");
        type = "add";
        set = new Set(oldValue);
        diff = newValue.filter((v) => !set.has(v));
      } else {
        console.log("delete");
        type = "delete";
        set = new Set(newValue);
        diff = oldValue.filter((v) => !set.has(v));
      }
      return { diff, type };
    },
  },
};
</script>

<style scoped lang="scss">
.tag_name{

  display: flex;
   flex-direction: row;
   align-items: center;
   justify-content: space-between;
   color: rgba(41, 44, 57, 0.40);
font-size: 28rpx;
}
.direction_tag{
  position: absolute;
  top: 4rpx;
  right: 30rpx;
  display: flex;
  flex-direction: row; 
  align-items: center; 
  color: rgba(41, 44, 57, 0.40);
  font-size: 28rpx;
}
.active{
  color: #2d84fb;
}
.clack{
  color: rgba(41, 44, 57, 0.40);
}
.choose{
  margin: 0 16rpx;
}
.tag-box {
  padding: 0 32rpx 200rpx;
  margin-top: 48rpx;
  .loadmore {
    text-align: center;
    color: #2d84fb;
  }
}
.l-title {
  position: relative;
  color: #292C39;
font-size: 36rpx;
font-weight: 500;
}
.tag_input {
  margin-top: 36rpx;
  // border: 1px solid #dde1e9;
  border-radius: 4px;
  align-items: center;
  // padding: 9px 12px;
  flex-wrap: wrap;
  width: 100%;
  .tag_item {
   
    text-align: center;
padding: 0 16rpx;
height: 64rpx;
line-height: 64rpx;
    margin-bottom: 24rpx;
    background: #F8F8F8;
    border-radius: 8px;
    border: 2rpx solid #eee;
    color: #8d9099;
    position: relative;
    overflow: hidden;
margin-right: 24rpx;
    &.checked {
color: #488AF6;
font-size: 28rpx;
      border: 2rpx solid #488AF6;
background: rgba(72, 138, 246, 0.10);
    }
    .checked {
      position: absolute;
      right: 0;
      top: 0;
      width: 32rpx;
      height: 24rpx;
      image {
        width: 100%;
        height: 100%;
      }
    }
  }
  input {
    width: 100%;
    font-size: 14px;
  }
}
.claim {
  text-align: center;
  width: 90%;
  height: 80rpx;
  line-height: 80rpx;
  font-weight: 500;
  margin: 24rpx 0;
  border-radius: 12rpx;
  background: #3172f6;
  color: #fff;
  margin-left: 24rpx;

}
.foot {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 12px 12px 40px 12px;
  justify-content: space-between;
  view {
    width: 48%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 6px;
    border: 1px solid #2d84fb;
    color: #fff;
    background: #2d84fb;
    font-weight: 500;
    &.c2 {
      border: 1px solid #dde1e9;
      background: #fff;
      color: #8a929f;
    }
  }
}
</style>
