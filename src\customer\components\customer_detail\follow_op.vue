<template>
    <view>
        <myPopup :show="show" @hide="show = false">
            <view class="popup-contianer">
                <view class="item" v-for="item in currentList" :key="item.name" @click="handleItemClick(item)">
                    {{ item.title }}
                </view>
            </view>
        </myPopup>

        <replyFollowComment :visible.sync="dialogs.replyComment" :followId="followId" @success="handleReplyFollowSuccess"></replyFollowComment>
    </view>
</template>

<script>
import { setCrmFollowTop, setCrmFollowPraise } from '@/common/utils/customer.js';
import myPopup from '@/components/myPopup';
import replyFollowComment from '@/components/customer/replyFollowComment.vue';
export default {
    props: {
        current: { type: String, default: 'my' },
        visible: { type: Boolean, default: false },
        detail: { type: Object, default: ()=>({}) },
        customer: { type: Object, default: ()=>({}) },
        hasRole: { type: Boolean, default: true },
        
    },
    components: {
        myPopup, replyFollowComment
    },
    data() {
        return {
            show: false,
            list: [],
            dialogs: {
                replyComment: false
            },
            setToping: false,
            setPraising: false,
        }
    },
    computed: {
        followId(){
            return this.detail ? this.detail.id : 0;
        },
        isTop(){
            return this.detail ? this.detail.order == 1 : false;
        },
        isPraise(){
            return this.detail ? this.detail.is_top == 1 : false;
        },
        currentList(){
            if(this.isQywxBinded){
                return this.list.filter(e => e.name !== 'bindQywx');
            }
            return this.list;
        }
    },
    watch: {
        visible(val){
            this.show = val;
        },
        show(val){
            this.$emit('update:visible', val)
        },
        isTop(){
            this.list = this.list.map( e => {
                if(e.name === 'setTop'){
                    e.title = this.isTop ? '取消置顶' : '设为置顶';   
                }
                return e;
            });
        },
        isPraise(){
            this.list = this.list.map( e => {
                if(e.name === 'setPraise'){
                    e.title = this.isPraise ? '取消点赞' : '点赞';   
                }
                return e;
            });
        }
    },
    created() {
        this.list = this.getList();
    },
    filters: {
        
    },
    methods: {
        getList(){
            const setTop = { title: '设为置顶', name: 'setTop' }
            const setPraise  = { title: '点赞', name: 'setPraise' }
            const copyContent  = { title: '复制内容', name: 'copyContent' }
            const replyComment  = { title: '批注回复', name: 'replyComment' }
            return [ setTop, setPraise, copyContent, replyComment ];
        },
        handleItemClick(item){
            this[item.name]();
        },
        
        //置顶
        async setTop(){
            if(this.setToping){
                return;
            }
            this.setToping = true;
            try{
                const res = await setCrmFollowTop(this.detail.id);
                this.show = false;
                this.$emit('refresh');
                uni.showToast({
                    title: res && res.msg ? res.msg : '操作成功',
                    icon: 'none',
                });
            }catch(e){}
            this.setToping = false;
        },
        //点赞
        async setPraise(){
            if(this.setPraising){
                return;
            }
            this.setPraising = true;
            try{
                const res = await setCrmFollowPraise(this.detail.id);
                this.show = false;
                this.$emit('refresh');
                uni.showToast({
                    title: res && res.msg ? res.msg : '操作成功',
                    icon: 'none',
                });
            }catch(e){}
            this.setPraising = false;
        },
        //复制内容
        copyContent(){
            let contents = this.detail.content;
            this.$copyText(contents, () => {
                this.show = false;
                uni.showToast({
                    title: '复制成功',
                    icon: "none",
                });
            })
        },
        //批注回复
        replyComment(){
            this.show = false;
            this.dialogs.replyComment = true;
        },
        handleReplyFollowSuccess(){
            this.$emit('refresh');
        }
    }
        
}

</script>
    
<style scoped lang="scss"> 
.popup-contianer {
    border-radius: 32rpx 32rpx 0px 0px;
    background: #fff;
    padding: 64rpx 0;
    color: #292C39;
 
    .item {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        height: 108rpx;
        font-size: 32rpx;
        +.item{
            border-top: 1rpx solid #F2F3F5;
        }
    }
}
</style>