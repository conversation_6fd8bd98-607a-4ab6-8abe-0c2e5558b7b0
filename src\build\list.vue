<template>
  <view class="list">
    <tab-bar
      ref="tab_bar"
      :tabs="build_cates"
      :nowIndex="
        build_cates.findIndex(
          (item) => Number(item.value) === params.build_category
        )
      "
      fixed_top
      top="0"
      @click="onClickCate"
    ></tab-bar>

    <view class="build-list">
      <my-build
        v-for="item in build_list"
        :key="item.id"
        :item="item"
        @click="
          $navigateTo(`/build/detail?id=${item.id}&buildID=${item.build_id}`)
        "
        ><template v-slot:yong v-if="display_brokerage">
          <view class="price-right row" v-if="item.brokerage_rule">
            <text class="yong">佣</text>
            <text class="jiage">{{
              item.store_brokerage_rule || item.brokerage_rule
            }}</text>
          </view>
        </template></my-build
      >
    </view>
    <myLoading
      ref="loading"
      :custom="false"
      :shadeClick="true"
      :type="1"
    ></myLoading>
    <load-more :status="load_status"></load-more>

    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import myBuild from "@/components/build-item";
import myLoading from "@/components/my-loading";
import tabBar from "@/components/tabBar";
import { mapActions } from "vuex";
import loadMore from "@/components/loadMore";
export default {
  components: {
    myBuild,
    myLoading,
    tabBar,
    loadMore,
  },
  data() {
    return {
      build_list: [],
      build_status_list: [],
      build_category_list: [],
      build_cates: [],
      params: {
        page: 1,
        row: 20,
        build_category: 0,
      },
      noData: false,
      display_brokerage: false,
      load_status: "",
    };
  },
  onReady() {
    this.$refs.loading.open();
  },
  onLoad(options) {
    if (options.type_value) {
      this.params.build_category = parseInt(options.type_value);
    }
    this.init();
  },
  methods: {
    ...mapActions(["getSetting"]),
    init() {
      this.$setDictionary((e) => {
        e.find((item) => {
          switch (item.name) {
            case "BUILD_STATUS":
              this.build_status_list = item.childs;
              break;
            case "BUILD_CATEGORY":
              this.build_category_list = item.childs;
              this.build_cates = [
                { value: "0", description: "全部" },
                ...item.childs,
              ];
              this.getDataList();
              break;
            default:
              break;
          }
        });
      });
      // 佣金是否显示
      this.getSetting((e) => {
        let display = e.login_display_brokerage_rule;
        let token = uni.getStorageSync("token" + this.$store.state.website_id);
        if (display == 1 || token) {
          this.display_brokerage = true;
        } else if (display == 0) {
          this.display_brokerage = false;
        }
      });
    },
    getDataList() {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.build_list = [];
      }
      this.$refs.loading.open();
      this.$ajax.get("/common/project/list", this.params, (res) => {
        this.load_status = "loadend";
        if (res.statusCode === 200) {
          this.$refs.loading.close();
          this.build_list = this.build_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
            uni.showToast({
              title: "没有更多数据了",
              icon: "none",
            });
          }
          this.$matchBuildType(
            this.build_list,
            this.build_status_list,
            this.build_category_list
          );
        } else {
          uni.showToast({
            title: res.data.message || "没有更多数据了",
            icon: "none",
            success: () => {
              this.params.page = 1;
            },
          });
        }
      });
    },
    onClickCate(e) {
      this.params.build_category = Number(e.value);
      this.params.page = 1;
      this.getDataList();
    },
    onPullDownRefresh() {
      this.params.page = 1;
      this.getDataList();
      uni.stopPullDownRefresh();
    },
    onReachBottom() {
      if (this.load_status === "nomore") {
        return;
      }
      this.params.page++;
      this.getDataList();
    },
  },
};
</script>

<style scoped lang="scss">
.build-list {
  margin-top: 80rpx;
  padding: 24rpx 48rpx;
  .price-right {
    align-items: center;
    .yong {
      line-height: 40rpx;
      text-align: center;
      width: 40rpx;
      height: 40rpx;
      color: #fff;
      background: #fec923;
      border-radius: 4px;
    }
    .jiage {
      font-size: 24rpx;
      color: #333;
    }
  }
}
</style>
