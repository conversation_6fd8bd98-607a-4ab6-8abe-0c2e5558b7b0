<template>
  <view :class="{ padb: from != '' }">
    <view class="top">
      <view class="top_c row">
        <view v-if="searchshow">
          <view class="search_box">
            <view v-for="(item, index) in rangeList" :key="item.value" class="search_text"
              :class="{ activee: item.value == value }" @tap="textFn(item.value, item.text)">{{ item.text }}</view>
          </view>
        </view>
        <view class="whole_input_left" @click="inputFn">
          <view class="whole_input_left_name">{{ mobiles || "电话" }}</view>
          <!-- <view>
            <image
              src="../../../static/icon/index/xia.png"
              style="width: 32rpx; height: 32rpx"
            ></image>
          </view> -->
        </view>
        <view class="search row c2 flex-1">
          <myIcon class="icon" type="ic_sousuo3x1" color="#8A929F" size="32rpx"></myIcon>
          <!-- v-model="params.mobile" -->
          <input type="text" v-model="keywords" @confirm="onSearch" placeholder="请输入搜索内容" />
        </view>
        <view class="depart row" @click="quickShow">
          <view class="depart_name">我的</view>
          <view class="sanjiao"> </view>
        </view>
      </view>

      <view class="second_tab flex-row flex-1 align-center js-between" id="second_tab">
        <view @click="changeSecondTab(1)" class="second_tab_item flex-1 flex-row justify-center items-center">
          {{ currentType }}
          <myIcon style="margin-left: 8px" color="#282A2F" type="xiala" size="10px"></myIcon>
        </view>

        <view @click="changeSecondTab(2)" class="second_tab_item flex-1 flex-row justify-center items-center">
          {{ currentStatus }}
          <myIcon style="margin-left: 8px" color="#282A2F" type="xiala" size="10px"></myIcon>
        </view>

        <view @click="changeSecondTab(3)" class="second_tab_item flex-1 flex-row justify-center items-center">
          {{ currentLabel }}
          <myIcon style="margin-left: 8px" color="#282A2F" type="xiala" size="10px"></myIcon>
        </view>

        <view @click="changeSecondTab(4)" class="second_tab_item flex-1 flex-row justify-center items-center">
          {{ currentMore }}
          <myIcon style="margin-left: 8px" color="#282A2F" type="xiala" size="10px"></myIcon>
        </view>
        <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 1">
          <view class="second_tab_con_i">
            <scroll-view scroll-y style="height: 300px">
              <view class="second_tab_con_item">
                <view class="second_tab_con_item_title">全部</view>
                <view class="second_tab_con_item_list row" style="margin-bottom: 0px;">
                  <view class="second_tab_con_item_list_item" v-for="item in cust_status_list" :key="item.id"
                    :class="{ isactive: params.c_type2 == item.id }" @click="
                      params.c_type2 = item.id;
                    currentType = item.title;
                    ">{{ item.title }}</view>
                </view>

                <view class="second_tab_con_item_title">我参与的</view>
                <view class="second_tab_con_item_list row" style="margin-bottom: 20px;">
                  <view class="second_tab_con_item_list_item" v-for="item in my_canyu_list" :key="item.id"
                    :class="{ isactive: params.c_type6 == item.id }" @click="params.c_type6 = item.id">{{ item.title }}
                  </view>
                </view>

              </view>
            </scroll-view>
            <view class="flex-row btn_group">
              <view class="btn" @click="resetData">重置</view>
              <view class="btn bg_highlight" @click="filterData">确定</view>
            </view>
          </view>
          <view class="mask" @click="hideErjiSearch"></view>
        </view>
        <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 2">
          <view class="second_tab_con_i">
            <view class="second_tab_con_item">
              <view class="second_tab_con_item_title">客户状态</view>
              <view class="second_tab_con_item_list row">
                <view class="second_tab_con_item_list_item" v-for="item in status_list" :key="item.id"
                  :class="{ isactive: params.tracking_id == item.id }" @click="
                    params.tracking_id = item.id;
                  currentStatus = item.title;
                  ">{{ item.title }}</view>
              </view>
            </view>
            <view class="flex-row btn_group">
              <view class="btn" @click="resetData">重置</view>
              <view class="btn bg_highlight" @click="filterData">确定</view>
            </view>
          </view>
          <view class="mask" @click="hideErjiSearch"></view>
          <!-- <view class="mask1" v-show="showMask"> </view> -->
        </view>
        <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 3">
          <view class="second_tab_con_i">
            <view class="second_tab_con_item filter_list row" style="height: 200px; margin: 0 -24px">
              <scroll-view class="filter_labels" scroll-y>
                客户标签
                <view class="item" v-for="(item, index) in tag_list" :key="index" @click="checkFilter(index, item)"
                  :class="{ active: index == scroll_into_view_index }">{{ item.name }}</view>
              </scroll-view>
              <scroll-view scroll-y class="second_tab_con_item_right">
                <view class="lab-list row" v-for="item in label_list" :key="item.id" @click="
                  params.label = item.id;
                currentLabel = item.name;
                " :class="{ active: item.id == params.label }">
                  <view class="lab-item">{{ item.name }}</view>
                  <image class="ischeck" :src="`https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/${item.id == params.label ? 'ischeck' : 'check'
                    }.png`"></image>
                </view>
              </scroll-view>
            </view>
            <view class="flex-row btn_group">
              <view class="btn" @click="resetData">重置</view>
              <view class="btn bg_highlight" @click="filterData">确定</view>
            </view>
          </view>
          <view class="mask" @click="hideErjiSearch"></view>
        </view>
        <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="current_second_tab == 4">
          <view class="second_tab_con_i">
            <scroll-view scroll-y style="height: 300px">
              <view class="second_tab_con_item_title">客户类型</view>
              <view style="margin-bottom: 0" class="second_tab_con_item_list row">
                <view class="second_tab_con_item_list_item" v-for="item in type_list" :key="item.id"
                  :class="{ isactive: params.type == item.id }" @click="params.type = item.id">{{ item.title }}</view>
              </view>
              <view class="second_tab_con_item_title">客户等级</view>
              <view style="margin-bottom: 0" class="second_tab_con_item_list row">
                <view class="second_tab_con_item_list_item" v-for="item in level_list" :key="item.id"
                  :class="{ isactive: params.level_id == item.id }" @click="params.level_id = item.id">{{ item.title }}
                </view>
              </view>
              <view class="second_tab_con_item_title">绑定企微</view>
              <view style="margin-bottom: 0" class="second_tab_con_item_list row">
                <view class="second_tab_con_item_list_item" v-for="item in bind_list" :key="item.id"
                  :class="{ isactive: params.is_bind_qywx == item.id }" @click="params.is_bind_qywx = item.id">{{
                    item.name }}</view>
              </view>
              <view class="second_tab_con_item_title">客户来源</view>
              <view style="margin-bottom: 0" class="second_tab_con_item_list row">
                <view class="second_tab_con_item_list_item" v-for="item in source_list" :key="item.id"
                  :class="{ isactive: params.source_id == item.id }" @click="params.source_id = item.id">{{ item.title }}
                </view>
              </view>

              <view class="second_tab_con_item_title">筛选时间</view>
              <view style="padding-bottom: 50rpx" class="second_tab_con_item_list row">
                <view class="second_tab_con_item_list_item" v-for="item in time_list" :key="item.id"
                  :class="{ isactive: params.date_type == item.id }" @click="params.date_type = item.id">{{ item.name }}
                </view>
              </view>
              <!-- <view class="second_tab_con_item_title">自定义</view>
              <view class="second_tab_con_item_list" style="padding-bottom: 150px">
                <view class="timebox row">
                  <input
                    type="number"
                    style="width: 128px"
                    placeholder-class="placls"
                    placeholder="请输入年"
                    v-model="startyear"
                  />
                  <input
                    type="number"
                    placeholder-class="placls"
                    placeholder="月"
                    v-model="startmonth"
                  />
                  <input
                    type="number"
                    placeholder-class="placls"
                    placeholder="日"
                    v-model="startday"
                  />
                </view>
                <view class="zhi">至</view>
                <view class="timebox row">
                  <input
                    type="number"
                    style="width: 128px"
                    placeholder-class="placls"
                    placeholder="请输入年"
                    v-model="endyear" />
                  <input
                    type="number"
                    placeholder-class="placls"
                    placeholder="月"
                    v-model="endmonth" />
                  <input type="number" placeholder-class="placls" v-model="endday" placeholder="日"
                /></view>
              </view> -->
            </scroll-view>
            <view class="flex-row btn_group">
              <view class="btn" @click="resetData">重置</view>
              <view class="btn bg_highlight" @click="filterData">确定</view>
            </view>
          </view>

          <view class="mask" @click="hideErjiSearch"></view>
          <!-- <view class="mask1" v-show="showMask"> </view> -->
        </view>
        <view class="second_tab_con quick" @touchmove.stop.prevent="disMove" v-if="quick_show">
          <view class="second_tab_con_i">
            <view class="tab_con_item" @click="toSeaPath"> 公海 </view>
            <view class="tab_con_item" @click="toQianzai"> 潜客 </view>
            <view class="tab_con_item" @click="departShow" v-if="is_editer">
              部门
            </view>
          </view>
          <view class="mask" @click="hideErjiSearch"></view>
          <!-- <view class="mask1" v-show="showMask"> </view> -->
        </view>
        <view class="second_tab_con" @touchmove.stop.prevent="disMove" v-if="depart_show">
          <view class="second_tab_con_i">
            <view class="second_tab_con_item member">
              <view class="second_tab_con_item_list row">
                <view style="width: 60%" class="flex-1">
                  <view class="second_tab_con_item_title">部门</view>
                  <tki-tree ref="tkitree" style="width: 100%; height: 600rpx" :range="department" childName="subs"
                    rangeKey="name" confirmColor="#4e8af7" @confirm="confirmDepartment" />
                </view>

                <scroll-view scroll-y class="second_tab_con_item_right member flex-1">
                  <view class="second_tab_con_item_title">成员</view>
                  <template v-if="userList.length">
                    <view class="lab-list row" v-for="item in userList" :key="item.id" @click="params.admin_id = item.id"
                      :class="{ active: item.id == params.admin_id }">
                      <view class="lab-item">{{ item.user_name }}</view>
                      <image class="ischeck" :src="`https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5detail/${item.id == params.admin_id ? 'ischeck' : 'check'
                        }.png`"></image>
                    </view>
                  </template>
                  <view v-else>该部门暂无成员</view>
                </scroll-view>
              </view>
            </view>
            <view class="flex-row btn_group">
              <view class="btn" @click="resetData">重置</view>
              <view class="btn bg_highlight" @click="filterData">确定</view>
            </view>
          </view>
          <view class="mask" @click="hideErjiSearch"></view>
          <!-- <view class="mask1" v-show="showMask"> </view> -->
        </view>
      </view>
      <view class="mask" @click="showMask = false"></view>
    </view>
    <view class="list" v-if="client_list.length">
      <myList :arr="client_list" @onClick="onClickDetail" @more="more" @tel="tel" @searchAddress="searchAddress"
        type="my"></myList>
    </view>
    <load-more :status="load_status"></load-more>
    <view @click="onClickPost" class="ent">
      <image src="../static/customer/lu.png" mode="widthFix"> </image>
    </view>

    <view v-if="from" style="margin: 24px 12px" class="claim" @click="$navigateBack()">返回</view>
    <myPopup ref="show_more" :show="show_more" @hide="show_more = false">
      <view class="more_box">
        <view class="more_title row"> 更多操作 </view>
        <view class="more_list row">
          <view class="more_item" @click="toEditer">
            <view class="icon">
              <image :src="'/yidongduan/customer/<EMAIL>' | imageFilter('w_80')"></image>
            </view>
            <view class="name"> 维护资料 </view>
          </view>
          <view class="more_item" @click="toSea">
            <view class="icon">
              <image :src="'/yidongduan/customer/<EMAIL>'
                | imageFilter('w_80')
                "></image>
            </view>
            <view class="name"> 转交到公海 </view>
          </view>
          <view class="more_item" @click="toFriend">
            <view class="icon">
              <image :src="'/yidongduan/customer/<EMAIL>'
                | imageFilter('w_80')
                "></image>
            </view>
            <view class="name"> 转交到同事 </view>
          </view>
          <view class="more_item" @click="toGenjin">
            <view class="icon">
              <image :src="'/yidongduan/customer/<EMAIL>' | imageFilter('w_80')
                "></image>
            </view>
            <view class="name"> 跟进 </view>
          </view>
          <!-- <view class="more_item">
            <view class="icon">
              <image :src="'/yidongduan/customer/<EMAIL>' | imageFilter('w_80')"></image>
            </view>
            <view class="name"> 审批 </view>
          </view> -->
        </view>
      </view>
    </myPopup>

    <my-popup ref="reason" :show="giveup_reason" @hide="giveup_reason = false" :touch_hide="true">
      <view class="reason" v-if="giveup_reason">
        <view class="close" @click="giveup_reason = false">×</view>
        <view class="label"> 请输入转到公海的原因 </view>
        <textarea v-model="giveup_content" placeholder="请输入转到公海的原因"></textarea>
        <view class="btn" @click="toSeas"> 转到公海 </view>
      </view>
    </my-popup>
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
import tabBar from "@/components/cusTabBar";
import loadMore from "@/components/loadMore.vue";
import myPopup from "@/components/myPopup.vue";
import myList from "./components/my_list";
import tkiTree from "@/components/tki-tree/tki-tree.vue";
// import tkiTree from "@/components/ba-tree-picker/ba-tree-picker.vue"
export default {
  components: {
    myIcon,
    tabBar,
    loadMore,
    myList,
    tkiTree,
    myPopup,
  },
  data() {
    return {
      rangeList: [
        { value: 0, text: "电话" },
        { value: 1, text: "姓名" },
        { value: 2, text: "线索" },
        { value: 3, text: "编号" },
      ],
      source_list: [],
      searchshow: false,
      type: "",
      params: {
        page: 1,
        form: 2, // 1:所有，2我的，3公海
        mobile: "",
        source_id: 0,
        tracking_id: 0,
        label: 0,
        type: 0,
        level_id: 0,
        is_bind_qywx: 0,
        date_type: 0,
        start_date: "",
        end_date: "",
        department_id: 0,
        admin_id: 0,
        c_type2: 0,
        c_type6: 0,
      },
      bind_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "已绑定" },
        { id: 2, name: "未绑定" },
      ],
      time_list: [
        { id: 0, name: "全部" },
        { id: 1, name: "今天" },
        { id: 2, name: "昨天" },
        { id: 3, name: "本周" },
        { id: 4, name: "上周" },
        { id: 5, name: "本月" },
        { id: 6, name: "上月" },
      ],
      cust_status_list: [
        {
          id: 0,
          title: "全部",
        },
        {
          id: 1,
          title: "已认领",
        },
        {
          id: 2,
          title: "已跟进",
        },
        {
          id: 3,
          title: "未跟进",
        },
        {
          id: 4,
          title: "私客",
        },
        {
          id: 5,
          title: "即将掉公",
        },
        {
          id: 7,
          title: "已带看",
        },
        {
          id: 8,
          title: "已成交",
        },
      ],
      my_canyu_list: [
        {
          id: 1,
          title: "我录入的",
        },
        {
          id: 3,
          title: "我带看的",
        },
        {
          id: 4,
          title: "我成交的",
        },
        {
          id: 5,
          title: "我转公的",
        },
        {
          id: 6,
          title: "我掉公的",
        }, {
          id: 7,
          title: "共享客户",
        },
      ],
      load_status: "",
      client_list: [],
      color: "#4D7BFE",
      hasChoose: false,
      tracking_list: [],
      type_list: [],
      current_second_tab: "",
      showMask: false,
      status_list: [],
      tag_list: [],
      scroll_into_view_index: 0,
      scroll_into_view_label: 0,
      label_list: [],
      level_list: [],
      is_search: "",
      currentType: "全部",
      currentStatus: "状态",
      currentLabel: "标签",
      currentMore: "更多",
      startyear: "",
      startmonth: "",
      startday: "",
      endyear: "",
      endmonth: "",
      endday: "",
      depart_show: false,
      from: "",
      department: [],
      userList: [],
      show_more: false,
      quick_show: false,
      user_info: {},
      is_editer: false,
      has_roles: false,
      detail: {},
      giveup_content: "",
      giveup_reason: false,
      value: "",
      mobiles: "",
      keywords: "",
    };
  },
  filters: {
    filterTime(val) {
      if (!val) return "";
      let new_val = val.replace(/-/g, "/");
      var value = new Date(new_val);
      //获取要过滤的时间与当前时间的时间差。（单位秒）
      const d = Math.floor((Date.now() - value.getTime()) / 1000);
      const arr = [
        0,
        60,
        60 * 60,
        60 * 60 * 24,
        60 * 60 * 24 * 30,
        60 * 60 * 24 * 30 * 12,
      ];
      const _arr = ["刚刚", "分钟前", "小时前", "天前", "月前", "年前"];
      /* 循环判断 */
      for (var i = arr.length - 1; i >= 0; i--) {
        if (d > arr[i]) {
          /* 使用三元进行判断 */
          return i == 0 ? _arr[i] : Math.floor(d / arr[i]) + _arr[i];
        }
      }
      return value;
    },
  },
  watch: {
    keywords: {
      handler(naval) {
        uni.setStorageSync("keywords", naval);
      },
      immediate: true,
    },
    mobiles: {
      handler(naval) {
        if (naval == "") {
          naval = "电话";
        }
        uni.setStorageSync("mobiles", naval);
      },
      immediate: true,
    },
  },
  onLoad(options) {
    let token = uni.getStorageSync("wxwork_token");
    if (!token) {
      if (this.$isWxWork() == "wxwork") {
        return;
      } else {
        localStorage.setItem("backUrl", location.href);
        this.$router.push("https://yun.tfcs.cn");
      }
      // 未登录中断请求
    }
    uni.$on("getDataAgain", () => {
      this.params.page = 1;
      this.getDataList();
    });
    if (options.tel) {
      this.params.mobile = options.tel;
    }
    if (options.from) {
      this.from = options.from;
    }
    if (options.c_type2) {
      this.params.c_type2 = options.c_type2;
    }
    if (options.c_type6) {
      this.params.c_type6 = options.c_type6;
    }
    // if (options.remark) {
    //   this.params.remark= options.remark
    // }
    // console.log(options,'options+');
    // 智慧经营 跳转
    if (options.wisdomWork) {
      this.params.data_type = options.data_type
      if (options.level_id) {
        this.params.level_id = options.level_id
      } else if (options.tracking_id) {
        this.params.tracking_id = options.tracking_id
      } else if (options.member) {
        this.departShow()
      }
    }
    let name = "我的客户";
    wx.setNavigationBarTitle({
      title: name,
    });
    this.user_info = uni.getStorageSync("userInfo")
      ? JSON.parse(uni.getStorageSync("userInfo"))
      : {};
    this.getSourceData();
    this.getDataList();
    this.getTypeData();
    this.getStatusData();
    this.getTagData();
    this.getLevelList();
  },
  onPageScroll() {
    this.quick_show = false;
    this.current_second_tab = "";
  },
  onUnload() {
    uni.$off("getDataAgain");
  },
  async onPullDownRefresh() {
    uni.$emit("getDataAgain")
    await this.$Utils.sleep(600)
    uni.stopPullDownRefresh();
  },
  methods: {
    inputFn() {
      this.searchshow = !this.searchshow;
    },
    textFn(item, text) {
      this.searchshow = !this.searchshow;
      this.value = item;
      this.mobiles = text;
      console.log(this.value, "this.value");
      console.log(this.mobiles, "this.mobiles");
      uni.setStorageSync("mobiles", this.mobiles);
      if (text != "") {
        this.keywords = "";
      }
    },
    disMove() { },
    getLevelList() {
      this.$ajax.get("/qywx/level/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.level_list = [{ id: 0, title: "全部" }, ...res.data];
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    quickShow() {
      this.quick_show = true;
      this.depart_show = false;
    },
    toQianzai() {
      this.$navigateTo("/customer/qianzai_list");
      this.quick_show = false;
    },
    toSeaPath() {
      this.$navigateTo("/customer/seas_list");
      this.quick_show = false;
    },
    toSea() {
      this.show_more = false;
      this.giveup_reason = true;
    },
    toSeas() {
      this.$ajax.post(
        "/qywx/client/discard",
        { ids: this.detail.id + "", content: this.giveup_content },
        (res) => {
          console.log(res);
          if (res.statusCode == 200) {
            uni.showToast({
              title: res.message || "操作成功",
              icon: "none",
            });
            this.giveup_reason = false;
            this.getDataList();
          }
        }
      );
    },
    more(e) {
      this.detail = e || {};
      this.show_more = true;
    },
    searchAddress(e) {
      this.$ajax.get(
        `/admin/crm/client/query_mobile_place/${e.id}`,
        {},
        (res) => {
          if (res.statusCode == 200) {
            console.log(res);
            let index = this.client_list.findIndex((item) => item.id == e.id);
            this.$set(this.client_list[index], "mobile_place", res.data);
          }
        }
      );
    },
    tel(e) {
      console.log(e, "eeereeeee");
      this.$ajax.get(`/qywx/client/see_tel/${e.id}`, {}, (res) => {
        if (res.statusCode === 200) {
          let view_tel = res.data;
          uni.setStorageSync("telInfo", JSON.stringify(view_tel));
          let url = `/customer/demand?id=${e.id
            }&tel=1&name=${encodeURIComponent(e.cname)}&telType=${e.call_open_crm
            }&has_roles=1`;
          this.$navigateTo(url);
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    departShow() {
      this.quick_show = false;
      setTimeout(() => {
        this.depart_show = true;
      }, 300);
      // this.getUserList()
      this.$ajax.get(
        "/admin/personnelMatters/departmentsAndMembers",
        {},
        (res) => {
          console.log(res);
          if (res.statusCode == 200) {
            this.department = res.data;
            this.userList = this.department.length
              ? this.department[0].user || []
              : [];
          }
        }
      );
    },
    confirmDepartment(e) {
      let depart = e[0];
      this.params.department_id = depart.id;
      this.userList = depart.user;
    },
    getTagData() {
      this.$ajax.get("/qywx/tag/search", {}, (res) => {
        if (res.statusCode === 200) {
          let arr = res.data.qiwei_tag.map((item) => {
            return {
              id: item.id,
              name: item.name,
              label: item.taggroup,
            };
          });
          this.tag_list = arr.concat(res.data.system_tag);
          this.label_list = this.tag_list[0].label;
        }
      });
    },
    getTypeData() {
      this.$ajax.get("/qywx/type/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.type_list = [{ id: 0, title: "全部" }, ...res.data];
        }
      });
    },
    getStatusData() {
      this.$ajax.get("/qywx/tracking/list", { type: 4 }, (res) => {
        if (res.statusCode === 200) {
          this.status_list = [{ id: 0, title: "全部" }, ...res.data];
        }
      });
    },
    getSourceData() {
      this.$ajax.get("/qywx/source/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.source_list = [{ title: "全部", id: 0 }, ...res.data];
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    toGenjin() {
      let url = `/customer/demand?id=${this.detail.id}`;
      this.$navigateTo(url);
    },
    toFriend() {
      this.$navigateTo(`/customer/transfer_customer?id=${this.detail.id}`);
    },

    toEditer() {
      this.$navigateTo(`uphold?type=2&id=${this.detail.id}`);
    },
    onClickTab(e) {
      this.params.source_id = e.id;
      this.params.page = 1;
      this.getDataList();
    },
    onSearch() {
      this.params.page = 1;
      this.getDataList();
    },
    getDataList() {
      const value = uni.getStorageSync("mobiles");
      const text = uni.getStorageSync("keywords");
      console.log(text, 'text');
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.client_list = [];
      }
      if (value == "电话") {
        this.params.number = ""
        this.params.cname = ""
        this.params.keywords = ""
        this.params.mobile = text;
      } else if (value == "编号") {
        this.params.mobile = ""
        this.params.cname = ""
        this.params.keywords = ""
        this.params.number = text;
      } else if (value == "姓名") {
        this.params.cname = text;
        this.params.mobile = ""
        this.params.number = ""
        this.params.keywords = ""
      } else if (value == "线索") {
        this.params.mobile = ""
        this.params.number = ""
        this.params.cname = ""
        this.params.keywords = text;
      }
      console.log(this.params, "this.params");
      const params = Object.assign({}, this.params);
      let url = '/qywx/client/search';
      //搜索共享客户
      if (this.params.c_type6 == 7) {
        url = '/admin/crm/share_follow/share_list';
        delete params.c_type6;
      }


      this.$ajax.get(url, params, (res) => {
        this.load_status = "loadend";
        if (res.statusCode === 200) {
          console.log(res, "1111111111111");
          console.log(res.data.data, "0000000888888");
          let admin_list =
            res.data && res.data.data.length ? res.data.data[0].admin_list : [];
          if (admin_list.includes(this.user_info.id + "")) {
            this.is_editer = true;
          }
          this.client_list = this.client_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        }
      });
    },
    onClickDetail(item) {
      /* console.log(item.last_call_status, "0011992883746");
      console.log("admin", "4444444444");
      this.$navigateTo(
        `/customer/detail?id=${item.id}&form=${this.params.form}&source=2&lastcallstatus=${item.last_call_status}`
      ); */
      this.$ajax.get('/admin/crm/client/verify_follow', {}, res => {
        console.log(res, '666666666');
        if (res.statusCode == 200) {
          console.log(res.data, "222222222222");
          if (res.data && res.data.id > 0 && res.data.client_id > 0) {
            uni.showModal({
              title: '提示',
              content: '您有查看电话未跟进 去跟进？',
              success: (result) => {
                if (result.confirm) {
                  console.log(5555);
                  this.$navigateTo(
                    `/customer/detail?id=${res.data.client_id}&form=${this.params.form}&source=2&lastcallstatus=${item.last_call_status}`
                  );
                  return
                } else if (result.cancel) {
                  console.log('用户点击取消');
                }
              }
            })
          } else {
            console.log(3333);
            this.$navigateTo(
              `/customer/detail?id=${item.id}&form=${this.params.form}&source=2&lastcallstatus=${item.last_call_status}`
            );

          }
        } else {
          console.log(4444);
          this.$navigateTo(
            `/customer/detail?id=${item.id}&form=${this.params.form}&source=2&lastcallstatus=${item.last_call_status}`
          );
        }
      })
    },
    onClickPost() {
      this.$navigateTo("uphold?type=1");
    },
    hideErjiSearch() {
      this.current_second_tab = "";
      this.quick_show = false;
      this.showMask = false;
    },
    changeSecondTab(index) {
      if (this.current_second_tab == index)
        return (this.current_second_tab = "");
      this.current_second_tab = index;
      this.showMask = true;
    },
    checkFilter(index, item) {
      this.scroll_into_view_index = index;
      this.label_list = item.label;
    },
    resetData() {
      this.params = {
        page: 1,
        form: 2, // 1:所有，2我的，3公海
        mobile: "",
        source_id: 0,
        tracking_id: 0,
        label: 0,
        type: 0,
        level_id: 0,
        is_bind_qywx: 0,
        date_type: 0,
        start_date: "",
        end_date: "",
        c_type2: 0,
        c_type6: 0,
      };
      this.current_second_tab = "";
      this.currentType = "全部";
      this.currentStatus = "状态";
      this.currentLabel = "标签";
      this.showMask = false;
      this.depart_show = false;
      this.getDataList();
    },
    filterData() {
      if (this.startyear && this.startmonth && this.startday) {
        let month =
          this.startmonth < 10 ? "0" + this.startmonth : this.startmonth;
        let day = this.startday < 10 ? "0" + this.startday : this.startday;
        this.params.start_date = this.startyear + "-" + month + "-" + day;
      }
      if (this.endyear && this.endmonth && this.endday) {
        let month = this.endmonth < 10 ? "0" + this.endmonth : this.endmonth;
        let day = this.endday < 10 ? "0" + this.endday : this.endday;
        this.params.end_date = this.endyear + "-" + month + "-" + day;
      }
      this.params.page = 1;
      this.getDataList();
      this.current_second_tab = "";
      this.depart_show = false;
      this.showMask = false;
    },
  },
  onReachBottom() {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>
<style scoped lang="scss">
page {
  background: #f6f6f6;
  color: #2e3c4e;
}

.c2 {
  color: #8a929f;
}

.list {
  padding: 12px;

  .info {
    background: #fff;
    border-radius: 12px;
    padding: 12px;
    margin-bottom: 12px;

    .claim {
      text-align: center;
      height: 40px;
      line-height: 40px;
      font-weight: 500;
      margin: 12px 0;
      border-radius: 6px;

      &.c1 {
        background: #eaf3ff;
        color: #2d84fb;
      }

      &.c2 {
        color: #eaf3ff;
        background: #2d84fb;
      }
    }

    .content {
      >text {
        line-height: 20px;
      }
    }

    .type {
      margin: 12px 0;
      line-height: 14px;

      text {
        font-size: 11px;
        margin-right: 24px;
      }
    }

    .pers {
      align-items: center;
      justify-content: space-between;

      .time {
        font-size: 11px;
      }

      .left {
        align-items: center;

        .level {
          width: 16px;
          height: 16px;
          line-height: 16px;
          text-align: center;
          color: #fff;
          font-weight: 500;
          font-size: 11px;
          background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
          border-radius: 2px;
        }

        .name {
          font-weight: 500;
        }

        text {
          margin-left: 12px;
        }

        .pic {
          width: 25px;
          height: 25px;
          border-radius: 50%;
        }

        .qw {
          width: 16px;
          margin-left: 12px;
        }
      }
    }
  }
}

.ent {
  display: inline-block;
  position: fixed;
  right: 10px;
  bottom: 150px;

  image {
    width: 80px;
    height: 80px;
  }
}

.sort {
  align-items: center;
  justify-content: space-between;
}

.top {
  background: #fff;
  padding: 6px 16px 0 16px;
  position: sticky;
  top: 0;
  z-index: 10;

  .search {
    background: #f6f6f6;
    border-radius: 6px;
    align-items: center;
    padding: 10px;

    input {
      font-size: 14px;
      margin-left: 12px;
      flex: 1;
    }
  }
}

.second_tab {
  padding: 24rpx;
  position: sticky;
  top: 200rpx;
  z-index: 6;
  background: #fff;
  justify-content: space-between;

  .second_tab_item {
    max-width: 20%;
    color: #282a2f;
    white-space: nowrap;

    .item_name {
      font-size: 28rpx;
      margin-right: 8rpx;
      color: #8a929f;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
    }

    .sanjiao {
      margin-top: 10rpx;
      width: 0;
      height: 0;
      border: 13rpx solid;
      border-color: #d8d8d8 transparent transparent transparent;
    }
  }

  .second_tab_con {
    position: absolute;
    top: 80rpx;
    left: 0;
    right: 0;
    z-index: 5;
    // padding: 0 48rpx;
    margin-left: -16px;
    margin-right: -16px;
    background: #fff;

    &.quick {
      top: 0;
      padding: 20rpx 0;

      .tab_con_item {
        padding: 20rpx 0;
      }
    }

    .second_tab_con_i {
      background: #fff;
      padding: 0 48rpx;
      max-height: 60vh;
      overflow-y: auto;
    }

    .second_tab_con_item {
      padding: 20rpx 0;

      &.member {
        .second_tab_con_item_list {
          margin-bottom: 0;
        }
      }

      &_title {
        margin: 10px 0;
        font-size: 16px;
      }

      &_list {
        flex-wrap: wrap;
        margin-bottom: 150px;

        .timebox {
          justify-content: space-between;

          .placls {
            font-size: 12px;
          }

          input {
            height: 35px;
            padding: 4px 10px;
            width: 80px;
            border-radius: 5px;
            border: 1px solid #eeeeee;
            text-align: center;
          }
        }

        .zhi {
          text-align: center;
          margin: 15px 0;
        }

        &_item {
          width: 30%;
          text-align: center;
          line-height: 35px;
          border-radius: 5px;
          margin: 2px 1.66%;
          border: 1px solid #eeeeee;

          &.isactive {
            border-radius: 5px;
            background: #f3f7fe;
            border: 1px solid #3172f6;
            color: #3172f6;
          }
        }
      }

      &_left {
        width: 50%;
        background: #fff;
        margin: 0 20px;
      }

      &_right {
        background: #f9f9f9;
        margin: -12px 0;
        line-height: 40px;
        padding-left: 24px;
        padding-top: 12px;

        .lab-list {
          line-height: 40px;
          align-items: center;
          justify-content: space-between;

          .lab-item {
            word-break: break-all;

            &.active {
              color: #3172f6;
            }
          }

          .ischeck {
            margin-right: 24px;
            width: 14px;
            height: 14px;
          }
        }
      }
    }

    .btn_group {
      min-height: 64px;
      box-shadow: 0 -3px 5px 0 #f1f1f1;
      padding: 12px 24px;
      margin-left: -24px;
      margin-right: -24px;

      .btn {
        text-align: center;
        width: 30%;
        border-radius: 3px;
        background: #e5eeff;
        color: #3172f6;
        line-height: 40px;

        &.bg_highlight {
          margin-left: 10px;
          background-color: $color-primary;
          width: 70%;
          color: #fff;
        }
      }
    }

    .mask {
      position: fixed;
      z-index: -1;
      width: 100%;
      height: 100vh;
      left: 0;
      background-color: #000;
      opacity: 0.5;
      transition: 0.26s;
    }
  }
}

.filter_list {
  overflow: hidden;

  .filter_labels {
    font-size: 16px;
    font-size: 13px;
    padding-left: 24px;
    line-height: 40px;

    &.active {
      color: #3172f6;
    }
  }
}

.claim {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  text-align: center;
  height: 40px;
  line-height: 40px;
  font-weight: 500;
  margin: 12px 0;
  border-radius: 6px;
  background: #3172f6;
  color: #fff;
}

.padb {
  padding-bottom: 100px;
}

.top_c {
  position: relative;

  .depart {
    padding: 14rpx 24rpx;
    border-radius: 4rpx;
    background: #f3f4f8;
    align-items: center;
    margin-left: 10rpx;

    // justify-content: center;
    .depart_name {
      color: #000000;
      font-size: 28rpx;
      margin-right: 10rpx;
    }

    .sanjiao {
      width: 0;
      height: 0;
      margin-top: 10rpx;
      border: 10rpx solid transparent;
      border-top-color: #000;
    }
  }
}

.second_tab_con_item_list ::v-deep .tki-tree-view {
  top: 0;
}

.second_tab .second_tab_con_item_right {
  &.member {
    height: 670rpx;
    background: #fff;

    .second_tab_con_item_title {
      margin-top: 0;
    }
  }
}

.more_box {
  border-radius: 20px 20px 0px 0px;
  background: #ffffff;

  .more_title {
    justify-content: center;
    color: #8a929f;
    font-size: 32rpx;
    padding: 24rpx;
    border-bottom: 2rpx solid #f2f2f2;
  }

  .more_list {
    padding: 24rpx;
    flex-wrap: wrap;

    .more_item {
      width: 25%;
      justify-content: center;
      align-items: center;
      padding: 24rpx 0;

      .icon {
        width: 40rpx;
        height: 40rpx;
        overflow: hidden;

        image {
          width: 100%;
          height: 100%;
        }
      }

      .name {
        color: #8a929f;
        margin-top: 24rpx;
        font-size: 24rpx;
      }
    }
  }
}

.reason {
  width: 80vw;
  height: 400rpx;
  margin: calc(100% - 200rpx) auto;
  padding: 40rpx;
  border-radius: 20rpx;
  background: #fff;
  position: relative;

  .close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    font-size: 48rpx;
  }

  textarea {
    margin-top: 8px;
    padding: 6px 16px;
    box-sizing: border-box;
    background: #f5f7fa;
    border: 1px solid #e8e8e8;
    height: 280rpx;
    width: 100%;
  }

  .btn {
    display: inline-block;
    padding: 20rpx 20rpx;
    background: #2d84fb;
    color: #fff;
    margin: 20rpx auto 0;
  }
}

.whole_input_left {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.whole_input_left_name {
  color: rgba(41, 44, 57, 0.7);
  font-size: 28rpx;
  font-weight: 400;
  line-height: 32rpx;
  margin-right: 16rpx;
}

.search_box {
  position: absolute;
  top: 50rpx;
  left: 0;
  background: #fff;
  width: 180rpx;
  margin-top: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0px 0px 40rpx 0px rgba(0, 0, 0, 0.1);
  z-index: 999999;
}

.search_text {
  padding: 20rpx 32rpx;
  width: 100%;
}

.activee {
  background: rgba(0, 0, 0, 0.03);
}
</style>
