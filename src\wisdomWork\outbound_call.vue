<template>
    <wisdomContainer>
        <wisdomPersonalCall :loading.sync="loading"></wisdomPersonalCall>
        <wisdomTeamCall v-if="this.perms.teamCallView"></wisdomTeamCall>
    </wisdomContainer>
</template>

<script>
import wisdomContainer from './components/wisdomContainer';
import wisdomPersonalCall from './components/wisdomPersonalCall';
import wisdomTeamCall from './components/wisdomTeamCall';
import { checkPerms } from '@/common/utils/wisdom-work.js';
export default {
    components: {
        wisdomContainer,
        wisdomPersonalCall,
        wisdomTeamCall
    },
    data(){
        return {
            loading: true,
            perms: {
                teamCallView: false
            }
        }
    },
    created(){
        uni.showLoading();
        const unwatch = this.$watch('loading', ()=>{
            uni.hideLoading();
            unwatch();
        })
        this.checkPerms();
    },
    methods: {
        //团队外呼查看权限
        async checkPerms(){
            this.perms.teamCallView = await checkPerms();
        }
    }
}
</script>