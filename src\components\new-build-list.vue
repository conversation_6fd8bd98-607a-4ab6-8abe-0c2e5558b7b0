<template>
  <!-- 
      @date：2022-04-20
      @description：首页组件更换
   -->
  <view class="newbuild">
    <view
      class="list-item bottom-line"
      v-for="(item, index) in list"
      :key="index"
      @click="onClick(item)"
    >
      <view class="top">
        <view v-if="type === 1" class="small row">
          <view class="s-img">
            <image
              mode="aspectFill"
              :src="
                item.build_image
                  ? item.build_image
                  : 'https://img.tfcs.cn/static/img/que.jpg' | imageFilter('w_220')
              "
            ></image>
          </view>
          <view class="detail small-d">
            <view class="d-title row">
              <view class="d-t-l">{{ item.build_name }}</view>
              <view class="d-t-r" v-if="item.label">{{ item.label }}</view>
            </view>
            <view class="d-price">{{ item.build_avg_price }}元/㎡</view>
            <view class="d-address">
              <text class="row"
                >{{ item.b_region_0_name || '' }} &nbsp;{{ item.b_region_1_name || '' }}</text
              >
            </view>
            <view class="d-type row">
              <view class="ttt" v-for="(type, index) in item.build_category_name" :key="index">{{
                type
              }}</view>
            </view>
          </view>
        </view>
        <view v-if="type === 2" class="big">
          <view class="big-img">
            <image
              mode="aspectFill"
              :src="
                item.build_image
                  ? item.build_image
                  : 'https://img.tfcs.cn/static/img/que.jpg' | imageFilter('w_220')
              "
            ></image>
            <view class="big-build-name">{{ item.build_name }}</view>
          </view>
          <view class="detail big-d">
            <view class="d-price row">
              <view class="d-price">{{ item.build_avg_price + '元/㎡' }}</view>
              <view class="d-t-r" v-if="item.label">{{ item.label }}</view>
            </view>
            <view class="d-address">
              <text class="row"
                >{{ item.b_region_0_name || '' }} &nbsp;{{ item.b_region_1_name || '' }}</text
              >
            </view>
            <view class="d-type row">
              <view class="ttt" v-for="(type, index) in item.build_category_name" :key="index">{{
                type
              }}</view>
            </view>
          </view>
        </view>
      </view>
      <view class="bottom row">
        <view class="b-l row">
          <image
            src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5index/youhuihuodong.png"
          ></image>
          <text v-if="item.buy_coupon" style="color: #7a828d; margin-left: 10rpx"
            >优惠活动 | {{ item.buy_coupon }}</text
          >
          <text v-else style="color: #7a828d; margin-left: 10rpx">靓盘好卖 佣金易拿</text>
        </view>
        <view class="b-r row">
          <view
            class="row"
            v-if="
              (item.company_list && !Array.isArray(item.company_list)) ||
              (item.brokerage_rule && is_display)
            "
          >
            <text class="y-icon">佣</text>
            <text class="isdesc">
              <!-- {{
                token && user_info.company_id
                  ? item.store_brokerage_rule || item.brokerage_rule
                  : item.brokerage_rule
              }} -->
              {{ item | filterYong }}
            </text>
          </view>
          <view v-else class="row">
            <text>我要推荐</text>
            <image
              src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/h5index/jiantou.png"
            ></image>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
var _this = null
import config from "@/page_outside/config/index";
export default {
  props: {
    list: Array,
    // 列表模式  1小图 2大图
    type: {
      type: [Number, String],
      default: 1,
    },
    is_display: {
      type: [Number, String, Boolean],
      default: false,
    }
  },
  created () {
    _this = this
  },
  filters: {
    filterYong (val) {
      let name = ''
      // if(val&& val.company_list &&val.company_list.length ){
      //     val.company_list.map(item=>company_ids.push(+item.company_id))
      // }
      // if(company_ids.includes(+_self.user_info.company_id)){
      //     company_info = val.company_list.filter(item=>item.company_id ==_self.user_info.company_id)
      // }
      if (val.company_list && !Array.isArray(val.company_list)) {
        // 公司设置独立分佣规则
        name = val.company_list.brokerage_rule
      } else {
        if (uni.getStorageSync("token" + config.website_id)) {
          if (_this && _this.user_info.company_id) {
            console.log(45);
            // 独立经纪人
            name = val.store_brokerage_rule || val.brokerage_rule
          } else {
            console.log(12);
            // 普通经纪人
            name = val.brokerage_rule
          }
        } else {
          // 游客
          name = val.brokerage_rule
        }
      }
      return name

    }
  },
  data () {
    return {
    };
  },
  computed: {
    user_info () {
      return this.$store.state.user_info
    }
  },
  methods: {
    onClick (item) {
      this.$emit("click", item);
    },
  },
};
</script>

<style scoped lang="scss">
.newbuild {
  width: 100%;
  .list-item {
    margin-top: 32rpx;
    .top {
      .small {
        .s-img {
          width: 220rpx;
          height: 160rpx;
          border-radius: 10rpx;
          overflow: hidden;
          image {
            width: 100%;
            height: 100%;
          }
        }
        .small-d {
          margin-left: 36rpx;
        }
      }
      .detail {
        line-height: 40rpx;
        flex: 1;
        .d-title {
          align-items: baseline;
          justify-content: space-between;
          .d-t-l {
            font-size: 30rpx;
          }
        }
        .d-price {
          font-size: 28rpx;
          color: #fe4438;
        }
        .d-address {
          font-size: 22rpx;
          color: #191c2f;
        }
        .d-type {
          font-size: 22rpx;
          flex-wrap: wrap;
          .ttt {
            color: #7a828d;
            padding: 0 6rpx;
            margin-right: 12rpx;
            background: #f0f1f6;
            border-radius: 4rpx;
            margin-top: 10rpx;
          }
        }
        .d-t-r {
          font-size: 22rpx;
          background: #3172f6;
          color: #fff;
          padding: 0rpx 8rpx;
          border-radius: 4rpx;
        }
      }
      .big-d {
        margin-top: 20rpx;
        .d-price {
          justify-content: space-between;
        }
      }
      .big {
        .big-img {
          position: relative;
          image {
            height: 380rpx;
            width: 100%;
            border-radius: 10rpx;
          }
          .big-build-name {
            position: absolute;
            bottom: 0;
            width: 100%;
            left: 0;
            height: 56rpx;
            line-height: 56rpx;
            color: #fff;
            border-radius: 0 0 10rpx 10rpx;
            font-size: 30rpx;
            padding-left: 28rpx;
            background: rgba($color: #000000, $alpha: 0.4);
          }
        }
      }
    }
    .bottom {
      border-radius: 20rpx;
      background: #fff9f1;
      padding: 10rpx 16rpx;
      font-size: 24rpx;
      justify-content: space-between;
      align-items: center;
      margin: 24rpx 0 32rpx;
      .b-l {
        align-items: center;
        image {
          width: 28rpx;
          margin-right: 8rpx;
          height: 28rpx;
        }
      }
      .b-r {
        color: #3172f6;
        align-items: center;
        .row {
          align-items: center;
          .isdesc {
            color: #ff9b15;
          }
        }
        .y-icon {
          background: #ff9b15;
          width: 34rpx;
          height: 34rpx;
          text-align: center;
          line-height: 34rpx;
          color: #fff;
          border-radius: 50%;
          margin-right: 10rpx;
        }
        image {
          margin-left: 8rpx;
          height: 20rpx;
          width: 20rpx;
        }
      }
    }
  }
}
</style>
