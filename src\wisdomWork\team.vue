<template>
    <wisdomContainer>
        <wisdomTeamBrief :loading.sync="loading"></wisdomTeamBrief>
    </wisdomContainer>
</template>

<script>
import wisdomContainer from './components/wisdomContainer';
import wisdomTeamBrief from './components/wisdomTeamBrief';
export default {
    components: {
        wisdomContainer,
        wisdomTeamBrief
    },
    data(){
        return {
            loading: true
        }
    },
    created(){
        uni.showLoading();
        const unwatch = this.$watch('loading', (val)=>{
            uni.hideLoading();
            unwatch();
        })
    }
}
</script>