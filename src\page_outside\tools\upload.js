import config from "@/page_outside/config/index";
// 文件上传
let upHeader = {};
upHeader.Authorization = uni.getStorageSync("token") || "";
// #ifdef MP-WEIXIN
upHeader.ClientType = 1;
// #endif
// #ifdef H5
var ua = navigator.userAgent.toLowerCase();
if (ua.match(/MicroMessenger/i) == "micromessenger") {
  //如果是微信
  upHeader.ClientType = 2;
} else {
  upHeader.ClientType = 3;
}
// #endif
// #ifdef MP-BAIDU
upHeader.ClientType = 5;
// #endif
// #ifdef APP-PLUS
if (uni.getSystemInfoSync().platform == "ios") {
  upHeader.ClientType = 9;
} else {
  upHeader.ClientType = 8;
}
// #endif

const uploadFile = function (
  action = config.uploadApi,
  filePath,
  formData = {
    type: "config",
  },
  doSuccess,
  doFail
) {
  let siteID;


    upHeader.Authorization = uni.getStorageSync("wxwork_token") ? ("Bearer " + uni.getStorageSync("wxwork_token")) : '';
    siteID = uni.getStorageSync("wxwork_id");
  formData.siteID = siteID
  uni.uploadFile({
    url: `${config.appApi}${action}`,
    filePath: filePath,
    name: "file",
    header: upHeader,
    formData: formData,
    success: (res) => {

      try {
        if (typeof res.data == 'string') {
          res.data = JSON.parse(res.data);
        }

      } catch (err) {
        uni.showToast({
          title: "上传失败",
          icon: "none",
        });
        return;
      }
      if (doSuccess) {
        doSuccess(res);
      }
    },
    fail: (err) => {
      if (doFail) {
        uni.showToast({
          title: "上传失败，请重试",
          icon: "none",
        });
        doFail(err);
      }
    },
  });
};
export default uploadFile;
