<template>
    <view class="grids">
        <view class="grids-row" v-for="(row, rowIndex) in list" :key="rowIndex">
            <view class="grids-item" v-for="(item, index) in row" :key="index">
                <view class="grids-item-label">{{ item.title }}</view>
                <view class="grids-item-value">
                    {{ item.number }}<text class="unit">{{ item.unit }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: {
        data: { type: Array, default: ()=>[] }
    },
    computed: {
        list(){
            const list = [];
            for(let i = 0, length = this.data.length; i < length; i++){
                let n = i%2, m = Math.floor(i / 2);
                if( n == 0){
                    list[m] = [];
                }
                list[m].push(this.data[i])
            }
            return list;
        }
    },
}
</script>

<style lang="scss" scoped>
.grids{
    display: flex;
    flex-direction: cloumn;
    background-color: #fff;
    border-radius: 16rpx;
    .grids-row{
        display: flex;
        flex-direction: row;
        height: 182rpx;
        box-sizing: border-box;
        &:first-child{
            padding-top: 32rpx;
            .grids-item{
                padding-top: 14rpx;
            }
        }
        &:last-child{
            padding-bottom: 32rpx;
        }
        &+.grids-row{
            border-top: 1px solid #F2F3F5;
        }

        .grids-item{
            width: 50%;
            display: flex;
            flex-direction: column;
            padding: 46rpx 32rpx 0 48rpx;
            &:nth-child(odd){
                border-right: 1px solid #F2F3F5;
            }
            .grids-item-label, 
            .grids-item-value .unit{
                color: #86909C;
                font-size: 24rpx;
                font-weight: 400;
            }
            .grids-item-value{
                display: flex;
                flex-direction: row;
                align-items: center;
                color: #131315;
                font-size: 40rpx;
                height: 76rpx;
          
                .unit{
                    padding-left: 17rpx;
                }
            }
        }
    }
}
</style>