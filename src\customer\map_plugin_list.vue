<template>
  <view class="container">
    <view class="header" v-if="isShowHeader">
      <view class="search-box">
        <view class="search-input">
          <text class="iconfont icon-search"></text>
          <input type="text" placeholder="搜索资料包" v-model="searchText" @input="handleSearch" />
        </view>
      </view>
    </view>
    
    <view class="content">
      <view class="package-list">
        <view 
          class="package-item" 
          v-for="(item, index) in pluginList" 
          :key="index"
        >
          <view class="package-left">
            <image class="package-image" :src="item.share_pic" mode="aspectFit"></image>
          </view>
          <view class="package-right">
            <view class="package-info">
              <text class="package-title">{{ item.title }}</text>
              <text class="package-time">{{ item.created_at || '' }}</text>
            </view>
            <view class="package-actions">
              <text class="action-btn share-btn" @click="handleShare(item)">分享</text>
              <text class="action-btn customer-btn" @click="handleGetCustomer(item)">获客</text>
            </view>
          </view>
        </view>
        
        <!-- 无数据提示 -->
        <view class="no-data" v-if="pluginList.length === 0">
          <text>{{load_status == 'loading' ? '加载中...' : '暂无资料包'}}</text>
        </view>
      </view>
    </view>

    <my-popup ref="remind" :show="showPop" height="500px" position="center" @hide="showPop = false">
      <view class="pop_c">
        <text class="close-btn" @click="showPop = false">X</text>
        <view class="pop_title"> 成员专属码 </view>
        <view class="pop_img">
          <image :src="imgCode" mode="widtFix"> </image>
        </view>
        <view class="pop_b"> 长按识别进入分享获客通道 </view>
      </view>
    </my-popup>

  </view>
</template>

<script>
import config from "@/page_outside/config/index.js";
import myPopup from '@/components/myPopup';
export default {
  components: {
    myPopup
  },
  data() {
    return {
      isShowHeader: false,
      searchText: '',
      pluginList: [],
      params: {
        page: 1,
        per_page: 10,
      },
      load_status: "",
      website_id: 0,
      user: {},
      showPop: false,
      imgCode: ''
    }
  },
  onLoad(options) {
    this.website_id = options.website_id || 0;
    if (this.website_id <= 0) {
      uni.showToast({
        title: '站点ID错误',
        icon: 'none'
      });
      return false;
    }
    this.getData();
  },
  onReachBottom () {
    if (this.load_status == 'more') {
      this.params.page++
      this.getData()
    }
  },
  onPullDownRefresh() {
    this.params.page = 1;
    this.getData();
    uni.stopPullDownRefresh();
  },
  methods: {
    getData () {
      this.load_status = "loading";
      if (this.params.page == 1) {
        this.pluginList = []
      }
      this.$ajax.get('/admin/map_plugin/select_map', this.params, res => {
        if (res.statusCode == 200) {
          this.pluginList = this.pluginList.concat(res.data.maps)
          if (res.data.maps.length < this.params.per_page) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'more'
          }
        }
      })
    },
    handleSearch() {
    },
    handleShare(item) {
      this.$ajax.get(`/admin/map_plugin/share_code_new/${item.id}`, {}, res => {
        if (res.statusCode == 200) {
          this.imgCode = res.data
          this.showPop = true
        }else{
          uni.showToast({
            title:res?.data?.message || '小程序码获取失败',
            icon: "none"
          })
        }
      })
    },
    handleGetCustomer(item) {
      this.$navigateTo(`/customer/customer_logs?id=${item.id}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #FFFFFF;
  
  /* 强制列布局 */
  display: flex !important;
  flex-direction: column !important;
}

.header {
  background-color: #FFFFFF;
  padding: 24rpx;
  
  .search-box {
    width: 100%;
    
    .search-input {
      display: flex !important;
      flex-direction: row !important;
      align-items: center;
      background-color: #F5F5F5;
      border-radius: 36rpx;
      padding: 16rpx 24rpx;
      
      .iconfont {
        font-size: 32rpx;
        color: #999999;
        margin-right: 16rpx;
      }
      
      input {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
      }
    }
  }
}

.content {
  flex: 1;
  
  .package-list {
    .package-item {
      background-color: #FFFFFF;
      padding: 32rpx 24rpx;
      border-bottom: 1px solid #F5F5F5;
      
      /* 强制水平布局 */
      display: flex !important;
      flex-direction: row !important;
      /* 添加固定最小高度和垂直居中对齐 */
      min-height: 160rpx;
      align-items: center;
      
      &:active {
        background-color: #F8F8F8;
      }
      
      .package-left {
        margin-right: 24rpx;
        /* 添加flex布局和垂直居中属性 */
        display: flex !important;
        align-items: center;
        align-self: stretch;
        
        .package-image {
          width: 80rpx;
          height: 80rpx;
          border-radius: 8rpx;
        }
      }
      
      .package-right {
        flex: 1;
        
        /* 强制水平布局 - 修改为水平布局以适应新的设计 */
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between;
        align-items: center;
        
        .package-info {
          flex: 1;
          /* 强制列布局 */
          display: flex !important;
          flex-direction: column !important;
          
          .package-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #333333;
            margin-bottom: 16rpx;
            line-height: 1.5;
            /* 添加文本溢出处理 */
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .package-time {
            font-size: 24rpx;
            color: #999999;
          }
        }
        
        .package-actions {
          /* 强制水平布局 */
          display: flex !important;
          flex-direction: row !important;
          align-items: center;
          
          .action-btn {
            font-size: 28rpx;
            padding: 8rpx 16rpx;
            margin-left: 16rpx;
            border-radius: 4rpx;
          }
          
          .share-btn {
            color: #0174FF;
          }
          
          .customer-btn {
            color: #0174FF;
          }
        }
      }
    }
    
    .no-data {
      padding: 100rpx 0;
      text-align: center;
      color: #999999;
      font-size: 28rpx;
    }
  }
}


.pop_c {
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 80vw;
  margin: 0 auto;
  padding: 40rpx;
  border-radius: 20rpx;
  position: relative;  /* 添加相对定位 */
  
  .close-btn {
    position: absolute;
    right: 20rpx;
    top: 20rpx;
    width: 44rpx;
    height: 44rpx;
    line-height: 44rpx;
    text-align: center;
    font-size: 28rpx;
    color: #999;
    cursor: pointer;
    
    &:active {
      opacity: 0.8;
    }
  }
  
  .pop_title {
    text-align: center;
    font-size: 32rpx;
  }
  .pop_img {
    width: 520rpx;
    height: 520rpx;
    margin: 30rpx auto;
    image {
      width: 100%;
    }
  }
  .pop_b {
    font-size: 24rpx;
    ~ .pop_b {
      margin-top: 15rpx;
    }
  }
}


</style>
