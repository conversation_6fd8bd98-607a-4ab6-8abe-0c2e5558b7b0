<template>
  <view class="list-box">
    <view class="list">
      <view class="title row" v-if="is_phone_login">{{ siteConfig.name || '' }}快捷登录</view>
      <image
        v-if="!is_phone_login"
        src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/img/login-img.png?x-oss-process=style/w_400"
        class="imgbg"
      ></image>
      <view class="login-box">
        <view class="login_phone row" v-if="is_phone_login">
          <input
            v-model="form.phone"
            type="number"
            placeholder-style="font-size:28rpx;color:#d8d8d8"
            maxlength="11"
            placeholder="请输入手机号码"
          />
          <view class="getcaptcha" @click="getCode">{{
            time ? time + 's后获取' : '获取验证码'
          }}</view>
        </view>
        <view class="login_phone" v-if="is_phone_login">
          <input
            maxlength="6"
            placeholder-style="font-size:28rpx;color:#d8d8d8"
            type="number"
            placeholder="请输入验证码"
            v-model="form.captcha"
            @input="inputValue"
          />
          <text v-if="code_value" class="clearCode" @click="clearCode">X</text>
        </view>
        <button
          v-if="is_phone_login"
          :class="checked ? 'hava-value' : 'btn'"
          class="btn"
          @click="onSubmit"
          style="margin-top: 20rpx"
        >
          立即登录
        </button>
        <button
          v-if="!is_phone_login"
          :class="checked ? 'hava-value' : 'btn'"
          class="btn btn-left"
          @click="getInfo"
        >
          <myIcon type="weixin" style="margin-right: 10rpx" color="#fff"></myIcon>
          快捷登录
        </button>
        <view class="radio-box row">
          <view class="radio-content row">
            <radio class="radio-form" :checked="checked" @click="changeRadio" />
            <text class="row">
              我已阅读并同意<text style="text-decoration: underline" @click="openContent(3)"
                >《隐私政策》</text
              >及<text style="text-decoration: underline" @click="openContent(4)"
                >《用户服务协议》</text
              >
            </text>
          </view>
        </view>
        <view class="phonelogin" @click="is_phone_login = !is_phone_login">
          {{ is_phone_login ? '快捷' : '手机号验证' }}登录
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState } from "vuex";
import myIcon from "@/components/my-icon.vue";
export default {
  components: {
    myIcon,
  },
  data () {
    return {
      checked: false,
      // 手机号
      form: {
        phone: "",
        captcha: "",
      },
      //验证码按钮
      sending: false,
      time: "",
      phone_token: "",
      code_value: false,
      loginUrl: "",
      params: {
        website_id: "",
        category: 1,
        code: "",
      },
      is_phone_login: false,
    };
  },
  computed: {
    ...mapState(["siteConfig"]),
  },
  onLoad (options) {
    this.params.website_id = options.website_id || 1;
    let url = uni.getStorageSync("loginUrl");
    this.loginUrl = url.split("fenxiao/")[1];
    if (options.code) {
      this.params.code = options.code;
      this.postCode();
    }
  },
  methods: {
    getConfigCode () { },
    changeRadio (e) {
      this.checked = !this.checked;
    },
    inputValue (e) {
      this.code_value = e.target.value ? true : false;
    },
    clearCode () {
      this.form.captcha = "";
      this.code_value = false;
    },
    onSubmit () {
      if (!this.checked) {
        uni.showToast({
          title: "请阅读并同意相关内容",
          icon: "none",
        });
        return;
      }
      if (!this.form.phone || !/^1[3456789]\d{9}$/.test(this.form.phone)) {
        uni.showToast({
          title: "请检查手机号码格式",
          icon: "none",
        });
        return;
      }
      if (!this.form.captcha) {
        uni.showToast({
          title: "请输入验证码",
          icon: "none",
        });
        return;
      }
      this.$ajax.post(
        "/auth/client/login/phone",
        {
          website_id: this.params.website_id,
          phone: this.form.phone,
          captcha: this.form.captcha,
        },
        (res) => {
          if (res.statusCode === 200) {
            this.phone_token = res.data.token;
            uni.showToast({
              title: "登录成功",
            });
            // uni.setStorageSync("token", this.phone_token);
            uni.setStorageSync(
              "token" + this.$store.state.website_id,
              this.phone_token
            );
            // setTimeout(() => {
            // history.go(-2);
            // if (this.loginUrl.indexOf("index") != -1) {
            //   uni.switchTab({
            //     url: "/" + this.loginUrl,
            //   });
            // } else {
            //   this.$navigateTo("/" + this.loginUrl);
            // }
            // window.open(uni.getStorageSync("loginUrl"));
            // history.go(-2);
            // uni.switchTab({
            //   url: "/index/mine",
            // });
            // }, 1000);
            uni.reLaunch({
              url: "/" + this.loginUrl,
            });
          } else {
            uni.showToast({
              title: res.data.message || "登录失败",
              icon: "none",
            });
          }
        }
      );
    },
    //获取验证码按钮点击计时事件
    getCode () {
      if (this.sending) {
        return;
      }
      if (!this.form.phone || !/^1[3456789]\d{9}$/.test(this.form.phone)) {
        uni.showToast({
          title: "请检查手机号码格式",
          icon: "none",
        });
        return;
      }
      this.sending = true;
      this.$ajax.post(
        "/auth/client/send/sms/captcha",
        {
          website_id: this.params.website_id,
          phone: this.form.phone,
        },
        (res) => {
          if (res.statusCode === 200) {
            uni.showToast({
              title: "发送成功",
              icon: "none",
            });
            this.time = 60;
            this.timerDown();
            this.sending = true;
          } else {
            uni.showToast({
              title: res.data.message || "网络错误",
              icon: "none",
            });
            // this.refCode();
          }
        }
      );
    },
    timerDown () {
      // 倒计时
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.timer = setInterval(() => {
        if (this.time <= 0) {
          clearInterval(this.timer);
          this.sending = false;
          return;
        }
        this.time--;
      }, 1000);
    },
    refCode () {
      this.img_code += "?";
    },
    openContent (type) {
      this.$navigateTo(`/user/agreement?type=${type}`);
    },
    getInfo () {
      if (!this.checked) {
        uni.showToast({
          title: "请阅读并同意相关内容",
          icon: "none",
        });
        return;
      }
      if (this.$isWxWork() === "wxwork" || this.$isWxWork() === "com-wx-pc") {
        // 判断当前环境是否是企业微信
        this.$ajax.post(
          "/common/wx_work/auth/get/link/user/login/3rd",
          {
            redirect_uri: window.location.href,
            scope: "snsapi_userinfo",
            state: "state",
          },
          (res) => {
            if (res.statusCode === 200) {
              window.location.href = res.data.link;
            } else {
              uni.showToast({
                title: res.data.message || "获取登录跳转路径失败",
                icon: "none",
              });
            }
          }
        );
      } else {
        //反之走微信公众号网页登录流程
        this.$ajax.get(
          `/common/config/query/wx_public_web_login/client/${this.params.website_id}`,
          {},
          (res) => {
            if (res.statusCode === 200) {
              window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${res.data.app_id}&redirect_uri=${window.location.href}&response_type=code&scope=snsapi_userinfo&state=%7b%22w_id%22%3a1%2c%22xx%22%3a11%2c%22b%22%3a%22c%22%7d&component_appid=${res.data.component_app_id}#wechat_redirect`;
            } else {
              uni.showToast({
                title: res.data.message,
                icon: "none",
              });
            }
          }
        );
      }
    },
    postCode () {
      var url;
      if (this.$isWxWork() === "wxwork" || this.$isWxWork() === "com-wx-pc") {
        //判断当前环境切换链接
        url = "/auth/client/login/wx_work/3rd";
      } else {
        url = "/auth/client/login/wx_public";
      }
      this.$ajax.post(url, this.params, (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: "登录成功",
          });
          // uni.setStorageSync("token", res.data.token);
          uni.setStorageSync("token" + this.params.website_id, res.data.token);
          if (!res.data.phone) {
            uni.showModal({
              content: "您还没有绑定手机号 是否绑定手机号",
              cancelText: "暂不绑定",
              confirmText: "立即绑定",
              success: res => {
                if (res.confirm) {
                  // uni.redirectTo({
                  //   url: '/user/change_tel'
                  // })
                  this.$navigateTo('/user/change_tel?is_bind=1')
                } else {
                  setTimeout(() => {
                    uni.switchTab({
                      url: `/index/mine`,
                    });
                  }, 1200);
                }
              }
            })
          } else {
            // uni.switchTab({
            //   url: `/index/mine`,
            // });
            setTimeout(() => {
              uni.switchTab({
                url: `/index/mine`,
              });
              // history.go(-2);
            }, 1200);
          }
          // uni.switchTab({
          //   url: `/index/mine`,
          // });
          // let loginUrl = uni.getStorageSync("loginUrl");
          // if (loginUrl) {
          //   window.open(loginUrl);
          // }
        } else {
          uni.showToast({
            title: res.data.message || "登录失败",
            icon: "none",
          });
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.list {
  padding: 24rpx 48rpx;
  .title {
    font-size: 46rpx;
    justify-content: space-between;
    color: #333;
    margin: 96rpx 0;
    align-items: center;
  }
  .login-box {
    .login_phone {
      justify-content: space-between;
      padding: 24rpx 0;
      width: 100%;
      border-bottom: 2rpx solid #eee;
      position: relative;
      .clearCode {
        position: absolute;
        right: 0;
        top: 40rpx;
        font-size: 28rpx;
        color: #999;
      }
    }
    .radio-content {
      font-size: 28rpx;
      color: #d8d8d8;
      margin: 48rpx 0;
      align-items: center;
      .radio-form {
        transform: scale(0.7);
      }
      //     background-color: #007aff
    }
    .btn {
      color: #fff;
      text-align: center;
      font-size: 36rpx;
      width: 100%;
      // height: 112rpx;
      opacity: 0.6;
      background: #0174ff;
      border-radius: 44rpx;
    }
    .hava-value {
      opacity: 1;
    }
  }
}

::v-deep uni-radio .uni-radio-input {
  border-radius: 50% !important;
}
.btn-left {
  margin-top: 20rpx;
}
.login-box-1 {
  width: 568rpx;
  height: 680rpx;
  background: #fff;
  border-radius: 8rpx;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  .title {
    margin: 60rpx auto 0;
    font-size: 36rpx;
    font-weight: 500rpx;
    color: #333;
  }
  image {
    height: 310rpx;
    width: 310rpx;
    display: block;
    margin: 75rpx auto 67rpx;
  }
  .login {
    color: #fff;
    height: 72rpx;
    line-height: 72rpx;
    margin: 0 auto;
    width: 400rpx;
    background: #2e8cef;
    border-radius: 36px;
    position: relative;
  }
}
.imgbg {
  height: 280rpx;
  width: 100%;
  border-radius: 24rpx;
  margin-bottom: 88rpx;
}
.phonelogin {
  font-size: 24rpx;
  color: #999;
  height: 64rpx;
  width: 244rpx;
  background: #f8f8f8;
  border-radius: 32rpx;
  text-align: center;
  line-height: 64rpx;
  position: fixed;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
}
</style>
