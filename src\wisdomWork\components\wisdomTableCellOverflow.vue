<template>
<view class="cont" :style="{'--rows':rows}">
    <view class="overflow">
        <text class="text"><slot>{{content}}</slot></text>
    </view>
    <view class="btn" @click="toggleExpand" v-if="isOvered">
        <myIcon type="xiala" size="24rpx" color="#488AF6" class="right-icon"></myIcon>
    </view>
</view> 
</template>
<script>
import myIcon from "@/components/my-icon";
export default {
    props: {
        rows: { type: Number, default: 2 },
        content: { type: String, default: '' },
    },
    components: {
        myIcon
    },
    data(){
        return {
            isOvered: false,
            isExpand: false
        }
    },
    mounted(){
        const queryCont = uni.createSelectorQuery().in(this);
        const queryText = uni.createSelectorQuery().in(this);
        setTimeout(()=>{
            queryCont.select('.overflow').boundingClientRect(e => {
                const height = e.height;
                console.log({height});
                queryText.select('.text').boundingClientRect(e => {
                    if(e.height > height + 10 ){
                        this.isOvered = true;
                    }
                }).exec();
            }).exec();
        }, 50)
    },
    methods: {
        toggleExpand(){
            uni.showModal({
                content: this.content,
                showCancel: false,
                confirmText: '关闭'
            })
        }
    }
}
</script>
<style lang="scss" scoped>
$rows: var(--rows, 3);
.cont{
    display: flex;
    flex-direction: row;
}
.overflow{
    flex: 1;
    overflow:hidden;
    text-overflow: clip;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $rows;
    height: auto;
    &.expand{
        overflow: visible;
        display: inline-block;
    }
    
}
.btn{
    color: #488AF6;
    font-size: 30rpx;
    align-items: center;
    justify-content: center;
    .right-icon{
        font-weight: 600!important;
        padding: 0 8rpx;
    }
}
</style>