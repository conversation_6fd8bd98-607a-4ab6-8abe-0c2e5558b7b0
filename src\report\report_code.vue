<template>
  <view class="code">
    <view class="title-box">
      <view class="title-tips">{{ tips }}</view>
    </view>
    <view class="code-box" v-if="is_show_qrcode">
      <view class="title row">
        <view class="left">编号：{{ customer_detail.reported_sn }}</view>
        <view class="right">{{ customer_detail.created_at }}</view>
      </view>
      <view class="code-ctn">
        <view class="title row">
          <view class="cus-name">{{ customer_detail.customer_name }}</view>
          <view class="cus-build">{{ customer_detail.build_name }}</view>
        </view>
        <template>
          <canvas canvas-id="qrcode" class="qrcode canvas-hide" />
        </template>
        <image
          class="image"
          style="width:400rpx;height:400rpx"
          :src="qrcodeSrc"
          @click="$previewImage(qrcodeSrc)"
        ></image>
        <view class="ctn">
          <view class="left row">
            <text class="label">客户电话：</text>
            <text class="label-ctn">{{ customer_detail.customer_phone }}</text>
          </view>
          <view class="left row">
            <text class="label">中介公司：</text>
            <text class="label-ctn">{{ customer_detail.u_company_name }}</text>
          </view>
        </view>
        <view class="desc">
          <text>当客户到达现场后</text>
          <text>出示二维码给案场扫描确认</text>
        </view>
      </view>
    </view>
    <gmyFloatTouch></gmyFloatTouch>
  </view>
</template>

<script>
import uQRCode from "@/common/uqrcode.js";
export default {
  components: { uQRCode },
  data() {
    return {
      customer_id: "",
      tips: "提示：客户超出时效后无法带看，请及时带看哦～",
      customer_detail: {},
      website_id: "",
      qrcodeSize: uni.upx2px(400),
      qrcodeSrc: "",
      reported_list: [],
      is_show_qrcode: false,
    };
  },
  onLoad(options) {
    if (options) {
      this.customer_id = options.id;
    }
    this.website_id = options.website_id || 1;
    // REPORTED_STATUS  ('报备状态字典')
    this.$getDictionaryList("REPORTED_STATUS", {}, (res) => {
      this.reported_list = res.data.data;
      this.getDetail();
    });
  },
  methods: {
    getDetail() {
      this.$ajax.get(
        `/client/customer/reported/query/broker/id/${this.customer_id}`,
        {},
        (res) => {
          if (res.statusCode === 200) {
            this.customer_detail = res.data;
            let status = this.reported_list.find((item) => {
              return item.value == res.data.status;
            });
            if (this.customer_detail.status !== 1) {
              uni.showModal({
                title: "提示",
                content: `客户状态：${status.description}，仅报备有效显示客户码`,
                showCancel: false,
                success: (res) => {
                  if (res.confirm) {
                    this.$navigateBack();
                  }
                },
              });
            } else {
              this.make();
            }
          } else {
            uni.showToast({
              title: res.data.message || "获取失败",
              icon: "none",
            });
          }
        }
      );
    },
    make() {
      /**
       * 添加地址栏信息
       * 站点id:website_id
       * 客户id:customer_id
       * 客户状态:status
       */
      this.is_show_qrcode = true;
      let url = `https://yun.tfcs.cn/fenxiao/project_broker/all_customers?website_id=${this.website_id}&customer_id=${this.customer_detail.id}&status=${this.customer_detail.status}&type=2`;
      uQRCode.make({
        canvasId: "qrcode",
        componentInstance: this,
        text: url,
        size: this.qrcodeSize,
        margin: 10,
        backgroundColor: "#ffffff",
        foregroundColor: "#000000",
        fileType: "png",
        correctLevel: 3,
        success: (res) => {
          this.qrcodeSrc = res;
        },
      });
    },
  },
};
</script>

<style scpoed lang="scss">
.image {
  margin: 0 auto;
}
.canvas-hide {
  /* 1 */
  position: fixed;
  right: 100vw;
  bottom: 100vh;
  /* 2 */
  z-index: -9999;
  /* 3 */
  opacity: 0;
}
page {
  background: #f3f3f3;
}
.code {
  .title-box {
    height: 200rpx;
    width: 100%;
    background: #0174ff;
    border-bottom-left-radius: 68rpx;
    border-bottom-right-radius: 68rpx;
    position: relative;
    .title-tips {
      height: 68rpx;
      top: 10%;
      position: absolute;
      padding: 20rpx 48rpx;
      width: 100%;
      bottom: 80rpx;
      background: #fff9ed;
      font-size: 28rpx;
      color: #ff7401;
    }
  }
  .code-box {
    position: absolute;
    height: 708rpx;
    width: 654rpx;
    background: #fff;
    border-radius: 16rpx;
    left: 50%;
    top: 40%;
    transform: translate(-50%, -50%);
    .title {
      margin: 24rpx;
      justify-content: space-between;
      font-size: 24rpx;
      color: #666;
    }
    .code-ctn {
      .title {
        width: 400rpx;
        font-size: 32rpx;
        color: #333;
        margin: 0 auto;
      }
      .qrcode {
        margin: 20rpx auto;
        width: 400rpx;
        height: 400rpx;
      }
    }
  }
  .desc {
    text-align: center;
    margin-top: 24rpx;
    color: #666;
    font-size: 24rpx;
  }
  .ctn {
    font-size: 30rpx;
    margin-left: 25%;
  }
}
</style>
