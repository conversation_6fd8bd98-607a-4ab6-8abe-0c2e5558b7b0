<template>
  <view class="title_bar" :class="{ fixed }" :style="{ backgroundColor: backgroundColor }">
    <view v-if="_back" class="left_box" @click="back()">
      <my-icon type="4" :color="textcolor" :size="iconSize"></my-icon>
    </view>
    <view v-if="_back === false" class="_left_box"> </view>
    <view class="center_box" :class="{ default: !custom }">
      <template v-if="custom">
        <slot />
      </template>
      <view v-else class="title" :style="{ color: textcolor }">{{ title }}</view>
    </view>
    <view v-if="right" class="right_box">
      <slot name="right" />
    </view>
  </view>
</template>

<script>
import myIcon from "./my-icon";
export default {
  components: { myIcon },
  data () {
    return {};
  },
  props: {
    fixed: {
      type: Boolean,
      default: true,
    },
    custom: {
      type: Boolean,
      default: false,
    },
    _back: {
      type: Boolean,
      default: true,
    },
    right: {
      type: Boolean,
      default: true,
    },
    title: String,
    backgroundColor: {
      type: String,
      default: "#ffffff",
    },
    textcolor: {
      type: String,
      default: ''
    },
    iconSize:{
      type: String,
      default: '27px'
    }
  },
  methods: {
    back () {
      if (getCurrentPages().length > 1) {
        uni.navigateBack();
      } else {
        uni.switchTab({
          url: "/",
        });
      }
    },
  },
};
</script>

<style scoped lang="scss">
.title_bar {
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 44px;
  padding: 7px 3px;
  &.fixed {
    position: fixed;
    top: 0;
  }
  .left_box,
  .right_box {
    min-width: 27px;
  }
  .left_box {
    width: 0;
  }
  .center_box {
    flex: 1;
    overflow: hidden;
    &.default {
      padding: 0 40px;
    }
    .title {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      font-size: 16px;
      font-weight: 700;
    }
  }
}
</style>
