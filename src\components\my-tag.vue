<template>
  <text
    class="my-tag"
    :class="{
      success: type === 'success',
      info: type === 'info',
      warning: type === 'warning',
      danger: type === 'danger',
      small: size === 'small',
      big: size === 'big',
      default: type === 'default',
    }"
    @click="$emit('click')"
  >
    <slot />
  </text>
</template>

<script>
export default {
  components: {},
  props: {
    type: String,
    size: String,
  },
  data() {
    return {};
  },
  methods: {},
};
</script>

<style scoped lang="scss">
.my-tag {
  display: inline-block;
  padding: 10rpx 18rpx;
  font-size: $uni-font-size-sm;
  line-height: 1;
  border-radius: 4rpx;
  flex-shrink: 0;
  color: $uni-color-primary;
  background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
  ~ .my-tag {
    // margin-left: 20rpx;
  }
  &.success {
    color: $uni-color-success;
    background-color: rgba($color: $uni-color-success, $alpha: 0.1);
  }
  &.info {
    color: $uni-text-color-grey;
    background-color: rgba($color: $uni-text-color-grey, $alpha: 0.1);
  }
  &.danger {
    color: $uni-color-error;
    background-color: rgba($color: $uni-color-error, $alpha: 0.1);
  }
  &.small {
    border-radius: 3rpx;
    padding: 6rpx 14rpx;
    font-size: 22rpx;
  }
  &.big {
    padding: 10rpx 20rpx;
    border-radius: 8rpx;
    font-size: $uni-font-size-base;
  }
  &.default {
    color: $uni-color-primary;
    background-color: rgba($color: $uni-color-primary, $alpha: 0.1);
  }
}
</style>
