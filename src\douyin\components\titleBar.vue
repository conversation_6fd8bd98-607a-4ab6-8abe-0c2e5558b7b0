<template>
  <view class="box">
    <view
      class="title_bar"
      :class="{ fixed }"
      :style="{ backgroundColor: backgroundColor, top: top }"
    >
      <view v-if="showback" class="left_box" @click="back()">
        <my-icon type="back" :color="textcolor" size="50rpx"></my-icon>
      </view>
      <slot name="left" />
      <view class="center_box" :class="{ default: !custom }">
        <template v-if="custom">
          <slot />
        </template>
        <view v-else class="title" :style="{ color: textcolor }">{{ title }}</view>
      </view>
      <view class="right_box">
        <slot name="right" />
      </view>
    </view>
    <view class="bk" v-if="showBk" :style="{ paddingTop: status_top + 'px' }"></view>
  </view>
</template>

<script>
import myIcon from '@/components/my-icon'
export default {
  components: { myIcon },
  data () {
    return {}
  },
  props: {
    fixed: {
      type: Boolean,
      default: true
    },
    custom: {
      type: Boolean,
      default: false
    },

    showBk: {
      type: <PERSON>olean,
      default: true
    },
    title: String,
    backgroundColor: {
      type: String,
      default: '#ffffff'
    },
    top: {
      type: String,
      default: '0'
    },
    showback: {
      type: Boolean,
      // #ifndef MP
      default: true,
      // #endif
      // #ifdef MP
      default: false
      // #endif
    },
    textcolor: {
      type: String,
      default: ''
    },
  },
  computed: {
    status_top () {
      return this.$store.state.systemInfo.statusBarHeight
    }
  },
  created () {
    console.log(this.textcolor, 1111111);
  },
  methods: {
    back () {
      if (getCurrentPages().length > 1) {
        uni.navigateBack()
      } else {
        // #ifdef H5
        var ua = window.navigator.userAgent.toLowerCase();
        //通过正则表达式匹配ua中是否含有MicroMessenger字符串
        if (ua.match(/MicroMessenger/i) == 'micromessenger') {
          uni.switchTab({
            url: '/pages/index/index'
          })
        } else {
          window.history.go(-1)
        }
        // #endif
        // #ifndef H5
        uni.switchTab({
          url: '/pages/index'
        })
        // #endif
      }
    }
  }
}
</script>

<style scoped lang="scss">
view {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}
.bk {
  height: 44px;
}

.title_bar {
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 44px;
  padding: 7px 3px;
  box-sizing: border-box;
  &.fixed {
    position: fixed;
    top: 0;
    z-index: 99999;
  }
  .left_box,
  .right_box {
    min-width: 27px;
  }
  .center_box {
    flex: 1;
    overflow: hidden;
    text-align: center;
    &.default {
      padding: 0 40px;
    }
    .title {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      font-size: 16px;
      font-weight: 700;
    }
  }
}
</style>
