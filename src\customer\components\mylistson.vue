<template>
  <view>
    <view class="mylist" v-for="item in arr" :key="item.id" @click="onClick(item)">
      <view class="mylist-title row">
        <view
          class="mylist-title_left"
          :style="{
            backgroundColor: item.sex == 1 ? '#3172F6' : item.sex == 2 ? '#FF82BE' : '#3172F6',
          }"
          >{{ item.cname[0] }}</view
        >
        <view class="mylist-title_right">
          <view class="mylist-title_right_top row">
            <span class="mylist-title_right_top_name">{{ item.cname }}</span>
            <image
              v-if="item.wxqy_id"
              class="mylist-title_right_top_qw"
              src="@/static/customer/qw.png"
              mode="widthFix"
            />
            <template v-if="item.level != null">
              <image
                class="mylist-title_right_top_qw mylist-title_right_top_level"
                :src="
                  ('/yidongduan/customer/level/' + item.level.title + '.png') | imageFilter('w_80')
                "
                mode="widthFix"
              />
            </template>
            <view class="gong_">
              <span class="gong_status" v-if="item.public2_status == 1"
                ><view class="gong_status_">已转公</view>
              </span>
              <span class="gong_status" v-if="item.public2_status == 2"
                ><view class="gong_status_"> 已掉公</view>
              </span>
            </view>
            <!-- <span class="mylist-title_right_top_tracking">{{
              item.tracking && (item.tracking.title || '')
            }}</span> -->

            <!-- <text
              :style="{
                backgroundColor: item.level.color,
              }"
              class="mylist-title_right_top_level"
              v-if="item.level_id > 0"
              >{{ item.level.title }}</text
            > -->
          </view>
          <!-- <view class="mylist-title_right_bottom row">
            <span>{{ item.mobile | mobileFilter }}</span>
            <span
              v-if="!item.mobile_place"
              class="search_address"
              @click.prevent.stop="searchAddress(item)"
              >归属地查询</span
            >
            <span v-else class="search_address address">{{ item.mobile_place }}</span>
          </view> -->
          <view class="mylist-title_right_top_tracking_box row">
            <span class="mylist-title_right_top_tracking" v-if="item.tracking">{{
              item.tracking && (item.tracking.title || '')
            }}</span>
            <span
              class="mylist-title_right_top_tracking mylist-title_right_top_source"
              v-if="item.source && item.source.title"
              >{{ item.source ? item.source.title : '' }}</span
            >
            <span
              class="mylist-title_right_top_tracking mylist-title_right_top_source"
              v-if="typeFilter(item.type)"
              >{{ typeFilter(item.type) }}</span
            >
          </view>
          <!-- <view class="gong_">
            <span class="gong_status" v-if="item.public2_status == 1"
              ><view class="gong_status_">已转公</view>
            </span>
            <span class="gong_status" v-if="item.public2_status == 2"
              ><view class="gong_status_"> 已掉公</view>
            </span>
          </view> -->
        </view>
        <view
          class="mylist-linqu"
          :class="{ disabled: checkStatus(item) }"
          v-if="(type == 'seas' || type == 'qianzai') && !item.follow_id"
          >认领</view
        >
        <!-- <view
          class="mylist-tarenlinqu img"
          v-if="item.follow_id || !((type == 'seas' || type == 'qianzai') && !item.follow_id)"
          @click.prevent.stop="tel(item)"
          ><image mode="widthFix" src="@/static/customer/tel.png"></image
        ></view> -->
      </view>
      <view class="mylist-content">
        <view class="mylist-content-item row">
          <view class="label">手机号码：</view>
          <view class="tel flex-1">
            <view class="tel_num row">
              <view
                class="content mobile flex-1"
                :class="{
                  has_follow: item.last_call_follow && item.last_call_follow.client_id > 0,
                }"
                @click.prevent.stop="
                  ;(item.show_last_call_follow_content = !item.show_last_call_follow_content),
                    $forceUpdate()
                "
              >
                <span
                  class="un_tong"
                  :class="{ tong: item.last_call_follow && item.last_call_follow.call_status > 0 }"
                >
                  {{ item.mobile | mobileFilter }}
                </span>
              </view>
              <view
                class="mylist-tarenlinqu mylist-tel img"
                v-if="item.follow_id > 0"
                @click.prevent.stop="checkFollowStatus(item, tel)"
                ><image mode="widthFix" src="../../static/icon/index/dianhua.png"></image
              ></view>
            </view>
            <view
              v-if="!item.mobile_place"
              class="search_address"
              @click.prevent.stop="checkFollowStatus(item, searchAddress)"
            >
              <view class="search_address_c"> 归属地查询</view></view
            >
            <view v-else class="search_address address">{{ item.mobile_place }}</view>
            <view
              class="last_follow_content"
              v-if="
                item.last_call_follow &&
                item.last_call_follow.content &&
                item.show_last_call_follow_content
              "
              >{{ item.last_call_follow.content }}</view
            >
          </view>
        </view>
        <!-- <view class="mylist-content-item row">
          <view class="label">客户来源：</view>
          <view class="content">{{ item.source ? item.source.title : '--' }}</view>
        </view> -->

        <!-- <view class="mylist-content-item row">
          <view class="label">客户类型：</view>
          <view class="content">{{ typeFilter(item.type) }}</view>
        </view> -->
        <view class="mylist-content-item row" v-if="item.intention_community">
          <view class="label">客户意向：</view>
          <view class="content">{{ item.intention_community }}</view>
        </view>
        <view class="mylist-content-item row" v-if="item.remark && item.remark != 'undefined'">
          <view class="label">客户线索：</view>
          <!-- <view class="content" >{{ item.tracking.desc }}</view> -->
          <view class="content">{{ item.remark || '--' }}</view>
        </view>
        <!-- <view class="mylist-content-item row" v-if="item.last_follow">
          <view class="label">跟进状态：</view>
          <view class="content">{{
            item.last_follow && item.last_follow.type_title ? item.last_follow.type_title.title : ''
          }}</view>
        </view> -->
        <!-- <view class="mylist-content-item row" v-if="item.remark">
          <view class="label">备注内容：</view>
          <view class="content">{{ item.remark }}</view>
        </view> -->
        <view class="mylist-content-item row">
          <view class="label">创建时间：</view>
          <view class="content">{{ item.created_at }}</view>
        </view>
        <view class="mylist-content-item row">
          <view class="flex-1 row">
            <view class="label">最后更新：</view>
            <view class="content">{{ item.operation_at }}</view>
          </view>
          <view
            class="more"
            v-if="showMore > 0 || item.follow_id == user_info.id"
            @click.prevent.stop="checkFollowStatus(item, more)"
          >
            更多
          </view>
        </view>
      </view>
      <view class="mylist-follow" v-if="item.follow_id > 0">
        <view class="mylist-follow_name" v-if="type == 'my'">
          维护人：{{ item.follow_user ? item.follow_user.user_name : '--' }}
        </view>
        <view class="mylist-follow_name" v-else>
          跟进人 ：{{
            item.last_follow && item.last_follow.admin ? item.last_follow.admin.user_name : '--'
          }}
        </view>
        <view class="mylist-follow_timeline row">
          <view class="time">{{
            item.last_follow && item.last_follow.created_at | captureTime
          }}</view>
          <!-- |企微联系  暂时隐藏-->
          <view class="c-right">
            <!-- {{
              item.last_follow && item.last_follow.type_title
                ? item.last_follow.type_title.title
                : ''
            }} -->
            <view class="desc">{{ item.last_follow && item.last_follow.content }}</view>
          </view>
        </view>
      </view>
      <view class="mylist-label" v-if="item.label && item.label.length > 0">
        <view class="mylist-label_name"> 客户标签 </view>
        <view class="mylist-label_list row">
          <view class="item" v-for="(i, i1) in item.label" :key="i1">{{ i }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    arr: Array,
    type: {
      type: String,
      default: "seas", // seas:公海  my:我的  qianzai  潜在客户
    },
    showMore: {
      type: [String, Number, Boolean],
      default: true,
    }
  },
  data () {
    return {
      type_list: [],
      label_list: [
        // { id: 1, name: "标签1" },
        // { id: 2, name: "标签2" },
        // { id: 3, name: "标签3" },
        // { id: 4, name: "标签4" },
        // { id: 5, name: "标签5" },
      ],
    };
  },
  filters: {
    mobileFilter (val) {
      let reg = /^(.{3}).*(.{3})$/;
      return val.replace(reg, "$1*****$2");
    },
    captureTime (fullTime) {
      if (!fullTime) return ""
      var timeArr = fullTime
        .replace(" ", ":")
        .replace(/\:/g, "-")
        .split("-");
      var nian = timeArr[0];
      var yue = timeArr[1];
      var ri = timeArr[2];
      var shi = timeArr[3];
      var fen = timeArr[4];
      var miao = timeArr[5];

      return shi + ":" + fen;
    },
  },
  created () {
    console.log(this.arr);
    // const value = uni.getStorageSync('mobiles')
    // const text = uni.getStorageSync('keywords')
    // console.log(text,'搜索内容');
    // if(value == '姓名'){
    //  this.arr.forEach(element => {
    //   // console.log( element.cname,'1111112222'); 
    //   element.cname === text 
    //   this.$emit('getCaname',)
    //  })
    // }else if(value == '线索'){
    //   this.arr.filter((item)=>{
    //   // console.log(item,'111');
    //   console.log( item.remark ,'1111s'); 
    //   return item.remark == text
    //  })
    // }
    this.user_info = uni.getStorageSync("userInfo") ? JSON.parse(uni.getStorageSync("userInfo")) : {}
    this.arr.map(item => {
      item.show_last_call_follow_content = false
      return item
    })
    this.getTypeData();
  },
  methods: {

    checkFollowStatus (e, callback) {
      this.$ajax.get('/admin/crm/client/verify_follow', {}, res => {
        if (res.statusCode == 200) {
          if (res.data && res.data.id > 0 && res.data.client_id > 0) {
            uni.showModal({
              title: '提示',
              content: '您有查看电话未跟进 去跟进？',
              success: (result) => {
                if (result.confirm) {
                  let type = (this.type == 'my') ? 2 : 3
                  let source = (this.type == 'my') ? 2 : 1
                  this.$navigateTo(
                    `/customer/detail?id=${res.data.client_id}&form=${type}&source=${source}`
                  );
                  return
                } else if (result.cancel) {
                  console.log('用户点击取消');
                }
              }
            })
          } else {
            callback && callback(e)
          }
        } else {
          callback && callback(e)
        }
      })
    },
    getTypeData () {
      this.$ajax.get("/qywx/type/list", {}, (res) => {
        if (res.statusCode === 200) {
          this.type_list = res.data;
        }
      });
    },
    more (item) {
      this.$emit("more", item)
    },
    tel (item) {
      this.$emit("tel", item)
    },
    checkStatus (item) {
      if (item.push_type == 2) {
        // 手动认领  
        // 潜在用户 我司成交的不可认领
        if ((this.type == "qianzai" || this.type == "seas") && item.tracking_identify == 1) {
          return true
        }
        // 掉工转公标记的可以领取 
        if (item.public2_status > 0) {
          return false
        }
        // 潜在客户可以领取
        if (this.type == "qianzai") {
          return false
        }
        // 其他情况 不可领取 
        return true
      }
      return false
      // if (item.public2_status>0 || )
    },
    searchAddress (item) {
      this.$emit("searchAddress", item)
    },
    typeFilter (val) {
      let arr = this.type_list.filter((item) => {
        if (item.id == val) {
          return item;
        }
      })[0];
      if (arr) {
        return arr.title;
      }
    },
    onClick (item) {
      this.$emit("onClick", item);
    },
  },
};
</script>

<style scoped lang="scss">
.mylist {
  background: #fff;
  border-radius: 10rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  &-title {
    align-items: center;
    position: relative;
    &_left {
      text-align: center;
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      color: #fff;
      line-height: 80rpx;
    }
    &_right {
      margin-left: 24rpx;
      &_top {
        align-items: center;

        &_name {
          max-width: 220rpx;
          font-size: 28rpx;
          color: #282a2f;
          display: inline-block;
          // flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        &_qw {
          height: 36rpx;
          width: 36rpx;
          margin-left: 8rpx;
          &.mylist-title_right_top_level {
            width: 60rpx;
          }
        }
        &_tracking {
          color: #3e8afd;
          margin: 0 12rpx 0 0;
          font-size: 24rpx;
          padding: 4rpx 10rpx;
          min-width: 96rpx;
          text-align: center;
          &.mylist-title_right_top_source {
            background: #f1f4fa;
            border-radius: 4px;

            color: #8a929f;
          }
        }
        &_level {
          image {
            width: 40rpx;
            height: 40rpx;
          }
          // color: #fff;
          // padding: 4rpx 24rpx;
          // border-radius: 4rpx;
        }
      }
      &_bottom {
        margin-top: 8rpx;
        span {
          color: #828488;
          font-size: 22rpx;
        }
      }
    }
  }
  &-linqu {
    position: absolute;
    border-radius: 28rpx;
    background: #eff4fc;
    border: 1px solid #eaeaea;
    right: 0;
    padding: 12rpx 32rpx;
    color: #3e8afd;
    &.disabled {
      background: #eff4fc;
      color: #8a929f;
    }
  }
  &-tarenlinqu {
    position: absolute;
    border-radius: 28rpx;
    background: #c5c5c5;
    border: 1px solid #c5c5c5;
    right: 0;
    padding: 8rpx 14rpx;
    color: #fff;
  }
  &-content {
    margin: 24rpx 0;
    &-item {
      font-size: 28rpx;
      color: #828488;
      line-height: 50rpx;
      .label {
        white-space: nowrap;
        min-width: 140rpx;
      }
      .content {
        max-width: 564rpx;
        &.mobile {
          &.has_follow {
            display: inline-block;
            .un_tong {
              position: relative;
              &.tong {
                &::after {
                  background: #9edf2e;
                }
              }
              &::after {
                content: '';
                position: absolute;
                right: -10rpx;
                top: -2rpx;
                background: #f56c6c;
                width: 10rpx;
                height: 10rpx;
                border-radius: 50%;
              }
            }
          }
        }
      }
    }
  }
  &-follow {
    padding: 12rpx 0;
    border-top: 1px solid #e6e6e6;
    border-bottom: 1px solid #e6e6e6;
    &_name {
      font-size: 28rpx;
    }
    &_timeline {
      font-size: 28rpx;
      margin-top: 20rpx;
      .time {
        color: #828488;
      }
      .c-right {
        margin-left: 28rpx;
        .desc {
          // margin-top: 16rpx;
          color: #828488;
          line-height: 1.3;
        }
      }
    }
  }
  &-label {
    &_name {
      margin-top: 18rpx;
      font-size: 24rpx;
    }
    &_list {
      margin-top: 16rpx;
      flex-wrap: wrap;
      .item {
        margin-bottom: 16rpx;
        padding: 8rpx 16rpx;
        border-radius: 8rpx;
        background: #e8f1ff;
        color: #2f6aff;
        font-size: 24rpx;
        margin-right: 20rpx;
      }
    }
  }
}
.mylist {
  .mylist-content-item {
    .tel {
      justify-content: space-between;
    }
  }
}
.mylist-tarenlinqu {
  width: 60rpx;
  height: 60rpx;
  &.mylist-tel {
    position: static;
  }
  &.img {
    background: #fff;
    border: 0;
  }
  image {
    width: 100%;
    height: 100%;
  }
}
.mylist-content-item {
  .more {
    color: #2d84fb;
    font-size: 24rpx;
    padding: 4rpx 43rpx;
    border-radius: 40rpx;
    border: 1px solid #2d84fb;
  }
}
.mylist-content-item .search_address {
  // margin-left: 10rpx;\
  display: block;
  margin-top: -5px;
  color: #999;
  font-size: 28rpx;
  .search_address_c {
    font-size: 24rpx;
    display: inline-block;
    padding: 2rpx 10rpx;
    border-radius: 4rpx;
    background: #e8f1ff;
    line-height: 1;
    border: 1rpx solid #2d84fb;
    color: #2d84fb;
  }
  // &.address {
  //   border: none;
  //   color: #999;
  //   background: #fff;
  // }
}
.gong_ {
  margin-left: 10rpx;
}
.gong_ .gong_status .gong_status_ {
  display: inline-block;
  color: #f56c6c;
  // margin-top: 8rpx;
  font-size: 22rpx;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  margin-left: 8rpx;
}
.mylist-title_right_top_tracking_box {
  margin-top: 20rpx;
  .mylist-title_right_top_tracking {
    ~ .mylist-title_right_top_tracking {
      margin-left: 10rpx;
    }
  }
}
</style>
