<template>
<myPopup :show="show"  @close="cancle">
    <view class="filter-wrapper">
    <view class="header">
            <view class="action cancle" @click.stop="cancle">取消</view>
            <view class="title">{{placeholder}}</view>
            <view class="action confirm" :class="{loading: submiting}" @click.stop="confirm">确认</view>
        </view>
        <view class="body">
            <view v-if="adminTypeSelect" class="left" v-show="!loading">
                <tCustomPickerView v-model="adminTypeValue" :datas="adminTypeList" ref="adminTypePicker"></tCustomPickerView>
            </view>
            <view class="right">
                <tCustomPickerView v-model="selectedId" :datas="memberList" ref="picker"  v-if="show" :multiple="multiple"
                    filter-placeholder="搜索成员"
                    filterable :map="{label: 'user_name', value: 'id'}" :loading="loading">
                </tCustomPickerView>
            </view>
        </view>
    </view>
</myPopup>
</template>

<script>
import myPopup from '@/components/myPopup';
import tCustomPickerView from '@/components/tplus/tCustomPickerView';
export default {
    components: {
        myPopup, tCustomPickerView
    },
    props: {
        value: { type: [ Number, String, Array ], default:'' },
        adminType: { type: Number, default: 0 },
        visible: { type: Boolean, default: false },
        adminTypeSelect: { type: Boolean, default: false },
        multiple: { type: Boolean, default: false },
        placeholder: { type: String, default: '' },
        submiting: { type: Boolean, default: false },
        filterId: { type: [Number, Array], default: 0 },
        api: { type: String, default:'/qywx/home/<USER>' },
    },
    data() {
        return {
            loading: false,
            show: false,
            isInited: false,
            curValue: '',
            list: [],
            adminTypeValue: 0,
            adminTypeList: [
                {value: 0, label: '全部'},
                {value: 1, label: '录入人'},
                {value: 2, label: '维护人'},
                {value: 3, label: '带看人'},
                {value: 4, label: '成交人'},
            ]
        }
    },
    computed: {
        memberList(){
            if(filterIds === 0){
                return this.list;
            }
            const filterIds = Array.isArray(this.filterId) ? this.filterId : [this.filterId];
            return this.list.filter(item => !filterIds.includes(item.id));
        },
        selectedId: {
            get() {
                if(this.multiple){
                    return this.curValue || [];    
                }
                return this.curValue || '';
            },
            set(val) {
                console.log(val);
                this.curValue = val;
            }
        }
    },
    watch: {
        value: {
            handler(val){
                this.selectedId = val
            },
            immediate: true
        },
        visible(val){
            this.show = val;
            if(val && !this.isInited){
                this.isInited = true;
                this.getList();
            }
        },
        show(val){
            this.$emit('update:visible', val)
        },
        adminType(val){
            this.adminTypeValue = val;
        }
    },
    created() {
        
    },
    methods: {
        getList(){
            this.loading = true;
            this.$ajax.get(this.api, {}, (res) => {
                this.loading = false;
                if (res.statusCode === 200) {
                    this.list = res.data;
                } else {
                    uni.showToast({
                        title: res.data.message,
                        icon: "none",
                    });
                }
            }, er=>{
                console.log(er)
                this.loading = false;
            })
        },
        cancle(){
            this.show = false;
            this.selectedId = this.value;
            this.adminTypeValue = this.adminType;
        },
        confirm(){
            if(this.submiting){
                return;
            }
            this.$emit('input', this.selectedId);
            if(this.adminTypeSelect){
                this.$emit('update:adminType', this.adminTypeValue);
                this.$emit('confirm', this.$refs.picker.getCheckedItem(), this.$refs.adminTypePicker.getCheckedItem());
            }else{
                this.$emit('confirm', this.$refs.picker.getCheckedItem());
            }
        },
    }
}

</script>

<style scoped lang="scss"> 
.filter-wrapper{
    background-color: #fff;
    border-top-left-radius: 24rpx;
    border-top-right-radius: 24rpx;
    overflow: hidden;
}
.header{
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 46px;
      
    .title{
        flex: 1;
        color: #999;
        text-align: center;
        display: inline-block;
        max-width: 50%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .action{
        padding: 10px 16px;
        font-size: 17px;
        &.cancle{
            color: #888;
        }
        &.confirm{
            position: relative;
            color: #007aff;
            &.loading{
                opacity: .6;
            }
        }
    }
}
.body{
    position: relative;
    display: flex;
    flex-direction: row;
    padding-top: 140rpx;
    .left{
        width: 206rpx;
        ::v-deep .picker-column{
            background-color: #f9f9f9;
            .picker-column-item{
                height: 92rpx;
                &.checked{
                    color: #2d84fb;
                    background-color: #fff;
                    .icon-checked{
                        display: none;
                    }
                    &::before{
                        content: " ";
                        display: inline-block;
                        position: absolute;
                        left: 0;
                        height: 28rpx;
                        width: 6rpx;
                        background-color: #2d84fb;
                    }
                }
                
            }
        }
    }
    .right{
        flex: 1;
        ::v-deep .search-input-wrapper{
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
        }
    }
}
</style>