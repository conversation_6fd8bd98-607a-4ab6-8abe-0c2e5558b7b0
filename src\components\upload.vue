<template>
  <view class="img-list row">
    <canvas id="canvas" style="width:200px;height:200px"></canvas>
    <view class="img-box" v-for="(image, index) in imageList" :key="index">
      <video :src="image" v-if="$isVideoReg(image)"></video>
      <image
        mode="aspectFill"
        @click="$previewImage(image)"
        :src="image"
        :v-if="imgShow && !$isVideoReg(image)"
      ></image>
      <view v-if="del" class="del-img" @click="delImg(index)">x</view>
    </view>
    <view class="icon-box " v-if="!$isVideoReg(imageList[0])">
      <myIcon
        @click="chooseVideoImage"
        type="icons01"
        color="#d8d8d8"
        size="100rpx"
      ></myIcon>
    </view>
  </view>
</template>

<script>
import myIcon from "@/components/my-icon";
import config from "@/page_outside/config/index";
export default {
  components: {
    myIcon,
  },
  props: {
    // 图片存放
    imageList: {
      type: Array,
      default: function() {
        return [];
      },
    },
    del: {
      type: Boolean,
      default: true,
    },
    imgShow: {
      type: Boolean,
      default: true,
    },
    time_watermark: {
      type: [Number, String],
      default: 0,
    },
    upload_category: [String, Number],
    is_visit: [Boolean],
    is_video: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 视频存放
      src: "",
      sourceTypeIndex: 2,
      sourceType: ["拍摄", "相册", "拍摄或相册"],
      VideoOfImagesShow: true,
      per: 0,
      image:''
    };
  },
  methods: {
    addImg() {
      uni.chooseImage({
        count: 9, // 选择数量  默认9
        sizeType: ["compressed "],
        sourceType: this.is_visit ? ["camera"] : ["camera", "album"], //从相册选择
        success: (value) => {
          if (value.tempFilePaths.length > 9) {
            uni.showToast({
              title: "最大上传9张",
              icon: "none",
              duration: 2000,
            });
          } else {
            uni.showLoading({
              title: "上传中",
            });
            for (var i = 0; i < value.tempFilePaths.length; i++) {
              this.compressionImg(value.tempFilePaths[i]);
            }
          }
          // this.imageList = this.imageList.concat(res.tempFilePaths)  //头条
        },
      });
    },
    compressionImg(img) {
      var that = this;
      uni.getImageInfo({
        src: img,
        success: function(res) {
          let canvasWidth = res.width; //图片原始长宽
          let canvasHeight = res.height;
          let base = canvasWidth / canvasHeight;
          if (canvasWidth > 500) {
            canvasWidth = 500;
            canvasHeight = Math.floor(canvasWidth / base);
          }
          // let img = new Image();
         this. image.src = res.path; // 要压缩的图片
          let canvas = document.createElement("canvas");
          let ctx = canvas.getContext("2d");
          canvas.width = canvasWidth;
          canvas.height = canvasHeight;
          //  将图片画到canvas上面   使用Canvas压缩
          ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);
          canvas.toBlob(function(fileSrc) {
            let imgSrc = window.URL.createObjectURL(fileSrc); //原生JS生成文件路径
            uni.downloadFile({
              url: imgSrc, //仅为示例，并非真实的资源
              success: (resFilePath) => {
                if (resFilePath.statusCode === 200) {
                  // canvas.toDataURL 返回的是一串Base64编码的URL
                  // 指定格式 PNG
                  var uploadimg = canvas.toDataURL("image/png");
                  uni.uploadFile({
                    url: config.h5Api + config.uploadApi,
                    method: "POST",
                    header: {
                      Authorization:
                        "Bearer " +
                        uni.getStorageSync(
                          "token" + that.$store.state.website_id
                        ),
                    },
                    name: "file",
                    filePath: uploadimg,
                    formData: {
                      category: that.upload_category,
                      time_watermark: that.time_watermark,
                    },
                    success: (res) => {
                      // let imgUrls = JSON.parse(res.data); //微信和头条支持
                      if (res.statusCode === 200) {
                        let img_list = JSON.parse(res.data);
                        that.imageList.push(img_list.url);
                        uni.hideLoading();
                      } else {
                        uni.showToast({
                          title: res.data.message || "上传失败",
                          icon: "none",
                        });
                      }
                    },
                  });
                }
              },
            });
          });
        },
      });
    },


    delImg(index) {
      this.imageList.splice(index, 1);
    },
    chooseVideoImage() {
      if (this.is_video) {
        uni.showActionSheet({
          title: "选择上传类型",
          itemList: this.imageList.length > 0 ? ["图片"] : ["图片", "视频"],
          success: (res) => {
            if (res.tapIndex == 0) {
              this.addImg();
            } else {
              this.addVideo();
            }
          },
        });
      } else {
        this.addImg();
      }
    },
    addVideo() {
      var that = this;
      // 选择视频
      uni.chooseVideo({
        count: 1,
        sourceType: ["camera", "album"],
        success: (res) => {
          const videoSrc = res.tempFilePath;
          uni.showLoading({
            title: "正在上传",
            mask: true,
          });
          const uploadTask = uni.uploadFile({
            url: config.h5Api + config.uploadApi,
            methods: "POST",
            header: {
              Authorization:
                "Bearer " +
                uni.getStorageSync("token" + that.$store.state.website_id),
            },
            name: "file",
            filePath: videoSrc,
            formData: {
              category: 103,
            },
            success: (res) => {
              if (res.statusCode === 200) {
                uni.hideLoading();
                let video_list = JSON.parse(res.data);
                that.imageList.push(video_list.url);
              } else {
                uni.showToast({
                  title: res.data.message || "上传失败",
                  icon: "none",
                });
              }
            },
            complete: (com) => {
              console.log(com);
            },
          });
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
#canvas {
  position: fixed;
  left: -1000px;
}
.img-list {
  justify-content: flex-start;
  flex-wrap: wrap;
  // margin-top: 40rpx;
  .icon-box {
    margin-top: 10rpx;
    align-items: center;
    padding: 46rpx;
    width: 180rpx;
    height: 180rpx;
    background: #efefef;
    border-radius: 10rpx;
  }
  .img-box {
    position: relative;
    .del-img {
      font-size: 32rpx;
      align-items: center;
      position: absolute;
      color: #fff;
      width: 40rpx;
      height: 40rpx;
      top: 0;
      right: 10rpx;
      border-radius: 50%;
      background: #708efc;
    }
    image {
      margin-right: 30rpx;
      margin-top: 10rpx;
      width: 180rpx;
      height: 180rpx;
      border-radius: 10rpx;
    }
    video {
      margin-right: 30rpx;
      margin-top: 10rpx;
      width: 180rpx;
      height: 180rpx;
      border-radius: 10rpx;
    }
  }
  &::after {
    content: "";
    margin-right: 30rpx;
    margin-top: 10rpx;
    width: 160rpx;
  }
}
</style>
