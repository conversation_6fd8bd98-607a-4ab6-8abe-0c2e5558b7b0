<template>
  <view class="merge">
    <view class="client-list">
      <view
        class="client-item row"
        :class="{ isactive: item.id === form_info.client_id }"
        v-for="item in client_list"
        :key="item.id"
        @click="form_info.client_id = item.id"
      >
        <view class="left">
          {{ item.cname[0] }}
        </view>
        <view class="right">
          <view class="t">{{ item.cname }}</view>
          <view class="b">{{ item.mobile }}</view>
        </view>
      </view>
    </view>
    <load-more :status="load_status"></load-more>
    <view class="btn-bottom">
      <view class="btn-box row">
        <view class="btn c2" @click="$navigateBack()">取消</view>
        <view class="btn" @click="onCreateData">提交</view>
      </view>
    </view>
  </view>
</template>

<script>
import loadMore from "@/components/loadMore.vue";
export default {
  components: {
    loadMore,
  },
  data () {
    return {
      params: {
        page: 1,
        per_page: 10,
        type: 2,
      },
      client_list: [],
      load_status: "",
      form_info: {
        client_id: "",
      },
    };
  },
  onLoad (options) {
    this.form_info.openid = options.wxuserid;
    this.getDataList();
  },
  methods: {
    getDataList () {
      this.load_status = "loading";
      if (this.params.page === 1) {
        this.client_list = [];
      }
      this.$ajax.get("/qywx/client/bind_search", this.params, (res) => {
        this.load_status = "loadend";
        if (res.statusCode === 200) {
          this.client_list = this.client_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.load_status = "nomore";
          }
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    onCreateData () {
      if (!this.form_info.client_id) {
        uni.showToast({
          title: "请选择用户",
          icon: "none",
        });
        return;
      }
      this.$ajax.post("/qywx/client/bind", this.form_info, (res) => {
        if (res.statusCode === 200) {
          this.$navigateTo(
            `/customer/detail?id=${this.form_info.client_id}&form=2`
          );
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
  },
  onReachBottom () {
    if (this.load_status === "nomore") {
      return;
    }
    this.params.page++;
    this.getDataList();
  },
};
</script>

<style scoped lang="scss">
page {
  color: #2e3c4e;
}
.merge {
}
.client-list {
  .client-item {
    text-align: center;
    align-items: center;
    padding: 10px 12px;
    border-bottom: 1px solid #f6f6f6;
    .left {
      width: 30px;
      line-height: 30px;
      height: 30px;
      border-radius: 50%;
      color: #fff;
      background: #2d84fb;
      margin-right: 12px;
    }
    .right {
      align-items: flex-start;
      line-height: 20px;
    }
    &.isactive {
      background: #f6f6f6;
    }
  }
}
.btn-bottom {
  height: 100px;
  .btn-box {
    background: #fff;
    justify-content: space-between;
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 24px;
    .btn {
      width: 48%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      border-radius: 6px;
      border: 1px solid #2d84fb;
      color: #fff;
      background: #2d84fb;
      font-weight: 500;
      &.c2 {
        border: 1px solid #dde1e9;
        background: #fff;
        color: #8a929f;
      }
    }
  }
}
</style>
