<template>
  <view class="follow">
    <view class="form-box" v-if="from === 'custom'">
      <view class="label">选择客户等级</view>
      <view class="level-box flex-row">
        <view
          :class="{ is_level: item.id == params.level }"
          class="level-item flex-box items-center"
          v-for="(item, index) in levelArr"
          :key="index"
          @click="onChangeLevel(item)"
        >
          <view class="name">
            {{ item.name }}
          </view>
          <view class="tip">
            {{ item.tip }}
          </view>
        </view>
      </view>
    </view>
    <view class="form-box" v-if="type != 'shikan'">
      <view class="label">选择跟进类型</view>
      <view class="type-box flex-row">
        <view
          class="type-item"
          v-for="item in cate_list"
          :key="item.value"
          :class="{ is_type: item.value == params.type }"
          @click="onChangeType(item)"
        >
          <view class="checked">
            <image v-show="item.value == params.type" src="@/static/img/checked.png"></image>
          </view>
          {{ item.name }}
        </view>
      </view>
    </view>
    <view class="form-box" v-if="params.type == 1 && from == 'private'">
      <view class="label">选择跟进标签</view>
      <view class="type-box flex-row">
        <view v-for="item in cate_list" :key="item.value" class="flex-row flex-wrap">
          <template v-for="child in item.label">
            <view
              class="type-item"
              v-if="child.values != 8"
              :key="child.values"
              :class="{ is_type: child.values == params.effect_type }"
              @click="onChangeEffect(child)"
            >
              <view class="checked">
                <image
                  v-show="child.values == params.effect_type"
                  src="../static/img/checked.png"
                ></image>
              </view>
              {{ child.name }}
            </view>
          </template>
        </view>
      </view>
    </view>
    <view class="form-box" v-if="type == 'shikan'">
      <view class="label tese_label flex-row items-center">
        <view class="tese">
          房源特色
        </view>
        <view class="add" @click ="addTese">
          添加
        </view>
      </view>
      <view class="type-box flex-row">
        <!-- v-show="item.value == params.type" :class="{ is_type: item.value == params.type }"  @click="onChangeType(item)" -->
        <view class="type-item is_type" v-for="(item, index) in house_info.label" :key="index">
          <view class="checked">
            <image src="@/static/img/checked.png"></image>
          </view>
          {{ item }}
        </view>
      </view>
    </view>
    <view class="form-box">
      <view class="label">跟进备注</view>
      <view class="flex-1 text_area">
        <my-input
          v-model="params.content"
          size="small"
          type="textarea"
          height="240rpx"
          border
          placeholder="请输入跟进内容(企业内公开)"
        ></my-input>
      </view>
    </view>
    <template v-if="type == 'shikan'">

      <view class="form-box">
        <view class="label mar-tb24" >图片</view>
        <my-upload
          :chooseType="1"
          :imgs="images"
          :maxCount="10"
          :upInfo="upInfo"
          :action="upload_api"
          @uploadDone="uploadChange"
        ></my-upload>
      </view> 
      <view class="form-box ">
        <view class="label mar-tb24">核心卖点</view>
        <view class="flex-1 text_area">
          <my-input
            v-model="shikan_params.selling_point"
            size="small"
            type="textarea"
            height="240rpx"
            border
            placeholder="请输入核心卖点"
          ></my-input>
        </view>
      </view>

      <view class="form-box">
        <view class="label mar-tb24">小区介绍</view>
        <view class="flex-1 text_area">
          <my-input
            v-model="shikan_params.community_introduction"
            size="small"
            type="textarea"
            height="240rpx"
            border
            placeholder="请输入小区介绍"
          ></my-input>
        </view>
      </view>
      <view class="form-box">
        <view class="label mar-tb24">户型介绍</view>
        <view class="flex-1 text_area ">
          <my-input
            v-model="shikan_params.structure_introduction"
            size="small"
            type="textarea"
            height="240rpx"
            border
            placeholder="请输入户型介绍"
          ></my-input>
        </view>
      </view>
      <view class="form-box">
        <view class="label  mar-tb24">交通出行</view>
        <view class="flex-1 text_area ">
          <my-input
            v-model="shikan_params.transportation"
            size="small"
            type="textarea"
            height="240rpx"
            border
            placeholder="请输入交通出行"
          ></my-input>
        </view>
      </view>
      
      <!-- <view class="select_btn flex-row items-center">
        <view class="add_friend" :class="{ to_pic: imageList.length }" @click="toPic">
          <view class="title"> + 图片 </view>
        </view>
      </view> -->
    </template>

    <!-- <view class="form-box">
      <view class="label mar-t" style="margin-bottom: 24rpx">图片</view>
      <my-upload
        :chooseType="1"
        :imgs="images"
        :maxCount="3"
        :upInfo="upInfo"
        :action="upload_api"
        @uploadDone="uploadChange"
      ></my-upload>
    </view> -->
    <myButton type="primary" :round="false" :loading="loading" class="onbtn" @click="submitFollow"
      >提交跟进</myButton
    >
    <view v-if="isLoading" class="allbgc"> 
      <view class="overlay loader"></view>
     </view>
  </view>
</template>

<script>
import myInput from './components/myInput'
import myButton from './components/myButton'
import myUpload from './components/upload'
export default {
  name: 'follow',
  components: {
    myInput,
    myButton,
    myUpload,
  },
  data () {
    return {
      // maxLength:100,
      isLoading: false,
      buttonLabel: "",
      loading: false,
      params: {
        level: '',
        type: '',
      },
      cate_list: [
        {
          type: 1,
          name: '',
        },
        {
          type: 2,
          name: '',
        },
      ],
      images: [],
      upload_api: '/common/file/upload/admin',
      from: 'custom',
      levelArr: [
        {
          name: 'A级',
          tip: '重点关注',
          id: 'A',
        },
        {
          name: 'B级',
          tip: '日常维护',
          id: 'B',
        },
        {
          name: 'C级',
          tip: '仅作记录',
          id: 'C',
        },
      ],
      last_cate_name: '',
      upInfo: {
        category: 6
      },
      house_info: {},
      type: "detail",
      shikan_params:{
        type:1,
        effect_type:2
      },
      imageList:[]
    }
  },
  onLoad (option) {
    this.from = option.from
    this.type = option.type || "detail"
    this.params.info_id = option.follow_id
    uni.$on("getDataAgain",()=>{
       this.init()
    })
    this.init()
  },
  onUnload(){
    uni.$off("getDataAgain")
  },
  methods: {
    init () {
      if (this.from == 'custom') {
        this.cate_list = [
          {
            value: 1,
            name: '有效客户',
          },
          {
            value: 2,
            name: '无效客户',
          },
          {
            value: 3,
            name: '已成交',
          },
        ]
      } else {
        this.cate_list = [
          {
            type: 1,
            name: '',
          },
          {
            type: 2,
            name: '',
          },
        ]
        if (this.type == "shikan") {
          this.getHouseInfo()
        }
        this.getFollowType()
      }
      this.images = []
    },
    addTese(){
      
      this.$navigateTo("/house/tag_list?id="+this.params.info_id)
      setTimeout(() => {
          uni.$emit("teseArr",{tese: this.house_info.label_id,teseArr :this.house_info.photo_label})
      }, 200);
      
    },
    getHouseInfo () {
      this.$ajax
        .get(`/admin/house/privateHousesDetail/${this.params.info_id}`, {}, (res) => {
          if (res.statusCode === 200) {
            this.house_info = res.data
          }
        }, () => {

        })
    },
    toPic () {

    },
    getFollowType () {
      this.$ajax
        .get('/admin/house/getFollowType', {}, (res) => {
          if (res.statusCode === 200) {
            this.cate_list = res.data
          } else {
            this.cate_list = []
          }
        }, () => {
          this.cate_list = []
        })
    },

    onChangeLevel (e) {
      this.params.level = e.id
    },
    onChangeType (e) {
      this.last_cate_name = this.current_cate_name || ''
      var current_cate = this.cate_list.find((item) => item.value === e.value)
      this.current_cate_name = current_cate.name
      // if (current_cate.value === 1 && !this.params.content) {
      this.params.content = this.params.content
        ? this.params.content.replace(this.last_cate_name, this.current_cate_name)
        : this.current_cate_name
      // }
      this.params.type = e.value
    },
    onChangeEffect (e) {
      this.params.effect_type = e.values
      this.$forceUpdate()
    },
    uploadChange (e) {
      this.images = e
      this.params.images = e.join(',')
    },
    submitFollow () {
      if (!this.params.type && this.type!='shikan') {
        uni.showToast({
          title: '请选择跟进类型',
          icon: 'none',
        })
        return
      }
      if (!this.params.content) {
        uni.showToast({
          title: '请输入跟进备注',
          icon: 'none',
        })
        return
      }
      if (this.params.content && this.params.content.length < 5) {
        uni.showToast({
          title: '跟进备注最少5个字符',
          icon: 'none',
        })
        return
      }
      this.loading = true
      let api = '/v1/wapLm/saveCustomerFollows'
      let params = {}
      if (this.from == 'private') {
        api = '/admin/house/addPrivateHouseFollow'
        params  = Object.assign({},this.params)
      }
      if (this.type == 'shikan') {
        params = Object.assign({},this.shikan_params)
        params.info_id =this.params.info_id 
        params.content =this.params.content 
        params.images = this.params.images
      }
      this.$ajax
        .post(api, params, (res) => {
          this.loading = false
          if (res.statusCode === 200) {
            this.isLoading = true;
      this.buttonLabel = "";
      setTimeout(() => {
        // 在这里执行你的异步操作
        uni.showToast({
              title: '提交成功',
              icon: 'success',
            })
            setTimeout(() => {
              this.$navigateBack()
            }, 500)
        // 操作完成后，隐藏加载器
        this.isLoading = false;
        this.buttonLabel = "点击加载";
      }, 2000); // 这里使用setTimeout模拟异
        
          } else {
            uni.showToast({
              title: '字数超出最大限制' || "添加失败",
              icon: 'none',
            })
          }
        }, () => {
          this.loading = false
        })

    },
  },
}
</script>

<style scoped lang="scss">
// 按钮自定义加载效果

.button-container {
  position: relative;
}

.custom-button {
  background-color: #007bff;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  z-index: 1;
}

.overlay {
  position: fixed;
  top: 50%;
  left: 42%;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.loader {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #007bff;
  border-radius: 50%;
  width: 110rpx;
  height: 110rpx;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.allbgc{
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);

}

.follow {
  width: 100vw;
  padding: 48rpx;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  background-color: #fff;
  padding-bottom: 140rpx;
  .tips {
    color: #fb656a;
    font-size: 22rpx;
    padding: 24rpx 48rpx;
  }
  > .flex-row {
    align-items: center;
    margin-bottom: 24rpx;
    font-size: 24rpx;
  }
  .title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 48rpx;
    padding-left: 12rpx;
    line-height: 1;
    border-left: 6rpx solid #2d84fb;
  }
  .label {
    margin-bottom: 24rpx;
    font-size: 32rpx;
    color: #2e3c4e;
  }
}
.form-box {
  .level-box {
    .level-item {
      background: #f7f7f7;
      border-radius: 8rpx;
      margin-right: 48rpx;
      margin-top: 24rpx;
      margin-bottom: 48rpx;
      padding: 18rpx 22rpx;
      color: #999;
      &.is_level {
        color: #fff;
        background-image: linear-gradient(180deg, #f9762e 0%, #fc0606 100%);
      }
    }
  }
  .tese_label {
    justify-content: space-between;
    .add {
      font-size: 24rpx;
    }
  }
  .text_area {
    border: 2rpx solid #dde1e9;
    border-radius: 8rpx;
    padding: 12rpx;
    ::v-deep .my-input {
      border: 0;
    }
  }
  .type-box {
    flex-wrap: wrap;
    .type-item {
      margin-bottom: 24rpx;
      width: calc((100% - 48rpx) / 3);
      // width: 200rpx;
      height: 80rpx;
      line-height: 80rpx;
      background: #ffffff;
      border-radius: 8rpx;
      margin-right: 24rpx;
      color: #8d9099;
      border: 2rpx solid #dedede;
      // background: #f7f7f7;
      text-align: center;
      position: relative;
      overflow: hidden;
      &:nth-child(3n) {
        margin-right: 0;
      }
      &.is_type {
        border: 2rpx solid rgba(45, 132, 251, 1);
        color: #2d84fb;
        background: #fff;
      }
      .checked {
        position: absolute;
        right: 0;
        top: 0;
        width: 32rpx;
        height: 24rpx;
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .mar-tb {
    margin: 24rpx 0 48rpx;
  }
  .mar-tb24 {
    margin: 24rpx 0;
  }
  .mar-t {
    margin: 24rpx 0 0;
  }
  .mar-b {
    margin-bottom: 24rpx;
    // margin: 24rpx 0 0;
  }
}
.onbtn {
  position: fixed;
  bottom: 50rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 654rpx;
  &.base {
    height: 80rpx;
  }
}
</style>
