<template>
    <view>
      <view class="search_outsaide">
        <view class="search_outsaide_select">
          <view class="whole_input">
            <view class="whole_input_left">
              <view class="whole_input_left_name">{{ text }}</view>
              <view>
                <image src="../static/icon/index/xia.png" style="width:32rpx;height:32rpx;" v-if="searchshowfn"></image>
                <image src="../static/icon/index/jiantousi.png" style="width:36rpx;height:30rpx" v-else></image>
  
              </view>
            </view>
            <view class="whole_shu">|</view>
            <view class="whole_bai">
              <!-- 请输入搜索内容 -->
              <view> <input placeholder="请输入搜索内容" placeholder-style="font-size:28rpx;color:#ACACAC;" type="text"
                  v-model="keywords"></view>
            </view>
          </view>
        </view>
      </view>
   <view>
    <view class="whole">
<view>
    <scroll-view class="weektop" scroll-x="true" @scroll="scroll">
                <view class="weeked" v-for="(tab, index) in navs" :key="tab.id" :id="tab.id"
                    :class="navIndex == index ? 'activite' : ''" @click="checkIndex(index)">{{ tab.name }}</view>
            </scroll-view>
</view>
<view class="approve_whole">
  <view class="approve_header">
    <view  class="approve_header_left">
        <view style="margin-right: 12rpx;">ID:   </view>
        <view>433</view>
    </view>
    <view class="approve_header_right">
        3031-06-25  10:00
    </view>
    </view>
    <view class="approve_wholes">
      <view class="approve_whole_top">
        <view style="color: rgba(41, 44, 57, 0.70);margin-right: 12rpx;">审批编号: </view>
        <view>166666</view>
      </view>
      <view class="approve_whole_bottom">
        <view style="color: rgba(41, 44, 57, 0.70);margin-right: 12rpx;">审批类型: </view>
        <view>删除客户</view>
      </view>
  </view>
  <view class="approve_footers">
<view style="  display: flex; flex-direction: row;">
  <view class="approve_footers_actrve">吴</view>
<view style="  display: flex; flex-direction: row;align-items: center;color: rgba(41, 44, 57, 0.40);">
  <view>由</view>
<view style="margin:0 6rpx">哇哇哇</view>
<view>提交审批</view>
</view>
</view>
<view v-if="appShow" style="padding:8rpx 20rpx; border-radius: 8rpx; background: rgba(72, 138, 246, 0.20);color: #488AF6;
font-size: 24rpx; line-height: 32rpx; /* 133.333% */">待同意</view>
<view v-else style="padding:8rpx 20rpx; border-radius: 8rpx; background: rgba(19, 168, 52, 0.20);
color: #13A834;
font-size: 24rpx; line-height: 32rpx; /* 133.333% */">已同意</view>
</view>
</view>
    </view>
   </view>
   <view class="footer">
    <view class="footer_text">
<view @click="footerFn(item.id)" v-for="item in footerList" :key="item.id" :class="{'footer_text_active':footerIndex== item.id }">{{ item.name }}</view>
    </view>
   </view>
    </view>
  </template>
  <script>
  export default {
    data() {
      return {
        appShow:false,
        footerIndex:1,
        searchshow: false,
        searchshowfn: true,
        navIndex:0,
        value: 0,
        range: [
          { value: 0, text: "篮球" },
          { value: 1, text: "足球" },
          { value: 2, text: "游泳" },
        ],
        footerList:[{id:1,name:'全部'},{id:2,name:"成交审批"},{id:3,name:'变更状态'},{id:4,name:"删除客户"}],
        navs: [
                { id: 1, name: '全部' },
                { id: 2, name: "审批中" },
                { id: 3, name: "已通过" },
                { id: 4, name: "已驳回" },
                { id: 5, name: "已撤销" },
                { id: 6, name: "抄送我" },
                { id: 7, name: "我的申请" },
            ],
        text: '部门/人员',
        keywords: ''
      };
    },
    onLoad() {
    },
    methods: {
        footerFn(id){
this.footerIndex = id
        },
        checkIndex(index) {
            // console.log(index, '000')
            this.navIndex = index;
        },
        scroll: function (e) {
            // console.log(e)
            // this.old.scrollTop = e.detail.scrollTop
        },
    },
  };
  </script>
  <style lang="scss">
  .approve_footers_actrve{
    width: 64rpx;
    height: 64rpx;
    background-color: #488AF6;
    color: #fff;
    border-radius: 50%;
    line-height: 64rpx;
    text-align: center;
    margin-right: 24rpx;
  }
  .approve_footers{
    display: flex;
    flex-direction: row;
    padding-top: 24rpx ; 
    align-items: center;
    justify-content: space-between;
  }
  .approve_header_right{
    color:  rgba(41, 44, 57, 0.40);
font-size: 24rpx;
  }

  .approve_whole_top{
    margin: 24rpx 0;
    display: flex;
    flex-direction: row;
font-size: 32rpx;
  }
  .approve_whole_bottom{
    display: flex;
    flex-direction: row;
font-size: 32rpx;
  }
  .approve_header_left{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    color: rgba(41, 44, 57, 0.40);
font-size: 24rpx;
  }
  .approve_header{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  .approve_whole{
    width: 686rpx;
    background: #fff;
    padding: 32rpx;
    margin-top: 32rpx;
    border-radius: 16rpx;
  }
  .whole{
    padding: 0 32rpx;
}
  .footer_text{
    display: flex;
    flex-direction: row;
    padding-bottom: 108rpx;
    justify-content: space-between;
    color:  rgba(41, 44, 57, 0.70);
    font-size: 32rpx;
  }
  .footer_text_active{
    color: #488AF6;
    font-size: 32rpx;
  }
  page{
    background: #292C3966;
  }
  .footer{
    position: fixed;
    bottom: 0;
    left: 0;
    background: #fff;
    width: 750rpx;
    padding: 17rpx 32rpx 32rpx 32rpx;
  }
  // 本周
.weeked {
    box-sizing: border-box;
    padding: 16rpx 20rpx;
    border-radius: 8rpx;
    margin-right: 16rpx;
    font-size: 24rpx;
    color: rgba(41, 44, 57, 0.40);
    background: #f8f8f8;
    display: inline-block;

    /* 必要，导航栏才能横向*/
    &.weeked:last-child {
        margin-right: 0;
    }
}

.activite {
    box-sizing: border-box;
    padding: 16rpx 20rpx;
    border-radius: 8rpx;
    color: #488AF6;
    font-size: 24rpx;
    background: rgba(72, 138, 246, 0.20);

}
  .weektop {
    // margin-left: 10rpx;
    // width: 25%;
    margin-top: 30rpx;
    white-space: nowrap;
    text-align: center;
}
  ::v-deep .uni-input-placeholder {
    top: -6rpx !important;
  }
  
  ::v-deep .uni-input-input {
    top: -6rpx !important;
  }
  
  .h {
    position: fixed;
    left: 138rpx;
    top: 124rpx;
  }
  
  .search_text {
    width: 100%;
    padding: 20rpx 32rpx;
  }
  
  .search_box {
    width: 180rpx;
    background: #fff;
    padding: 24rpx 0;
    margin-left: 24rpx;
    margin-top: 24rpx;
    border-radius: 16rpx;
    box-shadow: 0px 0px 40rpx 0px rgba(0, 0, 0, 0.10);
  }
  
  .active {
    background: #007aff8c;
    color: #fff;
  }
  
  page {
    background: #F6F6F6;
  }
  
  .search_outsaide_select {
    padding: 24rpx;
  }
  
  .search_outsaide {
    // padding: 64rpx 32rpx 32rpx 32rpx;
    background: #fff;
  }
  
  .whole_shu {
    color: rgba(41, 44, 57, 0.40);
    font-size: 24rpx;
    font-weight: 400;
    line-height: 28rpx;
  margin: 0 16rpx ;
  }
  
  .whole_bai {
    width: 70%;
    height: 32rpx;
    line-height: 32rpx;
    color: rgba(41, 44, 57, 0.40);
  }
  
  .whole_input_left_name {
    color: rgba(41, 44, 57, 0.70);
    font-size: 28rpx;
    font-weight: 400;
    line-height: 32rpx;
    margin-right: 16rpx;
    width: 126rpx;
  }
  
  .whole_input_left {
    display: flex;
    flex-direction: row;
  
  }
  
  .whole_input {
    width: 100%;
    // height: 80rpx;
    padding: 24rpx;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    // margin: 0 16rpx;
    background: #F6F6F6;
    border-radius: 16rpx;
  }</style>