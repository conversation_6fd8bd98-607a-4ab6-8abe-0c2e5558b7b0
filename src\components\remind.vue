<template>
  <view class="remind">
    <view class="title row">
      <text @click="$emit('cancel')">取消</text>
      <view @click = 't_type=1'>添加提醒</view>
      <!-- <text class ='template' v-if ='!temp_disable' @click.prevent.stop ='t_type=2' >模板消息</text> -->
    </view>
    <!-- <view>
      <radio-group @change="radioChange" class="row uni-group c2">
        <label class="row sex-box-row" v-for="item in list" :key="item.value">
          <radio :value="item.value" :checked="item.value === current"></radio>
          <view>{{ item.description }}</view>
        </label>
      </radio-group>
    </view> -->
    <!-- <view class="remark">
      <textarea
        placeholder="请在这里输入"
        maxlength="40"
        v-model="remark"
        @input="getNumber"
      ></textarea>
      <view>{{ reciprocal }}/40</view>
    </view> -->
    <view class="time" v-if = 't_type ==1'>
      <times
        @selectDateEvent="selectDateEvent"
        @selectTimeEvent="selectTimeEvent"
        :timeInterval="1"
        :isMultiple="false"
        :days="30"
        ref="times"
      ></times>
    </view>
    <view class="valuecontent" v-if = 't_type ==1'>
     <view class="tip">提醒内容</view>
     <view><textarea  style="height: 20px;" v-model="contentvalue" @input="inputChange" 
      maxlength='10' placeholder="可选填(10字内)"></textarea></view>
      
    </view>
    <view class="temp" v-if = 't_type ==2'>
      <view class="tabs">
        <tabBar  ref="tab_bar"
          :tabs='temp_tabs'
          :fixedTop="false"
          :nowIndex="
            nowIndex
          "
          @click="onClickCate"></tabBar>
      </view>
      <view class="temp_con">
        <view class="msg_con flex-row items-center">
          <!-- <view class="content1"> -->
            {{ msgList[nowIndex].content1 }}
          <!-- </view> -->
          <view class="inp">
            <input type="text" v-model ="params.name">
          </view>
          <!-- <view class="content1"> -->
            {{ msgList[nowIndex].content2 }}
          <!-- </view> -->
          <view class="inp">
            <input type="text" v-model ="params.mobile">
          </view>
          <!-- <view class="content1"> -->
            {{ msgList[nowIndex].content3 }}
          <!-- </view> -->
          <!-- {{ item.content }} -->
        </view>
        <view class="tip">
          每天只能发送一条模板消息
        </view>
      </view>
    </view>

    <view class="add" v-if='t_type ==1'  @click="onCreate">立即添加</view>
    <view class="add" v-if='t_type ==2'  @click="onCreate1">发送模板消息</view>
  </view>
</template>
<script>
import times from "@/components/pretty-times/pretty-times.vue";
import tabBar from "@/components/tabBar";
export default {
  components: {
    times,
    tabBar
  },
  data() {
    return {
      list: [
        { value: "1", description: "公众号提醒" },
        { value: "2", description: "短信提醒" },
      ],
      current: 1,
      reciprocal: 0,
      remark: "",
      t_type:1,
      nowIndex:0,
      currId:1,
      msgList: [
        {
          id: 1,
          check: false,
          title: "模板一",
          name:"",
          mobile:"",
          content1:'你好，我是',
          content2:'您预约的信息已经收到，在您方便的时候可以随时联系我'
          // content: "实客求租琶洲新村 11栋12栋 100到300方 用途办公，可长租，有意出租请回电158899***** "
        },
        {
          id: 2,
          check: false,
          title: "模板2",
          name:"",
          mobile:"",
          content1:'你好，我是',
          content2:'您预约的信息已经收到，在您方便的时候可以随时联系我',
          content3:"为你发送资料并分析解答。"
        },
        
      ],
      contentvalue:"",//提醒内容
      temp_tabs:
        [
          {
            value:1,
            description:"模板1"
          },
          {
            value:2,
            description:"模板2"
          },
      ],
      params:{
        name:'',
        mobile:""
      }
    };
  },
  props: {
    id: [String, Number],
    temp_disable:{
      type:[Boolean],
      default:false
    }
  },
  created(){
    this.userInfo = uni.getStorageSync('userInfo')?JSON.parse( uni.getStorageSync('userInfo')):{}
    if(this.userInfo){
      this.params.name = this.userInfo.user_name
      this.params.mobile = this.userInfo.phone
    }
  },
  methods: {
    radioChange(e) {
      this.current = e.detail.value;
      // console.log(this.current);
    },
    getNumber() {
      //备注字数
      this.reciprocal = this.remark.length;
    },
    formatDate: function(value) {
      let date = new Date(value); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
      let y = date.getFullYear();
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? "0" + MM : MM;
      let d = date.getDate();
      d = d < 10 ? "0" + d : d;
      let h = date.getHours();
      h = h < 10 ? "0" + h : h;
      let m = date.getMinutes();
      m = m < 10 ? "0" + m : m;
      let s = date.getSeconds();
      s = s < 10 ? "0" + s : s;
      // return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s;//多种时间格式的拼接
      return y + "-" + MM + "-" + d;
    },
    selectDateEvent(e) {
      this.time = this.formatDate(e.timeStamp);
    },
    selectTimeEvent(e) {
      this.time2 = e + ":00";
    },
    onClickCate(e){
      this.nowIndex =e.index 
      this.currId = e.value
    },
    onCreate1(){
      this.params.type = this.currId
      this.$emit("onClick1", this.params);
    },
    //提醒内容不能超过10个字
    inputChange(){
      // console.log(this.contentvalue.length );
      if (this.contentvalue.length > 10||this.contentvalue.length == 10) {
        uni.showToast({
              title: '不能大于10个字符',
              icon: 'none'
        })
      }
    },
    onCreate() {
      let date = new Date();
      //年
      let year = date.getFullYear();
      //月份是从0月开始获取的，所以要+1;
      let month =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      //日
      let day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      //时
      let hour = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
      //分
      let remind_time = "";
      let time = year + "-" + month + "-" + day + " " + hour + ":00:00";

   
      let e = this.$refs.times.getCurrentDate();
      if(e){
        this.time = this.formatDate(e.timeStamp);
      }
      let e2 = this.$refs.times.getCurretTime();
      if(e2){
        this.time2 = e2.time+':00';
      }

      if (!this.time || !this.time2) {
        remind_time = time;
      } else {
        remind_time = this.time + " " + this.time2;
      }
      let form = {
        content: this.remark,
        id: this.id,
        type: this.current,
        remind_time: remind_time,
      };
      if(this.contentvalue){
        form.content = this.contentvalue
      }
      this.$emit("onClick", form);
    },
  },
};
</script>
<style scoped lang="scss">
.c2 {
  color: #8a929f;
}
.add {
  width: 100%;
  color: #fff;
  background: #2d84fb;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 4px;
  margin-top: 50px;
}
.remark {
  position: relative;
  margin: 12px 0;
  background: #f8f8f8;
  textarea {
    border: 1px solid #dde1e9;
    border-radius: 4px;
    width: 100%;
    padding: 10px 12px;
    font-size: 14px;
    height: 80px;
  }
  view {
    position: absolute;
    bottom: 12px;
    right: 12px;
    font-size: 11px;
  }
}

.uni-group {
  display: flex;
  justify-content: space-between;
}
.sex-box-row {
  align-items: center;
  display: flex;
  justify-content: space-between;
}
.remind {
  background: #fff;
  border-top-left-radius: 25px;
  border-top-right-radius: 25px;
  padding: 25px 25px 35px 25px;
}
.title {
  position: relative;
  text-align: center;
  justify-content: center;
  margin-bottom: 25px;
  text {
    position: absolute;
    left: 0;
    top: 3px;
    &.template {
      left: auto;
      right: 0;
    }
  }
  view {
    font-size: 20px;
    font-weight: 500;
  }
}
.valuecontent{
    margin-top: 25px;
    .tip{
      margin-bottom: 10px;
    }
}
.temp_con {


.msg_con {
  margin-top: 24rpx;
  display: block;
  line-height: 1.4em;
  word-break: break-all;
  padding: 24rpx;
  border: 2rpx solid #f1f1f1;
  border-radius: 8rpx;
  /* flex-wrap: wrap; */
  color: #8a929f;
  font-size: 28rpx;
  .inp {
    display: inline-block;
    /* width: 200px; */
    border-bottom: 2rpx solid #8a929f;
    input{
      display: inline-block;
      margin-bottom: -3px;
    }
  }
}
.tip{
  margin-top: 10rpx;
  font-size: 24rpx;
}
}

</style>
