<template>
    <view class="picker-input-box">
        <view class="picker-input-wrapper" @click="openPicker">
            <view class="picker-input-label" v-if="label">{{label}}</view>
            <view class="picker-input-value" :class="{'is-placeholder input-placeholder': !selectedLabels}" :style="{'text-align': align}"><text>{{ selectedLabels || placeholder}}</text></view>
            <icons v-if="withIcon && !disabled" type="jinrujiantou" color="#999" :size="size === 'small' ? 24 : 32"></icons>
       </view>
        <myPopup ref="pickerSelector" :show="show" @close="show = false">
            <view class="picker-selector">
                <view class="picker-selector-header">
                    <view class="picker-selector-action cancle" @click.stop="cancle">取消</view>
                    <view class="picker-selector-title">{{placeholder}}</view>
                    <view class="picker-selector-action confirm" @click.stop="confirm">确定</view>
                </view>
                <picker-view :value="columnSelectedIndexs" @change="onSelectChange">
                    <picker-view-column v-for="(range, index) in rangeList" :key="index">
                        <view class="picker-selector-column-item" v-for="(item, key) in range" :key="key">
                            {{item[map.label]}}
                        </view>
                    </picker-view-column>
                </picker-view>
            </view>
        </myPopup> 
    </view>
</template>
<script>
import icons from '@/components/my-icon';
import myPopup from '@/components/myPopup';
export default {
    name: 'tPicker',
    props: {
        label: { type: String, default: ''},
        value: { type: [String, Number, Array], default: ''},
        datas: {type: Array, default:()=>[]},
        map: { type: Object, default: ()=>{ return {children: 'children', value: 'value', label: 'label'}}},
        placeholder: {type: String, default: '请选择'},
        size: {type: String, default: 'base'},
        align: {type: String, default: 'right'},
        withIcon: {type: Boolean, default: true},
        disabled: {type: Boolean, default: false},
    },
    components: {
        icons, myPopup
    },
    model: {
        prop: 'value',
        event: 'change',
    },
    data(){
        return {
            show: false,
            columnSelectedIndexs: [],
            expectedArrayValue: true,
            rangeList: [],
            ranges: [],
            currentIndexs: [],
            selectedLabels: ''
        }
    },
    computed: {
        checkedValues(){
            const currentIndexs = this.currentIndexs;
            const valueKey = this.map.value;
            const values = [];
            let _parentKey = '';
            for(const [index, value] of currentIndexs.entries()){
                const range = this.rangeList[index]  || (this.ranges[index] || []).filter(e => e._parentKey == _parentKey);;
                if(range && range[value]){
                    values.push(range[value][valueKey] || '');
                }
                _parentKey += (_parentKey ? _parentKey+'-' : '') + index;
            }
            return values;
        }
    },
    watch: {
        value: {
            handler(val){
                if(this._value === val){
                    return;
                }
                this.expectedArrayValue = Array.isArray(val);
                if(!this.ranges.length){
                    return;
                }
                this.initDatas();
            },
            immediate: true
        },
        datas: {
            handler(val){
                this.ranges = this.trees2Ranges(val);
                if(this.ranges.length){
                    this.initDatas();
                }
            },
            immediate: true
        }
    },
    created(){
     
    },
    methods: {
        initDatas(){
            const valueKey = this.map.value;
            let values = [];
            if(!this.expectedArrayValue){
                
                for(let i = this.ranges.length-1; i >=0; i-- ){
                    let range = this.ranges[i];
                    const item = range.find(e => e[valueKey] === this.value);
                    if(item){
                        if(item._parentKey){
                            values = item._parentKey.split('-').map(e=>e*1);
                            range = range.filter(e=>e._parentKey == item._parentKey);
                        }
                        values.push(range.findIndex(e => e[valueKey] === this.value))
                        break;
                    }
                }
                this.columnSelectedIndexs = values;
            }else{
                let _parentKey = '';
                for(const [index, value] of this.value.entries()){
                    const rangeIndex = (this.ranges[index] || []).filter(e=>e._parentKey == _parentKey).findIndex(item => item[valueKey] === value);
                    if(rangeIndex === -1){
                        break;
                    }else{
                        
                        _parentKey = (_parentKey?_parentKey+'-':'')+rangeIndex;

                        values.push(rangeIndex);
                    }
                }
                this.columnSelectedIndexs = values;
            }
            
            if(this.columnSelectedIndexs.length == 0){
                this.columnSelectedIndexs.push(0);
            }else{
                this.currentIndexs = [...this.columnSelectedIndexs];
            }

            
            this.rangeList = this.getNextRanges(0);
            this.setSelectedLabels();
        },
        cancle(){
            this.show = false
        },
        confirm(){
            this.currentIndexs = this.columnSelectedIndexs;
            this.setSelectedLabels();

          
            const values = [...this.checkedValues];
            this._value = this.expectedArrayValue ? values : values.pop() ?? '';
            this.$emit('input', this._value);
            this.$emit('change', this._value);

            this.show = false
        },
        openPicker(){
            if(this.disabled){
                return;
            }
            this.show = true
        },
        setSelectedLabels(){
            const labels = [];
            const labelKey = this.map.label;
            let _parentKey = '';
            for(const [index, value] of this.currentIndexs.entries()){
                const range = this.rangeList[index] || (this.ranges[index] || []).filter(e => e._parentKey == _parentKey);
                const item = range ? range[value] : null;
                labels.push(item ? item[labelKey] ?? '' : '' );
                _parentKey += (_parentKey ? _parentKey+'-' : '') + index;
            }
            this.selectedLabels = labels.join('/');
        },
        onSelectChange(e){
            const indexs = this.columnSelectedIndexs;
            const nowIndexs = e.detail.value;

            let changeIndex = 0;
            if(nowIndexs.length > 1){
                for(const [index, value] of nowIndexs.entries()){
                    if(value != indexs[index]){
                        changeIndex = index;
                        break;
                    }
                }
            }
            this.columnSelectedIndexs = nowIndexs.slice(0, changeIndex+1);
            
            this.rangeList.splice(changeIndex+1, this.rangeList.length, ...this.getNextRanges(changeIndex+1));
        },
        getNextRanges(fromIndex){
            const nextRanges = [];
            const ranges = this.ranges.slice(fromIndex);
            let parentKey = this.columnSelectedIndexs.slice(0, fromIndex).join('-');

            for(const range of ranges){
                const nextRange = range.filter(e => e._parentKey == parentKey );
                if(this.$Utils.isNotEmptyArray(nextRange)){
                    nextRanges.push(nextRange);
                    let nextIndex = this.columnSelectedIndexs[fromIndex++];
                    if(nextIndex === undefined){
                        nextIndex = 0;
                        this.columnSelectedIndexs.push(nextIndex);
                    }
                    parentKey += (parentKey ? '-' : '') + nextIndex;
                }else{
                    break;
                }
            }
            return nextRanges;
        },
        //tree数组转换为range所需数据
        trees2Ranges(list){
            const ranges = [], child = this.map.children || 'children';
            const _trans = function(cols, parentIndex, parentKey, columnIndex = 0){
                if(!ranges[columnIndex]){
                    ranges[columnIndex] = [];
                }
                for(const [index, item] of cols.entries()){
                    item._parentIndex = parentIndex
                    item._parentKey = parentKey
                    ranges[columnIndex].push(item);
                    if(Array.isArray(item[child]) && item[child].length){
                        _trans(item[child], index, (parentKey ? parentKey+'-' : '')+index, columnIndex+1);
                    }
                }
            }
            if(this.$Utils.isNotEmptyArray(list)){
                _trans(list, -1, '');
            }
            return ranges;	
            
        },

        getCheckedValues(){
            return this.checkedValues;
        }
    }
}
</script>
<style lang="scss" scoped>
.picker-input-box{
    flex:1
}
.picker-input-wrapper{
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    .picker-input-value{
        flex: 1;
        line-height: 1;
        padding-right: 5px;
        &.is-placeholder{
            color: #999;
        }
    }
}
.picker-selector{
    background-color: #fff;
    line-height: 1;
    
    .picker-selector-header{
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 46px;
        :after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            height: 1px;
            clear: both;
            border-bottom: 1px solid #e5e5e5;
            color: #e5e5e5;
            -webkit-transform-origin: 0 100%;
            transform-origin: 0 100%;
            -webkit-transform: scaleY(.5);
            transform: scaleY(.5);
        }
        .picker-selector-title{
            flex: 1;
            color: #999;
            text-align: center;
            display: inline-block;
            max-width: 50%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .picker-selector-action{
            padding: 0 14px;
            font-size: 17px;
            &.cancle{
                color: #888;
            }
            &.confirm{
                color: #007aff;
            }
        }
        
    }
}
::v-deep uni-picker-view, picker-view{
    height: 36vh;
    .picker-selector-column-item{
        display: flex;
        justify-content: center;
        padding: 0 14px;
        overflow: hidden;
        white-space: nowrap;
        text-align: center;
    }
}
</style>