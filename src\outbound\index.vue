<template>
  <view class="page">
    <view class="tab-box">
      <tabs
        :options="tab_list"
        :format="{ name: 'name', value: 'type' }"
        equispaced
        :showAnimation="false"
        v-model="params.status"
        @change="onTabChange"
      ></tabs>
    </view>
    <view class="list">
      <view class="list_item" v-for="item in packageList" :key="item.id">
        <packageItem :item="item" @toDetail="toDetail"></packageItem>
      </view>
      <loadMore :status="load_status" @reload="getData()" />
    </view>
  </view>
</template>

<script>
import Tabs from './components/Tabs'
import packageItem from './components/packageItem'
import loadMore from '@/components/loadMore'
export default {
  components: { Tabs, packageItem, loadMore },

  data () {
    return {
      // 0:未进行，1：进行中，2：完成
      tab_list: [
        {
          type: '',
          name: '全部',
          index: 0,
        },
        {
          name: '未进行',
          type: '0',
          index: 1,
        },
        {
          name: '进行中',
          type: '1',
          index: 2,
        },
        {
          name: '已完成',
          type: '2',
          index: 3,
        }

      ],
      params: {
        page: 1,
        rows: 20,
        status: '',
      },
      packageList: [],
      load_status: "loading"


    }
  },
  onLoad () {
    this.getData()
  },
  methods: {
    getData () {

      if (this.params.page == 1) {
        this.packageList = []
      }
      this.load_status = "loading"
      this.$ajax.get('/admin/call_clue/myCallTaskPackage', this.params, res => {

        if (res.statusCode == 200) {
          this.packageList = this.packageList.concat(res.data.data)
          if (res.data.data.length < this.params.rows) {
            this.load_status = 'nomore'
          } else {
            this.load_status = 'loadend'
          }
        } else {
          this.load_status = 'nomore'
        }
      }, () => {
        this.load_status = 'nomore'
      })
    },
    toDetail (e) {
      this.$navigateTo(`/outbound/taskList?id=${e.id}&status=${e.status}`)
    },
    onTabChange () {
      this.params.page = 1
      this.getData()
    }
  },
  onReachBottom () {
    if (this.load_status === 'loadend') {
      this.params.page++
      this.getData()
    }
  },
}
</script>

<style lang="scss" scoped>
.sticky {
  // padding: 0 48rpx;
  // #ifdef H5
  top: 0;
  // #ifdef H5-WEIXIN
  top: 0;
  // #endif
  // #endif
  // #ifndef H5
  top: 0;
  // #endif
  position: sticky;
  z-index: 9;
  background-color: #fff;
}
.page {
  background: #f6f6f6;
  min-height: 100vh;
}
.tab-box {
  width: 100%;
  padding: 0 24rpx;
  z-index: 9;
  background: #fff;
}
.list {
  margin: 24rpx;
}
</style>