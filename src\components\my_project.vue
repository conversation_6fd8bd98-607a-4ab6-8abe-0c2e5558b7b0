<template>
  <view
    class="list row"
    hover-class="item-hover"
    :hover-start-time="60"
    :hover-stay-time="120"
    @click="onClick()"
  >
    <view class="left">
      <image
        mode="aspectFill"
        :src="img ? img : 'https://img.tfcs.cn/static/img/que.jpg'"
      ></image>
      <myIcon class="icon" type="ic_vr" color="#fff"></myIcon>
      <view class="right-mark" v-if="build_type">
        {{ build_type }}
      </view>
    </view>
    <view class="right">
      <view class="title-top row">
        <view class="title">{{ build_name }}</view>
      </view>
      <view class="price-box row">
        <view v-if="price" class="price-left row">
          <text class="price">{{ price }}</text>
          <text class="unit">元/㎡</text>
        </view>
        <view v-else class="price-left">价格待定</view>
      </view>
      <view class="area-box">{{ region_0_name }} {{ region_1_name }}</view>
      <view class="build-type row">
        <text v-for="(type, index) in labels" :key="index">{{ type }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import myIcon from "./my-icon";
export default {
  components: {
    myIcon,
  },
  props: {
    img: {
      type: String,
    },
    build_type: String,
    build_name: String,
    region_0_name: String,
    region_1_name: String,
    labels: [Array, String],
    price: [String, Number],
  },
  methods: {
    onClick() {
      this.$emit("click");
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  margin-bottom: 12rpx;
  .left {
    width: 204rpx;
    height: 172rpx;
    position: relative;
    image {
      width: 100%;
      height: 100%;
    }
    .right-mark {
      align-items: center;
      font-size: 22rpx;
      color: #ffffff;
      line-height: 32rpx;
      width: 64rpx;
      height: 32rpx;
      left: 0;
      position: absolute;
      background-image: linear-gradient(180deg, #69d4bb 0%, #00caa7 100%);
    }
    .right-mark-l {
      background-image: linear-gradient(132deg, #f7918f 0%, #fb656a 100%);
    }
    .icon {
      position: absolute;
      bottom: 16rpx;
      left: 16rpx;
      width: 40rpx;
      height: 40rpx;
      border: 1rpx solid #fff;
      border-radius: 50rpx;
    }
  }
  .right {
    width: 484rpx;
    margin-left: 16rpx;
    justify-content: space-between;
    .title-top {
      justify-content: space-between;
      .title {
        font-size: 32rpx;
        color: #1e1f20;
        font-weight: bold;
      }
    }
    .area-box {
      font-size: 28rpx;
      color: #999999;
    }
    .build-type {
      flex-wrap: wrap;
      font-size: 28rpx;
      color: #999;
      text {
        margin: 4rpx 20rpx 4rpx 4rpx;
        background: #f2f2f2;
      }
    }
    .price-box {
      justify-content: space-between;
      .price-left {
        align-items: flex-end;
        color: #fb656a;
        font-size: 28rpx;
        .unit {
          font-size: 28rpx;
          color: #333333;
          margin-left: 10rpx;
        }
      }
    }
  }
}
</style>
