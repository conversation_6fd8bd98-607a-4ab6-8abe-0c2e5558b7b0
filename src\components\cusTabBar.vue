<template>
  <view
    class="nav-box"
    :class="{ fixed_top: fixedTop, small: small, theme2: theme == 2 }"
    :style="top === true ? '' : 'top:' + top"
  >
    <scroll-view
      scroll-x
      scroll-with-animation
      :scroll-into-view="'i' + (nowIndex > 0 ? nowIndex - 1 : nowIndex)"
      style="width: 100%"
      class="nav-list"
      :class="
        equispaced
          ? tabs.length <= maxNum
            ? 'equispaced col' + tabs.length
            : ''
          : 'col' + maxNum
      "
    >
      <template v-if="custom">
        <slot />
      </template>
      <template v-else>
        <view
          class="nav-item "
          :class="{ active: nowIndex == index }"
          :id="'i' + index"
          v-for="(item, index) in tabs"
          :key="item.index"
          @click="handelBar(index)"
          >{{ item.title }}
        </view>
      </template>
    </scroll-view>
  </view>
</template>

<script>
export default {
  props: {
    tabs: Array,
    showSearch: {
      type: Boolean,
      default: false,
    },
    col: {
      type: [String, Number],
      default: "",
    },
    fixedTop: {
      type: Boolean,
      default: true,
    },
    top: {
      type: [Boolean, String, Number],
      default: false,
    },
    nowIndex: {
      type: [Number, String],
      default: 0,
    },
    maxNum: {
      // 一屏最大显示数量
      type: [Number],
      default: 4,
    },
    equispaced: {
      // maxNum小于4时是否平均分布
      type: [Boolean],
      default: true,
    },
    small: {
      // 是否时缩小版的
      type: [Boolean],
      default: false,
    },
    theme: {
      type: [Number],
      default: 1,
    },
    custom: Boolean,
  },
  data() {
    return {};
  },
  methods: {
    handelBar(index) {
      this.$emit("click", Object.assign({ index: index }, this.tabs[index]));
    },
  },
};
</script>

<style lang="scss">
.nav-box {
  width: 100%;
  background-color: #fff;
  // border-bottom: 1upx solid $uni-border-color;
  // box-shadow: 0 4upx 10upx #e6e6e6;
}
.fixed_top {
  position: fixed;
  /* #ifdef H5 */
  top: 44px;
  /* #endif */
  /* #ifndef H5 */
  top: var(--window-top);
  /* #endif */
  z-index: 9;
}
.nav-box .search-title {
  width: 100%;
  padding: 15upx;
  box-sizing: border-box;
}
.nav-box .search-box input {
  height: 60upx;
  padding: 6upx 10upx;
  box-sizing: border-box;
  font-size: $uni-font-size-sm;
  border-radius: 6upx;
  border: 1upx solid $uni-border-color;
}
.nav-list {
  // height: 80upx;
  // line-height: 76upx;
  height: 40px;
  line-height: 40px;
  white-space: nowrap;
}
.small .nav-list {
  height: 60upx;
  line-height: 56upx;
  white-space: nowrap;
}
.nav-item {
  display: inline-block;
  text-align: center;
  padding: 8px 12px;
  margin: 0 3% 0 0;
  // width: 20%;
  // height: 50rpx;
  line-height: 8px;
  // overflow: hidden;
  // text-overflow: ellipsis;
  // white-space: nowrap;
  color: #8a929f;
  border-radius: 24px;
  background-color: #f6f6f6;
}
.equispaced.col4 .nav-item {
  width: 23%;
}
.equispaced.col3 .nav-item {
  width: 31.333%;
}
.equispaced.col2 .nav-item {
  width: 48%;
}
.col4 .nav-item {
  width: 20%;
}
.col3 .nav-item {
  width: 27.333%;
}
.col2 .nav-item {
  width: 40%;
}
.nav-item.active {
  color: #2d84fb;
  position: relative;
  background-color: #eaf3ff;
  font-weight: 500;
  // border-bottom: 4upx solid $uni-color-primary;
}
// .nav-item.active:before {
//   position: absolute;
//   content: "";
//   height: 8upx;
//   max-width: 18vw;
//   left: 0;
//   right: 0;
//   margin: auto;
//   bottom: 0;
//   background-color: $uni-color-primary;
//   border-radius: 4rpx;
//   width: 48rpx;
// }
.theme2 {
  padding: 15rpx 0;
  .nav-item {
    border: 1rpx solid $uni-color-primary;
    color: $uni-color-primary;
    border-radius: 3px;
    &.active {
      background-color: $uni-color-primary;
      color: #fff;
      position: relative;
    }
  }
}
</style>
