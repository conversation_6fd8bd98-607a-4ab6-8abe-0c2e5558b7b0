<template>
  <view class="voice-container">
    <view class="voiced" :class="{ show: show_voice }">
      <image
        class="voice_img"
        mode="widthFix"
        :src="
          (show_voice ? '/static/icon/voice/recorde.gif' : '/static/icon/none.png')
            | imageFilter('w_80')
        "
      ></image>
      <view class="tip">手指上滑取消录音</view>
    </view>
    <view class="voiced" :class="{ show: show_cancel_voice }">
      <image
        class="voice_img"
        mode="widthFix"
        :src="'/static/icon/voice/quxiao.png' | imageFilter('w_80')"
      ></image>
      <view class="tip">松开手指取消录音</view>
    </view>
    <view
      class="btn_box"
      :class="{ active: show_voice }"
      @longtap.navite="onLongTapVoiceBtn"
      @touchmove="onTouchMoveVoicBtn"
      @touchend="onTouchEndVoiceBtn"
    >
      <slot></slot>
    </view>
  </view>
</template>

<script>
const recorderManager = uni.getRecorderManager();
// #ifdef H5
import Recorder from 'js-audio-recorder';
// 判断是否是苹果手机
const isIos = function () {
  const u = navigator.userAgent;
  return u.indexOf("iPhone") > -1 || u.indexOf("Mac OS") > -1;
}
let recorder = new Recorder(
  {
    sampleBits: 16, // 采样位数，支持 8 或 16，默认是16
    sampleRate: 16000, // 采样率，支持 11025、16000、22050、24000、44100、48000，根据浏览器默认值，我的chrome是48000
    numChannels: 1, // 声道，支持 1 或 2， 默认是1
  }
);
// #endif
export default {
  components: {},
  data () {
    return {
      show_voice: false,
      show_cancel_voice: false,
      open_voice: true, //是否开启语音功能
      allow_send_voice: true //是否允许用户使用语音
    }
  },
  props: {
    recorder_max_time: { //最大录音时间
      type: Number,
      default: 30000
    },
    hasVioce: {
      type: Boolean,
      default: false
    },
    use_wx_voice: {
      type: Boolean,
      default: false //是否使用微信jssdk的录音功能
    }
  },
  created () {
    // #ifdef H5
    var ua = window.navigator.userAgent.toLowerCase();
    // this.use_wx_voice = true
    console.log(this.use_wx_voice, ua.match(/MicroMessenger/i) == 'micromessenger');
    if (ua.match(/microMessenger/i) && ua.match(/microMessenger/i).length && ua.match(/microMessenger/i)[0] == 'micromessenger' && this.use_wx_voice) {
      this.getWxConfig(['startRecord', 'stopRecord', 'onVoiceRecordEnd', 'playVoice', 'pauseVoice', 'stopVoice', 'onVoicePlayEnd', 'uploadVoice', 'downloadVoice'], (wx) => {
        console.log('初始化wxjssdk成功', wx)
        this.wx = wx
      })
    }
    // #endif

  },
  methods: {
    // #ifdef H5
    checkRecorderPermission (cb) {
      if (this.use_wx_voice) {
        this.wx.startRecord({
          success: () => {
            cb && cb()
            this.wx.stopRecord()
            console.log("已获取权限")
          }
        })
      } else {
        Recorder.getPermission().then(
          () => {
            cb && cb()
            console.log("已获取权限")
          },
          error => {
            uni.showToast({
              title: '没有录音权限',
              icon: 'none'
            })
            console.log(`${error.name} : ${error.message}`)
          }
        )
      }
      // 苹果手机检测权限会有问题
      if (isIos()) {
        cb && cb()
        return
      }
      // Recorder.getPermission().then(
      //   () => {
      //     cb && cb()
      //     console.log("已获取权限")
      //   },
      //   error => {
      //     uni.showToast({
      //       title: '没有录音权限',
      //       icon: 'none'
      //     })
      //     console.log(`${error.name} : ${error.message}`)
      //   }
      // )
    },
    // #endif
    /**
     * @date 2020-09-17 14:50:41
     * @desc 长按发送语音按钮
     */
    onLongTapVoiceBtn (e) {console.log(this.recordering);
      // 如果是正在录音则不进行任何操作
      if (this.recordering) {
        return
      }
      if (!this.open_voice) {
        uni.showToast({
          title: '语音功能暂未开放',
          icon: 'none'
        })
        return
      }
      if (!this.allow_send_voice) {
        uni.showToast({
          title: this.prohibit_send_voice_tip || '您已被禁止发送语音',
          icon: 'none'
        })
        return
      }
      this.touch_voice_start_position_y = e.changedTouches[0].clientY
      // 执行开始录音
      // #ifdef H5
      if (this.use_wx_voice) {
        this.wx.startRecord({
          success: function (res) {
            console.log("开始录音")
            console.log(res)
          }
        })
        this.onRecordering()
        this.voice_start_time = Date.parse(new Date())
        this.onRecorderStop()
      } else {
        this.checkRecorderPermission(() => {
          recorder.start()
          this.onRecordering()
        })
      }
      // this.checkRecorderPermission(() => {
      //   recorder.start()
      //   this.onRecordering()
      // })
      // #endif
      // #ifndef H5
      
      uni.authorize({
          scope: 'scope.record',
          success: ()=> {

            recorderManager.onError(er=>{
              uni.showToast({
                title: er.errMsg || '录音出错',
                icon: 'none'
              })
              console.log(er);
            })
            recorderManager.onStart(() => {
              this.onRecordering()
              this.onRecorderStop()
            })
            recorderManager.start({
              duration: this.recorder_max_time
            })    
          },
          fail: (er)=>{
            uni.showToast({
              title: '未获取到录音权限',
              icon: 'none'
            })
            console.log(er);
          }
      })


      
      // #endif
    },
    /**
     * @desc 滑动发送语音按钮
     */
    onTouchMoveVoicBtn (e) {
      if (!this.open_voice || !this.touch_voice_start_position_y || !this.recordering) {
        return
      }
      // 判断垂直滑动距离达到某个值则提示松开取消发送录音
      if (this.touch_voice_start_position_y && this.touch_voice_start_position_y - e.changedTouches[0].clientY >= 70) {
        this.show_cancel_voice = true
        this.show_voice = false
      } else {
        this.show_cancel_voice = false
        this.show_voice = true
      }

    },
    /**
     * @desc 松开发送语音按钮
     */
    onTouchEndVoiceBtn (e) {
      console.log(e, 'onTouchEndVoiceBtn', !this.open_voice || !this.touch_voice_start_position_y || !this.recordering);
      if (!this.open_voice || !this.touch_voice_start_position_y || !this.recordering) {
        return
      }
      // 记录结束触摸的位置
      this.touch_voic_end_position_y = e.changedTouches[0].clientY
      // 执行停止录音
      this.stopRecorder()
    },
    /**
     * @date 2020-09-17 14:50:35
     * @desc 录音倒计时
     */
    recorderCountDown () {
      if (this.recorder_timer) clearInterval(this.recorder_timer)
      let max_time = this.recorder_max_time / 1000
      this.recorder_timer = setInterval(() => {
        max_time--
        if (max_time >= 1 && max_time <= 3) {
          uni.showToast({
            title: `${max_time}秒后停止录音`,
            icon: 'none'
          })
        }
        if (max_time <= 0) {
          this.onTouchEndVoiceBtn()
          this.stopRecorder()
        }
      }, 1000)
    },
    /**
     * @date 2020-09-17 14:50:27
     * @desc 录音中的状态
     */
    onRecordering () {
      this.recordering = true
      this.recorderCountDown()
      this.show_voice = true
      this.voice_index = 0
      this.timer = setInterval(() => {
        this.voice_index++
        if (this.voice_index > 5) {
          this.voice_index = 0
        }
      }, 260)
      this.$emit('recordering')
    },
    /**
     * @date 2020-09-17 14:50:13
     * @desc 录音结束后的状态
     */
    onRecordered () {
      this.recordering = false
      if (this.recorder_timer) clearInterval(this.recorder_timer)
      this.show_voice = false
      this.show_cancel_voice = false
      if (this.timer) clearInterval(this.timer)
      this.$emit('recordered')
    },
    /**
     * @desc 执行停止录音
     */
    stopRecorder () {
      if (this.recorder_timer) clearInterval(this.recorder_timer)
      // #ifndef H5
    console.log(333333);
      recorderManager.stop && recorderManager.stop()
      // #endif
      // #ifdef H5
      if (this.use_wx_voice) {
        this.wx.stopRecord({
          success: (res) => {
            this.onRecordered()
            this.voice_end_time = Date.parse(new Date())
            // 如果大于等于70则是取消录音,否则获取录音文件
            if (this.touch_voice_start_position_y - this.touch_voic_end_position_y < 70) {
              this.$emit('recorded', {
                file: null,
                localId: res.localId,
                wx: this.wx,
                duration: this.voice_end_time - this.voice_start_time
              })
            }
            this.touch_voice_start_position_y = null
          }
        });
      } else {
        recorder.stop()
        this.onRecordered()
        if (this.touch_voice_start_position_y - this.touch_voic_end_position_y < 70) {
          let blob = recorder.getWAVBlob()
          let duration = Math.ceil(recorder.duration * 1000)
          recorder.destroy()
          var reader = new FileReader();
          reader.readAsDataURL(blob);
          reader.onloadend = () => {
            var base64data = reader.result;
            this.$emit('recorded', {
              file: base64data,
              duration
            })
          }
        } else {
          recorder.destroy()
        }
        this.touch_voice_start_position_y = null
      }
      // recorder.stop()
      // this.onRecordered()
      // if (this.touch_voice_start_position_y - this.touch_voic_end_position_y < 70) {
      //   let blob = recorder.getWAVBlob()
      //   let duration = Math.ceil(recorder.duration * 1000)
      //   recorder.destroy()
      //   var reader = new FileReader()
      //   reader.readAsDataURL(blob)
      //   reader.onloadend = () => {
      //     var base64data = reader.result
      //     this.$emit('recorded', {
      //       file: base64data,
      //       duration
      //     })
      //   }
      // } else {
      //   recorder.destroy()
      // }
      // this.touch_voice_start_position_y = null
      // #endif
    },
    // 监听到录音结束事件
    onRecorderStop () {
      recorderManager.onStop(e => {console.log(e,'已经停止录音');
        // 如果是自动停止的
        if (this.recorder_timer) {
          uni.showToast({
            title: '录音已自动终止',
            icon: 'none'
          })
        }
        this.onRecordered()
        console.log('已经停止录音')
        // 如果大于100则是取消录音,否则获取录音文件
        if (this.touch_voice_start_position_y - this.touch_voic_end_position_y < 70) {
          this.$emit('recorded', {
            file: e.tempFilePath,
            duration: e.duration
          })
        }
        this.touch_voice_start_position_y = null
        this.$emit('recorderStop')
      })
    }
  }
}
</script>

<style scoped lang="scss">
// 录音按钮动画
// @keyframes rotate {
//   0% {
//     background-image: radial-gradient(circle, rgb(255, 101, 107) 5%, rgb(247, 79, 85) 15%);
//     transform: scale(0.6);
//     opacity: .05;
//   }
//   25% {
//     background-image: radial-gradient(circle, rgb(255, 101, 107) 5%, rgb(247, 79, 85) 15%);
//     transform: scale(1.2);
//     opacity: .15;
//   }
//   50% {
//     background-image: radial-gradient(circle, rgb(255, 101, 107) 5%, rgb(247, 79, 85) 15%);
//     transform: scale(1.8);
//     opacity: .25;
//   }
//   75% {
//     background-image: radial-gradient(circle, rgb(255, 101, 107) 5%, rgb(247, 79, 85) 15%);
//     transform: scale(2.4);
//     opacity: .36;
//   }
//   100% {
//     background-image: radial-gradient(circle, rgb(255, 101, 107) 5%, rgb(247, 79, 85) 7%);
//     transform: scale(3.2);
//     transition: 0.3s;
//     opacity: .5;
//   }
// }

.voice-container {
  position: relative;
  // .btn_box{
  //   position: relative;
  //   &.active::after {
  //     animation: rotate 1.6s linear infinite;
  //   }
  //   &.active::before {
  //     animation: rotate 1.6s linear 0.8s infinite;
  //   }
  // }
  // .btn_box::after{
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   right: 0;
  //   bottom: 0;
  //   border-radius: 50%;
  //   transform: scale(0);
  //   opacity: 0;
  // }
  // .btn_box::before{
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   right: 0;
  //   bottom: 0;
  //   border-radius: 50%;
  //   transform: scale(0);
  //   opacity: 0;
  // }
}
// 录音中
.voiced {
  width: 240rpx;
  padding: 24rpx;
  text-align: center;
  box-sizing: border-box;
  height: 240rpx;
  position: absolute;
  z-index: -1;
  left: 0;
  right: 0;
  bottom: 444rpx;
  margin: auto;
  border-radius: 20rpx;
  background-color: rgba($color: #000000, $alpha: 0.5);
  opacity: 0;
  transition: 0.26s;
  &.show {
    opacity: 1;
    z-index: 10;
  }
  .tip {
    height: 30rpx;
    margin-top: 20rpx;
    line-height: 30rpx;
    text-align: center;
    font-size: 22rpx;
    color: #fff;
  }
  .voice_img {
    width: 140rpx;
  }
}
</style>
