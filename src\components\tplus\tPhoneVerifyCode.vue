<template>
    <view class="phone-code-wrapper">
        <view class="phone-code-label" v-if="label">{{label}}</view>
        <view class="picker-code-value">
            <input type="number" placeholder="请输入验证码" v-model.trim="inputValue" @input="updateModelValue">
        </view>
        <view class="picker-code-btn" :class="{'is-sending': isSending}" @click="getVerifyCode">{{sendingLabel}}</view>
    </view>
</template>
<script>
export default {
    name: 'tPhoneVerifyCode',
    props: {
        label: { type: String, default: ''},
        value: { type: [String, Number], default: ''},
        times: { type: Number, default: 120},
        phoneNumber: { type: [String, Number], default: ''},
        phoneName: { type: String, default: 'phone'},
        api: { type: String, default: ''}
    },
    data(){
        return {
            inputValue: '',
            seconds: 0,
            sending: false
        }
    },
    computed: {
        isSending(){
            return this.sending || this.seconds > 0;
        },
        sendingLabel(){
            return this.seconds ? this.seconds + "s后获取" : "获取验证码"
        }
    },
    watch: {
        value: {
            handler(val){
                this.inputValue = val
            }
        }
    },
    created(){
      
    },
    methods: {
        async getVerifyCode(){
            if(this.isSending){
                return;
            }
            if(!this.checkPhone(this.phoneNumber)){
                return;
            }
            
            this.sending = true;
            const res = await this.getCode();
            this.sending = false;
            if(res){
                this.setCountDown();
            }
            
        },
        async setCountDown(){
            this.seconds = this.times;
            while(this.seconds > 0){
                await this.$Utils.sleep(1000);
                this.seconds--;
            }
        },
        updateModelValue(){
            this.$emit('input', this.inputValue);
        },
        // 判断手机号码输入
        checkPhone(tel) {
            if (!tel) {
                uni.showToast({
                title: "请输入手机号码",
                icon: "none",
                });
                return false;
            }
            if (!/^1[3456789]\d{9}$/.test(tel)) {
                uni.showToast({
                    title: "手机号码格式错误",
                    icon: "none",
                });
                return false;
            }
            return true;
        },
        getCode() {
            return new Promise(resolve => {
                try{
                    this.$ajax.post(this.api, { [this.phoneName]: this.phoneNumber}, (res) => {
                        if (res.statusCode === 200) {
                            uni.showToast({
                                title: "发送成功",
                                icon: "none",
                            });
                            resolve(true);
                        } else {
                            uni.showToast({
                                title: res.data.message || "网络错误",
                                icon: "none",
                            });
                            resolve(false);
                        }
                    })
                }catch(e){
                    resolve(false);
                }
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.phone-code-wrapper{
    display: flex;
    flex-direction: row;
    align-items: center;
    .picker-code-value{
        flex: 1;
        text-align: right;
        padding: 0 8px;
        
    }
    .picker-code-btn{
        color:#3e8afd;
        border-left: 1px solid #79a4e4;
        font-size: 12px;
        display: flex;
        align-items: center;
        padding: 2px 0 2px 6px;

        &.is-sending{
            color:rgba(0,0,0,.35);
            border-color: rgba(0,0,0,.3);
        }
    }
}
</style>