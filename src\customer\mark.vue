<template>
  <view>
    <view style="height: 100vh">
      <view class="top">
        <view class="tit">客户信息</view>
        <view class="pers row">
          <image
            class="pic"
            :src="`https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/${
              user_detail.sex == 1 ? 'nan2' : 'nv2'
            }.png`"
            mode="aspectFill"
          />
          <text class="name">{{ user_detail.cname }}</text>
          <text class="level" v-if="user_detail.level">{{ user_detail.level.title }}</text>
          <image
            class="qw"
            src="https://tfybaobei.oss-cn-shanghai.aliyuncs.com/static/admin/customer/qywx.png"
            mode="widthFix"
          />
        </view>
        <view class="type c2">
          <text v-if="user_detail.create_user"
            >负责人：{{ user_detail.create_user.user_name }}</text
          >
          <text v-if="user_detail.tracking">客源状态：{{ user_detail.tracking.title }}</text>
          <!-- <text>下次跟进时间：2022/09/03 18:00(14天未跟进)</text> -->
        </view>
        <view class="type row c2">
          <text v-if="user_detail.client_type">客户类型：{{ user_detail.client_type.title }}</text>
          <text
            >客户意向：<text style="flex: 1">{{ user_detail.intention_community }}</text></text
          >
        </view>
        <view class="content">
          <text class="c2"><text>备注：</text>{{ user_detail.remark }}</text>
        </view>
        <view class="content" v-if="user_detail.last_follow_list">
          <text><text class="c2">最新跟进：</text>{{ user_detail.last_follow_list.content }}</text>
        </view>
        <view class="type c2" v-if="false"><text>标签</text></view>
        <view class="tag-box" style="margin-top: 12px" v-if="false">
          <block v-for="(item, index) in labels_list" :key="index">
            <view v-show="isOpen || index < max" class="l-title">{{ item.name }}</view>
            <view class="tag_input row" v-show="isOpen || index < max">
              <!-- <input
                type="text"
                class="input c2"
                maxlength="50"
                placeholder="请在这里输入"
              /> -->
              <block v-for="(item1, index1) in item.subset" :key="index1">
                <view
                  class="tag_item"
                  @click="onClickTags(item1.tagid)"
                  :class="{ checked: choose.includes(item1.tagid) }"
                  :key="item1.id"
                >
                  {{ item1.name }}
                </view>
              </block>
            </view>
          </block>
          <view
            class="loadmore"
            v-show="!isOpen && labels_list.length > max"
            @click="isOpen = !isOpen"
            >更多标签</view
          >
          <view
            class="loadmore"
            v-show="isOpen && labels_list.length > max"
            @click="isOpen = !isOpen"
            >收起</view
          >
        </view>
        <view class="btn-box row" v-if="false">
          <view class="btn" @click="onCreateLabels">确认</view>
        </view>
        <view class="tag row c2" v-if="user_detail.label_name && user_detail.label_name.length > 0">
          <block v-for="(item, index) in user_detail.label_name" :key="index">
            <text v-if="item.tag">{{ item.tag.name }}</text>
          </block>
        </view>
      </view>
      <follow
        from="mark"
        :udetail="user_detail"
        :f_page="follow_params.page"
        :follow_list="follow_list"
        :mark_tab="mark_tab"
        :logs_list="logs_list"
        :l_page="log_params.page"
        @loadmoreFollow="loadmoreFollow"
        @onClickLevel="getDataDetail"
        @onClickFollow="getDataDetail"
        @LoadMoreLogs="LoadMoreLogs"
        style="margin-bottom: 150rpx"
      ></follow>
    </view>
    <BottomBar @click="switchTab" :current="currentTabIndex"></BottomBar>
  </view>
</template>
<script>
import myIcon from "@/components/my-icon";
import follow from "@/components/cus_follow";
import BottomBar from "./components/tabbar";
export default {
  components: {
    myIcon,
    follow,
    BottomBar,
  },
  data () {
    return {
      max: 2, // 默认最多显示的个数；最大行数*每行显示的个数 Number
      isOpen: false, // 是否展开全部信息的标识 Boolean 默认false
      currentTabIndex: 0,
      user_id: "wm-VQJYQAALBjigprpHZvoFBOpfTdcSA",
      user_detail: {},
      follow_params: {
        page: 1,
        client_id: "",
      },
      log_params: {
        page: 1,
        client_id: "",
      },
      follow_list: [],
      is_f_loading: true,
      mark_tab: [],
      logs_list: [],
      is_l_loading: true,
      labels_list: [],
      choose: [],
      diff_choose: [],
    };
  },
  onLoad (options) {
    let token = uni.getStorageSync("token" + options.website_id);
    if (!token) {
      if (this.$isWxWork() == 'wxwork') {
        return
      } else {
        localStorage.setItem('backUrl', location.href)
        this.$router.push("https://yun.tfcs.cn")
      }
    }
    if (["wxwork", "com-wx-pc"].includes(this.$isWxWork())) {
      console.log("企业微信环境");
      // 企业微信弹窗解决
      this.getWxQyWxConfig(
        [
          "agentConfig",
          "getContext",
          "onMenuShareWechat",
          "scanQRCode", //  企业微信扫码
          "onMenuShareAppMessage", // 企业微信自定义‘网页内容分享’
          "selectExternalContact",
          "navigateToAddCustomer",
          "getCurExternalContact",
        ],
        (wx) => {
          this.wx = wx;
          this.testQywx();
        }
      );
    } else {
      // 测试其他环境直接调取获取客户详情仅限158测试站点
      this.getUserDetail();
    }
    // this.getLabelsData();
  },
  methods: {
    onClickTags (id) {
      if (this.choose.includes(id)) {
        let indexex = this.choose.indexOf(id);
        this.choose.splice(indexex, 1);
      } else {
        this.choose.push(id);
      }
    },
    getLabelsData () {
      this.$ajax.get("/qywx/group_tag/search", {}, (res) => {
        if (res.statusCode === 200) {
          if (res.data.length > 0) {
            this.labels_list = res.data;
          }
        }
      });
    },
    onCreateLabels () {
      let arr = this.findArrDiff(this.diff_choose, this.choose);
      var addarr = [];
      var revarr = [];
      if (arr.type === "add") {
        addarr = arr.diff;
      } else {
        revarr = arr.diff;
      }
      let from = {
        external_userid: this.user_detail.wx_work_userid,
        add_tag: addarr.join(","),
        remove_tag: revarr.join(","),
      };
      this.$ajax.post("/qywx/client/up_tag", from, (res) => {
        if (res.statusCode === 200) {
          this.getUserDetail();
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "none",
          });
        }
      });
    },
    findArrDiff (oldValue, newValue) {
      let set;
      let diff;
      let type;
      if (newValue.length > oldValue.length) {
        console.log("add");
        type = "add";
        set = new Set(oldValue);
        diff = newValue.filter((v) => !set.has(v));
      } else {
        console.log("delete");
        type = "delete";
        set = new Set(newValue);
        diff = oldValue.filter((v) => !set.has(v));
      }
      return { diff, type };
    },
    testQywx () {
      var _this = this;
      _this.wx.invoke("getCurExternalContact", {}, function (res) {
        if (res.err_msg == "getCurExternalContact:ok") {
          _this.user_id = res.userId;
          _this.getUserDetail();
        } else {
          //错误处理
        }
      });
    },
    getUserDetail () {
      this.$ajax.get(
        // "/qywx/client/info",
        `/qywx/client/qw_info/${this.user_id}`,
        {},
        // { external_userid:  },
        (res) => {
          if (res.statusCode === 200) {
            this.user_detail = res.data;
            if (res.data.qw_tag_list) {
              this.choose = JSON.parse(JSON.stringify(res.data.qw_tag_list));
              this.diff_choose = JSON.parse(
                JSON.stringify(res.data.qw_tag_list)
              );
            }
            this.follow_params.client_id = res.data.id;
            this.log_params.client_id = res.data.id;
            this.mark_tab = [
              { description: "跟进(" + res.data.follow_num + ")", type: 0 },
              { description: "用户画像", type: 1 },
              {
                description: "客户线索(" + res.data.operation_log + ")",
                type: 2,
              },
            ];
            uni.setStorageSync("crm_client_id", res.data.id);
            this.getFollowData();
          } else {
            uni.showToast({
              title: res.data.message,
              icon: "none",
            });
          }
        }
      );
    },
    switchTab (index, item) {
      if (this.currentTabIndex == index) {
        return;
      }
      this.$navigateTo(item.path);
    },
    getFollowData () {
      uni.showLoading();
      if (this.follow_params.page === 1) {
        this.follow_list = [];
      }
      this.$ajax.get(
        "/qywx/client_follow/search",
        this.follow_params,
        (res) => {
          if (res.statusCode === 200) {
            this.follow_list = this.follow_list.concat(res.data.data);
            this.getLogsData();
            if (res.data.data.length === 0) {
              this.is_f_loading = false;
            }
          }
        }
      );
    },
    getLogsData () {
      if (this.log_params.page === 1) {
        this.logs_list = [];
      }
      this.$ajax.get("/qywx/client_clue/search", this.log_params, (res) => {
        uni.hideLoading();
        if (res.statusCode === 200) {
          this.logs_list = this.logs_list.concat(res.data.data);
          if (res.data.data.length === 0) {
            this.is_l_loading = false;
          }
        }
      });
    },
    loadmoreFollow () {
      if (!this.is_f_loading) {
        uni.showToast({
          title: "没有更多了",
          icon: "none",
        });
        return;
      }
      this.follow_params.page++;
      this.getFollowData();
    },
    LoadMoreLogs () {
      if (!this.is_l_loading) {
        uni.showToast({
          title: "没有更多了",
          icon: "none",
        });
        return;
      }
      this.log_params.page++;
      this.getLogsData();
    },
  },
};
</script>

<style lang="scss" scoped>
@import './css/markpx.scss';
</style>
