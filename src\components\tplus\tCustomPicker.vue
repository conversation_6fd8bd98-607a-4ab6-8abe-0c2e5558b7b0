<template>
    <view class="picker-input-box">
        <view class="picker-input-wrapper" @click="openPicker">
            <view class="picker-input-label" v-if="label">{{label}}</view>
            <view class="picker-input-value" :class="{'is-placeholder input-placeholder': isPlaceholder, 'align-left': align === 'left'}">
                <template v-if="isPlaceholder">{{placeholder}}</template>
                <template v-else-if="multiple && selectedLabels && selectedLabels.length">
                    <view v-for="(item,index) in selectedLabels" :key="index" class="tag-item">
                        <view>{{item.label}}</view>
                        <view class="icon-remove" @click.stop="removeSeledItem(index)"><icons type="guanbi" color="#aaa" size="28rpx"></icons></view>
                    </view>
                </template>
                <template v-else>
                    <text>{{selectedLabels}}</text>
                </template>
            </view>
            <icons v-if="withIcon && !disabled" type="jinrujiantou" color="#999" :size="size === 'small' ? 24 : 32"></icons>
       </view>
        <myPopup :show="isShow"  @close="cancle">
            <view class="container">
                <view class="header">
                    <view class="action cancle" @click.stop="cancle">取消</view>
                    <view class="title">{{placeholder}}</view>
                    <view class="action confirm" @click.stop="confirm">确认</view>
                </view>
                <view class="body">
                    <tCustomPickerView v-model="checkedValues" :allowCreate="allowCreate" :datas="datas" :filterable="filterable" :multiple="multiple" :map="map" ref="pickerView"></tCustomPickerView>
                </view>
            </view>
        </myPopup>
    </view>
</template>
<script>
import icons from '@/components/my-icon';
import myPopup from '@/components/myPopup';
import tCustomPickerView from '@/components/tplus/tCustomPickerView';
export default {
    components: {
        icons, myPopup, tCustomPickerView
    },
    props: {
        label: { type: String, default: ''},
        value: { type: [String, Number, Array], default: ''},
        datas: {type: Array, default:()=>[]},
        map: { type: Object, default: ()=>{ return {children: 'children', value: 'value', label: 'label'}}},
        placeholder: {type: String, default: ''},
        size: {type: String, default: 'base'},
        align: {type: String, default: 'right'},
        withIcon: {type: Boolean, default: true},
        disabled: {type: Boolean, default: false},

        multiple: {type: Boolean, default:false},
        filterable: {type: Boolean, default:false},
        allowCreate : { type: Boolean, default: false },
    },
    data(){
        return {
            isShow: false,
            checkedValues: '',
            selectedItems: [],
        }
    },
    computed: {
        isPlaceholder(){
            return !this.selectedItems || this.selectedItems.length == 0
        },
        selectedLabels(){

            console.log(this.selectedItems);
            return this.multiple ? this.selectedItems.map(e => {
                return {
                    label: e.label[e.label.length-1] || '',
                    value: e.value[e.value.length-1] || ''
                };
            }) : this.selectedItems.label || ''
        },
        isReady(){
            return this.datas.length && (this.multiple ? Array.isArray(this.value) && this.value.length : this.value !== '')
        }
    },
    watch: {
        value: {
            handler(val) {
                this.checkedValues = this.multiple ? [...val] : val;
            },
            immediate: true
        },
        isShow(val){
            if(val){
                this.checkedValues = this.multiple ? [...this.value] : this.value;
            }
        },
        selectedItems(list){
            const values = (list || []).map(e => {
                return e.value[e.value.length-1]
            })
            this.$emit('input', this.multiple ? values : values[0] || '');
        }
    },
    mounted(){
        const unwatch = this.$watch('isReady', {
            handler(val){
                if(val){
                    setTimeout(()=>{
                        this.selectedItems =  JSON.parse(JSON.stringify(this.$refs.pickerView.getCheckedItem()));
                        unwatch();
                    })
                }
            },
            immediate: true
        })

    },
    methods: {
        openPicker(){
            this.isShow = true;
        },
        cancle(){
            this.isShow = false;
        },
        confirm(){
            this.isShow = false;
            this.selectedItems = JSON.parse(JSON.stringify(this.$refs.pickerView.getCheckedItem() || []));
        },
        removeSeledItem(index){
            this.selectedItems.splice(index, 1);
        },
    }
}
</script>
<style lang="scss" scoped>
.picker-input-box{
    flex:1;
}
.picker-input-wrapper{
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    .picker-input-value{
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: flex-end;
        flex: 1;
        line-height: 1;
        padding-right: 5px;
        &.align-left{
            justify-content: flex-start;
            .tag-item{
                margin-right: 16rpx;
                &+.tag-item{
                    margin-left: 0;
                }
            }
        }
        &.is-placeholder{
            color: #999;
        }
        .tag-item{
            display: inline-flex;
            flex-direction: row;
            align-items: center;
            line-height: 1;
            color: #4E5969;
            padding: 12rpx 24rpx;
            margin: 8rpx 0;
            border-radius: 4rpx;
            background-color: #f0f0f0;
            &+.tag-item{
                margin-left: 16rpx;
            }
            .icon-remove{
                margin-left: 8rpx;
            }
        }
    }
}
.container{
    background: #fff;
    line-height: 1;
    
    .header{
        position: relative;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        height: 46px;
        :after {
            content: "";
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            height: 1px;
            clear: both;
            border-bottom: 1px solid #e5e5e5;
            color: #e5e5e5;
            -webkit-transform-origin: 0 100%;
            transform-origin: 0 100%;
            -webkit-transform: scaleY(.5);
            transform: scaleY(.5);
        }
        .title{
            flex: 1;
            color: #999;
            text-align: center;
            display: inline-block;
            max-width: 50%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .action{
            padding: 0 14px;
            font-size: 17px;
            &.cancle{
                color: #888;
            }
            &.confirm{
                color: #007aff;
            }
        }
    }
}
</style>