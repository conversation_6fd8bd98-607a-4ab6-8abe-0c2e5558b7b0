import moment from "moment";
const Utils = {
	/**
	 * 组装url参数
	 */
	buildHttpQuery(obj) { 
		const queryArr =[];
		for(let key in obj){ 
			let value = obj[key]; 
			key = encodeURIComponent(key);

			if(Array.isArray(value)){
				for (const val of value) { 
					queryArr.push(key + '=' + (val == null ? '' : encodeURIComponent(String(val)))); 
			   	} 
			}else{
				queryArr.push(key + '=' + (value == null ? '' : encodeURIComponent(String(value)))); 
			}
	   } 
	   return queryArr.join('&');
	},

	/**
	 * 解析 url 参数
	 */
	parseUrlQuery(query){
		const params = {};
		const queryArr = query.split("&");
		for(const item of queryArr){
			const [key, val] = item.split("=");
			params[decodeURIComponent(key)] = decodeURIComponent(val);
		}
		return params;
	},
	/**
	 * 是否数值或字符串数字
	 */
	isNumeric(num){
		return typeof num === 'number' || num  && num !== true && !isNaN(num);
	},
	/**
	 * 判断是否非空数字
	 */
	isNotEmptyArray(arr){
		return Array.isArray(arr) && arr.length ? true : false;
	},
	/** 
	 * 非阻塞暂停
	 */
	sleep(delay) { 
		return new Promise((resolve) => setTimeout(resolve, delay)); 
	},
	/**
	 * 防抖
	 */
	debounce(fn, delay = 300){
		let timer = null
		return function (...args) {
			if(timer != null){
				clearTimeout(timer)
				timer = null
			}
			timer = setTimeout(()=>{
				fn.call(this, ...args)
			}, delay);
		}
	},
	/**
	 * 节流
	 */
	throttle (fn, delay = 300) {
		let timer = null
		return function (...args) {
			if(timer == null){
				timer = setTimeout(() => {
					fn.call(this, ...args)
					clearTimeout(timer)
					timer = null
				}, delay);
			}
		}
	},

	formatDuration(t){
		if(!t) return 0;
		let units = [
				{t:':',v:3600},
				{t:'\'',v:60},
				{t:'"',v:1}
			],
			formatT = '', 
			val = 0;
		
		
		units.forEach(item=>{
			if(t >= item.v){
				val = Math.floor(t / item.v);
				formatT += val + item.t;
				t -= val*item.v 
			}
		})
		return formatT;
	},
	
	getDateRange(value, format="YYYY-MM-DD HH:ii:ss"){
		let startDate = '',
			endDate = '';        
		switch(value){
			case 'today':
				startDate = moment().format('YYYY-MM-DD');
				endDate = startDate;     
				break;
			case 'yestoday':
				startDate = moment().subtract(1, 'days').format('YYYY-MM-DD');
				endDate = startDate;
				break;
			case 'now_week':
				startDate = moment().startOf('week').add(1, 'day').format('YYYY-MM-DD');
				endDate = moment().endOf('week').add(1, 'day').format('YYYY-MM-DD');
				break;
			case 'last_week':
				startDate = moment().subtract(1, 'week').startOf('week').add(1, 'day').format('YYYY-MM-DD');
				endDate = moment().subtract(1, 'week').endOf('week').add(1, 'day').format('YYYY-MM-DD');
				break;
			case 'now_month':
				startDate = moment().startOf('month').format('YYYY-MM-DD');
				endDate = moment().endOf('month').format('YYYY-MM-DD');
				break;
			case 'last_month':
				startDate = moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD');
				endDate = moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD');
				break;
		}

		if(format == 'YYYY-MM-DD'){
			return [startDate, endDate];
		}

		return [startDate && startDate+' 00:00:00' , endDate && endDate+' 23:59:59'];
	},

};




export default Utils;